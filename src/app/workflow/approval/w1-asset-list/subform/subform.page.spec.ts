import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule } from '@ionic/angular';

import { SubformPage } from './subform.page';

describe('SubformPage', () => {
  let component: SubformPage;
  let fixture: ComponentFixture<SubformPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [SubformPage],
      imports: [IonicModule.forRoot()],
    }).compileComponents();

    fixture = TestBed.createComponent(SubformPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  }));

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
