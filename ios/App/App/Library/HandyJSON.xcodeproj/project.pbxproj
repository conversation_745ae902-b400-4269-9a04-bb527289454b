// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		210A76441E210FBB000C3DDD /* AnyExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 210A76291E210FBB000C3DDD /* AnyExtensions.swift */; };
		210A76451E210FBB000C3DDD /* AnyExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 210A76291E210FBB000C3DDD /* AnyExtensions.swift */; };
		210A76461E210FBB000C3DDD /* AnyExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 210A76291E210FBB000C3DDD /* AnyExtensions.swift */; };
		210A76471E210FBB000C3DDD /* AnyExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 210A76291E210FBB000C3DDD /* AnyExtensions.swift */; };
		210A765C1E210FBB000C3DDD /* Metadata.swift in Sources */ = {isa = PBXBuildFile; fileRef = 210A762F1E210FBB000C3DDD /* Metadata.swift */; };
		210A765D1E210FBB000C3DDD /* Metadata.swift in Sources */ = {isa = PBXBuildFile; fileRef = 210A762F1E210FBB000C3DDD /* Metadata.swift */; };
		210A765E1E210FBB000C3DDD /* Metadata.swift in Sources */ = {isa = PBXBuildFile; fileRef = 210A762F1E210FBB000C3DDD /* Metadata.swift */; };
		210A765F1E210FBB000C3DDD /* Metadata.swift in Sources */ = {isa = PBXBuildFile; fileRef = 210A762F1E210FBB000C3DDD /* Metadata.swift */; };
		210A76741E210FBB000C3DDD /* ContextDescriptorType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 210A76351E210FBB000C3DDD /* ContextDescriptorType.swift */; };
		210A76751E210FBB000C3DDD /* ContextDescriptorType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 210A76351E210FBB000C3DDD /* ContextDescriptorType.swift */; };
		210A76761E210FBB000C3DDD /* ContextDescriptorType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 210A76351E210FBB000C3DDD /* ContextDescriptorType.swift */; };
		210A76771E210FBB000C3DDD /* ContextDescriptorType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 210A76351E210FBB000C3DDD /* ContextDescriptorType.swift */; };
		210A767C1E210FBB000C3DDD /* PointerType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 210A76371E210FBB000C3DDD /* PointerType.swift */; };
		210A767D1E210FBB000C3DDD /* PointerType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 210A76371E210FBB000C3DDD /* PointerType.swift */; };
		210A767E1E210FBB000C3DDD /* PointerType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 210A76371E210FBB000C3DDD /* PointerType.swift */; };
		210A767F1E210FBB000C3DDD /* PointerType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 210A76371E210FBB000C3DDD /* PointerType.swift */; };
		210A76801E210FBB000C3DDD /* Properties.swift in Sources */ = {isa = PBXBuildFile; fileRef = 210A76381E210FBB000C3DDD /* Properties.swift */; };
		210A76811E210FBB000C3DDD /* Properties.swift in Sources */ = {isa = PBXBuildFile; fileRef = 210A76381E210FBB000C3DDD /* Properties.swift */; };
		210A76821E210FBB000C3DDD /* Properties.swift in Sources */ = {isa = PBXBuildFile; fileRef = 210A76381E210FBB000C3DDD /* Properties.swift */; };
		210A76831E210FBB000C3DDD /* Properties.swift in Sources */ = {isa = PBXBuildFile; fileRef = 210A76381E210FBB000C3DDD /* Properties.swift */; };
		210A76A61E211331000C3DDD /* ReflectionHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 210A76A51E211331000C3DDD /* ReflectionHelper.swift */; };
		210A76A71E211331000C3DDD /* ReflectionHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 210A76A51E211331000C3DDD /* ReflectionHelper.swift */; };
		210A76A81E211331000C3DDD /* ReflectionHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 210A76A51E211331000C3DDD /* ReflectionHelper.swift */; };
		210A76A91E211331000C3DDD /* ReflectionHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 210A76A51E211331000C3DDD /* ReflectionHelper.swift */; };
		21356CDA1F1A82220094A4F5 /* Measuable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2145B0A81F1A375F00707730 /* Measuable.swift */; };
		21356CDB1F1A82220094A4F5 /* Transformable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2145B0AA1F1A3AF600707730 /* Transformable.swift */; };
		21356CDC1F1A82230094A4F5 /* BuiltInBridgeType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2145B0AE1F1A3F1400707730 /* BuiltInBridgeType.swift */; };
		21356CDD1F1A82230094A4F5 /* ExtendCustomModelType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2145B0B21F1A7DE700707730 /* ExtendCustomModelType.swift */; };
		21356CDE1F1A82230094A4F5 /* Export.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2145B0B01F1A7B4700707730 /* Export.swift */; };
		21356CE21F1A82440094A4F5 /* Measuable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2145B0A81F1A375F00707730 /* Measuable.swift */; };
		21356CE31F1A82440094A4F5 /* Transformable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2145B0AA1F1A3AF600707730 /* Transformable.swift */; };
		21356CE41F1A82440094A4F5 /* BuiltInBridgeType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2145B0AE1F1A3F1400707730 /* BuiltInBridgeType.swift */; };
		21356CE51F1A82440094A4F5 /* ExtendCustomModelType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2145B0B21F1A7DE700707730 /* ExtendCustomModelType.swift */; };
		21356CE61F1A82440094A4F5 /* Export.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2145B0B01F1A7B4700707730 /* Export.swift */; };
		21356CE81F1A824B0094A4F5 /* Measuable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2145B0A81F1A375F00707730 /* Measuable.swift */; };
		21356CE91F1A824B0094A4F5 /* Transformable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2145B0AA1F1A3AF600707730 /* Transformable.swift */; };
		21356CEA1F1A824B0094A4F5 /* BuiltInBridgeType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2145B0AE1F1A3F1400707730 /* BuiltInBridgeType.swift */; };
		21356CEB1F1A824B0094A4F5 /* ExtendCustomModelType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2145B0B21F1A7DE700707730 /* ExtendCustomModelType.swift */; };
		21356CEC1F1A824B0094A4F5 /* Export.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2145B0B01F1A7B4700707730 /* Export.swift */; };
		21356CEE1F1A841C0094A4F5 /* EnumType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21356CED1F1A841C0094A4F5 /* EnumType.swift */; };
		21356CEF1F1A841C0094A4F5 /* EnumType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21356CED1F1A841C0094A4F5 /* EnumType.swift */; };
		21356CF01F1A841C0094A4F5 /* EnumType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21356CED1F1A841C0094A4F5 /* EnumType.swift */; };
		21356CF11F1A841C0094A4F5 /* EnumType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21356CED1F1A841C0094A4F5 /* EnumType.swift */; };
		214329261DFBDC1400CA386A /* TestUtils.swift in Sources */ = {isa = PBXBuildFile; fileRef = 214329251DFBDC1400CA386A /* TestUtils.swift */; };
		214329271DFBDC1400CA386A /* TestUtils.swift in Sources */ = {isa = PBXBuildFile; fileRef = 214329251DFBDC1400CA386A /* TestUtils.swift */; };
		214329281DFBDC1400CA386A /* TestUtils.swift in Sources */ = {isa = PBXBuildFile; fileRef = 214329251DFBDC1400CA386A /* TestUtils.swift */; };
		2144A50E1D9A7A770090F50E /* CustomTransformTest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2144A50D1D9A7A770090F50E /* CustomTransformTest.swift */; };
		2145B0A91F1A375F00707730 /* Measuable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2145B0A81F1A375F00707730 /* Measuable.swift */; };
		2145B0AB1F1A3AF600707730 /* Transformable.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2145B0AA1F1A3AF600707730 /* Transformable.swift */; };
		2145B0AF1F1A3F1400707730 /* BuiltInBridgeType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2145B0AE1F1A3F1400707730 /* BuiltInBridgeType.swift */; };
		2145B0B11F1A7B4700707730 /* Export.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2145B0B01F1A7B4700707730 /* Export.swift */; };
		2145B0B31F1A7DE700707730 /* ExtendCustomModelType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2145B0B21F1A7DE700707730 /* ExtendCustomModelType.swift */; };
		21520C481D3341B200F53111 /* HandyJSON.h in Headers */ = {isa = PBXBuildFile; fileRef = 21520C471D3341B200F53111 /* HandyJSON.h */; settings = {ATTRIBUTES = (Public, ); }; };
		21520C551D3341FC00F53111 /* BuiltInBasicType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21520C511D3341FC00F53111 /* BuiltInBasicType.swift */; };
		21520C561D3341FC00F53111 /* Deserializer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21520C521D3341FC00F53111 /* Deserializer.swift */; };
		21520C5E1D3344A900F53111 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21520C5D1D3344A900F53111 /* AppDelegate.swift */; };
		21520C601D3344A900F53111 /* ViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21520C5F1D3344A900F53111 /* ViewController.swift */; };
		21520C651D3344A900F53111 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 21520C641D3344A900F53111 /* Assets.xcassets */; };
		2153EB721D92162A002E41BB /* HandyJSON.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 21520C441D3341B200F53111 /* HandyJSON.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		215F13291E2175C500B0FFEC /* OtherExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 215F13281E2175C500B0FFEC /* OtherExtension.swift */; };
		215F132A1E2175C500B0FFEC /* OtherExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 215F13281E2175C500B0FFEC /* OtherExtension.swift */; };
		215F132B1E2175C500B0FFEC /* OtherExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 215F13281E2175C500B0FFEC /* OtherExtension.swift */; };
		215F132C1E2175C500B0FFEC /* OtherExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 215F13281E2175C500B0FFEC /* OtherExtension.swift */; };
		215F132E1E2179E700B0FFEC /* LICENSE in Resources */ = {isa = PBXBuildFile; fileRef = 215F132D1E2179E700B0FFEC /* LICENSE */; };
		215F132F1E2179E700B0FFEC /* LICENSE in Resources */ = {isa = PBXBuildFile; fileRef = 215F132D1E2179E700B0FFEC /* LICENSE */; };
		215F13301E2179E700B0FFEC /* LICENSE in Resources */ = {isa = PBXBuildFile; fileRef = 215F132D1E2179E700B0FFEC /* LICENSE */; };
		215F13311E2179E700B0FFEC /* LICENSE in Resources */ = {isa = PBXBuildFile; fileRef = 215F132D1E2179E700B0FFEC /* LICENSE */; };
		215F13481E217B9C00B0FFEC /* BasicTypesInClass.swift in Sources */ = {isa = PBXBuildFile; fileRef = 215F13461E217B9C00B0FFEC /* BasicTypesInClass.swift */; };
		215F13491E217B9C00B0FFEC /* BasicTypesInClassTestsToJSON.swift in Sources */ = {isa = PBXBuildFile; fileRef = 215F13471E217B9C00B0FFEC /* BasicTypesInClassTestsToJSON.swift */; };
		216652021F49CA5B003B7F9A /* PropertyInfo.swift in Sources */ = {isa = PBXBuildFile; fileRef = 216652011F49CA5B003B7F9A /* PropertyInfo.swift */; };
		217801E81F5E60C900EDEB38 /* ExtendCustomBasicType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 217801E71F5E60C900EDEB38 /* ExtendCustomBasicType.swift */; };
		217801E91F5E60C900EDEB38 /* ExtendCustomBasicType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 217801E71F5E60C900EDEB38 /* ExtendCustomBasicType.swift */; };
		217801EA1F5E60C900EDEB38 /* ExtendCustomBasicType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 217801E71F5E60C900EDEB38 /* ExtendCustomBasicType.swift */; };
		217801EB1F5E60C900EDEB38 /* ExtendCustomBasicType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 217801E71F5E60C900EDEB38 /* ExtendCustomBasicType.swift */; };
		217801ED1F5E794800EDEB38 /* BasicTypesInStruct.swift in Sources */ = {isa = PBXBuildFile; fileRef = 217801EC1F5E794800EDEB38 /* BasicTypesInStruct.swift */; };
		217801EE1F5E794800EDEB38 /* BasicTypesInStruct.swift in Sources */ = {isa = PBXBuildFile; fileRef = 217801EC1F5E794800EDEB38 /* BasicTypesInStruct.swift */; };
		217801EF1F5E794800EDEB38 /* BasicTypesInStruct.swift in Sources */ = {isa = PBXBuildFile; fileRef = 217801EC1F5E794800EDEB38 /* BasicTypesInStruct.swift */; };
		217801F11F5E7ACB00EDEB38 /* BasicTypesInStructTestsToJSON.swift in Sources */ = {isa = PBXBuildFile; fileRef = 217801F01F5E7ACB00EDEB38 /* BasicTypesInStructTestsToJSON.swift */; };
		217801F21F5E7ACB00EDEB38 /* BasicTypesInStructTestsToJSON.swift in Sources */ = {isa = PBXBuildFile; fileRef = 217801F01F5E7ACB00EDEB38 /* BasicTypesInStructTestsToJSON.swift */; };
		217801F31F5E7ACB00EDEB38 /* BasicTypesInStructTestsToJSON.swift in Sources */ = {isa = PBXBuildFile; fileRef = 217801F01F5E7ACB00EDEB38 /* BasicTypesInStructTestsToJSON.swift */; };
		217801F51F5E7CFD00EDEB38 /* BasicTypesInStructTestsFromJSON.swift in Sources */ = {isa = PBXBuildFile; fileRef = 217801F41F5E7CFD00EDEB38 /* BasicTypesInStructTestsFromJSON.swift */; };
		217801F61F5E7CFD00EDEB38 /* BasicTypesInStructTestsFromJSON.swift in Sources */ = {isa = PBXBuildFile; fileRef = 217801F41F5E7CFD00EDEB38 /* BasicTypesInStructTestsFromJSON.swift */; };
		217801F71F5E7CFD00EDEB38 /* BasicTypesInStructTestsFromJSON.swift in Sources */ = {isa = PBXBuildFile; fileRef = 217801F41F5E7CFD00EDEB38 /* BasicTypesInStructTestsFromJSON.swift */; };
		217801F91F5E80A200EDEB38 /* CustomTransfromTypes.swift in Sources */ = {isa = PBXBuildFile; fileRef = 217801F81F5E80A200EDEB38 /* CustomTransfromTypes.swift */; };
		217801FA1F5E80A200EDEB38 /* CustomTransfromTypes.swift in Sources */ = {isa = PBXBuildFile; fileRef = 217801F81F5E80A200EDEB38 /* CustomTransfromTypes.swift */; };
		217801FB1F5E80A200EDEB38 /* CustomTransfromTypes.swift in Sources */ = {isa = PBXBuildFile; fileRef = 217801F81F5E80A200EDEB38 /* CustomTransfromTypes.swift */; };
		2179B6B31D911DB4007B0320 /* HelpingMapper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2179B6B21D911DB4007B0320 /* HelpingMapper.swift */; };
		217B04701D571B9F0035A8E2 /* OtherFeaturesTest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 217B046F1D571B9F0035A8E2 /* OtherFeaturesTest.swift */; };
		217B04721D571B9F0035A8E2 /* HandyJSON.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 21520C441D3341B200F53111 /* HandyJSON.framework */; };
		21A191F41E21E62500CD7609 /* BasicTypesInClassTestsFromJSON.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21A191F31E21E62500CD7609 /* BasicTypesInClassTestsFromJSON.swift */; };
		21A191F51E21E62E00CD7609 /* BasicTypesInClassTestsFromJSON.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21A191F31E21E62500CD7609 /* BasicTypesInClassTestsFromJSON.swift */; };
		21A191F61E21E63100CD7609 /* BasicTypesInClassTestsFromJSON.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21A191F31E21E62500CD7609 /* BasicTypesInClassTestsFromJSON.swift */; };
		21A191FD1E222E8C00CD7609 /* Logger.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21A191FC1E222E8C00CD7609 /* Logger.swift */; };
		21A191FE1E222E8C00CD7609 /* Logger.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21A191FC1E222E8C00CD7609 /* Logger.swift */; };
		21A191FF1E222E8C00CD7609 /* Logger.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21A191FC1E222E8C00CD7609 /* Logger.swift */; };
		21A192001E222E8C00CD7609 /* Logger.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21A191FC1E222E8C00CD7609 /* Logger.swift */; };
		21A192021E22332000CD7609 /* Configuration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21A192011E22332000CD7609 /* Configuration.swift */; };
		21A192031E22332000CD7609 /* Configuration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21A192011E22332000CD7609 /* Configuration.swift */; };
		21A192041E22332000CD7609 /* Configuration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21A192011E22332000CD7609 /* Configuration.swift */; };
		21A192051E22332000CD7609 /* Configuration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21A192011E22332000CD7609 /* Configuration.swift */; };
		21A192061E223F8900CD7609 /* BasicTypesInClass.swift in Sources */ = {isa = PBXBuildFile; fileRef = 215F13461E217B9C00B0FFEC /* BasicTypesInClass.swift */; };
		21A192071E223F8900CD7609 /* BasicTypesInClassTestsToJSON.swift in Sources */ = {isa = PBXBuildFile; fileRef = 215F13471E217B9C00B0FFEC /* BasicTypesInClassTestsToJSON.swift */; };
		21A192081E223F9000CD7609 /* BasicTypesInClass.swift in Sources */ = {isa = PBXBuildFile; fileRef = 215F13461E217B9C00B0FFEC /* BasicTypesInClass.swift */; };
		21A192091E223F9000CD7609 /* BasicTypesInClassTestsToJSON.swift in Sources */ = {isa = PBXBuildFile; fileRef = 215F13471E217B9C00B0FFEC /* BasicTypesInClassTestsToJSON.swift */; };
		21A7A59D1D9EBF450015E3F0 /* Serializer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21A7A59C1D9EBF450015E3F0 /* Serializer.swift */; };
		21D7AD3B1F61018C008FC7A4 /* PropertyInfo.swift in Sources */ = {isa = PBXBuildFile; fileRef = 216652011F49CA5B003B7F9A /* PropertyInfo.swift */; };
		21D7AD3C1F610192008FC7A4 /* PropertyInfo.swift in Sources */ = {isa = PBXBuildFile; fileRef = 216652011F49CA5B003B7F9A /* PropertyInfo.swift */; };
		21D7AD3D1F610197008FC7A4 /* PropertyInfo.swift in Sources */ = {isa = PBXBuildFile; fileRef = 216652011F49CA5B003B7F9A /* PropertyInfo.swift */; };
		21D7AD3F1F61021A008FC7A4 /* NestTypes.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21D7AD3E1F61021A008FC7A4 /* NestTypes.swift */; };
		21D7AD401F61021A008FC7A4 /* NestTypes.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21D7AD3E1F61021A008FC7A4 /* NestTypes.swift */; };
		21D7AD411F61021A008FC7A4 /* NestTypes.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21D7AD3E1F61021A008FC7A4 /* NestTypes.swift */; };
		21DFCA561E18EB9C00856775 /* DateTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA521E18EB9C00856775 /* DateTransform.swift */; };
		21DFCA571E18EB9C00856775 /* DateTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA521E18EB9C00856775 /* DateTransform.swift */; };
		21DFCA581E18EB9C00856775 /* DateTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA521E18EB9C00856775 /* DateTransform.swift */; };
		21DFCA591E18EB9C00856775 /* DateTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA521E18EB9C00856775 /* DateTransform.swift */; };
		21DFCA5A1E18EB9C00856775 /* DateFormatterTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA531E18EB9C00856775 /* DateFormatterTransform.swift */; };
		21DFCA5B1E18EB9C00856775 /* DateFormatterTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA531E18EB9C00856775 /* DateFormatterTransform.swift */; };
		21DFCA5C1E18EB9C00856775 /* DateFormatterTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA531E18EB9C00856775 /* DateFormatterTransform.swift */; };
		21DFCA5D1E18EB9C00856775 /* DateFormatterTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA531E18EB9C00856775 /* DateFormatterTransform.swift */; };
		21DFCA5E1E18EB9C00856775 /* ISO8601DateTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA541E18EB9C00856775 /* ISO8601DateTransform.swift */; };
		21DFCA5F1E18EB9C00856775 /* ISO8601DateTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA541E18EB9C00856775 /* ISO8601DateTransform.swift */; };
		21DFCA601E18EB9C00856775 /* ISO8601DateTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA541E18EB9C00856775 /* ISO8601DateTransform.swift */; };
		21DFCA611E18EB9C00856775 /* ISO8601DateTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA541E18EB9C00856775 /* ISO8601DateTransform.swift */; };
		21DFCA621E18EB9C00856775 /* CustomDateFormatTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA551E18EB9C00856775 /* CustomDateFormatTransform.swift */; };
		21DFCA631E18EB9C00856775 /* CustomDateFormatTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA551E18EB9C00856775 /* CustomDateFormatTransform.swift */; };
		21DFCA641E18EB9C00856775 /* CustomDateFormatTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA551E18EB9C00856775 /* CustomDateFormatTransform.swift */; };
		21DFCA651E18EB9C00856775 /* CustomDateFormatTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA551E18EB9C00856775 /* CustomDateFormatTransform.swift */; };
		21DFCA6E1E18EBA600856775 /* TransformOf.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA661E18EBA600856775 /* TransformOf.swift */; };
		21DFCA6F1E18EBA600856775 /* TransformOf.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA661E18EBA600856775 /* TransformOf.swift */; };
		21DFCA701E18EBA600856775 /* TransformOf.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA661E18EBA600856775 /* TransformOf.swift */; };
		21DFCA711E18EBA600856775 /* TransformOf.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA661E18EBA600856775 /* TransformOf.swift */; };
		21DFCA721E18EBA600856775 /* TransformType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA671E18EBA600856775 /* TransformType.swift */; };
		21DFCA731E18EBA600856775 /* TransformType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA671E18EBA600856775 /* TransformType.swift */; };
		21DFCA741E18EBA600856775 /* TransformType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA671E18EBA600856775 /* TransformType.swift */; };
		21DFCA751E18EBA600856775 /* TransformType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA671E18EBA600856775 /* TransformType.swift */; };
		21DFCA761E18EBA600856775 /* URLTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA681E18EBA600856775 /* URLTransform.swift */; };
		21DFCA771E18EBA600856775 /* URLTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA681E18EBA600856775 /* URLTransform.swift */; };
		21DFCA781E18EBA600856775 /* URLTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA681E18EBA600856775 /* URLTransform.swift */; };
		21DFCA791E18EBA600856775 /* URLTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA681E18EBA600856775 /* URLTransform.swift */; };
		21DFCA7A1E18EBA600856775 /* EnumTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA691E18EBA600856775 /* EnumTransform.swift */; };
		21DFCA7B1E18EBA600856775 /* EnumTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA691E18EBA600856775 /* EnumTransform.swift */; };
		21DFCA7C1E18EBA600856775 /* EnumTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA691E18EBA600856775 /* EnumTransform.swift */; };
		21DFCA7D1E18EBA600856775 /* EnumTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA691E18EBA600856775 /* EnumTransform.swift */; };
		21DFCA7E1E18EBA600856775 /* NSDecimalNumberTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA6A1E18EBA600856775 /* NSDecimalNumberTransform.swift */; };
		21DFCA7F1E18EBA600856775 /* NSDecimalNumberTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA6A1E18EBA600856775 /* NSDecimalNumberTransform.swift */; };
		21DFCA801E18EBA600856775 /* NSDecimalNumberTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA6A1E18EBA600856775 /* NSDecimalNumberTransform.swift */; };
		21DFCA811E18EBA600856775 /* NSDecimalNumberTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA6A1E18EBA600856775 /* NSDecimalNumberTransform.swift */; };
		21DFCA861E18EBA600856775 /* DataTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA6C1E18EBA600856775 /* DataTransform.swift */; };
		21DFCA871E18EBA600856775 /* DataTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA6C1E18EBA600856775 /* DataTransform.swift */; };
		21DFCA881E18EBA600856775 /* DataTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA6C1E18EBA600856775 /* DataTransform.swift */; };
		21DFCA891E18EBA600856775 /* DataTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA6C1E18EBA600856775 /* DataTransform.swift */; };
		21DFCA8A1E18EBA600856775 /* HexColorTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA6D1E18EBA600856775 /* HexColorTransform.swift */; };
		21DFCA8B1E18EBA600856775 /* HexColorTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA6D1E18EBA600856775 /* HexColorTransform.swift */; };
		21DFCA8C1E18EBA600856775 /* HexColorTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA6D1E18EBA600856775 /* HexColorTransform.swift */; };
		21DFCA8D1E18EBA600856775 /* HexColorTransform.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21DFCA6D1E18EBA600856775 /* HexColorTransform.swift */; };
		21F252AA1D58BE9100B214BB /* NestTypesTest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21F252A91D58BE9100B214BB /* NestTypesTest.swift */; };
		21F252B41D599DE200B214BB /* InvalidStateHandlingTest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21F252B31D599DE200B214BB /* InvalidStateHandlingTest.swift */; };
		393E0FA71DA165E300B3BC12 /* HelpingMapper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2179B6B21D911DB4007B0320 /* HelpingMapper.swift */; };
		393E0FA81DA165E300B3BC12 /* BuiltInBasicType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21520C511D3341FC00F53111 /* BuiltInBasicType.swift */; };
		393E0FA91DA165E300B3BC12 /* Deserializer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21520C521D3341FC00F53111 /* Deserializer.swift */; };
		393E0FAA1DA165E300B3BC12 /* Serializer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21A7A59C1D9EBF450015E3F0 /* Serializer.swift */; };
		393E0FAB1DA165F900B3BC12 /* HandyJSON.h in Headers */ = {isa = PBXBuildFile; fileRef = 21520C471D3341B200F53111 /* HandyJSON.h */; settings = {ATTRIBUTES = (Public, ); }; };
		393E0FBA1DA2441E00B3BC12 /* HandyJSON.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 393E0FB11DA2441E00B3BC12 /* HandyJSON.framework */; };
		393E0FCE1DA2484F00B3BC12 /* HelpingMapper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2179B6B21D911DB4007B0320 /* HelpingMapper.swift */; };
		393E0FCF1DA2484F00B3BC12 /* BuiltInBasicType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21520C511D3341FC00F53111 /* BuiltInBasicType.swift */; };
		393E0FD01DA2484F00B3BC12 /* Deserializer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21520C521D3341FC00F53111 /* Deserializer.swift */; };
		393E0FD11DA2484F00B3BC12 /* Serializer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21A7A59C1D9EBF450015E3F0 /* Serializer.swift */; };
		393E0FD21DA2485800B3BC12 /* HandyJSON.h in Headers */ = {isa = PBXBuildFile; fileRef = 21520C471D3341B200F53111 /* HandyJSON.h */; settings = {ATTRIBUTES = (Public, ); }; };
		393E0FD31DA2486C00B3BC12 /* OtherFeaturesTest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 217B046F1D571B9F0035A8E2 /* OtherFeaturesTest.swift */; };
		393E0FD51DA2486C00B3BC12 /* NestTypesTest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21F252A91D58BE9100B214BB /* NestTypesTest.swift */; };
		393E0FD61DA2486C00B3BC12 /* CustomTransformTest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2144A50D1D9A7A770090F50E /* CustomTransformTest.swift */; };
		393E0FDA1DA2486C00B3BC12 /* InvalidStateHandlingTest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21F252B31D599DE200B214BB /* InvalidStateHandlingTest.swift */; };
		393E0FEA1DA2494F00B3BC12 /* HandyJSON.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 393E0FE11DA2494F00B3BC12 /* HandyJSON.framework */; };
		393E0FFA1DA2495900B3BC12 /* HelpingMapper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2179B6B21D911DB4007B0320 /* HelpingMapper.swift */; };
		393E0FFB1DA2495900B3BC12 /* BuiltInBasicType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21520C511D3341FC00F53111 /* BuiltInBasicType.swift */; };
		393E0FFC1DA2495900B3BC12 /* Deserializer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21520C521D3341FC00F53111 /* Deserializer.swift */; };
		393E0FFD1DA2495900B3BC12 /* Serializer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21A7A59C1D9EBF450015E3F0 /* Serializer.swift */; };
		393E0FFE1DA249D000B3BC12 /* HandyJSON.h in Headers */ = {isa = PBXBuildFile; fileRef = 21520C471D3341B200F53111 /* HandyJSON.h */; settings = {ATTRIBUTES = (Public, ); }; };
		393E0FFF1DA249DC00B3BC12 /* OtherFeaturesTest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 217B046F1D571B9F0035A8E2 /* OtherFeaturesTest.swift */; };
		393E10011DA249DC00B3BC12 /* NestTypesTest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21F252A91D58BE9100B214BB /* NestTypesTest.swift */; };
		393E10021DA249DC00B3BC12 /* CustomTransformTest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2144A50D1D9A7A770090F50E /* CustomTransformTest.swift */; };
		393E10061DA249DC00B3BC12 /* InvalidStateHandlingTest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21F252B31D599DE200B214BB /* InvalidStateHandlingTest.swift */; };
		7D2813E722058D060044E052 /* MangledName.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7D2813E622058D060044E052 /* MangledName.swift */; };
		7D2813E822058D060044E052 /* MangledName.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7D2813E622058D060044E052 /* MangledName.swift */; };
		7D2813E922058D060044E052 /* MangledName.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7D2813E622058D060044E052 /* MangledName.swift */; };
		7D2813EA22058D060044E052 /* MangledName.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7D2813E622058D060044E052 /* MangledName.swift */; };
		7D460F662203511D0027428E /* FieldDescriptor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7D460F652203511D0027428E /* FieldDescriptor.swift */; };
		7D460F672203511D0027428E /* FieldDescriptor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7D460F652203511D0027428E /* FieldDescriptor.swift */; };
		7D460F682203511D0027428E /* FieldDescriptor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7D460F652203511D0027428E /* FieldDescriptor.swift */; };
		7D460F692203511D0027428E /* FieldDescriptor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7D460F652203511D0027428E /* FieldDescriptor.swift */; };
		7D912DA02403E42D00897258 /* OCUIInheritanceTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7D912D9F2403E42D00897258 /* OCUIInheritanceTests.swift */; };
		7D912DA22403E4C800897258 /* OCUIInheritanceClass.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7D912DA12403E4C800897258 /* OCUIInheritanceClass.swift */; };
		7D9EFA7F224BA3E3002496B7 /* GenericTypes.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7D9EFA7E224BA3E3002496B7 /* GenericTypes.swift */; };
		7D9EFA80224BA3E3002496B7 /* GenericTypes.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7D9EFA7E224BA3E3002496B7 /* GenericTypes.swift */; };
		7D9EFA81224BA3E3002496B7 /* GenericTypes.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7D9EFA7E224BA3E3002496B7 /* GenericTypes.swift */; };
		7D9EFA83224BA40D002496B7 /* GenericTypesTest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7D9EFA82224BA40D002496B7 /* GenericTypesTest.swift */; };
		7D9EFA84224BA40D002496B7 /* GenericTypesTest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7D9EFA82224BA40D002496B7 /* GenericTypesTest.swift */; };
		7D9EFA85224BA40D002496B7 /* GenericTypesTest.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7D9EFA82224BA40D002496B7 /* GenericTypesTest.swift */; };
		7DCD448220FB593600370EF4 /* CBridge.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7DCD448120FB593600370EF4 /* CBridge.swift */; };
		7DCD448320FB593600370EF4 /* CBridge.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7DCD448120FB593600370EF4 /* CBridge.swift */; };
		7DCD448420FB593600370EF4 /* CBridge.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7DCD448120FB593600370EF4 /* CBridge.swift */; };
		7DCD448520FB593600370EF4 /* CBridge.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7DCD448120FB593600370EF4 /* CBridge.swift */; };
		7DCD448620FB593600370EF4 /* CBridge.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7DCD448120FB593600370EF4 /* CBridge.swift */; };
		7DCD448720FB593600370EF4 /* CBridge.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7DCD448120FB593600370EF4 /* CBridge.swift */; };
		7DCD448820FB593600370EF4 /* CBridge.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7DCD448120FB593600370EF4 /* CBridge.swift */; };
		7DCD448920FB593600370EF4 /* CBridge.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7DCD448120FB593600370EF4 /* CBridge.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		21520C6D1D33452200F53111 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 21520C3B1D3341B200F53111 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 21520C431D3341B200F53111;
			remoteInfo = HandyJSON;
		};
		217B04731D571B9F0035A8E2 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 21520C3B1D3341B200F53111 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 21520C431D3341B200F53111;
			remoteInfo = HandyJSON;
		};
		393E0FBB1DA2441E00B3BC12 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 21520C3B1D3341B200F53111 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 393E0FB01DA2441E00B3BC12;
			remoteInfo = "HandyJSON tvOS";
		};
		393E0FEB1DA2494F00B3BC12 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 21520C3B1D3341B200F53111 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 393E0FE01DA2494F00B3BC12;
			remoteInfo = "HandyJSON macOS";
		};
		3ABCC36F1EBEF2890001BDBB /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 21520C3B1D3341B200F53111 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 21520C5A1D3344A900F53111;
			remoteInfo = HandyJSONDemo;
		};
		7DAEE14223F2E9A0002C156F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 21520C3B1D3341B200F53111 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 21520C5A1D3344A900F53111;
			remoteInfo = HandyJSONDemo;
		};
		7DAEE14423F2E9A4002C156F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 21520C3B1D3341B200F53111 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 21520C5A1D3344A900F53111;
			remoteInfo = HandyJSONDemo;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		2153EB731D92162A002E41BB /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				2153EB721D92162A002E41BB /* HandyJSON.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		210A76291E210FBB000C3DDD /* AnyExtensions.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AnyExtensions.swift; sourceTree = "<group>"; };
		210A762F1E210FBB000C3DDD /* Metadata.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Metadata.swift; sourceTree = "<group>"; };
		210A76351E210FBB000C3DDD /* ContextDescriptorType.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ContextDescriptorType.swift; sourceTree = "<group>"; };
		210A76371E210FBB000C3DDD /* PointerType.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PointerType.swift; sourceTree = "<group>"; };
		210A76381E210FBB000C3DDD /* Properties.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Properties.swift; sourceTree = "<group>"; };
		210A76A51E211331000C3DDD /* ReflectionHelper.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ReflectionHelper.swift; sourceTree = "<group>"; };
		21356CED1F1A841C0094A4F5 /* EnumType.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = EnumType.swift; sourceTree = "<group>"; };
		214329251DFBDC1400CA386A /* TestUtils.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TestUtils.swift; sourceTree = "<group>"; };
		2144A50D1D9A7A770090F50E /* CustomTransformTest.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CustomTransformTest.swift; sourceTree = "<group>"; };
		2145B0A81F1A375F00707730 /* Measuable.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Measuable.swift; sourceTree = "<group>"; };
		2145B0AA1F1A3AF600707730 /* Transformable.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Transformable.swift; sourceTree = "<group>"; };
		2145B0AE1F1A3F1400707730 /* BuiltInBridgeType.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BuiltInBridgeType.swift; sourceTree = "<group>"; };
		2145B0B01F1A7B4700707730 /* Export.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Export.swift; sourceTree = "<group>"; };
		2145B0B21F1A7DE700707730 /* ExtendCustomModelType.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ExtendCustomModelType.swift; sourceTree = "<group>"; };
		21520C441D3341B200F53111 /* HandyJSON.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = HandyJSON.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		21520C471D3341B200F53111 /* HandyJSON.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HandyJSON.h; sourceTree = "<group>"; };
		21520C491D3341B200F53111 /* Info-iOS.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "Info-iOS.plist"; sourceTree = "<group>"; };
		21520C511D3341FC00F53111 /* BuiltInBasicType.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BuiltInBasicType.swift; sourceTree = "<group>"; };
		21520C521D3341FC00F53111 /* Deserializer.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Deserializer.swift; sourceTree = "<group>"; };
		21520C5B1D3344A900F53111 /* HandyJSONDemo.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = HandyJSONDemo.app; sourceTree = BUILT_PRODUCTS_DIR; };
		21520C5D1D3344A900F53111 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		21520C5F1D3344A900F53111 /* ViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewController.swift; sourceTree = "<group>"; };
		21520C641D3344A900F53111 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		21520C691D3344A900F53111 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		215F13281E2175C500B0FFEC /* OtherExtension.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OtherExtension.swift; sourceTree = "<group>"; };
		215F132D1E2179E700B0FFEC /* LICENSE */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = LICENSE; sourceTree = "<group>"; };
		215F13461E217B9C00B0FFEC /* BasicTypesInClass.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BasicTypesInClass.swift; sourceTree = "<group>"; };
		215F13471E217B9C00B0FFEC /* BasicTypesInClassTestsToJSON.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BasicTypesInClassTestsToJSON.swift; sourceTree = "<group>"; };
		216652011F49CA5B003B7F9A /* PropertyInfo.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PropertyInfo.swift; sourceTree = "<group>"; };
		217801E71F5E60C900EDEB38 /* ExtendCustomBasicType.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ExtendCustomBasicType.swift; sourceTree = "<group>"; };
		217801EC1F5E794800EDEB38 /* BasicTypesInStruct.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BasicTypesInStruct.swift; sourceTree = "<group>"; };
		217801F01F5E7ACB00EDEB38 /* BasicTypesInStructTestsToJSON.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BasicTypesInStructTestsToJSON.swift; sourceTree = "<group>"; };
		217801F41F5E7CFD00EDEB38 /* BasicTypesInStructTestsFromJSON.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BasicTypesInStructTestsFromJSON.swift; sourceTree = "<group>"; };
		217801F81F5E80A200EDEB38 /* CustomTransfromTypes.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CustomTransfromTypes.swift; sourceTree = "<group>"; };
		2179B6B21D911DB4007B0320 /* HelpingMapper.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = HelpingMapper.swift; sourceTree = "<group>"; };
		217B046D1D571B9F0035A8E2 /* HandyJSON iOS Tests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "HandyJSON iOS Tests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		217B046F1D571B9F0035A8E2 /* OtherFeaturesTest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OtherFeaturesTest.swift; sourceTree = "<group>"; };
		217B04711D571B9F0035A8E2 /* Info-iOS.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "Info-iOS.plist"; sourceTree = "<group>"; };
		21A191F31E21E62500CD7609 /* BasicTypesInClassTestsFromJSON.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BasicTypesInClassTestsFromJSON.swift; sourceTree = "<group>"; };
		21A191FC1E222E8C00CD7609 /* Logger.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Logger.swift; sourceTree = "<group>"; };
		21A192011E22332000CD7609 /* Configuration.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Configuration.swift; sourceTree = "<group>"; };
		21A7A59C1D9EBF450015E3F0 /* Serializer.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Serializer.swift; sourceTree = "<group>"; };
		21D7AD3E1F61021A008FC7A4 /* NestTypes.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NestTypes.swift; sourceTree = "<group>"; };
		21DFCA521E18EB9C00856775 /* DateTransform.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DateTransform.swift; sourceTree = "<group>"; };
		21DFCA531E18EB9C00856775 /* DateFormatterTransform.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DateFormatterTransform.swift; sourceTree = "<group>"; };
		21DFCA541E18EB9C00856775 /* ISO8601DateTransform.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ISO8601DateTransform.swift; sourceTree = "<group>"; };
		21DFCA551E18EB9C00856775 /* CustomDateFormatTransform.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CustomDateFormatTransform.swift; sourceTree = "<group>"; };
		21DFCA661E18EBA600856775 /* TransformOf.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TransformOf.swift; sourceTree = "<group>"; };
		21DFCA671E18EBA600856775 /* TransformType.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TransformType.swift; sourceTree = "<group>"; };
		21DFCA681E18EBA600856775 /* URLTransform.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = URLTransform.swift; sourceTree = "<group>"; };
		21DFCA691E18EBA600856775 /* EnumTransform.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = EnumTransform.swift; sourceTree = "<group>"; };
		21DFCA6A1E18EBA600856775 /* NSDecimalNumberTransform.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NSDecimalNumberTransform.swift; sourceTree = "<group>"; };
		21DFCA6C1E18EBA600856775 /* DataTransform.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DataTransform.swift; sourceTree = "<group>"; };
		21DFCA6D1E18EBA600856775 /* HexColorTransform.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = HexColorTransform.swift; sourceTree = "<group>"; };
		21F252A91D58BE9100B214BB /* NestTypesTest.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NestTypesTest.swift; sourceTree = "<group>"; };
		21F252B31D599DE200B214BB /* InvalidStateHandlingTest.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = InvalidStateHandlingTest.swift; sourceTree = "<group>"; };
		393E0F9B1DA1650E00B3BC12 /* HandyJSON.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = HandyJSON.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		393E0FA31DA165CE00B3BC12 /* Info-watchOS.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "Info-watchOS.plist"; sourceTree = "<group>"; };
		393E0FB11DA2441E00B3BC12 /* HandyJSON.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = HandyJSON.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		393E0FB91DA2441E00B3BC12 /* HandyJSON tvOS Tests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "HandyJSON tvOS Tests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		393E0FC81DA245E300B3BC12 /* Info-tvOS.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "Info-tvOS.plist"; sourceTree = "<group>"; };
		393E0FCA1DA2481000B3BC12 /* Info-tvOS.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "Info-tvOS.plist"; sourceTree = "<group>"; };
		393E0FE11DA2494F00B3BC12 /* HandyJSON.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = HandyJSON.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		393E0FE91DA2494F00B3BC12 /* HandyJSON macOS Tests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "HandyJSON macOS Tests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		393E10081DA249F100B3BC12 /* Info-macOS.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "Info-macOS.plist"; sourceTree = "<group>"; };
		393E100A1DA24A0600B3BC12 /* Info-macOS.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "Info-macOS.plist"; sourceTree = "<group>"; };
		7D2813E622058D060044E052 /* MangledName.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MangledName.swift; sourceTree = "<group>"; };
		7D460F652203511D0027428E /* FieldDescriptor.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FieldDescriptor.swift; sourceTree = "<group>"; };
		7D912D9F2403E42D00897258 /* OCUIInheritanceTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OCUIInheritanceTests.swift; sourceTree = "<group>"; };
		7D912DA12403E4C800897258 /* OCUIInheritanceClass.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OCUIInheritanceClass.swift; sourceTree = "<group>"; };
		7D9EFA7E224BA3E3002496B7 /* GenericTypes.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GenericTypes.swift; sourceTree = "<group>"; };
		7D9EFA82224BA40D002496B7 /* GenericTypesTest.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GenericTypesTest.swift; sourceTree = "<group>"; };
		7DCD448120FB593600370EF4 /* CBridge.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CBridge.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		21520C401D3341B200F53111 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		21520C581D3344A900F53111 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		217B046A1D571B9F0035A8E2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				217B04721D571B9F0035A8E2 /* HandyJSON.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		393E0F971DA1650E00B3BC12 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		393E0FAD1DA2441E00B3BC12 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		393E0FB61DA2441E00B3BC12 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				393E0FBA1DA2441E00B3BC12 /* HandyJSON.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		393E0FDD1DA2494F00B3BC12 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		393E0FE61DA2494F00B3BC12 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				393E0FEA1DA2494F00B3BC12 /* HandyJSON.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		210A76271E210F5B000C3DDD /* Reflection */ = {
			isa = PBXGroup;
			children = (
				7D460F652203511D0027428E /* FieldDescriptor.swift */,
				210A76291E210FBB000C3DDD /* AnyExtensions.swift */,
				215F13281E2175C500B0FFEC /* OtherExtension.swift */,
				210A762F1E210FBB000C3DDD /* Metadata.swift */,
				210A76351E210FBB000C3DDD /* ContextDescriptorType.swift */,
				210A76371E210FBB000C3DDD /* PointerType.swift */,
				210A76381E210FBB000C3DDD /* Properties.swift */,
				210A76A51E211331000C3DDD /* ReflectionHelper.swift */,
				215F132D1E2179E700B0FFEC /* LICENSE */,
				7DCD448120FB593600370EF4 /* CBridge.swift */,
				7D2813E622058D060044E052 /* MangledName.swift */,
			);
			name = Reflection;
			sourceTree = "<group>";
		};
		21520C3A1D3341B200F53111 = {
			isa = PBXGroup;
			children = (
				21520C461D3341B200F53111 /* HandyJSON */,
				217B046E1D571B9F0035A8E2 /* HandyJSONTests */,
				21520C5C1D3344A900F53111 /* HandyJSONDemo */,
				21520C451D3341B200F53111 /* Products */,
			);
			sourceTree = "<group>";
		};
		21520C451D3341B200F53111 /* Products */ = {
			isa = PBXGroup;
			children = (
				21520C441D3341B200F53111 /* HandyJSON.framework */,
				21520C5B1D3344A900F53111 /* HandyJSONDemo.app */,
				217B046D1D571B9F0035A8E2 /* HandyJSON iOS Tests.xctest */,
				393E0F9B1DA1650E00B3BC12 /* HandyJSON.framework */,
				393E0FB11DA2441E00B3BC12 /* HandyJSON.framework */,
				393E0FB91DA2441E00B3BC12 /* HandyJSON tvOS Tests.xctest */,
				393E0FE11DA2494F00B3BC12 /* HandyJSON.framework */,
				393E0FE91DA2494F00B3BC12 /* HandyJSON macOS Tests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		21520C461D3341B200F53111 /* HandyJSON */ = {
			isa = PBXGroup;
			children = (
				21520C471D3341B200F53111 /* HandyJSON.h */,
				2145B0A81F1A375F00707730 /* Measuable.swift */,
				2145B0AA1F1A3AF600707730 /* Transformable.swift */,
				21520C511D3341FC00F53111 /* BuiltInBasicType.swift */,
				2145B0AE1F1A3F1400707730 /* BuiltInBridgeType.swift */,
				21356CED1F1A841C0094A4F5 /* EnumType.swift */,
				2145B0B21F1A7DE700707730 /* ExtendCustomModelType.swift */,
				217801E71F5E60C900EDEB38 /* ExtendCustomBasicType.swift */,
				216652011F49CA5B003B7F9A /* PropertyInfo.swift */,
				2145B0B01F1A7B4700707730 /* Export.swift */,
				21520C521D3341FC00F53111 /* Deserializer.swift */,
				21A7A59C1D9EBF450015E3F0 /* Serializer.swift */,
				2179B6B21D911DB4007B0320 /* HelpingMapper.swift */,
				21A191FC1E222E8C00CD7609 /* Logger.swift */,
				21A192011E22332000CD7609 /* Configuration.swift */,
				210A76271E210F5B000C3DDD /* Reflection */,
				21DFCA381E18EA9E00856775 /* Transforms */,
				392627FF1DA3E1A400B0C28D /* Supporting Files */,
			);
			name = HandyJSON;
			path = Source;
			sourceTree = "<group>";
		};
		21520C5C1D3344A900F53111 /* HandyJSONDemo */ = {
			isa = PBXGroup;
			children = (
				21520C5D1D3344A900F53111 /* AppDelegate.swift */,
				21520C5F1D3344A900F53111 /* ViewController.swift */,
				21520C641D3344A900F53111 /* Assets.xcassets */,
				21520C691D3344A900F53111 /* Info.plist */,
			);
			path = HandyJSONDemo;
			sourceTree = "<group>";
		};
		217B046E1D571B9F0035A8E2 /* HandyJSONTests */ = {
			isa = PBXGroup;
			children = (
				215F13461E217B9C00B0FFEC /* BasicTypesInClass.swift */,
				215F13471E217B9C00B0FFEC /* BasicTypesInClassTestsToJSON.swift */,
				21A191F31E21E62500CD7609 /* BasicTypesInClassTestsFromJSON.swift */,
				217801EC1F5E794800EDEB38 /* BasicTypesInStruct.swift */,
				217801F01F5E7ACB00EDEB38 /* BasicTypesInStructTestsToJSON.swift */,
				217801F41F5E7CFD00EDEB38 /* BasicTypesInStructTestsFromJSON.swift */,
				21D7AD3E1F61021A008FC7A4 /* NestTypes.swift */,
				21F252A91D58BE9100B214BB /* NestTypesTest.swift */,
				217B046F1D571B9F0035A8E2 /* OtherFeaturesTest.swift */,
				217801F81F5E80A200EDEB38 /* CustomTransfromTypes.swift */,
				2144A50D1D9A7A770090F50E /* CustomTransformTest.swift */,
				21F252B31D599DE200B214BB /* InvalidStateHandlingTest.swift */,
				214329251DFBDC1400CA386A /* TestUtils.swift */,
				392628001DA3E8E100B0C28D /* Supporting Files */,
				7D9EFA7E224BA3E3002496B7 /* GenericTypes.swift */,
				7D9EFA82224BA40D002496B7 /* GenericTypesTest.swift */,
				7D912D9F2403E42D00897258 /* OCUIInheritanceTests.swift */,
				7D912DA12403E4C800897258 /* OCUIInheritanceClass.swift */,
			);
			name = HandyJSONTests;
			path = Tests/HandyJSONTests;
			sourceTree = "<group>";
		};
		21DFCA381E18EA9E00856775 /* Transforms */ = {
			isa = PBXGroup;
			children = (
				21DFCA661E18EBA600856775 /* TransformOf.swift */,
				21DFCA671E18EBA600856775 /* TransformType.swift */,
				21DFCA681E18EBA600856775 /* URLTransform.swift */,
				21DFCA691E18EBA600856775 /* EnumTransform.swift */,
				21DFCA6A1E18EBA600856775 /* NSDecimalNumberTransform.swift */,
				21DFCA6C1E18EBA600856775 /* DataTransform.swift */,
				21DFCA6D1E18EBA600856775 /* HexColorTransform.swift */,
				21DFCA391E18EAC700856775 /* Date */,
			);
			name = Transforms;
			sourceTree = "<group>";
		};
		21DFCA391E18EAC700856775 /* Date */ = {
			isa = PBXGroup;
			children = (
				21DFCA521E18EB9C00856775 /* DateTransform.swift */,
				21DFCA531E18EB9C00856775 /* DateFormatterTransform.swift */,
				21DFCA541E18EB9C00856775 /* ISO8601DateTransform.swift */,
				21DFCA551E18EB9C00856775 /* CustomDateFormatTransform.swift */,
			);
			name = Date;
			sourceTree = "<group>";
		};
		392627FF1DA3E1A400B0C28D /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				21520C491D3341B200F53111 /* Info-iOS.plist */,
				393E0FC81DA245E300B3BC12 /* Info-tvOS.plist */,
				393E100A1DA24A0600B3BC12 /* Info-macOS.plist */,
				393E0FA31DA165CE00B3BC12 /* Info-watchOS.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		392628001DA3E8E100B0C28D /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				393E0FCA1DA2481000B3BC12 /* Info-tvOS.plist */,
				217B04711D571B9F0035A8E2 /* Info-iOS.plist */,
				393E10081DA249F100B3BC12 /* Info-macOS.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		21520C411D3341B200F53111 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				21520C481D3341B200F53111 /* HandyJSON.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		393E0F981DA1650E00B3BC12 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				393E0FAB1DA165F900B3BC12 /* HandyJSON.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		393E0FAE1DA2441E00B3BC12 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				393E0FD21DA2485800B3BC12 /* HandyJSON.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		393E0FDE1DA2494F00B3BC12 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				393E0FFE1DA249D000B3BC12 /* HandyJSON.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		21520C431D3341B200F53111 /* HandyJSON iOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 21520C4C1D3341B200F53111 /* Build configuration list for PBXNativeTarget "HandyJSON iOS" */;
			buildPhases = (
				21520C3F1D3341B200F53111 /* Sources */,
				21520C401D3341B200F53111 /* Frameworks */,
				21520C411D3341B200F53111 /* Headers */,
				21520C421D3341B200F53111 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "HandyJSON iOS";
			productName = HandyJSON;
			productReference = 21520C441D3341B200F53111 /* HandyJSON.framework */;
			productType = "com.apple.product-type.framework";
		};
		21520C5A1D3344A900F53111 /* HandyJSONDemo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 21520C6A1D3344A900F53111 /* Build configuration list for PBXNativeTarget "HandyJSONDemo" */;
			buildPhases = (
				21520C571D3344A900F53111 /* Sources */,
				21520C581D3344A900F53111 /* Frameworks */,
				21520C591D3344A900F53111 /* Resources */,
				2153EB731D92162A002E41BB /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				21520C6E1D33452200F53111 /* PBXTargetDependency */,
			);
			name = HandyJSONDemo;
			productName = HandyJSONDemo;
			productReference = 21520C5B1D3344A900F53111 /* HandyJSONDemo.app */;
			productType = "com.apple.product-type.application";
		};
		217B046C1D571B9F0035A8E2 /* HandyJSON iOS Tests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 217B04751D571B9F0035A8E2 /* Build configuration list for PBXNativeTarget "HandyJSON iOS Tests" */;
			buildPhases = (
				217B04691D571B9F0035A8E2 /* Sources */,
				217B046A1D571B9F0035A8E2 /* Frameworks */,
				217B046B1D571B9F0035A8E2 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				217B04741D571B9F0035A8E2 /* PBXTargetDependency */,
				3ABCC3701EBEF2890001BDBB /* PBXTargetDependency */,
			);
			name = "HandyJSON iOS Tests";
			productName = HandyJSONTests;
			productReference = 217B046D1D571B9F0035A8E2 /* HandyJSON iOS Tests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		393E0F9A1DA1650E00B3BC12 /* HandyJSON watchOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 393E0FA01DA1650E00B3BC12 /* Build configuration list for PBXNativeTarget "HandyJSON watchOS" */;
			buildPhases = (
				393E0F961DA1650E00B3BC12 /* Sources */,
				393E0F971DA1650E00B3BC12 /* Frameworks */,
				393E0F981DA1650E00B3BC12 /* Headers */,
				393E0F991DA1650E00B3BC12 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "HandyJSON watchOS";
			productName = "HandyJSON watchOS";
			productReference = 393E0F9B1DA1650E00B3BC12 /* HandyJSON.framework */;
			productType = "com.apple.product-type.framework";
		};
		393E0FB01DA2441E00B3BC12 /* HandyJSON tvOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 393E0FC21DA2441E00B3BC12 /* Build configuration list for PBXNativeTarget "HandyJSON tvOS" */;
			buildPhases = (
				393E0FAC1DA2441E00B3BC12 /* Sources */,
				393E0FAD1DA2441E00B3BC12 /* Frameworks */,
				393E0FAE1DA2441E00B3BC12 /* Headers */,
				393E0FAF1DA2441E00B3BC12 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "HandyJSON tvOS";
			productName = "HandyJSON tvOS";
			productReference = 393E0FB11DA2441E00B3BC12 /* HandyJSON.framework */;
			productType = "com.apple.product-type.framework";
		};
		393E0FB81DA2441E00B3BC12 /* HandyJSON tvOS Tests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 393E0FC51DA2441E00B3BC12 /* Build configuration list for PBXNativeTarget "HandyJSON tvOS Tests" */;
			buildPhases = (
				393E0FB51DA2441E00B3BC12 /* Sources */,
				393E0FB61DA2441E00B3BC12 /* Frameworks */,
				393E0FB71DA2441E00B3BC12 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				393E0FBC1DA2441E00B3BC12 /* PBXTargetDependency */,
				7DAEE14523F2E9A4002C156F /* PBXTargetDependency */,
			);
			name = "HandyJSON tvOS Tests";
			productName = "HandyJSON tvOSTests";
			productReference = 393E0FB91DA2441E00B3BC12 /* HandyJSON tvOS Tests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		393E0FE01DA2494F00B3BC12 /* HandyJSON macOS */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 393E0FF21DA2494F00B3BC12 /* Build configuration list for PBXNativeTarget "HandyJSON macOS" */;
			buildPhases = (
				393E0FDC1DA2494F00B3BC12 /* Sources */,
				393E0FDD1DA2494F00B3BC12 /* Frameworks */,
				393E0FDE1DA2494F00B3BC12 /* Headers */,
				393E0FDF1DA2494F00B3BC12 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "HandyJSON macOS";
			productName = "HandyJSON macOS";
			productReference = 393E0FE11DA2494F00B3BC12 /* HandyJSON.framework */;
			productType = "com.apple.product-type.framework";
		};
		393E0FE81DA2494F00B3BC12 /* HandyJSON macOS Tests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 393E0FF51DA2494F00B3BC12 /* Build configuration list for PBXNativeTarget "HandyJSON macOS Tests" */;
			buildPhases = (
				393E0FE51DA2494F00B3BC12 /* Sources */,
				393E0FE61DA2494F00B3BC12 /* Frameworks */,
				393E0FE71DA2494F00B3BC12 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				393E0FEC1DA2494F00B3BC12 /* PBXTargetDependency */,
				7DAEE14323F2E9A0002C156F /* PBXTargetDependency */,
			);
			name = "HandyJSON macOS Tests";
			productName = "HandyJSON macOSTests";
			productReference = 393E0FE91DA2494F00B3BC12 /* HandyJSON macOS Tests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		21520C3B1D3341B200F53111 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 0800;
				LastUpgradeCheck = 1020;
				ORGANIZATIONNAME = aliyun;
				TargetAttributes = {
					21520C431D3341B200F53111 = {
						CreatedOnToolsVersion = 7.3.1;
						LastSwiftMigration = 1020;
						ProvisioningStyle = Automatic;
					};
					21520C5A1D3344A900F53111 = {
						CreatedOnToolsVersion = 7.3.1;
						DevelopmentTeam = QBMN2BBW3K;
						LastSwiftMigration = 1020;
						ProvisioningStyle = Automatic;
					};
					217B046C1D571B9F0035A8E2 = {
						CreatedOnToolsVersion = 7.3.1;
						LastSwiftMigration = 1020;
						TestTargetID = 21520C5A1D3344A900F53111;
					};
					393E0F9A1DA1650E00B3BC12 = {
						CreatedOnToolsVersion = 8.0;
						LastSwiftMigration = 1000;
						ProvisioningStyle = Automatic;
					};
					393E0FB01DA2441E00B3BC12 = {
						CreatedOnToolsVersion = 8.0;
						LastSwiftMigration = 1000;
						ProvisioningStyle = Automatic;
					};
					393E0FB81DA2441E00B3BC12 = {
						CreatedOnToolsVersion = 8.0;
						LastSwiftMigration = 1000;
						ProvisioningStyle = Automatic;
					};
					393E0FE01DA2494F00B3BC12 = {
						CreatedOnToolsVersion = 8.0;
						LastSwiftMigration = 1000;
						ProvisioningStyle = Automatic;
					};
					393E0FE81DA2494F00B3BC12 = {
						CreatedOnToolsVersion = 8.0;
						LastSwiftMigration = 1000;
						ProvisioningStyle = Manual;
					};
				};
			};
			buildConfigurationList = 21520C3E1D3341B200F53111 /* Build configuration list for PBXProject "HandyJSON" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
				Base,
			);
			mainGroup = 21520C3A1D3341B200F53111;
			productRefGroup = 21520C451D3341B200F53111 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				21520C431D3341B200F53111 /* HandyJSON iOS */,
				21520C5A1D3344A900F53111 /* HandyJSONDemo */,
				217B046C1D571B9F0035A8E2 /* HandyJSON iOS Tests */,
				393E0F9A1DA1650E00B3BC12 /* HandyJSON watchOS */,
				393E0FB01DA2441E00B3BC12 /* HandyJSON tvOS */,
				393E0FB81DA2441E00B3BC12 /* HandyJSON tvOS Tests */,
				393E0FE01DA2494F00B3BC12 /* HandyJSON macOS */,
				393E0FE81DA2494F00B3BC12 /* HandyJSON macOS Tests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		21520C421D3341B200F53111 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				215F132E1E2179E700B0FFEC /* LICENSE in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		21520C591D3344A900F53111 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				21520C651D3344A900F53111 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		217B046B1D571B9F0035A8E2 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		393E0F991DA1650E00B3BC12 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				215F132F1E2179E700B0FFEC /* LICENSE in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		393E0FAF1DA2441E00B3BC12 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				215F13301E2179E700B0FFEC /* LICENSE in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		393E0FB71DA2441E00B3BC12 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		393E0FDF1DA2494F00B3BC12 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				215F13311E2179E700B0FFEC /* LICENSE in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		393E0FE71DA2494F00B3BC12 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		21520C3F1D3341B200F53111 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				7D2813E722058D060044E052 /* MangledName.swift in Sources */,
				210A76441E210FBB000C3DDD /* AnyExtensions.swift in Sources */,
				21DFCA6E1E18EBA600856775 /* TransformOf.swift in Sources */,
				2145B0B31F1A7DE700707730 /* ExtendCustomModelType.swift in Sources */,
				217801E81F5E60C900EDEB38 /* ExtendCustomBasicType.swift in Sources */,
				210A76741E210FBB000C3DDD /* ContextDescriptorType.swift in Sources */,
				2145B0A91F1A375F00707730 /* Measuable.swift in Sources */,
				21DFCA621E18EB9C00856775 /* CustomDateFormatTransform.swift in Sources */,
				2145B0B11F1A7B4700707730 /* Export.swift in Sources */,
				216652021F49CA5B003B7F9A /* PropertyInfo.swift in Sources */,
				210A767C1E210FBB000C3DDD /* PointerType.swift in Sources */,
				21DFCA561E18EB9C00856775 /* DateTransform.swift in Sources */,
				21356CEE1F1A841C0094A4F5 /* EnumType.swift in Sources */,
				21520C551D3341FC00F53111 /* BuiltInBasicType.swift in Sources */,
				21DFCA7E1E18EBA600856775 /* NSDecimalNumberTransform.swift in Sources */,
				7DCD448220FB593600370EF4 /* CBridge.swift in Sources */,
				21DFCA721E18EBA600856775 /* TransformType.swift in Sources */,
				2145B0AF1F1A3F1400707730 /* BuiltInBridgeType.swift in Sources */,
				21DFCA8A1E18EBA600856775 /* HexColorTransform.swift in Sources */,
				21DFCA7A1E18EBA600856775 /* EnumTransform.swift in Sources */,
				21A7A59D1D9EBF450015E3F0 /* Serializer.swift in Sources */,
				210A765C1E210FBB000C3DDD /* Metadata.swift in Sources */,
				21DFCA861E18EBA600856775 /* DataTransform.swift in Sources */,
				21520C561D3341FC00F53111 /* Deserializer.swift in Sources */,
				210A76A61E211331000C3DDD /* ReflectionHelper.swift in Sources */,
				215F13291E2175C500B0FFEC /* OtherExtension.swift in Sources */,
				21DFCA5A1E18EB9C00856775 /* DateFormatterTransform.swift in Sources */,
				21DFCA5E1E18EB9C00856775 /* ISO8601DateTransform.swift in Sources */,
				21A192021E22332000CD7609 /* Configuration.swift in Sources */,
				21A191FD1E222E8C00CD7609 /* Logger.swift in Sources */,
				7D460F662203511D0027428E /* FieldDescriptor.swift in Sources */,
				210A76801E210FBB000C3DDD /* Properties.swift in Sources */,
				2179B6B31D911DB4007B0320 /* HelpingMapper.swift in Sources */,
				2145B0AB1F1A3AF600707730 /* Transformable.swift in Sources */,
				21DFCA761E18EBA600856775 /* URLTransform.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		21520C571D3344A900F53111 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				21520C601D3344A900F53111 /* ViewController.swift in Sources */,
				21520C5E1D3344A900F53111 /* AppDelegate.swift in Sources */,
				7DCD448320FB593600370EF4 /* CBridge.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		217B04691D571B9F0035A8E2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				7D9EFA7F224BA3E3002496B7 /* GenericTypes.swift in Sources */,
				217801F91F5E80A200EDEB38 /* CustomTransfromTypes.swift in Sources */,
				217801F51F5E7CFD00EDEB38 /* BasicTypesInStructTestsFromJSON.swift in Sources */,
				7D9EFA83224BA40D002496B7 /* GenericTypesTest.swift in Sources */,
				21A191F41E21E62500CD7609 /* BasicTypesInClassTestsFromJSON.swift in Sources */,
				215F13481E217B9C00B0FFEC /* BasicTypesInClass.swift in Sources */,
				215F13491E217B9C00B0FFEC /* BasicTypesInClassTestsToJSON.swift in Sources */,
				7D912DA02403E42D00897258 /* OCUIInheritanceTests.swift in Sources */,
				21F252B41D599DE200B214BB /* InvalidStateHandlingTest.swift in Sources */,
				2144A50E1D9A7A770090F50E /* CustomTransformTest.swift in Sources */,
				214329261DFBDC1400CA386A /* TestUtils.swift in Sources */,
				7D912DA22403E4C800897258 /* OCUIInheritanceClass.swift in Sources */,
				217B04701D571B9F0035A8E2 /* OtherFeaturesTest.swift in Sources */,
				217801ED1F5E794800EDEB38 /* BasicTypesInStruct.swift in Sources */,
				217801F11F5E7ACB00EDEB38 /* BasicTypesInStructTestsToJSON.swift in Sources */,
				21F252AA1D58BE9100B214BB /* NestTypesTest.swift in Sources */,
				7DCD448420FB593600370EF4 /* CBridge.swift in Sources */,
				21D7AD3F1F61021A008FC7A4 /* NestTypes.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		393E0F961DA1650E00B3BC12 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				7D2813E822058D060044E052 /* MangledName.swift in Sources */,
				21D7AD3D1F610197008FC7A4 /* PropertyInfo.swift in Sources */,
				21356CDA1F1A82220094A4F5 /* Measuable.swift in Sources */,
				21356CDB1F1A82220094A4F5 /* Transformable.swift in Sources */,
				21356CDC1F1A82230094A4F5 /* BuiltInBridgeType.swift in Sources */,
				21356CDD1F1A82230094A4F5 /* ExtendCustomModelType.swift in Sources */,
				21356CDE1F1A82230094A4F5 /* Export.swift in Sources */,
				210A76451E210FBB000C3DDD /* AnyExtensions.swift in Sources */,
				21DFCA6F1E18EBA600856775 /* TransformOf.swift in Sources */,
				210A76751E210FBB000C3DDD /* ContextDescriptorType.swift in Sources */,
				21DFCA631E18EB9C00856775 /* CustomDateFormatTransform.swift in Sources */,
				210A767D1E210FBB000C3DDD /* PointerType.swift in Sources */,
				21356CEF1F1A841C0094A4F5 /* EnumType.swift in Sources */,
				21DFCA571E18EB9C00856775 /* DateTransform.swift in Sources */,
				21DFCA7F1E18EBA600856775 /* NSDecimalNumberTransform.swift in Sources */,
				7DCD448520FB593600370EF4 /* CBridge.swift in Sources */,
				21DFCA731E18EBA600856775 /* TransformType.swift in Sources */,
				21DFCA8B1E18EBA600856775 /* HexColorTransform.swift in Sources */,
				21DFCA7B1E18EBA600856775 /* EnumTransform.swift in Sources */,
				210A765D1E210FBB000C3DDD /* Metadata.swift in Sources */,
				21DFCA871E18EBA600856775 /* DataTransform.swift in Sources */,
				393E0FA71DA165E300B3BC12 /* HelpingMapper.swift in Sources */,
				210A76A71E211331000C3DDD /* ReflectionHelper.swift in Sources */,
				215F132A1E2175C500B0FFEC /* OtherExtension.swift in Sources */,
				393E0FA81DA165E300B3BC12 /* BuiltInBasicType.swift in Sources */,
				21DFCA5B1E18EB9C00856775 /* DateFormatterTransform.swift in Sources */,
				21DFCA5F1E18EB9C00856775 /* ISO8601DateTransform.swift in Sources */,
				21A192031E22332000CD7609 /* Configuration.swift in Sources */,
				21A191FE1E222E8C00CD7609 /* Logger.swift in Sources */,
				210A76811E210FBB000C3DDD /* Properties.swift in Sources */,
				7D460F672203511D0027428E /* FieldDescriptor.swift in Sources */,
				393E0FA91DA165E300B3BC12 /* Deserializer.swift in Sources */,
				217801E91F5E60C900EDEB38 /* ExtendCustomBasicType.swift in Sources */,
				393E0FAA1DA165E300B3BC12 /* Serializer.swift in Sources */,
				21DFCA771E18EBA600856775 /* URLTransform.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		393E0FAC1DA2441E00B3BC12 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				7D2813E922058D060044E052 /* MangledName.swift in Sources */,
				21D7AD3C1F610192008FC7A4 /* PropertyInfo.swift in Sources */,
				21356CE21F1A82440094A4F5 /* Measuable.swift in Sources */,
				21356CE31F1A82440094A4F5 /* Transformable.swift in Sources */,
				21356CE41F1A82440094A4F5 /* BuiltInBridgeType.swift in Sources */,
				21356CE51F1A82440094A4F5 /* ExtendCustomModelType.swift in Sources */,
				21356CE61F1A82440094A4F5 /* Export.swift in Sources */,
				210A76461E210FBB000C3DDD /* AnyExtensions.swift in Sources */,
				21DFCA701E18EBA600856775 /* TransformOf.swift in Sources */,
				210A76761E210FBB000C3DDD /* ContextDescriptorType.swift in Sources */,
				21DFCA641E18EB9C00856775 /* CustomDateFormatTransform.swift in Sources */,
				210A767E1E210FBB000C3DDD /* PointerType.swift in Sources */,
				21356CF01F1A841C0094A4F5 /* EnumType.swift in Sources */,
				21DFCA581E18EB9C00856775 /* DateTransform.swift in Sources */,
				21DFCA801E18EBA600856775 /* NSDecimalNumberTransform.swift in Sources */,
				7DCD448620FB593600370EF4 /* CBridge.swift in Sources */,
				21DFCA741E18EBA600856775 /* TransformType.swift in Sources */,
				21DFCA8C1E18EBA600856775 /* HexColorTransform.swift in Sources */,
				21DFCA7C1E18EBA600856775 /* EnumTransform.swift in Sources */,
				210A765E1E210FBB000C3DDD /* Metadata.swift in Sources */,
				21DFCA881E18EBA600856775 /* DataTransform.swift in Sources */,
				393E0FCE1DA2484F00B3BC12 /* HelpingMapper.swift in Sources */,
				210A76A81E211331000C3DDD /* ReflectionHelper.swift in Sources */,
				215F132B1E2175C500B0FFEC /* OtherExtension.swift in Sources */,
				393E0FCF1DA2484F00B3BC12 /* BuiltInBasicType.swift in Sources */,
				21DFCA5C1E18EB9C00856775 /* DateFormatterTransform.swift in Sources */,
				21DFCA601E18EB9C00856775 /* ISO8601DateTransform.swift in Sources */,
				21A192041E22332000CD7609 /* Configuration.swift in Sources */,
				21A191FF1E222E8C00CD7609 /* Logger.swift in Sources */,
				210A76821E210FBB000C3DDD /* Properties.swift in Sources */,
				7D460F682203511D0027428E /* FieldDescriptor.swift in Sources */,
				393E0FD01DA2484F00B3BC12 /* Deserializer.swift in Sources */,
				217801EA1F5E60C900EDEB38 /* ExtendCustomBasicType.swift in Sources */,
				393E0FD11DA2484F00B3BC12 /* Serializer.swift in Sources */,
				21DFCA781E18EBA600856775 /* URLTransform.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		393E0FB51DA2441E00B3BC12 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				7D9EFA80224BA3E3002496B7 /* GenericTypes.swift in Sources */,
				217801FA1F5E80A200EDEB38 /* CustomTransfromTypes.swift in Sources */,
				217801F61F5E7CFD00EDEB38 /* BasicTypesInStructTestsFromJSON.swift in Sources */,
				7D9EFA84224BA40D002496B7 /* GenericTypesTest.swift in Sources */,
				21A192081E223F9000CD7609 /* BasicTypesInClass.swift in Sources */,
				21A192091E223F9000CD7609 /* BasicTypesInClassTestsToJSON.swift in Sources */,
				21A191F51E21E62E00CD7609 /* BasicTypesInClassTestsFromJSON.swift in Sources */,
				393E0FD31DA2486C00B3BC12 /* OtherFeaturesTest.swift in Sources */,
				214329271DFBDC1400CA386A /* TestUtils.swift in Sources */,
				393E0FD51DA2486C00B3BC12 /* NestTypesTest.swift in Sources */,
				217801EE1F5E794800EDEB38 /* BasicTypesInStruct.swift in Sources */,
				393E0FD61DA2486C00B3BC12 /* CustomTransformTest.swift in Sources */,
				217801F21F5E7ACB00EDEB38 /* BasicTypesInStructTestsToJSON.swift in Sources */,
				393E0FDA1DA2486C00B3BC12 /* InvalidStateHandlingTest.swift in Sources */,
				7DCD448720FB593600370EF4 /* CBridge.swift in Sources */,
				21D7AD401F61021A008FC7A4 /* NestTypes.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		393E0FDC1DA2494F00B3BC12 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				7D2813EA22058D060044E052 /* MangledName.swift in Sources */,
				21D7AD3B1F61018C008FC7A4 /* PropertyInfo.swift in Sources */,
				21356CE81F1A824B0094A4F5 /* Measuable.swift in Sources */,
				21356CE91F1A824B0094A4F5 /* Transformable.swift in Sources */,
				21356CEA1F1A824B0094A4F5 /* BuiltInBridgeType.swift in Sources */,
				21356CEB1F1A824B0094A4F5 /* ExtendCustomModelType.swift in Sources */,
				21356CEC1F1A824B0094A4F5 /* Export.swift in Sources */,
				210A76471E210FBB000C3DDD /* AnyExtensions.swift in Sources */,
				21DFCA711E18EBA600856775 /* TransformOf.swift in Sources */,
				210A76771E210FBB000C3DDD /* ContextDescriptorType.swift in Sources */,
				21DFCA651E18EB9C00856775 /* CustomDateFormatTransform.swift in Sources */,
				210A767F1E210FBB000C3DDD /* PointerType.swift in Sources */,
				21356CF11F1A841C0094A4F5 /* EnumType.swift in Sources */,
				21DFCA591E18EB9C00856775 /* DateTransform.swift in Sources */,
				21DFCA811E18EBA600856775 /* NSDecimalNumberTransform.swift in Sources */,
				7DCD448820FB593600370EF4 /* CBridge.swift in Sources */,
				21DFCA751E18EBA600856775 /* TransformType.swift in Sources */,
				21DFCA8D1E18EBA600856775 /* HexColorTransform.swift in Sources */,
				21DFCA7D1E18EBA600856775 /* EnumTransform.swift in Sources */,
				210A765F1E210FBB000C3DDD /* Metadata.swift in Sources */,
				21DFCA891E18EBA600856775 /* DataTransform.swift in Sources */,
				393E0FFA1DA2495900B3BC12 /* HelpingMapper.swift in Sources */,
				210A76A91E211331000C3DDD /* ReflectionHelper.swift in Sources */,
				215F132C1E2175C500B0FFEC /* OtherExtension.swift in Sources */,
				393E0FFB1DA2495900B3BC12 /* BuiltInBasicType.swift in Sources */,
				21DFCA5D1E18EB9C00856775 /* DateFormatterTransform.swift in Sources */,
				21DFCA611E18EB9C00856775 /* ISO8601DateTransform.swift in Sources */,
				21A192051E22332000CD7609 /* Configuration.swift in Sources */,
				21A192001E222E8C00CD7609 /* Logger.swift in Sources */,
				210A76831E210FBB000C3DDD /* Properties.swift in Sources */,
				7D460F692203511D0027428E /* FieldDescriptor.swift in Sources */,
				393E0FFC1DA2495900B3BC12 /* Deserializer.swift in Sources */,
				217801EB1F5E60C900EDEB38 /* ExtendCustomBasicType.swift in Sources */,
				393E0FFD1DA2495900B3BC12 /* Serializer.swift in Sources */,
				21DFCA791E18EBA600856775 /* URLTransform.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		393E0FE51DA2494F00B3BC12 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				7D9EFA81224BA3E3002496B7 /* GenericTypes.swift in Sources */,
				217801FB1F5E80A200EDEB38 /* CustomTransfromTypes.swift in Sources */,
				217801F71F5E7CFD00EDEB38 /* BasicTypesInStructTestsFromJSON.swift in Sources */,
				7D9EFA85224BA40D002496B7 /* GenericTypesTest.swift in Sources */,
				21A192061E223F8900CD7609 /* BasicTypesInClass.swift in Sources */,
				21A192071E223F8900CD7609 /* BasicTypesInClassTestsToJSON.swift in Sources */,
				21A191F61E21E63100CD7609 /* BasicTypesInClassTestsFromJSON.swift in Sources */,
				393E0FFF1DA249DC00B3BC12 /* OtherFeaturesTest.swift in Sources */,
				214329281DFBDC1400CA386A /* TestUtils.swift in Sources */,
				393E10011DA249DC00B3BC12 /* NestTypesTest.swift in Sources */,
				217801EF1F5E794800EDEB38 /* BasicTypesInStruct.swift in Sources */,
				393E10021DA249DC00B3BC12 /* CustomTransformTest.swift in Sources */,
				217801F31F5E7ACB00EDEB38 /* BasicTypesInStructTestsToJSON.swift in Sources */,
				393E10061DA249DC00B3BC12 /* InvalidStateHandlingTest.swift in Sources */,
				7DCD448920FB593600370EF4 /* CBridge.swift in Sources */,
				21D7AD411F61021A008FC7A4 /* NestTypes.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		21520C6E1D33452200F53111 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 21520C431D3341B200F53111 /* HandyJSON iOS */;
			targetProxy = 21520C6D1D33452200F53111 /* PBXContainerItemProxy */;
		};
		217B04741D571B9F0035A8E2 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 21520C431D3341B200F53111 /* HandyJSON iOS */;
			targetProxy = 217B04731D571B9F0035A8E2 /* PBXContainerItemProxy */;
		};
		393E0FBC1DA2441E00B3BC12 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 393E0FB01DA2441E00B3BC12 /* HandyJSON tvOS */;
			targetProxy = 393E0FBB1DA2441E00B3BC12 /* PBXContainerItemProxy */;
		};
		393E0FEC1DA2494F00B3BC12 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 393E0FE01DA2494F00B3BC12 /* HandyJSON macOS */;
			targetProxy = 393E0FEB1DA2494F00B3BC12 /* PBXContainerItemProxy */;
		};
		3ABCC3701EBEF2890001BDBB /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 21520C5A1D3344A900F53111 /* HandyJSONDemo */;
			targetProxy = 3ABCC36F1EBEF2890001BDBB /* PBXContainerItemProxy */;
		};
		7DAEE14323F2E9A0002C156F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 21520C5A1D3344A900F53111 /* HandyJSONDemo */;
			targetProxy = 7DAEE14223F2E9A0002C156F /* PBXContainerItemProxy */;
		};
		7DAEE14523F2E9A4002C156F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 21520C5A1D3344A900F53111 /* HandyJSONDemo */;
			targetProxy = 7DAEE14423F2E9A4002C156F /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		21520C4A1D3341B200F53111 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		21520C4B1D3341B200F53111 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SWIFT_OPTIMIZATION_LEVEL = "-Owholemodule";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		21520C4D1D3341B200F53111 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer: 业灿 许 (U997QZ92HC)";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = "";
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = "$(SRCROOT)/Source/Info-iOS.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = com.aliyun.app.HandyJSON;
				PRODUCT_NAME = "$(PROJECT_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		21520C4E1D3341B200F53111 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer: 业灿 许 (U997QZ92HC)";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "";
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = "";
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = "$(SRCROOT)/Source/Info-iOS.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = com.aliyun.app.HandyJSON;
				PRODUCT_NAME = "$(PROJECT_NAME)";
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		21520C6B1D3344A900F53111 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_IDENTITY = "iPhone Developer: 业灿 许 (U997QZ92HC)";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				DEVELOPMENT_TEAM = QBMN2BBW3K;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = HandyJSONDemo/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = com.aliyun.app.HandyJSONDemo;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		21520C6C1D3344A900F53111 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CODE_SIGN_IDENTITY = "iPhone Developer: 业灿 许 (U997QZ92HC)";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				DEVELOPMENT_TEAM = QBMN2BBW3K;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = HandyJSONDemo/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = com.aliyun.app.HandyJSONDemo;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		217B04761D571B9F0035A8E2 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = "Tests/HandyJSONTests/Info-iOS.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = com.aliyun.app.HandyJSONTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/HandyJSONDemo.app/HandyJSONDemo";
			};
			name = Debug;
		};
		217B04771D571B9F0035A8E2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = "Tests/HandyJSONTests/Info-iOS.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = com.aliyun.app.HandyJSONTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/HandyJSONDemo.app/HandyJSONDemo";
			};
			name = Release;
		};
		393E0FA11DA1650E00B3BC12 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				APPLICATION_EXTENSION_API_ONLY = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_SUSPICIOUS_MOVES = YES;
				CODE_SIGN_IDENTITY = "";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				INFOPLIST_FILE = "$(SRCROOT)/Source/Info-watchOS.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = "alibaba.HandyJSON-watchOS";
				PRODUCT_NAME = "$(PROJECT_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 2.0;
			};
			name = Debug;
		};
		393E0FA21DA1650E00B3BC12 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				APPLICATION_EXTENSION_API_ONLY = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_SUSPICIOUS_MOVES = YES;
				CODE_SIGN_IDENTITY = "";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				INFOPLIST_FILE = "$(SRCROOT)/Source/Info-watchOS.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = "alibaba.HandyJSON-watchOS";
				PRODUCT_NAME = "$(PROJECT_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 2.0;
			};
			name = Release;
		};
		393E0FC31DA2441E00B3BC12 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_SUSPICIOUS_MOVES = YES;
				CODE_SIGN_IDENTITY = "";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				INFOPLIST_FILE = "$(SRCROOT)/Source/Info-tvOS.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = "alibaba.HandyJSON-tvOS";
				PRODUCT_NAME = "$(PROJECT_NAME)";
				SDKROOT = appletvos;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 3;
				TVOS_DEPLOYMENT_TARGET = 9.0;
			};
			name = Debug;
		};
		393E0FC41DA2441E00B3BC12 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_SUSPICIOUS_MOVES = YES;
				CODE_SIGN_IDENTITY = "";
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				INFOPLIST_FILE = "$(SRCROOT)/Source/Info-tvOS.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = "alibaba.HandyJSON-tvOS";
				PRODUCT_NAME = "$(PROJECT_NAME)";
				SDKROOT = appletvos;
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 3;
				TVOS_DEPLOYMENT_TARGET = 9.0;
			};
			name = Release;
		};
		393E0FC61DA2441E00B3BC12 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_SUSPICIOUS_MOVES = YES;
				INFOPLIST_FILE = "Tests/HandyJSONTests/Info-tvOS.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = "alibaba.HandyJSON-tvOSTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_VERSION = 5.0;
				TVOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = Debug;
		};
		393E0FC71DA2441E00B3BC12 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_SUSPICIOUS_MOVES = YES;
				INFOPLIST_FILE = "Tests/HandyJSONTests/Info-tvOS.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = "alibaba.HandyJSON-tvOSTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = appletvos;
				SWIFT_VERSION = 5.0;
				TVOS_DEPLOYMENT_TARGET = 10.0;
			};
			name = Release;
		};
		393E0FF31DA2494F00B3BC12 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_SUSPICIOUS_MOVES = YES;
				CODE_SIGN_IDENTITY = "";
				COMBINE_HIDPI_IMAGES = YES;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_VERSION = A;
				INFOPLIST_FILE = "$(SRCROOT)/Source/Info-macOS.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/../Frameworks @loader_path/Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 10.9;
				PRODUCT_BUNDLE_IDENTIFIER = "alibaba.HandyJSON-macOS";
				PRODUCT_NAME = "$(PROJECT_NAME)";
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		393E0FF41DA2494F00B3BC12 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_SUSPICIOUS_MOVES = YES;
				CODE_SIGN_IDENTITY = "-";
				COMBINE_HIDPI_IMAGES = YES;
				DEFINES_MODULE = YES;
				DEVELOPMENT_TEAM = "";
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				FRAMEWORK_VERSION = A;
				INFOPLIST_FILE = "$(SRCROOT)/Source/Info-macOS.plist";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/../Frameworks @loader_path/Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 10.9;
				PRODUCT_BUNDLE_IDENTIFIER = "alibaba.HandyJSON-macOS";
				PRODUCT_NAME = "$(PROJECT_NAME)";
				SDKROOT = macosx;
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		393E0FF61DA2494F00B3BC12 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_SUSPICIOUS_MOVES = YES;
				CODE_SIGN_IDENTITY = "-";
				COMBINE_HIDPI_IMAGES = YES;
				INFOPLIST_FILE = "Tests/HandyJSONTests/Info-macOS.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/../Frameworks @loader_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 10.11;
				PRODUCT_BUNDLE_IDENTIFIER = "alibaba.HandyJSON-macOSTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		393E0FF71DA2494F00B3BC12 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_SUSPICIOUS_MOVES = YES;
				CODE_SIGN_IDENTITY = "-";
				COMBINE_HIDPI_IMAGES = YES;
				INFOPLIST_FILE = "Tests/HandyJSONTests/Info-macOS.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/../Frameworks @loader_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 10.11;
				PRODUCT_BUNDLE_IDENTIFIER = "alibaba.HandyJSON-macOSTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = macosx;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		21520C3E1D3341B200F53111 /* Build configuration list for PBXProject "HandyJSON" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				21520C4A1D3341B200F53111 /* Debug */,
				21520C4B1D3341B200F53111 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		21520C4C1D3341B200F53111 /* Build configuration list for PBXNativeTarget "HandyJSON iOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				21520C4D1D3341B200F53111 /* Debug */,
				21520C4E1D3341B200F53111 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		21520C6A1D3344A900F53111 /* Build configuration list for PBXNativeTarget "HandyJSONDemo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				21520C6B1D3344A900F53111 /* Debug */,
				21520C6C1D3344A900F53111 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		217B04751D571B9F0035A8E2 /* Build configuration list for PBXNativeTarget "HandyJSON iOS Tests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				217B04761D571B9F0035A8E2 /* Debug */,
				217B04771D571B9F0035A8E2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		393E0FA01DA1650E00B3BC12 /* Build configuration list for PBXNativeTarget "HandyJSON watchOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				393E0FA11DA1650E00B3BC12 /* Debug */,
				393E0FA21DA1650E00B3BC12 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		393E0FC21DA2441E00B3BC12 /* Build configuration list for PBXNativeTarget "HandyJSON tvOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				393E0FC31DA2441E00B3BC12 /* Debug */,
				393E0FC41DA2441E00B3BC12 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		393E0FC51DA2441E00B3BC12 /* Build configuration list for PBXNativeTarget "HandyJSON tvOS Tests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				393E0FC61DA2441E00B3BC12 /* Debug */,
				393E0FC71DA2441E00B3BC12 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		393E0FF21DA2494F00B3BC12 /* Build configuration list for PBXNativeTarget "HandyJSON macOS" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				393E0FF31DA2494F00B3BC12 /* Debug */,
				393E0FF41DA2494F00B3BC12 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		393E0FF51DA2494F00B3BC12 /* Build configuration list for PBXNativeTarget "HandyJSON macOS Tests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				393E0FF61DA2494F00B3BC12 /* Debug */,
				393E0FF71DA2494F00B3BC12 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 21520C3B1D3341B200F53111 /* Project object */;
}
