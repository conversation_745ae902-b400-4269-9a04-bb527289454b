import { NgModule } from '@angular/core';
import { PreloadAllModules, RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'login',
    pathMatch: 'full',
  },
  {
    path: 'login',
    loadChildren: () => import('./login/login.module').then((m) => m.LoginPageModule),
  },
  {
    path: 'tabs',
    loadChildren: () => import('./tabs/tabs.module').then((m) => m.TabsPageModule),
  },
  {
    path: 'mypage',
    loadChildren: () => import('./mypage/mypage.module').then((m) => m.MypagePageModule),
  },
  {
    path: 'asset-type',
    loadChildren: () => import('./asset-type/asset-type.module').then((m) => m.AssetTypePageModule),
  },
  {
    path: 'asset-list',
    loadChildren: () => import('./assets-list/assets-list.module').then((m) => m.AssetsListPageModule),
  },
  {
    path: 'password-reset',
    loadChildren: () => import('./login/password-reset/password-reset.module').then((m) => m.PasswordResetModule),
  },
  //MFA
  {
    path: 'login/otp',
    loadChildren: () => import('./login/otp/otp.module').then((m) => m.OtpPageModule),
  },
  {
    path: 'login/otp-first',
    loadChildren: () => import('./login/otp-first/otp-first.module').then((m) => m.OtpFirstPageModule),
  },

  //マルチテナント
  {
    path: 'login/tenant',
    loadChildren: () => import('./login/tenant/tenant.module').then((m) => m.TenantPageModule),
  },
  // 可能没有地方调用，需要测试
  {
    path: 'login/tenant-otp',
    loadChildren: () => import('./login/tenant-otp/tenant-otp.module').then((m) => m.TenantOtpPageModule),
  },
  // 可能没有地方调用，需要测试
  {
    path: 'login/tenant-otp-first',
    loadChildren: () =>
      import('./login/tenant-otp-first/tenant-otp-first.module').then((m) => m.TenantOtpFirstPageModule),
  },

  // 資産
  {
    path: 'asset/detail',
    loadChildren: () => import('./asset/detail/detail.module').then((m) => m.DetailPageModule),
  },
  {
    path: 'asset/detail/:level',
    loadChildren: () => import('./asset/detail/detail.module').then((m) => m.DetailPageModule),
  },
  {
    path: 'asset/schedule',
    loadChildren: () => import('./asset/schedule/schedule.module').then((m) => m.SchedulePageModule),
  },
  {
    path: 'asset/relate',
    loadChildren: () => import('./asset/relate/relate.module').then((m) => m.RelatePageModule),
  },
  {
    path: 'asset/relate/:level',
    loadChildren: () => import('./asset/relate/relate.module').then((m) => m.RelatePageModule),
  },
  // 関連資産
  {
    path: 'asset/relation-list',
    loadChildren: () => import('./asset/relation-list/relation-list.module').then((m) => m.RelationListPageModule),
  },
  {
    path: 'asset/relation-list/:level',
    loadChildren: () => import('./asset/relation-list/relation-list.module').then((m) => m.RelationListPageModule),
  },
  {
    path: 'asset/relation-add',
    loadChildren: () => import('./asset/relation-add/relation-add.module').then((m) => m.RelationAddPageModule),
  },
  {
    path: 'asset/relation-add/:level',
    loadChildren: () => import('./asset/relation-add/relation-add.module').then((m) => m.RelationAddPageModule),
  },
  // 登録（登録済み）
  {
    path: 'add/registered/list',
    loadChildren: () => import('./add/registered/list/list.module').then((m) => m.ListPageModule),
  },
  // 登録（未登録）
  {
    path: 'add/new/list',
    loadChildren: () => import('./add/new/list/list.module').then((m) => m.ListPageModule),
  },
  {
    path: 'add/new/select',
    loadChildren: () => import('./add/new/select/select.module').then((m) => m.SelectPageModule),
  },
  {
    path: 'add/new/edit',
    loadChildren: () => import('./add/new/edit/edit.module').then((m) => m.EditPageModule),
  },
  // action 処理一覧
  {
    path: 'action/list/list',
    loadChildren: () => import('./action/list/list/list.module').then((m) => m.ListPageModule),
  },
  {
    path: 'action/list-input-helper',
    loadChildren: () =>
      import('./action/list-input-helper/list-input-helper.module').then((m) => m.ListInputHelperModule),
  },
  {
    path: 'action/list/detail',
    loadChildren: () => import('./action/list/detail/detail.module').then((m) => m.DetailPageModule),
  },
  {
    path: 'action/list/preview-inside',
    loadChildren: () =>
      import('./action/list/preview-inside/preview-inside.module').then((m) => m.PreviewInsidePageModule),
  },
  {
    path: 'action/master',
    loadChildren: () => import('./action/master/master/master.module').then((m) => m.MasterPageModule),
  },
  //マスタ
  {
    //master-select
    path: 'master-info/master-select',
    loadChildren: () =>
      import('./edit-item/master-info/master-select/master-select.module').then((m) => m.Level1PageModule),
  },
  // action 一時保存
  {
    path: 'action/progress/list',
    loadChildren: () => import('./action/progress/list/list.module').then((m) => m.ListPageModule),
  },
  {
    path: 'action/progress/detail',
    loadChildren: () => import('./action/progress/detail/detail.module').then((m) => m.DetailPageModule),
  },
  // action 実行完了
  {
    path: 'action/complete/list',
    loadChildren: () => import('./action/complete/list/list.module').then((m) => m.ListPageModule),
  },
  {
    path: 'action/complete/detail',
    loadChildren: () => import('./action/complete/detail/detail.module').then((m) => m.DetailPageModule),
  },

  // workflow new
  {
    path: 'workflow/new',
    loadChildren: () => import('./workflow/new/new.module').then((m) => m.NewPageModule),
  },
  {
    path: 'workflow/application',
    loadChildren: () => import('./workflow/application/application.module').then((m) => m.ApplicationPageModule),
  },
  {
    path: 'workflow/approval',
    loadChildren: () => import('./workflow/approval/approval.module').then((m) => m.ApprovalPageModule),
  },
  {
    path: 'workflow/new/w1/form',
    loadChildren: () => import('./workflow/new/w1/form/form.module').then((m) => m.FormPageModule),
  },
  {
    path: 'workflow/new/w1-asset-list/form',
    loadChildren: () => import('./workflow/new/w1-asset-list/form/form.module').then((m) => m.FormPageModule),
  },
  {
    path: 'workflow/new/w1-asset-list/scan-list',
    loadChildren: () =>
      import('./workflow/new/w1-asset-list/scan-list/scan-list.module').then((m) => m.ScanListPageModule),
  },
  {
    path: 'workflow/new/w1-asset-list/new-scan-list',
    loadChildren: () =>
      import('./workflow/new/w1-asset-list/new-scan-list/new-scan-list.module').then((m) => m.NewScanListPageModule),
  },
  {
    // NOTICE 不可删除
    path: 'workflow/new/w1-asset-list/new-scan-list/copy',
    loadChildren: () =>
      import('./workflow/new/w1-asset-list/new-scan-list/new-scan-list.module').then((m) => m.NewScanListPageModule),
  },
  {
    // 没有用到这个路由
    path: 'workflow/new/w1-asset-list/scan-list-preview',
    loadChildren: () =>
      import('./workflow/new/w1-asset-list/scan-list/scan-list.module').then((m) => m.ScanListPageModule),
  },
  {
    path: 'workflow/new/w1-asset-list/submit',
    loadChildren: () => import('./workflow/new/w1-asset-list/submit/submit.module').then((m) => m.SubmitPageModule),
  },
  {
    path: 'workflow/new/w2/form',
    loadChildren: () => import('./workflow/new/w2/form/form.module').then((m) => m.FormPageModule),
  },
  {
    path: 'workflow/new/w2/scan-list',
    loadChildren: () => import('./workflow/new/w2/scan-list/scan-list.module').then((m) => m.ScanListPageModule),
  },
  {
    path: 'workflow/new/w2/submit',
    loadChildren: () => import('./workflow/new/w2/submit/submit.module').then((m) => m.SubmitPageModule),
  },
  {
    path: 'workflow/new/w3/form',
    loadChildren: () => import('./workflow/new/w3/form/form.module').then((m) => m.FormPageModule),
  },
  {
    path: 'workflow/new/w3/scan',
    loadChildren: () => import('./workflow/new/w3/scan/scan.module').then((m) => m.ScanPageModule),
  },
  {
    path: 'workflow/new/w3/scan-list',
    loadChildren: () => import('./workflow/new/w3/scan-list/scan-list.module').then((m) => m.ScanListPageModule),
  },
  {
    path: 'workflow/new/w3/submit',
    loadChildren: () => import('./workflow/new/w3/submit/submit.module').then((m) => m.SubmitPageModule),
  },

  // workflow application
  {
    path: 'workflow/application/w1/view',
    loadChildren: () => import('./workflow/application/w1/view/view.module').then((m) => m.ViewPageModule),
  },
  {
    path: 'workflow/application/w1/form',
    loadChildren: () => import('./workflow/application/w1/form/form.module').then((m) => m.FormPageModule),
  },
  {
    path: 'workflow/application/w1-asset-list/view',
    loadChildren: () => import('./workflow/application/w1-asset-list/view/view.module').then((m) => m.ViewPageModule),
  },
  {
    path: 'workflow/application/w1-asset-list/scan-list',
    loadChildren: () =>
      import('./workflow/application/w1-asset-list/scan-list/scan-list.module').then((m) => m.ScanListPageModule),
  },
  // {
  //   path: 'workflow/application/w1-asset-list/edit',
  //   loadChildren: () => import('./workflow/application/w1-asset-list/edit/edit.module').then(m => m.EditPageModule)
  // },
  {
    path: 'workflow/application/w2/view',
    loadChildren: () => import('./workflow/application/w2/view/view.module').then((m) => m.ViewPageModule),
  },
  {
    path: 'workflow/application/w2/edit1',
    loadChildren: () => import('./workflow/application/w2/edit1/edit1.module').then((m) => m.Edit1PageModule),
  },
  {
    path: 'workflow/application/w3/view',
    loadChildren: () => import('./workflow/application/w3/view/view.module').then((m) => m.ViewPageModule),
  },
  {
    path: 'workflow/application/w3/scan',
    loadChildren: () => import('./workflow/application/w3/scan/scan.module').then((m) => m.ScanPageModule),
  },
  {
    path: 'workflow/application/w3/scan-list',
    loadChildren: () =>
      import('./workflow/application/w3/scan-list/scan-list.module').then((m) => m.ScanListPageModule),
  },
  {
    path: 'workflow/application/w3/submit',
    loadChildren: () => import('./workflow/application/w3/submit/submit.module').then((m) => m.SubmitPageModule),
  },
  // workflow approval
  {
    path: 'workflow/approval/w1/view',
    loadChildren: () => import('./workflow/approval/w1/view/view.module').then((m) => m.ViewPageModule),
  },
  {
    path: 'workflow/approval/w1-asset-list/view',
    loadChildren: () => import('./workflow/approval/w1-asset-list/view/view.module').then((m) => m.ViewPageModule),
  },
  {
    path: 'workflow/approval/w1-asset-list/list',
    loadChildren: () => import('./workflow/approval/w1-asset-list/list/list.module').then((m) => m.ListPageModule),
  },
  {
    path: 'workflow/approval/w1-asset-list/subform',
    loadChildren: () =>
      import('./workflow/approval/w1-asset-list/subform/subform.module').then((m) => m.SubformPageModule),
  },
  {
    path: 'workflow/approval/w1-maihama/view',
    loadChildren: () => import('./workflow/approval/w1-maihama/view/view.module').then((m) => m.ViewPageModule),
  },
  {
    path: 'workflow/approval/w1-maihama/list',
    loadChildren: () => import('./workflow/approval/w1-maihama/list/list.module').then((m) => m.ListPageModule),
  },
  {
    path: 'workflow/approval/w2/view',
    loadChildren: () => import('./workflow/approval/w2/view/view.module').then((m) => m.ViewPageModule),
  },
  {
    path: 'workflow/approval/w2/list',
    loadChildren: () => import('./workflow/approval/w2/list/list.module').then((m) => m.ListPageModule),
  },
  {
    path: 'workflow/approval/w2/edit2',
    loadChildren: () => import('./workflow/approval/w2/edit2/edit2.module').then((m) => m.Edit2PageModule),
  },
  {
    path: 'workflow/approval/w3/view',
    loadChildren: () => import('./workflow/approval/w3/view/view.module').then((m) => m.ViewPageModule),
  },
  {
    path: 'workflow/approval/w3/edit',
    loadChildren: () => import('./workflow/approval/w3/edit/edit.module').then((m) => m.EditPageModule),
  },
  {
    path: 'workflow/approval/w3/list1',
    loadChildren: () => import('./workflow/approval/w3/list1/list1.module').then((m) => m.List1PageModule),
  },
  {
    path: 'workflow/approval/w3/list2',
    loadChildren: () => import('./workflow/approval/w3/list2/list2.module').then((m) => m.List2PageModule),
  },
  {
    path: 'workflow/approval/w3/step',
    loadChildren: () => import('./workflow/approval/w3/step/step.module').then((m) => m.StepPageModule),
  },
  {
    path: 'workflow/approval/w3/scan',
    loadChildren: () => import('./workflow/approval/w3/scan/scan.module').then((m) => m.ScanPageModule),
  },
  {
    path: 'workflow/approval/w3/scan-list',
    loadChildren: () => import('./workflow/approval/w3/scan-list/scan-list.module').then((m) => m.ScanListPageModule),
  },
  // {
  //   path: 'workflow/edit-item/quantity',
  //   loadChildren: () => import('./workflow/edit-item/quantity/quantity.module').then(m => m.QuantityPageModule)
  // },
  {
    path: 'workflow/search-condition',
    loadChildren: () =>
      import('./workflow/search-condition/search-condition.module').then((m) => m.SearchConditionPageModule),
  },
  {
    path: 'workflow/asset-list-no-claim/detail',
    loadChildren: () =>
      import('./workflow/approval/w1-asset-list-no-claim/detail/detail.module').then((m) => m.DetailPageModule),
  },
  {
    path: 'workflow/asset-list-no-claim/asset-list',
    loadChildren: () =>
      import('./workflow/approval/w1-asset-list-no-claim/asset-list/asset-list.module').then(
        (m) => m.AssetListPageModule,
      ),
  },
  {
    path: 'workflow/form-report-display',
    loadChildren: () =>
      import('./workflow/form-report-display/form-report-display.module').then((m) => m.FormReportDisplayPageModule),
  },
  // edit item
  {
    path: 'edit-item/input',
    loadChildren: () => import('./edit-item/input/input.module').then((m) => m.InputPageModule),
  },
  {
    path: 'edit-item/userinfo-input',
    loadChildren: () =>
      import('./edit-item/userinfo-input/userinfo-input.module').then((m) => m.UserinfoInputPageModule),
  },
  {
    path: 'edit-item/textarea',
    loadChildren: () => import('./edit-item/textarea/textarea.module').then((m) => m.TextareaPageModule),
  },
  {
    path: 'edit-item/phone-number',
    loadChildren: () => import('./edit-item/phone-number/phone-number.module').then((m) => m.PhoneNumberPageModule),
  },
  {
    path: 'edit-item/phone-number/ipn',
    loadChildren: () => import('./edit-item/phone-number/ipn/ipn-routing.module').then((m) => m.IpnPageRoutingModule),
  },
  {
    path: 'edit-item/staff',
    loadChildren: () => import('./edit-item/staff/staff.module').then((m) => m.StaffPageModule),
  },
  {
    path: 'edit-item/extraction-condition',
    loadChildren: () =>
      import('./edit-item/extraction-condition/extraction-condition.module').then(
        (m) => m.ExtractionConditionPageModule,
      ),
  },
  // preview
  {
    path: 'preview/image',
    loadChildren: () => import('./preview/image/image.module').then((m) => m.ImagePageModule),
  },
  {
    path: 'preview/image/:level',
    loadChildren: () => import('./preview/image/image.module').then((m) => m.ImagePageModule),
  },
  {
    path: 'preview/map',
    loadChildren: () => import('./preview/map/map.module').then((m) => m.MapPageModule),
  },
  {
    path: 'edit-item/relate',
    loadChildren: () => import('./edit-item/relate/relate.module').then((m) => m.RelatePageModule),
  },
  {
    path: 'location-select',
    loadChildren: () => import('./location-select/location-select.module').then((m) => m.LocationSelectPageModule),
  },
  {
    path: 'workflow/edit-item/edit-form',
    loadChildren: () => import('./workflow/edit-item/edit-form/edit-form.module').then((m) => m.EditFormPageModule),
  },
  {
    path: 'search/search',
    loadChildren: () => import('./search/search.module').then((m) => m.SearchPageModule),
  },
  {
    path: 'component/list',
    loadChildren: () => import('./component/list/list.module').then((m) => m.ListPageModule),
  },
  {
    path: 'component/list/:level',
    loadChildren: () => import('./component/list/list.module').then((m) => m.ListPageModule),
  },
  {
    path: 'relation-list/relation-list',
    loadChildren: () => import('./asset/relation-list/relation-list.module').then((m) => m.RelationListPageModule),
  },
  {
    path: 'asset-type-modal',
    loadChildren: () =>
      import('./asset/asset-type-modal/asset-type-modal.module').then((m) => m.AssetTypeModalPageModule),
  },
  {
    path: 'relation-scan',
    loadChildren: () => import('./asset/relation-scan/relation-scan.module').then((m) => m.RelationScanPageModule),
  },
  {
    path: 'action/list/view',
    loadChildren: () => import('./action/list/view/view.module').then((m) => m.ViewPageModule),
  },
  {
    path: 'action/edit-item/edit-item',
    loadChildren: () => import('./action/edit-item/edit-form/edit-form.module').then((m) => m.EditFormPageModule),
  },
  {
    path: 'action/list/asset-list',
    loadChildren: () => import('./action/list/asset-list/asset-list.module').then((m) => m.AssetListPageModule),
  },
  {
    path: 'action/list/preview-confirm',
    loadChildren: () =>
      import('./action/list/preview-confirm/preview-confirm.module').then((m) => m.PreviewConfirmPageModule),
  },
  {
    path: 'action/list/preview-confirm/preview-asset-list',
    loadChildren: () =>
      import('./action/list/preview-confirm/preview-asset-list/preview-asset-list.module').then(
        (m) => m.PreviewAssetListPageModule,
      ),
  },
  {
    path: 'action/complete/view',
    loadChildren: () => import('./action/complete/view/view.module').then((m) => m.ViewPageModule),
  },
  {
    path: 'asset-category',
    loadChildren: () => import('./asset-category/asset-category.module').then((m) => m.AssetCategoryPageModule),
  },
  {
    path: 'maintenance',
    loadChildren: () => import('./maintenance/maintenance.module').then((m) => m.MaintenancePageModule),
  },
  {
    path: 'asset/schedule-alert',
    loadChildren: () => import('./asset/schedule-alert/schedule-alert.module').then((m) => m.ScheduleAlertPageModule),
  },
  {
    path: 'worlkflow/w1-choose-tantousha',
    loadChildren: () =>
      import('./workflow/new/w1-choose-tantousha/w1-choose-tantousha.module').then(
        (m) => m.W1ChooseTantoushaPageModule,
      ),
  },
  {
    path: 'asset/rfid-list',
    loadChildren: () => import('./asset/rfid-list/rfid-list.module').then((m) => m.RfidListPageModule),
  },
  {
    path: 'asset/submit-complete',
    loadChildren: () =>
      import('./asset/submit-complete/submit-complete.module').then((m) => m.SubmitCompletePageModule),
  },
  {
    path: 'asset/rfid-pair',
    loadChildren: () => import('./asset/rfid-pair/rfid-pair.module').then((m) => m.RfidPairPageModule),
  },
  {
    path: 'asset/rfid-scan',
    loadChildren: () => import('./asset/rfid-scan/rfid-scan.module').then((m) => m.RfidScanPageModule),
  },
  {
    path: 'action/create-or-save-temporarily',
    loadChildren: () =>
      import('./action/create-or-save-temporarily/create-or-save-temporarily.module').then(
        (m) => m.CreateOrSaveTemporarilyPageModule,
      ),
  },
  {
    path: 'action/offline-progress',
    loadChildren: () =>
      import('./action/offline-progress/offline-progress.module').then((m) => m.OfflineProgressPageModule),
  },
  {
    path: 'action/offline-progress/view',
    loadChildren: () => import('./action/offline-progress/view/view.module').then((m) => m.ViewPageModule),
  },
];

@NgModule({
  imports: [RouterModule.forRoot(routes, { preloadingStrategy: PreloadAllModules })],
  exports: [RouterModule],
})
export class AppRoutingModule {}
