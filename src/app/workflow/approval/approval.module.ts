import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { ApprovalPageRoutingModule } from './approval-routing.module';

import { ApprovalPage } from './approval.page';
import { File } from '@awesome-cordova-plugins/file/ngx';
import { ScanService } from 'src/app/services/scan/scan.service';
import { ComponentsModule } from 'src/app/component/component.module';

@NgModule({
  imports: [CommonModule, FormsModule, IonicModule, ApprovalPageRoutingModule, ComponentsModule],
  declarations: [ApprovalPage],
  providers: [ScanService, File],
})
export class ApprovalPageModule {}
