//
//  Utils.swift
//  SmartBooking
//
//  Created by <PERSON> on 2019/01/21.
//  Copyright © 2019年 Zhang <PERSON>. All rights reserved.
//

import UIKit
import SVProgressHUD
import AVFoundation
//import Reachability


enum ErrorType {
    case requetFaild
    case service404
    case noRecords
    case networkErr
    case noData
}

class Utils: NSObject {
    /// スキャンQRコードモードを起動
    ///
    /// - Parameter controller: controller description
//    class func startScanQRCode(controller:UIViewController) {
//
//        var style = LBXScanViewStyle()
//        style.centerUpOffset = 60
//        style.xScanRetangleOffset = 30
//        if UIScreen.main.bounds.size.height <= 480 {
//            style.centerUpOffset = 40
//            style.xScanRetangleOffset = 20
//        }
//
//        style.color_NotRecoginitonArea = UIColor(red: 0.4, green: 0.4, blue: 0.4, alpha: 0.4)
//        style.photoframeAngleStyle = LBXScanViewPhotoframeAngleStyle.Inner
//        style.photoframeLineW = 2.0
//        style.photoframeAngleW = 16
//        style.photoframeAngleH = 16
//        style.isNeedShowRetangle = false
//        style.anmiationStyle = LBXScanViewAnimationStyle.NetGrid
//        style.animationImage = UIImage(named: "CodeScan.bundle/qrcode_scan_full_net")
//        let vc = LBXScanViewController()
//
//        vc.scanStyle = style
//        vc.scanResultDelegate = controller as? LBXScanViewControllerDelegate
//        vc.title = "Scan"
//        controller.navigationController?.pushViewController(vc, animated: true)
//    }
    class func clearScanedData() {
        SingleInstance.share.isWillScanMore = false
        if SingleInstance.shareList.barCodeList.count > 0 {
            SingleInstance.shareList.barCodeList.removeAll()
            SingleInstance.shareList.barCodeList = []
        }
        if SingleInstance.shareList.barCodeDetailsInfoList.count > 0 {
            SingleInstance.shareList.barCodeDetailsInfoList.removeAll()
            SingleInstance.shareList.barCodeDetailsInfoList = []
        }
        if SingleInstance.shareList.registerBarcodeList.count > 0 {
            SingleInstance.shareList.registerBarcodeList.removeAll()
            SingleInstance.shareList.registerBarcodeList = []
        }
        if SingleInstance.shareList.registerStatusImageList.count > 0 {
            SingleInstance.shareList.registerStatusImageList.removeAll()
            SingleInstance.shareList.registerStatusImageList = []
        }
    }
    /// JSONStringからDictionaryへ変換
    ///
    /// - Parameter jsonString: jsonString description
    /// - Returns: return value description
    class func getDictionaryFromJSONString(jsonString:String) ->NSDictionary{
        let jsonData:Data = jsonString.data(using: .utf8)!
        let dict = try? JSONSerialization.jsonObject(with: jsonData, options: .mutableContainers)
        if dict != nil {
            if dict is NSDictionary {
                return dict as! NSDictionary
            }
        }
        return NSDictionary()
    }
    
    class  func makeData(data:ScanResult)->NSDictionary{
        let dic:NSDictionary = ["state":data.state ?? "",
                                "isString":data.isString ?? false,
                                "lastState":data.lastState ?? "",
                                "bpmnTaskDefinitions":data.bpmnTaskDefinitions ?? "",
                                "code":data.code ?? 0,
                                "color":data.color ?? "",
                                "msg":data.msg ?? "",
                                "bpmnSequenceFlows":data.bpmnSequenceFlows ?? "",
                                "assetActionList":data.assetActionList ?? NSDictionary.init(),
                                "assetText":data.assetText ?? "",
                                "assetDic":data.assetDic ?? NSDictionary.init(),
                                "scanUserName":data.scanUserName ?? "",
                                "form":data.form ?? "",
                                "scanTime":data.scanTime ?? "",
                                "taskDefinitionKey":data.taskDefinitionKey ?? ""]
        return dic
    }
    
    /// JSONStringからArrayへ変換
    ///
    /// - Parameter jsonString: jsonString description
    /// - Returns: return value description
    class func getNSArrayFromJSONString(jsonString:String) ->NSArray{
        
        let jsonData:Data = jsonString.data(using: .utf8)!
        let dict = try? JSONSerialization.jsonObject(with: jsonData, options: .mutableContainers)
        if dict != nil {
            return dict as! NSArray
        }
        return NSArray()
    }
    
    /// ArrayからJSONStringへ変換
    ///
    /// - Parameter array: array description
    /// - Returns: return value description
    class func getJsonFromArray(_ array: NSArray)->String{
        if (!JSONSerialization.isValidJSONObject(array)) {
            return ""
        }
        let data : Data! = try? JSONSerialization.data(withJSONObject: array, options: [])
        let str = NSString(data:data, encoding: String.Encoding.utf8.rawValue)
        return str! as String
    }
    
    /// 日付からStringへ変換
    ///
    /// - Parameters:
    ///   - string: string description
    ///   - dateFormat: dateFormat description
    /// - Returns: return value description
    class func stringConvertDate(string:String, dateFormat:String="yyyy/MM/dd") -> Date {
        if string.count > 10 {
            let dateFormatter = DateFormatter.init()
            dateFormatter.dateFormat = "yyyy/MM/dd HH:mm"
            let date = dateFormatter.date(from: string)
            return date ?? Date()
        }

        let dateFormatter = DateFormatter.init()
        dateFormatter.dateFormat = "yyyy/MM/dd"
        let date = dateFormatter.date(from: string)
        return date ?? Date()
    }
    
    /// Stringから日付へ変換
    ///
    /// - Parameters:
    ///   - date: date description
    ///   - dateFormat: dateFormat description
    /// - Returns: return value description
    class func dateConcertString(date: Date, dateFormat:String="yyyy/MM/dd") -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = dateFormat
        let dateStr = formatter.string(from: date)
        return dateStr
    }
    
    /// DictionaryからStringへ変換
    ///
    /// - Parameter dic: dic description
    /// - Returns: return value description
    class func getStringFromDic(_ dic:[String : Any]) -> String? {
        let data = try? JSONSerialization.data(withJSONObject: dic, options: [])
        let str = String(data: data!, encoding: String.Encoding.utf8)
        return str
    }
    
    /// DictionaryからJSONStringへ変換
    ///
    /// - Parameter dict: dict description
    /// - Returns: return value description
    class func getJSONStringFromDictionary(dict:NSDictionary?) -> String {
        let data = try? JSONSerialization.data(withJSONObject: dict!, options: JSONSerialization.WritingOptions.prettyPrinted)
        let strJson = NSString(data: data!, encoding: String.Encoding.utf8.rawValue);
        return strJson! as String
    }
    
    /// NSDataからNSDictionaryへ変換
    ///
    /// - Parameter data: data description
    /// - Returns: return value description
    class func nsdataToJSON(data: NSData) -> NSDictionary? {
        do {
            return try JSONSerialization.jsonObject(with: data as Data, options: .mutableContainers) as? NSDictionary
        } catch {
            DEBUGLOG.NSLog(message:error)
        }
        return nil
    }
    
    /// 1.５秒遅延でSVProgressViewを隠す
    class func hideSVProgressView() {
        if #available(iOS 10.0, *) {
            Timer.scheduledTimer(withTimeInterval: 1.5, repeats: false) { (timer) in
                SVProgressHUD.dismiss()
            }
        } else {
            // Fallback on earlier versions
        }
    }
    
    
    
    /// ラベルのカラーが設定可能
    class func changeSomeTextColor(text: String, inText result: String, color: UIColor) -> NSAttributedString {
        let attributeStr = NSMutableAttributedString(string: result)
        let colorRange = NSMakeRange(attributeStr.mutableString.range(of: text).location, attributeStr.mutableString.range(of: text).length)
        attributeStr.addAttribute(NSAttributedString.Key.foregroundColor, value:color , range: colorRange)
        
        return attributeStr
    }
    
    /// 必須項目チェックの上、必須項目であれば赤い※をつける
    ///
    /// - Parameters:
    ///   - str: str description
    ///   - inputFlag: inputFlag description
    /// - Returns: return value description
    class func attributteString(str: String, inputFlag: Int) -> NSAttributedString {
        let attrs1 = [NSAttributedString.Key.font : UIFont.boldSystemFont(ofSize: 12), NSAttributedString.Key.foregroundColor : UIColor.black]
        
        let attrs2 = [NSAttributedString.Key.font : UIFont.boldSystemFont(ofSize: 12), NSAttributedString.Key.foregroundColor : UIColor.red]
        
        let attributedString1 = NSMutableAttributedString(string:str, attributes:attrs1)
        // １の場合：必須項目
        if inputFlag == 1 {
            let attributedString2 = NSMutableAttributedString(string:"※", attributes:attrs2)
            attributedString1.append(attributedString2)
        }
        return attributedString1
    }
    
    /// HTTP Error
    ///
    /// - Parameters:
    ///   - resultDic: resultDic description
    ///   - controller: controller description
    class func systemError(resultDic: NSDictionary, controller: UIViewController) {
        //        SVProgressHUD.dismiss()
        DispatchQueue.main.async {
            AlertManager.shareInstance.showAlertCancel("", message: "\(resultDic["msg"] ?? "不明なエラーが発生しました。管理者までご連絡ください。")", cancelTitle: "OK", actionStyle: .default, controller: controller, cancelBlock: { (str) in
            })
        }
    }
    
    class func getPriousorLaterDate(from date: Date?, withMonth month: Int) -> Date? {
        var comps = DateComponents()
        comps.month = month
        let calender = Calendar(identifier: .gregorian)
        var mDate: Date? = nil
        if let date = date {
            mDate = calender.date(byAdding: comps, to: date)
        }
        return mDate
    }
    
    
    class func convertDateAfterDays(dateString: String, days: Int) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy/MM/dd"
        if dateString.count > 10 {
            let dateStr = dateString.components(separatedBy: " ").first
            if dateStr != "" {
                let myDate = dateFormatter.date(from: dateStr ?? "")!
                let tomorrow = Calendar.current.date(byAdding: .day, value: days, to: myDate)
                let somedateString = dateFormatter.string(from: tomorrow!)
                return somedateString
            }
        }
        let myDate = dateFormatter.date(from: dateString)!
        let tomorrow = Calendar.current.date(byAdding: .day, value: days, to: myDate)
        let somedateString = dateFormatter.string(from: tomorrow!)
        return somedateString
    }
    
    class func isOnlyNumber(_ str:String) -> Bool {
        let predicate = NSPredicate(format: "SELF MATCHES '\\\\d+'")
        return predicate.evaluate(with: str)
    }
    
    /// 当日の日付を取る
    ///
    /// - Parameter format: デフォルト： YYYY/MM/dd
    /// - Returns: return value dateString
    class func getCurrentDateByFormat(format: String = "YYYY/MM/dd") -> String {
        let dateformatter = DateFormatter()

        dateformatter.dateFormat = format

        let time = dateformatter.string(from: Date())

        return time
    }

    
    /// テキストからリストに変換
    ///
    /// - Parameters:
    ///   - separator: separator description
    ///   - str: str description
    /// - Returns: return value description
    class func getArrayFromStringSeparatedBy(separator: String, str: String) -> Set<String> {
        let array = str.components(separatedBy: separator)
        return Set(array)
    }
    /// 少数点の計算
    ///
    /// - Parameters:
    ///   - separator: separator description
    ///   - str: str description
    /// - Returns: return value description
    class func getdecimalPoint(str: String,decimalPoint: Int) -> String {
        var suffixString = ""
        if str.contains(".") {
            if str.index(of: ".") != nil {
                suffixString = String(str.suffix(from: str.index(of: ".")!))
                if decimalPoint == 0 {
                    suffixString = ""
                } else {
                    suffixString = String(suffixString.prefix(decimalPoint + 1))
//                    if (Array(suffixString).count - 1) >= decimalPoint {
//                        suffixString = String(suffixString.prefix(decimalPoint + 1))
//                    }
                }
            }
        }
        return suffixString
    }
    
    class func setMasterValueByLayoutSetting(arMobielSettingInfoByTypeIdDic: NSMutableDictionary,arLevelMasterLayoutSetting: String,itemValue:String) -> String {
        
        let masterLayoutSetting = arMobielSettingInfoByTypeIdDic[arLevelMasterLayoutSetting] as? NSMutableDictionary ?? NSMutableDictionary()
        let option = Utils.getDictionaryFromJSONString(jsonString: masterLayoutSetting["option"] as? String ?? "") as? NSMutableDictionary ?? NSMutableDictionary()
        let itemType = masterLayoutSetting["itemType"] as? String ?? ""
        if itemType == "number" {
            //AR显示web传来的值
//            var value = ""
//            var numberDecimalPoint = Int(option["numberDecimalPoint"] as? String ?? "0") ?? 0
//            if let percentage = option["percentage"] as? String, percentage == "1" {  //显示百分率的情况下不显示千分符
//                value = self.getPercentage(number: itemValue, decimal: numberDecimalPoint)
//            } else {
//                var numberCommaDecimalPoint = Int(option["numberCommaDecimalPoint"] as? String ?? "0") ?? 0
//                let needComma = numberCommaDecimalPoint as! Int == 3
//
//                value = Utils.getCurrencyString(str:itemValue, decimalPoint: numberDecimalPoint as! Int, needComma: needComma)
//            }
//            if value != "" {
//                if let unitFlg = option["unitFlg"] as? String, unitFlg == "1" {  //单位
//                    value = "\(value) \(option["unit"] ?? "")"
//                }
//            }
//            return value
            return itemValue
        // 判断如果是checkbox并且是单选的checkbox的话，改变显示值
        }else if itemType == "checkbox" {
            var value = ""
            if masterLayoutSetting["option"] is String {
                let option = Utils.getDictionaryFromJSONString(jsonString: masterLayoutSetting["option"] as? String ?? "") as? NSMutableDictionary
                if option?["checkboxMultiFlg"] as? String ?? "" != "1" {
                    if itemValue == "1" {
                        value = "あり"
                    }else {
                        value = "なし"
                    }
                }else {
                    value = itemValue
                }
            }
            return value
        } else {
            return itemValue
        }
    }
    
    /// カンマ設定
    class func setCommaDecimalPoint(itemOption: String,itemType: String,itemValue:String) -> String {
        var tempString = itemValue
        if itemType == "" {
            return tempString
        }
        let option:NSMutableDictionary = Utils.getDictionaryFromJSONString(jsonString:itemOption) as! NSMutableDictionary
        
        if itemType == "number" {
            let numberDecimalPoint = Int(option.object(forKey: "numberDecimalPoint") as? String ?? "0") ?? 0
            if let percentage = option.object(forKey: "percentage") as? String, percentage == "1" {
                tempString = self.getPercentage(number: tempString, decimal: numberDecimalPoint)
            } else {
                if let numberCommaDecimalPoint = option.object(forKey: "numberCommaDecimalPoint") as? String, numberCommaDecimalPoint == "3" {
                    tempString = Utils.getCurrencyString(str: (itemValue ), decimalPoint: numberDecimalPoint, needComma: true)
                }
            }
            if let unitFlg = option.object(forKey: "unitFlg") as? String, unitFlg == "1" {
                if tempString != "" {
                    if let unit = option.object(forKey: "unit") as? String {
                        tempString = "\(tempString) \(unit)"
                    }
                }
            }
        }
        if itemType == "currency" {
            let currencyTypettributedString = option.object(forKey: "currencyType") as? String ?? ""
            if option.object(forKey: "currencyDecimalPoint") is Int{
                tempString = Utils.getCurrencyString(str: (itemValue ), decimalPoint: option.object(forKey: "currencyDecimalPoint") as! Int, needComma: true)
            }
            if option.object(forKey: "currencyDecimalPoint") is String{
                tempString = Utils.getCurrencyString(str: (itemValue ), decimalPoint: Int(option.object(forKey: "currencyDecimalPoint") as! String) ?? 0, needComma: true)
            }
            tempString = currencyTypettributedString + tempString
        }
        if itemType == "calculate" {
            if option.object(forKey: "calculateType") as! String == "currency" {
                if option.object(forKey: "calculateDecimalPoint") is Int {
                    tempString = Utils.getCurrencyString(str: (itemValue ), decimalPoint: option.object(forKey: "calculateDecimalPoint") as! Int, needComma: true)
                }
                if option.object(forKey: "calculateDecimalPoint") is String {
                    tempString = Utils.getCurrencyString(str: (itemValue ), decimalPoint: Int(option.object(forKey: "calculateDecimalPoint") as! String) ?? 0, needComma: true)
                }
            } else if option.object(forKey: "calculateType") as! String == "digital" {
                if option.object(forKey: "calculateCommaDecimalPoint") is Int && (option.object(forKey: "calculateCommaDecimalPoint") as! Int == 3){
                    if option.object(forKey: "calculateDecimalPoint") is Int {
                        tempString = Utils.getCurrencyString(str: (itemValue ), decimalPoint: option.object(forKey: "calculateDecimalPoint") as! Int, needComma: true)
                    }
                    if option.object(forKey: "calculateDecimalPoint") is String {
                        tempString = Utils.getCurrencyString(str: (itemValue ), decimalPoint: Int(option.object(forKey: "calculateDecimalPoint") as! String) ?? 0, needComma: true)
                    }
                }
                if option.object(forKey: "calculateCommaDecimalPoint") is String && (option.object(forKey: "calculateCommaDecimalPoint") as! String == "3"){
                    if option.object(forKey: "calculateDecimalPoint") is Int {
                        tempString = Utils.getCurrencyString(str: (itemValue ), decimalPoint: option.object(forKey: "calculateDecimalPoint") as! Int, needComma: true)
                    }
                    if option.object(forKey: "calculateDecimalPoint") is String {
                        tempString = Utils.getCurrencyString(str: (itemValue ), decimalPoint: Int(option.object(forKey: "calculateDecimalPoint") as! String) ?? 0, needComma: true)
                    }
                }
                if tempString.contains("e+") {
                    // 科学计数法情况
                    let formatter = NumberFormatter()
                    formatter.usesSignificantDigits = true
                    formatter.maximumSignificantDigits = 100
                    formatter.groupingSeparator = ""
                    formatter.numberStyle = NumberFormatter.Style.decimal
                    tempString = formatter.string(from: NSNumber.init(value: Double.init(tempString)!)) ?? ""
                }

                if !tempString.contains(",") {
                    if let percentage = option.object(forKey: "percentage") as? String {
                        if percentage == "1" {
                            if let numberDecimalPoint = Int(option.object(forKey: "calculateDecimalPoint") as? String ?? "0" ) {
                                tempString = self.getPercentage(number: tempString, decimal: numberDecimalPoint)
                            }
                        }
                    }
                }
                if let unitFlg = option.object(forKey: "UnitFlg") as? String, unitFlg == "1" {
                    if let unit = option.object(forKey: "Unit") as? String  {
                        tempString = "\(tempString) \(unit)"
                    }
                }
            }
        }
        if itemType == "checkbox" {
            if option["checkboxMultiFlg"] as? String ?? "" != "1" {
                if itemValue == "1" {
                    tempString = "あり"
                }else {
                    tempString = "なし"
                }
            }else {
                let data = getNSArrayFromJSONString(jsonString: itemValue)
                tempString = data.componentsJoined(by: ",")
            }
        }
        if itemType == "date" {
            if let type = option.object(forKey: "dateType") as? String  {
                if type  == "date" {
                    if itemValue.count > 9 {
                        tempString = (itemValue as NSString).substring(to: 10)
                    }
                } else if type  == "dateTime" {
                    if itemValue.count == 10 {
                        tempString = itemValue + " 00:00"
                    }
                }
            }
        }
        // 履历情报合计计算
        if itemType == "appurInfoSummary" {
            tempString = Utils.getCurrencyString(str: (itemValue ), decimalPoint: 3, needComma: true)
        }
        return tempString
    }
    
    /// カンマ設定
    class func setCommaDecimalPoint(arMobielSettingInfoByTypeIdDic: NSMutableDictionary,arLevelItemOption: String,arLevelItemType: String,itemValue:String) -> String {
//        return   itemValue //AR显示web传来的值
        let resultDic:NSMutableDictionary = Utils.getDictionaryFromJSONString(jsonString: arMobielSettingInfoByTypeIdDic[arLevelItemOption] as? String ?? "") as? NSMutableDictionary ?? NSMutableDictionary()
        var tempString = itemValue
        if arMobielSettingInfoByTypeIdDic[arLevelItemType] as? String == "number" {
            let numberDecimalPoint = Int(resultDic.object(forKey: "numberDecimalPoint") as? String ?? "0") ?? 0
            if let percentage = resultDic.object(forKey: "percentage") as? String, percentage == "1" {
                tempString = self.getPercentage(number: tempString, decimal: numberDecimalPoint)
            } else {
                if let numberCommaDecimalPoint = resultDic.object(forKey: "numberCommaDecimalPoint") as? String, numberCommaDecimalPoint == "3" {
                    tempString = Utils.getCurrencyString(str: (itemValue ), decimalPoint: numberDecimalPoint, needComma: true)
                }
            }
            if let unitFlg = resultDic.object(forKey: "unitFlg") as? String, unitFlg == "1" {
                if tempString != "" {
                    if let unit = resultDic.object(forKey: "unit") as? String {
                        tempString = "\(tempString) \(unit)"
                    }
                }
            }
        }
        if arMobielSettingInfoByTypeIdDic[arLevelItemType] as? String == "currency" {
            let currencyTypettributedString = resultDic.object(forKey: "currencyType") as? String ?? ""
            if resultDic.object(forKey: "currencyDecimalPoint") is Int{
                tempString = Utils.getCurrencyString(str: (itemValue ), decimalPoint: resultDic.object(forKey: "currencyDecimalPoint") as! Int, needComma: true)
            }
            if resultDic.object(forKey: "currencyDecimalPoint") is String{
                tempString = Utils.getCurrencyString(str: (itemValue ), decimalPoint: Int(resultDic.object(forKey: "currencyDecimalPoint") as! String) ?? 0, needComma: true)
            }
            tempString = currencyTypettributedString + tempString
        }
        if arMobielSettingInfoByTypeIdDic[arLevelItemType] as? String == "calculate" {
            if resultDic.object(forKey: "calculateType") as! String == "currency" {
                if resultDic.object(forKey: "calculateDecimalPoint") is Int {
                    tempString = Utils.getCurrencyString(str: (itemValue ), decimalPoint: resultDic.object(forKey: "calculateDecimalPoint") as! Int, needComma: true)
                }
                if resultDic.object(forKey: "calculateDecimalPoint") is String {
                    tempString = Utils.getCurrencyString(str: (itemValue ), decimalPoint: Int(resultDic.object(forKey: "calculateDecimalPoint") as! String) ?? 0, needComma: true)
                }
            } else if resultDic.object(forKey: "calculateType") as! String == "digital" {
                if resultDic.object(forKey: "calculateCommaDecimalPoint") is Int && (resultDic.object(forKey: "calculateCommaDecimalPoint") as! Int == 3){
                    if resultDic.object(forKey: "calculateDecimalPoint") is Int {
                        tempString = Utils.getCurrencyString(str: (itemValue ), decimalPoint: resultDic.object(forKey: "calculateDecimalPoint") as! Int, needComma: true)
                    }
                    if resultDic.object(forKey: "calculateDecimalPoint") is String {
                        tempString = Utils.getCurrencyString(str: (itemValue ), decimalPoint: Int(resultDic.object(forKey: "calculateDecimalPoint") as! String) ?? 0, needComma: true)
                    }
                }
                if resultDic.object(forKey: "calculateCommaDecimalPoint") is String && (resultDic.object(forKey: "calculateCommaDecimalPoint") as! String == "3"){
                    if resultDic.object(forKey: "calculateDecimalPoint") is Int {
                        tempString = Utils.getCurrencyString(str: (itemValue ), decimalPoint: resultDic.object(forKey: "calculateDecimalPoint") as! Int, needComma: true)
                    }
                    if resultDic.object(forKey: "calculateDecimalPoint") is String {
                        tempString = Utils.getCurrencyString(str: (itemValue ), decimalPoint: Int(resultDic.object(forKey: "calculateDecimalPoint") as! String) ?? 0, needComma: true)
                    }
                }
                if tempString.contains("e+") {
                    // 科学计数法情况
                    let formatter = NumberFormatter()
                    formatter.usesSignificantDigits = true
                    formatter.maximumSignificantDigits = 100
                    formatter.groupingSeparator = ""
                    formatter.numberStyle = NumberFormatter.Style.decimal
                    tempString = formatter.string(from: NSNumber.init(value: Double.init(tempString)!)) ?? ""
                }

                if !tempString.contains(",") {
                    if let percentage = resultDic.object(forKey: "percentage") as? String {
                        if percentage == "1" {
                            if let numberDecimalPoint = Int(resultDic.object(forKey: "calculateDecimalPoint") as? String ?? "0" ) {
                                tempString = self.getPercentage(number: tempString, decimal: numberDecimalPoint)
                            }
                        }
                    }
                }
                if let unitFlg = resultDic.object(forKey: "UnitFlg") as? String, unitFlg == "1" {
                    if let unit = resultDic.object(forKey: "Unit") as? String  {
                        tempString = "\(tempString) \(unit)"
                    }
                }
            }
        }
        if arMobielSettingInfoByTypeIdDic[arLevelItemType] as? String == "date" {
            if let type = resultDic.object(forKey: "dateType") as? String  {
                if type  == "date" {
                    if itemValue.count > 9 {
                        tempString = (itemValue as NSString).substring(to: 10)
                    }
                } else if type  == "dateTime" {
                    if itemValue.count == 10 {
                        tempString = itemValue + " 00:00"
                    }
                }
            }
        }
        // 履历情报合计计算
        if arMobielSettingInfoByTypeIdDic[arLevelItemType] as? String == "appurInfoSummary" {
            tempString = Utils.getCurrencyString(str: (itemValue ), decimalPoint: 3, needComma: true)
        }
        return tempString
    }
    
    /// テキストから通貨に変換
    ///
    /// - Parameters:
    ///   - separator: separator description
    ///   - str: str description
    /// - Returns: return value description
    class func getCurrencyString(str: String,decimalPoint: Int, needComma: Bool) -> String {
        let pattern = NSRegularExpression.escapedPattern(for: ".")
        let regex = try! NSRegularExpression(pattern: pattern, options: .caseInsensitive)
        if regex.numberOfMatches(in: str, range: NSRange(0..<str.utf16.count)) > 1 || str.last == "." || str.first == "."{
            return "0"
        }
        var currencyString = ""
        var tempCount:Int = 0
        var prefixString = ""
        var suffixString = ""
        var tempStr = str
        if tempStr.contains("e+") {
            // 科学计数法情况
            let formatter = NumberFormatter()
            formatter.usesSignificantDigits = true
            formatter.maximumSignificantDigits = 100
            formatter.groupingSeparator = ""
            formatter.numberStyle =  NumberFormatter.Style.decimal
            tempStr = formatter.string(from: NSNumber.init(value: Double.init(str)!)) ?? ""
        }
        tempStr = tempStr.replacingOccurrences(of: ",", with: "")
        var arr = Array(tempStr)
        if tempStr.contains(".") {
            if tempStr.index(of: ".") != nil {
                prefixString = String(tempStr.prefix(upTo: tempStr.index(of: ".")!))
                suffixString = String(tempStr.suffix(from: tempStr.index(of: ".")!))
                if decimalPoint == 0 {
                    suffixString = ""
                } else {
                    if (Array(suffixString).count - 1) >= decimalPoint {
                        suffixString = String(suffixString.prefix(decimalPoint + 1))
                    }
                }
                arr = Array(prefixString)
            }
        }
        if arr.count > 3 && needComma{
            for i in (0 ..< arr.count).reversed(){
                tempCount = tempCount + 1
                currencyString = String(arr[i]) + currencyString
                if !(tempCount == arr.count){
                    if tempCount%3 == 0 {
                        currencyString = "," + currencyString
                    }
                }
            }
        } else {
            currencyString = tempStr
            if prefixString != "" {
                if decimalPoint == 0 || suffixString != ""{
                    currencyString = prefixString
                }
            }
        }
        if suffixString != "" {
            currencyString = currencyString + suffixString
        }
        return currencyString
    }
    /// エラー種類によってバックグランドビューを変わる
    ///
    /// - Parameters:
    ///   - errorType: errorType description
    ///   - onView: onView description
    class func createBackgroundViewForRequestErr(_ errorType:ErrorType, onView:UIView) {
        let backgroundImageView = UIImageView.init(frame: CGRect(x: onView.frame.origin.x,y: onView.frame.origin.y,width: onView.frame.width,height:onView.frame.height-100))
        backgroundImageView.tag = 3000001
        backgroundImageView.contentMode = .scaleAspectFit
        backgroundImageView.backgroundColor = .white
        onView.addSubview(backgroundImageView)
        let errorLabel = UILabel.init(frame: CGRect(x: 16,y: backgroundImageView.frame.height - 100,width: onView.frame.width - 32,height: 50))
        errorLabel.textColor = UIColor.lightGray
        errorLabel.font = UIFont.systemFont(ofSize: 20.0)
        errorLabel.numberOfLines = 0
        errorLabel.textAlignment = .center
        backgroundImageView.addSubview(errorLabel)
        switch errorType {
        case .networkErr:
            errorLabel.text = NETWORK_ERROR
            backgroundImageView.image = UIImage(named: "Common_problem_network")
            break
        case .noRecords:
            errorLabel.text = NO_RECORDS
            backgroundImageView.image = UIImage(named: "Common_problem_none records")
            break
        case .requetFaild:
            errorLabel.text = REQUEST_FAILD
            backgroundImageView.image = UIImage(named: "Common_problem_no data")
            break
        case .service404:
            errorLabel.text = SERVICE_404
            backgroundImageView.image = UIImage(named: "Common_problem_service 404")
            break
        case .noData:
            errorLabel.text = NO_DATA
            backgroundImageView.image = UIImage(named: "Common_problem_none records")
            break
        }
    }
    /// バックグランドビューを削除する
    class func deleteBackgroundImageViewFromSuperView(view: UIView) {
        let backgroundImageView = view.viewWithTag(3000001)
        if backgroundImageView != nil {
            backgroundImageView?.removeFromSuperview()
        }
    }
    
    class func setRegixSymbolAndSequenceNumber(regixSymbol: String = "", sequenceNumber: Int = 0) {
           let regixSymbolLocal = SecureUserDefaults.shared.secureDefaults.string(forKey: "regixSymbol")
           let sequenceNumberLocal = SecureUserDefaults.shared.secureDefaults.integer(forKey: "sequenceNumber")
           if regixSymbol != "" {
               SingleInstance.share.regixSymbol = regixSymbol
               SecureUserDefaults.shared.secureDefaults.set(SingleInstance.share.regixSymbol, forKey: "regixSymbol")
           } else if regixSymbolLocal != nil && regixSymbolLocal != "" {
               SingleInstance.share.regixSymbol = regixSymbolLocal ?? ""
           }else {
               SecureUserDefaults.shared.secureDefaults.set(SingleInstance.share.regixSymbol, forKey: "regixSymbol")
           }
           if sequenceNumber != 0 {
               SingleInstance.share.sequenceNumber = sequenceNumber
               SecureUserDefaults.shared.secureDefaults.set(SingleInstance.share.sequenceNumber, forKey: "sequenceNumber")
           } else if sequenceNumberLocal != 0 {
               SingleInstance.share.sequenceNumber = sequenceNumberLocal
           }else {
               SecureUserDefaults.shared.secureDefaults.set(SingleInstance.share.sequenceNumber, forKey: "sequenceNumber")
           }
           SecureUserDefaults.shared.secureDefaults.synchronize()
       }
    
    /// get current controller
    /// - Parameter base: base description
    class func getCurrentViewController(base: UIViewController? = UIApplication.shared.keyWindow?.rootViewController) -> UIViewController? {
        if let nav = base as? UINavigationController {
            return getCurrentViewController(base: nav.visibleViewController)
        }
        if let tab = base as? UITabBarController {
            return getCurrentViewController(base: tab.selectedViewController)
        }
        if let presented = base?.presentedViewController {
            return getCurrentViewController(base: presented)
        }
        return base
    }
    

    class func separateBarcode(barcode: String, separatedBarcode: @escaping (_ barcode: String)-> Void){
           var resultBarcodeStr: String = ""
           let regex = "\(SingleInstance.share.regixSymbol)" //  "(?<=\\|)\\w*"
           let RE = try? NSRegularExpression(pattern: regex, options: .caseInsensitive)
           let matches = RE?.matches(in: barcode, options: .reportProgress, range: NSRange(location: 0, length: barcode.count))
           if matches?.count ?? 0 > 0 {
               if SingleInstance.share.sequenceNumber > 0 && matches?.count ?? 0 > (SingleInstance.share.sequenceNumber-1) {
                   let resultBarcode = (barcode as NSString).substring(with: (matches?[SingleInstance.share.sequenceNumber-1])?.range(at: 0) ?? NSRange())
                   if resultBarcode == "" {
                       resultBarcodeStr = barcode
                   } else {
                       resultBarcodeStr = resultBarcode
                   }
               }else {
                   resultBarcodeStr = barcode
               }
           }else {
               resultBarcodeStr = barcode
           }
           separatedBarcode(resultBarcodeStr)
       }
    
    class func getLocation() -> String {
        let userName = SecureUserDefaults.shared.secureDefaults.string(forKey: USER_NAME)
        if SecureUserDefaults.shared.secureDefaults.string(forKey: Utils.base64Key(key: userName ?? "") ) != nil {
            return SecureUserDefaults.shared.secureDefaults.string(forKey: Utils.base64Key(key: userName ?? "")) ?? ""
        }
        return "null"
    }
    
    class func setLocationInfoToLocal(location: String) {
        let userName = SecureUserDefaults.shared.secureDefaults.string(forKey: USER_NAME) ?? ""
        SecureUserDefaults.shared.secureDefaults.setValue(location, forKey: Utils.base64Key(key: userName))
        SecureUserDefaults.shared.secureDefaults.synchronize()
    }
    
    /// レンタルのテナントでサブプロセスなしのケースか
    /// - Parameter taskForm: taskForm description
    class func isNoSubprocess(taskForm: TaskForm) -> Bool {
        if taskForm.tenantKbn == true && taskForm.nonSubProcess == true {
            return true
        }
        return false
    }
    
    class func dismissVC(vc: UIViewController) {
        DispatchQueue.main.async {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3, execute:{
                vc.dismiss(animated: true, completion: nil)
            })
        }
    }
    
    // テキスト前後の記号(controlCharacters, whitespaces, newlines)を削除
    class func resetCharacterSet(barcode: String?) -> String {
        guard let tempResetBarcode = barcode else {
            return ""
        }
        // 替换所有的换行符和回车符
        let regex = try! NSRegularExpression(pattern: "[\\n\\r]", options: [])
        let range = NSRange(location: 0, length: tempResetBarcode.utf16.count)
        let cleanedBarcode = regex.stringByReplacingMatches(in: tempResetBarcode, options: [], range: range, withTemplate: "")
        
        var resetBarcode = (cleanedBarcode.trimmingCharacters(in: .controlCharacters))
        resetBarcode = resetBarcode.trimmingCharacters(in: .newlines)
        resetBarcode = resetBarcode.trimmingCharacters(in: .whitespaces)
        return resetBarcode
    }
    
    // barcode trim
    class func barcodeTrim(barcodeList: Array<String>) -> Array<String>{
        var tempBarcodeList = Array<String>()
        for barcode in barcodeList {
           let tempBarcode = Utils.resetCharacterSet(barcode: barcode)
            tempBarcodeList.append(tempBarcode)
        }
        return tempBarcodeList
    }
    
    class func setCloseBtnLayoutTop() -> CGFloat{
        if OS_VERSION_AFTER16 {
            return 48.0
        }
        return 8.0
    }
    
    class func RFIDConnectionStatus() -> Bool {
        let userDefauts = SecureUserDefaults.shared.secureDefaults
        let RFIDHasBeenUsed = userDefauts.object(forKey: "RFIDHasBeenUsed")
        return (RFIDHasBeenUsed != nil && RFIDHasBeenUsed as! Bool) && ((zt_RfidAppEngine.shared()?.activeReader()?.isActive())! || (AppUtils.shared.scannerConnected && AppUtils.shared.haveSP1DeviceConnected()))
    }
    
    class func RFIDIsTheLastOpne() -> Bool {
        let lastOpen = SecureUserDefaults.shared.secureDefaults.object(forKey: LAST_OPEN) as? String ?? ""

        if !Utils.RFIDConnectionStatus() {
            return false
        }else {
            if lastOpen == "barcode" {
                return false
            }
            return true
        }
    }
    
    class func getImagesSize(images: [UIImage]) -> Double {
        var size = 0.0
        for image in images {
            // 等比例压缩图片
            let imageData:Int = Utils.compressedImageSize(image: image, compressionQuality: 0.5) ?? 0
            let dataToKB = Double(imageData) / 1024.0
            size += dataToKB
            DEBUGLOG.NSLog(message: "aqaz Utils getImagesSize imageData:\(imageData), image.imageData:\(image.sd_imageData()), size:\(size), dataToKB:\(dataToKB)")
        }
        return size
    }
    
    
    /// 压缩图片并获取图片大小
    /// - Parameters:
    ///   - image: 选中的图片
    ///   - compressionQuality: 压缩比例。压缩质量，取值范围：0.0（最低质量）到 1.0（最高质量）
    /// - Returns: 图片大小
    class func compressedImageSize(image: UIImage, compressionQuality: CGFloat) -> Int? {
        if let imageData = image.jpegData(compressionQuality: compressionQuality) {
            return imageData.count
        }
        return nil
    }
    
    class func getPercentage(number:String, decimal:Int = 2, digit:Int = 2) -> String {
        let n = number.replacingOccurrences(of: ",", with: "")

        if let num = Double(n) {
            let dec = pow(10.0, Double(decimal))
            let v = floor(num * dec) / dec
            let dit = pow(10.0, Double(digit))
            let val = v * dit
            let value = round(val * dec) / dec;
            return "\(String(format: "%g", value))%"
        } else {
            return number
        }
    }
        
    class func toDownloadModelOnceMoreWhenError(serialNo: String) {
        let userDefaults = SecureUserDefaults.shared.secureDefaults
        userDefaults.setValue("", forKey: "\(serialNo)-AITeamModelVersion")
        userDefaults.synchronize()
    }
    
    class func base64Key(key: String) -> String {
        let data: Data = key.data(using: .utf8)!
        let base64Key = data.base64EncodedString()
        return base64Key
    }
    
    /// カテゴリー大・中・小の設定
    ///
    /// - Parameters:
    ///   - arMobielSettingInfoByTypeIdDic: arMobielSettingInfoByTypeIdDic description
    ///   - arLevelItemId: arLevelItemId description
    ///   - arLevelItemName: arLevelItemName description
    ///   - arLevelItemType: arLevelItemType description
    ///   - model: model description
    ///   - assetName: assetName description
    /// - Returns: return value description
    ///
    class func setCategoryInfo(arMobielSettingInfoByTypeIdDic: NSMutableDictionary, categoryInfo: CategoryInfo, model: AssetModel, assetName: String) -> String {
        var ARLevelItemName: String?
        if arMobielSettingInfoByTypeIdDic[categoryInfo.itemId] is Int && arMobielSettingInfoByTypeIdDic[categoryInfo.itemId] as! Int != 0 {
            if  arMobielSettingInfoByTypeIdDic[categoryInfo.itemName] is String {
                //keyチェック
                if let title = arMobielSettingInfoByTypeIdDic[categoryInfo.itemDisplayName] {
                    //valueチェック
                    if let tempItemValue = model.assetDic![arMobielSettingInfoByTypeIdDic[categoryInfo.itemName] ?? ""] {
                        if arMobielSettingInfoByTypeIdDic[categoryInfo.itemType] as? String == "master" {
                            let subItemValue = ((model.assetDic?[arMobielSettingInfoByTypeIdDic[categoryInfo.itemName] ?? ""] as? NSMutableDictionary)?["display"] as? NSMutableDictionary)
                            let subItemKey = "\(arMobielSettingInfoByTypeIdDic[categoryInfo.subItemId] ?? "")"
                            let subItemByVal = subItemValue?[subItemKey]
                            var subItemDa: String = ""
                            if subItemByVal != nil {
                                if subItemByVal is Array<String> {
                                    let subItemAry: Array<String> = subItemByVal as? Array<String> ?? []
                                    subItemDa = subItemAry.joined(separator: ",")
                                } else if subItemByVal is Array<Int> {
                                    let subItemAry: Array<Int> = subItemByVal as? Array<Int> ?? []
                                    subItemDa = subItemAry.map{String($0)}.joined(separator: ",")
                                }
                                else if subItemByVal is String {
                                    subItemDa = subItemByVal as? String ?? ""
                                }
                            }
                            var tempString = ""
                            // 当master本身并没有被选择的时候，要显示为空并不是なし，所以要进行这个判断
                            if tempItemValue is NSDictionary && (tempItemValue as! NSDictionary).allKeys.count > 0 {
                                tempString = Utils.setMasterValueByLayoutSetting(arMobielSettingInfoByTypeIdDic: arMobielSettingInfoByTypeIdDic, arLevelMasterLayoutSetting: categoryInfo.masterLayoutSetting, itemValue: subItemDa);
                            }
                            let masterLayoutSetting = arMobielSettingInfoByTypeIdDic[categoryInfo.masterLayoutSetting] as? NSMutableDictionary ?? NSMutableDictionary()
                            let masterItemTitle = masterLayoutSetting["itemDisplayName"] as? String ?? ""
                            ARLevelItemName = (title as? String ?? "") + "(" + masterItemTitle + ")" + SEPARATOR + tempString
                        } else {
                            //checkタイプ
                            if arMobielSettingInfoByTypeIdDic[categoryInfo.itemType] as? String != "checkbox" {
                                var itemValue = ""
                                //Intタイプ
                                if tempItemValue is Int || tempItemValue is Double || tempItemValue is Float {
                                    let value = model.assetDic?[arMobielSettingInfoByTypeIdDic[categoryInfo.itemName] ?? ""] ?? ""
                                    itemValue = "\(value)"
                                } else if tempItemValue is String {
                                    itemValue = tempItemValue as! String
                                }
                                if arMobielSettingInfoByTypeIdDic[categoryInfo.itemOption] is String {
                                    let tempString = Utils.setCommaDecimalPoint(arMobielSettingInfoByTypeIdDic: arMobielSettingInfoByTypeIdDic,arLevelItemOption: categoryInfo.itemOption,arLevelItemType: categoryInfo.itemType,itemValue: itemValue)
                                    ARLevelItemName = (title as? String ?? "") + SEPARATOR + tempString
                                } else {
                                    ARLevelItemName = (title as? String ?? "") + SEPARATOR + itemValue
                                }
                                
                            } else {
                                if tempItemValue is String {
                                    if (tempItemValue as! String) == "1" {
                                        ARLevelItemName = (title as? String ?? "") + SEPARATOR + "✅"
                                    } else {
                                        ARLevelItemName = (title as? String ?? "") + SEPARATOR + "☑️"
                                    }
                                    
                                }else if tempItemValue is Int {
                                    if (tempItemValue as! Int) == 1 {
                                        ARLevelItemName = (title as? String ?? "") + SEPARATOR + "✅"
                                    } else {
                                        ARLevelItemName = (title as? String ?? "") + SEPARATOR + "☑️"
                                    }
                                }
                            }
                        }
                    } else {
                        ARLevelItemName = (title as? String ?? "") + SEPARATOR + ""
                    }
                } else {
                    ARLevelItemName = " "
                }
            } else {
                ARLevelItemName = " "
            }
        } else if arMobielSettingInfoByTypeIdDic[categoryInfo.itemId] is Int && arMobielSettingInfoByTypeIdDic[categoryInfo.itemId] as! Int == 0 {
            ARLevelItemName = "資産種類" + SEPARATOR + (model.assetTypeName ?? "")!
        } else {
            ARLevelItemName = ""
        }
        
        //概覧権限チェック
        AuthorizedUtils.shared.isCheckAuthority(itemOption: arMobielSettingInfoByTypeIdDic[categoryInfo.itemOption] as? String ?? "", privateGroupsStr: SECTION_PRIVATEGROUPS, authorizedBlock: { (message) in
            
        }, noAuthorizedBlock: { (message) in
            ARLevelItemName = "  "
        })
        return ARLevelItemName ?? ""
        
        
        
    }
    
    /// ダークモードかを判断して、色を変える
    /// - Parameters:
    ///   - dark: ダークモードの色
    ///   - light: ホワイトモード
    /// - Returns: 色
    public class func dynamicColor(dark:UIColor, light:UIColor) -> UIColor {
        if #available(iOS 13, *) {  // 版本号大于等于13
          return UIColor { (traitCollection: UITraitCollection) -> UIColor in
            return traitCollection.userInterfaceStyle == UIUserInterfaceStyle.dark ?
                          dark : light
          }
        }
        return light
      }

    /// 没有相机权限时候弹出alert
    class func presentCameraPermissionsDeniedAlert() {
        let alertController = UIAlertController(title: "カメラのアクセス権限を許可して下さい。", message: "", preferredStyle: .alert)

        let loginAction = UIAlertAction(title: "設定", style: .default, handler: { [self]
            action in
            let url:URL = URL(string: UIApplication.openSettingsURLString)!
            if #available(iOS 10.0, *) {
                UIApplication.shared.open(url, options: [:], completionHandler: nil)
            } else {
                // Fallback on earlier versions
            }
        })

        let cancelAction = UIAlertAction(title: "キャンセル", style: .cancel, handler: {
            action in

        })
        alertController.addAction(loginAction)
        alertController.addAction(cancelAction)
        let ownSelf = Utils.getCurrentViewController() ?? UIViewController()
        DispatchQueue.main.async {
            ownSelf.present(alertController, animated: true, completion: nil)
        }
    }
    
    /// check是否打开了相机的访问权限
    class func checkCameraAuthorizationStatus(deniedOrRestricted: @escaping (_ isHaveCameraAuthorization: Bool)-> Void) {
        let cameraAuthorizationStatus = AVCaptureDevice.authorizationStatus(for: .video)
        
        switch cameraAuthorizationStatus {
        case .authorized:
            // 相机权限已授权
            DEBUGLOG.NSLog(message: "Camera access authorized.")
            deniedOrRestricted(true)
        case .notDetermined:
            // 尚未决定权限，您可以请求权限
            DEBUGLOG.NSLog(message: "Camera is notDetermined.")
            deniedOrRestricted(false)
        case .denied, .restricted:
            // 相机权限已拒绝或受限
            DEBUGLOG.NSLog(message: "Camera access denied or restricted.")
            deniedOrRestricted(false)
        @unknown default:
            // 处理未知情况
            DEBUGLOG.NSLog(message: "Unknown authorization status.")
            deniedOrRestricted(false)
        }
    }
    
    class func setCloseBtnColor(permissionStatus: AVAuthorizationStatus, closeBtn: UIButton) {
        if permissionStatus == .authorized {
            closeBtn.tintColor = Utils.dynamicColor(dark: .white, light: .black)
            closeBtn.setImage(UIImage(named: "baseline_close_black"), for: UIControl.State.normal)
        } else {
            closeBtn.tintColor = .white
            closeBtn.setImage(UIImage(named: "scan_close"), for: UIControl.State.normal)
        }
    }
    
    // iPad dose not has torch, so set lightButton hidden
    class func deviceHasTorch() -> Bool {
        let devices: AVCaptureDevice? = AVCaptureDevice.default(for: .video)
        if devices != nil && devices!.hasTorch {
            return true
        } else {
            return false
        }
    }
    
    class func device(with mediaType: AVMediaType, preferringPosition position: AVCaptureDevice.Position) -> AVCaptureDevice? {
        let devices = AVCaptureDevice.DiscoverySession(deviceTypes: [.builtInDualCamera, .builtInWideAngleCamera], mediaType: mediaType, position: position).devices
        var captureDevice: AVCaptureDevice? = devices.first
        for device in devices where device.position == position {
            captureDevice = device
            break
        }
        return captureDevice
    }
    
    class func formatVal(scanItems: [ScanItem]) -> [ScanItem] {
        for scanItem in scanItems {
            let itemValue = setCommaDecimalPoint(itemOption: scanItem.itemOption ?? "", itemType: scanItem.itemType ?? "", itemValue: scanItem.itemVal ?? "")
            scanItem.itemVal = itemValue
        }

        return scanItems
    }
}
