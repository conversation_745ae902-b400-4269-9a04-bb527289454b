<!--
 * @Author: your name
 * @Date: 2020-06-23 16:42:41
 * @LastEditTime: 2020-07-21 10:47:10
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /asset-force/src/app/asset-type/asset-type.page.html
-->
<div class="global-bg"></div>

<ion-header
  class="header"
  translucent="true">
  <ion-toolbar class="title-toolbar">
    <ion-buttons slot="start">
      <ion-back-button
        defaultHref="/tabs/assets-list"
        icon="chevron-back"
        text="">
      </ion-back-button>
    </ion-buttons>
    <ion-title>資産種類選択</ion-title>
    <ion-buttons
      *ngIf="menuVisible === true"
      slot="end">
      <ion-button
        (click)="qrcodeScan()"
        class="icon-button mr-2">
        <ion-icon
          class="icon-20"
          slot="icon-only"
          src="assets/images/header/qrcode-scan.svg"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>

  <ion-toolbar class="title-toolbar">
    <!-- searchbar -->
    <div class="input-group searchbar searchbar-assets-type px-3">
      <span class="input-group-prepend pr-0">
        <button
          class="btn btn-outline bg-white text-primary pl-2 pr-1 pt-0 pb-0 shadow-none no-opacity d-flex align-items-center"
          disabled
          type="button">
          <ion-icon src="./assets/images/toolbar/icon-search.svg"></ion-icon>
        </button>
      </span>
      <ion-input
        (ionInput)="valueChange($event.target.value)"
        class="form-control pl-0 searchbar-assets-type"
        placeholder="資産種類を検索"
        type="search" />
    </div>
  </ion-toolbar>
</ion-header>

<ion-content
  #content
  (ionScroll)="onScroll($event)"
  [scrollEvents]="true">
  <!--資産あり（検索していない or 検索結果あり）-->
  <div
    class="container layout-default"
    style="padding-bottom: unset !important">
    <div
      [class.hidden-div]="!isContentViewDisplay"
      class="scrollingContainer invisible-scrollbar"
      style="overflow: hidden; position: relative">
      <div
        [style.height.px]="phantomHeight"
        class="phantomContainer"></div>
      <div
        [style.transform]="getTransform()"
        class="actualContent invisible-scrollbar">
        <ng-container *ngFor="let idx of rangeArr">
          <div
            #slidingItems
            [id]="idx"
            class="ion-item-sliding-container">
            <div
              class="list-container list-container-right-icon"
              style="margin-bottom: 10px !important">
              <div
                (click)="back(getItem(idx))"
                class="list-item">
                <div class="list-body has-icon">
                  <span class="line-clamp-3 searchbar-assets-type-title">{{ getItem(idx).assetTypeName }}</span>
                  <div class="icon-content">
                    <img
                      alt=""
                      class="icon-next"
                      src="assets/images/float-icon/angle-right.svg" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ng-container>
      </div>
    </div>
  </div>
  <!--一覧なし-->
  <div
    *ngIf="isEmpty"
    class="empty-message">
    <p class="empty-message-title">データがありません</p>
  </div>

  <!--全件表示されました 非限制情况noMore-->
  <ng-template [ngIf]="isNoMoreData">
    <ion-row style="margin-bottom: 1.875rem">
      <ion-col class="text-center text-white font-weight-light all-data-assets-list"> 全件表示されました</ion-col>
    </ion-row>
  </ng-template>
</ion-content>

<ion-footer class="page-footer ion-no-border">
  <ion-toolbar>
    <div class="footer-buttons">
      <ion-button
        class="footer-button"
        color="blue-default"
        expand="block"
        fill="outline"
        routerDirection="root"
        routerLink="/tabs/assets-list"
        size="small">
        キャンセル
      </ion-button>
    </div>
  </ion-toolbar>
</ion-footer>
