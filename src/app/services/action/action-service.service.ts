import { Injectable } from '@angular/core';
import { NavigationExtras } from '@angular/router';
import { AlertController, NavController } from '@ionic/angular';
import { HttpActionService } from 'src/app/httpUtils/action/http-action.service';
import {
  AssetActionData,
  AssetActionInsertData,
  SavedInventoryAssetActionInfoInterface,
  AssetListByProcessIdInterface,
  AssetAction,
} from 'src/app/models/assetAction';
import { AssetService } from '../asset/asset.service';
import { ConstService } from '../workflow/workflow-service/other/wf-const.service';
import { ActionLabelAndIcon } from '../../models/ActionLabelAndIcon';
import { ActionListType } from '../../models/ActionListType';
import { StorageUtils } from 'src/app/utils/storage-utils';
import { Store } from '@ngxs/store';
import _ from 'lodash';
import { AlertButton } from '@ionic/core';
import { ActionNewProcessIdAdd, ActionNewProcessIdAddState } from 'src/app/services/action/action-service.actions';
import { LoginService } from 'src/app/services/login/login.service';
import { HttpUtilsService } from 'src/app/utils/http-utils.service';
import { HttpParams } from '@angular/common/http';

@Injectable({
  providedIn: 'root',
})
export class ActionService {
  constructor(
    private actionService: HttpActionService,
    private alertController: AlertController,
    private assetService: AssetService,
    private navController: NavController,
    private store: Store,
    private loginService: LoginService,
    private http: HttpUtilsService,
  ) {}

  // 抽出条件の演算子（メソッド）を定義する
  public static M = {
    include: { value: 'include', text: 'キーワードを含む', display: '{itemName}は「{searchData}」を含む' },
    notInclude: {
      value: 'notInclude',
      text: 'キーワードを含まない',
      display: '{itemName}は「{searchData}」を含まない',
    },

    equeue: { value: 'equeue', text: 'キーワードと一致する', display: '{itemName}は「{searchData}」と一致する' },
    equals: { value: 'equals', text: 'キーワードと一致する', display: '{itemName}は「{searchData}」と一致する' },

    unequeue: {
      value: 'unequeue',
      text: 'キーワードと一致しない',
      display: '{itemName}は「{searchData}」と一致しない',
    },
    unequals: {
      value: 'unequals',
      text: 'キーワードと一致しない',
      display: '{itemName}は「{searchData}」と一致しない',
    },

    includeFirst: {
      value: 'includeFirst',
      text: 'キーワードで始まる',
      display: '{itemName}は「{searchData}」で始まる',
    },
    includeLast: { value: 'includeLast', text: 'キーワードで終わる', display: '{itemName}は「{searchData}」で終わる' },

    dateUp: { value: 'dateUp', text: '次より後', display: '{itemName}は{searchData}より後' },
    dateUpDynamic: { value: 'dateUpDynamic', text: '次より後', display: '{itemName}は{searchData}より後' },

    dateDown: { value: 'dateDown', text: '次より前', display: '{itemName}は{searchData}より前' },
    dateDownDynamic: { value: 'dateDownDynamic', text: '次より前', display: '{itemName}は{searchData}より前' },

    dateUpEqu: { value: 'dateUpEqu', text: '以降', display: '{itemName}は{searchData}以降' },
    dateUpEquDynamic: { value: 'dateUpEquDynamic', text: '以降', display: '{itemName}は{searchData}以降' },

    dateDownEqu: { value: 'dateDownEqu', text: '以前', display: '{itemName}は{searchData}以前' },
    dateDownEquDynamic: { value: 'dateDownEquDynamic', text: '以前', display: '{itemName}は{searchData}以前' },

    dateInRange: { value: 'dateInRange', text: '次の範囲内', display: '{itemName}は{searchData}の範囲内' },
    dateEqu: { value: 'dateEqu', text: '次の日付', display: '{itemName}は{searchData}に等しい' },
    dateEquDynamic: { value: 'dateEquDynamic', text: '次の日付', display: '{itemName}は{searchData}に等しい' },

    dateUnEqu: { value: 'dateUnEqu', text: '以外', display: '{itemName}は{searchData}以外' },
    dateUnEquDynamic: { value: 'dateUnEquDynamic', text: '以外', display: '{itemName}は{searchData}以外' },

    dateSetting: { value: 'dateSetting', text: '指定日付期限', display: '{itemName}は{searchData}ヵ月以内' },
    todaySetting: { value: 'todaySetting', text: '今日', display: '{itemName}は今日' },
    todayUpSetting: { value: 'todayUpSetting', text: '明日以降', display: '{itemName}は明日以降' },
    dateThisMonth: { value: 'dateThisMonth', text: '今月', display: '{itemName}は今月' },
    dateNextMonth: { value: 'dateNextMonth', text: '来月', display: '{itemName}は来月' },
    dateThisYear: { value: 'dateThisYear', text: '今年', display: '{itemName}は今年' },
    dateNextYear: { value: 'dateNextYear', text: '来年', display: '{itemName}は来年' },
    dateEqualBeforeByDay: { value: 'dateEqualBeforeByDay', text: '〇日前', display: '{itemName}は{searchData}日前' },
    dateEqualAfterByDay: { value: 'dateEqualAfterByDay', text: '〇日後', display: '{itemName}は{searchData}日後' },
    dateBeforeByDay: { value: 'dateBeforeByDay', text: '〇日前以前', display: '{itemName}は{searchData}日前以前' },
    dateAfterByDay: { value: 'dateAfterByDay', text: '〇日後以後', display: '{itemName}は{searchData}日後以後' },
    dateAfterLastByDay: {
      value: 'dateAfterLastByDay',
      text: '過去〇日間',
      display: '{itemName}は過去の{searchData}日間',
    },
    dateBeforeNextByDay: {
      value: 'dateBeforeNextByDay',
      text: '翌〇日間',
      display: '{itemName}は翌の{searchData}日間',
    },
    dateEqualBeforeByWeek: { value: 'dateEqualBeforeByWeek', text: '〇週前', display: '{itemName}は{searchData}週前' },
    dateEqualAfterByWeek: { value: 'dateEqualAfterByWeek', text: '〇週後', display: '{itemName}は{searchData}週後' },
    dateBeforeByWeek: { value: 'dateBeforeByWeek', text: '〇週前以前', display: '{itemName}は{searchData}週前以前' },
    dateAfterByWeek: { value: 'dateAfterByWeek', text: '〇週後以後', display: '{itemName}は{searchData}週後以後' },
    dateAfterLastByWeek: {
      value: 'dateAfterLastByWeek',
      text: '過去〇週間',
      display: '{itemName}は過去の{searchData}週間',
    },
    dateBeforeNextByWeek: {
      value: 'dateBeforeNextByWeek',
      text: '翌〇週間',
      display: '{itemName}は翌の{searchData}週間',
    },
    dateEqualBeforeByMonth: {
      value: 'dateEqualBeforeByMonth',
      text: '〇ヵ月前',
      display: '{itemName}は{searchData}ヵ月前',
    },
    dateEqualAfterByMonth: {
      value: 'dateEqualAfterByMonth',
      text: '〇ヵ月後',
      display: '{itemName}は{searchData}ヵ月後',
    },
    dateBeforeByMonth: {
      value: 'dateBeforeByMonth',
      text: '〇ヵ月前以前',
      display: '{itemName}は{searchData}ヵ月前以前',
    },
    dateAfterByMonth: {
      value: 'dateAfterByMonth',
      text: '〇ヵ月後以後',
      display: '{itemName}は{searchData}ヵ月後以後',
    },
    dateAfterLastByMonth: {
      value: 'dateAfterLastByMonth',
      text: '過去〇ヵ月間',
      display: '{itemName}は過去の{searchData}ヵ月間',
    },
    dateBeforeNextByMonth: {
      value: 'dateBeforeNextByMonth',
      text: '翌〇ヵ月間',
      display: '{itemName}は翌の{searchData}ヵ月間',
    },
    dateAfterLastByYear: {
      value: 'dateAfterLastByYear',
      text: '過去〇年間',
      display: '{itemName}は過去の{searchData}年間',
    },
    dateBeforeNextByYear: {
      value: 'dateBeforeNextByYear',
      text: '翌〇年間',
      display: '{itemName}は翌の{searchData}年間',
    },

    numberUp: { value: 'numberUp', text: '次より大きい', display: '{itemName}は{searchData}より大きい' },
    numberUpDynamic: { value: 'numberUpDynamic', text: '次より大きい', display: '{itemName}は{searchData}より大きい' },

    numberDown: { value: 'numberDown', text: '次より小さい', display: '{itemName}は{searchData}より小さい' },
    numberDownDynamic: {
      value: 'numberDownDynamic',
      text: '次より小さい',
      display: '{itemName}は{searchData}より小さい',
    },

    numberUpEqu: { value: 'numberUpEqu', text: '以上', display: '{itemName}は{searchData}以上' },
    numberUpEquDynamic: { value: 'numberUpEquDynamic', text: '以上', display: '{itemName}は{searchData}以上' },

    numberDownEqu: { value: 'numberDownEqu', text: '以下', display: '{itemName}は{searchData}以下' },
    numberDownEquDynamic: { value: 'numberDownEquDynamic', text: '以下', display: '{itemName}は{searchData}以下' },

    numberEqu: { value: 'numberEqu', text: '等しい', display: '{itemName}は{searchData}に等しい' },
    numberEquDynamic: { value: 'numberEquDynamic', text: '等しい', display: '{itemName}は{searchData}に等しい' },

    numberUnEqu: { value: 'numberUnEqu', text: '等しくない', display: '{itemName}は{searchData}に等しくない' },
    numberUnEquDynamic: {
      value: 'numberUnEquDynamic',
      text: '等しくない',
      display: '{itemName}は{searchData}に等しくない',
    },

    unEquBlank: { value: 'unEquBlank', text: '空白ではない場合', display: '{itemName}は空白ではない場合' },
    equBlank: { value: 'equBlank', text: '空白の場合', display: '{itemName}は空白の場合' },

    listInclude: {
      value: 'listInclude',
      text: '選択した項目のいずれかを含む',
      display: '{itemName}は「{searchData}」に含まれる',
    },
    list: { value: 'list', text: '選択した項目のいずれかを含む', display: '{itemName}は「{searchData}」に含まれる' },

    listUnInclude: {
      value: 'listUnInclude',
      text: '選択した項目を含まない',
      display: '{itemName}は「{searchData}」に含まれない',
    },

    checked: { value: 'checked', text: 'チェックあり', display: '' },

    unChecked: { value: 'unChecked', text: 'チェックなし', display: '' },

    userInclude: { value: 'userInclude', text: '選択した担当者を含む', display: '' },

    userUnInclude: { value: 'userUnInclude', text: '選択した担当者を含まない', display: '' },

    checkboxInclude: { value: 'checkboxInclude', text: '選択した項目のいずれかを含む', display: '' },

    checkboxUnInclude: { value: 'checkboxUnInclude', text: '選択した項目を含まない', display: '' },
  };

  /**
   * 抽出条件の演算子取得
   * @param methodKey 抽出条件キー
   */
  getActionConditionMethodText(methodKey: string): string {
    const text = Object.values(ActionService.M).find((m) => m.value === methodKey)?.text;
    return text || '';
  }

  /**
   * 抽出条件の値取得
   * @param searchData 抽出条件の値
   * @param methodKey 抽出条件キー
   */
  getSearchData(searchData: any, method: string, display: string) {
    if (['listInclude', 'listUnInclude'].indexOf(method) >= 0) {
      if (!searchData) {
        return '【未入力】';
      } else if (typeof searchData === 'string') {
        return searchData
          .split(',')
          .map((item) => {
            if (item == '') {
              item = '【未入力】';
            }
            return item;
          })
          .join(',');
      } else {
        return searchData
          .map((item) => {
            if (item == '') {
              item = '【未入力】';
            }
            return item;
          })
          .join(',');
      }
    }
    // 复数个checkBox的时候和担当者
    if (['checkboxUnInclude', 'checkboxInclude', 'userUnInclude', 'userInclude'].indexOf(method) >= 0) {
      let displayStr = display.substring(display.indexOf('「') + 1, display.indexOf('」'));
      return displayStr === '' ? '【未入力】' : displayStr;
    }

    if (searchData != undefined && searchData != null) {
      if (searchData instanceof Array) {
        let str = searchData.join('、');
        return str;
      } else {
        if (searchData == 'today') {
          return '今日';
        } else if (searchData == 'now') {
          return '現在';
        } else if (searchData == 'currentUserName') {
          return 'ログインユーザーのユーザー名';
        } else {
          return searchData;
        }
      }
    } else {
      return '';
    }
  }

  // 一時保存された件数
  async getActionListCount(processType: number): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const result1 = await this.actionService.httpGetAssetActionDataListCount(processType);
      resolve(result1);
    });
  }

  // 一時保存された件数
  async getSelectedAssetList(processId: string): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const result1 = await this.actionService.httpGetSelectedAssetList(processId);
      resolve(result1);
    });
  }

  // 一時保存、実行完了
  async getActionSaveListData(
    processType: number,
    skip: number,
    row: number,
    searchText: string,
    needLoading = true,
    delay = true,
  ): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const result1 = await this.actionService.httpGetActioDataBySearchKey(
        processType,
        skip,
        row,
        searchText,
        needLoading,
        delay,
      );
      resolve(result1);
    });
  }

  async getActionOfflineListData(
    skip: number,
    row: number,
    searchText: string,
  ): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const result1 = await this.actionService.httpGetOfflineActioDataBySearchKey(
        skip,
        row,
        searchText
      );
      resolve(result1);
    });
  }

  // 一時保存AllData
  async getAllActionSaveListData(processType: number): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const result1 = await this.actionService.httpGetAllAssetActionDataListCountSaveData(processType);
      resolve(result1);
    });
  }

  // 処理一覧
  async getActionListData(skip: number, row: number, searchKey: string): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const result1 = await this.actionService.httpGetActionData(skip, row);
      resolve(result1);
    });
  }

  // 処理一覧画面で、キーワードで一覧を検索
  async getActionListBySearchKey(
    skip: number,
    row: number,
    searchKey: string,
    actionListType: ActionListType,
    actionLabel: string,
  ): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const result1 = await this.actionService.httpGetActioListBySearchKey(
        skip,
        row,
        searchKey,
        actionListType,
        actionLabel,
      );
      resolve(result1);
    });
  }

  // 処理一覧全部数据
  async getAllActionListData(): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const result1 = await this.actionService.httpGetActionDataAll();
      resolve(result1);
    });
  }

  // 資産IDで資産項目リストをとる
  async getItemByAppurtenancesInformationType(id: number): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const result1 = await this.actionService.httpGetItemByAppurtenancesInformationType(id);
      resolve(result1);
    });
  }

  // 資産情報を取得
  async getItemByAssetType(assetTypeId: number): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const result1 = await this.actionService.httpGetItemByAssetType(assetTypeId);
      resolve(result1);
    });
  }

  //アクション 一時保存 削除
  async deleteActionByAssetActionId(assetActionId: string, processId: string): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const result1 = await this.actionService.httpActionAssetActionListDelete(assetActionId, processId);
      resolve(result1);
    });
  }

  // Master一覧
  async getActionMasterData(masterTypeId: number | null, needLoading = true): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const result1 = await this.actionService.httpGetActionMasterData(masterTypeId, needLoading);
      resolve(result1);
    });
  }

  /**
   * 脚本专用 Master List 取得接口
   * @param masterTypeId [number]
   * @param needLoading [bool]
   * @param delay [bool]
   * @returns data
   */
  async getMasterInfoById(masterTypeId: number, needLoading = true, delay = false): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const result1 = await this.actionService.httpGetMasterInfoById(masterTypeId, needLoading, delay);
      resolve(result1);
    });
  }

  /**
   * Master List 取得（ASCHK-28350 非表示专用接口）
   * @param masterTypeId [number]
   * @param needLoading [bool]
   * @param delay [bool]
   * @returns data
   */
  async getRevisedMasterInfoById(masterTypeId: number, needLoading = true, delay = false): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const result1 = await this.actionService.httpRevisedGetMasterInfoById(masterTypeId, needLoading, delay);
      resolve(result1);
    });
  }

  /**
   * 用户改变处理设定状态警告
   * @param item
   */
  async isOnlineModeChangeWarning() {
    const alert = await this.alertController.create({
      header: '設定が変更されたためモバイルから処理を実行できません',
      message: 'Webにて権限設定を戻してから処理を実行しなおしてください。',
      buttons: [
        {
          text: 'OK',
          cssClass: 'font-weight-bold',
          handler: () => {
            // This is intentional
          },
        },
      ],
      backdropDismiss: false,
    });
    await alert.present();
  }

  // 処理設定1回目から一時保存update
  async updateAssetActionData(assetActionData: AssetActionData): Promise<any> {
    return new Promise(async (resolve, reject) => {
      try {
        const result1 = await this.actionService.httpUpdateAssetActionData(assetActionData);
        resolve(result1);
      } catch  (error) {
        // 暂时为了保障寄存业务正常，只限制离线扫描技能判断
        if (error['msg'] == 'ONLINE_MODE_CHANGE_WARNING' && error['code'] == 111 ) {
          await this.isOnlineModeChangeWarning();
        }
      }
    });
  }

  // 処理設定1回目から一時保存insert
  async insertAssetActionData(assetActionData: AssetActionData | AssetActionInsertData): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const result1 = await this.actionService.httpInsertAssetActionData(assetActionData);
      resolve(result1);
    });
  }

  async getGroupColumn(assetTypeId: string, searchId: string): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const result1 = await this.actionService.httpGetGroupColumn(assetTypeId, searchId);
      resolve(result1);
    });
  }

  // 実行
  async transitAssetActionData(assetActionData: AssetActionData): Promise<any> {
    console.log('******实行数据**********');
    console.log(assetActionData);
    return new Promise(async (resolve, reject) => {
      const result1 = await this.actionService.httpTransitAssetActionData(assetActionData);
      resolve(result1);
    });
  }

  async updateTransitAssetActionData(assetActionData: AssetActionData): Promise<any> {
    console.log('******实行数据**********');
    console.log(assetActionData);
    return new Promise(async (resolve, reject) => {
      try {
        const result1 = await this.actionService.httpUpdateTransitAssetActionData(assetActionData);
        resolve(result1);
      } catch (error) {
        // 暂时为了保障寄存业务正常，只限制离线扫描技能判断
        if (error['msg'] == 'ONLINE_MODE_CHANGE_WARNING' && error['code'] == 111 ) {
          await this.isOnlineModeChangeWarning();
        }
      }
    });
  }

  // ダイナミックな資産項目リストをとる
  async actionGetCustomItem(): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const result1 = await this.actionService.actionGetCustomItem();
      resolve(result1);
    });
  }

  async getAssetDataForAction(actionid, assetIds: any[]): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const result1 = await this.actionService.actionGetAssetDeta(actionid, assetIds);
      resolve(result1);
    });
  }

  async getActionPreviewInfo(assetActionId: string, assetList: any[], moveTo: string): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const result1 = await this.actionService.httpActionPreviewInfo(assetActionId, assetList, moveTo);
      resolve(result1);
    });
  }

  async actionGetBarcodeAndLocation(assetTypeId: string, willUpdateLocation: string, requestBody: any): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const result1 = await this.actionService.httpActionGetBarcodeAndLocation(
        assetTypeId,
        willUpdateLocation,
        requestBody,
      );
      resolve(result1);
    });
  }

  findAssetsByKeyword(assetTypeId, keyword, skip, rows, assetIds, needLoading = true): Promise<any> {
    return new Promise(async (resolve, reject) => {
      this.actionService.httpFindAssetsByKeyword(assetTypeId, keyword, skip, rows, assetIds, needLoading).then(
        (result) => {
          resolve(result);
        },
        (error) => {
          resolve(null);
        },
      );
    });
  }

  // 資産項目リストをとる
  async actionGetAssetItemCommon(needLoading = true): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const result1 = await this.actionService.actionGetAssetItemCommon(needLoading);
      resolve(result1);
    });
  }

  checkInputRequired(assetActionItemList) {
    var inputCheck = true;
    assetActionItemList.forEach((item) => {
      if ((item.method == '2' && item.itemValue == 'currentUserInput') || item.editable) {
        item['inputRequired'] = false;
        if (item.defaultData == 'Invalid date' || item.defaultData == '' || item.defaultData == 'currentUserInput') {
          inputCheck = false;
          item['inputRequired'] = true;
        }
      }
    });

    if (!inputCheck) {
      this.alertInputRequired();
      return false;
    }
    {
      assetActionItemList.forEach((item) => {
        delete item['inputRequired'];
      });
    }

    return true;
  }

  /**
   * 実行時に入力する設定値に未入力の項目が残っている
   * アラート
   */
  async alertInputRequired() {
    const alert = await this.alertController.create({
      message: `実行時に入力する設定値に未入力の項目が残っているため、処理実行後の設定値を入力してください。`,
      cssClass: 'alert-title-primary',
      buttons: [
        {
          text: '閉じる',
          handler: () => {
            // This is intentional
          },
        },
      ],
    });
    await alert.present();
  }

  // 資産詳細に遷移する。
  async gotoAssetDetail(asset, fromType, params, registeredList) {
    let urlMap = {
      action_list_scaned_list: {
        fromScanPage: ConstService.ACTION_SCANED_LIST,
        currentPage: '/action/list/preview-inside',
      },
    };
    var fromScanPage = urlMap[fromType].fromScanPage;
    var currentPage = urlMap[fromType].currentPage;
    if (!fromScanPage && !currentPage) {
      return;
    }

    const result1 = await this.assetService.getAssetAuthority(asset.assetId, asset.assetTypeId);
    if (result1.count === 1) {
      let navigationExtras: NavigationExtras = {
        queryParams: {
          assetText: asset.assetText,
          assetId: asset.assetId,
          assetTypeId: asset.assetTypeId,
          fromScanPage: fromScanPage,
          backUrl: currentPage,
          params: params,
          registeredList: registeredList,
        },
      };
      this.navController.navigateForward('/asset/detail', navigationExtras);
    } else {
      alert('資産が削除された又は権限が変更された可能性があるので、処理できません。');
    }
  }

  /**
   * @description:amountActionTaskUpdateItemName
   * @param {type}
   * @return {type}
   */
  minusClick(item: any, canItBe0 = false) {
    item.assetScanedCount = _.isNumber(item.assetScanedCount)
      ? item.assetScanedCount
      : _.toNumber(item.assetScanedCount);
    item.assetScanedCount -= 1;
    if (canItBe0) {
      if (item.assetScanedCount < 0) {
        item.assetScanedCount = 0;
      }
    } else {
      this.checkAssetCount(item);
    }
    return item;
  }
  /**
   * @description:
   * @param {type}
   * @return {type}
   */
  plusClick(item: any) {
    item.assetScanedCount = _.isNumber(item.assetScanedCount)
      ? item.assetScanedCount
      : _.toNumber(item.assetScanedCount);
    item.assetScanedCount += 1;
    return item;
  }

  /**
   *
   * @param item 在庫数量はマイナスにならないように
   */
  checkAssetCount(item) {
    if (item.assetScanedCount < 1) {
      item.assetScanedCount = 1;
    }
  }

  /**
   * @description:
   * @param {type}
   * @return {type}
   */
  updateCount(item: any, $event, amountType4: boolean = false): any {
    const hankakudata = this.hankaku2Zenkaku($event.target.value).replace(/\b(0+)/gi, '');
    if (hankakudata != null) {
      if (this.isIntegerAll(hankakudata)) {
        item.assetScanedCount = Number(hankakudata);
      } else {
        if (amountType4) {
          item.assetScanedCount = 0;
        } else {
          item.assetScanedCount = 1;
        }
      }
    }
    return item;
  }

  //全角⇨半角
  hankaku2Zenkaku(str) {
    return str.replace(/[Ａ-Ｚａ-ｚ０-９]/g, function (s) {
      return String.fromCharCode(s.charCodeAt(0) - 0xfee0);
    });
  }

  /**
   * 正則表現で数字のみかチェック
   * @param value
   */
  isIntegerAll(value) {
    var z_reg = /^\d+$/;
    return z_reg.test(value);
  }

  /**
   *
   * @param item 数字のみの場合
   * @param amount
   */
  isWhenAllInteger(item, amount) {
    item.assetScanedCount = Number(amount);
  }

  /**
   * 数字のみではない場合
   * @param item
   */
  async isWhenNotInteger(item, hankakudata) {
    item.assetScanedCount = Number(hankakudata);
    const alert = await this.alertController.create({
      header: '内容に不備があります',
      message: '表示されたメッセージをご確認の上、もう一度登録してください。',
      buttons: [
        {
          text: 'OK',
          cssClass: 'font-weight-bold',
          handler: () => {
            // This is intentional
          },
        },
      ],
      backdropDismiss: false,
    });
    await alert.present();
  }

  // 処理のラベルとアイコンを取得
  getActionLabelAndIcon(customErrorCallback?: () => Promise<any>): Promise<ActionLabelAndIcon[]> {
    return this.actionService.httpGetActionLabelAndIcon(customErrorCallback);
  }

  getAssetActionById(assetActionId: number, scanFlg = ''): Promise<any> {
    return new Promise<any>((resolve, reject) => {
      this.actionService
        .httpGetAssetActionById(assetActionId, scanFlg)
        .then((data) => {
          resolve(data);
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  getOfflineGetAssetActionById(assetActionId: string): Promise<any> {
    return new Promise<any>((resolve, reject) => {
      this.actionService
        .httpOfflineGetAssetActionById(assetActionId)
        .then((data) => {
          resolve(data);
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  /**
   * 一时保存
   * @param processId 核心查询数据
   * @param withAssetSumInfo 是否返回资产列表
   * @returns
   */
  getSavedInventoryAssetActionInfo(
    processId: number,
    withAssetSumInfo: boolean = false,
    needLoading = true,
    delay = true,
  ): Promise<SavedInventoryAssetActionInfoInterface> {
    return new Promise<SavedInventoryAssetActionInfoInterface>((resolve, reject) => {
      this.actionService
        .httpGetSavedInventoryAssetActionInfo(processId, withAssetSumInfo, needLoading, delay)
        .then((data: SavedInventoryAssetActionInfoInterface) => {
          resolve(data);
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  /**
   * 实行前确认界面的对比数据.
   * @param actionId
   * @returns
   */
  getCompareInfoForPreview(actionId: string, assetIds: any[]): Promise<any> {
    return new Promise<any>((resolve, reject) => {
      this.actionService
        .httpGetCompareInfoForPreview(actionId, assetIds)
        .then(
          (result) => {
            if (result && result.code === 0) {
              resolve(result.assetInfoList);
            } else {
              resolve(null);
            }
          },
          (error) => {
            resolve(null);
          },
        )
        .catch((error) => {
          resolve(null);
        });
    });
  }

  /**
   * 棚卸解锁
   * @param processId
   * @returns
   */
  unlockAssetActionData(processId: number): Promise<any> {
    return new Promise<any>((resolve, reject) => {
      this.actionService.httpPostUnlockAssetActionData(processId).then((data) => {
        resolve(data);
      });
    });
  }

  /**
   * 棚卸锁定
   * @param processId
   * @returns
   */
  lockAssetActionData(processId: number): Promise<any> {
    return new Promise<any>((resolve, reject) => {
      this.actionService.httpPostLockAssetActionData(processId).then((data) => {
        resolve(data);
      });
    });
  }

  /**
   * 棚卸取到所有扫描汇总数组
   * @param processId
   * @returns
   */
  getAssetListByProcessId(processId: number): Promise<AssetListByProcessIdInterface> {
    return new Promise<AssetListByProcessIdInterface>((resolve, reject) => {
      this.actionService.httpGetAssetListByProcessId(processId).then((data: AssetListByProcessIdInterface) => {
        resolve(data);
      });
    });
  }

  /**
   * 校验是否提前被人锁定或者完了
   * @param {boolean} isFromHome 是否从home页面进入
   * @param {number} processId
   * @returns { titleStr: string, isLock: boolean }
   */
  async lockAndunlockDisplayClick(
    isFromHome: boolean,
    processId: number,
  ): Promise<{ titleStr: string; isLock: boolean }> {
    try {
      const inventoryAmountAlert = await this.inventoryAmountAlertC();
      const openAl = await inventoryAmountAlert?.alertOpen;

      if (inventoryAmountAlert?.isExecuted) {
        await openAl.present();
        openAl.onDidDismiss().then((data) => {
          if (data['role'] === 'cancel') {
            if (isFromHome) {
              this.navController.navigateForward('/tabs/my-home');
            } else {
              this.navController.navigateForward('/tabs/task');
            }
          }
        });
        return undefined;
      }
      if (!_.isUndefined(inventoryAmountAlert) && !inventoryAmountAlert.isLoginUser) {
        await openAl.present();
        return undefined;
      }
      const unlock = await this.lockAndunlockActionFun(processId, inventoryAmountAlert?.isLocked ? false : true);
      return unlock;
    } catch (error) {
      console.error('asset-force/src/app/services/action/action-service.service.ts');
      console.error('lockAndunlockDisplayClick');
      console.error(error);
    }
  }

  /**
   * 棚卸专用，锁定与解锁封装
   * @param {number} processId
   * @param {boolean} isLock
   * @returns { titleStr: string; isLock: boolean  }
   */
  async lockAndunlockActionFun(processId: number, isLock: boolean): Promise<{ titleStr: string; isLock: boolean }> {
    // 锁定 & 解锁 弹窗方法封装
    const confirmationAlert = async (header: string, message: string): Promise<boolean> => {
      let resolveFunction: (confirm: boolean) => void;
      const promise = new Promise<boolean>((resolve) => {
        resolveFunction = resolve;
      });
      const alert = await this.alertController.create({
        header: header,
        message: message,
        backdropDismiss: false,
        buttons: [
          {
            text: 'キャンセル',
            role: 'cancel',
            handler: () => resolveFunction(false),
          },
          {
            text: 'OK', // TODO 与 figma 中的不一致 figma是 OK
            role: 'okAnt',
            handler: () => resolveFunction(true),
          },
        ],
      });
      await alert.present();
      return promise;
    };

    // 锁定&解锁 网络请求
    let result = undefined;
    if (isLock) {
      const confirm = await confirmationAlert(
        '処理を確定しますか？',
        'この操作は、処理の実行前であれば元に戻すことができます。',
      );
      if (confirm) {
        // 锁定请求
        result = await this.lockAssetActionData(processId);
      }
    } else {
      const confirm = await confirmationAlert('確定を解除しますか？', '');
      if (confirm) {
        // 解锁请求
        result = await this.unlockAssetActionData(processId);
      }
    }

    // 判断请求是否成功
    if (_.isEmpty(result)) {
      return;
    }
    if (!_.isEqual(_.toString(result['code']), '0')) {
      return;
    }

    if (isLock) {
      // 当用户点击锁定以后相应的确定按钮转换成确认解除文言
      return { titleStr: '確定を解除', isLock: false };
    } else {
      // 当用户点击解除锁定以后相应的确认解除文言按钮转换成确认
      return { titleStr: '確定', isLock: true };
    }
  }

  /**
   * 棚卸专用 获取处理设定状态，也可以用于处理设定固定弹窗
   * @param {boolean} isPreviewConfirmPage 是否为执行页面（src/app/action/list/preview-confirm/preview-confirm.page.ts）
   * @returns { isLoginUser: boolean, isLocked: boolean, isExecuted: boolean, alertOpen: Promise<HTMLIonAlertElement> }
   */
  async inventoryAmountAlertC(isPreviewConfirmPage: boolean = false): Promise<{
    isLoginUser: boolean;
    isLocked: boolean;
    isExecuted: boolean;
    alertOpen: Promise<HTMLIonAlertElement>;
  }> {
    const alert = async (header: string, title: string, buttons: AlertButton[]): Promise<HTMLIonAlertElement> => {
      const alert = await this.alertController.create({
        header: header,
        message: title,
        buttons: buttons,
        backdropDismiss: false,
      });
      return alert;
    };
    const getNewProcessId = await this.store.selectSnapshot(ActionNewProcessIdAddState.getNewProcessId);
    if (_.isEmpty(getNewProcessId)) {
      return undefined;
    }
    const processId = _.toNumber(getNewProcessId.processId);
    let result: SavedInventoryAssetActionInfoInterface;
    try {
      result = await this.getSavedInventoryAssetActionInfo(processId, false, true, false);
    } catch (error) {
      // 获取处理设定状态时 网络请求出点错误或者超时 处理方案
      console.error('inventoryAmountAlertC => getSavedInventoryAssetActionInfo error =>>>>>>>>>', error);
      let errMsg = 'システムエラーが発生しました。管理者にご連絡ください。';
      if (!_.isEmpty(error) && _.has(error, 'msg') && !_.isEmpty(error.msg) && _.toString(error['code']) === '107') {
        errMsg = error.msg;
      }
      let alertHandler = undefined;
      // 107当前处理设定已被删除
      if (_.toString(error['code']) === '107') {
        alertHandler = async () => {
          this.navController.navigateForward('/tabs/task');
        };
      }
      const alertERR = await this.alertController.create({
        header: errMsg,
        buttons: [
          {
            text: 'OK',
            role: 'cancel',
            handler: alertHandler,
          },
        ],
      });
      alertERR.present();

      // 向上层抛出ERR阻止调用此方法进行下一步操作
      throw new Error(error);
    }
    const userId = await StorageUtils.get(StorageUtils.KEY_USER_ID);
    const getIsLoginUser = _.isEqual(_.toString(result.lockUser), _.toString(userId));
    // 当前处理设定已经完了判断
    if (result.isExecuted) {
      const alertExecuted = alert(
        'この処理は完了済みです',
        '完了済みの処理は続行できないため、この処理は破棄されます。',
        [
          {
            text: 'OK',
            role: 'cancel',
          },
        ],
      );
      return {
        isLoginUser: getIsLoginUser,
        isLocked: result?.isLocked,
        isExecuted: result?.isExecuted,
        alertOpen: alertExecuted,
      };
    }
    // 是否锁定
    if (!result.isLocked) {
      // 未锁定
      return undefined;
    }
    // 当前登陆用户 和 锁定者 相同
    if (getIsLoginUser) {
      // 解锁方法
      const unlockFun = async () => {
        const unlockResult = await this.unlockAssetActionData(processId);
        if (_.isEmpty(unlockResult)) {
          return undefined;
        }
        if (!_.isEqual(_.toString(unlockResult['code']), '0')) {
          return undefined;
        }
      };
      let userLockedButtons;
      let userLockedHeaderMsg;
      let userLockedMessage;
      if (isPreviewConfirmPage) {
        // 在执行页面点击返回时候
        userLockedHeaderMsg = 'この処理は確定済みです';
        userLockedMessage = '処理内容を変更する場合には、確定を解除してください。';
        userLockedButtons = [
          {
            text: 'OK',
            role: 'cancel',
          },
        ];
      } else {
        // 在一时保存点击某一个处理设定
        userLockedHeaderMsg = '処理を再開しますか？';
        userLockedMessage = '再開する場合は、確定が解除されます。';
        userLockedButtons = [
          {
            text: 'キャンセル',
            role: 'cancel',
          },
          {
            text: 'OK',
            role: 'yes',
            handler: async () => {
              await unlockFun();
            },
          },
        ];
      }
      const userLocked = alert(userLockedHeaderMsg, userLockedMessage, userLockedButtons);
      return {
        isLoginUser: getIsLoginUser,
        isLocked: result?.isLocked,
        isExecuted: result?.isExecuted,
        alertOpen: userLocked,
      };
    }
    const noUserLocked = alert(
      'この処理は確定済みです',
      `確定担当者は ${result.lockUserName} さんです。処理を続ける場合には、${result.lockUserName} さんに解除を依頼してください。`,
      [
        {
          text: 'OK',
          role: 'cancel',
        },
      ],
    );
    return {
      isLoginUser: getIsLoginUser,
      isLocked: result?.isLocked,
      isExecuted: result?.isExecuted,
      alertOpen: noUserLocked,
    };
  }

  /**
   * home画像取得
   * @param {Asset[]} assetItemList
   * @param {AssetMobileSetting} assetTypeeSetting
   * @returns {Asset[]}
   */
  async getHomeImageFun(assetItemd: any, assetItemTypeSettingList: any[]): Promise<any> {
    let assetItemTypeSettingListClone = _.cloneDeep(assetItemTypeSettingList);
    let assetItemClone = _.cloneDeep(assetItemd);
    if (_.isString(assetItemClone.assetText)) {
      assetItemClone.assetText = JSON.parse(assetItemClone.assetText);
    }
    for (let index = 0; index < assetItemTypeSettingListClone.length; index++) {
      const assetItemTypeSetting = assetItemTypeSettingListClone[index];
      if (assetItemTypeSetting.itemType !== 'image' || assetItemTypeSetting.mobileFlg !== '1') {
        continue;
      }
      if (!_.has(assetItemClone.assetText, assetItemTypeSetting.itemName)) {
        continue;
      }
      const assetImageAry = assetItemClone.assetText[assetItemTypeSetting.itemName];
      if (!_.isArray(assetImageAry)) {
        continue;
      }
      if (assetImageAry.length === 0) {
        continue;
      }
      const assetImage = _.find(assetImageAry as any[], (n) => {
        return n['isHomeImage'] === true;
      });
      if (_.isEmpty(assetImage)) {
        continue;
      }
      // 过滤掉没有权限预览的home画像
      let optionObj = JSON.parse(assetItemTypeSetting.option);
      let sectionPrivateGroups = optionObj.sectionPrivateGroups;
      var isView = true;
      if (sectionPrivateGroups) {
        isView = await this.loginService.visiblePermissionsCheck(sectionPrivateGroups);
      }
      // assetItemTypeSetting.mobileFlg == '0' 设定为非表示项目的时候也不要在一览画面显示
      if (!isView || assetItemTypeSetting.mobileFlg == '0') {
        //没有权限预览
        continue;
      }
      assetImage['homeImgLoaded'] = false;
      assetItemClone.homeImage = assetImage;
    }
    return assetItemClone;
  }

  /**
   * 专为棚卸跳转准备
   */
  async goInventoryAmountType(data: AssetAction, callBackStratScan: () => void) {
    const alert = await this.alertController.create({
      header: '棚卸を開始しますか？',
      message: 'この処理は開始時点で一時保存されます。',
      buttons: [
        {
          text: 'キャンセル',
          cssClass: 'font-weight-normal',
          role: 'cancel',
        },
        {
          text: 'OK',
          cssClass: 'font-weight-bold',
          handler: async () => {
            await saveTemporarilyAction();
          },
        },
      ],
    });
    await alert.present();

    const saveTemporarilyAction = async () => {
      let assetActionData: AssetActionInsertData = {
        assetActionId: _.toNumber(data.assetActionId),
        assetTypeId: data.assetTypeId,
        customerActionName: '',
        assetList: JSON.stringify([]),
        processType: '0',
        isUpdateAmount: _.toString(data.isUpdateAmount),
        amountType: _.toString(data.amountType),
        assetActionItem: data.assetActionItem,
        appurtenancesInformationTypeInfo: data.appurtenancesInformationTypeInfo,
      };
      await this.http.addLoadingQueue();
      try {
        const result = await this.insertAssetActionData(assetActionData);
        if (_.isEmpty(result)) {
          throw '';
        }
        if (!_.isEqual(_.toString(result['code']), '0')) {
          throw '';
        }
        // 新规棚卸专用
        this.store.dispatch(new ActionNewProcessIdAdd(result['processId']));
        callBackStratScan();
        // await this.toScan();
      } finally {
        await this.http.removeLoadingQueue();
      }
    };
  }

  /**
   * 离线扫描处理设定提出用
   * @param timestamp
   * @param assetList
   * @param assetActionData
   * @returns {Promise<unknown>}
   */
  httpOfflineAssetActionBatch(timestamp: string, assetList, assetActionData) {
    return new Promise(async (resolve, reject) => {
      this.actionService
        .httpOfflineAssetActionBatch(timestamp, assetList, assetActionData)
        .then((value) => {
          resolve(value);
        })
        .catch( async (error) => {
          if (error['msg'] == 'ONLINE_MODE_CHANGE_WARNING' && error['code'] == 111 ) {
            await this.isOnlineModeChangeWarning();
          } else {
            reject(error);
          }
        });
    });
  }
}
