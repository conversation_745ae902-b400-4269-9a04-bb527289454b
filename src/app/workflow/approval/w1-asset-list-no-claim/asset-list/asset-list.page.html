<div class="global-bg"></div>

<ion-header
  translucent="true"
  class="header">
  <ion-toolbar class="title-toolbar">
    <ion-buttons slot="start">
      <ion-back-button
        defaultHref="/tabs/temp-task"
        text=""
        icon="chevron-back"></ion-back-button>
    </ion-buttons>
    <ion-title>資産情報</ion-title>
    <ion-buttons
      slot="end"
      class="mr-2">
      <ion-button
        class="icon-button"
        *ngIf="this.isScanTask"
        (click)="refresh()">
        <img
          *ngIf="this.isScanTask"
          alt="svgImg"
          src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHg9IjBweCIgeT0iMHB4Igp3aWR0aD0iMTgiIGhlaWdodD0iMTgiCnZpZXdCb3g9IjAgMCAxNzIgMTcyIgpzdHlsZT0iIGZpbGw6IzAwMDAwMDsiPjxnIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0ibm9uemVybyIgc3Ryb2tlPSJub25lIiBzdHJva2Utd2lkdGg9IjEiIHN0cm9rZS1saW5lY2FwPSJidXR0IiBzdHJva2UtbGluZWpvaW49Im1pdGVyIiBzdHJva2UtbWl0ZXJsaW1pdD0iMTAiIHN0cm9rZS1kYXNoYXJyYXk9IiIgc3Ryb2tlLWRhc2hvZmZzZXQ9IjAiIGZvbnQtZmFtaWx5PSJub25lIiBmb250LXdlaWdodD0ibm9uZSIgZm9udC1zaXplPSJub25lIiB0ZXh0LWFuY2hvcj0ibm9uZSIgc3R5bGU9Im1peC1ibGVuZC1tb2RlOiBub3JtYWwiPjxwYXRoIGQ9Ik0wLDE3MnYtMTcyaDE3MnYxNzJ6IiBmaWxsPSJub25lIj48L3BhdGg+PGcgZmlsbD0iI2ZmZmZmZiI+PHBhdGggZD0iTTY4LjgsMTMuNzZjLTE3LjAxMTg3LDAgLTMwLjk2LDEzLjk0ODEzIC0zMC45NiwzMC45NnY3Ny42MTVsLTE4LjE2NzUsLTE4LjE2NzVsLTQuOTQ1LDQuOTQ1bDI0LjA4LDI0LjA4bDIuNDcyNSwyLjM2NWwyLjQ3MjUsLTIuMzY1bDI0LjA4LC0yNC4wOGwtNC45NDUsLTQuOTQ1bC0xOC4xNjc1LDE4LjE2NzV2LTc3LjYxNWMwLC0xMy4yNjI4MSAxMC44MTcxOSwtMjQuMDggMjQuMDgsLTI0LjA4aDM3Ljg0di02Ljg4ek0xMzAuNzIsMzYuNDQyNWwtMi40NzI1LDIuMzY1bC0yNC4wOCwyNC4wOGw0Ljk0NSw0Ljk0NWwxOC4xNjc1LC0xOC4xNjc1djc3LjYxNWMwLDEzLjI2MjgxIC0xMC44MTcxOSwyNC4wOCAtMjQuMDgsMjQuMDhoLTM3Ljg0djYuODhoMzcuODRjMTcuMDExODgsMCAzMC45NiwtMTMuOTQ4MTIgMzAuOTYsLTMwLjk2di03Ny42MTVsMTguMTY3NSwxOC4xNjc1bDQuOTQ1LC00Ljk0NWwtMjQuMDgsLTI0LjA4eiI+PC9wYXRoPjwvZz48L2c+PC9zdmc+" />
      </ion-button>
      <ion-button
        class="icon-button"
        *ngIf="this.isScanTask && !scanTaskIsLocked && isPermission"
        (click)="toScan()"
        routerDirection="back">
        <ion-icon
          slot="icon-only"
          *ngIf="this.isScanTask && !scanTaskIsLocked && isPermission"
          src="assets/images/header/icon-camera.svg"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<div
  class="container layout-default"
  style="padding-bottom: 0px">
  <div
    class="divMain"
    style="display: flex; justify-content: center; align-items: center">
    <!-- テーブル要素の子要素をテーブルセル要素にする -->
    <div
      class="divLeft"
      style="width: unset">
      <div
        class="right"
        style="text-align: left">
        <p
          class="right"
          style="text-align: left">
          総資産数 : {{ assetListDatas.length }}件<br />
        </p>
      </div>
    </div>
    <!--  ヒット数 复数人情况暂时不需要，注释掉了  -->
    <!--    <div class="divCenter" *ngIf="search&&search.length!=0">-->
    <!--      <div class="center">-->
    <!--        <p class="center">-->
    <!--          ヒット数：{{countOfSearchHit}}件-->
    <!--        </p>-->
    <!--      </div>-->
    <!--    </div>-->
    <div
      class="divRight"
      style="width: unset">
      <p class="left">未スキャン数 : {{ willBescan }}件<br /></p>
    </div>
  </div>
  <div
    class="segment-group group-workflow w-100 mb-3"
    style="position: relative"
    [ngClass]="{ 'group-left-corner-fill': progress === 'all', 'group-right-corner-fill': progress === 'not-yet' }">
    <a
      class="segment-item item-workflow"
      [ngClass]="{ 'is-active': progress === 'all' }"
      (click)="openDone($event)"
      >すべて
    </a>
    <a
      class="segment-item item-workflow"
      [ngClass]="{ 'is-active': progress === 'not-yet' }"
      (click)="openNotYet($event)"
      >未完了
    </a>
  </div>
</div>
<ion-content
  #content
  [scrollEvents]="true"
  (ionScroll)="onScroll($event)"
  class="offset-bottom-0">
  <div class="container layout-default clear-padding-ltr">
    <!-- segment -->
    <div class="mb-3 margin-lr-10-b-0">
      <!--検索-->
      <div class="input-group searchbar">
        <span class="input-group-prepend pr-0">
          <button
            class="btn btn-outline bg-white text-primary pl-2 pr-1 pt-0 pb-0 shadow-none no-opacity d-flex align-items-center"
            type="button"
            disabled>
            <ion-icon src="./assets/images/toolbar/icon-search.svg"></ion-icon>
          </button>
        </span>
        <ion-input
          (keyup.enter)="doSearch()"
          [(ngModel)]="search"
          class="form-control pl-0"
          enterkeyhint="search"
          placeholder="キーワードで検索"
          type="search"
          value="{{ search }}" />
      </div>
    </div>
    <!--リスト表示-->
    <div class="assets-list by-list">
      <div
        class="scrollingContainer invisible-scrollbar"
        style="overflow: hidden; position: relative"
        [class.hidden-div]="!isContentViewDisplay">
        <div
          class="phantomContainer"
          [style.height.px]="phantomHeight"></div>
        <div
          class="actualContent invisible-scrollbar"
          [style.transform]="getTransform()">
          <ng-container *ngFor="let idx of rangeArr">
            <div
              #slidingItems
              [id]="idx"
              class="ion-item-sliding-container v-list-item">
              <div
                class="list-table-item"
                style="flex-wrap: wrap"
                [ngClass]="{ 'is-primary': getItem(idx).finishScan }"
                (click)="clickAsset(getItem(idx).assetId)">
                <!--コンテンツ-->
                <div
                  class="list-table-item-start overflow"
                  [ngStyle]="{ 'flex-basis': hasHomeImage(getItem(idx)) ? 'calc(100% - 65px)' : '100%' }">
                  <table
                    class="list-table-item-wi-tab"
                    style="overflow-x: auto">
                    <tr *ngFor="let assetItem of getItem(idx)['assetItems']; let itemIndex = index">
                      <ng-container *ngIf="more[getItem(idx).assetId] || itemIndex < 10">
                        <!-- <div> -->
                        <th
                          class="list-table-item-wi-th"
                          scope="row"
                          ><span class="with-colon">{{ assetItem['itemDisplayName'] }}</span></th
                        >
                        <td
                          ><span class="table-content">{{ assetItem['value'] }}</span></td
                        >
                        <!-- </div> -->
                      </ng-container>
                    </tr>
                    <!-- <div > -->
                    <tr *ngIf="isCountingType">
                      <th scope="row"><span class="with-colon">数量</span></th>
                      <td
                        ><span class="table-content line-clamp-3"
                          >{{ getItem(idx).assetScanedCount }}/{{ getItem(idx).assetTotalCount }}</span
                        ></td
                      >
                    </tr>
                    <!-- </div> -->
                  </table>
                </div>
                <!--サムネイル-->
                <div
                  class="asset-item__thumbnail align-self-center"
                  *ngIf="hasHomeImage(getItem(idx))"
                  style="width: 50px; height: 50px">
                  <figure>
                    <div class="homeImage">
                      <ion-spinner
                        [ngClass]="{ center: true }"
                        *ngIf="getItem(idx).homeImage.turl && !getItem(idx).homeImgLoaded"></ion-spinner>
                      <ion-img
                        style="object-fit: cover"
                        src="{{ getItem(idx).homeImage.turl }}"
                        alt=""
                        (ionImgDidLoad)="getItem(idx).homeImgLoaded = true"
                        class="is-rounded"
                        (ionError)="errorImgLoad(getItem(idx))"></ion-img>
                    </div>
                  </figure>
                </div>
                <!-- 計算 -->
                <div
                  (click)="$event.stopPropagation()"
                  style="flex-basis: 100%; padding-top: 10px"
                  *ngIf="isCountingType && this.isScanTask && !scanTaskIsLocked && isPermission">
                  <div class="quantity-value">
                    <div class="input-group input-spinner">
                      <div class="input-group-prepend">
                        <button
                          class="btn btn-sm btn-large-size d-flex align-items-center justify-content-center"
                          type="button"
                          (click)="$event.stopPropagation(); minusClick(getItem(idx))"
                          [ngClass]="getItem(idx).finishScan ? 'btn-outline-blue-inverse' : 'btn-outline-blue'">
                          <ion-icon
                            *ngIf="getItem(idx).finishScan"
                            src="assets/images/icon/minus-white.svg"
                            class="icon-large-size"></ion-icon>
                          <ion-icon
                            *ngIf="!getItem(idx).finishScan"
                            src="assets/images/icon/minus.svg"
                            class="icon-large-size"></ion-icon>
                        </button>
                      </div>
                      <ion-input
                        inputSelectAll
                        inputLengthLimit
                        value="{{ getItem(idx).assetScanedCount }}"
                        [(ngModel)]="getItem(idx).assetScanedCount"
                        (ionFocus)="updateCountFocus(getItem(idx))"
                        (ionBlur)="updateCountBlur(getItem(idx), $event)"
                        (change)="updateCount(getItem(idx), $event)"></ion-input>
                      <div class="input-group-append">
                        <button
                          class="btn btn-sm btn-large-size d-flex align-items-center justify-content-center"
                          type="button"
                          (click)="$event.stopPropagation(); plusClick(getItem(idx))"
                          [ngClass]="getItem(idx).finishScan ? 'btn-outline-blue-inverse' : 'btn-outline-blue'">
                          <ion-icon
                            *ngIf="getItem(idx).finishScan"
                            src="assets/images/icon/plus-white.svg"
                            class="icon-large-size"></ion-icon>
                          <ion-icon
                            *ngIf="!getItem(idx).finishScan"
                            src="assets/images/icon/plus.svg"
                            class="icon-large-size"></ion-icon>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="pt-2"
                  *ngIf="getItem(idx)?.assetItems?.length > 10"
                  style="margin: 10px 0 5px; width: 100%"
                  (click)="$event.stopPropagation()">
                  <a
                    *ngIf="!more[getItem(idx).assetId]"
                    (click)="this.onMoreClick(idx, true, cd)"
                    class="btn btn-sm w-20 float-icon-right contents-button"
                    [ngClass]="
                      getItem(idx).finishScan
                        ? 'btn-outline-blue-inverse float-icon-more-white'
                        : 'btn-outline-blue float-icon-more-primary'
                    "
                    style="width: 100%"
                    >さらに表示</a
                  >
                  <a
                    *ngIf="more[getItem(idx).assetId]"
                    (click)="this.onMoreClick(idx, false, cd)"
                    class="btn btn-sm w-20 float-icon-right contents-button"
                    [ngClass]="
                      getItem(idx).finishScan
                        ? 'btn-outline-blue-inverse float-icon-less-white'
                        : 'btn-outline-blue float-icon-less-primary'
                    "
                    style="width: 100%"
                    >表示を減らす</a
                  >
                </div>
              </div>
            </div>
          </ng-container>
        </div>
      </div>

      <!--一覧なし-->
      <ng-template
        [ngIf]="isResultEmpty()"
        [ngIfElse]="allData">
        <div
          class="empty-message"
          style="margin-top: 10px">
          <p class="empty-message-title">データがありません</p>
        </div>
      </ng-template>
      <ng-template #allData>
        <div>
          <ion-row>
            <ion-col class="text-center text-white font-weight-light"> 全件表示されました</ion-col>
          </ion-row>
        </div>
      </ng-template>
    </div>
  </div>
</ion-content>

<ion-footer
  class="page-footer ion-no-border"
  *ngIf="isShowFooter">
  <ion-toolbar>
    <div
      class="footer-buttons"
      [ngClass]="{
        'flex-row-reverse':
          !showSendBackButton &&
          !showRejectButton &&
          !isPermissionWF &&
          !(scanTaskIsLocked && assetListDatas.length !== 0)
      }">
      <ion-button
        color="blue-default"
        fill="outline"
        size="small"
        class="footer-button"
        *ngIf="isPermissionWF"
        (click)="saveTemporaryFromScanTask()"
        >一時保存</ion-button
      >
      <ion-button
        color="red-default"
        fill="outline"
        size="small"
        (click)="rejection()"
        class="footer-button"
        *ngIf="showRejectButton"
        >否決</ion-button
      >
      <ion-button
        color="red-default"
        fill="outline"
        size="small"
        (click)="sendBack()"
        class="footer-button"
        *ngIf="showSendBackButton"
        >差戻し</ion-button
      >

      <ion-button
        color="blue-default"
        fill="outline"
        size="small"
        class="footer-button"
        (click)="restartScantask()"
        *ngIf="scanTaskIsLocked && assetListDatas.length !== 0"
        >解除</ion-button
      >

      <ion-button
        color="blue-default"
        size="small"
        class="footer-button"
        (click)="approval()"
        *ngIf="
          ((scanTaskIsLocked && confrimStatus) || (this.isUserTask && confrimStatus)) && assetListDatas.length !== 0
        "
        >承認</ion-button
      >
      <ion-button
        color="blue-default"
        size="small"
        class="footer-button"
        (click)="scanTasktoFinish()"
        *ngIf="this.isScanTask && !scanTaskIsLocked && isAllScaned"
        >完了</ion-button
      >
    </div>
  </ion-toolbar>
</ion-footer>

<ion-footer
  class="page-footer ion-no-border"
  *ngIf="!isShowFooter">
  <ion-toolbar>
    <div class="footer-buttons">
      <ion-button
        color="blue-default"
        fill="outline"
        size="small"
        (click)="back()"
        class="footer-button">
        戻る
      </ion-button>
    </div>
  </ion-toolbar>
</ion-footer>
