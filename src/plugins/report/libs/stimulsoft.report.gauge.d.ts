declare namespace Stimulsoft.Report.Gauge {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiElementStyleIdent = Stimulsoft.Report.Dashboard.StiElementStyleIdent;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiComponent = Stimulsoft.Report.Components.StiComponent;
    import Graphics = Stimulsoft.System.Drawing.Graphics;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Type = Stimulsoft.System.Type;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import StiBaseStyle = Stimulsoft.Report.Styles.StiBaseStyle;
    class StiGaugeStyleXF extends StiBaseStyle implements IStiGaugeStyle, IStiJsonReportObject {
        private static implementsStiGaugeStyleXF;
        implements(): any[];
        get componentId(): StiComponentId;
        meta(): StiMeta[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        static createFromJsonObject(jObject: StiJson): StiGaugeStyleXF;
        static createFromXml(xmlNode: XmlNode): StiGaugeStyleXF;
        get serviceName(): string;
        get serviceCategory(): string;
        get serviceType(): Type;
        core: StiGaugeStyleCoreXF;
        allowDashboard: boolean;
        styleIdent: StiElementStyleIdent;
        toString(): string;
        compareGaugeStyle(style: StiGaugeStyleXF): boolean;
        drawStyle(g: Graphics, rect: Rectangle, paintValue: boolean, paintImage: boolean): void;
        drawBox(g: Graphics, rect: Rectangle, paintValue: boolean, paintImage: boolean): void;
        getStyleFromComponent(component: StiComponent, styleElements: StiStyleElements): void;
        setStyleToComponent(component: StiComponent): void;
        createNew(): StiGaugeStyleXF;
    }
}
declare namespace Stimulsoft.Report.Gauge {
    import StiElementStyleIdent = Stimulsoft.Report.Dashboard.StiElementStyleIdent;
    class StiGaugeStyleXF26 extends StiGaugeStyleXF {
        allowDashboard: boolean;
        get dashboardName(): string;
        styleIdent: StiElementStyleIdent;
        createNew(): StiGaugeStyleXF;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Gauge {
    import StiElementStyleIdent = Stimulsoft.Report.Dashboard.StiElementStyleIdent;
    class StiGaugeStyleXF27 extends StiGaugeStyleXF {
        allowDashboard: boolean;
        get dashboardName(): string;
        styleIdent: StiElementStyleIdent;
        createNew(): StiGaugeStyleXF;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Gauge {
    import Font = Stimulsoft.System.Drawing.Font;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import StiGauge = Stimulsoft.Report.Components.StiGauge;
    class StiGaugeStyleCoreXF implements IStiGaugeStyleCoreXF {
        get localizedName(): string;
        brush: StiBrush;
        foreColor: Color;
        borderColor: Color;
        borderWidth: number;
        targetColor: Color;
        tickMarkMajorBrush: StiBrush;
        tickMarkMajorBorder: StiBrush;
        tickMarkMajorBorderWidth: number;
        tickMarkMinorBrush: StiBrush;
        tickMarkMinorBorder: StiBrush;
        tickMarkMinorBorderWidth: number;
        tickLabelMajorTextBrush: StiBrush;
        tickLabelMajorFont: Font;
        tickLabelMinorTextBrush: StiBrush;
        tickLabelMinorFont: Font;
        linearScaleBrush: StiBrush;
        linearBarBrush: StiBrush;
        linearBarBorderBrush: StiBrush;
        linearBarEmptyBrush: StiBrush;
        linearBarEmptyBorderBrush: StiBrush;
        linearBarStartWidth: number;
        linearBarEndWidth: number;
        radialBarBrush: StiBrush;
        radialBarBorderBrush: StiBrush;
        radialBarEmptyBrush: StiBrush;
        radialBarEmptyBorderBrush: StiBrush;
        radialBarStartWidth: number;
        radialBarEndWidth: number;
        needleBrush: StiBrush;
        needleBorderBrush: StiBrush;
        needleCapBrush: StiBrush;
        needleCapBorderBrush: StiBrush;
        needleBorderWidth: number;
        needleCapBorderWidth: number;
        needleStartWidth: number;
        needleEndWidth: number;
        needleRelativeHeight: number;
        needleRelativeWith: number;
        markerSkin: StiMarkerSkin;
        markerBrush: StiBrush;
        markerBorderBrush: StiBrush;
        markerBorderWidth: number;
        styleId: StiGaugeStyleId;
        gauge: StiGauge;
    }
}
declare namespace Stimulsoft.Report.Gauge {
    import Color = Stimulsoft.System.Drawing.Color;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Font = Stimulsoft.System.Drawing.Font;
    class StiGaugeStyleCoreXF25 extends StiGaugeStyleCoreXF {
        get localizedName(): string;
        brush: StiBrush;
        foreColor: Color;
        borderColor: Color;
        borderWidth: number;
        targetColor: Color;
        tickMarkMajorBrush: StiBrush;
        tickMarkMajorBorder: StiBrush;
        tickMarkMinorBrush: StiBrush;
        tickMarkMinorBorder: StiBrush;
        tickLabelMajorTextBrush: StiBrush;
        tickLabelMajorFont: Font;
        tickLabelMinorTextBrush: StiBrush;
        tickLabelMinorFont: Font;
        markerBrush: StiBrush;
        linearScaleBrush: StiBrush;
        linearBarBrush: StiBrush;
        linearBarBorderBrush: StiBrush;
        linearBarEmptyBrush: any;
        linearBarEmptyBorderBrush: any;
        linearBarStartWidth: number;
        linearBarEndWidth: number;
        radialBarBrush: StiBrush;
        radialBarBorderBrush: StiBrush;
        radialBarEmptyBrush: StiBrush;
        radialBarEmptyBorderBrush: StiBrush;
        radialBarStartWidth: number;
        radialBarEndWidth: number;
        needleBrush: StiBrush;
        needleBorderBrush: StiBrush;
        needleCapBrush: StiBrush;
        needleCapBorderBrush: StiBrush;
        needleBorderWidth: number;
        needleCapBorderWidth: number;
        needleStartWidth: number;
        needleEndWidth: number;
        needleRelativeHeight: number;
        needleRelativeWith: number;
    }
}
declare namespace Stimulsoft.Report.Gauge {
    import StiGaugeStyle = Stimulsoft.Report.StiGaugeStyle;
    class StiCustomGaugeStyleCoreXF extends StiGaugeStyleCoreXF25 {
        get localizedName(): string;
        get reportStyleName(): string;
        reportGaugeStyle: StiGaugeStyle;
        constructor(style: StiGaugeStyle);
    }
}
declare namespace Stimulsoft.Report.Gauge {
    import StiCustomGaugeStyleCoreXF = Stimulsoft.Report.Gauge.StiCustomGaugeStyleCoreXF;
    class StiCustomGaugeStyle extends StiGaugeStyleXF27 {
        get serviceName(): string;
        get customCore(): StiCustomGaugeStyleCoreXF;
        constructor(style?: StiGaugeStyle);
    }
}
declare namespace Stimulsoft.Report.Gauge.Helpers {
    class StiMathHelper {
        static length1(value1: number, value2: number): number;
        static maxMinusMin(value1: number, value2: number): number;
        static getMax(...list: number[]): number;
    }
}
declare namespace Stimulsoft.Report.Gauge.Helpers {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    class StiRectangleHelper {
        static centerX(rect: Rectangle): number;
        static centerY(rect: Rectangle): number;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge.Primitives {
    import StiGraphicsPathLinesGaugeGeom = Stimulsoft.Report.Gauge.GaugeGeoms.StiGraphicsPathLinesGaugeGeom;
    import StiPlacement = Stimulsoft.Report.Gauge.StiPlacement;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    import IStiScaleBarGeometry = Stimulsoft.Report.Gauge.Primitives.IStiScaleBarGeometry;
    import Size = Stimulsoft.System.Drawing.Size;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    class StiLinearBarGeometry implements IStiScaleBarGeometry {
        private scale;
        size: Size;
        rectGeometry: Rectangle;
        radius: number;
        diameter: number;
        center: Point;
        checkRectGeometry(rect: Rectangle): void;
        private getRectGeometry;
        getRestToLenght(): number;
        private checkMinMaxWidth;
        drawScaleGeometry(context: StiGaugeContextPainter): void;
        drawGeometry(context: StiGaugeContextPainter, startValue1: number, endValue1: number, startWidth: number, endWidth: number, offset: number, placement: StiPlacement, REFrect: any, returnOnlyRect: boolean): StiGraphicsPathLinesGaugeGeom;
        drawPrimitiveGeometry(context: StiGaugeContextPainter, rect: Rectangle, minAscent: number, maxAscent: number, startWidth: number, endWidth: number, placement: StiPlacement, restOffset: number, isStartGreaterEnd: boolean): StiGraphicsPathLinesGaugeGeom;
        constructor(scale: StiLinearScale);
    }
}
declare namespace Stimulsoft.Report.Gauge {
    let IStiApplyStyleGauge: System.Interface<IStiApplyStyleGauge>;
    interface IStiApplyStyleGauge {
        applyStyle(style: IStiGaugeStyle): void;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge.Primitives {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import IAsIs = Stimulsoft.System.IAsIs;
    import ICloneable = Stimulsoft.System.ICloneable;
    import IStiApplyStyleGauge = Stimulsoft.Report.Gauge.IStiApplyStyleGauge;
    import IStiGaugeStyle = Stimulsoft.Report.Gauge.IStiGaugeStyle;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    class StiElementBase implements ICloneable, IStiApplyStyleGauge, IAsIs {
        implements(): any[];
        is<T>(type: (new (...args: any[]) => T) | Stimulsoft.System.Interface<T>): this is T;
        is2<T>(type: (new (...args: any[]) => T) | Stimulsoft.System.Interface<T>): boolean;
        as<T>(type: (new (...args: any[]) => T) | Stimulsoft.System.Interface<T>): T;
        protected _hash: StiMeta[];
        meta(): StiMeta[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        applyStyle(style: IStiGaugeStyle): void;
        clone(): any;
        allowApplyStyle: boolean;
        drawElement(context: StiGaugeContextPainter): void;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge.Primitives {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import EventArgs = Stimulsoft.System.EventArgs;
    import IStiScaleBarGeometry = Stimulsoft.Report.Gauge.Primitives.IStiScaleBarGeometry;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import StiGauge = Stimulsoft.Report.Components.StiGauge;
    import StiGaugeElemenType = Stimulsoft.Report.Gauge.StiGaugeElemenType;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import IStiScaleBase = Stimulsoft.Report.Components.Gauge.IStiScaleBase;
    import StiElementBase = Stimulsoft.Report.Components.Gauge.Primitives.StiElementBase;
    class StiScaleHelper {
        actualMinimum: number;
        actualMaximum: number;
        minWidth: number;
        maxWidth: number;
        private _totalLength;
        get totalLength(): number;
        set totalLength(value: number);
    }
    class StiScaleBase extends StiElementBase implements IStiJsonReportObject, IStiScaleBase {
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        get propName(): string;
        clone(): StiScaleBase;
        barGeometry: IStiScaleBarGeometry;
        scaleHelper: StiScaleHelper;
        get isUp(): boolean;
        gauge: StiGauge;
        seriesKey: {};
        private _left;
        get left(): number;
        set left(value: number);
        private _top;
        get top(): number;
        set top(value: number);
        private _startWidth;
        get startWidth(): number;
        set startWidth(value: number);
        private _endWidth;
        get endWidth(): number;
        set endWidth(value: number);
        private _majorInterval;
        get majorInterval(): number;
        set majorInterval(value: number);
        private _minorInterval;
        get minorInterval(): number;
        set minorInterval(value: number);
        private _minimum;
        get minimum(): number;
        set minimum(value: number);
        private _maximum;
        get maximum(): number;
        set maximum(value: number);
        private _isReversed;
        get isReversed(): boolean;
        set isReversed(value: boolean);
        private _brush;
        get brush(): StiBrush;
        set brush(value: StiBrush);
        private _borderBrush;
        get borderBrush(): StiBrush;
        set borderBrush(value: StiBrush);
        _items: Stimulsoft.Report.Gauge.Collections.StiGaugeElementCollection;
        get items(): Stimulsoft.Report.Gauge.Collections.StiGaugeElementCollection;
        set(value: Stimulsoft.Report.Gauge.Collections.StiGaugeElementCollection): void;
        get scaleType(): StiGaugeElemenType;
        prepare(gauge: StiGauge): void;
        calculateMinMaxScaleHelper(): void;
        calculateWidthScaleHelper(): void;
        getPosition(value: number): number;
        interactiveClick(e: EventArgs): void;
        createNew(): StiScaleBase;
        drawElement(context: StiGaugeContextPainter): void;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Components.Gauge {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import EventArgs = Stimulsoft.System.EventArgs;
    import Orientation = Stimulsoft.System.Drawing.Orientation;
    import StiScaleBase = Stimulsoft.Report.Components.Gauge.Primitives.StiScaleBase;
    import IStiGaugeStyle = Stimulsoft.Report.Gauge.IStiGaugeStyle;
    class StiLinearScale extends StiScaleBase implements IStiJsonReportObject {
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        applyStyle(style: IStiGaugeStyle): void;
        private _orientation;
        get orientation(): Orientation;
        set orientation(value: Orientation);
        private _relativeHeight;
        get relativeHeight(): number;
        set relativeHeight(value: number);
        get scaleType(): Stimulsoft.Report.Gauge.StiGaugeElemenType;
        interactiveClick(e: EventArgs): void;
        createNew(): StiScaleBase;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Components.Gauge.Primitives {
    import StiGraphicsPathLinesGaugeGeom = Stimulsoft.Report.Gauge.GaugeGeoms.StiGraphicsPathLinesGaugeGeom;
    import StiPlacement = Stimulsoft.Report.Gauge.StiPlacement;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import Size = Stimulsoft.System.Drawing.Size;
    import IStiScaleBarGeometry = Stimulsoft.Report.Gauge.Primitives.IStiScaleBarGeometry;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    class StiRadialBarGeometry implements IStiScaleBarGeometry {
        scale: StiRadialScale;
        size: Size;
        rectGeometry: Rectangle;
        radius: number;
        diameter: number;
        center: Point;
        checkRectGeometry(rect: Rectangle): void;
        drawScaleGeometry(context: StiGaugeContextPainter): void;
        getRestToLenght(): number;
        drawGeometry(context: StiGaugeContextPainter, startValue: number, endValue: number, startWidth: number, endWidth: number, offset: number, placement: StiPlacement, REFrect: any, returnOnlyRect: boolean): StiGraphicsPathLinesGaugeGeom;
        constructor(scale: StiRadialScale);
    }
}
declare namespace Stimulsoft.Report.Components.Gauge {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import EventArgs = Stimulsoft.System.EventArgs;
    import Point = Stimulsoft.System.Drawing.Point;
    import IStiGaugeStyle = Stimulsoft.Report.Gauge.IStiGaugeStyle;
    import StiScaleBase = Stimulsoft.Report.Components.Gauge.Primitives.StiScaleBase;
    class StiRadialScale extends StiScaleBase implements IStiJsonReportObject {
        meta(): StiMeta[];
        protected loadPointFromXml(text: string): Point;
        get componentId(): StiComponentId;
        clone(): StiRadialScale;
        applyStyle(style: IStiGaugeStyle): void;
        private _radius;
        get radius(): number;
        set radius(value: number);
        private _radiusMode;
        get radiusMode(): Stimulsoft.Report.Gauge.StiRadiusMode;
        set radiusMode(value: Stimulsoft.Report.Gauge.StiRadiusMode);
        private _center;
        get center(): Point;
        set center(value: Point);
        private _startAngle;
        get startAngle(): number;
        set startAngle(value: number);
        private _sweepAngle;
        get sweepAngle(): number;
        set sweepAngle(value: number);
        private _skin;
        get skin(): Stimulsoft.Report.Gauge.StiRadialScaleSkin;
        set skin(value: Stimulsoft.Report.Gauge.StiRadialScaleSkin);
        get scaleType(): Stimulsoft.Report.Gauge.StiGaugeElemenType;
        getRadius(): number;
        getStartWidth(): number;
        getEndWidth(): number;
        getSweepAngle(): number;
        getCurrentAngle(angle: number): number;
        interactiveClick(e: EventArgs): void;
        createNew(): StiScaleBase;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Gauge.Collections {
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import CollectionBase = Stimulsoft.System.Collections.CollectionBase;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiScaleBase = Stimulsoft.Report.Components.Gauge.Primitives.StiScaleBase;
    import StiGauge = Stimulsoft.Report.Components.StiGauge;
    class StiScaleCollection extends CollectionBase<StiScaleBase> implements ICloneable, IStiJsonReportObject {
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(jObject: StiJson): void;
        loadFromXml(xmlNode: XmlNode): void;
        private parent;
        clone(): StiScaleCollection;
        get isReadOnly(): boolean;
        private setParent;
        private clearParent;
        add(element: StiScaleBase): void;
        insert(index: number, element: StiScaleBase): void;
        remove(element: StiScaleBase): boolean;
        copyTo(elements: StiScaleBase[], arrayIndex: number): void;
        moveUp(element: StiScaleBase): boolean;
        moveDown(element: StiScaleBase): boolean;
        constructor(parent: StiGauge);
    }
}
declare namespace Stimulsoft.Report.Components {
    import StiAnimation = Stimulsoft.Base.Context.Animation.StiAnimation;
    import List = Stimulsoft.System.Collections.List;
    import StiScaleMode = Stimulsoft.Report.Gauge.StiScaleMode;
    import StiGaugeCalculationMode = Stimulsoft.Report.Gauge.StiGaugeCalculationMode;
    import StiGaugeType = Stimulsoft.Report.Gauge.StiGaugeType;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import Image = Stimulsoft.System.Drawing.Image;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    import StiScaleCollection = Stimulsoft.Report.Gauge.Collections.StiScaleCollection;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import StiBorder = Stimulsoft.Base.Drawing.StiBorder;
    import StiComponent = Stimulsoft.Report.Components.StiComponent;
    import IStiExportImageExtended = Stimulsoft.Report.Components.IStiExportImageExtended;
    import IStiBorder = Stimulsoft.Report.Components.IStiBorder;
    import IStiBrush = Stimulsoft.Report.Components.IStiBrush;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiGauge = Stimulsoft.Report.Components.Gauge.IStiGauge;
    import IStiGaugeStyle = Stimulsoft.Report.Gauge.IStiGaugeStyle;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiFormatService = Stimulsoft.Report.Components.TextFormats.StiFormatService;
    class StiGauge extends StiComponent implements IStiExportImageExtended, IStiBorder, IStiBrush, IStiGauge, IStiJsonReportObject {
        private static implementsStiGauge;
        implements(): any[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(jObject: StiJson): void;
        loadFromXml(xmlNode: XmlNode): void;
        get componentId(): StiComponentId;
        clone(cloneProperties?: boolean, cloneComponents?: boolean): StiGauge;
        prepareInit(): void;
        getImage(REFzoom: any, format?: StiExportFormat): Image;
        isExportAsImage(format: StiExportFormat): boolean;
        private _border;
        get border(): StiBorder;
        set border(value: StiBorder);
        private _brush;
        get brush(): StiBrush;
        set brush(value: StiBrush);
        private valueFormat_;
        get valueFormat(): StiFormatService;
        set valueFormat(value: StiFormatService);
        private static getValueFormatDefault;
        get localizedCategory(): string;
        get localizedName(): string;
        defaultClientRectangle: Rectangle;
        mode: StiScaleMode;
        shortValue: boolean;
        minimum: number;
        maximum: number;
        type: StiGaugeType;
        calculationMode: StiGaugeCalculationMode;
        painter: StiGaugeContextPainter;
        private _style;
        get style(): IStiGaugeStyle;
        set style(value: IStiGaugeStyle);
        private _allowApplyStyle;
        get allowApplyStyle(): boolean;
        set allowApplyStyle(value: boolean);
        customStyleName: string;
        scales: StiScaleCollection;
        isAnimation: boolean;
        previousAnimations: List<StiAnimation>;
        private changeSkin;
        private getGaugeStyle;
        drawGauge(context: StiGaugeContextPainter): void;
        createNew(): StiComponent;
        applyStyle(style: IStiGaugeStyle): void;
        constructor(rect?: Rectangle);
    }
}
declare namespace Stimulsoft.Report.Gauge.Collections {
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import CollectionBase = Stimulsoft.System.Collections.CollectionBase;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiIndicatorRangeInfo = Stimulsoft.Report.Components.Gauge.StiIndicatorRangeInfo;
    class StiBarRangeListCollection extends CollectionBase<StiIndicatorRangeInfo> implements ICloneable, IStiJsonReportObject {
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(jObject: StiJson): void;
        loadFromXml(xmlNode: XmlNode): void;
        private barType;
        clone(): StiBarRangeListCollection;
        get isReadOnly(): boolean;
        add(element: StiIndicatorRangeInfo): void;
        insert(index: number, element: StiIndicatorRangeInfo): void;
        copyTo(elements: StiIndicatorRangeInfo[], arrayIndex: number): void;
        moveUp(element: StiIndicatorRangeInfo): boolean;
        moveDown(element: StiIndicatorRangeInfo): boolean;
        constructor(barType: StiBarRangeListType);
    }
}
declare namespace Stimulsoft.Report.Components.Gauge {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiPlacement = Stimulsoft.Report.Gauge.StiPlacement;
    import IStiCustomValueBase = Stimulsoft.Report.Components.Gauge.IStiCustomValueBase;
    class StiCustomValueBase implements ICloneable, IStiCustomValueBase, IStiJsonReportObject {
        protected _hash: StiMeta[];
        meta(): StiMeta[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        get componentId(): StiComponentId;
        get propName(): string;
        clone(): any;
        private _value;
        get value(): number;
        set value(value: number);
        private _placement;
        get placement(): StiPlacement;
        set placement(value: StiPlacement);
        private _offset;
        get offset(): number;
        set offset(value: number);
        get localizedName(): string;
        createNew(): StiCustomValueBase;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import StiCustomValueBase = Stimulsoft.Report.Components.Gauge.StiCustomValueBase;
    import StiGaugeElementSkin = Stimulsoft.Report.Gauge.StiGaugeElementSkin;
    import StiPlacement = Stimulsoft.Report.Gauge.StiPlacement;
    class StiRadialTickMarkCustomValue extends StiCustomValueBase implements IStiJsonReportObject {
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        clone(): StiRadialTickMarkCustomValue;
        useBrush: boolean;
        useBorderBrush: boolean;
        useBorderWidth: boolean;
        private _relativeWidth;
        get relativeWidth(): number;
        set relativeWidth(value: number);
        private _relativeHeight;
        get relativeHeight(): number;
        set relativeHeight(value: number);
        private _offsetAngle;
        get offsetAngle(): number;
        set offsetAngle(value: number);
        private _skin;
        get skin(): StiGaugeElementSkin;
        set skin(value: StiGaugeElementSkin);
        private _brush;
        get brush(): StiBrush;
        set brush(value: StiBrush);
        private _borderBrush;
        get borderBrush(): StiBrush;
        set borderBrush(value: StiBrush);
        private _borderWidth;
        get borderWidth(): number;
        set borderWidth(value: number);
        get localizedName(): string;
        toString(): string;
        createNew(): StiCustomValueBase;
        constructor(value?: number, offset?: number, relativeWidth?: number, relativeHeight?: number, offsetAngle?: number, placement?: StiPlacement, brush?: StiBrush, borderBrush?: StiBrush, borderWidth?: number, skin?: StiGaugeElementSkin);
    }
}
declare namespace Stimulsoft.Report.Components.Gauge {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiLabelRotationMode = Stimulsoft.Report.Gauge.StiLabelRotationMode;
    import StiPlacement = Stimulsoft.Report.Gauge.StiPlacement;
    class StiRadialTickLabelCustomValue extends StiCustomValueBase implements IStiJsonReportObject {
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        text: string;
        offsetAngle: number;
        labelRotationMode: StiLabelRotationMode;
        get localizedName(): string;
        toString(): string;
        createNew(): StiCustomValueBase;
        constructor(value?: number, text?: string, offset?: number, offsetAngle?: number, labelRotationMode?: StiLabelRotationMode, placement?: StiPlacement);
    }
}
declare namespace Stimulsoft.Report.Components.Gauge {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiGaugeElementSkin = Stimulsoft.Report.Gauge.StiGaugeElementSkin;
    import StiPlacement = Stimulsoft.Report.Gauge.StiPlacement;
    class StiLinearTickMarkCustomValue extends StiCustomValueBase implements IStiJsonReportObject {
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        private _relativeWidth;
        get relativeWidth(): number;
        set relativeWidth(value: number);
        private _relativeHeight;
        get(): number;
        set relativeHeight(value: number);
        private _skin;
        get skin(): StiGaugeElementSkin;
        set skin(value: StiGaugeElementSkin);
        get localizedName(): string;
        toString(): string;
        createNew(): StiCustomValueBase;
        constructor(value?: number, offset?: number, relativeWidth?: number, relativeHeight?: number, placement?: StiPlacement, skin?: StiGaugeElementSkin);
    }
}
declare namespace Stimulsoft.Report.Components.Gauge {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiPlacement = Stimulsoft.Report.Gauge.StiPlacement;
    class StiLinearTickLabelCustomValue extends StiCustomValueBase implements IStiJsonReportObject {
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        private _text;
        get text(): string;
        set text(value: string);
        get localizedName(): string;
        toString(): string;
        createNew(): StiCustomValueBase;
        constructor(value?: number, text?: string, offset?: number, placement?: StiPlacement);
    }
}
declare namespace Stimulsoft.Report.Gauge.Collections {
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import CollectionBase = Stimulsoft.System.Collections.CollectionBase;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiCustomValueBase = Stimulsoft.Report.Components.Gauge.StiCustomValueBase;
    class StiCustomValuesCollection extends CollectionBase<StiCustomValueBase> implements ICloneable, IStiJsonReportObject {
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(jObject: StiJson): void;
        loadFromXml(xmlNode: XmlNode): void;
        clone(): StiCustomValuesCollection;
        get isReadOnly(): boolean;
        copyTo(elements: StiCustomValueBase[], arrayIndex: number): void;
        moveUp(element: StiCustomValueBase): boolean;
        moveDown(element: StiCustomValueBase): boolean;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge {
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiStateIndicatorFilter implements ICloneable, IStiJsonReportObject {
        protected _hash: StiMeta[];
        meta(): StiMeta[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        get componentId(): StiComponentId;
        get propName(): string;
        clone(): any;
        private _startValue;
        get startValue(): number;
        set startValue(value: number);
        private _endValue;
        get endValue(): number;
        set endValue(value: number);
        private _brush;
        get brush(): StiBrush;
        set brush(value: StiBrush);
        private _borderBrush;
        get borderBrush(): StiBrush;
        set borderBrush(value: StiBrush);
        toString(): string;
    }
}
declare namespace Stimulsoft.Report.Gauge.Collections {
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import CollectionBase = Stimulsoft.System.Collections.CollectionBase;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiStateIndicatorFilter = Stimulsoft.Report.Components.Gauge.StiStateIndicatorFilter;
    class StiFilterCollection extends CollectionBase<StiStateIndicatorFilter> implements ICloneable, IStiJsonReportObject {
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(jObject: StiJson): void;
        loadFromXml(xmlNode: XmlNode): void;
        clone(): StiFilterCollection;
        get isReadOnly(): boolean;
        moveUp(element: StiStateIndicatorFilter): boolean;
        moveDown(element: StiStateIndicatorFilter): boolean;
    }
}
declare namespace Stimulsoft.Report.Gauge {
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    class StiGaugeElementSkin {
        draw(context: StiGaugeContextPainter, element: StiGaugeElement, rect: Rectangle, angle?: number, centerPoint?: Point): void;
    }
}
declare namespace Stimulsoft.Report.Gauge.Skins {
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiIndicatorBase = Stimulsoft.Report.Components.Gauge.Primitives.StiIndicatorBase;
    import StringFormat = Stimulsoft.System.Drawing.StringFormat;
    import StiAnimation = Stimulsoft.Base.Context.Animation.StiAnimation;
    class StiMarkerBaseSkin extends StiGaugeElementSkin {
        addLines(context: StiGaugeContextPainter, indicator: StiIndicatorBase, points: Point[], rect: Rectangle, angle: number, centerPoint: Point, sf: StringFormat, animation: StiAnimation): void;
    }
}
declare namespace Stimulsoft.Report.Gauge.Skins {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    class StiMarker10Skin extends StiMarkerBaseSkin {
        draw(context: StiGaugeContextPainter, element: StiGaugeElement, rect: Rectangle, angle: number, centerPoint: Point): void;
    }
}
declare namespace Stimulsoft.Report.Gauge.Skins {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    class StiMarker11Skin extends StiMarkerBaseSkin {
        draw(context: StiGaugeContextPainter, element: StiGaugeElement, rect: Rectangle, angle: number, centerPoint: Point): void;
    }
}
declare namespace Stimulsoft.Report.Gauge.Skins {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    class StiMarker12Skin extends StiMarkerBaseSkin {
        draw(context: StiGaugeContextPainter, element: StiGaugeElement, rect: Rectangle, angle: number, centerPoint: Point): void;
    }
}
declare namespace Stimulsoft.Report.Gauge.Skins {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    class StiMarker13Skin extends StiMarkerBaseSkin {
        draw(context: StiGaugeContextPainter, element: StiGaugeElement, rect: Rectangle, angle: number, centerPoint: Point): void;
    }
}
declare namespace Stimulsoft.Report.Gauge.Skins {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    class StiMarker14Skin extends StiMarkerBaseSkin {
        draw(context: StiGaugeContextPainter, element: StiGaugeElement, rect: Rectangle, angle: number, centerPoint: Point): void;
    }
}
declare namespace Stimulsoft.Report.Gauge.Skins {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    class StiMarker15Skin extends StiMarkerBaseSkin {
        draw(context: StiGaugeContextPainter, element: StiGaugeElement, rect: Rectangle, angle: number, centerPoint: Point): void;
    }
}
declare namespace Stimulsoft.Report.Gauge.Skins {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    class StiMarker1Skin extends StiMarkerBaseSkin {
        draw(context: StiGaugeContextPainter, element: StiGaugeElement, rect: Rectangle, angle: number, centerPoint: Point): void;
    }
}
declare namespace Stimulsoft.Report.Gauge.Skins {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    class StiMarker2Skin extends StiMarkerBaseSkin {
        draw(context: StiGaugeContextPainter, element: StiGaugeElement, rect: Rectangle, angle: number, centerPoint: Point): void;
    }
}
declare namespace Stimulsoft.Report.Gauge.Skins {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    class StiMarker3Skin extends StiMarkerBaseSkin {
        draw(context: StiGaugeContextPainter, element: StiGaugeElement, rect: Rectangle, angle: number, centerPoint: Point): void;
    }
}
declare namespace Stimulsoft.Report.Gauge.Skins {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    class StiMarker4Skin extends StiMarkerBaseSkin {
        draw(context: StiGaugeContextPainter, element: StiGaugeElement, rect: Rectangle, angle: number, centerPoint: Point): void;
    }
}
declare namespace Stimulsoft.Report.Gauge.Skins {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    class StiMarker5Skin extends StiMarkerBaseSkin {
        draw(context: StiGaugeContextPainter, element: StiGaugeElement, rect: Rectangle, angle: number, centerPoint: Point): void;
    }
}
declare namespace Stimulsoft.Report.Gauge.Skins {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    class StiMarker6Skin extends StiMarkerBaseSkin {
        draw(context: StiGaugeContextPainter, element: StiGaugeElement, rect: Rectangle, angle: number, centerPoint: Point): void;
    }
}
declare namespace Stimulsoft.Report.Gauge.Skins {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    class StiMarker7Skin extends StiMarkerBaseSkin {
        draw(context: StiGaugeContextPainter, element: StiGaugeElement, rect: Rectangle, angle: number, centerPoint: Point): void;
    }
}
declare namespace Stimulsoft.Report.Gauge.Skins {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    class StiMarker8Skin extends StiMarkerBaseSkin {
        draw(context: StiGaugeContextPainter, element: StiGaugeElement, rect: Rectangle, angle: number, centerPoint: Point): void;
    }
}
declare namespace Stimulsoft.Report.Gauge.Skins {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    class StiMarker9Skin extends StiMarkerBaseSkin {
        draw(context: StiGaugeContextPainter, element: StiGaugeElement, rect: Rectangle, angle: number, centerPoint: Point): void;
    }
}
declare namespace Stimulsoft.Report.Gauge.Skins {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiGaugeElementSkin = Stimulsoft.Report.Gauge.StiGaugeElementSkin;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    class StiNeedleIndicator1Skin extends StiGaugeElementSkin {
        draw(context: StiGaugeContextPainter, element: StiGaugeElement, rect: Rectangle, angle: number, centerPoint: Point): void;
    }
}
declare namespace Stimulsoft.Report.Gauge.Skins {
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiGaugeElementSkin = Stimulsoft.Report.Gauge.StiGaugeElementSkin;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    class StiNeedleIndicator2Skin extends StiGaugeElementSkin {
        draw(context: StiGaugeContextPainter, element: StiGaugeElement, rect: Rectangle, angle: number, centerPoint: Point): void;
    }
}
declare namespace Stimulsoft.Report.Gauge.Skins {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiGaugeElementSkin = Stimulsoft.Report.Gauge.StiGaugeElementSkin;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    class StiNeedleIndicator3Skin extends StiGaugeElementSkin {
        draw(context: StiGaugeContextPainter, element: StiGaugeElement, rect: Rectangle, angle: number, centerPoint: Point): void;
    }
}
declare namespace Stimulsoft.Report.Gauge.Skins {
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiGaugeElementSkin = Stimulsoft.Report.Gauge.StiGaugeElementSkin;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    class StiNeedleIndicator4Skin extends StiGaugeElementSkin {
        draw(context: StiGaugeContextPainter, element: StiGaugeElement, rect: Rectangle, angle: number, centerPoint: Point): void;
    }
}
declare namespace Stimulsoft.Report.Gauge.Skins {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGaugeElementSkin = Stimulsoft.Report.Gauge.StiGaugeElementSkin;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    class StiState1Skin extends StiGaugeElementSkin {
        draw(context: StiGaugeContextPainter, element: StiGaugeElement, rect: Rectangle, angle: number, centerPoint: Point): void;
    }
}
declare namespace Stimulsoft.Report.Gauge.Skins {
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGaugeElementSkin = Stimulsoft.Report.Gauge.StiGaugeElementSkin;
    class StiState2Skin extends StiGaugeElementSkin {
        draw(context: StiGaugeContextPainter, element: StiGaugeElement, rect: Rectangle, angle: number, centerPoint: Point): void;
    }
}
declare namespace Stimulsoft.Report.Gauge.Skins {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    class StiState3Skin extends StiMarkerBaseSkin {
        draw(context: StiGaugeContextPainter, element: StiGaugeElement, rect: Rectangle, angle: number, centerPoint: Point): void;
    }
}
declare namespace Stimulsoft.Report.Gauge.Skins {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    class StiMark1Skin extends StiGaugeElementSkin {
        draw(context: StiGaugeContextPainter, element: StiGaugeElement, rect: Rectangle, angle: number, centerPoint: Point): void;
    }
}
declare namespace Stimulsoft.Report.Gauge.Skins {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    class StiMark2Skin extends StiGaugeElementSkin {
        draw(context: StiGaugeContextPainter, element: StiGaugeElement, rect: Rectangle, angle: number, centerPoint: Point): void;
    }
}
declare namespace Stimulsoft.Report.Gauge.Skins {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    class StiMark3Skin extends StiGaugeElementSkin {
        draw(context: StiGaugeContextPainter, element: StiGaugeElement, rect: Rectangle, angle: number, centerPoint: Point): void;
    }
}
declare namespace Stimulsoft.Report.Gauge.Skins {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    class StiMark4Skin extends StiGaugeElementSkin {
        draw(context: StiGaugeContextPainter, element: StiGaugeElement, rect: Rectangle, angle: number, centerPoint: Point): void;
    }
}
declare namespace Stimulsoft.Report.Gauge.Skins {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    class StiMark5Skin extends StiGaugeElementSkin {
        draw(context: StiGaugeContextPainter, element: StiGaugeElement, rect: Rectangle, angle: number, centerPoint: Point): void;
    }
}
declare namespace Stimulsoft.Report.Gauge.Skins {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    class StiMark6Skin extends StiGaugeElementSkin {
        draw(context: StiGaugeContextPainter, element: StiGaugeElement, rect: Rectangle, angle: number, centerPoint: Point): void;
    }
}
declare namespace Stimulsoft.Report.Gauge.Skins {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    class StiMark7Skin extends StiGaugeElementSkin {
        draw(context: StiGaugeContextPainter, element: StiGaugeElement, rect: Rectangle, angle: number, centerPoint: Point): void;
    }
}
declare namespace Stimulsoft.Report.Gauge.Helpers {
    class StiGaugeSkinHelper {
        static getMarkerSkin(skin: StiMarkerSkin): StiGaugeElementSkin;
        static getTickMarkSkin(skin: StiTickMarkSkin): StiGaugeElementSkin;
        static getStateIndicatorSkin(skin: StiStateSkin): StiGaugeElementSkin;
        static getNeedleIndicatorSkin(skin: StiNeedleSkin): StiGaugeElementSkin;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge.Primitives {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiGaugeElemenType = Stimulsoft.Report.Gauge.StiGaugeElemenType;
    import IStiGaugeElement = Stimulsoft.Report.Components.Gauge.IStiGaugeElement;
    import StiAnimation = Stimulsoft.Base.Context.Animation.StiAnimation;
    class StiGaugeElement extends StiElementBase implements IStiJsonReportObject, IStiGaugeElement {
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        get PropName(): string;
        animation: StiAnimation;
        get elementType(): StiGaugeElemenType;
        get localizeName(): string;
        scale: StiScaleBase;
        createNew(): StiGaugeElement;
        prepareGaugeElement(): void;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge.Primitives {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import Point = Stimulsoft.System.Drawing.Point;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import StiGetValueEvent = Stimulsoft.Report.Events.StiGetValueEvent;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import StiGetValueEventArgs = Stimulsoft.Report.Events.StiGetValueEventArgs;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    class StiIndicatorBase extends StiGaugeElement implements IStiJsonReportObject {
        meta(): StiMeta[];
        clone(): StiIndicatorBase;
        private _valueObj;
        get valueObj(): number;
        set valueObj(value: number);
        private _placement;
        get placement(): Stimulsoft.Report.Gauge.StiPlacement;
        set placement(value: Stimulsoft.Report.Gauge.StiPlacement);
        private _brush;
        get brush(): StiBrush;
        set brush(value: StiBrush);
        private _borderBrush;
        get borderBrush(): StiBrush;
        set borderBrush(value: StiBrush);
        private _borderWidth;
        get borderWidth(): number;
        set borderWidth(value: number);
        onGetValue(e: StiGetValueEventArgs): void;
        invokeGetValue(sender: StiGaugeElement, e: StiGetValueEventArgs): void;
        private _getValueEvent;
        get getValueEvent(): StiGetValueEvent;
        set getValueEvent(value: StiGetValueEvent);
        private _value;
        get value(): string;
        set value(value: string);
        prepareGaugeElement(): void;
        interactiveClick(rect: Rectangle, p: Point): void;
        onValueChanged(): void;
        getActualValue(): number;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import IStiGaugeStyle = Stimulsoft.Report.Gauge.IStiGaugeStyle;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGaugeElemenType = Stimulsoft.Report.Gauge.StiGaugeElemenType;
    import StiGaugeElementSkin = Stimulsoft.Report.Gauge.StiGaugeElementSkin;
    import Point = Stimulsoft.System.Drawing.Point;
    import Font = Stimulsoft.System.Drawing.Font;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import StiIndicatorBase = Stimulsoft.Report.Components.Gauge.Primitives.StiIndicatorBase;
    class StiNeedle extends StiIndicatorBase implements IStiJsonReportObject {
        meta(): StiMeta[];
        protected loadPointFromXml(text: string): Point;
        get componentId(): StiComponentId;
        applyStyle(style: IStiGaugeStyle): void;
        private _format;
        get format(): string;
        set format(value: string);
        private _showValue;
        get showValue(): boolean;
        set showValue(value: boolean);
        private _textBrush;
        get textBrush(): StiBrush;
        set textBrush(value: StiBrush);
        private _font;
        get font(): Font;
        set font(value: Font);
        private _capBrush;
        get capBrush(): StiBrush;
        set capBrush(value: StiBrush);
        private _capBorderBrush;
        get capBorderBrush(): StiBrush;
        set capBorderBrush(value: StiBrush);
        private _capBorderWidth;
        get capBorderWidth(): number;
        set capBorderWidth(value: number);
        offsetNeedle: number;
        startWidth: number;
        endWidth: number;
        private _autoCalculateCenterPoint;
        get autoCalculateCenterPoint(): boolean;
        set autoCalculateCenterPoint(value: boolean);
        private _centerPoint;
        get centerPoint(): Point;
        set centerPoint(value: Point);
        private _relativeHeight;
        get relativeHeight(): number;
        set relativeHeight(value: number);
        private _relativeWidth;
        get relativeWidth(): number;
        set relativeWidth(value: number);
        private _skin;
        get skin(): Stimulsoft.Report.Gauge.StiNeedleSkin;
        set skin(value: Stimulsoft.Report.Gauge.StiNeedleSkin);
        customSkin: StiGaugeElementSkin;
        get elementType(): StiGaugeElemenType;
        get localizeName(): string;
        createNew(): StiGaugeElement;
        drawElement(context: StiGaugeContextPainter): any;
        interactiveClick(rect: Rectangle, p: Point): void;
        private getActualCenterPoint;
        private getActualSkin;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Components.Gauge.Primitives {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Font = Stimulsoft.System.Drawing.Font;
    import StiGaugeElementSkin = Stimulsoft.Report.Gauge.StiGaugeElementSkin;
    import IStiGaugeMarker = Stimulsoft.Report.Gauge.IStiGaugeMarker;
    class StiMarkerBase extends StiIndicatorBase implements IStiGaugeMarker, IStiJsonReportObject {
        meta(): StiMeta[];
        clone(): StiMarkerBase;
        private _offset;
        get offset(): number;
        set offset(value: number);
        private _relativeWidth;
        get relativeWidth(): number;
        set relativeWidth(value: number);
        private _relativeHeight;
        get relativeHeight(): number;
        set relativeHeight(value: number);
        private _skin;
        get skin(): Stimulsoft.Report.Gauge.StiMarkerSkin;
        set skin(value: Stimulsoft.Report.Gauge.StiMarkerSkin);
        customSkin: StiGaugeElementSkin;
        private _format;
        get format(): string;
        set format(value: string);
        private _showValue;
        get showValue(): boolean;
        set showValue(value: boolean);
        private _textBrush;
        get textBrush(): StiBrush;
        set textBrush(value: StiBrush);
        private _font;
        get font(): Font;
        set font(value: Font);
        getActualSkin(): StiGaugeElementSkin;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import IStiGaugeStyle = Stimulsoft.Report.Gauge.IStiGaugeStyle;
    import StiMarkerBase = Stimulsoft.Report.Components.Gauge.Primitives.StiMarkerBase;
    class StiLinearMarker extends StiMarkerBase implements IStiJsonReportObject {
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        applyStyle(style: IStiGaugeStyle): void;
        get localizeName(): string;
        createNew(): StiGaugeElement;
        drawElement(context: StiGaugeContextPainter): void;
        private getRectangle;
        interactiveClick(rect: Rectangle, p: Point): void;
        private getBarPosition;
    }
}
declare namespace Stimulsoft.Report.Gauge.Helpers {
    import Color = Stimulsoft.System.Drawing.Color;
    class StiMixedColorHelper {
        static colorMixed(colors: Color[]): Color;
        private static colorMixer;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge.Primitives {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import StiBarRangeListCollection = Stimulsoft.Report.Gauge.Collections.StiBarRangeListCollection;
    import StiBarRangeListType = Stimulsoft.Report.Gauge.StiBarRangeListType;
    class StiBarBase extends StiIndicatorBase implements IStiJsonReportObject {
        meta(): StiMeta[];
        clone(): StiBarBase;
        private _emptyBrush;
        get emptyBrush(): StiBrush;
        set emptyBrush(value: StiBrush);
        private _emptyBorderBrush;
        get emptyBorderBrush(): StiBrush;
        set emptyBorderBrush(value: StiBrush);
        private _emptyBorderWidth;
        get emptyBorderWidth(): number;
        set emptyBorderWidth(value: number);
        private _offset;
        get offset(): number;
        set offset(value: number);
        private _startWidth;
        get startWidth(): number;
        set startWidth(value: number);
        private _endWidth;
        get endWidth(): number;
        set endWidth(value: number);
        private _useRangeColor;
        get useRangeColor(): boolean;
        set useRangeColor(value: boolean);
        rangeList: StiBarRangeListCollection;
        get barType(): StiBarRangeListType;
        onRangeColorChanged(): void;
        checkActualBrushForTopGeometry(): void;
        onValueChanged(): void;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Components.Gauge {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import IStiGaugeStyle = Stimulsoft.Report.Gauge.IStiGaugeStyle;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import StiBarBase = Stimulsoft.Report.Components.Gauge.Primitives.StiBarBase;
    import Point = Stimulsoft.System.Drawing.Point;
    class StiLinearBar extends StiBarBase implements IStiJsonReportObject {
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        applyStyle(style: IStiGaugeStyle): void;
        private colorModeHelper;
        private actualBackground;
        private _skin;
        get skin(): Stimulsoft.Report.Gauge.StiLinearBarSkin;
        set skin(value: Stimulsoft.Report.Gauge.StiLinearBarSkin);
        private _rangeColorMode;
        get rangeColorMode(): Stimulsoft.Report.Gauge.StiLinearRangeColorMode;
        set rangeColorMode(value: Stimulsoft.Report.Gauge.StiLinearRangeColorMode);
        onRangeColorChanged(): void;
        get barType(): Stimulsoft.Report.Gauge.StiBarRangeListType;
        get localizeName(): string;
        checkActualBrushForTopGeometry(): void;
        private getRangeBrush;
        createNew(): StiGaugeElement;
        interactiveClick(rect: Rectangle, p: Point): void;
        drawElement(context: StiGaugeContextPainter): void;
        private drawHorizontalThermometer;
        private drawVerticalThermometer;
        private getGeometryHelperForTopIndicator;
        private getTopGeometry;
    }
}
declare namespace Stimulsoft.Report.Gauge.Events {
    import StiEvent = Stimulsoft.Report.Events.StiEvent;
    class StiGetSkipValuesEvent extends StiEvent {
        toString(): string;
    }
}
declare namespace Stimulsoft.Report.Gauge.Events {
    import StiEvent = Stimulsoft.Report.Events.StiEvent;
    class StiGetSkipIndicesEvent extends StiEvent {
        toString(): string;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge.Primitives {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiGetSkipValuesEvent = Stimulsoft.Report.Gauge.Events.StiGetSkipValuesEvent;
    import StiGetSkipIndicesEvent = Stimulsoft.Report.Gauge.Events.StiGetSkipIndicesEvent;
    import StiGetValueEventArgs = Stimulsoft.Report.Events.StiGetValueEventArgs;
    import Hashtable = Stimulsoft.System.Collections.Hashtable;
    class StiTickBase extends StiGaugeElement implements IStiJsonReportObject {
        meta(): StiMeta[];
        clone(): StiTickBase;
        onGetSkipValues(e: StiGetValueEventArgs): void;
        invokeGetSkipValues(sender: StiGaugeElement, e: StiGetValueEventArgs): void;
        private _getSkipValuesEvent;
        get getSkipValuesEvent(): StiGetSkipValuesEvent;
        set getSkipValuesEvent(value: StiGetSkipValuesEvent);
        onGetSkipIndices(e: StiGetValueEventArgs): void;
        invokeGetSkipIndices(sender: StiGaugeElement, e: StiGetValueEventArgs): void;
        private _getSkipIndicesEvent;
        get getSkipIndicesEvent(): StiGetSkipIndicesEvent;
        set getSkipIndicesEvent(value: StiGetSkipIndicesEvent);
        private _skipValues;
        get skipValues(): string;
        set skipValues(value: string);
        private _skipIndices;
        get skipIndices(): string;
        set skipIndices(value: string);
        private _placement;
        get placement(): Stimulsoft.Report.Gauge.StiPlacement;
        set placement(value: Stimulsoft.Report.Gauge.StiPlacement);
        private _skipValuesObj;
        get skipValuesObj(): number[];
        set skipValuesObj(value: number[]);
        private _skipIndicesObj;
        get skipIndicesObj(): number[];
        set skipIndicesObj(value: number[]);
        private _offset;
        get offset(): number;
        set offset(value: number);
        private _minimumValue;
        get minimumValue(): number;
        set minimumValue(value: number);
        private _maximumValue;
        get maximumValue(): number;
        set maximumValue(value: number);
        get isSkipMajorValues(): boolean;
        getPointCollection(): Hashtable;
        getMinorCollections(): Hashtable;
        getMajorCollections(): Hashtable;
        checkTickValue(skipValues: number[], skipIndices: number[], key: number, value: number): boolean;
        prepareGaugeElement(): void;
        getOffset(value: number): number;
        getPlacement(value: Stimulsoft.Report.Gauge.StiPlacement): Stimulsoft.Report.Gauge.StiPlacement;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge.Primitives {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Font = Stimulsoft.System.Drawing.Font;
    import StiFormatService = Stimulsoft.Report.Components.TextFormats.StiFormatService;
    class StiTickLabelBase extends StiTickBase implements IStiJsonReportObject {
        meta(): StiMeta[];
        clone(): StiTickLabelBase;
        private _textFormat;
        get textFormat(): string;
        set textFormat(value: string);
        private _formatService;
        get formatService(): StiFormatService;
        set formatService(value: StiFormatService);
        private _textBrush;
        get textBrush(): StiBrush;
        set textBrush(value: StiBrush);
        private _font;
        get font(): Font;
        set font(value: Font);
        getTextForRender(value: number, format: string): string;
        getTextForRender2(value: string, format?: string): string;
    }
}
declare namespace Stimulsoft.Report.Gauge.Helpers {
    import Dictionary = Stimulsoft.System.Collections.Dictionary;
    import Hashtable = Stimulsoft.System.Collections.Hashtable;
    class CacheInfo {
        valueKey: number;
        valueStr: string;
        count: number;
        toString(): string;
        constructor(valueKey: number, valueStr: string, count: number);
    }
    class StiTickLabelHelper {
        static getLabels(collection: Hashtable): Dictionary<number, string>;
        private static prepare;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge.Primitives {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiTickLabelBase = Stimulsoft.Report.Components.Gauge.Primitives.StiTickLabelBase;
    import StiGaugeElemenType = Stimulsoft.Report.Gauge.StiGaugeElemenType;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    import Point = Stimulsoft.System.Drawing.Point;
    import Size = Stimulsoft.System.Drawing.Size;
    class StiRadialTickLabelBase extends StiTickLabelBase implements IStiJsonReportObject {
        meta(): StiMeta[];
        private _labelRotationMode;
        get labelRotationMode(): Stimulsoft.Report.Gauge.StiLabelRotationMode;
        set labelRotationMode(value: Stimulsoft.Report.Gauge.StiLabelRotationMode);
        private _offsetAngle;
        get offsetAngle(): number;
        set offsetAngle(value: number);
        get elementType(): StiGaugeElemenType;
        drawElement(context: StiGaugeContextPainter): void;
        getMatrixRotation(context: StiGaugeContextPainter, centerPoint: Point, textSize: Size, rotateMode: Stimulsoft.Report.Gauge.StiLabelRotationMode, radius: number, angle: number, REFposition: any): number;
        private getRadialPosition;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import Hashtable = Stimulsoft.System.Collections.Hashtable;
    import StiGaugeElemenType = Stimulsoft.Report.Gauge.StiGaugeElemenType;
    import StiRadialTickLabelBase = Stimulsoft.Report.Components.Gauge.Primitives.StiRadialTickLabelBase;
    import IStiGaugeStyle = Stimulsoft.Report.Gauge.IStiGaugeStyle;
    class StiRadialTickLabelMinor extends StiRadialTickLabelBase implements IStiJsonReportObject {
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        applyStyle(style: IStiGaugeStyle): void;
        private _skipMajorValues;
        get skipMajorValues(): boolean;
        set skipMajorValues(value: boolean);
        get isSkipMajorValues(): boolean;
        get elementType(): StiGaugeElemenType;
        get localizeName(): string;
        createNew(): StiGaugeElement;
        getPointCollection(): Hashtable;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import Hashtable = Stimulsoft.System.Collections.Hashtable;
    import StiGaugeElemenType = Stimulsoft.Report.Gauge.StiGaugeElemenType;
    import StiRadialTickLabelBase = Stimulsoft.Report.Components.Gauge.Primitives.StiRadialTickLabelBase;
    import IStiGaugeStyle = Stimulsoft.Report.Gauge.IStiGaugeStyle;
    class StiRadialTickLabelMajor extends StiRadialTickLabelBase {
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        applyStyle(style: IStiGaugeStyle): void;
        get elementType(): StiGaugeElemenType;
        get localizeName(): string;
        createNew(): StiGaugeElement;
        getPointCollection(): Hashtable;
    }
}
declare namespace Stimulsoft.Report.Gauge.Events {
    import EventHandler = Stimulsoft.System.EventHandler;
    import EventArgs = Stimulsoft.System.EventArgs;
    let StiGetTextEventHandler: EventHandler;
    class StiGetTextEventArgs extends EventArgs {
        private _value;
        get value(): string;
        set value(value: string);
    }
}
declare namespace Stimulsoft.Report.Gauge.Events {
    import StiEvent = Stimulsoft.Report.Events.StiEvent;
    class StiGetTextEvent extends StiEvent {
        toString(): string;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiGetTextEventArgs = Stimulsoft.Report.Gauge.Events.StiGetTextEventArgs;
    import StiGetTextEvent = Stimulsoft.Report.Gauge.Events.StiGetTextEvent;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGetValueEventArgs = Stimulsoft.Report.Events.StiGetValueEventArgs;
    import StiGaugeElemenType = Stimulsoft.Report.Gauge.StiGaugeElemenType;
    import StiGetValueEvent = Stimulsoft.Report.Events.StiGetValueEvent;
    import StiCustomValuesCollection = Stimulsoft.Report.Gauge.Collections.StiCustomValuesCollection;
    import StiRadialTickLabelBase = Stimulsoft.Report.Components.Gauge.Primitives.StiRadialTickLabelBase;
    import IStiTickCustom = Stimulsoft.Report.Gauge.Primitives.IStiTickCustom;
    class StiRadialTickLabelCustom extends StiRadialTickLabelBase implements IStiTickCustom, IStiJsonReportObject {
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        clone(): StiRadialTickLabelCustom;
        valueObj: number;
        textObj: string;
        values: StiCustomValuesCollection;
        get elementType(): StiGaugeElemenType;
        get localizeName(): string;
        onGetValue(e: StiGetValueEventArgs): void;
        invokeGetValue(sender: StiGaugeElement, e: StiGetValueEventArgs): void;
        getValueEvent: StiGetValueEvent;
        onGetText(e: StiGetTextEventArgs): void;
        invokeGetText(sender: StiGaugeElement, e: StiGetTextEventArgs): void;
        getTextEvent: StiGetTextEvent;
        value: string;
        text: string;
        createNew(): StiGaugeElement;
        prepareGaugeElement(): void;
        drawElement(context: StiGaugeContextPainter): void;
        private getOffsetAngle;
        private getLabelRotationMode;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge.Primitives {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiGaugeElementSkin = Stimulsoft.Report.Gauge.StiGaugeElementSkin;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import StiTickBase = Stimulsoft.Report.Components.Gauge.Primitives.StiTickBase;
    class StiTickMarkBase extends StiTickBase implements IStiJsonReportObject {
        meta(): StiMeta[];
        clone(): StiTickMarkBase;
        private _relativeHeight;
        get relativeHeight(): number;
        set relativeHeight(value: number);
        private _relativeWidth;
        get relativeWidth(): number;
        set relativeWidth(value: number);
        private _skin;
        get skin(): Stimulsoft.Report.Gauge.StiTickMarkSkin;
        set skin(value: Stimulsoft.Report.Gauge.StiTickMarkSkin);
        customSkin: StiGaugeElementSkin;
        private _brush;
        get brush(): StiBrush;
        set brush(value: StiBrush);
        private _borderBrush;
        get borderBrush(): StiBrush;
        set borderBrush(value: StiBrush);
        private _borderWidth;
        get borderWidth(): number;
        set borderWidth(value: number);
        getActualSkin(): StiGaugeElementSkin;
        getRelativeWidth(value: number): number;
        getRelativeHeight(value: number): number;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge.Primitives {
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    class StiLinearTickMarkBase extends StiTickMarkBase {
        drawElement(context: StiGaugeContextPainter): void;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import Hashtable = Stimulsoft.System.Collections.Hashtable;
    import StiLinearTickMarkBase = Stimulsoft.Report.Components.Gauge.Primitives.StiLinearTickMarkBase;
    import IStiGaugeStyle = Stimulsoft.Report.Gauge.IStiGaugeStyle;
    class StiLinearTickMarkMinor extends StiLinearTickMarkBase implements IStiJsonReportObject {
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        applyStyle(style: IStiGaugeStyle): void;
        private _skipMajorValues;
        get skipMajorValues(): boolean;
        set skipMajorValues(value: boolean);
        get isSkipMajorValues(): boolean;
        get localizeName(): string;
        createNew(): StiGaugeElement;
        getPointCollection(): Hashtable;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import Hashtable = Stimulsoft.System.Collections.Hashtable;
    import StiLinearTickMarkBase = Stimulsoft.Report.Components.Gauge.Primitives.StiLinearTickMarkBase;
    import IStiGaugeStyle = Stimulsoft.Report.Gauge.IStiGaugeStyle;
    class StiLinearTickMarkMajor extends StiLinearTickMarkBase {
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        applyStyle(style: IStiGaugeStyle): void;
        get localizeName(): string;
        createNew(): StiGaugeElement;
        getPointCollection(): Hashtable;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    import StiGetValueEventArgs = Stimulsoft.Report.Events.StiGetValueEventArgs;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGetValueEvent = Stimulsoft.Report.Events.StiGetValueEvent;
    import StiLinearTickMarkBase = Stimulsoft.Report.Components.Gauge.Primitives.StiLinearTickMarkBase;
    import IStiTickCustom = Stimulsoft.Report.Gauge.Primitives.IStiTickCustom;
    import StiCustomValuesCollection = Stimulsoft.Report.Gauge.Collections.StiCustomValuesCollection;
    class StiLinearTickMarkCustom extends StiLinearTickMarkBase implements IStiTickCustom, IStiJsonReportObject {
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        clone(): StiLinearTickMarkCustom;
        private _valueObj;
        get valueObj(): number;
        set valueObj(value: number);
        values: StiCustomValuesCollection;
        get localizeName(): string;
        onGetValue(e: StiGetValueEventArgs): void;
        invokeGetValue(sender: StiGaugeElement, e: StiGetValueEventArgs): void;
        private _getValueEvent;
        get getValueEvent(): StiGetValueEvent;
        set getValueEvent(value: StiGetValueEvent);
        private _value;
        get value(): string;
        set value(value: string);
        createNew(): StiGaugeElement;
        prepareGaugeElement(): void;
        drawElement(context: StiGaugeContextPainter): void;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge.Primitives {
    import StiTickLabelBase = Stimulsoft.Report.Components.Gauge.Primitives.StiTickLabelBase;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    class StiLinearTickLabelBase extends StiTickLabelBase {
        drawElement(context: StiGaugeContextPainter): void;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import Hashtable = Stimulsoft.System.Collections.Hashtable;
    import StiLinearTickLabelBase = Stimulsoft.Report.Components.Gauge.Primitives.StiLinearTickLabelBase;
    import IStiGaugeStyle = Stimulsoft.Report.Gauge.IStiGaugeStyle;
    class StiLinearTickLabelMinor extends StiLinearTickLabelBase implements IStiJsonReportObject {
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        applyStyle(style: IStiGaugeStyle): void;
        private _skipMajorValues;
        get skipMajorValues(): boolean;
        set skipMajorValues(value: boolean);
        get isSkipMajorValues(): boolean;
        get localizeName(): string;
        createNew(): StiGaugeElement;
        getPointCollection(): Hashtable;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import Hashtable = Stimulsoft.System.Collections.Hashtable;
    import StiLinearTickLabelBase = Stimulsoft.Report.Components.Gauge.Primitives.StiLinearTickLabelBase;
    import IStiGaugeStyle = Stimulsoft.Report.Gauge.IStiGaugeStyle;
    class StiLinearTickLabelMajor extends StiLinearTickLabelBase {
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        applyStyle(style: IStiGaugeStyle): void;
        get localizeName(): string;
        createNew(): StiGaugeElement;
        getPointCollection(): Hashtable;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge.Primitives {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    import StiGaugeElemenType = Stimulsoft.Report.Gauge.StiGaugeElemenType;
    import StiTickMarkBase = Stimulsoft.Report.Components.Gauge.Primitives.StiTickMarkBase;
    class StiRadialTickMarkBase extends StiTickMarkBase implements IStiJsonReportObject {
        meta(): StiMeta[];
        private _offsetAngle;
        get offsetAngle(): number;
        set offsetAngle(value: number);
        get elementType(): StiGaugeElemenType;
        drawElement(context: StiGaugeContextPainter): void;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiRadialTickMarkBase = Stimulsoft.Report.Components.Gauge.Primitives.StiRadialTickMarkBase;
    import IStiTickCustom = Stimulsoft.Report.Gauge.Primitives.IStiTickCustom;
    import StiGetValueEvent = Stimulsoft.Report.Events.StiGetValueEvent;
    import StiCustomValuesCollection = Stimulsoft.Report.Gauge.Collections.StiCustomValuesCollection;
    import StiGetValueEventArgs = Stimulsoft.Report.Events.StiGetValueEventArgs;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGaugeElemenType = Stimulsoft.Report.Gauge.StiGaugeElemenType;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    class StiRadialTickMarkCustom extends StiRadialTickMarkBase implements IStiTickCustom, IStiJsonReportObject {
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        clone(): StiRadialTickMarkCustom;
        valueObj: number;
        values: StiCustomValuesCollection;
        onGetValue(e: StiGetValueEventArgs): void;
        invokeGetValue(sender: StiGaugeElement, e: StiGetValueEventArgs): void;
        private _getValueEvent;
        get getValueEvent(): StiGetValueEvent;
        set getValueEvent(value: StiGetValueEvent);
        private _value;
        get value(): string;
        set value(value: string);
        get elementType(): StiGaugeElemenType;
        get localizeName(): string;
        createNew(): StiGaugeElement;
        prepareGaugeElement(): void;
        drawElement(context: StiGaugeContextPainter): void;
        private getOffsetAngle;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import StiRadialTickMarkBase = Stimulsoft.Report.Components.Gauge.Primitives.StiRadialTickMarkBase;
    import IStiGaugeStyle = Stimulsoft.Report.Gauge.IStiGaugeStyle;
    import StiGaugeElemenType = Stimulsoft.Report.Gauge.StiGaugeElemenType;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import Hashtable = Stimulsoft.System.Collections.Hashtable;
    class StiRadialTickMarkMajor extends StiRadialTickMarkBase {
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        applyStyle(style: IStiGaugeStyle): void;
        get elementType(): StiGaugeElemenType;
        get localizeName(): string;
        createNew(): StiGaugeElement;
        getPointCollection(): Hashtable;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiRadialTickMarkBase = Stimulsoft.Report.Components.Gauge.Primitives.StiRadialTickMarkBase;
    import IStiGaugeStyle = Stimulsoft.Report.Gauge.IStiGaugeStyle;
    import StiGaugeElemenType = Stimulsoft.Report.Gauge.StiGaugeElemenType;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import Hashtable = Stimulsoft.System.Collections.Hashtable;
    class StiRadialTickMarkMinor extends StiRadialTickMarkBase implements IStiJsonReportObject {
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        applyStyle(style: IStiGaugeStyle): void;
        private _skipMajorValues;
        get skipMajorValues(): boolean;
        set skipMajorValues(value: boolean);
        get isSkipMajorValues(): boolean;
        get elementType(): StiGaugeElemenType;
        get localizeName(): string;
        createNew(): StiGaugeElement;
        getPointCollection(): Hashtable;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiBarRangeListType = Stimulsoft.Report.Gauge.StiBarRangeListType;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import ICloneable = Stimulsoft.System.ICloneable;
    import IStiIndicatorRangeInfo = Stimulsoft.Report.Components.Gauge.IStiIndicatorRangeInfo;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    class StiIndicatorRangeInfo implements ICloneable, IStiIndicatorRangeInfo, IStiJsonReportObject {
        protected _hash: StiMeta[];
        meta(): StiMeta[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xmlNode: XmlNode, report: StiReport): void;
        get componentId(): StiComponentId;
        get propName(): string;
        clone(): any;
        private _value;
        get value(): number;
        set value(value: number);
        get rangeListType(): StiBarRangeListType;
        createNew(): StiIndicatorRangeInfo;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiBarRangeListType = Stimulsoft.Report.Gauge.StiBarRangeListType;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    class StiLinearIndicatorRangeInfo extends StiIndicatorRangeInfo implements IStiJsonReportObject {
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        private _color;
        get color(): Color;
        set color(value: Color);
        private _brush;
        get brush(): StiBrush;
        set brush(value: StiBrush);
        get rangeListType(): StiBarRangeListType;
        createNew(): StiIndicatorRangeInfo;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import IStiGaugeStyle = Stimulsoft.Report.Gauge.IStiGaugeStyle;
    import StiBarBase = Stimulsoft.Report.Components.Gauge.Primitives.StiBarBase;
    import StiGaugeElemenType = Stimulsoft.Report.Gauge.StiGaugeElemenType;
    import Point = Stimulsoft.System.Drawing.Point;
    class StiRadialBar extends StiBarBase implements IStiJsonReportObject {
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        applyStyle(style: IStiGaugeStyle): void;
        private actualBush;
        private colorModeHelper;
        get elementType(): StiGaugeElemenType;
        get barType(): Stimulsoft.Report.Gauge.StiBarRangeListType;
        get localizeName(): string;
        checkActualBrushForTopGeometry(): void;
        createNew(): StiGaugeElement;
        drawElement(context: StiGaugeContextPainter): void;
        onRangeColorChanged(): void;
        interactiveClick(rect: Rectangle, p: Point): void;
        private getRangeGeometry;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiBarRangeListType = Stimulsoft.Report.Gauge.StiBarRangeListType;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    class StiRadialIndicatorRangeInfo extends StiIndicatorRangeInfo implements IStiJsonReportObject {
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        private _brush;
        get brush(): StiBrush;
        set brush(value: StiBrush);
        get rangeListType(): StiBarRangeListType;
        createNew(): StiIndicatorRangeInfo;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGaugeElemenType = Stimulsoft.Report.Gauge.StiGaugeElemenType;
    import IStiGaugeStyle = Stimulsoft.Report.Gauge.IStiGaugeStyle;
    import StiMarkerBase = Stimulsoft.Report.Components.Gauge.Primitives.StiMarkerBase;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    class StiRadialMarker extends StiMarkerBase implements IStiJsonReportObject {
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        applyStyle(style: IStiGaugeStyle): void;
        get elementType(): StiGaugeElemenType;
        get localizeName(): string;
        createNew(): StiGaugeElement;
        drawElement(context: StiGaugeContextPainter): void;
        interactiveClick(rect: Rectangle, p: Point): void;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    import StiFilterCollection = Stimulsoft.Report.Gauge.Collections.StiFilterCollection;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import Font = Stimulsoft.System.Drawing.Font;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiIndicatorBase = Stimulsoft.Report.Components.Gauge.Primitives.StiIndicatorBase;
    import IStiGaugeMarker = Stimulsoft.Report.Gauge.IStiGaugeMarker;
    class StiStateIndicator extends StiIndicatorBase implements IStiGaugeMarker, IStiJsonReportObject {
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        private lastFilter;
        private _format;
        get format(): string;
        set format(value: string);
        private _showValue;
        get showValue(): boolean;
        set showValue(value: boolean);
        private _textBrush;
        get textBrush(): StiBrush;
        set textBrush(value: StiBrush);
        private _font;
        get font(): Font;
        set font(value: Font);
        get elementType(): Stimulsoft.Report.Gauge.StiGaugeElemenType;
        get localizeName(): string;
        filters: StiFilterCollection;
        private _left;
        get left(): number;
        set left(value: number);
        private _top;
        get top(): number;
        set top(value: number);
        private _relativeWidth;
        get relativeWidth(): number;
        set relativeWidth(value: number);
        private _relativeHeight;
        get relativeHeight(): number;
        set relativeHeight(value: number);
        private _skin;
        get skin(): Stimulsoft.Report.Gauge.StiStateSkin;
        set skin(value: Stimulsoft.Report.Gauge.StiStateSkin);
        private _customSkin;
        get customSkin(): Stimulsoft.Report.Gauge.StiGaugeElementSkin;
        set customSkin(value: Stimulsoft.Report.Gauge.StiGaugeElementSkin);
        createNew(): StiGaugeElement;
        onValueChanged(): void;
        interactiveClick(rect: Rectangle, p: Point): void;
        drawElement(context: StiGaugeContextPainter): void;
        getActualSkin(): Stimulsoft.Report.Gauge.StiGaugeElementSkin;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge.Primitives {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    import StiPlacement = Stimulsoft.Report.Gauge.StiPlacement;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import IStiRangeBase = Stimulsoft.Report.Components.Gauge.IStiRangeBase;
    class StiRangeBase implements ICloneable, IStiRangeBase, IStiJsonReportObject {
        protected _hash: StiMeta[];
        meta(): StiMeta[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        get componentId(): StiComponentId;
        get propName(): string;
        clone(): StiRangeBase;
        private _brush;
        get brush(): StiBrush;
        set brush(value: StiBrush);
        private _borderBrush;
        get borderBrush(): StiBrush;
        set borderBrush(value: StiBrush);
        private _borderWidth;
        get borderWidth(): number;
        set borderWidth(value: number);
        private _startValue;
        get startValue(): number;
        set startValue(value: number);
        private _endValue;
        get endValue(): number;
        set endValue(value: number);
        private _startWidth;
        get startWidth(): number;
        set startWidth(value: number);
        private _endWidth;
        get endWidth(): number;
        set endWidth(value: number);
        private _placement;
        get placement(): StiPlacement;
        set placement(value: StiPlacement);
        private _offset;
        get offset(): number;
        set offset(value: number);
        rangeList: StiScaleRangeList;
        get localizeName(): string;
        drawRange(context: StiGaugeContextPainter, scale: StiScaleBase): void;
        createNew(): StiRangeBase;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    import StiRangeBase = Stimulsoft.Report.Components.Gauge.Primitives.StiRangeBase;
    import StiScaleBase = Stimulsoft.Report.Components.Gauge.Primitives.StiScaleBase;
    class StiLinearRange extends StiRangeBase implements IStiJsonReportObject {
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        drawRange(context: StiGaugeContextPainter, scale: StiScaleBase): void;
        get localizeName(): string;
        createNew(): StiRangeBase;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiScaleBase = Stimulsoft.Report.Components.Gauge.Primitives.StiScaleBase;
    import StiRangeBase = Stimulsoft.Report.Components.Gauge.Primitives.StiRangeBase;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    class StiRadialRange extends StiRangeBase implements IStiJsonReportObject {
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        useValuesFromTheSpecifiedRange: boolean;
        get localizeName(): string;
        drawRange(context: StiGaugeContextPainter, scale: StiScaleBase): void;
        createNew(): StiRangeBase;
    }
}
declare namespace Stimulsoft.Report.Gauge.Collections {
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import CollectionBase = Stimulsoft.System.Collections.CollectionBase;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiRangeBase = Stimulsoft.Report.Components.Gauge.Primitives.StiRangeBase;
    import StiScaleRangeList = Stimulsoft.Report.Components.Gauge.Primitives.StiScaleRangeList;
    class StiRangeCollection extends CollectionBase<StiRangeBase> implements ICloneable, IStiJsonReportObject {
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(jObject: StiJson): void;
        loadFromXml(xmlNode: XmlNode): any;
        private parent;
        clone(): StiRangeCollection;
        get isReadOnly(): boolean;
        setByIndex(index: number, value: StiRangeBase): void;
        setParent(element: StiRangeBase): void;
        clearParent(element: StiRangeBase): void;
        add(element: StiRangeBase): void;
        insert(index: number, element: StiRangeBase): void;
        remove(element: StiRangeBase): boolean;
        copyTo(elements: StiRangeBase[], arrayIndex: number): void;
        moveUp(element: StiRangeBase): boolean;
        moveDown(element: StiRangeBase): boolean;
        constructor(parent: StiScaleRangeList);
    }
}
declare namespace Stimulsoft.Report.Components.Gauge.Primitives {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiRangeCollection = Stimulsoft.Report.Gauge.Collections.StiRangeCollection;
    class StiScaleRangeList extends StiGaugeElement implements IStiJsonReportObject {
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        clone(): StiScaleRangeList;
        ranges: StiRangeCollection;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Components.Gauge {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiScaleRangeList = Stimulsoft.Report.Components.Gauge.Primitives.StiScaleRangeList;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    class StiLinearRangeList extends StiScaleRangeList implements IStiJsonReportObject {
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        createNew(): StiGaugeElement;
        drawElement(context: StiGaugeContextPainter): void;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiGaugeElemenType = Stimulsoft.Report.Gauge.StiGaugeElemenType;
    import StiScaleRangeList = Stimulsoft.Report.Components.Gauge.Primitives.StiScaleRangeList;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    class StiRadialRangeList extends StiScaleRangeList implements IStiJsonReportObject {
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        get elementType(): StiGaugeElemenType;
        createNew(): StiGaugeElement;
        drawElement(context: StiGaugeContextPainter): void;
    }
}
declare namespace Stimulsoft.Report.Gauge.Helpers {
    import IStiScaleBase = Stimulsoft.Report.Components.Gauge.IStiScaleBase;
    import TimeSpan = Stimulsoft.System.TimeSpan;
    import StiGauge = Stimulsoft.Report.Components.StiGauge;
    class StiGaugeHelper {
        static globalDurationElement: TimeSpan;
        static globalBeginTimeElement: TimeSpan;
        private static currentCulture;
        static getFloatValueFromObject(valueObj: any, scale: IStiScaleBase): number;
        static getFloatValueFromObject2(valueObj: any, defaultValue: number): number;
        static getFloatArrayValueFromString(value: any): number[];
        private static initializeGauge;
        private static initializeName;
        static checkGaugeName(gauge: StiGauge): void;
        static simpleRadialGauge(gauge: StiGauge, report: StiReport): void;
        static radialTwoScalesGauge(gauge: StiGauge, report: StiReport): void;
        static radialBarGauge(gauge: StiGauge, report: StiReport): void;
        static simpleTwoBarGauge(gauge: StiGauge, report: StiReport): void;
        static defaultRadialGauge(gauge: StiGauge, report: StiReport): void;
        static defaultLinearGauge(gauge: StiGauge, report: StiReport): void;
        static linearGaugeRangeList(gauge: StiGauge, report: StiReport): void;
        static bulletGraphsGreen(gauge: StiGauge, report: StiReport): void;
        static halfDonutsGauge(gauge: StiGauge, report: StiReport): void;
        static halfDonutsGauge2(gauge: StiGauge, report: StiReport): void;
        static radialGaugeHalfCircleN(gauge: StiGauge, report: StiReport): void;
        static radialGaugeHalfCircleS(gauge: StiGauge, report: StiReport): void;
        static radialGaugeQuarterCircleNW(gauge: StiGauge, report: StiReport): void;
        static radialGaugeQuarterCircleNE(gauge: StiGauge, report: StiReport): void;
        static radialGaugeQuarterCircleSW(gauge: StiGauge, report: StiReport): void;
        static radialGaugeQuarterCircleSE(gauge: StiGauge, report: StiReport): void;
        private static radialGaugeQuarterCircle;
        static horizontalThermometer(gauge: StiGauge, report: StiReport): void;
        static verticalThermometer(gauge: StiGauge, report: StiReport): void;
        static lightSpeedometer(gauge: StiGauge, report: StiReport): void;
        static darkSpeedometer(gauge: StiGauge, report: StiReport): void;
    }
}
declare namespace Stimulsoft.Report.Components.Gauge {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiGetTextEventArgs = Stimulsoft.Report.Gauge.Events.StiGetTextEventArgs;
    import StiGetTextEvent = Stimulsoft.Report.Gauge.Events.StiGetTextEvent;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    import StiGetValueEventArgs = Stimulsoft.Report.Events.StiGetValueEventArgs;
    import StiCustomValuesCollection = Stimulsoft.Report.Gauge.Collections.StiCustomValuesCollection;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiGetValueEvent = Stimulsoft.Report.Events.StiGetValueEvent;
    import StiLinearTickLabelBase = Stimulsoft.Report.Components.Gauge.Primitives.StiLinearTickLabelBase;
    import IStiTickCustom = Stimulsoft.Report.Gauge.Primitives.IStiTickCustom;
    class StiLinearTickLabelCustom extends StiLinearTickLabelBase implements IStiTickCustom, IStiJsonReportObject {
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        clone(): StiLinearTickLabelCustom;
        private _valueObj;
        get valueObj(): number;
        set valueObj(value: number);
        private _textObj;
        get textObj(): string;
        set textObj(value: string);
        values: StiCustomValuesCollection;
        get localizeName(): string;
        onGetValue(e: StiGetValueEventArgs): void;
        invokeGetValue(sender: StiGaugeElement, e: StiGetValueEventArgs): void;
        private _getValueEvent;
        get getValueEvent(): StiGetValueEvent;
        set getValueEvent(value: StiGetValueEvent);
        onGetText(e: StiGetTextEventArgs): void;
        invokeGetText(sender: StiGaugeElement, e: StiGetTextEventArgs): void;
        private _getTextEvent;
        get getTextEvent(): StiGetTextEvent;
        set getTextEvent(value: StiGetTextEvent);
        private _value;
        get value(): string;
        set value(value: string);
        private _text;
        get text(): string;
        set text(value: string);
        createNew(): StiGaugeElement;
        prepareGaugeElement(): void;
        drawElement(context: StiGaugeContextPainter): void;
    }
}
declare namespace Stimulsoft.Report.Gauge.Collections {
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import CollectionBase = Stimulsoft.System.Collections.CollectionBase;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiGaugeElement = Stimulsoft.Report.Components.Gauge.Primitives.StiGaugeElement;
    import StiScaleBase = Stimulsoft.Report.Components.Gauge.Primitives.StiScaleBase;
    class StiGaugeElementCollection extends CollectionBase<StiGaugeElement> implements ICloneable, IStiJsonReportObject {
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(jObject: StiJson): void;
        loadFromXml(xmlNode: XmlNode, report: StiReport): void;
        private scale;
        private scaleType;
        clone(): StiGaugeElementCollection;
        get isReadOnly(): boolean;
        setByIndex(index: number, value: StiGaugeElement): void;
        toArray(): StiGaugeElement[];
        private addCore;
        add(element: StiGaugeElement): void;
        addRange(elements: StiGaugeElement[]): void;
        insert(index: number, element: StiGaugeElement): void;
        remove(element: StiGaugeElement): boolean;
        copyTo(elements: StiGaugeElement[], arrayIndex: number): void;
        private setItemInternal;
        moveUp(element: StiGaugeElement): boolean;
        moveDown(element: StiGaugeElement): boolean;
        constructor(scale: StiScaleBase);
    }
}
declare namespace Stimulsoft.Report.Gauge.Events {
    import StiEvent = Stimulsoft.Report.Events.StiEvent;
    class StiGetValueEvent extends StiEvent {
        toString(): string;
    }
}
declare namespace Stimulsoft.Report.Gauge.Events {
    import EventHandler = Stimulsoft.System.EventHandler;
    import EventArgs = Stimulsoft.System.EventArgs;
    let StiGetValueEventHandler: EventHandler;
    class StiGetValueEventArgs extends EventArgs {
        private _value;
        get value(): any;
        set value(value: any);
    }
}
declare namespace Stimulsoft.Report.Gauge.Helpers {
    import Point = Stimulsoft.System.Drawing.Point;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    class StiDrawingHelper {
        static getRoundedPath(rect: Rectangle, offset: number, leftTop: number, rightTop: number, rightBottom: number, leftBottom: number): void;
        private static PiDiv180;
        private static FourDivThree;
        static getArcGeometry(rect: Rectangle, startAngle: number, sweepAngle: number, startWidth: number, endWidth: number): void;
        static getRadialRangeGeometry(centerPoint: Point, startAngle: number, sweepAngle: number, radius1: number, radius2: number, radius3: number, radius4: number): void;
        private static round;
        private static convertArcToCubicBezier;
        private static convertArcToCubicBezier2;
    }
}
declare namespace Stimulsoft.Report.Gauge.Helpers {
    import IStiGauge = Stimulsoft.Report.Components.Gauge.IStiGauge;
    import StiGauge = Stimulsoft.Report.Components.StiGauge;
    class StiGaugeInitHelper {
        static isGaugeV2(gauge: IStiGauge): boolean;
        static init(gauge: StiGauge, type: StiGaugeType, skipText?: boolean): any;
        static prepare(gauge: IStiGauge): void;
        private static isFullCircularScale;
        private static createFullCircularScale;
        private static isHalfCircularScale;
        private static createHalfCircularScale;
        private static isLinearScale;
        private static createLinearScale;
        private static isBulletScale;
        private static createBullet;
        private static addLinearRanges;
    }
}
declare namespace Stimulsoft.Report.Gauge.Primitives {
    import StiGraphicsPathLinesGaugeGeom = Stimulsoft.Report.Gauge.GaugeGeoms.StiGraphicsPathLinesGaugeGeom;
    import Size = Stimulsoft.System.Drawing.Size;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiGaugeContextPainter = Stimulsoft.Report.Painters.StiGaugeContextPainter;
    let IStiScaleBarGeometry: System.Interface<IStiScaleBarGeometry>;
    interface IStiScaleBarGeometry {
        size: Size;
        rectGeometry: Rectangle;
        center: Point;
        radius: number;
        diameter: number;
        checkRectGeometry(rect: Rectangle): void;
        drawScaleGeometry(context: StiGaugeContextPainter): void;
        getRestToLenght(): number;
        drawGeometry(context: StiGaugeContextPainter, startValue: number, endValue: number, startWidth: number, endWidth: number, offset: number, placement: StiPlacement, REFrect: any, returnOnlyRect: boolean): StiGraphicsPathLinesGaugeGeom;
    }
}
declare namespace Stimulsoft.Report.Gauge.Primitives {
    import StiCustomValuesCollection = Stimulsoft.Report.Gauge.Collections.StiCustomValuesCollection;
    let IStiTickCustom: System.Interface<IStiTickCustom>;
    interface IStiTickCustom {
        valueObj: number;
        values: StiCustomValuesCollection;
    }
}
declare namespace Stimulsoft.Report.Gauge {
    import Color = Stimulsoft.System.Drawing.Color;
    import StiSolidBrush = Stimulsoft.Base.Drawing.StiSolidBrush;
    import StiEmptyBrush = Stimulsoft.Base.Drawing.StiEmptyBrush;
    import Font = Stimulsoft.System.Drawing.Font;
    class StiGaugeStyleCoreXF24 extends StiGaugeStyleCoreXF {
        get localizedName(): string;
        brush: StiSolidBrush;
        foreColor: Color;
        borderColor: Color;
        borderWidth: number;
        targetColor: Color;
        tickMarkMajorBrush: StiSolidBrush;
        tickMarkMajorBorder: StiEmptyBrush;
        tickMarkMinorBrush: StiSolidBrush;
        tickMarkMinorBorder: StiEmptyBrush;
        tickLabelMajorTextBrush: StiSolidBrush;
        tickLabelMajorFont: Font;
        tickLabelMinorTextBrush: StiSolidBrush;
        tickLabelMinorFont: Font;
        markerBrush: StiSolidBrush;
        linearScaleBrush: StiSolidBrush;
        linearBarBrush: StiSolidBrush;
        linearBarBorderBrush: StiEmptyBrush;
        linearBarEmptyBrush: StiEmptyBrush;
        linearBarEmptyBorderBrush: StiEmptyBrush;
        linearBarStartWidth: number;
        linearBarEndWidth: number;
        radialBarBrush: StiSolidBrush;
        radialBarBorderBrush: StiEmptyBrush;
        radialBarEmptyBrush: StiSolidBrush;
        radialBarEmptyBorderBrush: StiEmptyBrush;
        radialBarStartWidth: number;
        radialBarEndWidth: number;
        needleBrush: StiSolidBrush;
        needleBorderBrush: StiEmptyBrush;
        needleCapBrush: StiSolidBrush;
        needleCapBorderBrush: StiSolidBrush;
        needleBorderWidth: number;
        needleCapBorderWidth: number;
        needleStartWidth: number;
        needleEndWidth: number;
        needleRelativeHeight: number;
        needleRelativeWith: number;
    }
}
declare namespace Stimulsoft.Report.Gauge {
    import Color = Stimulsoft.System.Drawing.Color;
    import StiSolidBrush = Stimulsoft.Base.Drawing.StiSolidBrush;
    import StiEmptyBrush = Stimulsoft.Base.Drawing.StiEmptyBrush;
    import Font = Stimulsoft.System.Drawing.Font;
    class StiGaugeStyleCoreXF26 extends StiGaugeStyleCoreXF {
        get localizedName(): string;
        brush: StiSolidBrush;
        foreColor: Color;
        borderColor: Color;
        borderWidth: number;
        targetColor: Color;
        tickMarkMajorBrush: StiSolidBrush;
        tickMarkMajorBorder: StiEmptyBrush;
        tickMarkMinorBrush: StiSolidBrush;
        tickMarkMinorBorder: StiEmptyBrush;
        tickLabelMajorTextBrush: StiSolidBrush;
        tickLabelMajorFont: Font;
        tickLabelMinorTextBrush: StiSolidBrush;
        tickLabelMinorFont: Font;
        markerBrush: StiSolidBrush;
        linearScaleBrush: StiSolidBrush;
        linearBarBrush: StiSolidBrush;
        linearBarBorderBrush: StiEmptyBrush;
        linearBarEmptyBrush: StiEmptyBrush;
        linearBarEmptyBorderBrush: StiEmptyBrush;
        linearBarStartWidth: number;
        linearBarEndWidth: number;
        radialBarBrush: StiSolidBrush;
        radialBarBorderBrush: StiEmptyBrush;
        radialBarEmptyBrush: StiSolidBrush;
        radialBarEmptyBorderBrush: StiEmptyBrush;
        radialBarStartWidth: number;
        radialBarEndWidth: number;
        needleBrush: StiSolidBrush;
        needleBorderBrush: StiEmptyBrush;
        needleCapBrush: StiSolidBrush;
        needleCapBorderBrush: StiSolidBrush;
        needleBorderWidth: number;
        needleCapBorderWidth: number;
        needleStartWidth: number;
        needleEndWidth: number;
        needleRelativeHeight: number;
        needleRelativeWith: number;
    }
}
declare namespace Stimulsoft.Report.Gauge {
    import Color = Stimulsoft.System.Drawing.Color;
    import StiSolidBrush = Stimulsoft.Base.Drawing.StiSolidBrush;
    import StiEmptyBrush = Stimulsoft.Base.Drawing.StiEmptyBrush;
    import Font = Stimulsoft.System.Drawing.Font;
    class StiGaugeStyleCoreXF27 extends StiGaugeStyleCoreXF {
        get localizedName(): string;
        brush: StiSolidBrush;
        foreColor: Color;
        borderColor: Color;
        borderWidth: number;
        targetColor: Color;
        tickMarkMajorBrush: StiEmptyBrush;
        tickMarkMajorBorder: StiEmptyBrush;
        tickMarkMinorBrush: StiEmptyBrush;
        tickMarkMinorBorder: StiEmptyBrush;
        tickLabelMajorTextBrush: StiSolidBrush;
        tickLabelMajorFont: Font;
        tickLabelMinorTextBrush: StiSolidBrush;
        tickLabelMinorFont: Font;
        markerBrush: StiSolidBrush;
        linearScaleBrush: StiSolidBrush;
        linearBarBrush: StiSolidBrush;
        linearBarBorderBrush: StiEmptyBrush;
        linearBarEmptyBrush: StiEmptyBrush;
        linearBarEmptyBorderBrush: StiEmptyBrush;
        linearBarStartWidth: number;
        linearBarEndWidth: number;
        radialBarBrush: StiSolidBrush;
        radialBarBorderBrush: StiEmptyBrush;
        radialBarEmptyBrush: StiSolidBrush;
        radialBarEmptyBorderBrush: StiEmptyBrush;
        radialBarStartWidth: number;
        radialBarEndWidth: number;
        needleBrush: StiSolidBrush;
        needleBorderBrush: StiEmptyBrush;
        needleCapBrush: StiSolidBrush;
        needleCapBorderBrush: StiSolidBrush;
        needleBorderWidth: number;
        needleCapBorderWidth: number;
        needleStartWidth: number;
        needleEndWidth: number;
        needleRelativeHeight: number;
        needleRelativeWith: number;
    }
}
declare namespace Stimulsoft.Report.Gauge {
    import Color = Stimulsoft.System.Drawing.Color;
    import StiSolidBrush = Stimulsoft.Base.Drawing.StiSolidBrush;
    import StiEmptyBrush = Stimulsoft.Base.Drawing.StiEmptyBrush;
    import Font = Stimulsoft.System.Drawing.Font;
    class StiGaugeStyleCoreXF28 extends StiGaugeStyleCoreXF {
        get localizedName(): string;
        brush: StiSolidBrush;
        foreColor: Color;
        borderColor: Color;
        borderWidth: number;
        targetColor: Color;
        tickMarkMajorBrush: StiEmptyBrush;
        tickMarkMajorBorder: StiEmptyBrush;
        tickMarkMinorBrush: StiEmptyBrush;
        tickMarkMinorBorder: StiEmptyBrush;
        tickLabelMajorTextBrush: StiSolidBrush;
        tickLabelMajorFont: Font;
        tickLabelMinorTextBrush: StiSolidBrush;
        tickLabelMinorFont: Font;
        markerBrush: StiSolidBrush;
        linearScaleBrush: StiSolidBrush;
        linearBarBrush: StiSolidBrush;
        linearBarBorderBrush: StiEmptyBrush;
        linearBarEmptyBrush: StiEmptyBrush;
        linearBarEmptyBorderBrush: StiEmptyBrush;
        linearBarStartWidth: number;
        linearBarEndWidth: number;
        radialBarBrush: StiSolidBrush;
        radialBarBorderBrush: StiEmptyBrush;
        radialBarEmptyBrush: StiSolidBrush;
        radialBarEmptyBorderBrush: StiEmptyBrush;
        radialBarStartWidth: number;
        radialBarEndWidth: number;
        needleBrush: StiSolidBrush;
        needleBorderBrush: StiEmptyBrush;
        needleCapBrush: StiSolidBrush;
        needleCapBorderBrush: StiSolidBrush;
        needleBorderWidth: number;
        needleCapBorderWidth: number;
        needleStartWidth: number;
        needleEndWidth: number;
        needleRelativeHeight: number;
        needleRelativeWith: number;
    }
}
declare namespace Stimulsoft.Report.Gauge {
    import Font = Stimulsoft.System.Drawing.Font;
    import StiEmptyBrush = Stimulsoft.Base.Drawing.StiEmptyBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiSolidBrush = Stimulsoft.Base.Drawing.StiSolidBrush;
    class StiGaugeStyleCoreXF29 extends StiGaugeStyleCoreXF {
        get localizedName(): string;
        brush: StiSolidBrush;
        foreColor: Color;
        borderColor: Color;
        borderWidth: number;
        targetColor: Color;
        tickMarkMajorBrush: StiSolidBrush;
        tickMarkMajorBorder: StiEmptyBrush;
        tickMarkMinorBrush: StiSolidBrush;
        tickMarkMinorBorder: StiEmptyBrush;
        tickLabelMajorTextBrush: StiSolidBrush;
        tickLabelMajorFont: Font;
        tickLabelMinorTextBrush: StiSolidBrush;
        tickLabelMinorFont: Font;
        markerBrush: StiSolidBrush;
        linearMarkerBorder: StiSolidBrush;
        linearScaleBrush: StiSolidBrush;
        linearBarBrush: StiSolidBrush;
        linearBarBorderBrush: StiEmptyBrush;
        linearBarEmptyBrush: StiEmptyBrush;
        linearBarEmptyBorderBrush: StiEmptyBrush;
        linearBarStartWidth: number;
        linearBarEndWidth: number;
        radialBarBrush: StiSolidBrush;
        radialBarBorderBrush: StiEmptyBrush;
        radialBarEmptyBrush: StiSolidBrush;
        radialBarEmptyBorderBrush: StiEmptyBrush;
        radialBarStartWidth: number;
        radialBarEndWidth: number;
        needleBrush: StiSolidBrush;
        needleBorderBrush: StiEmptyBrush;
        needleCapBrush: StiSolidBrush;
        needleCapBorderBrush: StiSolidBrush;
        needleBorderWidth: number;
        needleCapBorderWidth: number;
        needleStartWidth: number;
        needleEndWidth: number;
        needleRelativeHeight: number;
        needleRelativeWith: number;
    }
}
declare namespace Stimulsoft.Report.Gauge {
    import Font = Stimulsoft.System.Drawing.Font;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiGaugeStyleCoreXF = Stimulsoft.Report.Gauge.StiGaugeStyleCoreXF;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    class StiGaugeStyleCoreXF30 extends StiGaugeStyleCoreXF {
        get localizedName(): string;
        brush: StiBrush;
        foreColor: Color;
        borderColor: Color;
        borderWidth: number;
        targetColor: Color;
        tickMarkMajorBrush: StiBrush;
        tickMarkMajorBorder: StiBrush;
        tickMarkMinorBrush: StiBrush;
        tickMarkMinorBorder: StiBrush;
        tickLabelMajorTextBrush: StiBrush;
        tickLabelMajorFont: Font;
        tickLabelMinorTextBrush: StiBrush;
        tickLabelMinorFont: Font;
        markerBrush: StiBrush;
        linearMarkerBorder: StiBrush;
        linearScaleBrush: StiBrush;
        linearBarBrush: StiBrush;
        linearBarBorderBrush: StiBrush;
        linearBarEmptyBrush: StiBrush;
        linearBarEmptyBorderBrush: StiBrush;
        linearBarStartWidth: number;
        linearBarEndWidth: number;
        radialBarBrush: StiBrush;
        radialBarBorderBrush: StiBrush;
        radialBarEmptyBrush: StiBrush;
        radialBarEmptyBorderBrush: StiBrush;
        radialBarStartWidth: number;
        radialBarEndWidth: number;
        needleBrush: StiBrush;
        needleBorderBrush: StiBrush;
        needleCapBrush: StiBrush;
        needleCapBorderBrush: StiBrush;
        needleBorderWidth: number;
        needleCapBorderWidth: number;
        needleStartWidth: number;
        needleEndWidth: number;
        needleRelativeHeight: number;
        needleRelativeWith: number;
    }
}
declare namespace Stimulsoft.Report.Gauge {
    import StiGaugeStyleCoreXF30 = Stimulsoft.Report.Gauge.StiGaugeStyleCoreXF30;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    class StiGaugeStyleCoreXF31 extends StiGaugeStyleCoreXF30 {
        get localizedName(): string;
        brush: StiBrush;
        targetColor: System.Drawing.Color;
        foreColor: System.Drawing.Color;
        markerBrush: StiBrush;
        linearMarkerBorder: StiBrush;
        linearScaleBrush: StiBrush;
        linearBarBrush: StiBrush;
        radialBarBrush: StiBrush;
        radialBarEmptyBrush: StiBrush;
    }
}
declare namespace Stimulsoft.Report.Gauge {
    import StiGaugeStyleCoreXF30 = Stimulsoft.Report.Gauge.StiGaugeStyleCoreXF30;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    class StiGaugeStyleCoreXF32 extends StiGaugeStyleCoreXF30 {
        get localizedName(): string;
        brush: StiBrush;
        targetColor: System.Drawing.Color;
        foreColor: System.Drawing.Color;
        markerBrush: StiBrush;
        linearMarkerBorder: StiBrush;
        needleBrush: StiBrush;
        needleBorderBrush: StiBrush;
        needleCapBrush: StiBrush;
        needleCapBorderBrush: StiBrush;
        needleCapBorderWidth: number;
        linearScaleBrush: StiBrush;
        linearBarBrush: StiBrush;
        radialBarBrush: StiBrush;
        radialBarEmptyBrush: StiBrush;
    }
}
declare namespace Stimulsoft.Report.Gauge {
    import StiGaugeStyleCoreXF30 = Stimulsoft.Report.Gauge.StiGaugeStyleCoreXF30;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Font = Stimulsoft.System.Drawing.Font;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiGaugeStyleCoreXF33 extends StiGaugeStyleCoreXF30 {
        get localizedName(): string;
        brush: StiBrush;
        targetColor: Color;
        foreColor: Color;
        tickLabelMajorTextBrush: StiBrush;
        tickLabelMajorFont: Font;
        tickLabelMinorTextBrush: StiBrush;
        tickLabelMinorFont: Font;
        markerBrush: StiBrush;
        linearMarkerBorder: StiBrush;
        needleBrush: StiBrush;
        needleBorderBrush: StiBrush;
        needleCapBrush: StiBrush;
        needleCapBorderBrush: StiBrush;
        needleCapBorderWidth: number;
        linearScaleBrush: StiBrush;
        linearBarBrush: StiBrush;
        radialBarBrush: StiBrush;
        radialBarEmptyBrush: StiBrush;
    }
}
declare namespace Stimulsoft.Report.Gauge {
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Font = Stimulsoft.System.Drawing.Font;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiGaugeStyleCoreXF34 extends StiGaugeStyleCoreXF {
        get localizedName(): string;
        brush: StiBrush;
        borderColor: Color;
        borderWidth: number;
        targetColor: Color;
        foreColor: Color;
        tickMarkMajorBrush: StiBrush;
        tickMarkMajorBorder: StiBrush;
        tickMarkMinorBrush: StiBrush;
        tickMarkMinorBorder: StiBrush;
        tickLabelMajorTextBrush: StiBrush;
        tickLabelMajorFont: Font;
        tickLabelMinorTextBrush: StiBrush;
        tickLabelMinorFont: Font;
        markerBrush: StiBrush;
        linearMarkerBorder: StiBrush;
        linearScaleBrush: StiBrush;
        linearBarBrush: StiBrush;
        linearBarBorderBrush: StiBrush;
        linearBarEmptyBrush: StiBrush;
        linearBarEmptyBorderBrush: StiBrush;
        linearBarStartWidth: number;
        linearBarEndWidth: number;
        radialBarBrush: StiBrush;
        radialBarBorderBrush: StiBrush;
        radialBarEmptyBrush: StiBrush;
        radialBarEmptyBorderBrush: StiBrush;
        radialBarStartWidth: number;
        radialBarEndWidth: number;
        needleBrush: StiBrush;
        needleBorderBrush: StiBrush;
        needleCapBrush: StiBrush;
        needleCapBorderBrush: StiBrush;
        needleBorderWidth: number;
        needleCapBorderWidth: number;
        needleStartWidth: number;
        needleEndWidth: number;
        needleRelativeHeight: number;
        needleRelativeWith: number;
    }
}
declare namespace Stimulsoft.Report.Gauge {
    import StiSolidBrush = Stimulsoft.Base.Drawing.StiSolidBrush;
    import StiEmptyBrush = Stimulsoft.Base.Drawing.StiEmptyBrush;
    import Font = Stimulsoft.System.Drawing.Font;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiGaugeStyleCoreXF35 extends StiGaugeStyleCoreXF {
        get localizedName(): string;
        brush: StiSolidBrush;
        borderColor: Color;
        foreColor: Color;
        borderWidth: number;
        targetColor: Color;
        tickMarkMajorBrush: StiSolidBrush;
        tickMarkMajorBorder: StiEmptyBrush;
        tickMarkMinorBrush: StiEmptyBrush;
        tickMarkMinorBorder: StiEmptyBrush;
        tickLabelMajorTextBrush: StiSolidBrush;
        tickLabelMajorFont: Font;
        tickLabelMinorTextBrush: StiSolidBrush;
        tickLabelMinorFont: Font;
        markerBrush: StiSolidBrush;
        linearMarkerBorder: StiSolidBrush;
        linearScaleBrush: StiSolidBrush;
        linearBarBrush: StiSolidBrush;
        linearBarBorderBrush: StiEmptyBrush;
        linearBarEmptyBrush: StiEmptyBrush;
        linearBarEmptyBorderBrush: StiEmptyBrush;
        linearBarStartWidth: number;
        linearBarEndWidth: number;
        radialBarBrush: StiSolidBrush;
        radialBarBorderBrush: StiEmptyBrush;
        radialBarEmptyBrush: StiSolidBrush;
        radialBarEmptyBorderBrush: StiEmptyBrush;
        radialBarStartWidth: number;
        radialBarEndWidth: number;
        needleBrush: StiSolidBrush;
        needleBorderBrush: StiEmptyBrush;
        needleCapBrush: StiSolidBrush;
        needleCapBorderBrush: StiSolidBrush;
        needleBorderWidth: number;
        needleCapBorderWidth: number;
        needleStartWidth: number;
        needleEndWidth: number;
        needleRelativeHeight: number;
        needleRelativeWith: number;
    }
}
declare namespace Stimulsoft.Report.Gauge {
    import StiElementStyleIdent = Stimulsoft.Report.Dashboard.StiElementStyleIdent;
    class StiGaugeStyleXF24 extends StiGaugeStyleXF {
        allowDashboard: boolean;
        get dashboardName(): string;
        styleIdent: StiElementStyleIdent;
        createNew(): StiGaugeStyleXF;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Gauge {
    import StiElementStyleIdent = Stimulsoft.Report.Dashboard.StiElementStyleIdent;
    class StiGaugeStyleXF25 extends StiGaugeStyleXF {
        allowDashboard: boolean;
        get dashboardName(): string;
        styleIdent: StiElementStyleIdent;
        createNew(): StiGaugeStyleXF;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Gauge {
    import StiElementStyleIdent = Stimulsoft.Report.Dashboard.StiElementStyleIdent;
    class StiGaugeStyleXF28 extends StiGaugeStyleXF {
        allowDashboard: boolean;
        get dashboardName(): string;
        styleIdent: StiElementStyleIdent;
        createNew(): StiGaugeStyleXF;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Gauge {
    import StiElementStyleIdent = Stimulsoft.Report.Dashboard.StiElementStyleIdent;
    class StiGaugeStyleXF29 extends StiGaugeStyleXF {
        allowDashboard: boolean;
        get dashboardName(): string;
        styleIdent: StiElementStyleIdent;
        createNew(): StiGaugeStyleXF;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Gauge {
    import StiElementStyleIdent = Stimulsoft.Report.Dashboard.StiElementStyleIdent;
    class StiGaugeStyleXF30 extends StiGaugeStyleXF {
        allowDashboard: boolean;
        get dashboardName(): string;
        styleIdent: StiElementStyleIdent;
        createNew(): StiGaugeStyleXF;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Gauge {
    import StiElementStyleIdent = Stimulsoft.Report.Dashboard.StiElementStyleIdent;
    class StiGaugeStyleXF31 extends StiGaugeStyleXF {
        allowDashboard: boolean;
        get dashboardName(): string;
        styleIdent: StiElementStyleIdent;
        createNew(): StiGaugeStyleXF;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Gauge {
    import StiElementStyleIdent = Stimulsoft.Report.Dashboard.StiElementStyleIdent;
    class StiGaugeStyleXF32 extends StiGaugeStyleXF {
        allowDashboard: boolean;
        get dashboardName(): string;
        styleIdent: StiElementStyleIdent;
        createNew(): StiGaugeStyleXF;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Gauge {
    import StiElementStyleIdent = Stimulsoft.Report.Dashboard.StiElementStyleIdent;
    class StiGaugeStyleXF33 extends StiGaugeStyleXF {
        allowDashboard: boolean;
        get dashboardName(): string;
        styleIdent: StiElementStyleIdent;
        createNew(): StiGaugeStyleXF;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Gauge {
    import StiElementStyleIdent = Stimulsoft.Report.Dashboard.StiElementStyleIdent;
    class StiGaugeStyleXF34 extends StiGaugeStyleXF {
        allowDashboard: boolean;
        get dashboardName(): string;
        styleIdent: StiElementStyleIdent;
        createNew(): StiGaugeStyleXF;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Gauge {
    import StiElementStyleIdent = Stimulsoft.Report.Dashboard.StiElementStyleIdent;
    class StiGaugeStyleXF35 extends StiGaugeStyleXF {
        allowDashboard: boolean;
        get dashboardName(): string;
        styleIdent: StiElementStyleIdent;
        createNew(): StiGaugeStyleXF;
        constructor();
    }
}
