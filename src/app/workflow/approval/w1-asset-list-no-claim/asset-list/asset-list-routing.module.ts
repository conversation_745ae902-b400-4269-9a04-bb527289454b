import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { AssetListPage } from './asset-list.page';

const routes: Routes = [
  {
    path: '',
    component: AssetListPage,
  },
  {
    path: 'scanned-pages-unfinished-assets',
    loadChildren: () =>
      import('./scanned-pages-unfinished-assets/scanned-pages-unfinished-assets.module').then(
        (m) => m.ScannedPagesUnfinishedAssetsPageModule,
      ),
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AssetListPageRoutingModule {}
