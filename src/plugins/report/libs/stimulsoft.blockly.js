var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Model;
        (function (Model) {
            let EscapeMode;
            (function (EscapeMode) {
                EscapeMode[EscapeMode["None"] = 0] = "None";
                EscapeMode[EscapeMode["Break"] = 1] = "Break";
                EscapeMode[EscapeMode["Continue"] = 2] = "Continue";
            })(EscapeMode = Model.EscapeMode || (Model.EscapeMode = {}));
        })(Model = Blockly.Model || (Blockly.Model = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Model;
        (function (Model) {
            var List = Stimulsoft.System.Collections.List;
            class ListExt extends List {
                static evaluate(values, name, context) {
                    let value = values.firstOrDefault(x => x.name == name);
                    if (value == null)
                        throw new Error("value " + name + " not found");
                    return value.evaluate(context);
                }
                static async evaluateAsync(values, name, context) {
                    let value = values.firstOrDefault(x => x.name == name);
                    if (value == null)
                        throw new Error("value " + name + " not found");
                    return await value.evaluateAsync(context);
                }
                static get(fields, name) {
                    let field = fields.firstOrDefault(x => x.name == name);
                    if (field == null)
                        throw new Error("field " + name + " not found");
                    return field.value;
                }
                static get2(statements, name) {
                    let statement = statements.firstOrDefault(x => x.name == name);
                    if (statement == null)
                        throw new Error("statement " + name + " not found");
                    return statement;
                }
                static getValue(mutations, name, domain = "mutation") {
                    let mut = mutations.firstOrDefault(x => x.domain == domain && x.name == name);
                    if (mut == null)
                        return null;
                    return mut.value;
                }
                static average(list) {
                    let valSum = list.sum();
                    return valSum / list.length;
                }
            }
            Model.ListExt = ListExt;
        })(Model = Blockly.Model || (Blockly.Model = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Model;
        (function (Model) {
            var IAsIs = Stimulsoft.System.IAsIs;
            var List = Stimulsoft.System.Collections.List;
            class IronBlock {
                implements() {
                    return [Model.IFragment, IAsIs];
                }
                is(type) {
                    if (type instanceof Stimulsoft.System.Interface)
                        return this.implements().indexOf(type) != -1;
                    if (typeof type == "function")
                        return this instanceof type;
                    if (type == undefined)
                        throw new Error("Type for comparison is 'undefined'");
                    return false;
                }
                is2(type) {
                    return this.is(type);
                }
                as(type) {
                    if (this.is(type))
                        return this;
                    return null;
                }
                evaluate(context) {
                    if (this.next != null && context.escapeMode == Model.EscapeMode.None) {
                        return this.next.evaluate(context);
                    }
                    return null;
                }
                async evaluateAsync(context) {
                    if (this.next != null && context.escapeMode == Model.EscapeMode.None) {
                        return await this.next.evaluateAsync(context);
                    }
                    return null;
                }
                constructor() {
                    this.fields = new List();
                    this.values = new List();
                    this.statements = new List();
                    this.mutations = new List();
                }
            }
            Model.IronBlock = IronBlock;
        })(Model = Blockly.Model || (Blockly.Model = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Maths;
            (function (Maths) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class MathRandomInt extends IronBlock {
                    evaluate(context) {
                        let from = Blockly.StiObjConverter.toDouble(ListExt.evaluate(this.values, "FROM", context));
                        let to = Blockly.StiObjConverter.toDouble(ListExt.evaluate(this.values, "TO", context));
                        let min = Math.floor(Math.min(from, to));
                        let max = Math.floor(Math.max(from, to));
                        return Math.floor(Math.random() * (max - min) + min);
                    }
                    async evaluateAsync(context) {
                        let from = Blockly.StiObjConverter.toDouble(await ListExt.evaluateAsync(this.values, "FROM", context));
                        let to = Blockly.StiObjConverter.toDouble(await ListExt.evaluateAsync(this.values, "TO", context));
                        let min = Math.floor(Math.min(from, to));
                        let max = Math.floor(Math.max(from, to));
                        return Math.floor(Math.random() * (max - min) + min);
                    }
                }
                Maths.MathRandomInt = MathRandomInt;
            })(Maths = Blocks.Maths || (Blocks.Maths = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Model;
        (function (Model) {
            class Field {
            }
            Model.Field = Field;
        })(Model = Blockly.Model || (Blockly.Model = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Model;
        (function (Model) {
            class Mutation {
                constructor(domain, name, value) {
                    this.domain = domain;
                    this.name = name;
                    this.value = value;
                }
            }
            Model.Mutation = Mutation;
        })(Model = Blockly.Model || (Blockly.Model = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Model;
        (function (Model) {
            class Value {
                implements() {
                    return [Model.IFragment];
                }
                evaluate(context) {
                    if (this.block == null)
                        return null;
                    return this.block.evaluate(context);
                }
                async evaluateAsync(context) {
                    if (this.block == null)
                        return null;
                    return await this.block.evaluateAsync(context);
                }
            }
            Model.Value = Value;
        })(Model = Blockly.Model || (Blockly.Model = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Text;
            (function (Text) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class TextPrint extends IronBlock {
                    evaluate(context) {
                        let text = ListExt.evaluate(this.values, "TEXT", context).stimulsoft().toString();
                        alert(text);
                        return super.evaluate(context);
                    }
                    async evaluateAsync(context) {
                        let text = (await ListExt.evaluateAsync(this.values, "TEXT", context)).stimulsoft().toString();
                        alert(text);
                        return await super.evaluateAsync(context);
                    }
                }
                Text.TextPrint = TextPrint;
            })(Text = Blocks.Text || (Blocks.Text = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Text;
            (function (Text) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class TextBlock extends IronBlock {
                    evaluate(context) {
                        return ListExt.get(this.fields, "TEXT");
                    }
                    async evaluateAsync(context) {
                        return ListExt.get(this.fields, "TEXT");
                    }
                }
                Text.TextBlock = TextBlock;
            })(Text = Blocks.Text || (Blocks.Text = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Text;
            (function (Text) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class TextAppend extends IronBlock {
                    evaluate(context) {
                        var _a;
                        let variables = context.variables;
                        let variableName = ListExt.get(this.fields, "VAR");
                        let textToAppend = (_a = ListExt.evaluate(this.values, "TEXT", context).stimulsoft().toString()) !== null && _a !== void 0 ? _a : "";
                        if (!variables.containsKey(variableName)) {
                            variables.add(variableName, "");
                        }
                        let value = variables.get(variableName).toString();
                        variables.set(variableName, value + textToAppend);
                        return super.evaluate(context);
                    }
                    async evaluateAsync(context) {
                        var _a;
                        let variables = context.variables;
                        let variableName = ListExt.get(this.fields, "VAR");
                        let textToAppend = (_a = (await ListExt.evaluateAsync(this.values, "TEXT", context)).stimulsoft().toString()) !== null && _a !== void 0 ? _a : "";
                        if (!variables.containsKey(variableName)) {
                            variables.add(variableName, "");
                        }
                        let value = variables.get(variableName).toString();
                        variables.set(variableName, value + textToAppend);
                        return await super.evaluateAsync(context);
                    }
                }
                Text.TextAppend = TextAppend;
            })(Text = Blocks.Text || (Blocks.Text = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Text;
            (function (Text) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class TextIndexOf extends IronBlock {
                    evaluate(context) {
                        var _a, _b;
                        let mode = ListExt.get(this.fields, "END");
                        let text = (_a = ListExt.evaluate(this.values, "VALUE", context).stimulsoft().toString()) !== null && _a !== void 0 ? _a : "";
                        let term = (_b = ListExt.evaluate(this.values, "FIND", context).stimulsoft().toString()) !== null && _b !== void 0 ? _b : "";
                        switch (mode) {
                            case "FIRST": return Blockly.StiObjConverter.toDouble(text.indexOf(term)) + 1;
                            case "LAST": return Blockly.StiObjConverter.toDouble(text.lastIndexOf(term)) + 1;
                            default: throw new Error("unknown mode");
                        }
                    }
                    async evaluateAsync(context) {
                        var _a, _b;
                        let mode = ListExt.get(this.fields, "END");
                        let text = (_a = (await ListExt.evaluateAsync(this.values, "VALUE", context)).stimulsoft().toString()) !== null && _a !== void 0 ? _a : "";
                        let term = (_b = (await ListExt.evaluateAsync(this.values, "FIND", context)).stimulsoft().toString()) !== null && _b !== void 0 ? _b : "";
                        switch (mode) {
                            case "FIRST": return Blockly.StiObjConverter.toDouble(text.indexOf(term)) + 1;
                            case "LAST": return Blockly.StiObjConverter.toDouble(text.lastIndexOf(term)) + 1;
                            default: throw new Error("unknown mode");
                        }
                    }
                }
                Text.TextIndexOf = TextIndexOf;
            })(Text = Blocks.Text || (Blocks.Text = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Text;
            (function (Text) {
                var CultureInfo = Stimulsoft.System.Globalization.CultureInfo;
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class TextCaseChange extends IronBlock {
                    evaluate(context) {
                        var _a;
                        let toCase = ListExt.get(this.fields, "CASE");
                        let text = (_a = ListExt.evaluate(this.values, "TEXT", context).stimulsoft().toString()) !== null && _a !== void 0 ? _a : "";
                        switch (toCase) {
                            case "UPPERCASE":
                                return text.toUpperCase();
                            case "LOWERCASE":
                                return text.toLowerCase();
                            case "TITLECASE":
                                return CultureInfo.InvariantCulture.textInfo.toTitleCase(text.toLowerCase());
                            default:
                                throw new Error("unknown case");
                        }
                    }
                    async evaluateAsync(context) {
                        var _a;
                        let toCase = ListExt.get(this.fields, "CASE");
                        let text = (_a = (await ListExt.evaluateAsync(this.values, "TEXT", context)).stimulsoft().toString()) !== null && _a !== void 0 ? _a : "";
                        switch (toCase) {
                            case "UPPERCASE":
                                return text.toUpperCase();
                            case "LOWERCASE":
                                return text.toLowerCase();
                            case "TITLECASE":
                                return CultureInfo.InvariantCulture.textInfo.toTitleCase(text.toLowerCase());
                            default:
                                throw new Error("unknown case");
                        }
                    }
                }
                Text.TextCaseChange = TextCaseChange;
            })(Text = Blocks.Text || (Blocks.Text = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Text;
            (function (Text) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class TextTrim extends IronBlock {
                    evaluate(context) {
                        var _a;
                        let mode = ListExt.get(this.fields, "MODE");
                        let text = (_a = ListExt.evaluate(this.values, "TEXT", context).stimulsoft().toString()) !== null && _a !== void 0 ? _a : "";
                        switch (mode) {
                            case "BOTH": return text.trim();
                            case "LEFT": return text.stimulsoft().trimStart();
                            case "RIGHT": return text.stimulsoft().trimEnd();
                            default: throw new Error("unknown mode");
                        }
                    }
                    async evaluateAsync(context) {
                        var _a;
                        let mode = ListExt.get(this.fields, "MODE");
                        let text = (_a = (await ListExt.evaluateAsync(this.values, "TEXT", context)).stimulsoft().toString()) !== null && _a !== void 0 ? _a : "";
                        switch (mode) {
                            case "BOTH": return text.trim();
                            case "LEFT": return text.stimulsoft().trimStart();
                            case "RIGHT": return text.stimulsoft().trimEnd();
                            default: throw new Error("unknown mode");
                        }
                    }
                }
                Text.TextTrim = TextTrim;
            })(Text = Blocks.Text || (Blocks.Text = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Text;
            (function (Text) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class TextLength extends IronBlock {
                    evaluate(context) {
                        var _a;
                        let text = (_a = ListExt.evaluate(this.values, "VALUE", context).stimulsoft().toString()) !== null && _a !== void 0 ? _a : "";
                        return text.length;
                    }
                    async evaluateAsync(context) {
                        var _a;
                        let text = (_a = (await ListExt.evaluateAsync(this.values, "VALUE", context)).stimulsoft().toString()) !== null && _a !== void 0 ? _a : "";
                        return text.length;
                    }
                }
                Text.TextLength = TextLength;
            })(Text = Blocks.Text || (Blocks.Text = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Text;
            (function (Text) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class TextJoin extends IronBlock {
                    evaluate(context) {
                        let items = Number.parseInt(ListExt.getValue(this.mutations, "items"));
                        let sb = "";
                        for (let i = 0; i < items; i++) {
                            if (!this.values.any(x => x.name == "ADD" + i))
                                continue;
                            sb += ListExt.evaluate(this.values, "ADD" + i, context).stimulsoft().toString();
                        }
                        return sb;
                    }
                    async evaluateAsync(context) {
                        let items = Number.parseInt(ListExt.getValue(this.mutations, "items"));
                        let sb = "";
                        for (let i = 0; i < items; i++) {
                            if (!this.values.any(x => x.name == "ADD" + i))
                                continue;
                            sb += (await ListExt.evaluateAsync(this.values, "ADD" + i, context)).stimulsoft().toString();
                        }
                        return sb;
                    }
                }
                Text.TextJoin = TextJoin;
            })(Text = Blocks.Text || (Blocks.Text = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Text;
            (function (Text) {
                var StiString = Stimulsoft.System.StiString;
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class TextIsEmpty extends IronBlock {
                    evaluate(context) {
                        var _a;
                        let text = (_a = ListExt.evaluate(this.values, "VALUE", context).stimulsoft().toString()) !== null && _a !== void 0 ? _a : "";
                        return StiString.isNullOrEmpty(text);
                    }
                    async evaluateAsync(context) {
                        var _a;
                        let text = (_a = (await ListExt.evaluateAsync(this.values, "VALUE", context)).stimulsoft().toString()) !== null && _a !== void 0 ? _a : "";
                        return StiString.isNullOrEmpty(text);
                    }
                }
                Text.TextIsEmpty = TextIsEmpty;
            })(Text = Blocks.Text || (Blocks.Text = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Text;
            (function (Text) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class ColourRgb extends IronBlock {
                    evaluate(context) {
                        let red = ListExt.evaluate(this.values, "RED", context);
                        let green = ListExt.evaluate(this.values, "GREEN", context);
                        let blue = ListExt.evaluate(this.values, "BLUE", context);
                        return this.evaluateInner(red, green, blue);
                    }
                    async evaluateAsync(context) {
                        let red = await ListExt.evaluateAsync(this.values, "RED", context);
                        let green = await ListExt.evaluateAsync(this.values, "GREEN", context);
                        let blue = await ListExt.evaluateAsync(this.values, "BLUE", context);
                        return this.evaluateInner(red, green, blue);
                    }
                    evaluateInner(red, green, blue) {
                        let redString = red.toString(16);
                        let greenString = green.toString(16);
                        let blueString = blue.toString(16);
                        if (redString.length == 1)
                            redString = "0" + redString;
                        if (greenString.length == 1)
                            greenString = "0" + greenString;
                        if (blueString.length == 1)
                            blueString = "0" + blueString;
                        return ("#" + redString + greenString + blueString).toUpperCase();
                    }
                }
                Text.ColourRgb = ColourRgb;
            })(Text = Blocks.Text || (Blocks.Text = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Text;
            (function (Text) {
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class ColourRandom extends IronBlock {
                    evaluate(context) {
                        let letters = '0123456789ABCDEF';
                        let color = '#';
                        for (let i = 0; i < 6; i++) {
                            color += letters[Math.floor(Math.random() * 16)];
                        }
                        return color;
                    }
                    async evaluateAsync(context) {
                        return this.evaluate(context);
                    }
                }
                Text.ColourRandom = ColourRandom;
            })(Text = Blocks.Text || (Blocks.Text = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Text;
            (function (Text) {
                var StiString = Stimulsoft.System.StiString;
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class ColourBlend extends IronBlock {
                    evaluate(context) {
                        var _a, _b;
                        let colour1 = (_a = ListExt.evaluate(this.values, "COLOUR1", context).stimulsoft().toString()) !== null && _a !== void 0 ? _a : "";
                        let colour2 = (_b = ListExt.evaluate(this.values, "COLOUR2", context).stimulsoft().toString()) !== null && _b !== void 0 ? _b : "";
                        let ratio = Math.min(Math.max(ListExt.evaluate(this.values, "RATIO", context), 0), 1);
                        return this.evaluateInner(colour1, colour2, ratio);
                    }
                    async evaluateAsync(context) {
                        var _a, _b;
                        let colour1 = (_a = (await ListExt.evaluateAsync(this.values, "COLOUR1", context)).stimulsoft().toString()) !== null && _a !== void 0 ? _a : "";
                        let colour2 = (_b = (await ListExt.evaluateAsync(this.values, "COLOUR2", context)).stimulsoft().toString()) !== null && _b !== void 0 ? _b : "";
                        let ratio = Math.min(Math.max(await ListExt.evaluateAsync(this.values, "RATIO", context), 0), 1);
                        return this.evaluateInner(colour1, colour2, ratio);
                    }
                    evaluateInner(colour1, colour2, ratio) {
                        if (StiString.isNullOrWhiteSpace(colour1) || colour1.length != 7)
                            return null;
                        if (StiString.isNullOrWhiteSpace(colour2) || colour2.length != 7)
                            return null;
                        let result1 = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(colour1);
                        let result2 = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(colour2);
                        let red1 = parseInt(result1[1], 16);
                        let green1 = parseInt(result1[2], 16);
                        let blue1 = parseInt(result1[3], 16);
                        let red2 = parseInt(result2[1], 16);
                        let green2 = parseInt(result2[2], 16);
                        let blue2 = parseInt(result2[3], 16);
                        let red = red1 * (1 - ratio) + red2 * ratio;
                        let green = green1 * (1 - ratio) + green2 * ratio;
                        let blue = blue1 * (1 - ratio) + blue2 * ratio;
                        return this.rgbToHex(red, green, blue);
                    }
                    rgbToHex(r, g, b) {
                        return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
                    }
                }
                Text.ColourBlend = ColourBlend;
            })(Text = Blocks.Text || (Blocks.Text = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Text;
            (function (Text) {
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                class ColourPicker extends IronBlock {
                    evaluate(context) {
                        var _a;
                        return (_a = ListExt.get(this.fields, "COLOUR")) !== null && _a !== void 0 ? _a : "#000000";
                    }
                    async evaluateAsync(context) {
                        return this.evaluate(context);
                    }
                }
                Text.ColourPicker = ColourPicker;
            })(Text = Blocks.Text || (Blocks.Text = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Controls;
            (function (Controls) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class ControlsFor extends IronBlock {
                    evaluate(context) {
                        let variableName = ListExt.get(this.fields, "VAR");
                        let fromValue = Blockly.StiObjConverter.toDouble(ListExt.evaluate(this.values, "FROM", context));
                        let toValue = Blockly.StiObjConverter.toDouble(ListExt.evaluate(this.values, "TO", context));
                        let byValue = Blockly.StiObjConverter.toDouble(ListExt.evaluate(this.values, "BY", context));
                        let statement = this.statements.firstOrDefault();
                        if (context.variables.containsKey(variableName)) {
                            context.variables.set(variableName, fromValue);
                        }
                        else {
                            context.variables.add(variableName, fromValue);
                        }
                        while (context.variables.get(variableName) <= toValue) {
                            statement.evaluate(context);
                            context.variables.set(variableName, context.variables.get(variableName) + byValue);
                        }
                        return super.evaluate(context);
                    }
                    async evaluateAsync(context) {
                        let variableName = ListExt.get(this.fields, "VAR");
                        let fromValue = Blockly.StiObjConverter.toDouble(await ListExt.evaluateAsync(this.values, "FROM", context));
                        let toValue = Blockly.StiObjConverter.toDouble(await ListExt.evaluateAsync(this.values, "TO", context));
                        let byValue = Blockly.StiObjConverter.toDouble(await ListExt.evaluateAsync(this.values, "BY", context));
                        let statement = this.statements.firstOrDefault();
                        if (context.variables.containsKey(variableName)) {
                            context.variables.set(variableName, fromValue);
                        }
                        else {
                            context.variables.add(variableName, fromValue);
                        }
                        while (context.variables.get(variableName) <= toValue) {
                            await statement.evaluateAsync(context);
                            context.variables.set(variableName, context.variables.get(variableName) + byValue);
                        }
                        return await super.evaluateAsync(context);
                    }
                }
                Controls.ControlsFor = ControlsFor;
            })(Controls = Blocks.Controls || (Blocks.Controls = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Controls;
            (function (Controls) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class ControlsIf extends IronBlock {
                    evaluate(context) {
                        let ifCount = 1;
                        if (ListExt.getValue(this.mutations, "elseif") != null) {
                            let elseIf = ListExt.getValue(this.mutations, "elseif");
                            ifCount = Number.parseInt(elseIf) + 1;
                        }
                        let done = false;
                        for (let i = 0; i < ifCount; i++) {
                            if (ListExt.evaluate(this.values, "IF" + i, context)) {
                                let statement = ListExt.get2(this.statements, "DO" + i);
                                statement.evaluate(context);
                                done = true;
                                break;
                            }
                        }
                        if (!done) {
                            if (ListExt.getValue(this.mutations, "else") != null) {
                                let elseExists = ListExt.getValue(this.mutations, "else");
                                if (elseExists == "1") {
                                    let statement = ListExt.get2(this.statements, "ELSE");
                                    statement.evaluate(context);
                                }
                            }
                        }
                        return super.evaluate(context);
                    }
                    async evaluateAsync(context) {
                        let ifCount = 1;
                        if (ListExt.getValue(this.mutations, "elseif") != null) {
                            let elseIf = ListExt.getValue(this.mutations, "elseif");
                            ifCount = Number.parseInt(elseIf) + 1;
                        }
                        let done = false;
                        for (let i = 0; i < ifCount; i++) {
                            if (await ListExt.evaluateAsync(this.values, "IF" + i, context)) {
                                let statement = ListExt.get2(this.statements, "DO" + i);
                                await statement.evaluateAsync(context);
                                done = true;
                                break;
                            }
                        }
                        if (!done) {
                            if (ListExt.getValue(this.mutations, "else") != null) {
                                let elseExists = ListExt.getValue(this.mutations, "else");
                                if (elseExists == "1") {
                                    let statement = ListExt.get2(this.statements, "ELSE");
                                    await statement.evaluateAsync(context);
                                }
                            }
                        }
                        return await super.evaluateAsync(context);
                    }
                }
                Controls.ControlsIf = ControlsIf;
            })(Controls = Blocks.Controls || (Blocks.Controls = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Controls;
            (function (Controls) {
                var CollectionBase = Stimulsoft.System.Collections.CollectionBase;
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class ControlsForEach extends IronBlock {
                    evaluate(context) {
                        let variableName = ListExt.get(this.fields, "VAR");
                        let list = ListExt.evaluate(this.values, "LIST", context);
                        let statement = this.statements.where(x => x.name == "DO").firstOrDefault();
                        if (statement == null)
                            return super.evaluate(context);
                        if (list.stimulsoft().is(CollectionBase))
                            list = list.toList();
                        for (let item of list) {
                            if (context.variables.containsKey(variableName)) {
                                context.variables.set(variableName, item);
                            }
                            else {
                                context.variables.add(variableName, item);
                            }
                            statement.evaluate(context);
                        }
                        return super.evaluate(context);
                    }
                    async evaluateAsync(context) {
                        let variableName = ListExt.get(this.fields, "VAR");
                        let list = await ListExt.evaluateAsync(this.values, "LIST", context);
                        let statement = this.statements.where(x => x.name == "DO").firstOrDefault();
                        if (statement == null)
                            return await super.evaluateAsync(context);
                        if (list.stimulsoft().is(CollectionBase))
                            list = list.toList();
                        for (let item of list) {
                            if (context.variables.containsKey(variableName)) {
                                context.variables.set(variableName, item);
                            }
                            else {
                                context.variables.add(variableName, item);
                            }
                            await statement.evaluateAsync(context);
                        }
                        return await super.evaluateAsync(context);
                    }
                }
                Controls.ControlsForEach = ControlsForEach;
            })(Controls = Blocks.Controls || (Blocks.Controls = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Controls;
            (function (Controls) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class ControlsWhileUntil extends IronBlock {
                    evaluate(context) {
                        let mode = ListExt.get(this.fields, "MODE");
                        let value = this.values.firstOrDefault(x => x.name == "BOOL");
                        if (!this.statements.any(x => x.name == "DO") || value == null)
                            return super.evaluate(context);
                        let statement = ListExt.get2(this.statements, "DO");
                        if (mode == "WHILE") {
                            while (value.evaluate(context)) {
                                statement.evaluate(context);
                            }
                        }
                        else {
                            while (!value.evaluate(context)) {
                                statement.evaluate(context);
                            }
                        }
                        return super.evaluate(context);
                    }
                    async evaluateAsync(context) {
                        let mode = ListExt.get(this.fields, "MODE");
                        let value = this.values.firstOrDefault(x => x.name == "BOOL");
                        if (!this.statements.any(x => x.name == "DO") || value == null)
                            return await super.evaluateAsync(context);
                        let statement = ListExt.get2(this.statements, "DO");
                        if (mode == "WHILE") {
                            while (await value.evaluateAsync(context)) {
                                await statement.evaluateAsync(context);
                            }
                        }
                        else {
                            while (!(await value.evaluateAsync(context))) {
                                await statement.evaluateAsync(context);
                            }
                        }
                        return await super.evaluateAsync(context);
                    }
                }
                Controls.ControlsWhileUntil = ControlsWhileUntil;
            })(Controls = Blocks.Controls || (Blocks.Controls = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Controls;
            (function (Controls) {
                var EscapeMode = Stimulsoft.Blockly.Model.EscapeMode;
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class ControlsFlowStatement extends IronBlock {
                    evaluate(context) {
                        let flow = ListExt.get(this.fields, "FLOW");
                        if (flow == "CONTINUE") {
                            context.escapeMode = EscapeMode.Continue;
                            return null;
                        }
                        if (flow == "BREAK") {
                            context.escapeMode = EscapeMode.Break;
                            return null;
                        }
                        throw new Error(flow + "flow is not supported");
                    }
                    async evaluateAsync(context) {
                        return this.evaluate(context);
                    }
                }
                Controls.ControlsFlowStatement = ControlsFlowStatement;
            })(Controls = Blocks.Controls || (Blocks.Controls = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Controls;
            (function (Controls) {
                var EscapeMode = Stimulsoft.Blockly.Model.EscapeMode;
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class ControlsRepeatExt extends IronBlock {
                    evaluate(context) {
                        let timesValue = Blockly.StiObjConverter.toDouble(ListExt.evaluate(this.values, "TIMES", context));
                        if (!this.statements.any(x => x.name == "DO"))
                            return super.evaluate(context);
                        let statement = ListExt.get2(this.statements, "DO");
                        for (let i = 0; i < timesValue; i++) {
                            statement.evaluate(context);
                            if (context.escapeMode == EscapeMode.Break) {
                                context.escapeMode = EscapeMode.None;
                                break;
                            }
                            context.escapeMode = EscapeMode.None;
                        }
                        context.escapeMode = EscapeMode.None;
                        return super.evaluate(context);
                    }
                    async evaluateAsync(context) {
                        let timesValue = Blockly.StiObjConverter.toDouble(await ListExt.evaluateAsync(this.values, "TIMES", context));
                        if (!this.statements.any(x => x.name == "DO"))
                            return await super.evaluateAsync(context);
                        let statement = ListExt.get2(this.statements, "DO");
                        for (let i = 0; i < timesValue; i++) {
                            await statement.evaluateAsync(context);
                            if (context.escapeMode == EscapeMode.Break) {
                                context.escapeMode = EscapeMode.None;
                                break;
                            }
                            context.escapeMode = EscapeMode.None;
                        }
                        context.escapeMode = EscapeMode.None;
                        return await super.evaluateAsync(context);
                    }
                }
                Controls.ControlsRepeatExt = ControlsRepeatExt;
            })(Controls = Blocks.Controls || (Blocks.Controls = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Model;
        (function (Model) {
            class Statement {
                implements() {
                    return [Model.IFragment];
                }
                evaluate(context) {
                    if (this.block == null)
                        return null;
                    return this.block.evaluate(context);
                }
                async evaluateAsync(context) {
                    if (this.block == null)
                        return null;
                    return await this.block.evaluateAsync(context);
                }
            }
            Model.Statement = Statement;
        })(Model = Blockly.Model || (Blockly.Model = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Procedures;
            (function (Procedures) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var Statement = Stimulsoft.Blockly.Model.Statement;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                var StiString = Stimulsoft.System.StiString;
                class ProceduresDef extends IronBlock {
                    evaluate(context) {
                        let name = ListExt.get(this.fields, "NAME");
                        let statement = this.statements.firstOrDefault(x => x.name == "STACK");
                        if (StiString.isNullOrWhiteSpace(name))
                            return null;
                        if (statement == null) {
                            statement = new Statement();
                            statement.block = null;
                            statement.name = "STACK";
                        }
                        if (this.values.any(x => x.name == "RETURN")) {
                            let valueBlock = new Procedures.ValueBlock(this.values.first(x => x.name == "RETURN"));
                            if (statement.block == null) {
                                statement.block = valueBlock;
                            }
                            else {
                                this.findEndOfChain(statement.block).next = valueBlock;
                            }
                        }
                        if (context.functions.containsKey(name)) {
                            context.functions.set(name, statement);
                        }
                        else {
                            context.functions.add(name, statement);
                        }
                        return null;
                    }
                    async evaluateAsync(context) {
                        return this.evaluate(context);
                    }
                    findEndOfChain(block) {
                        if (block.next == null)
                            return block;
                        return this.findEndOfChain(block.next);
                    }
                }
                Procedures.ProceduresDef = ProceduresDef;
            })(Procedures = Blocks.Procedures || (Blocks.Procedures = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Model;
        (function (Model) {
            var Dictionary = Stimulsoft.System.Collections.Dictionary;
            class Context {
                constructor() {
                    this.variables = new Dictionary();
                    this.functions = new Dictionary();
                    this.escapeMode = Model.EscapeMode.None;
                }
            }
            Model.Context = Context;
        })(Model = Blockly.Model || (Blockly.Model = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Procedures;
            (function (Procedures) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                var Context = Stimulsoft.Blockly.Model.Context;
                class ProceduresCallNoReturn extends IronBlock {
                    evaluate(context) {
                        let name = ListExt.getValue(this.mutations, "name");
                        if (!context.functions.containsKey(name))
                            throw new Error("Method $" + name + " not defined");
                        let statement = context.functions.get(name);
                        let funcContext = new Context();
                        funcContext.parent = context;
                        funcContext.functions = context.functions;
                        let counter = 0;
                        for (let mutation of this.mutations.where(x => x.domain == "arg" && x.name == "name")) {
                            let value = ListExt.evaluate(this.values, "ARG" + counter, context);
                            funcContext.variables.add(mutation.value, value);
                            counter++;
                        }
                        statement.evaluate(funcContext);
                        return super.evaluate(context);
                    }
                    async evaluateAsync(context) {
                        let name = ListExt.getValue(this.mutations, "name");
                        if (!context.functions.containsKey(name))
                            throw new Error("Method $" + name + " not defined");
                        let statement = context.functions.get(name);
                        let funcContext = new Context();
                        funcContext.parent = context;
                        funcContext.functions = context.functions;
                        let counter = 0;
                        for (let mutation of this.mutations.where(x => x.domain == "arg" && x.name == "name")) {
                            let value = await ListExt.evaluateAsync(this.values, "ARG" + counter, context);
                            funcContext.variables.add(mutation.value, value);
                            counter++;
                        }
                        await statement.evaluateAsync(funcContext);
                        return await super.evaluateAsync(context);
                    }
                }
                Procedures.ProceduresCallNoReturn = ProceduresCallNoReturn;
            })(Procedures = Blocks.Procedures || (Blocks.Procedures = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Procedures;
            (function (Procedures) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var ProceduresCallNoReturn = Stimulsoft.Blockly.Blocks.Procedures.ProceduresCallNoReturn;
                var Context = Stimulsoft.Blockly.Model.Context;
                class ProceduresCallReturn extends ProceduresCallNoReturn {
                    evaluate(context) {
                        let name = ListExt.getValue(this.mutations, "name");
                        if (!context.functions.containsKey(name))
                            throw new Error("Method '" + name + "' not defined");
                        let statement = context.functions.get(name);
                        let funcContext = new Context();
                        funcContext.parent = context;
                        funcContext.functions = context.functions;
                        let counter = 0;
                        for (let mutation of this.mutations.where(x => x.domain == "arg" && x.name == "name")) {
                            let value = ListExt.evaluate(this.values, "ARG" + counter, context);
                            funcContext.variables.add(mutation.value, value);
                            counter++;
                        }
                        return statement.evaluate(funcContext);
                    }
                    async evaluateAsync(context) {
                        let name = ListExt.getValue(this.mutations, "name");
                        if (!context.functions.containsKey(name))
                            throw new Error("Method '" + name + "' not defined");
                        let statement = context.functions.get(name);
                        let funcContext = new Context();
                        funcContext.parent = context;
                        funcContext.functions = context.functions;
                        let counter = 0;
                        for (let mutation of this.mutations.where(x => x.domain == "arg" && x.name == "name")) {
                            let value = await ListExt.evaluateAsync(this.values, "ARG" + counter, context);
                            funcContext.variables.add(mutation.value, value);
                            counter++;
                        }
                        return await statement.evaluateAsync(funcContext);
                    }
                }
                Procedures.ProceduresCallReturn = ProceduresCallReturn;
            })(Procedures = Blocks.Procedures || (Blocks.Procedures = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Procedures;
            (function (Procedures) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class ProceduresIfReturn extends IronBlock {
                    evaluate(context) {
                        let condition = ListExt.evaluate(this.values, "CONDITION", context);
                        if (condition) {
                            return ListExt.evaluate(this.values, "VALUE", context);
                        }
                        return super.evaluate(context);
                    }
                    async evaluateAsync(context) {
                        let condition = await ListExt.evaluateAsync(this.values, "CONDITION", context);
                        if (condition) {
                            return await ListExt.evaluateAsync(this.values, "VALUE", context);
                        }
                        return await super.evaluateAsync(context);
                    }
                }
                Procedures.ProceduresIfReturn = ProceduresIfReturn;
            })(Procedures = Blocks.Procedures || (Blocks.Procedures = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Logic;
            (function (Logic) {
                var StiString = Stimulsoft.System.StiString;
                var StiConvert = Stimulsoft.Base.StiConvert;
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class LogicCompare extends IronBlock {
                    evaluate(context) {
                        let a = Blockly.StiObjConverter.getValue(ListExt.evaluate(this.values, "A", context));
                        let b = Blockly.StiObjConverter.getValue(ListExt.evaluate(this.values, "B", context));
                        return this.compare(a, b);
                    }
                    async evaluateAsync(context) {
                        let a = Blockly.StiObjConverter.getValue(await ListExt.evaluateAsync(this.values, "A", context));
                        let b = Blockly.StiObjConverter.getValue(await ListExt.evaluateAsync(this.values, "B", context));
                        return this.compare(a, b);
                    }
                    compare(a, b) {
                        let opValue = ListExt.get(this.fields, "OP");
                        if (typeof a == "string")
                            return this.compareString(opValue, a, StiConvert.changeType(b, String));
                        if (typeof a == "number")
                            return this.compareNumber(opValue, StiConvert.changeType(a, Number), StiConvert.changeType(b, Number));
                        if (typeof a == "boolean")
                            return this.compareBool(opValue, StiConvert.changeType(a, Boolean), StiConvert.changeType(b, Boolean));
                        if (a == null && b == null) {
                            switch (opValue) {
                                case "EQ": return true;
                                case "NEQ": return false;
                            }
                        }
                        else if (a == null && b != null) {
                            switch (opValue) {
                                case "EQ": return false;
                                case "NEQ": return true;
                            }
                        }
                        else if (a != null && b == null) {
                            switch (opValue) {
                                case "EQ": return false;
                                case "NEQ": return true;
                            }
                        }
                        throw new Error("unexpected value type");
                    }
                    compareString(op, a, b) {
                        switch (op) {
                            case "EQ": return a == b;
                            case "NEQ": return a != b;
                            case "LT": return StiString.compareTo(a, b) < 0;
                            case "LTE": return StiString.compareTo(a, b) <= 0;
                            case "GT": return StiString.compareTo(a, b) > 0;
                            case "GTE": return StiString.compareTo(a, b) >= 0;
                            default: throw new Error("Unknown OP " + op);
                        }
                        return false;
                    }
                    compareNumber(op, a, b) {
                        switch (op) {
                            case "EQ": return a == b;
                            case "NEQ": return a != b;
                            case "LT": return a < b;
                            case "LTE": return a <= b;
                            case "GT": return a > b;
                            case "GTE": return a >= b;
                            default: throw new Error("Unknown OP " + op);
                        }
                    }
                    compareBool(op, a, b) {
                        switch (op) {
                            case "EQ": return a == b;
                            case "NEQ": return a != b;
                            default: throw new Error("Unknown OP " + op);
                        }
                    }
                }
                Logic.LogicCompare = LogicCompare;
            })(Logic = Blocks.Logic || (Blocks.Logic = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Logic;
            (function (Logic) {
                var StiValueHelper = Stimulsoft.Base.Helpers.StiValueHelper;
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class LogicBoolean extends IronBlock {
                    evaluate(context) {
                        return StiValueHelper.tryToBool(ListExt.get(this.fields, "BOOL"));
                    }
                    async evaluateAsync(context) {
                        return StiValueHelper.tryToBool(ListExt.get(this.fields, "BOOL"));
                    }
                }
                Logic.LogicBoolean = LogicBoolean;
            })(Logic = Blocks.Logic || (Blocks.Logic = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Logic;
            (function (Logic) {
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class LogicNull extends IronBlock {
                    evaluate(context) {
                        return null;
                    }
                    async evaluateAsync(context) {
                        return null;
                    }
                }
                Logic.LogicNull = LogicNull;
            })(Logic = Blocks.Logic || (Blocks.Logic = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Logic;
            (function (Logic) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class LogicTernary extends IronBlock {
                    evaluate(context) {
                        let ifValue = ListExt.evaluate(this.values, "IF", context);
                        if (ifValue) {
                            if (this.values.any(x => x.name == "THEN")) {
                                return ListExt.evaluate(this.values, "THEN", context);
                            }
                        }
                        else {
                            if (this.values.any(x => x.name == "ELSE")) {
                                return ListExt.evaluate(this.values, "ELSE", context);
                            }
                        }
                        return null;
                    }
                    async evaluateAsync(context) {
                        let ifValue = await ListExt.evaluateAsync(this.values, "IF", context);
                        if (ifValue) {
                            if (this.values.any(x => x.name == "THEN")) {
                                return await ListExt.evaluateAsync(this.values, "THEN", context);
                            }
                        }
                        else {
                            if (this.values.any(x => x.name == "ELSE")) {
                                return await ListExt.evaluateAsync(this.values, "ELSE", context);
                            }
                        }
                        return null;
                    }
                }
                Logic.LogicTernary = LogicTernary;
            })(Logic = Blocks.Logic || (Blocks.Logic = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Logic;
            (function (Logic) {
                var StiValueHelper = Stimulsoft.Base.Helpers.StiValueHelper;
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class LogicNegate extends IronBlock {
                    evaluate(context) {
                        var _a;
                        return !(((_a = StiValueHelper.tryToBool(ListExt.evaluate(this.values, "BOOL", context))) !== null && _a !== void 0 ? _a : false));
                    }
                    async evaluateAsync(context) {
                        var _a;
                        return !(((_a = StiValueHelper.tryToBool(await ListExt.evaluateAsync(this.values, "BOOL", context))) !== null && _a !== void 0 ? _a : false));
                    }
                }
                Logic.LogicNegate = LogicNegate;
            })(Logic = Blocks.Logic || (Blocks.Logic = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Logic;
            (function (Logic) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class LogicOperation extends IronBlock {
                    evaluate(context) {
                        var _a, _b;
                        let a = (_a = ListExt.evaluate(this.values, "A", context)) !== null && _a !== void 0 ? _a : false;
                        let b = (_b = ListExt.evaluate(this.values, "B", context)) !== null && _b !== void 0 ? _b : false;
                        return this.evaluateInner(a, b);
                    }
                    async evaluateAsync(context) {
                        var _a, _b;
                        let a = (_a = await ListExt.evaluateAsync(this.values, "A", context)) !== null && _a !== void 0 ? _a : false;
                        let b = (_b = await ListExt.evaluateAsync(this.values, "B", context)) !== null && _b !== void 0 ? _b : false;
                        return this.evaluateInner(a, b);
                    }
                    evaluateInner(a, b) {
                        let op = ListExt.get(this.fields, "OP");
                        switch (op) {
                            case "AND": return a && b;
                            case "OR": return a || b;
                            default: throw new Error("Unknown OP " + op);
                        }
                    }
                }
                Logic.LogicOperation = LogicOperation;
            })(Logic = Blocks.Logic || (Blocks.Logic = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Lists;
            (function (Lists) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class ListsSplit extends IronBlock {
                    evaluate(context) {
                        let input = ListExt.evaluate(this.values, "INPUT", context);
                        let delim = ListExt.evaluate(this.values, "DELIM", context).stimulsoft().toString();
                        return this.evaluateInner(input, delim);
                    }
                    async evaluateAsync(context) {
                        let input = await ListExt.evaluateAsync(this.values, "INPUT", context);
                        let delim = (await ListExt.evaluateAsync(this.values, "DELIM", context)).stimulsoft().toString();
                        return this.evaluateInner(input, delim);
                    }
                    evaluateInner(input, delim) {
                        let mode = ListExt.get(this.fields, "MODE");
                        switch (mode) {
                            case "SPLIT": {
                                let text = input.stimulsoft().toString();
                                return text.split(delim).stimulsoft().toList();
                            }
                            case "JOIN": {
                                let list = input;
                                return list.join(delim);
                            }
                            default:
                                throw new Error("unknown mode: " + mode);
                        }
                        return null;
                    }
                }
                Lists.ListsSplit = ListsSplit;
            })(Lists = Blocks.Lists || (Blocks.Lists = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Lists;
            (function (Lists) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class ListsIndexOf extends IronBlock {
                    evaluate(context) {
                        let direction = ListExt.get(this.fields, "END");
                        let value = ListExt.evaluate(this.values, "VALUE", context);
                        let find = ListExt.evaluate(this.values, "FIND", context);
                        switch (direction) {
                            case "FIRST":
                                return value.indexOf(find) + 1;
                            case "LAST":
                                return value.lastIndexOf(find) + 1;
                            default:
                                throw new Error("Unknown end: " + direction);
                        }
                    }
                    async evaluateAsync(context) {
                        let direction = ListExt.get(this.fields, "END");
                        let value = await ListExt.evaluateAsync(this.values, "VALUE", context);
                        let find = await ListExt.evaluateAsync(this.values, "FIND", context);
                        switch (direction) {
                            case "FIRST":
                                return value.indexOf(find) + 1;
                            case "LAST":
                                return value.lastIndexOf(find) + 1;
                            default:
                                throw new Error("Unknown end: " + direction);
                        }
                    }
                }
                Lists.ListsIndexOf = ListsIndexOf;
            })(Lists = Blocks.Lists || (Blocks.Lists = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Lists;
            (function (Lists) {
                var CollectionBase = Stimulsoft.System.Collections.CollectionBase;
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var Convert = Stimulsoft.System.Convert;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class ListsGetIndex extends IronBlock {
                    evaluate(context) {
                        let values = ListExt.evaluate(this.values, "VALUE", context);
                        let mode = ListExt.get(this.fields, "MODE");
                        let where = ListExt.get(this.fields, "WHERE");
                        let index = -1;
                        switch (where) {
                            case "FROM_START":
                                index = Convert.toInt32(ListExt.evaluate(this.values, "AT", context)) - 1;
                                break;
                            case "FROM_END":
                                index = values.length - Convert.toInt32(ListExt.evaluate(this.values, "AT", context));
                                break;
                            case "FIRST":
                                index = 0;
                                break;
                            case "LAST":
                                index = values.length - 1;
                                break;
                            case "RANDOM":
                                index = Math.random() * values.length;
                                break;
                            default:
                                throw new Error("unsupported where (" + where + ")");
                        }
                        switch (mode) {
                            case "GET": {
                                if (values.stimulsoft().is(CollectionBase))
                                    values = values.toList();
                                return values[index];
                            }
                            case "GET_REMOVE": {
                                let value = values.getByIndex(index);
                                values.removeAt(index);
                                return value;
                            }
                            case "REMOVE":
                                values.removeAt(index);
                                return null;
                            default:
                                throw new Error("unsupported mode (" + mode + ")");
                        }
                    }
                    async evaluateAsync(context) {
                        let values = await ListExt.evaluateAsync(this.values, "VALUE", context);
                        let mode = ListExt.get(this.fields, "MODE");
                        let where = ListExt.get(this.fields, "WHERE");
                        let index = -1;
                        switch (where) {
                            case "FROM_START":
                                index = Convert.toInt32(await ListExt.evaluateAsync(this.values, "AT", context)) - 1;
                                break;
                            case "FROM_END":
                                index = values.length - Convert.toInt32(await ListExt.evaluateAsync(this.values, "AT", context));
                                break;
                            case "FIRST":
                                index = 0;
                                break;
                            case "LAST":
                                index = values.length - 1;
                                break;
                            case "RANDOM":
                                index = Math.random() * values.length;
                                break;
                            default:
                                throw new Error("unsupported where (" + where + ")");
                        }
                        switch (mode) {
                            case "GET": {
                                if (values.stimulsoft().is(CollectionBase))
                                    values = values.toList();
                                return values[index];
                            }
                            case "GET_REMOVE": {
                                let value = values.getByIndex(index);
                                values.removeAt(index);
                                return value;
                            }
                            case "REMOVE":
                                values.removeAt(index);
                                return null;
                            default:
                                throw new Error("unsupported mode (" + mode + ")");
                        }
                    }
                }
                Lists.ListsGetIndex = ListsGetIndex;
            })(Lists = Blocks.Lists || (Blocks.Lists = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Lists;
            (function (Lists) {
                var List = Stimulsoft.System.Collections.List;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class ListsCreateWith extends IronBlock {
                    evaluate(context) {
                        let list = new List();
                        for (let value of this.values) {
                            list.add(value.evaluate(context));
                        }
                        return list;
                    }
                    async evaluateAsync(context) {
                        let list = new List();
                        for (let value of this.values) {
                            list.add(await value.evaluateAsync(context));
                        }
                        return list;
                    }
                }
                Lists.ListsCreateWith = ListsCreateWith;
            })(Lists = Blocks.Lists || (Blocks.Lists = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Lists;
            (function (Lists) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class ListsLength extends IronBlock {
                    evaluate(context) {
                        let value = ListExt.evaluate(this.values, "VALUE", context);
                        if (value == null)
                            return 0;
                        return value.length;
                    }
                    async evaluateAsync(context) {
                        let value = await ListExt.evaluateAsync(this.values, "VALUE", context);
                        if (value == null)
                            return 0;
                        return value.length;
                    }
                }
                Lists.ListsLength = ListsLength;
            })(Lists = Blocks.Lists || (Blocks.Lists = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Lists;
            (function (Lists) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                var List = Stimulsoft.System.Collections.List;
                class ListsRepeat extends IronBlock {
                    evaluate(context) {
                        let item = ListExt.evaluate(this.values, "ITEM", context);
                        let num = Blockly.StiObjConverter.toDouble(ListExt.evaluate(this.values, "NUM", context));
                        let list = new List();
                        for (let i = 0; i < num; i++) {
                            list.add(item);
                        }
                        return list;
                    }
                    async evaluateAsync(context) {
                        let item = await ListExt.evaluateAsync(this.values, "ITEM", context);
                        let num = Blockly.StiObjConverter.toDouble(await ListExt.evaluateAsync(this.values, "NUM", context));
                        let list = new List();
                        for (let i = 0; i < num; i++) {
                            list.add(item);
                        }
                        return list;
                    }
                }
                Lists.ListsRepeat = ListsRepeat;
            })(Lists = Blocks.Lists || (Blocks.Lists = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Lists;
            (function (Lists) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class ListsIsEmpty extends IronBlock {
                    evaluate(context) {
                        let value = ListExt.evaluate(this.values, "VALUE", context);
                        if (value == null)
                            return true;
                        return !value.any();
                    }
                    async evaluateAsync(context) {
                        let value = await ListExt.evaluateAsync(this.values, "VALUE", context);
                        if (value == null)
                            return true;
                        return !value.any();
                    }
                }
                Lists.ListsIsEmpty = ListsIsEmpty;
            })(Lists = Blocks.Lists || (Blocks.Lists = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Maths;
            (function (Maths) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class MathArithmetic extends IronBlock {
                    evaluate(context) {
                        let a = Blockly.StiObjConverter.toDouble(ListExt.evaluate(this.values, "A", context));
                        let b = Blockly.StiObjConverter.toDouble(ListExt.evaluate(this.values, "B", context));
                        return this.evaluateInner(a, b);
                    }
                    async evaluateAsync(context) {
                        let a = Blockly.StiObjConverter.toDouble(await ListExt.evaluateAsync(this.values, "A", context));
                        let b = Blockly.StiObjConverter.toDouble(await ListExt.evaluateAsync(this.values, "B", context));
                        return this.evaluateInner(a, b);
                    }
                    evaluateInner(a, b) {
                        let opValue = ListExt.get(this.fields, "OP");
                        switch (opValue) {
                            case "MULTIPLY": return a * b;
                            case "DIVIDE": return a / b;
                            case "ADD": return a + b;
                            case "MINUS": return a - b;
                            case "POWER": return Math.pow(a, b);
                            default: throw new Error("Unknown OP " + opValue);
                        }
                    }
                }
                Maths.MathArithmetic = MathArithmetic;
            })(Maths = Blocks.Maths || (Blocks.Maths = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Maths;
            (function (Maths) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class MathNumber extends IronBlock {
                    evaluate(context) {
                        return Number.parseFloat(ListExt.get(this.fields, "NUM"));
                    }
                    async evaluateAsync(context) {
                        return Number.parseFloat(ListExt.get(this.fields, "NUM"));
                    }
                }
                Maths.MathNumber = MathNumber;
            })(Maths = Blocks.Maths || (Blocks.Maths = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Maths;
            (function (Maths) {
                var StiMath = Stimulsoft.System.StiMath;
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class MathSingle extends IronBlock {
                    evaluate(context) {
                        let op = ListExt.get(this.fields, "OP");
                        let number = Blockly.StiObjConverter.toDouble(ListExt.evaluate(this.values, "NUM", context));
                        return this.evaluateInner(op, number);
                    }
                    async evaluateAsync(context) {
                        let op = ListExt.get(this.fields, "OP");
                        let number = Blockly.StiObjConverter.toDouble(await ListExt.evaluateAsync(this.values, "NUM", context));
                        return this.evaluateInner(op, number);
                    }
                    evaluateInner(op, number) {
                        switch (op) {
                            case "ROOT": return Math.sqrt(number);
                            case "ABS": return Math.abs(number);
                            case "NEG": return -1 * number;
                            case "LN": return Math.log(number);
                            case "LOG10": return StiMath.log10(number);
                            case "EXP": return Math.exp(number);
                            case "POW10": return Math.pow(10, number);
                            case "SIN": return Math.sin(number / 180 * Math.PI);
                            case "COS": return Math.cos(number / 180 * Math.PI);
                            case "TAN": return Math.tan(number / 180 * Math.PI);
                            case "ASIN": return Math.asin(number);
                            case "ACOS": return Math.acos(number);
                            case "ATAN": return Math.atan(number);
                            default: throw new Error("Unknown OP " + op);
                        }
                    }
                }
                Maths.MathSingle = MathSingle;
            })(Maths = Blocks.Maths || (Blocks.Maths = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Maths;
            (function (Maths) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class MathRound extends IronBlock {
                    evaluate(context) {
                        let op = ListExt.get(this.fields, "OP");
                        let number = Blockly.StiObjConverter.toDouble(ListExt.evaluate(this.values, "NUM", context));
                        return this.evaluateInner(op, number);
                    }
                    async evaluateAsync(context) {
                        let op = ListExt.get(this.fields, "OP");
                        let number = Blockly.StiObjConverter.toDouble(await ListExt.evaluateAsync(this.values, "NUM", context));
                        return this.evaluateInner(op, number);
                    }
                    evaluateInner(op, number) {
                        switch (op) {
                            case "ROUND": return Math.round(number);
                            case "ROUNDUP": return Math.ceil(number);
                            case "ROUNDDOWN": return Math.floor(number);
                            default: throw new Error("Unknown OP " + op);
                        }
                    }
                }
                Maths.MathRound = MathRound;
            })(Maths = Blocks.Maths || (Blocks.Maths = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Maths;
            (function (Maths) {
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class MathRandomFloat extends IronBlock {
                    evaluate(context) {
                        return Math.random();
                    }
                    async evaluateAsync(context) {
                        return Math.random();
                    }
                }
                Maths.MathRandomFloat = MathRandomFloat;
            })(Maths = Blocks.Maths || (Blocks.Maths = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Maths;
            (function (Maths) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class MathModulo extends IronBlock {
                    evaluate(context) {
                        let dividend = Blockly.StiObjConverter.toDouble(ListExt.evaluate(this.values, "DIVIDEND", context));
                        let divisor = Blockly.StiObjConverter.toDouble(ListExt.evaluate(this.values, "DIVISOR", context));
                        return dividend % divisor;
                    }
                    async evaluateAsync(context) {
                        let dividend = Blockly.StiObjConverter.toDouble(await ListExt.evaluateAsync(this.values, "DIVIDEND", context));
                        let divisor = Blockly.StiObjConverter.toDouble(await ListExt.evaluateAsync(this.values, "DIVISOR", context));
                        return dividend % divisor;
                    }
                }
                Maths.MathModulo = MathModulo;
            })(Maths = Blocks.Maths || (Blocks.Maths = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Maths;
            (function (Maths) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class MathConstrain extends IronBlock {
                    evaluate(context) {
                        let value = Blockly.StiObjConverter.toDouble(ListExt.evaluate(this.values, "VALUE", context));
                        let low = Blockly.StiObjConverter.toDouble(ListExt.evaluate(this.values, "LOW", context));
                        let high = Blockly.StiObjConverter.toDouble(ListExt.evaluate(this.values, "HIGH", context));
                        return Math.min(Math.max(value, low), high);
                    }
                    async evaluateAsync(context) {
                        let value = Blockly.StiObjConverter.toDouble(await ListExt.evaluateAsync(this.values, "VALUE", context));
                        let low = Blockly.StiObjConverter.toDouble(await ListExt.evaluateAsync(this.values, "LOW", context));
                        let high = Blockly.StiObjConverter.toDouble(await ListExt.evaluateAsync(this.values, "HIGH", context));
                        return Math.min(Math.max(value, low), high);
                    }
                }
                Maths.MathConstrain = MathConstrain;
            })(Maths = Blocks.Maths || (Blocks.Maths = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Maths;
            (function (Maths) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class MathOnList extends IronBlock {
                    evaluate(context) {
                        let op = ListExt.get(this.fields, "OP");
                        let list = ListExt.evaluate(this.values, "LIST", context);
                        return this.evaluateInner(list, op);
                    }
                    async evaluateAsync(context) {
                        let op = ListExt.get(this.fields, "OP");
                        let list = await ListExt.evaluateAsync(this.values, "LIST", context);
                        return this.evaluateInner(list, op);
                    }
                    evaluateInner(list, op) {
                        let doubleList = list.select(x => x);
                        switch (op) {
                            case "SUM": return doubleList.sum();
                            case "MIN": return doubleList.min();
                            case "MAX": return doubleList.max();
                            case "AVERAGE": return ListExt.average(doubleList);
                            case "MEDIAN": return this.median(doubleList);
                            case "RANDOM": return doubleList.any() ? doubleList[Math.random() * doubleList.length] : null;
                            case "STD_DEV":
                                throw new Error("OP " + op + " not implemented");
                            default: throw new Error("Unknown OP " + op);
                        }
                    }
                    median(values) {
                        if (!values.any())
                            return null;
                        let sortedValues = values.orderBy(x => x);
                        let mid = (sortedValues.length - 1) / 2.0;
                        return (sortedValues[Math.floor(mid)] + sortedValues[Math.floor(mid + 0.5)]) / 2;
                    }
                }
                Maths.MathOnList = MathOnList;
            })(Maths = Blocks.Maths || (Blocks.Maths = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Maths;
            (function (Maths) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class MathConstant extends IronBlock {
                    evaluate(context) {
                        let constant = ListExt.get(this.fields, "CONSTANT");
                        return this.getValue(constant);
                    }
                    async evaluateAsync(context) {
                        let constant = ListExt.get(this.fields, "CONSTANT");
                        return this.getValue(constant);
                    }
                    getValue(constant) {
                        switch (constant) {
                            case "PI": return Math.PI;
                            case "E": return Math.E;
                            case "GOLDEN_RATIO": return (1 + Math.sqrt(5)) / 2;
                            case "SQRT2": return Math.sqrt(2);
                            case "SQRT1_2": return Math.sqrt(0.5);
                            case "INFINITY": return Number.POSITIVE_INFINITY;
                            default: throw new Error("Unknown CONSTANT " + constant);
                        }
                    }
                }
                Maths.MathConstant = MathConstant;
            })(Maths = Blocks.Maths || (Blocks.Maths = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Maths;
            (function (Maths) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class MathNumberProperty extends IronBlock {
                    evaluate(context) {
                        let op = ListExt.get(this.fields, "PROPERTY");
                        let number = Number.parseFloat(ListExt.evaluate(this.values, "NUMBER_TO_CHECK", context));
                        return this.evaluateInner(op, number, context);
                    }
                    async evaluateAsync(context) {
                        let op = ListExt.get(this.fields, "PROPERTY");
                        let number = Number.parseFloat(await ListExt.evaluateAsync(this.values, "NUMBER_TO_CHECK", context));
                        return this.evaluateInner(op, number, context);
                    }
                    evaluateInner(op, number, context) {
                        switch (op) {
                            case "EVEN": return 0 == number % 2.0;
                            case "ODD": return 1 == number % 2.0;
                            case "PRIME": return this.isPrime(number);
                            case "WHOLE": return 0 == number % 1.0;
                            case "POSITIVE": return number > 0;
                            case "NEGATIVE": return number < 0;
                            case "DIVISIBLE_BY": return 0 == number % ListExt.evaluate(this.values, "DIVISOR", context);
                            default: throw new Error("Unknown PROPERTY " + op);
                        }
                    }
                    isPrime(number) {
                        if (number == 1)
                            return false;
                        if (number == 2)
                            return true;
                        if (number % 2 == 0)
                            return false;
                        let boundary = Math.floor(Math.sqrt(number));
                        for (let i = 3; i <= boundary; i += 2) {
                            if (number % i == 0)
                                return false;
                        }
                        return true;
                    }
                }
                Maths.MathNumberProperty = MathNumberProperty;
            })(Maths = Blocks.Maths || (Blocks.Maths = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Variables;
            (function (Variables) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class VariablesGet extends IronBlock {
                    evaluate(context) {
                        let variableName = ListExt.get(this.fields, "VAR");
                        if (!context.variables.containsKey(variableName))
                            return null;
                        return context.variables.get(variableName);
                    }
                    async evaluateAsync(context) {
                        let variableName = ListExt.get(this.fields, "VAR");
                        if (!context.variables.containsKey(variableName))
                            return null;
                        return context.variables.get(variableName);
                    }
                }
                Variables.VariablesGet = VariablesGet;
            })(Variables = Blocks.Variables || (Blocks.Variables = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Variables;
            (function (Variables) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class VariablesSet extends IronBlock {
                    evaluate(context) {
                        let variables = context.variables;
                        let value = ListExt.evaluate(this.values, "VALUE", context);
                        let variableName = ListExt.get(this.fields, "VAR");
                        if (variables.containsKey(variableName)) {
                            variables.set(variableName, value);
                        }
                        else {
                            variables.add(variableName, value);
                        }
                        return super.evaluate(context);
                    }
                    async evaluateAsync(context) {
                        let variables = context.variables;
                        let value = await ListExt.evaluateAsync(this.values, "VALUE", context);
                        let variableName = ListExt.get(this.fields, "VAR");
                        if (variables.containsKey(variableName)) {
                            variables.set(variableName, value);
                        }
                        else {
                            variables.add(variableName, value);
                        }
                        return await super.evaluateAsync(context);
                    }
                }
                Variables.VariablesSet = VariablesSet;
            })(Variables = Blocks.Variables || (Blocks.Variables = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Visuals;
            (function (Visuals) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class StiColorHex extends IronBlock {
                    evaluate(context) {
                        return ListExt.evaluate(this.values, "TEXT", context).stimulsoft().toString();
                    }
                    async evaluateAsync(context) {
                        return (await ListExt.evaluateAsync(this.values, "TEXT", context)).stimulsoft().toString();
                    }
                }
                Visuals.StiColorHex = StiColorHex;
            })(Visuals = StiBlocks.Visuals || (StiBlocks.Visuals = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Visuals;
            (function (Visuals) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class StiColorARGB extends IronBlock {
                    evaluate(context) {
                        let alpha = ListExt.evaluate(this.values, "ALPHA", context);
                        let red = ListExt.evaluate(this.values, "RED", context);
                        let green = ListExt.evaluate(this.values, "GREEN", context);
                        let blue = ListExt.evaluate(this.values, "BLUE", context);
                        let alphaString = alpha.toString(16);
                        let redString = red.toString(16);
                        let greenString = green.toString(16);
                        let blueString = blue.toString(16);
                        if (alphaString.length == 1)
                            alphaString = "0" + alphaString;
                        if (redString.length == 1)
                            redString = "0" + redString;
                        if (greenString.length == 1)
                            greenString = "0" + greenString;
                        if (blueString.length == 1)
                            blueString = "0" + blueString;
                        return ("#" + alphaString + redString + greenString + blueString).toUpperCase();
                    }
                    async evaluateAsync(context) {
                        let alpha = await ListExt.evaluateAsync(this.values, "ALPHA", context);
                        let red = await ListExt.evaluateAsync(this.values, "RED", context);
                        let green = await ListExt.evaluateAsync(this.values, "GREEN", context);
                        let blue = await ListExt.evaluateAsync(this.values, "BLUE", context);
                        let alphaString = alpha.toString(16);
                        let redString = red.toString(16);
                        let greenString = green.toString(16);
                        let blueString = blue.toString(16);
                        if (alphaString.length == 1)
                            alphaString = "0" + alphaString;
                        if (redString.length == 1)
                            redString = "0" + redString;
                        if (greenString.length == 1)
                            greenString = "0" + greenString;
                        if (blueString.length == 1)
                            blueString = "0" + blueString;
                        return ("#" + alphaString + redString + greenString + blueString).toUpperCase();
                    }
                }
                Visuals.StiColorARGB = StiColorARGB;
            })(Visuals = StiBlocks.Visuals || (StiBlocks.Visuals = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Visuals;
            (function (Visuals) {
                var StiConvert = Stimulsoft.Base.StiConvert;
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var FontStyle = Stimulsoft.System.Drawing.FontStyle;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                var Font = Stimulsoft.System.Drawing.Font;
                class StiNewFont extends IronBlock {
                    evaluate(context) {
                        let name = ListExt.get(this.fields, "NAME").toString();
                        let size = StiConvert.changeType(ListExt.evaluate(this.values, "SIZE", context), Number);
                        let bold = StiConvert.changeType(ListExt.get(this.fields, "BOLD"), Boolean);
                        let italic = StiConvert.changeType(ListExt.get(this.fields, "ITALIC"), Boolean);
                        let underline = StiConvert.changeType(ListExt.get(this.fields, "UNDERLINE"), Boolean);
                        let strikeout = StiConvert.changeType(ListExt.get(this.fields, "STRIKEOUT"), Boolean);
                        let fontStyle = FontStyle.Regular;
                        if (bold)
                            fontStyle |= FontStyle.Bold;
                        if (italic)
                            fontStyle |= FontStyle.Italic;
                        if (underline)
                            fontStyle |= FontStyle.Underline;
                        if (strikeout)
                            fontStyle |= FontStyle.Strikeout;
                        return new Font(name, size, fontStyle);
                    }
                    async evaluateAsync(context) {
                        let name = ListExt.get(this.fields, "NAME").toString();
                        let size = StiConvert.changeType(await ListExt.evaluateAsync(this.values, "SIZE", context), Number);
                        let bold = StiConvert.changeType(ListExt.get(this.fields, "BOLD"), Boolean);
                        let italic = StiConvert.changeType(ListExt.get(this.fields, "ITALIC"), Boolean);
                        let underline = StiConvert.changeType(ListExt.get(this.fields, "UNDERLINE"), Boolean);
                        let strikeout = StiConvert.changeType(ListExt.get(this.fields, "STRIKEOUT"), Boolean);
                        let fontStyle = FontStyle.Regular;
                        if (bold)
                            fontStyle |= FontStyle.Bold;
                        if (italic)
                            fontStyle |= FontStyle.Italic;
                        if (underline)
                            fontStyle |= FontStyle.Underline;
                        if (strikeout)
                            fontStyle |= FontStyle.Strikeout;
                        return new Font(name, size, fontStyle);
                    }
                }
                Visuals.StiNewFont = StiNewFont;
            })(Visuals = StiBlocks.Visuals || (StiBlocks.Visuals = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Visuals;
            (function (Visuals) {
                var ColorTranslator = Stimulsoft.System.Drawing.ColorTranslator;
                var StiSolidBrush = Stimulsoft.Base.Drawing.StiSolidBrush;
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class StiNewSolidBrush extends IronBlock {
                    evaluate(context) {
                        let color = ListExt.evaluate(this.values, "COLOR", context).stimulsoft().toString();
                        return new StiSolidBrush(ColorTranslator.fromHtml(color));
                    }
                    async evaluateAsync(context) {
                        let color = (await ListExt.evaluateAsync(this.values, "COLOR", context)).stimulsoft().toString();
                        return new StiSolidBrush(ColorTranslator.fromHtml(color));
                    }
                }
                Visuals.StiNewSolidBrush = StiNewSolidBrush;
            })(Visuals = StiBlocks.Visuals || (StiBlocks.Visuals = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Visuals;
            (function (Visuals) {
                var Convert = Stimulsoft.System.Convert;
                var ColorTranslator = Stimulsoft.System.Drawing.ColorTranslator;
                var StiGradientBrush = Stimulsoft.Base.Drawing.StiGradientBrush;
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class StiNewGradientBrush extends IronBlock {
                    evaluate(context) {
                        let startColor = ListExt.evaluate(this.values, "STARTCOLOR", context).stimulsoft().toString();
                        let endColor = ListExt.evaluate(this.values, "ENDCOLOR", context).stimulsoft().toString();
                        let angle = ListExt.evaluate(this.values, "ANGLE", context);
                        return new StiGradientBrush(ColorTranslator.fromHtml(startColor), ColorTranslator.fromHtml(endColor), Convert.toDouble(angle));
                    }
                    async evaluateAsync(context) {
                        let startColor = (await ListExt.evaluateAsync(this.values, "STARTCOLOR", context)).stimulsoft().toString();
                        let endColor = (await ListExt.evaluateAsync(this.values, "ENDCOLOR", context)).stimulsoft().toString();
                        let angle = await ListExt.evaluateAsync(this.values, "ANGLE", context);
                        return new StiGradientBrush(ColorTranslator.fromHtml(startColor), ColorTranslator.fromHtml(endColor), Convert.toDouble(angle));
                    }
                }
                Visuals.StiNewGradientBrush = StiNewGradientBrush;
            })(Visuals = StiBlocks.Visuals || (StiBlocks.Visuals = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Visuals;
            (function (Visuals) {
                var StiConvert = Stimulsoft.Base.StiConvert;
                var StiBorderSides = Stimulsoft.Base.Drawing.StiBorderSides;
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var ColorTranslator = Stimulsoft.System.Drawing.ColorTranslator;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                var StiBorder = Stimulsoft.Base.Drawing.StiBorder;
                class StiNewBorder extends IronBlock {
                    evaluate(context) {
                        let color = ColorTranslator.fromHtml(ListExt.evaluate(this.values, "COLOR", context).toString());
                        let size = StiConvert.changeType(ListExt.evaluate(this.values, "SIZE", context), Number);
                        let penStyle = ListExt.evaluate(this.values, "STYLE", context);
                        let top = StiConvert.changeType(ListExt.get(this.fields, "TOP"), Boolean);
                        let left = StiConvert.changeType(ListExt.get(this.fields, "LEFT"), Boolean);
                        let right = StiConvert.changeType(ListExt.get(this.fields, "RIGHT"), Boolean);
                        let bottom = StiConvert.changeType(ListExt.get(this.fields, "BOTTOM"), Boolean);
                        let borderSides = StiBorderSides.None;
                        if (top)
                            borderSides |= StiBorderSides.Top;
                        if (left)
                            borderSides |= StiBorderSides.Left;
                        if (right)
                            borderSides |= StiBorderSides.Right;
                        if (bottom)
                            borderSides |= StiBorderSides.Bottom;
                        return new StiBorder(borderSides, color, size, penStyle);
                    }
                    async evaluateAsync(context) {
                        let color = ColorTranslator.fromHtml((await ListExt.evaluateAsync(this.values, "COLOR", context)).toString());
                        let size = StiConvert.changeType(await ListExt.evaluateAsync(this.values, "SIZE", context), Number);
                        let penStyle = await ListExt.evaluateAsync(this.values, "STYLE", context);
                        let top = StiConvert.changeType(ListExt.get(this.fields, "TOP"), Boolean);
                        let left = StiConvert.changeType(ListExt.get(this.fields, "LEFT"), Boolean);
                        let right = StiConvert.changeType(ListExt.get(this.fields, "RIGHT"), Boolean);
                        let bottom = StiConvert.changeType(ListExt.get(this.fields, "BOTTOM"), Boolean);
                        let borderSides = StiBorderSides.None;
                        if (top)
                            borderSides |= StiBorderSides.Top;
                        if (left)
                            borderSides |= StiBorderSides.Left;
                        if (right)
                            borderSides |= StiBorderSides.Right;
                        if (bottom)
                            borderSides |= StiBorderSides.Bottom;
                        return new StiBorder(borderSides, color, size, penStyle);
                    }
                }
                Visuals.StiNewBorder = StiNewBorder;
            })(Visuals = StiBlocks.Visuals || (StiBlocks.Visuals = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Visuals;
            (function (Visuals) {
                var StiGradientBrush = Stimulsoft.Base.Drawing.StiGradientBrush;
                var StiEmptyBrush = Stimulsoft.Base.Drawing.StiEmptyBrush;
                var ColorTranslator = Stimulsoft.System.Drawing.ColorTranslator;
                var StiSolidBrush = Stimulsoft.Base.Drawing.StiSolidBrush;
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                var Convert = Stimulsoft.System.Convert;
                class StiNewBrush extends IronBlock {
                    evaluate(context) {
                        let value = ListExt.get(this.fields, "VALUE").toLowerCase();
                        switch (value) {
                            case "empty":
                                return new StiEmptyBrush();
                            case "solid":
                                {
                                    let color = ListExt.evaluate(this.values, "COLOR", context).stimulsoft().toString();
                                    return new StiSolidBrush(ColorTranslator.fromHtml(color));
                                }
                            case "gradient":
                                {
                                    let startColor = ListExt.evaluate(this.values, "STARTCOLOR", context).stimulsoft().toString();
                                    let endColor = ListExt.evaluate(this.values, "ENDCOLOR", context).stimulsoft().toString();
                                    let angle = ListExt.evaluate(this.values, "ANGLE", context);
                                    return new StiGradientBrush(ColorTranslator.fromHtml(startColor), ColorTranslator.fromHtml(endColor), Convert.toDouble(angle));
                                }
                        }
                        return new StiEmptyBrush();
                    }
                    async evaluateAsync(context) {
                        let value = ListExt.get(this.fields, "VALUE");
                        switch (value) {
                            case "empty":
                                return new StiEmptyBrush();
                            case "solid":
                                {
                                    let color = (await ListExt.evaluateAsync(this.values, "COLOR", context)).stimulsoft().toString();
                                    return new StiSolidBrush(ColorTranslator.fromHtml(color));
                                }
                            case "gradient":
                                {
                                    let startColor = (await ListExt.evaluateAsync(this.values, "STARTCOLOR", context)).stimulsoft().toString();
                                    let endColor = (await ListExt.evaluateAsync(this.values, "ENDCOLOR", context)).stimulsoft().toString();
                                    let angle = await ListExt.evaluateAsync(this.values, "ANGLE", context);
                                    return new StiGradientBrush(ColorTranslator.fromHtml(startColor), ColorTranslator.fromHtml(endColor), Convert.toDouble(angle));
                                }
                        }
                        return new StiEmptyBrush();
                    }
                }
                Visuals.StiNewBrush = StiNewBrush;
            })(Visuals = StiBlocks.Visuals || (StiBlocks.Visuals = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Visuals;
            (function (Visuals) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var StiPenStyle = Stimulsoft.Base.Drawing.StiPenStyle;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class StiNewPenStyle extends IronBlock {
                    evaluate(context) {
                        let value = ListExt.get(this.fields, "VALUE");
                        switch (value) {
                            case "SOLID":
                                return StiPenStyle.Solid;
                            case "DASH":
                                return StiPenStyle.Dash;
                            case "DASHDOT":
                                return StiPenStyle.DashDot;
                            case "DASHDOTDOT":
                                return StiPenStyle.DashDotDot;
                            case "DOT":
                                return StiPenStyle.Dot;
                            case "DOUBLE":
                                return StiPenStyle.Double;
                            case "NONE":
                                return StiPenStyle.None;
                        }
                        return StiPenStyle.Solid;
                    }
                    async evaluateAsync(context) {
                        return this.evaluate(context);
                    }
                }
                Visuals.StiNewPenStyle = StiNewPenStyle;
            })(Visuals = StiBlocks.Visuals || (StiBlocks.Visuals = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Visuals;
            (function (Visuals) {
                var StiMargins = Stimulsoft.Report.Components.StiMargins;
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var Convert = Stimulsoft.System.Convert;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class StiNewMargin extends IronBlock {
                    evaluate(context) {
                        let left = Convert.toDouble(ListExt.evaluate(this.values, "LEFT", context));
                        let top = Convert.toDouble(ListExt.evaluate(this.values, "TOP", context));
                        let right = Convert.toDouble(ListExt.evaluate(this.values, "RIGHT", context));
                        let bottom = Convert.toDouble(ListExt.evaluate(this.values, "BOTTOM", context));
                        return new StiMargins(left, right, top, bottom);
                    }
                    async evaluateAsync(context) {
                        let left = Convert.toDouble(await ListExt.evaluateAsync(this.values, "LEFT", context));
                        let top = Convert.toDouble(await ListExt.evaluateAsync(this.values, "TOP", context));
                        let right = Convert.toDouble(await ListExt.evaluateAsync(this.values, "RIGHT", context));
                        let bottom = Convert.toDouble(await ListExt.evaluateAsync(this.values, "BOTTOM", context));
                        return new StiMargins(left, right, top, bottom);
                    }
                }
                Visuals.StiNewMargin = StiNewMargin;
            })(Visuals = StiBlocks.Visuals || (StiBlocks.Visuals = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Visuals;
            (function (Visuals) {
                var StiPadding = Stimulsoft.Report.Dashboard.StiPadding;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var Convert = Stimulsoft.System.Convert;
                class StiNewPadding extends IronBlock {
                    evaluate(context) {
                        let left = Convert.toDouble(ListExt.evaluate(this.values, "LEFT", context));
                        let top = Convert.toDouble(ListExt.evaluate(this.values, "TOP", context));
                        let right = Convert.toDouble(ListExt.evaluate(this.values, "RIGHT", context));
                        let bottom = Convert.toDouble(ListExt.evaluate(this.values, "BOTTOM", context));
                        return new StiPadding(left, top, right, bottom);
                    }
                    async evaluateAsync(context) {
                        let left = Convert.toDouble(await ListExt.evaluateAsync(this.values, "LEFT", context));
                        let top = Convert.toDouble(await ListExt.evaluateAsync(this.values, "TOP", context));
                        let right = Convert.toDouble(await ListExt.evaluateAsync(this.values, "RIGHT", context));
                        let bottom = Convert.toDouble(await ListExt.evaluateAsync(this.values, "BOTTOM", context));
                        return new StiPadding(left, top, right, bottom);
                    }
                }
                Visuals.StiNewPadding = StiNewPadding;
            })(Visuals = StiBlocks.Visuals || (StiBlocks.Visuals = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Visuals;
            (function (Visuals) {
                var StiCornerRadius = Stimulsoft.Base.Drawing.StiCornerRadius;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var Convert = Stimulsoft.System.Convert;
                class StiNewCornerRadius extends IronBlock {
                    evaluate(context) {
                        let topLeft = Convert.toDouble(ListExt.evaluate(this.values, "TOPLEFT", context));
                        let topRight = Convert.toDouble(ListExt.evaluate(this.values, "TOPRIGHT", context));
                        let bottomRight = Convert.toDouble(ListExt.evaluate(this.values, "BOTTOMRIGHT", context));
                        let bottomLeft = Convert.toDouble(ListExt.evaluate(this.values, "BOTTOMLEFT", context));
                        return new StiCornerRadius(topLeft, topRight, bottomRight, bottomLeft);
                    }
                    async evaluateAsync(context) {
                        let topLeft = Convert.toDouble(await ListExt.evaluateAsync(this.values, "TOPLEFT", context));
                        let topRight = Convert.toDouble(await ListExt.evaluateAsync(this.values, "TOPRIGHT", context));
                        let bottomRight = Convert.toDouble(await ListExt.evaluateAsync(this.values, "BOTTOMRIGHT", context));
                        let bottomLeft = Convert.toDouble(await ListExt.evaluateAsync(this.values, "BOTTOMLEFT", context));
                        return new StiCornerRadius(topLeft, topRight, bottomRight, bottomLeft);
                    }
                }
                Visuals.StiNewCornerRadius = StiNewCornerRadius;
            })(Visuals = StiBlocks.Visuals || (StiBlocks.Visuals = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Data;
            (function (Data) {
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                class StiGetDataSource extends IronBlock {
                    evaluate(context) {
                        let name = ListExt.get(this.fields, "NAME");
                        return context.report.dictionary.dataSources.getByName(name);
                    }
                    async evaluateAsync(context) {
                        let name = ListExt.get(this.fields, "NAME");
                        return context.report.dictionary.dataSources.getByName(name);
                    }
                }
                Data.StiGetDataSource = StiGetDataSource;
            })(Data = StiBlocks.Data || (StiBlocks.Data = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Data;
            (function (Data) {
                var StiRenderProvider = Stimulsoft.Report.Engine.StiRenderProvider;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                class StiDataSourceGetData extends IronBlock {
                    evaluate(context) {
                        let dataSource = ListExt.evaluate(this.values, "DATA", context);
                        if (dataSource != null) {
                            if (dataSource.isConnected == false) {
                                let list = new Array();
                                list.push(dataSource);
                                context.report.dictionary.connect(true, list);
                            }
                            let columnName = ListExt.evaluate(this.values, "COLUMN", context);
                            let rowIndex = ListExt.evaluate(this.values, "ROW", context);
                            return dataSource.getData(columnName, rowIndex);
                        }
                        return super.evaluate(context);
                    }
                    async evaluateAsync(context) {
                        let dataSource = ListExt.evaluate(this.values, "DATA", context);
                        if (dataSource != null) {
                            await dataSource.getDataAdapter().connectDataSourceToDataAsync(dataSource.dictionary, dataSource, true).promise();
                            if (dataSource.isConnected == false)
                                await StiRenderProvider.connectToDataAsync(dataSource.dictionary.report);
                            let columnName = ListExt.evaluate(this.values, "COLUMN", context);
                            let rowIndex = ListExt.evaluate(this.values, "ROW", context);
                            return await dataSource.getDataAsync(columnName, rowIndex);
                        }
                        return await super.evaluateAsync(context);
                    }
                }
                Data.StiDataSourceGetData = StiDataSourceGetData;
            })(Data = StiBlocks.Data || (StiBlocks.Data = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Data;
            (function (Data) {
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                class StiGetDataSourceByName extends IronBlock {
                    evaluate(context) {
                        let name = ListExt.evaluateAsync(this.values, "NAME", context).stimulsoft().toString();
                        return context.report.dictionary.dataSources.getByName(name);
                    }
                    async evaluateAsync(context) {
                        let name = (await ListExt.evaluateAsync(this.values, "NAME", context)).stimulsoft().toString();
                        return context.report.dictionary.dataSources.getByName(name);
                    }
                }
                Data.StiGetDataSourceByName = StiGetDataSourceByName;
            })(Data = StiBlocks.Data || (StiBlocks.Data = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Data;
            (function (Data) {
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                class StiDataSourceProperty extends IronBlock {
                    evaluate(context) {
                        let obj = ListExt.evaluate(this.values, "OBJECT", context);
                        if (obj != null) {
                            let propertyName = ListExt.get(this.fields, "NAME").stimulsoft().toLowerFirst();
                            return obj[propertyName];
                        }
                        return super.evaluate(context);
                    }
                    async evaluateAsync(context) {
                        let obj = await ListExt.evaluateAsync(this.values, "OBJECT", context);
                        if (obj != null) {
                            let propertyName = ListExt.get(this.fields, "NAME").stimulsoft().toLowerFirst();
                            return obj[propertyName];
                        }
                        return await super.evaluateAsync(context);
                    }
                }
                Data.StiDataSourceProperty = StiDataSourceProperty;
            })(Data = StiBlocks.Data || (StiBlocks.Data = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Data;
            (function (Data) {
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                class StiDataSourceMethod extends IronBlock {
                    evaluate(context) {
                        let obj = ListExt.evaluate(this.values, "OBJECT", context);
                        if (obj != null) {
                            let methodName = ListExt.get(this.fields, "NAME");
                            obj[methodName]();
                        }
                        return super.evaluate(context);
                    }
                    async evaluateAsync(context) {
                        let obj = await ListExt.evaluateAsync(this.values, "OBJECT", context);
                        if (obj != null) {
                            let methodName = ListExt.get(this.fields, "NAME");
                            obj[methodName]();
                        }
                        return await super.evaluateAsync(context);
                    }
                }
                Data.StiDataSourceMethod = StiDataSourceMethod;
            })(Data = StiBlocks.Data || (StiBlocks.Data = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Data;
            (function (Data) {
                var StiCalculationMode = Stimulsoft.Report.StiCalculationMode;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                class StiSetDataSourceSqlCommand extends IronBlock {
                    evaluate(context) {
                        var _a;
                        let sqlSource = ListExt.evaluate(this.values, "DATA", context);
                        if (sqlSource != null) {
                            let value = ListExt.evaluate(this.values, "VALUE", context).stimulsoft().toString();
                            let isInterpretationMode = ((_a = context.report) === null || _a === void 0 ? void 0 : _a.calculationMode) == StiCalculationMode.Interpretation;
                            if (isInterpretationMode) {
                                context.report.variables.set("**StoredDataSourceSqlCommandForInterpretationMode**" + sqlSource.name, value);
                            }
                            else {
                                sqlSource.sqlCommand = value;
                            }
                        }
                        return super.evaluate(context);
                    }
                    async evaluateAsync(context) {
                        var _a;
                        let sqlSource = await ListExt.evaluateAsync(this.values, "DATA", context);
                        if (sqlSource != null) {
                            let value = (await ListExt.evaluateAsync(this.values, "VALUE", context)).stimulsoft().toString();
                            let isInterpretationMode = ((_a = context.report) === null || _a === void 0 ? void 0 : _a.calculationMode) == StiCalculationMode.Interpretation;
                            if (isInterpretationMode) {
                                context.report.variables.set("**StoredDataSourceSqlCommandForInterpretationMode**" + sqlSource.name, value);
                            }
                            else {
                                sqlSource.sqlCommand = value;
                            }
                        }
                        return await super.evaluateAsync(context);
                    }
                }
                Data.StiSetDataSourceSqlCommand = StiSetDataSourceSqlCommand;
            })(Data = StiBlocks.Data || (StiBlocks.Data = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Variables;
            (function (Variables) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class StiGetVariable extends IronBlock {
                    evaluate(context) {
                        let variableName = ListExt.get(this.fields, "VALUE").toString();
                        let variable = context.report.dictionary.variables.getByName(variableName);
                        if (variable != null && variable.readOnly) {
                            let comp = context.sender.as(Stimulsoft.Report.Components.StiComponent);
                            if (comp == null) {
                                comp = new Stimulsoft.Report.Components.StiText();
                                comp.Name = "*BlocklyEvaluateVariable*";
                            }
                            return Stimulsoft.Report.Engine.StiParser.StiParser.parseTextValue("{" + variableName + "}", comp);
                        }
                        return context.report.getVariable(variableName, true);
                    }
                    async evaluateAsync(context) {
                        let variableName = ListExt.get(this.fields, "VALUE").toString();
                        let variable = context.report.dictionary.variables.getByName(variableName);
                        if (variable != null && variable.readOnly) {
                            let comp = context.sender.as(Stimulsoft.Report.Components.StiComponent);
                            if (comp == null) {
                                comp = new Stimulsoft.Report.Components.StiText();
                                comp.Name = "*BlocklyEvaluateVariable*";
                            }
                            return Stimulsoft.Report.Engine.StiParser.StiParser.parseTextValue("{" + variableName + "}", comp);
                        }
                        return context.report.getVariable(variableName, true);
                    }
                }
                Variables.StiGetVariable = StiGetVariable;
            })(Variables = StiBlocks.Variables || (StiBlocks.Variables = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Variables;
            (function (Variables) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class StiGetVariableByName extends IronBlock {
                    evaluate(context) {
                        let name = ListExt.evaluate(this.values, "VALUE", context);
                        return context.report.getVariable(name);
                    }
                    async evaluateAsync(context) {
                        let name = await ListExt.evaluateAsync(this.values, "VALUE", context);
                        return context.report.getVariable(name);
                    }
                }
                Variables.StiGetVariableByName = StiGetVariableByName;
            })(Variables = StiBlocks.Variables || (StiBlocks.Variables = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Variables;
            (function (Variables) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class StiSetVariable extends IronBlock {
                    evaluate(context) {
                        let variableName = ListExt.evaluate(this.values, "NAME", context).stimulsoft().toString();
                        let variableValue = ListExt.evaluate(this.values, "VALUE", context);
                        context.report.setVariable(variableName, variableValue);
                        return super.evaluate(context);
                    }
                    async evaluateAsync(context) {
                        let variableName = (await ListExt.evaluateAsync(this.values, "NAME", context)).stimulsoft().toString();
                        let variableValue = await ListExt.evaluate(this.values, "VALUE", context);
                        context.report.setVariable(variableName, variableValue);
                        return await super.evaluateAsync(context);
                    }
                }
                Variables.StiSetVariable = StiSetVariable;
            })(Variables = StiBlocks.Variables || (StiBlocks.Variables = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Variables;
            (function (Variables) {
                var StiReportParser = Stimulsoft.Report.Dashboard.StiReportParser;
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class StiSystemVariable extends IronBlock {
                    evaluate(context) {
                        let variableName = ListExt.get(this.fields, "NAME").toString();
                        return StiReportParser.parse("{" + variableName + "}", context.report.fetchPages().firstOrDefault());
                    }
                    async evaluateAsync(context) {
                        let variableName = ListExt.get(this.fields, "NAME").toString();
                        return await StiReportParser.parseAsync("{" + variableName + "}", context.report.fetchPages().firstOrDefault());
                    }
                }
                Variables.StiSystemVariable = StiSystemVariable;
            })(Variables = StiBlocks.Variables || (StiBlocks.Variables = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Objects;
            (function (Objects) {
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class StiGetCurrentValue extends IronBlock {
                    evaluate(context) {
                        if (context.eventArgs) {
                            return context.eventArgs["value"];
                        }
                        return super.evaluate(context);
                    }
                    async evaluateAsync(context) {
                        if (context.eventArgs) {
                            return context.eventArgs["value"];
                        }
                        return await super.evaluateAsync(context);
                    }
                }
                Objects.StiGetCurrentValue = StiGetCurrentValue;
            })(Objects = StiBlocks.Objects || (StiBlocks.Objects = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Report;
            (function (Report) {
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class StiThisComponent extends IronBlock {
                    evaluate(context) {
                        return context.sender;
                    }
                    async evaluateAsync(context) {
                        return context.sender;
                    }
                }
                Report.StiThisComponent = StiThisComponent;
            })(Report = StiBlocks.Report || (StiBlocks.Report = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Report;
            (function (Report) {
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class StiThisReport extends IronBlock {
                    evaluate(context) {
                        return context.report;
                    }
                    async evaluateAsync(context) {
                        return context.report;
                    }
                }
                Report.StiThisReport = StiThisReport;
            })(Report = StiBlocks.Report || (StiBlocks.Report = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Report;
            (function (Report) {
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class StiAllComponents extends IronBlock {
                    evaluate(context) {
                        return context.report.isRendered ?
                            context.report.getRenderedComponents().toList()
                            : context.report.getComponents().toList();
                    }
                    async evaluateAsync(context) {
                        return context.report.isRendered ?
                            context.report.getRenderedComponents().toList()
                            : context.report.getComponents().toList();
                    }
                }
                Report.StiAllComponents = StiAllComponents;
            })(Report = StiBlocks.Report || (StiBlocks.Report = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Report;
            (function (Report) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class StiAllComponentsFrom extends IronBlock {
                    evaluate(context) {
                        let name = ListExt.get(this.fields, "NAME").toString();
                        let compContainer = this.getComponent(context, name);
                        let page = compContainer;
                        if (page != null) {
                            return page.getComponents().toList();
                        }
                        let container = compContainer;
                        if (container != null) {
                            return container.getComponents().toList();
                        }
                        return null;
                    }
                    async evaluateAsync(context) {
                        let name = ListExt.get(this.fields, "NAME").toString();
                        let compContainer = this.getComponent(context, name);
                        let page = compContainer;
                        if (page != null) {
                            return page.getComponents().toList();
                        }
                        let container = compContainer;
                        if (container != null) {
                            return container.getComponents().toList();
                        }
                        return null;
                    }
                    getComponent(context, name) {
                        let compContainer = null;
                        if (context.report.isRendered) {
                            for (let page of context.report.renderedPages.toList()) {
                                if (page.name == name)
                                    return page;
                            }
                            compContainer = context.report.renderedPages.getComponentByName(name);
                        }
                        else {
                            compContainer = context.report.getComponentByName(name);
                        }
                        return compContainer;
                    }
                }
                Report.StiAllComponentsFrom = StiAllComponentsFrom;
            })(Report = StiBlocks.Report || (StiBlocks.Report = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Objects;
            (function (Objects) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class StiSetCurrentValue extends IronBlock {
                    evaluate(context) {
                        let args = context.eventArgs;
                        if (args != null) {
                            let value = ListExt.evaluate(this.values, "VALUE", context);
                            if (typeof args["value"] == "string" && value != null)
                                args["value"] = value.toString();
                            else
                                args["value"] = value;
                        }
                        return super.evaluate(context);
                    }
                    async evaluateAsync(context) {
                        let args = context.eventArgs;
                        if (args != null) {
                            let value = await ListExt.evaluateAsync(this.values, "VALUE", context);
                            if (typeof args["value"] == "string" && value != null)
                                args["value"] = value.toString();
                            else
                                args["value"] = value;
                        }
                        return await super.evaluateAsync(context);
                    }
                }
                Objects.StiSetCurrentValue = StiSetCurrentValue;
            })(Objects = StiBlocks.Objects || (StiBlocks.Objects = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Objects;
            (function (Objects) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class StiGetComponent extends IronBlock {
                    evaluate(context) {
                        let name = ListExt.get(this.fields, "NAME").toString();
                        return context.report.isRendered ? context.report.renderedPages.getComponentByName(name) : context.report.getComponentByName(name);
                    }
                    async evaluateAsync(context) {
                        let name = ListExt.get(this.fields, "NAME").toString();
                        return context.report.isRendered ? context.report.renderedPages.getComponentByName(name) : context.report.getComponentByName(name);
                    }
                }
                Objects.StiGetComponent = StiGetComponent;
            })(Objects = StiBlocks.Objects || (StiBlocks.Objects = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Objects;
            (function (Objects) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class StiGetComponentByName extends IronBlock {
                    evaluate(context) {
                        let name = ListExt.get(this.fields, "NAME");
                        return context.report.isRendered
                            ? context.report.renderedPages.getComponentByName(name)
                            : context.report.getComponentByName(name);
                    }
                    async evaluateAsync(context) {
                        let name = ListExt.get(this.fields, "NAME");
                        return context.report.isRendered
                            ? context.report.renderedPages.getComponentByName(name)
                            : context.report.getComponentByName(name);
                    }
                }
                Objects.StiGetComponentByName = StiGetComponentByName;
            })(Objects = StiBlocks.Objects || (StiBlocks.Objects = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Objects;
            (function (Objects) {
                var StiReportObjectStringConverter = Stimulsoft.System.Text.StiReportObjectStringConverter;
                var Color = Stimulsoft.System.Drawing.Color;
                var StiExpression = Stimulsoft.Report.Expressions.StiExpression;
                var StiConvert = Stimulsoft.Base.StiConvert;
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class StiSetPropertyOfObjectTo extends IronBlock {
                    evaluate(context) {
                        let obj = ListExt.evaluate(this.values, "OBJECT", context);
                        if (obj != null) {
                            let propertyName = ListExt.evaluate(this.values, "PROPERTY", context).stimulsoft().toString().stimulsoft().toLowerFirst();
                            let propertyNames = propertyName.split('.');
                            let value = ListExt.evaluate(this.values, "VALUE", context);
                            this.setValue(obj, propertyNames, value);
                        }
                        return super.evaluate(context);
                    }
                    async evaluateAsync(context) {
                        let obj = await ListExt.evaluate(this.values, "OBJECT", context);
                        if (obj != null) {
                            let propertyName = (await ListExt.evaluateAsync(this.values, "PROPERTY", context)).stimulsoft().toString().stimulsoft().toLowerFirst();
                            let propertyNames = propertyName.split('.');
                            let value = await ListExt.evaluateAsync(this.values, "VALUE", context);
                            this.setValue(obj, propertyNames, value);
                        }
                        return await super.evaluateAsync(context);
                    }
                    setValue(baseObj, properties, value) {
                        for (let index = 0; index < properties.length; index++) {
                            let propertyName = properties[index].stimulsoft().toLowerFirst();
                            propertyName = this.checkPropertyName(baseObj, propertyName);
                            if (index == properties.length - 1) {
                                let prop = baseObj[propertyName];
                                let propertyType = prop.stimulsoft().getType();
                                let exp = value == null ? value : value.stimulsoft().as(StiExpression);
                                if (exp != null)
                                    value = exp.value;
                                if (propertyType == StiExpression) {
                                    let textValue = StiConvert.changeType(value, String);
                                    value = new StiExpression();
                                    value.value = textValue;
                                }
                                else if (propertyType == Color && typeof value == "string") {
                                    value = StiReportObjectStringConverter.convertStringToColor(value);
                                }
                                let valueType = StiConvert.changeType(value, propertyType);
                                baseObj[propertyName] = valueType;
                            }
                            else {
                                baseObj = baseObj[propertyName];
                            }
                        }
                    }
                    checkPropertyName(obj, propertyName) {
                        let chartElement = obj;
                        if (chartElement != null && propertyName == "style")
                            return "customStyleName";
                        return propertyName;
                    }
                }
                Objects.StiSetPropertyOfObjectTo = StiSetPropertyOfObjectTo;
            })(Objects = StiBlocks.Objects || (StiBlocks.Objects = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Objects;
            (function (Objects) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class StiGetPropertyOfObject extends IronBlock {
                    evaluate(context) {
                        let obj = ListExt.evaluate(this.values, "OBJECT", context);
                        if (obj != null) {
                            let propertyName = ListExt.evaluate(this.values, "PROPERTY", context).stimulsoft().toString().stimulsoft().toLowerFirst();
                            let propertyNames = propertyName.split('.');
                            return this.getValue(obj, propertyNames);
                        }
                        return super.evaluate(context);
                    }
                    async evaluateAsync(context) {
                        let obj = await ListExt.evaluateAsync(this.values, "OBJECT", context);
                        if (obj != null) {
                            let propertyName = (await ListExt.evaluateAsync(this.values, "PROPERTY", context)).stimulsoft().toString().stimulsoft().toLowerFirst();
                            let propertyNames = propertyName.split('.');
                            return this.getValue(obj, propertyNames);
                        }
                        return await super.evaluateAsync(context);
                    }
                    getValue(baseObj, properties) {
                        let valueObj = null;
                        for (let index = 0; index < properties.length; index++) {
                            if (index == 0)
                                valueObj = baseObj;
                            let propertyName = properties[index].stimulsoft().toLowerFirst();
                            valueObj = valueObj[propertyName];
                        }
                        return valueObj;
                    }
                }
                Objects.StiGetPropertyOfObject = StiGetPropertyOfObject;
            })(Objects = StiBlocks.Objects || (StiBlocks.Objects = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Objects;
            (function (Objects) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class StiGetStyleByName extends IronBlock {
                    evaluate(context) {
                        let styleName = ListExt.evaluate(this.values, "VALUE", context).stimulsoft().toString();
                        return context.report.styles.getByName(styleName);
                    }
                    async evaluateAsync(context) {
                        let styleName = (await ListExt.evaluateAsync(this.values, "VALUE", context)).stimulsoft().toString();
                        return context.report.styles.getByName(styleName);
                    }
                }
                Objects.StiGetStyleByName = StiGetStyleByName;
            })(Objects = StiBlocks.Objects || (StiBlocks.Objects = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var System;
            (function (System) {
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class StiIsFirstPass extends IronBlock {
                    evaluate(context) {
                        return context.report.isFirstPass;
                    }
                    async evaluateAsync(context) {
                        return context.report.isFirstPass;
                    }
                }
                System.StiIsFirstPass = StiIsFirstPass;
            })(System = StiBlocks.System || (StiBlocks.System = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var System;
            (function (System) {
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class StiIsSecondPass extends IronBlock {
                    evaluate(context) {
                        return context.report.isSecondPass;
                    }
                    async evaluateAsync(context) {
                        return context.report.isSecondPass;
                    }
                }
                System.StiIsSecondPass = StiIsSecondPass;
            })(System = StiBlocks.System || (StiBlocks.System = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Functions;
            (function (Functions) {
                var StiConvert = Stimulsoft.Base.StiConvert;
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var List = Stimulsoft.System.Collections.List;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class StiFunctionRun extends IronBlock {
                    evaluate(context) {
                        let funct = Functions.StiBlocklyFunctionBlockKeyCache.getFunction(this.type);
                        let argValues = new List();
                        for (let index = 0; index < funct.argumentNames.length; index++) {
                            let arg = funct.argumentNames[index];
                            let type = funct.argumentTypes[index];
                            let value = ListExt.evaluate(this.values, arg, context);
                            let valueArg = StiConvert.changeType(value, type);
                            argValues.add(valueArg);
                        }
                        return funct.invoke(argValues);
                    }
                    async evaluateAsync(context) {
                        let funct = Functions.StiBlocklyFunctionBlockKeyCache.getFunction(this.type);
                        let argValues = new List();
                        for (let index = 0; index < funct.argumentNames.length; index++) {
                            let arg = funct.argumentNames[index];
                            let type = funct.argumentTypes[index];
                            let value = await ListExt.evaluateAsync(this.values, arg, context);
                            let valueArg = StiConvert.changeType(value, type);
                            argValues.add(valueArg);
                        }
                        return funct.invoke(argValues);
                    }
                }
                Functions.StiFunctionRun = StiFunctionRun;
            })(Functions = StiBlocks.Functions || (StiBlocks.Functions = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Functions;
            (function (Functions) {
                var StiFunctions = Stimulsoft.Report.Dictionary.StiFunctions;
                var Hashtable = Stimulsoft.System.Collections.Hashtable;
                class StiBlocklyFunctionBlockKeyCache {
                    static createKey(funct) {
                        let key = funct.functionName + ".";
                        if (funct.argumentNames != null) {
                            for (let index = 0; index < funct.argumentNames.length; index++) {
                                key += funct.argumentNames[index] + "." + funct.argumentTypes[index].name;
                            }
                        }
                        return key;
                    }
                    static getFunction(key) {
                        if (this.cache != null)
                            this.fillCache();
                        return this.cache.get(key);
                    }
                    static fillCache() {
                        this.cache = new Hashtable();
                        let functions = StiFunctions.getFunctions(false);
                        for (let func of functions) {
                            this.cache.add(this.createKey(func), func);
                        }
                    }
                    static getBlockKeyTable() {
                        if (this.cache == null)
                            this.fillCache();
                        return this.cache;
                    }
                }
                Functions.StiBlocklyFunctionBlockKeyCache = StiBlocklyFunctionBlockKeyCache;
            })(Functions = StiBlocks.Functions || (StiBlocks.Functions = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Process;
            (function (Process) {
                var ListExt = Stimulsoft.Blockly.Model.ListExt;
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class StiOpenLink extends IronBlock {
                    evaluate(context) {
                        var _a;
                        let value = ListExt.evaluate(this.values, "VALUE", context).stimulsoft().toString();
                        (_a = window.open(value, "_blank")) === null || _a === void 0 ? void 0 : _a.focus();
                        return super.evaluate(context);
                    }
                    async evaluateAsync(context) {
                        var _a;
                        let value = (await ListExt.evaluateAsync(this.values, "VALUE", context)).stimulsoft().toString();
                        (_a = window.open(value, "_blank")) === null || _a === void 0 ? void 0 : _a.focus();
                        return await super.evaluateAsync(context);
                    }
                }
                Process.StiOpenLink = StiOpenLink;
            })(Process = StiBlocks.Process || (StiBlocks.Process = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiBlocks;
        (function (StiBlocks) {
            var Process;
            (function (Process) {
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class StiRefreshViewer extends IronBlock {
                    evaluate(context) {
                        context.report.invokeRefreshViewer();
                        return super.evaluate(context);
                    }
                    async evaluateAsync(context) {
                        context.report.invokeRefreshViewer();
                        return await super.evaluateAsync(context);
                    }
                }
                Process.StiRefreshViewer = StiRefreshViewer;
            })(Process = StiBlocks.Process || (StiBlocks.Process = {}));
        })(StiBlocks = Blockly.StiBlocks || (Blockly.StiBlocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Model;
        (function (Model) {
            var ProceduresDef = Stimulsoft.Blockly.Blocks.Procedures.ProceduresDef;
            var List = Stimulsoft.System.Collections.List;
            class Workspace {
                implements() {
                    return [Model.IFragment];
                }
                constructor() {
                    this.blocks = new List();
                }
                evaluate(context) {
                    let returnValue = null;
                    let processedProcedureDefBlocks = new List();
                    for (let block of this.blocks) {
                        if (block.is(ProceduresDef)) {
                            block.evaluate(context);
                            processedProcedureDefBlocks.add(block);
                        }
                    }
                    for (let block of this.blocks) {
                        if (!processedProcedureDefBlocks.contains(block)) {
                            returnValue = block.evaluate(context);
                        }
                    }
                    return returnValue;
                }
                async evaluateAsync(context) {
                    let returnValue = null;
                    let processedProcedureDefBlocks = new List();
                    for (let block of this.blocks) {
                        if (block.is(ProceduresDef)) {
                            await block.evaluateAsync(context);
                            processedProcedureDefBlocks.add(block);
                        }
                    }
                    for (let block of this.blocks) {
                        if (!processedProcedureDefBlocks.contains(block)) {
                            returnValue = await block.evaluateAsync(context);
                        }
                    }
                    return returnValue;
                }
                evaluate2(report, sender, valueArg, args = null) {
                    let ctx = new Model.Context();
                    ctx.report = report;
                    ctx.sender = sender;
                    ctx.eventArgs = valueArg;
                    if (args != null) {
                        ctx.variables = args;
                    }
                    return this.evaluate(ctx);
                }
                async evaluateAsync2(report, sender, valueArg, args = null) {
                    let ctx = new Model.Context();
                    ctx.report = report;
                    ctx.sender = sender;
                    ctx.eventArgs = valueArg;
                    if (args != null) {
                        ctx.variables = args;
                    }
                    return await this.evaluateAsync(ctx);
                }
            }
            Model.Workspace = Workspace;
        })(Model = Blockly.Model || (Blockly.Model = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiString = Stimulsoft.System.StiString;
        var ColourBlend = Stimulsoft.Blockly.Blocks.Text.ColourBlend;
        var ColourRandom = Stimulsoft.Blockly.Blocks.Text.ColourRandom;
        var StiOpenLink = Stimulsoft.Blockly.StiBlocks.Process.StiOpenLink;
        var StiRefreshViewer = Stimulsoft.Blockly.StiBlocks.Process.StiRefreshViewer;
        var StiIsSecondPass = Stimulsoft.Blockly.StiBlocks.System.StiIsSecondPass;
        var StiIsFirstPass = Stimulsoft.Blockly.StiBlocks.System.StiIsFirstPass;
        var StiFunctionRun = Stimulsoft.Blockly.StiBlocks.Functions.StiFunctionRun;
        var StiBlocklyFunctionBlockKeyCache = Stimulsoft.Blockly.StiBlocks.Functions.StiBlocklyFunctionBlockKeyCache;
        var StiValueHelper = Stimulsoft.Base.Helpers.StiValueHelper;
        var XmlConverter = Stimulsoft.System.Xml.XmlConverter;
        var TextPrint = Stimulsoft.Blockly.Blocks.Text.TextPrint;
        var StiGetStyleByName = Stimulsoft.Blockly.StiBlocks.Objects.StiGetStyleByName;
        var StiGetPropertyOfObject = Stimulsoft.Blockly.StiBlocks.Objects.StiGetPropertyOfObject;
        var StiSetPropertyOfObjectTo = Stimulsoft.Blockly.StiBlocks.Objects.StiSetPropertyOfObjectTo;
        var StiGetComponentByName = Stimulsoft.Blockly.StiBlocks.Objects.StiGetComponentByName;
        var StiGetComponent = Stimulsoft.Blockly.StiBlocks.Objects.StiGetComponent;
        var StiSetCurrentValue = Stimulsoft.Blockly.StiBlocks.Objects.StiSetCurrentValue;
        var StiGetCurrentValue = Stimulsoft.Blockly.StiBlocks.Objects.StiGetCurrentValue;
        var StiAllComponentsFrom = Stimulsoft.Blockly.StiBlocks.Report.StiAllComponentsFrom;
        var StiAllComponents = Stimulsoft.Blockly.StiBlocks.Report.StiAllComponents;
        var StiThisComponent = Stimulsoft.Blockly.StiBlocks.Report.StiThisComponent;
        var StiThisReport = Stimulsoft.Blockly.StiBlocks.Report.StiThisReport;
        var StiSystemVariable = Stimulsoft.Blockly.StiBlocks.Variables.StiSystemVariable;
        var StiSetVariable = Stimulsoft.Blockly.StiBlocks.Variables.StiSetVariable;
        var StiGetVariableByName = Stimulsoft.Blockly.StiBlocks.Variables.StiGetVariableByName;
        var StiGetVariable = Stimulsoft.Blockly.StiBlocks.Variables.StiGetVariable;
        var StiSetDataSourceSqlCommand = Stimulsoft.Blockly.StiBlocks.Data.StiSetDataSourceSqlCommand;
        var StiDataSourceMethod = Stimulsoft.Blockly.StiBlocks.Data.StiDataSourceMethod;
        var StiDataSourceProperty = Stimulsoft.Blockly.StiBlocks.Data.StiDataSourceProperty;
        var StiGetDataSourceByName = Stimulsoft.Blockly.StiBlocks.Data.StiGetDataSourceByName;
        var StiGetDataSource = Stimulsoft.Blockly.StiBlocks.Data.StiGetDataSource;
        var StiDataSourceGetData = Stimulsoft.Blockly.StiBlocks.Data.StiDataSourceGetData;
        var StiNewPadding = Stimulsoft.Blockly.StiBlocks.Visuals.StiNewPadding;
        var StiNewCornerRadius = Stimulsoft.Blockly.StiBlocks.Visuals.StiNewCornerRadius;
        var StiNewMargin = Stimulsoft.Blockly.StiBlocks.Visuals.StiNewMargin;
        var StiNewPenStyle = Stimulsoft.Blockly.StiBlocks.Visuals.StiNewPenStyle;
        var StiNewBrush = Stimulsoft.Blockly.StiBlocks.Visuals.StiNewBrush;
        var StiNewBorder = Stimulsoft.Blockly.StiBlocks.Visuals.StiNewBorder;
        var StiNewGradientBrush = Stimulsoft.Blockly.StiBlocks.Visuals.StiNewGradientBrush;
        var StiNewSolidBrush = Stimulsoft.Blockly.StiBlocks.Visuals.StiNewSolidBrush;
        var StiNewFont = Stimulsoft.Blockly.StiBlocks.Visuals.StiNewFont;
        var ListsGetIndex = Stimulsoft.Blockly.Blocks.Lists.ListsGetIndex;
        var ListsIndexOf = Stimulsoft.Blockly.Blocks.Lists.ListsIndexOf;
        var StiColorHex = Stimulsoft.Blockly.StiBlocks.Visuals.StiColorHex;
        var StiColorARGB = Stimulsoft.Blockly.StiBlocks.Visuals.StiColorARGB;
        var ListsIsEmpty = Stimulsoft.Blockly.Blocks.Lists.ListsIsEmpty;
        var ListsRepeat = Stimulsoft.Blockly.Blocks.Lists.ListsRepeat;
        var ListsLength = Stimulsoft.Blockly.Blocks.Lists.ListsLength;
        var ListsCreateWith = Stimulsoft.Blockly.Blocks.Lists.ListsCreateWith;
        var ListsSplit = Stimulsoft.Blockly.Blocks.Lists.ListsSplit;
        var ProceduresIfReturn = Stimulsoft.Blockly.Blocks.Procedures.ProceduresIfReturn;
        var ProceduresCallReturn = Stimulsoft.Blockly.Blocks.Procedures.ProceduresCallReturn;
        var ProceduresCallNoReturn = Stimulsoft.Blockly.Blocks.Procedures.ProceduresCallNoReturn;
        var ProceduresDef = Stimulsoft.Blockly.Blocks.Procedures.ProceduresDef;
        var ColourRgb = Stimulsoft.Blockly.Blocks.Text.ColourRgb;
        var ColourPicker = Stimulsoft.Blockly.Blocks.Text.ColourPicker;
        var VariablesSet = Stimulsoft.Blockly.Blocks.Variables.VariablesSet;
        var VariablesGet = Stimulsoft.Blockly.Blocks.Variables.VariablesGet;
        var TextIndexOf = Stimulsoft.Blockly.Blocks.Text.TextIndexOf;
        var TextJoin = Stimulsoft.Blockly.Blocks.Text.TextJoin;
        var TextAppend = Stimulsoft.Blockly.Blocks.Text.TextAppend;
        var TextCaseChange = Stimulsoft.Blockly.Blocks.Text.TextCaseChange;
        var TextTrim = Stimulsoft.Blockly.Blocks.Text.TextTrim;
        var TextIsEmpty = Stimulsoft.Blockly.Blocks.Text.TextIsEmpty;
        var TextLength = Stimulsoft.Blockly.Blocks.Text.TextLength;
        var TextBlock = Stimulsoft.Blockly.Blocks.Text.TextBlock;
        var LogicNegate = Stimulsoft.Blockly.Blocks.Logic.LogicNegate;
        var LogicOperation = Stimulsoft.Blockly.Blocks.Logic.LogicOperation;
        var MathConstant = Stimulsoft.Blockly.Blocks.Maths.MathConstant;
        var MathNumberProperty = Stimulsoft.Blockly.Blocks.Maths.MathNumberProperty;
        var MathOnList = Stimulsoft.Blockly.Blocks.Maths.MathOnList;
        var MathConstrain = Stimulsoft.Blockly.Blocks.Maths.MathConstrain;
        var MathModulo = Stimulsoft.Blockly.Blocks.Maths.MathModulo;
        var MathRandomFloat = Stimulsoft.Blockly.Blocks.Maths.MathRandomFloat;
        var MathRandomInt = Stimulsoft.Blockly.Blocks.Maths.MathRandomInt;
        var MathRound = Stimulsoft.Blockly.Blocks.Maths.MathRound;
        var MathSingle = Stimulsoft.Blockly.Blocks.Maths.MathSingle;
        var MathNumber = Stimulsoft.Blockly.Blocks.Maths.MathNumber;
        var MathArithmetic = Stimulsoft.Blockly.Blocks.Maths.MathArithmetic;
        var LogicTernary = Stimulsoft.Blockly.Blocks.Logic.LogicTernary;
        var LogicNull = Stimulsoft.Blockly.Blocks.Logic.LogicNull;
        var LogicBoolean = Stimulsoft.Blockly.Blocks.Logic.LogicBoolean;
        var LogicCompare = Stimulsoft.Blockly.Blocks.Logic.LogicCompare;
        var ControlsForEach = Stimulsoft.Blockly.Blocks.Controls.ControlsForEach;
        var ControlsFlowStatement = Stimulsoft.Blockly.Blocks.Controls.ControlsFlowStatement;
        var ControlsWhileUntil = Stimulsoft.Blockly.Blocks.Controls.ControlsWhileUntil;
        var ControlsIf = Stimulsoft.Blockly.Blocks.Controls.ControlsIf;
        var ControlsRepeatExt = Stimulsoft.Blockly.Blocks.Controls.ControlsRepeatExt;
        var ControlsFor = Stimulsoft.Blockly.Blocks.Controls.ControlsFor;
        var Statement = Stimulsoft.Blockly.Model.Statement;
        var Value = Stimulsoft.Blockly.Model.Value;
        var Field = Stimulsoft.Blockly.Model.Field;
        var Dictionary = Stimulsoft.System.Collections.Dictionary;
        var Workspace = Stimulsoft.Blockly.Model.Workspace;
        var Mutation = Stimulsoft.Blockly.Model.Mutation;
        class Parser {
            constructor() {
                this.blocks = new Dictionary();
            }
            addBlock(c, type) {
                if (this.blocks.containsKey(type)) {
                    this.blocks.set(type, () => new c());
                    return this;
                }
                this.blocks.add(type, () => new c());
                return this;
            }
            parse(xml, preserveWhitespace = false) {
                let workspace = new Workspace();
                if (StiString.isNullOrEmpty(xml))
                    return workspace;
                let root = XmlConverter.toXml(xml);
                let xmlNode = root.getNodeByName("xml");
                if (xmlNode != null) {
                    for (let node of xmlNode.childNodes) {
                        if (node.localName == "block" || node.localName == "shadow") {
                            let block = this.parseBlock(node);
                            if (block != null)
                                workspace.blocks.add(block);
                        }
                    }
                }
                return workspace;
            }
            parseBlock(node) {
                var _a;
                if ((_a = StiValueHelper.tryToBool(node.getAttribute("disabled"))) !== null && _a !== void 0 ? _a : "false")
                    return null;
                let type = node.getAttribute("type");
                if (!this.blocks.containsKey(type))
                    throw new Error("block type not registered: '" + type + "'");
                let block = this.blocks.get(type)();
                block.type = type;
                block.id = node.getAttribute("id");
                for (let childNode of node.childNodes) {
                    switch (childNode.localName) {
                        case "mutation":
                            this.parseMutation(childNode, block);
                            break;
                        case "field":
                            this.parseField(childNode, block);
                            break;
                        case "value":
                            this.parseValue(childNode, block);
                            break;
                        case "statement":
                            this.parseStatement(childNode, block);
                            break;
                        case "comment":
                            break;
                        case "next":
                            {
                                let nextBlock = this.parseBlock(childNode.firstChild);
                                if (nextBlock != null)
                                    block.next = nextBlock;
                            }
                            break;
                        default:
                            throw new Error("unknown xml type: " + childNode.localName);
                    }
                }
                return block;
            }
            parseField(fieldNode, block) {
                let field = new Field();
                field.name = fieldNode.getAttribute("name");
                field.value = fieldNode.textContent;
                block.fields.add(field);
            }
            parseValue(valueNode, block) {
                var _a;
                let childNode = (_a = valueNode.getNodeByName("block")) !== null && _a !== void 0 ? _a : valueNode.getNodeByName("shadow");
                if (childNode == null)
                    return;
                let childBlock = this.parseBlock(childNode);
                let value = new Value();
                value.name = valueNode.getAttribute("name"),
                    value.block = childBlock;
                block.values.add(value);
            }
            parseStatement(statementNode, block) {
                var _a;
                let childNode = (_a = statementNode.getNodeByName("block")) !== null && _a !== void 0 ? _a : statementNode.getNodeByName("shadow");
                if (childNode == null)
                    return;
                let childBlock = this.parseBlock(childNode);
                let statement = new Statement();
                statement.name = statementNode.getAttribute("name"),
                    statement.block = childBlock;
                block.statements.add(statement);
            }
            parseMutation(mutationNode, block) {
                for (let attribute of mutationNode.attributes.toList()) {
                    block.mutations.add(new Mutation("mutation", attribute.name, attribute.value));
                }
                for (let node of mutationNode.childNodes) {
                    for (let attribute of node.attributes.toList()) {
                        block.mutations.add(new Mutation(node.nodeName, attribute.name, attribute.value));
                    }
                }
            }
            static addStandardBlocks(parser) {
                parser.addBlock(ControlsRepeatExt, "controls_repeat_ext");
                parser.addBlock(ControlsIf, "controls_if");
                parser.addBlock(ControlsWhileUntil, "controls_whileUntil");
                parser.addBlock(ControlsFlowStatement, "controls_flow_statements");
                parser.addBlock(ControlsForEach, "controls_forEach");
                parser.addBlock(ControlsFor, "controls_for");
                parser.addBlock(LogicCompare, "logic_compare");
                parser.addBlock(LogicBoolean, "logic_boolean");
                parser.addBlock(LogicNegate, "logic_negate");
                parser.addBlock(LogicOperation, "logic_operation");
                parser.addBlock(LogicNull, "logic_null");
                parser.addBlock(LogicTernary, "logic_ternary");
                parser.addBlock(MathArithmetic, "math_arithmetic");
                parser.addBlock(MathNumber, "math_number");
                parser.addBlock(MathSingle, "math_single");
                parser.addBlock(MathSingle, "math_trig");
                parser.addBlock(MathRound, "math_round");
                parser.addBlock(MathConstant, "math_constant");
                parser.addBlock(MathNumberProperty, "math_number_property");
                parser.addBlock(MathOnList, "math_on_list");
                parser.addBlock(MathConstrain, "math_constrain");
                parser.addBlock(MathModulo, "math_modulo");
                parser.addBlock(MathRandomFloat, "math_random_float");
                parser.addBlock(MathRandomInt, "math_random_int");
                parser.addBlock(TextBlock, "text");
                parser.addBlock(TextLength, "text_length");
                parser.addBlock(TextIsEmpty, "text_isEmpty");
                parser.addBlock(TextTrim, "text_trim");
                parser.addBlock(TextCaseChange, "text_changeCase");
                parser.addBlock(TextAppend, "text_append");
                parser.addBlock(TextJoin, "text_join");
                parser.addBlock(TextIndexOf, "text_indexOf");
                parser.addBlock(VariablesGet, "variables_get");
                parser.addBlock(VariablesSet, "variables_set");
                parser.addBlock(ColourPicker, "colour_picker");
                parser.addBlock(ColourRandom, "colour_random");
                parser.addBlock(ColourRgb, "colour_rgb");
                parser.addBlock(ColourBlend, "colour_blend");
                parser.addBlock(ProceduresDef, "procedures_defnoreturn");
                parser.addBlock(ProceduresDef, "procedures_defreturn");
                parser.addBlock(ProceduresCallNoReturn, "procedures_callnoreturn");
                parser.addBlock(ProceduresCallReturn, "procedures_callreturn");
                parser.addBlock(ProceduresIfReturn, "procedures_ifreturn");
                parser.addBlock(ListsSplit, "lists_split");
                parser.addBlock(ListsCreateWith, "lists_create_with");
                parser.addBlock(ListsLength, "lists_length");
                parser.addBlock(ListsRepeat, "lists_repeat");
                parser.addBlock(ListsIsEmpty, "lists_isEmpty");
                parser.addBlock(ListsGetIndex, "lists_getIndex");
                parser.addBlock(ListsIndexOf, "lists_indexOf");
                parser.addBlock(StiColorHex, "sti_color_hex");
                parser.addBlock(StiColorARGB, "sti_color_argb");
                parser.addBlock(StiNewFont, "sti_new_font");
                parser.addBlock(StiNewBrush, "sti_new_brush");
                parser.addBlock(StiNewSolidBrush, "sti_new_solid_brush");
                parser.addBlock(StiNewGradientBrush, "sti_new_gradient_brush");
                parser.addBlock(StiNewBorder, "sti_new_border");
                parser.addBlock(StiNewPenStyle, "sti_new_pen_style");
                parser.addBlock(StiNewMargin, "sti_new_margin");
                parser.addBlock(StiNewPadding, "sti_new_padding");
                parser.addBlock(StiNewCornerRadius, "sti_new_corner_radius");
                parser.addBlock(StiGetDataSource, "sti_get_data_source");
                parser.addBlock(StiGetDataSourceByName, "sti_get_data_source_by_name");
                parser.addBlock(StiDataSourceProperty, "sti_data_source_property");
                parser.addBlock(StiDataSourceMethod, "sti_data_source_method");
                parser.addBlock(StiSetDataSourceSqlCommand, "sti_set_data_source_sql_command");
                parser.addBlock(StiDataSourceGetData, "sti_data_source_get_data");
                parser.addBlock(StiGetVariable, "sti_get_variable");
                parser.addBlock(StiGetVariableByName, "sti_get_variable_by_name");
                parser.addBlock(StiSetVariable, "sti_set_variable");
                parser.addBlock(StiSystemVariable, "sti_system_variable");
                parser.addBlock(StiThisReport, "sti_this_report");
                parser.addBlock(StiThisComponent, "sti_this_component");
                parser.addBlock(StiAllComponents, "sti_all_components");
                parser.addBlock(StiAllComponentsFrom, "sti_all_components_from");
                parser.addBlock(StiGetCurrentValue, "sti_get_current_value");
                parser.addBlock(StiSetCurrentValue, "sti_set_current_value");
                parser.addBlock(StiGetComponentByName, "sti_get_component_by_name");
                parser.addBlock(StiOpenLink, "sti_open_link");
                parser.addBlock(StiRefreshViewer, "sti_refresh_viewer");
                parser.addBlock(StiIsFirstPass, "sti_is_first_pass");
                parser.addBlock(StiIsSecondPass, "sti_is_second_pass");
                parser.addBlock(StiGetComponent, "sti_get_component");
                parser.addBlock(StiSetPropertyOfObjectTo, "sti_set_property_of_object_to");
                parser.addBlock(StiGetPropertyOfObject, "sti_get_property_of_object");
                parser.addBlock(StiGetStyleByName, "sti_get_style_by_name");
                parser.addBlock(TextPrint, "sti_show_message");
                for (let key of StiBlocklyFunctionBlockKeyCache.getBlockKeyTable().keys) {
                    parser.addBlock(StiFunctionRun, key.stimulsoft().toString());
                }
                return parser;
            }
        }
        Blockly.Parser = Parser;
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        class StiBlocksParser {
            evaluate(report, sender, xml, args) {
                Blockly.Parser
                    .addStandardBlocks(new Blockly.Parser())
                    .parse(xml)
                    .evaluate2(report, sender, args);
            }
            async evaluateAsync(report, sender, xml, args) {
                await Blockly.Parser
                    .addStandardBlocks(new Blockly.Parser())
                    .parse(xml)
                    .evaluateAsync2(report, sender, args);
            }
        }
        Blockly.StiBlocksParser = StiBlocksParser;
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var StiExpression = Stimulsoft.Report.Expressions.StiExpression;
        var StiConvert = Stimulsoft.Base.StiConvert;
        class StiObjConverter {
            static toDouble(obj) {
                return StiConvert.changeType(StiObjConverter.getValue(obj), Number);
            }
            static getValue(obj) {
                if (obj != null && obj.stimulsoft().is(StiExpression))
                    return obj.value;
                return obj;
            }
        }
        Blockly.StiObjConverter = StiObjConverter;
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Blocks;
        (function (Blocks) {
            var Procedures;
            (function (Procedures) {
                var IronBlock = Stimulsoft.Blockly.Model.IronBlock;
                class ValueBlock extends IronBlock {
                    evaluate(context) {
                        return this.value.evaluate(context);
                    }
                    async evaluateAsync(context) {
                        return await this.value.evaluateAsync(context);
                    }
                    constructor(value) {
                        super();
                        this.value = value;
                    }
                }
                Procedures.ValueBlock = ValueBlock;
            })(Procedures = Blocks.Procedures || (Blocks.Procedures = {}));
        })(Blocks = Blockly.Blocks || (Blockly.Blocks = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Model;
        (function (Model) {
            Model.IFragment = new Stimulsoft.System.Interface("IFragment");
        })(Model = Blockly.Model || (Blockly.Model = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Model;
        (function (Model) {
            var Dictionary = Stimulsoft.System.Collections.Dictionary;
            class ProcedureContext extends Model.Context {
                constructor() {
                    super();
                    this.parameters = new Dictionary();
                }
            }
            Model.ProcedureContext = ProcedureContext;
        })(Model = Blockly.Model || (Blockly.Model = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Blockly;
    (function (Blockly) {
        var Resources;
        (function (Resources) {
            class StiBlocklyResourcesHelper {
                static loadResourceFile(fileName) {
                    switch (fileName) {
                        case "blocklyToolbox":
                            return Stimulsoft.ExternalLibrary.Blockly.blocklyToolbox;
                        case "blocklyToolboxCurrentValue":
                            return Stimulsoft.ExternalLibrary.Blockly.blocklyToolboxCurrentValue;
                        case "blocklyWorkspace":
                            return Stimulsoft.ExternalLibrary.Blockly.blocklyWorkspace;
                        case "blocklyBlocks":
                            return Stimulsoft.ExternalLibrary.Blockly.blocklyBlocks;
                    }
                    return "";
                }
            }
            Resources.StiBlocklyResourcesHelper = StiBlocklyResourcesHelper;
        })(Resources = Blockly.Resources || (Blockly.Resources = {}));
    })(Blockly = Stimulsoft.Blockly || (Stimulsoft.Blockly = {}));
})(Stimulsoft || (Stimulsoft = {}));
