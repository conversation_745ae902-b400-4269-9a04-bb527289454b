<div class="global-bg"></div>

<ion-header
  translucent="true"
  class="header"
>
  <ion-toolbar class="title-toolbar">
    <ion-buttons slot="start">
      <ion-back-button
        [defaultHref]="backUrl"
        text=""
        icon="chevron-back"
      ></ion-back-button>
    </ion-buttons>
    <ion-title>ワークフロー</ion-title>
    <ion-buttons slot="end">
      <ion-button
        class="icon-button mr-2"
        (click)="toggleSortOrder()"
      >
        <span>日付順</span>
        <ion-icon
          slot="end"
          src="assets/images/icon/arrow-down.svg"
          class="ml-1"
          *ngIf="order === 'ascend'"
        ></ion-icon>
        <ion-icon
          slot="end"
          src="assets/images/icon/arrow-up.svg"
          class="ml-1"
          *ngIf="order === 'descend'"
        ></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>

  <ion-toolbar class="segment-tab">
    <ion-segment
      value="approval"
      class="w-100"
    >
      <ion-segment-button
        value="new"
        class="segment-tab-button"
        routerLink="/tabs/workflow/new"
        [queryParams]="{ backUrl: backUrl }"
        routerDirection="root"
      >
        <ion-label>新規申請</ion-label>
      </ion-segment-button>
      <ion-segment-button
        value="application"
        class="segment-tab-button"
        routerLink="/tabs/workflow/application"
        [queryParams]="{ backUrl: backUrl }"
        routerDirection="root"
      >
        <ion-label>申請</ion-label>
      </ion-segment-button>
      <ion-segment-button
        value="approval"
        class="segment-tab-button"
        routerLink="/tabs/workflow/approval"
        [queryParams]="{ backUrl: backUrl }"
        routerDirection="root"
      >
        <ion-label>承認</ion-label>
      </ion-segment-button>
    </ion-segment>
  </ion-toolbar>
</ion-header>

<ion-content
  fullscreen="true"
  class="offset-bottom-0"
>
  <!--引き下げて更新-->
  <ion-refresher
    slot="fixed"
    (ionRefresh)="doRefresh($event)"
  >
    <ion-refresher-content
      pullingIcon="arrow-down-outline"
      pullingText="引き下げて更新"
    ></ion-refresher-content>
  </ion-refresher>
  <div class="approval-search-input-sty">
    <!-- 2024-11-14 ASCHK-35311 需求修改隐藏app-af-component-search-input-modal组件的检索 option-->
    <!-- 原始代码内容 setIsProgressNotYetWF="{{ progress !== 'not-yet' }}" -->
    <app-af-component-search-input-modal
      placeholderText="WF名で検索"
      valueText="{{ search }}"
      (inputValueOutPut)="valueChange($event)"
      (WFSearchOutPut)="toSearchCondition()"
    ></app-af-component-search-input-modal>
  </div>
  <div
    class="container layout-default approval-div-sty"
    [ngClass]="{ 'is-empty': isEmptyResult }"
  >
    <!-- segment -->
    <div
      class="segment-group group-workflow w-100 mb-3"
      [ngClass]="{ 'group-left-corner-fill': progress === 'not-yet', 'group-right-corner-fill': progress === 'all' }"
    >
      <a
        class="segment-item item-workflow"
        [ngClass]="{ 'is-active': progress === 'not-yet' }"
        (click)="openNotYet($event)"
        >未承認
        <span class="count-hint">{{ unaapveCount > 99 ? '99+' : unaapveCount }}</span>
      </a>
      <a
        class="segment-item item-workflow"
        [ngClass]="{ 'is-active': progress === 'done' }"
        (click)="openDone($event)"
        >承認済</a
      >
      <a
        class="segment-item item-workflow"
        [ngClass]="{ 'is-active': progress === 'all' }"
        (click)="openAll($event)"
        >すべて</a
      >
    </div>

    <div
      class="list-table"
      *ngIf="!isEmptyResult"
    >
      <ng-container *ngFor="let item of filterItems">
        <div
          class="list-table-item float-icon-right float-icon-next"
          (click)="toDetailPage(item)"
        >
          <table>
            <tr>
              <th
                scope="row"
                class="text-nowrap"
              >
                <span class="with-colon">WFID</span>
              </th>
              <td>
                <span class="table-content break-line">{{ item.processInstanceId }}</span>
              </td>
            </tr>
            <tr>
              <th
                scope="row"
                class="text-nowrap"
              >
                <span class="with-colon">WF名</span>
              </th>
              <td>
                <span class="table-content break-line">{{ item.wfName }}</span>
              </td>
            </tr>
            <tr>
              <th
                scope="row"
                class="text-nowrap"
              >
                <span class="with-colon">WF種類名</span>
              </th>
              <td>
                <span class="table-content break-line">{{ item.workflowName }}</span>
              </td>
            </tr>
            <tr>
              <th
                scope="row"
                class="text-nowrap"
              >
                <span class="with-colon">期限</span>
              </th>
              <td>
                <span class="table-content break-line">{{ item.deadlineDate }}</span>
              </td>
            </tr>
            <tr>
              <th
                scope="row"
                class="text-nowrap"
              >
                <span class="with-colon">ステータス</span>
              </th>
              <td>
                <span class="table-content break-line">{{ item.state }}</span>
              </td>
            </tr>
            <tr>
              <th
                scope="row"
                class="text-nowrap"
              >
                <span class="with-colon"> 作成日</span>
              </th>
              <td>
                <span class="table-content break-line">{{ item.createdDate }}</span>
              </td>
            </tr>
            <tr *ngIf="item.assignedBy">
              <th
                scope="row"
                class="text-nowrap"
              >
                <span class="with-colon">担当者</span>
              </th>
              <td>
                <span class="table-content break-line">{{ item.assignedBy }}</span>
              </td>
            </tr>
            <tr *ngIf="item.approvedBy">
              <th
                scope="row"
                class="text-nowrap"
              >
                <span class="with-colon">依頼者</span>
              </th>
              <td>
                <span class="table-content break-line">{{ item.approvedBy }}</span>
              </td>
            </tr>
          </table>
        </div>
      </ng-container>
    </div>

    <!--アイテムなし-->
    <div
      class="empty-message"
      *ngIf="isEmptyResult"
    >
      <p class="empty-message-title">一致する結果はありません</p>
    </div>
  </div>
</ion-content>
