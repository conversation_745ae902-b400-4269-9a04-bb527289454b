package jp.smfl.assetforce.plugins.barcodescanner;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import androidx.activity.result.ActivityResult;
import com.getcapacitor.JSObject;
import com.getcapacitor.PluginCall;
import com.getcapacitor.PluginMethod;
import com.getcapacitor.annotation.ActivityCallback;
import com.getcapacitor.annotation.CapacitorPlugin;
import com.getcapacitor.annotation.Permission;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.core.Single;
import io.reactivex.rxjava3.schedulers.Schedulers;
import jp.smfl.assetforce.barcodescanner.activities.workflow.NoClaimWorkflowScanActivity;
import jp.smfl.assetforce.plugins.ActivityRequestCode;
import jp.smfl.assetforce.plugins.barcodescanner.factory.NoClaimWorkflowResponseFactory;
import jp.smfl.assetforce.plugins.barcodescanner.payloads.ScanPluginPayload;
import jp.smfl.assetforce.utilities.BigDataTransportHelper;
import jp.smfl.assetforce.utilities.FileUtils;
import jp.smfl.assetforce.utilities.helpers.JsonHelpers;

import java.io.FileOutputStream;

@CapacitorPlugin(
  permissions={
    @Permission(strings = { Manifest.permission.CAMERA }, alias = "cameraList")
  }
)
public class NoClaimWorkFlowScanditPlugin extends BaseScanPlugin {

  public static ScanPluginPayload pluginPayLoad;

  @PluginMethod()
  public void toNoClaimWorkflowScan(PluginCall call) {
    requestPermission(call);
  }

  @Override
  protected void action(PluginCall savedCall) {
    String dataFileName = savedCall.getString("value");
    Single.fromCallable(() -> {
        // This will be executed in a background thread.
        return FileUtils.getStringFromFile(getContext(), dataFileName);
      })
      .map(jsonString -> {
        // This will be executed in a computation thread.
        return JsonHelpers.parseLargeJson(jsonString);
      })
      .subscribeOn(Schedulers.io())
      .observeOn(AndroidSchedulers.mainThread())
      .subscribe(payload -> {
        pluginPayLoad = payload;
        // This will be executed in the main thread.
        setupPlugin(payload);
        setupScanPlugin(payload);

        Intent intent = new Intent(getContext().getApplicationContext(), NoClaimWorkflowScanActivity.class);
        intent.putExtra("data", dataFileName);
        startActivityForResult(savedCall, intent, getRequestCode());
      }, throwable -> {
        // Handle error
        throwable.printStackTrace();
      });
  }

  @SuppressLint("CheckResult")
  @ActivityCallback
  protected void callBackNoClaimWorkFlowScanditPlugin(PluginCall call, ActivityResult result) {
    // try {
    //   pluginPayLoad = null;
    //   Intent data = result.getData();
    //   int resultCode = result.getResultCode();
    //   Intent mData = data;
    //   if (data==null) {
    //     mData = BigDataTransportHelper.get(resultCode);
    //   }
    //   NoClaimWorkflowResponseFactory responseFactory = new NoClaimWorkflowResponseFactory(mData, getRequestCode(), resultCode);
    //   JSObject ret = responseFactory.buildResponseData();
    //   call.resolve(ret);
    // } catch (Exception e) {
    //   e.printStackTrace();
    //   call.reject(null);
    // }

    try {
      pluginPayLoad = null;
      Intent data = result.getData();
      int resultCode = result.getResultCode();
      Intent mData = data;
      if (data == null) {
        mData = BigDataTransportHelper.get(resultCode);
      }

      // 使用 Observable 异步处理数据
      final Intent finalTempData = mData;

      Observable.fromCallable(() -> {
            NoClaimWorkflowResponseFactory responseFactory = new NoClaimWorkflowResponseFactory(finalTempData, getRequestCode(), resultCode);
            return responseFactory.buildResponseJsonData();
          })
          // 在 IO 线程执行操作
          .subscribeOn(Schedulers.io())
          // 在主线程观察结果
          .observeOn(AndroidSchedulers.mainThread()).subscribe(ret -> {
            // 处理成功的情况
            FileOutputStream value = getContext().openFileOutput(call.getString("value"), Context.MODE_PRIVATE);
            JsonHelpers.writeJsonToFile(ret, value);
            value.close();
            JSObject obj = new JSObject();
            JSObject dataObj = new JSObject();
            dataObj.put("type", ret.data.type);
            dataObj.put("file", call.getString("value"));
            obj.put("data", dataObj);
            call.resolve(obj);
          }, error -> {
            // 处理异常情况
            error.printStackTrace();
            call.reject(null);
          });
    } catch (Exception e) {
      e.printStackTrace();
      call.reject(null);
    }
  }

  @Override
  protected String getRequestCode() {
    return ActivityRequestCode.NoClaimWorkFlowScanditPlugin;
  }
}
