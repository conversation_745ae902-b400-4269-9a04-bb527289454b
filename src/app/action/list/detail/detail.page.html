<div class="global-bg"></div>

<ion-header
  translucent="true"
  class="header"
>
  <ion-toolbar
    class="title-toolbar"
    appTitleCenter
  >
    <ion-buttons slot="start">
      <ion-button
        class="back-button"
        (click)="toBack()"
      >
        <ion-icon
          slot="icon-only"
          name="chevron-back"
        ></ion-icon>
      </ion-button>
    </ion-buttons>
    <div
      id="titleContainer"
      class="title-container"
    >
      <div class="title">{{ pageTitle }}</div>
    </div>
    <ion-buttons slot="end">
      <ion-button
        (click)="onClickNextStep()"
        class="p-l-5 p-r-10"
        >次へ</ion-button
      >
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content fullscreen="true">
  <div class="container layout-default">
    <div class="list-container">
      <div
        class="list-item float-icon-right float-icon-locked float-icon-no-gutter"
        *ngIf="showAssetTypeName"
      >
        <div
          class="list-head mr-auto text-dark"
          style="width: 30px"
          >資産種類</div
        >
        <div class="list-body break-line text-right">{{ assetActionModel.assetTypeName }}</div>
      </div>
      <div
        class="list-item float-icon-right float-icon-locked float-icon-no-gutter"
        *ngIf="showActionName"
      >
        <div
          class="list-head mr-auto text-dark"
          style="width: 30px"
          >処理名</div
        >
        <div class="list-body break-line text-right">{{ assetActionModel.assetActionName }}</div>
      </div>
      <div
        class="list-item float-icon-right float-icon-next float-icon-no-gutter"
        (click)="clickInputItem()"
      >
        <div
          class="list-head mr-auto text-dark"
          style="width: 30px"
          >処理表示名</div
        >
        <div class="list-body break-line text-right">{{ assetActionModel.customerActionName }}</div>
      </div>
    </div>

    <app-af-customize-view
      #customizeViewAsset
      [dynamicItemDic]="dynamicItemDic"
      [itemData]="assetActionItemListDict"
      [backPath]="'/action/list/detail'"
    >
    </app-af-customize-view>

    <div
      class="list-title"
      (click)="toggleExpand()"
      [class.is-no-rounded-bottom]="isExpand"
      *ngIf="scanConditionList.length !== 0"
    >
      <span class="head"></span>
      <p class="title font-weight-bold text-truncate mr-auto">抽出条件</p>
      <ion-icon
        src="assets/images/icon/collapse.svg"
        *ngIf="isExpand"
      ></ion-icon>
      <ion-icon
        src="assets/images/icon/expand.svg"
        *ngIf="!isExpand"
      ></ion-icon>
    </div>
    <div
      class="list-container has-list-title is-no-rounded-top"
      *ngIf="isExpand && scanConditionList.length !== 0"
    >
      <div class="list-item">
        <table class="list-item-table">
          <thead>
            <tr>
              <th scope="col">項目名</th>
              <th scope="col">条件</th>
              <th scope="col">値</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let filter of scanConditionList">
              <td>{{ filter.itemDisplayName || filter.itemName }}</td>
              <td>{{ filter.method }}</td>
              <td>{{ filter.searchData }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <app-af-customize-view
      #customizeViewAppur
      [itemData]="appurtenancesInformationActionItemListDict"
      [backPath]="'/action/list/detail'"
    >
    </app-af-customize-view>
  </div>
</ion-content>
