/*
 * @Author: your name
 * @Date: 2020-06-08 12:01:19
 * @,@LastEditTime: ,: 2021-08-24 18:30:36
 * @,@LastEditors: ,: Please set LastEditors
 * @Description: 資産詳細画面
 * @FilePath: /asset-force/src/app/asset/detail/detail.page.ts
 */
/// <reference path="../schedule/schedule.page.d.ts" />
import {ChangeDetectorRef, Component, NgZone, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {ActivatedRoute, NavigationExtras, Router} from '@angular/router';
import {AlertController, NavController, Platform} from '@ionic/angular';
import {ToastrService} from 'ngx-toastr';
import {ScanService} from '../../services/scan/scan.service';
import {ActionService} from '../../services/action/action-service.service';
import {GetUserinfoService} from '../../httpUtils/mypage/getuserinfo/http-getuserinfo.service';
import {environment} from '../../../environments/environment';
import {AssetDetailService} from '../../services/assetdetail/asset-detail.service';
import {PrinterService} from '../../services/printer/printer.service';
import {LoadService} from '../../services/load/load.service';
import {AddAssetService} from '../../services/add/add-asset.service';
import {MypageGetuserinfoService} from '../../services/mypage/get-userInfo/mypage-getuserinfo.service';
import {ConstService} from '../../services/workflow/workflow-service/other/wf-const.service';
import {AssetTypeItem} from '../../models/assetTypeItem';
import {StorageUtils} from '../../utils/storage-utils';
import {AfCustomizeViewComponent} from '../../component/af-customize-view/af-customize-view.component';
import {GetAuthorityService} from '../../services/getAuthority/get-authority.service';
import _ from 'lodash';
import {Throttle} from 'src/app/utils/throttle.decorator';
import {Overlay} from 'src/app/live/overlay.component';
import {FilterTalkParameter} from 'src/app/models/talkFilter';

import { deepEqual, deleteFileFromS3, setPercentageUnit } from 'src/app/utils/utils';
import {Keyboard} from '@capacitor/keyboard';
import {getFormatDate, getFormatDateTime} from 'src/app/utils/time-utils';
import {RouterPage} from '../../base/router-page';
import {MemCache} from 'src/app/utils/MemCache';
import {NavigationOptions} from '@ionic/angular/common/providers/nav-controller';
import { HttpUtilsService } from 'src/app/utils/http-utils.service';

@Component({
  selector: 'app-detail',
  templateUrl: './detail.page.html',
  styleUrls: ['./detail.page.scss'],
})
export class DetailPage extends RouterPage implements OnInit, OnDestroy {
  @ViewChild(AfCustomizeViewComponent, { static: false }) customizeViewComponent: AfCustomizeViewComponent;
  // 更新日
  modifiedDate: string;
  // 資産内容
  assetText: string;
  // 資産ID
  assetId: string;
  isAction: boolean;
  isTabScan: boolean;
  // 資産TypeID
  assetTypeId: string;
  // 画面遷移のパラメータ
  queryParams: {};
  // 資産詳細活性設定
  activateDetail: boolean = true;
  // 履歴情報活性設定
  activateRelate: boolean = true;
  // 画面タイトル
  pageTitle: string;
  // レイアウト表示部品へ引渡す変数
  assetDict: { [key: string]: AssetTypeItem[] };
  relateDict: { [key: string]: AssetTypeItem[] } = {};
  relateListString: string = '';
  //currentAssetDict
  currentAssetDict: { [key: string]: AssetTypeItem[] };
  rawAssetTypeItemList: AssetTypeItem[];
  // 画面初期ロードフラグ
  firstLoad: boolean = true;
  //プリントフラグ
  printAvailable: boolean = false;
  //スキャン画面の遷移か
  fromScanPage: string;

  insideFromScanPage: string;
  //バーコード
  barcode: string;
  //assetDataBefore
  assetDataBefore: string;
  // 关联资产编辑前数据
  relationDataBefore: string;
  // 保存ボタン表示されるか
  showSaveBtn: boolean;
  // routerLink使うか
  useRouterlink = true;
  defaultSegement = 'detail';
  // 是否在跳转到履历情报页面的时候变更并保存了资产信息
  isNeedToReloadAssetInfo = false;
  //js
  jsString: string;
  extra;
  currentPageURL = '/tabs/asset/detail';
  params;
  registeredList;
  isHasSchedule = false;
  assetSchedulerInfo: AppointmentListByAssetId;
  scheduleExpandable = true;
  assetActionModel;
  registeredAssetList;
  isFromActionPage;
  isFromScanType;
  isFromNewAction;
  isFromMiddleButton = false;
  ocrType;
  actionListType;
  actionLabel;
  fromPage;
  isCreatedUser;
  private dynamicItemDic: Map<any, any>;
  private userInfo;
  private userCustomInfo;
  private appurtenancesInformationTypeInfo;
  private arrayAsset;
  liveTalkListUrl;
  isAllowTalk = false;
  private fromListPage;
  fontsize = 'normal';

  masterResult = {};
  isActionToPageDetail: boolean;
  /**
   * @description: constructor
   * @return:
   * @param route
   * @param assetDetailService
   * @param alertController
   * @param toastr
   * @param navController
   * @param printerService
   * @param loadService
   * @param authorityService
   * @param ref
   * @param addAssetService
   * @param scanService
   * @param actionService
   * @param getUserinfoService
   * @param getinfoService
   * @param router
   * @param platform
   * @param ngZone
   */
  constructor(
    route: ActivatedRoute,
    private assetDetailService: AssetDetailService,
    private alertController: AlertController,
    private toastr: ToastrService,
    navController: NavController,
    private printerService: PrinterService,
    public loadService: LoadService,
    private authorityService: GetAuthorityService,
    private ref: ChangeDetectorRef,
    private addAssetService: AddAssetService,
    private scanService: ScanService,
    private actionService: ActionService,
    private getUserinfoService: GetUserinfoService,
    private getinfoService: MypageGetuserinfoService,
    private router: Router,
    private platform: Platform,
    ngZone: NgZone,
    private httpUtilsService: HttpUtilsService,
  ) {
    super(navController, route, ngZone);
    this.mQueryParams = this.activatedRoute.snapshot.queryParams;

    this.appurtenancesInformationTypeInfo = undefined;
    // from deeplink
    // 针对循环多此跳转同一个资产信息时的特殊处理. (DeepLink 内部会增加标记位.)
    if (
      this.activatedRoute.snapshot.queryParams['sourcePage'] == 'deeplink' &&
      MemCache.instance.get(MemCache.KEY_ASSET_DETAIL_JUMP_HANDLE) === true
    ) {
      MemCache.instance.remove(MemCache.KEY_ASSET_DETAIL_JUMP_HANDLE);
      this.mQueryParams = this.activatedRoute.snapshot.queryParams;
      this.firstLoad = true;
      this.pageLoad();

      this.forceRefreshUI();
      return;
    }

    let isFromValidDeeplink = this.activatedRoute.snapshot.queryParams['sourcePage'] == 'deeplink';
    let isFromAssetList = this.activatedRoute.snapshot.queryParams['naviChangeCount'] == 1;

    if (isFromValidDeeplink || isFromAssetList) {
      this.mQueryParams = this.activatedRoute.snapshot.queryParams;
      this.firstLoad = true;
      this.pageLoad();
      this.forceRefreshUI();
      return;
    }

    if (
      this.activatedRoute.snapshot.queryParams.assetId &&
      this.mQueryParams.assetId != this.activatedRoute.snapshot.queryParams.assetId
    ) {
      this.mQueryParams = this.activatedRoute.snapshot.queryParams;
    }
    let params = this.mQueryParams;
    if (this.activatedRoute.snapshot.queryParams.needRefresh == false) {
      console.log('不需要刷新');
    } else if (params['fromScanPage'] !== 'relateAddPage') {
      if (Object.keys(params).length != 0 && params['fromScanPage'] !== 'assetScan') {
        console.log('detail.page.ts ionViewDidEnter');
        Keyboard.hide();
        if (
          params['fromScanPage'] &&
          params['fromScanPage'] !== 'undefined' &&
          params['fromScanPage'] != ConstService.ACTION_SCANED_LIST &&
          params['insideFromScanPage'] != ConstService.ACTION_SCANED_LIST
        ) {
          if (!this.isAction && !this.isTabScan && !this.isActionToPageDetail) {
            this.firstLoad = true;
            this.appurtenancesInformationTypeInfo = null;
          }
        }
        this.pageLoad();
      }
    }
  }

  /***
   * ionViewDidEnter
   ***/
  ionViewDidEnter() {
    // 重置默认值
    this.isNeedToReloadAssetInfo = false;
    this.appurtenancesInformationTypeInfo = undefined;
    // from deeplink
    // 针对循环多此跳转同一个资产信息时的特殊处理. (DeepLink 内部会增加标记位.)
    if (
      this.activatedRoute.snapshot.queryParams['sourcePage'] == 'deeplink' &&
      MemCache.instance.get(MemCache.KEY_ASSET_DETAIL_JUMP_HANDLE) === true
    ) {
      MemCache.instance.remove(MemCache.KEY_ASSET_DETAIL_JUMP_HANDLE);
      this.mQueryParams = this.activatedRoute.snapshot.queryParams;
      this.firstLoad = true;
      this.pageLoad();

      this.forceRefreshUI();
      return;
    }

    let isFromValidDeeplink = this.activatedRoute.snapshot.queryParams['sourcePage'] == 'deeplink';
    let isFromAssetList = this.activatedRoute.snapshot.queryParams['naviChangeCount'] == 1;
    let isNeedToReloadAssetInfo = this.activatedRoute.snapshot.queryParams['isNeedToReloadAssetInfo']

    if (isFromValidDeeplink || isFromAssetList || isNeedToReloadAssetInfo) {
      this.mQueryParams = this.activatedRoute.snapshot.queryParams;
      this.firstLoad = true;
      this.pageLoad();
      this.forceRefreshUI();
      return;
    }

    this.forceRefreshUI();
    if (this.activatedRoute.snapshot.queryParams.needRefresh == true){
      this.pageLoad();
    }
  }

  private forceRefreshUI() {
    setTimeout(() => {
      this.ref.markForCheck();
      this.ref.detectChanges();
      this.ngZone.run(() => {});
    }, 100);
  }

  protected mQueryParams;

  /**
   * @description: ngOnInit
   * @param {}
   * @return:
   */
  ngOnInit() {
    console.log(`【detail.page.ts - ngOnInit】函数执行`);
    this.getUserinfoService.httpGetUserInfo().then(async (data) => {
      console.log(data);
      this.userInfo = data.data;
    });
    this.actionService.actionGetCustomItem().then((data) => {
      console.log(data);
      if (data.code === 0) {
        this.userCustomInfo = data.layoutSettingList;
      }
    });
    this.mQueryParams = this.activatedRoute.snapshot.queryParams;
    this.appurtenancesInformationTypeInfo = undefined;
    let params = this.mQueryParams;
    this.params = params['params'];
    this.assetActionModel = params['assetActionModel'];
    this.registeredList = params['registeredList'];
    this.registeredAssetList = params['registeredAssetList'];
    this.isFromActionPage = params['isFromActionPage'];
    this.isCreatedUser = params['isCreatedUser'];
    this.fromListPage = params['fromListPage'];
    this.isFromNewAction = params['isFromNewAction'];
    this.isFromScanType = params['isFromScanType'];
    this.ocrType = params['ocrType'];
    this.actionListType = params['actionListType'];
    this.actionLabel = params['actionLabel'];
    this.isActionToPageDetail = params['isActionToPageDetail'];
    if (params['fromScanPage'] === 'assetScan') {
      console.log('detail.page.ts ngOnInit');
      Keyboard.hide();
      this.firstLoad = true;
      this.pageLoad();
    }

    StorageUtils.get(StorageUtils.KEY_FONT_SIZE).then((sizetype) => {
      this.fontsize = _.isEmpty(sizetype) ? 'normal' : sizetype;
    });
  }

  ngOnDestroy(): void {
    console.log('页面销毁：appurtenancesInformationTypeInfo状态清空');
    this.appurtenancesInformationTypeInfo = undefined;
  }

  /**
   * @description: pageLoad
   * @param {}
   * @return:
   */
  async pageLoad() {
    console.log(this.firstLoad);
    if (
      this.firstLoad ||
      this.mQueryParams['isFromRelationListPage'] ||
      this.activatedRoute.snapshot.queryParams[this._needRefresh]
    ) {
      let params = this.mQueryParams;
      this.isAction = params['isAction'];
      this.isTabScan = params['isTabScan'];
      this.assetId = params['assetId'];
      this.assetTypeId = params['assetTypeId'];
      this.assetText = params['assetText'];
      this.fromScanPage = params['fromScanPage'];
      // スケジュール
      this.initScheduler();

      this.isAllowTalk = await this.authorityService
        .hasFunctionPermission(GetAuthorityService.TALK_FUNCTION_PERMISSION_ID)
        .catch(() => false);

      //最新の資産テキスト取得
      let result = await this.assetDetailService.getFindById(this.assetId);
      this.arrayAsset = result.arrayAsset[0];
      this.assetText = result.arrayAsset[0].assetText;
      this.modifiedDate = result.arrayAsset[0].modifiedDate;
      console.log(this.assetText);
      this.insideFromScanPage = params['insideFromScanPage'] ?? params['fromScanPage'];
      this.barcode = params['barcode'];
      this.loadData(this.assetText);
      this.showSaveBtn = this.insideFromScanPage != 'wf';
      this.useRouterlink =
        this.insideFromScanPage != 'wf' && !ConstService.ASSET_DETAIL_ROUTER_DEFAULT.includes(this.insideFromScanPage);
      if (
        this.fromScanPage === 'relationList' ||
        this.fromScanPage == 'asset-list' ||
        this.fromScanPage == 'assetScan'
      ) {
        this.useRouterlink = false;
      }
      this.backUrl = params['backUrl'] ?? '/tabs/asset/detail';
      this.extra = params['extra'];
      this.isFromMiddleButton = params['isFromMiddleButton']; // 是否从中间的扫描按钮进来, 特殊返回.

      this.currentPageURL = this.router.url;
      if (!this.useRouterlink) {
        this.currentPageURL = '/asset/detail';
      }

      this.liveTalkListUrl =
        environment.liveBaseUrl +
        '/af-integrate/room-list/asset/' +
        this.assetId +
        '?accessToken=' +
        (await StorageUtils.get<string>(StorageUtils.KEY_TOKEN)) +
        `&zoneId=${await StorageUtils.get<string>(StorageUtils.KEY_ZONE_ID)}`;

      // overlayのfilter条件を更新
      const assetInfo = JSON.parse(this.assetText);
      const parameter: FilterTalkParameter = {
        assetTypeId: String(this.assetTypeId),
        assetIds: [String(this.assetId)],
        assetTypeName: String(assetInfo['資産種類']),
        assetName: String(assetInfo.assetName),
      };
      Overlay.filterTalkObservable.next(parameter);

      this.firstLoad = false;
    }
    // 画面遷移の共通パラメータ
    this.queryParams = {
      assetId: this.assetId,
      assetTypeId: this.assetTypeId,
      assetText: this.assetText,
      insideFromScanPage: this.insideFromScanPage,
      registeredAssetList: this.registeredAssetList,
      barcode: this.barcode,
      backUrl: this.backUrl,
      modifiedDate: this.modifiedDate,
      fromScanPage: this.fromPage ?? this.fromScanPage,
      fromListPage: this.fromListPage,
      assetActionModel: this.assetActionModel,
      isFromActionPage: this.isFromActionPage,
      isFromScanType: this.isFromScanType,
      isCreatedUser: this.isCreatedUser,
      extra: this.extra,
      isFromMiddleButton: this.isFromMiddleButton,
      actionLabel: this.actionLabel,
    };
    this.defaultSegement = 'detail';

    this.forceRefreshUI();
  }

  initScheduler() {
    this.assetDetailService
      .getGetAppointmentListByAssetId(this.assetId, this.assetTypeId)
      .then((tempAssetSchedule: AppointmentListByAssetId) => {
        const appointmentList: Array<AppointmentEntity> = tempAssetSchedule.appointmentList;
        const extraReservationItemCommonList: Array<AssetReservationItemCommonEntity> = _.isArray(
          tempAssetSchedule.extraReservationItemCommonList,
        )
          ? tempAssetSchedule.extraReservationItemCommonList
          : [];
        const reservationItemCommonList: Array<AssetReservationItemCommonEntity> = _.isArray(
          tempAssetSchedule.reservationItemCommonList,
        )
          ? tempAssetSchedule.reservationItemCommonList
          : [];
        // 动态取标题名 itemid均为固定不会轻易改变
        // itemId == "1"（名称） itemId == "2"（開始日） itemId == "3"（終了日）itemId == "6"（イベントタイプ）
        const headReservationName = reservationItemCommonList.find((common) => common.itemId == 1);
        const headStart = reservationItemCommonList.find((common) => common.itemId == 2);
        const headEnd = reservationItemCommonList.find((common) => common.itemId == 3);
        const headEventTypeName = extraReservationItemCommonList.find((common) => common.itemId == 6);
        for (const appointmentListElement of appointmentList) {
          appointmentListElement['extraCommonTextObj'] = JSON.parse(appointmentListElement.extraCommonText);
          appointmentListElement['reservationTextObj'] = JSON.parse(appointmentListElement.reservationText);
          appointmentListElement['headReservationName'] = _.isUndefined(headReservationName)
            ? ''
            : headReservationName.itemLabel;
          const isUnitDay = _.isEqual(appointmentListElement.unitDay, '1');
          const start = isUnitDay ? _.head(appointmentListElement.start.split(' ')) : appointmentListElement.start;
          const end = isUnitDay ? _.head(appointmentListElement.end.split(' ')) : appointmentListElement.end;
          appointmentListElement['headStart'] = _.isUndefined(headStart)
            ? ''
            : {
                headStart: headStart.itemLabel,
                textStart: start,
              };
          appointmentListElement['headEnd'] = _.isUndefined(headEnd)
            ? ''
            : { headEnd: headEnd.itemLabel, textEnd: end };
          appointmentListElement['headEventTypeName'] = _.isUndefined(headEventTypeName)
            ? ''
            : headEventTypeName.itemLabel;
        }
        const eventTypeList: Array<OptionEntity> = tempAssetSchedule.eventTypeList;
        for (const eventTypeListElement of eventTypeList) {
          for (const itemListElement of eventTypeListElement.itemList) {
            itemListElement['itemValObj'] = JSON.parse(itemListElement.itemVal);
          }
        }
        tempAssetSchedule.eventTypeList = eventTypeList;
        this.assetSchedulerInfo = tempAssetSchedule;
      });
  }

  /**
   * @description: 資産詳細編集前のデータ整理
   * @param {*}
   * @return {*}
   */
  async assetDataChangeBefore(): Promise<Object> {
    var tempAssetDict: { [key: string]: AssetTypeItem[] } = {};
    const assetDict = _.cloneDeep(this.assetDict);
    tempAssetDict = await this.customizeViewComponent.setInitChangeBeforeData(assetDict);
    const assetTypeListChangeBefore: AssetTypeItem[] = [];
    let map = {};
    for (let sectionName in tempAssetDict) {
      assetTypeListChangeBefore.push(...tempAssetDict[sectionName]);
    }
    assetTypeListChangeBefore.forEach((assetType) => {
      assetType.changeBeforeDefaultData = assetType.defaultData;
      // 因为画像加载之后会设置为true，提前设置为true
      if (assetType.itemType === 'homeImage' || assetType.itemType === 'image') {
        assetType.loaded = true;
      }
      if (assetType.itemType === 'homeImage' || assetType.itemType === 'image' || assetType.itemType === 'file') {
        if (assetType.defaultData == null || assetType.defaultData == undefined || assetType.defaultData == '') {
          assetType.defaultData = [];
          assetType.changeBeforeDefaultData = [];
        }
      }
      if (assetType.itemType === 'master') {
        if (assetType.defaultData == null || assetType.defaultData == undefined || assetType.defaultData == '') {
          assetType.defaultData = {};
          assetType.changeBeforeDefaultData = {};
        }
      }
      // 通貨/数値  の場合のチェック
      if (assetType.itemType === 'currency' || assetType.itemType === 'number') {
        if (assetType.defaultData == null || assetType.defaultData == '' || assetType.defaultData == undefined) {
          // This is intentional
        } else {
          const str: string = _.toString(assetType.defaultData);
          let a = String(str).split(',').join('');
          assetType.changeBeforeDefaultData = a;
        }
      }
    });

    assetTypeListChangeBefore.forEach((assetType) => {
      const arr: Array<Object> = assetType.changeBeforeDefaultData;
      map[assetType.itemName] = arr;
    });
    return map;
  }

  /**
   * @description: データロード
   * @param {}
   * @return:
   */
  async loadData(assetText: string) {
    this.checkPrintAvailable();
    this.pageTitle = JSON.parse(assetText)['資産種類'];
    //資産情報設定
    let result1 = await this.assetDetailService.getItemByAssetType(assetText, this.assetTypeId, this.assetId);
    this.rawAssetTypeItemList = result1['rawAssetTypeItemList'];
    this.assetDict = result1['secitonDic'];
    this.queryParams['assetDict'] = JSON.stringify(this.assetDict);

    // トークリスト有効場合、トークリストアイテムを追加
    if (this.isAllowTalk) {
      const itemDic: unknown = {
        sectionName: 'トーク',
        itemName: 'talkList',
        itemDisplayName: 'トーク',
        itemType: 'talkList',
        defaultData: '',
        inputFlag: '0',
        sysSetFlg: '0',
        sectionSort: Number.MAX_VALUE,
        sectionType: 'talkList',
        optionObject: { readOnly: '1' },
      };
      this.assetDict['トーク'] = [itemDic as AssetTypeItem];
    }
    let before = await this.assetDataChangeBefore();
    this.assetDataBefore = JSON.stringify(before);

    // カスタマイズロジックの取得
    const [result, result2] = await Promise.all([
      this.assetDetailService.getAssetTypeWithId(this.assetTypeId),
      this.assetDetailService.getAssetRelationList(this.assetId),
    ]);
    let workJs = '';
    // 如果js从后台取得后不为空那么才把commonJS拼接到js
    if (!_.isEmpty(result?.js)) {
      const cjs = _.isEmpty(result['commonJS']) ? '' : result['commonJS'];
      workJs = cjs + result.js;
    }
    // 関連情報設定
    this.jsString = workJs;
    var dict: { [key: string]: AssetTypeItem[] } = {};
    result2.assetRelationList.forEach((assetRelation) => {
      // データ設定
      if (Object.keys(dict).includes(String(assetRelation.assetTypeId))) {
        dict[assetRelation.assetTypeId].push(assetRelation);
      } else {
        var itemList: AssetTypeItem[] = [];
        itemList.push(assetRelation);
        dict[String(assetRelation.assetTypeId)] = itemList;
      }
    });
    this.relateDict = dict;
    this.relateListString = '';
    for (let item of Object.keys(this.relateDict)) {
      this.relateDict[item].forEach((data) => {
        this.relateListString = this.relateListString + data.assetId + ',';
      });
    }

    // 关联资产编辑前数据整理
    this.relationDataBefore = JSON.stringify(this.relateDict);
  }

  /**
   * ホーム画像の追加
   * @param resultDic
   * @param assetText
   */
  insertHomeImage(resultDic, assetText): any {
    var dic = resultDic;
    let keys = Object.keys(dic);
    var isHomeImageInsert: boolean;
    var itemNameKey: string;
    let assetTextObj = JSON.parse(assetText);
    if (assetTextObj['homeImage'] != undefined && assetTextObj['homeImage'] != null) {
      // 用于索引homeImage插入位置
      var lastKey = '';
      for (let assetTextObjKey in assetTextObj) {
        if (assetTextObjKey == 'homeImage') {
          break;
        } else {
          lastKey = assetTextObjKey;
        }
      }
      var insertPosition = -1;

      keys.forEach((key) => {
        insertPosition = -1;
        dic[key].forEach(function (assetItemDic) {
          if (assetItemDic['itemName'] === lastKey) {
            // 获取插入索引
            insertPosition = dic[key].indexOf(assetItemDic) + 1;
            isHomeImageInsert = true;
            itemNameKey = key;
          }
        });
      });
      let homeDisplayName;
      const homeImageOptionObject = { readOnly: '0' };
      this.rawAssetTypeItemList.forEach((item) => {
        if (item.itemName === 'homeImage') {
          homeDisplayName = item.itemDisplayName;
          const optionObj = JSON.parse(item.option);
          homeImageOptionObject['sectionPrivateGroups'] = optionObj['sectionPrivateGroups'];
          homeImageOptionObject['sectionPrivateEditGroups'] = optionObj['sectionPrivateEditGroups'];
        }
      });
      if (isHomeImageInsert) {
        var positionXVal = 0;
        var positionYVal = 0;
        if (dic[itemNameKey]) {
          if (insertPosition != 0) {
            const pervItemDic = dic[itemNameKey][insertPosition - 1];
            positionXVal = pervItemDic.positionX;
            positionYVal = pervItemDic.positionY;
          }
        }
        const itemDic = {
          sectionName: '基本情報',
          itemName: 'homeImage',
          itemDisplayName: homeDisplayName,
          itemType: 'homeImage',
          defaultData: '',
          inputFlag: '0',
          sysSetFlg: '0',
          positionX: positionXVal,
          positionY: positionYVal,
          optionObject: homeImageOptionObject,
        };
        if (assetTextObj.homeImage !== undefined) {
          if (assetTextObj.homeImage !== '') {
            itemDic['defaultData'] = assetTextObj.homeImage;
          }
        }
        dic[itemNameKey].splice(insertPosition, 0, itemDic);
      }
    }
    return dic;
  }

  /**
   * @description: キャンセルの確認ダイアログ
   * @param {*}
   * @return {*}
   */
  @Throttle(3000)
  async presentCancelConfirm() {
    if (this.fromScanPage === 'wf' || this.insideFromScanPage === 'wf') {
      this.goBackWithOutParams(this.backUrl);
      return;
    }

    const back = async () => {
      if (this.fromScanPage === 'scan' || this.insideFromScanPage === 'scan') {
        await new Promise((resolve) => setTimeout(resolve, 100));
        const params = this.activatedRoute.snapshot.queryParams;
        await this.navController.navigateBack('add/registered/list', { queryParams: { isAction: !!this.isAction } });
      } else if (this.fromScanPage === 'fromActionScan' || this.insideFromScanPage === 'fromActionScan') {
        this.fromPage = 'scanPage';
        let option: NavigationOptions = {
          queryParams: {
            fromPage: 'scanPage',
            assetActionModel: this.assetActionModel,
            registeredAssetList: this.registeredAssetList,
            isFromActionPage: this.isFromActionPage,
            isFromScanType: this.isFromScanType,
            isFromNewAction: this.isFromNewAction,
            params: this.params,
            actionListType: this.actionListType,
            actionLabel: this.actionLabel,
          },
        };
        this.navController.navigateBack(this.backUrl, option);
      } else if (
        ConstService.ASSET_DETAIL_ROUTER_DEFAULT.includes(this.fromScanPage) ||
        ConstService.ASSET_DETAIL_ROUTER_DEFAULT.includes(this.insideFromScanPage)
      ) {
        let navigationExtras: NavigationExtras = {
          queryParams: {
            params: this.params,
            actionRegisteredAssetList: this.registeredList,
          },
        };
        this.navController.navigateBack(this.backUrl, navigationExtras);
      } else {
        if (this.isFromMiddleButton) {
          if (this.extra['handled'] == true) {
            this.extra['handled'] = false;
          }
          let navigationExtras: NavigationExtras = {
            queryParams: {
              extra: this.extra,
            },
          };
          this.navController.navigateBack(this.backUrl, navigationExtras);
        } else if (this.mQueryParams['isFromRelationListPage'] || this.backUrl != '/tabs/asset/detail') {
          let navigationExtras: NavigationExtras = {
            queryParams: JSON.parse(this.extra ?? '{}'),
          };
          this.navController.navigateBack(this.backUrl, navigationExtras);
        } else {
          this.assetDict = {}; // 画面から離れると表示データをクリアする
          this.goRoot('/tabs/assets-list');
        }
      }
    };

    var dealData;
    dealData = await this.customizeViewComponent.dealData();
    if (dealData === undefined) {
      back();
      return;
    }
    let isCheckDataHasBeenEdited: boolean;
    try {
      isCheckDataHasBeenEdited = this.checkDataHasBeenEdited(this.assetDataBefore, dealData);
    } catch (error) {
      this.goBack();
      return;
    }
    if (!isCheckDataHasBeenEdited) {
      back();
    } else {
      const alert = await this.alertController.create({
        message: '保存ボタンを押さないとデータは保存されません。戻ってよろしいでしょうか？', // smartsheet 520
        buttons: [
          {
            text: 'いいえ',
            role: 'cancel',
            cssClass: 'font-weight-bold',
          },
          {
            text: 'はい',
            cssClass: 'text-danger font-weight-normal',
            handler: () => {
              back();
            },
          },
        ],
      });
      setTimeout(async () => {
        await alert.present();
      }, 2000);
    }
  }

  /**
   * 判断数据是否已经被编辑了，
   * @param assetDataBefore
   * @param dealData
   */
  checkDataHasBeenEdited(assetDataBefore, dealData) {
    if (_.isEmpty(assetDataBefore) && _.isEmpty(dealData)) {
      throw new Error('Basic data is empty');
    }
    const tempAssetDataBefore = this.deleteUnusedKey(JSON.parse(assetDataBefore));
    const tempDealData = this.deleteUnusedKey(dealData);
    return !deepEqual(tempAssetDataBefore, tempDealData);
  }

  genSortJson(jsonObj) {
    // 将对象转换为数组，并按键的升序排序
    const sortedArray = Object.entries(jsonObj).sort((a, b) => a[0].localeCompare(b[0]));
    // 创建新的对象，按排序后的顺序添加键值对
    const sortedObj = {};
    for (const [key, value] of sortedArray) {
      if (!(value instanceof Array) && value instanceof Object) {
        sortedObj[key] = this.genSortJson(value);
      } else {
        sortedObj[key] = value;
      }
    }
    return sortedObj;
  }

  /**
   * 判断资产是够被编辑的时候有一些项目并非资产信息，而是为了标记某种状态设的flag，影响到了判断资产是否被编辑
   * 所以要临时删除图片，file，sign里不用的turl以及那些不能编辑的系统时间项目（因为format可能影响判断）等
   * @param cloneForm
   */
  deleteUnusedKey(cloneForm) {
    const deleteNonEditableSystemObj = ['createdDate', 'updatedDate', 'passedTime'];
    const delObjAry = ['fileName', 'loaded', 'uploadDate', 'isHomeImage', 'turl'];
    for (var key in cloneForm) {
      // 删除系统自动登录的不能手动改变的key，防止format不一致导致check失败
      deleteNonEditableSystemObj.forEach((element) => {
        if (key == element) {
          delete cloneForm[element];
        }
      });
      if (!_.isEmpty(cloneForm[key])) {
        if (_.isArray(cloneForm[key])) {
          const valAry = [];
          cloneForm[key].forEach((element) => {
            valAry.push(_.omit(element, delObjAry));
          });
          cloneForm[key] = valAry;
        }
        if (_.isObject(cloneForm[key])) {
          cloneForm[key] = _.omit(cloneForm[key], delObjAry);
        }
      }
    }
    _.forEach(this.assetDict, (value) => {
      _.forEach(value, (value1, index) => {
        if (value1.itemType == 'calculate' || value1.itemType == 'appurInfoSummary') {
          delete cloneForm[value1.itemName];
        }
      });
    });
    return cloneForm;
  }

  /**
   * @description: saveData
   * @param {type}
   * @return:
   */
  @Throttle()
  async saveData() {
    const alert = await this.alertController.create({
      message: '保存しますか？',
      cssClass: 'alert-message-font16-bold',
      buttons: [
        {
          text: 'キャンセル',
          role: 'cancel',
          cssClass: 'font-weight-bold',
        },
        {
          text: 'OK',
          cssClass: 'text-danger font-weight-normal',
          handler: async () => {
            alert.dismiss();
            let relatesString = await this.customizeViewComponent.getRelateListString();
            // 保存時のカスタマイズロジックを取得

            let customsLogicResult = await this.addAssetService.getCustomsLogicJSSetting(this.assetTypeId);
            console.log('[blog] 保存ロジック　実行完了')
            let jsSaveAsset = '';
            // 如果js从后台取得后不为空那么才把commonJS拼接到js
            if (!_.isEmpty(customsLogicResult?.jsSaveAsset)) {
              const cjs = _.isEmpty(customsLogicResult['commonJS']) ? '' : customsLogicResult['commonJS'];
              jsSaveAsset = cjs + customsLogicResult.jsSaveAsset;
            }
            // カスタマイズロジック実行、（資産の新規作成、アップデート、取得）
            let result = await this.customizeViewComponent.runJavascriptTogetAssetOrUpdateAssetOrInsertAsset(
              jsSaveAsset,
              false,
              '',
            );
            console.log('result098 ++++======+++++++', result);
            if (result === false) {
              return;
            }

            let map = this.customizeViewComponent.checkAndGetData();
            if (!map) {
              return;
            }

            const dealMap = await this.customizeViewComponent.dealData();
            // 資産種類
            dealMap['資産種類'] = JSON.parse(this.assetText)['資産種類'];
            // 更新日を現在の日時に設定する ASCHK-26883 票号中 后台指出 这个字段可以去掉
            // dealMap['更新日'] = getFormatDateTime(new Date());
            let assetText = JSON.stringify(dealMap);
            let assetId = this.assetId;
            let assetTypeId = this.assetTypeId;
            let modifiedDate = this.modifiedDate;
            let barcode = JSON.parse(this.assetText)['identityCode'];
            this.assetDetailService
              .saveAssetdata(assetTypeId, assetId, assetText, modifiedDate, relatesString, barcode)
              .then(async (data) => {
                // 点击保存按钮后删除S3文件
                deleteFileFromS3(this.httpUtilsService);
                console.log('asset-force/src/app/asset/detail/detail.page.ts ===> 714', data);
                this.queryParams['assetDict'] = JSON.stringify(this.assetDict);
                this.toastr.success('アップデート完了しました', '', {
                  positionClass: 'toast-center-center',
                  timeOut: 500,
                });
                if (this.fromScanPage === 'assetScan') {
                  this.assetDataBefore = JSON.stringify(await this.customizeViewComponent.dealData()); //保存成功後、assetDataBefore更新必要
                  let result = await this.assetDetailService.getFindById(this.assetId);
                  this.modifiedDate = result.arrayAsset[0].modifiedDate;
                }

                const navigateBack = async (isFromAddRelate: boolean) => {
                  if (this.fromScanPage === 'relationList') {
                    let navigationExtras: NavigationExtras = {
                      queryParams: JSON.parse(this.extra ?? '{}'),
                    };
                    this.navController.navigateBack(this.backUrl, navigationExtras);
                  } else if (this.fromScanPage === 'asset-list' || this.insideFromScanPage === 'asset-list') {
                    this.navController.navigateBack('tabs/assets-list');
                  } else if (this.fromScanPage === 'scan' || this.insideFromScanPage === 'scan') {
                    await this.backToRegistedList(assetText, assetTypeId, assetId);
                  } else if (this.fromScanPage === 'assetScan' || this.insideFromScanPage === 'assetScan') {
                    // 履歴情報登録から戻る場合、履歴情報一覧画面へ遷移
                    if (isFromAddRelate) {
                      const backUrl = this.useRouterlink ? '/tabs/asset/relate' : '/asset/relate';
                      this.navController.navigateBack(backUrl, {
                        queryParams: this.queryParams,
                      });
                    } else {
                      console.warn(`this.isFromMiddleButton=${this.isFromMiddleButton} + backurl=${this.backUrl}`);
                      if (this.isFromMiddleButton) {
                        // this.navController.navigateBack(this.backUrl);
                        if (this.extra['handled'] == true) {
                          this.extra['handled'] = false;
                        }
                        let navigationExtras: NavigationExtras = {
                          queryParams: {
                            extra: this.extra,
                          },
                        };
                        this.navController.navigateBack(this.backUrl, navigationExtras);
                      } else {
                        // 保存を押してからそのままで画面遷移しない
                        this.goBack();
                      }
                    }
                    return;
                  } else if (this.fromScanPage === 'fromActionScan' || this.insideFromScanPage === 'fromActionScan') {
                    this.fromPage = 'scanPage';
                    let option: NavigationOptions = {
                      queryParams: {
                        fromPage: 'scanPage',
                        params: this.params,
                        assetActionModel: this.assetActionModel,
                        registeredAssetList: this.registeredAssetList,
                        isFromActionPage: this.isFromActionPage,
                        isFromNewAction: this.isFromNewAction,
                        isFromScanType: this.isFromScanType,
                        // ocrType: this.ocrType,
                        actionListType: this.actionListType,
                        actionLabel: this.actionLabel,
                      },
                    };
                    this.navController.navigateBack(this.backUrl, option);
                  } else if (
                    ConstService.ASSET_DETAIL_ROUTER_DEFAULT.includes(this.fromScanPage) ||
                    ConstService.ASSET_DETAIL_ROUTER_DEFAULT.includes(this.insideFromScanPage)
                  ) {
                    const navigationExtras: NavigationExtras = {
                      queryParams: {
                        params: this.params,
                        actionRegisteredAssetList: this.registeredList,
                      },
                    };
                    this.navController.navigateBack(this.backUrl, navigationExtras);
                  } else {
                    this.navController.navigateBack('tabs/assets-list');
                  }
                };

                if (this.appurtenancesInformationTypeInfo) {
                  await this.goRelateAddPage(assetText, assetTypeId, assetId, dealMap, navigateBack);
                  return;
                } else {
                  await navigateBack(false);
                }
              });
          },
        },
      ],
    });
    await alert.present();
  }

  private async goRelateAddPage(
    assetText: string,
    assetTypeId: string,
    assetId: string,
    dealMap: Object,
    navigateBack: (boolean) => Promise<void>,
  ) {
    const jsonObj = JSON.parse(this.appurtenancesInformationTypeInfo);
    const element = jsonObj[0];
    if (element !== null && element !== undefined) {
      const appurtenancesInformationTypeId = element['appurtenancesInformationTypeId'];
      const appurtenancesInformationTypeName = element['appurtenancesInformationTypeName'];
      const dataList = element['dataList'];
      const appurtenancesInformationMobileLayoutList = await this.getMobileColumnList(appurtenancesInformationTypeId);
      let tempLayoutSettingList: AssetTypeItem[] = [];
      const userInfo = await this.getinfoService.getUserInfo();
      let user = userInfo.data;
      let firstName = user.firstName;
      let lastName = user.lastName;
      //データ設定
      for (const mobileLayout of appurtenancesInformationMobileLayoutList) {
        // mobileLayout.defaultData = '';
        if (mobileLayout.itemName === 'ユーザー名') {
          mobileLayout.defaultData = lastName + ' ' + firstName;
        }
        if (mobileLayout.itemName === 'ロケーション') {
          mobileLayout.defaultData = await StorageUtils.get(StorageUtils.KEY_LOCATION);
          mobileLayout.itemType = 'textarea';
        }
        mobileLayout.optionObject = JSON.parse(mobileLayout.option);
        tempLayoutSettingList.push(mobileLayout);
      }

      const params = this.activatedRoute.snapshot.queryParams;
      let navigationExtras: NavigationExtras = {
        queryParams: {
          layoutSettingList: tempLayoutSettingList,
          assetId: this.assetId,
          appurtenancesInformationTypeId: appurtenancesInformationTypeId,
          isEditPermissions: '1',
          appurtenancesInformationTypeName: appurtenancesInformationTypeName,
          fromPage: 'detail',
          fromListPage: params['fromListPage'],
          jsString: this.jsString,
          dataInfo: dataList,
          assetDict: this.assetDict,
          isAction: this.isAction,
          isDetail: params['isDetail'],
          callback: async () => {
            await navigateBack(true);
          },
          headerText: `${dealMap['assetName']}の資産更新が完了しました。あわせて、以下の履歴情報も登録します。内容を確認、編集の上「保存」ボタンをタップしてください`,
        },
      };
      this.navController.navigateForward('/asset/relate/list/add', navigationExtras);
    }
  }

  private async backToRegistedList(assetText: string, assetTypeId: string, assetId: string) {
    this.assetDetailService.getItemByAssetType(assetText, assetTypeId, assetId).then((result) => {
      this.assetDict = result['secitonDic'];
      // 画面遷移の共通パラメータ
      this.queryParams = {
        assetId: this.assetId,
        assetTypeId: this.assetTypeId,
        assetText: assetText,
        fromScanPage: this.fromScanPage ?? this.insideFromScanPage,
      };
    });

    await this.assetDetailService.updateLocalScanedAssetData(this.assetId);

    // const params = this.activatedRoute.snapshot.queryParams;
    if (this.isAction) {
      this.navController.navigateBack('add/registered/list', { queryParams: { isAction: true } });
    } else {
      if (this.fromListPage) {
        this.navController.navigateBack(this.fromListPage);
      } else {
        this.navController.navigateBack('/add/new/list');
      }
    }
  }

  async getMobileColumnList(appurtenancesInformationTypeId: number): Promise<AssetTypeItem[]> {
    let mobileColumnList: AssetTypeItem[] = [];

    // LayoutSetting
    let resultLayoutSetting =
      await this.assetDetailService.getAppurtenancesInfoLayoutSetting(appurtenancesInformationTypeId);
    let layoutSetting = resultLayoutSetting.layoutSettingList;
    let workJs = '';
    // 如果js从后台取得后不为空那么才把commonJS拼接到js
    if (!_.isEmpty(resultLayoutSetting?.js)) {
      const cjs = _.isEmpty(resultLayoutSetting['commonJS']) ? '' : resultLayoutSetting['commonJS'];
      workJs = cjs + resultLayoutSetting.js;
    }
    this.jsString = workJs;
    // mobileColumnList
    let resultMobileSetting =
      await this.assetDetailService.getAppurtenancesInfoMobileSetting(appurtenancesInformationTypeId);
    let tempMobileColumnList =
      resultMobileSetting.appurtenancesInformationType.appurtenancesInformationMobileColumnList;

    tempMobileColumnList.forEach((mobileColumn) => {
      let item = layoutSetting.find((item) => item.itemName === mobileColumn.itemName);
      mobileColumnList.push(item);
    });
    return mobileColumnList;
  }

  /**
   * @description: print Label
   * @param {type}
   * @return:
   */
  @Throttle()
  async printLabel() {
    let tenantId = await this.getTenantID();
    let apiHost = await StorageUtils.get(StorageUtils.KEY_API_HOST);
    let data = {
      value: this.assetDict,
      assetId: this.assetId,
      keys: [this.assetTypeId],
      tenantId: tenantId,
      apiHost: apiHost,
    };
    this.printerService.printLable(data).then((result) => {
      // This is intentional
    });
  }

  /**
   * 資産印刷可能確認
   */
  async checkPrintAvailable() {
    let tenantId = await this.getTenantID();
    let assetsIDS = this.assetTypeId;

    this.printerService.checkPrintAvailable([assetsIDS], tenantId).then((result) => {
      this.printAvailable = result;
    });
  }

  /**
   * @description: getTenantID
   * @param {*}
   * @return {*}
   */
  async getTenantID() {
    let res = await StorageUtils.get(StorageUtils.KEY_TENANT_ID);
    return res;
  }

  /***
   * ionViewWillEnter
   ***/
  ionViewWillEnter() {
    this.defaultSegement = 'detail';
    Keyboard.hide();
  }

  /**
   * @description: getCurrentDate
   * @param {*}
   * @return {*}
   */
  getCurrentDate() {
    this.currentAssetDict = this.customizeViewComponent.getCurrentData();
  }

  /**
   * detail
   */
  @Throttle()
  gotoOutSide() {
    console.log('detail=====relate======>');
    this.activateRelate = false;
    setTimeout(() => {
      this.activateDetail = true;
      this.activateRelate = true;
    }, 1000);
    if (!this.showSaveBtn) {
      this.jumpToHistory();
      return;
    }
    let callBack = () => {
      this.jumpToHistory();
      if (!this.useRouterlink) {
        this.reloadAssetInfo();
      }
    };
    if (this.fromScanPage === 'relationList' || this.fromScanPage == 'asset-list' || !this.useRouterlink) {
      this.doCheckEditedProcess(callBack);
    }
  }

  /**
   * 跳转到履历情报
   */
  private jumpToHistory() {
    this.queryParams['isNeedToReloadAssetInfo'] = this.isNeedToReloadAssetInfo;
    if (this.fromScanPage === 'relationList' || this.fromScanPage == 'asset-list') {
      this.goForward({
        url: this.getTabsPrefixUrl('/asset/relate'),
        params: this.queryParams,
        unique: true,
        autoConvert: true,
        sameLevel: true,
        attr: {
          animated: false,
        },
      });
    } else if (!this.useRouterlink) {
      this.goForward({
        url: this.getTabsPrefixUrl('/asset/relate'),
        params: this.queryParams,
        unique: true,
        sameLevel: true,
        attr: {
          animated: false,
        },
      });
    }
    setTimeout(() => {
      this.defaultSegement = 'detail';
    }, 1000);
  }

  /**
   * relate
   */
  @Throttle()
  async gotoInSide() {
    this.activateDetail = false;
    setTimeout(() => {
      this.activateRelate = true;
      this.activateDetail = true;
    }, 1000);
    var callBack = async () => {
      if (!this.useRouterlink) {
        //最新の資産テキスト取得
        await this.reloadAssetInfo();
        return;
      }
    };
    if (
      this.fromScanPage === 'relationList' ||
      this.fromScanPage == 'asset-list' ||
      (!this.useRouterlink && this.showSaveBtn)
    ) {
      this.doCheckEditedProcess(callBack);
    }
  }

  private async reloadAssetInfo() {
    let result = await this.assetDetailService.getFindById(this.assetId);
    if (result && Array.isArray(result.arrayAsset) && result.arrayAsset.length > 0) {
      this.arrayAsset = result.arrayAsset[0];
      this.assetText = result.arrayAsset[0].assetText;
      this.modifiedDate = result.arrayAsset[0].modifiedDate;
      this.firstLoad = true;
      await this.loadData(this.assetText);
      this.ref.detectChanges();
    }
  }

  /**
   * 检查是否编辑过内容，如果编辑过，弹出提示框
   * @param callBack
   * @returns {Promise<void>}
   */
  private async doCheckEditedProcess(callBack: () => void) {
    var dealData;
    dealData = await this.customizeViewComponent.dealData();
    if (dealData === undefined) {
      callBack();
      return;
    }
    let isCheckDataHasBeenEdited: boolean;
    try {
      // 检查资产信息变更
      isCheckDataHasBeenEdited = this.checkDataHasBeenEdited(this.assetDataBefore, dealData);
      if (!isCheckDataHasBeenEdited) {
        // 检查关联资产变更
        isCheckDataHasBeenEdited = !_.isEqual(
          this.genSortJson(JSON.parse(this.relationDataBefore)),
          this.genSortJson(this.relateDict),
        );
      }
    } catch (error) {
      console.error(error);
      callBack();
      return;
    }
    if (!isCheckDataHasBeenEdited) {
      callBack();
    } else {
      const alert = await this.alertController.create({
        message: 'これまでの編集内容を保存しますか？',
        buttons: [
          {
            text: 'いいえ',
            cssClass: 'font-weight-bold',
            handler: () => {
              callBack();
            },
          },
          {
            text: 'はい',
            cssClass: 'text-danger font-weight-normal',
            handler: () => {
              this.saveChangedData(callBack);
              this.isNeedToReloadAssetInfo = true
            },
          },
        ],
      });
      setTimeout(async () => {
        await alert.present();
      }, 2000);
    }
  }

  // 是否含有schedule
  hasSchedule() {
    return !_.isEmpty(this.assetSchedulerInfo) && !_.isEmpty(this.assetSchedulerInfo.appointmentList);
  }

  // 点击单个scheduleItem
  @Throttle()
  clickScheduleItem(appointmentItem: AppointmentEntity, index: number) {
    console.log('clickScheduleItem appointmentItem ==>', appointmentItem);
    // deep copy
    const navigationExtras: NavigationExtras = {
      queryParams: {
        appointItem: appointmentItem,
        scheduleData: this.assetSchedulerInfo,
        callback: async (info) => {
          this.assetSchedulerInfo = undefined;
          this.initScheduler(); // 最新のmodifiedDateを取得するために、APIを呼び出す必要になる
          console.log('clickScheduleItem => callback => ', info);
          this.ref.detectChanges();
        },
      },
    };
    this.navController.navigateForward('asset/schedule', navigationExtras);
  }

  @Throttle()
  toggleScheduleExpand() {
    this.scheduleExpandable = !this.scheduleExpandable;
  }

  @Throttle(1500)
  async qrcodeScan() {
    console.log('blog-qrcodeScan');
    const data = await this.scanService.scanQRCode({ customLogic: 1, isQRCodeScanScreenTextView: 1 });
    const ada = await this.doActionDataAutoInput(data);
    if (_.isEmpty(ada)) {
      return;
    }
    this.showScanActionQrCodeSucToast();
    this.assetDict = ada;
  }

  // data: 处理设定的值
  private async doActionDataAutoInput(data) {
    let assetAction = data['assetAction'];
    if (_.isEmpty(assetAction)) {
      return;
    }
    assetAction = JSON.parse(data['assetAction']);
    const parse = JSON.parse(assetAction.assetActionItem);
    if (String(this.assetTypeId) !== String(assetAction['assetTypeId'])) {
      // 如果资产种类不一致提示alert
      this.showScanActionQrCodeFaiToast();
      return;
    }
    this.appurtenancesInformationTypeInfo = assetAction.appurtenancesInformationTypeInfo;
    // assetDict ： 资产信息
    if (_.isEmpty(this.assetDict)) {
      return;
    }
    const ad = _.cloneDeep(this.assetDict);
    for (const sectionName of Object.keys(ad)) {
      if (_.isEmpty(ad[sectionName])) {
        continue;
      }
      for (const assetDictElement of ad[sectionName]) {
        const foundItem = parse.find((item) => item.itemName === assetDictElement.itemName);
        if (_.isEmpty(foundItem)) {
          continue;
        }
        if (foundItem.itemValue === 'valueBlank' || foundItem.itemValue === 'currentUserInput') {
          if (foundItem.itemType === 'master') {
            if (assetDictElement.displayList) {
              if (assetDictElement.defaultData.display) {
                for (let key of Object.keys(assetDictElement.defaultData.display)) {
                  const find = assetDictElement.displayList.find((item) => String(item.itemId) === String(key));
                  if (find) {
                    find.itemValue = '';
                  }
                }
              } else {
                for (let tmpItem of assetDictElement.optionObject.masterDisplayItems) {
                  const find = assetDictElement.displayList.find(
                    (item) => String(item.itemId) === String(tmpItem['itemId']),
                  );
                  if (find) {
                    find.itemValue = '';
                  }
                }
              }
            }
            assetDictElement.defaultData = '';
          } else {
            // 处理输入空白
            assetDictElement.defaultData = '';
          }
        } else if (
          foundItem &&
          typeof foundItem.itemValue === 'string' &&
          foundItem.itemValue.startsWith('assetItemName:')
        ) {
          // 判断是从其他地方取值
          let itemId = String(foundItem.itemValue).split(':').pop();
          assetDictElement.defaultData = '';
          for (let key of Object.keys(ad)) {
            const findAssetItem = ad[key].find((item) => String(item.itemId) === String(itemId));
            if (findAssetItem !== null && findAssetItem !== undefined) {
              if (assetDictElement.itemType == 'master') {
                if (assetDictElement.displayList) {
                  let maseterId = findAssetItem.defaultData.masterId;
                  let display = {};
                  for (let tmpItem of assetDictElement.optionObject.masterDisplayItems) {
                    let masterDefaultValue = await this.getMasterDefaultValue(
                      assetDictElement.optionObject.masterTypeId,
                      maseterId,
                      tmpItem['itemName'],
                    );
                    if (!_.isEmpty(masterDefaultValue)) {
                      display[tmpItem['itemId']] = masterDefaultValue;
                      const find = assetDictElement.displayList.find(
                        (item) => String(item.itemId) === String(tmpItem['itemId']),
                      );
                      if (find) {
                        find.itemValue = masterDefaultValue;
                        const findP = assetDictElement.optionObject.masterDisplayItems.find(
                          (itemP) => String(itemP['itemId']) === String(find.itemId),
                        );
                        if (!_.isEmpty(findP['option'])) {
                          const option = JSON.parse(findP['option']);
                          if (option.checkboxMultiFlg == '0' && findP['itemType'] == 'checkbox') {
                            find.itemValue = masterDefaultValue === '1' ? 'あり' : 'なし';
                          }
                          find['type'] = 'number';
                          setPercentageUnit(find, find.itemValue, findP['option']);
                        }
                      }
                    }
                  }
                  assetDictElement.defaultData = {
                    masterId: maseterId,
                    display,
                  };
                }
              } else {
                assetDictElement.defaultData = findAssetItem.defaultData;
                break;
              }
            }
          }
        } else if (foundItem.itemType === 'number' || foundItem.itemType === 'currency') {
          foundItem.itemValue = _.toString(foundItem.itemValue).replace(/,/g, '');
          assetDictElement.defaultData = foundItem.itemValue;
          setPercentageUnit(assetDictElement, assetDictElement.defaultData, assetDictElement.optionObject);
        } else if (foundItem.itemType === 'input') {
          if (foundItem.itemValue === 'currentUserName') {
            assetDictElement.defaultData = `${this.userInfo.lastName} ${this.userInfo.firstName}`;
          } else if (foundItem.itemValue === 'currentUserInput' || foundItem.itemValue === 'valueBlank') {
            assetDictElement.defaultData = '';
          } else if (
            foundItem &&
            typeof foundItem.itemValue === 'string' &&
            foundItem.itemValue.startsWith('currentUserItem_') === true
          ) {
            const userItemId = foundItem.itemValue.split('_')[1];
            const foundCustomItem = this.userCustomInfo.find((item) => String(item.itemId) === String(userItemId));
            console.log(foundCustomItem);
            const userInfoJson = JSON.parse(this.userInfo.userText);
            assetDictElement.defaultData = userInfoJson[foundCustomItem.itemDisplayName];
          } else if (foundItem.itemValue === 'currentUserEmail') {
            assetDictElement.defaultData = this.userInfo.userName;
          } else {
            assetDictElement.defaultData = foundItem.itemValue;
          }
        } else if (foundItem.itemType === 'date') {
          // 时间类型
          if (foundItem.itemValue === 'today' || foundItem.itemValue === 'now') {
            // 处理数据-今天
            if (foundItem.option.dateType === 'dateTime') {
              assetDictElement.defaultData = getFormatDateTime(new Date());
            } else {
              assetDictElement.defaultData = getFormatDate(new Date());
            }
          } else if (foundItem.itemValue === 'valueBlank' || foundItem.itemValue === 'currentUserInput') {
            // 处理输入空白
            assetDictElement.defaultData = '';
          } else {
            assetDictElement.defaultData = foundItem.itemValue;
          }
        } else if (foundItem.itemType === 'master') {
          assetDictElement.defaultData = foundItem.itemValue;
          if (assetDictElement.displayList) {
            if (assetDictElement.defaultData.display) {
              for (let key of Object.keys(assetDictElement.defaultData.display)) {
                const find = assetDictElement.displayList.find((item) => String(item.itemId) === String(key));
                if (find) {
                  find.itemValue = assetDictElement.defaultData.display[key];
                  const findP = assetDictElement.optionObject.masterDisplayItems.find(
                    (itemP) => String(itemP['itemId']) === String(find.itemId),
                  );
                  if (!_.isEmpty(findP['option'])) {
                    find['type'] = 'number';
                    setPercentageUnit(find, find.itemValue, findP['option']);
                  }
                }
              }
            } else {
              // 进入这个逻辑就证明master完全为空，需要进一步清空
              (assetDictElement.displayList as any[]).forEach((dp) => {
                dp['itemValue'] = '';
              });
              assetDictElement.masterId = '';
            }
          }
        } else {
          if (assetDictElement.itemType == 'calculate') {
            setPercentageUnit(assetDictElement, foundItem.itemValue, assetDictElement.optionObject, true);
          }
          assetDictElement.defaultData = foundItem.itemValue;
        }
      }
    }
    return ad;
  }

  // 批量处理设定流程代码
  // 批量扫描时会用到下方业务逻辑暂时保存
  private async doActionScanProgress(data) {
    this.registeredAssetList = null;
    this.assetActionModel = data;
    StorageUtils.set(StorageUtils.KEY_ASSET_ACTION_MODEL, this.assetActionModel);
    let navigationExtras: NavigationExtras = {
      queryParams: {
        dynamicItemDic: this.dynamicItemDic,
        isFromScanType: this.isFromScanType,
        isFromHome: true,
        registeredAssetList: [this.arrayAsset],
        assetActionModel: this.assetActionModel,
        processType: 0,
        isFromActionPage: 'true',
        isFromNewAction: 'true',
        fromPageRink: '/tabs/home',
        updateScanedAssetAction: undefined,
        assetActionItemList: undefined,
        callback: async () => {
          const parse = JSON.parse(data.assetActionItem);
          this.appurtenancesInformationTypeInfo = data.appurtenancesInformationTypeInfo;
          if (this.assetDict !== null && this.assetDict !== undefined) {
            for (const sectionName of Object.keys(this.assetDict)) {
              if (this.assetDict[sectionName] !== null && this.assetDict[sectionName] !== undefined) {
                for (const assetDictElement of this.assetDict[sectionName]) {
                  const foundItem = parse.find((item) => item.itemName === assetDictElement.itemName);
                  if (foundItem !== null && foundItem !== undefined) {
                    console.log(foundItem);
                    if (foundItem.itemValue === 'valueBlank' || foundItem.itemValue === 'currentUserInput') {
                      // 处理输入空白
                      assetDictElement.defaultData = '';
                    } else if (
                      foundItem &&
                      typeof foundItem.itemValue === 'string' &&
                      foundItem.itemValue.startsWith('assetItemName:')
                    ) {
                      // 判断是从其他地方取值
                      let itemId = String(foundItem.itemValue).split(':').pop();
                      const findAssetItem = this.assetDict[sectionName].find(
                        (item) => String(item.itemId) === String(itemId),
                      );
                      if (findAssetItem !== null && findAssetItem !== undefined) {
                        assetDictElement.defaultData = findAssetItem.defaultData;
                      } else {
                        assetDictElement.defaultData = '';
                      }
                    } else if (foundItem.itemType === 'input' && foundItem.itemName === '1行テキスト') {
                      if (foundItem.itemValue === 'currentUserName') {
                        assetDictElement.defaultData = `${this.userInfo.lastName} ${this.userInfo.firstName}`;
                      } else if (foundItem.itemValue === 'currentUserInput' || foundItem.itemValue === 'valueBlank') {
                        assetDictElement.defaultData = '';
                      } else if (
                        foundItem &&
                        typeof foundItem.itemValue === 'string' &&
                        foundItem.itemValue.startsWith('currentUserItem_') === true
                      ) {
                        const userItemId = foundItem.itemValue.split('_')[1];
                        const foundCustomItem = this.userCustomInfo.find(
                          (item) => String(item.itemId) === String(userItemId),
                        );
                        console.log(foundCustomItem);
                        const userInfoJson = JSON.parse(this.userInfo.userText);
                        assetDictElement.defaultData = userInfoJson[foundCustomItem.itemDisplayName];
                      } else if (foundItem.itemValue === 'currentUserEmail') {
                        assetDictElement.defaultData = this.userInfo.userName;
                      } else {
                        assetDictElement.defaultData = foundItem.itemValue;
                      }
                    } else if (foundItem.itemType === 'date') {
                      // 时间类型
                      if (foundItem.itemValue === 'today' || foundItem.itemValue === 'now') {
                        // 处理数据-今天
                        if (foundItem.option.dateType === 'dateTime') {
                          assetDictElement.defaultData = getFormatDateTime(new Date());
                        } else {
                          assetDictElement.defaultData = getFormatDate(new Date());
                        }
                      } else if (foundItem.itemValue === 'valueBlank' || foundItem.itemValue === 'currentUserInput') {
                        // 处理输入空白
                        assetDictElement.defaultData = '';
                      } else {
                        assetDictElement.defaultData = foundItem.itemValue;
                      }
                    } else if (foundItem.itemType === 'number' || foundItem.itemType === 'currency') {
                      foundItem.itemValue = _.toString(foundItem.itemValue).replace(/,/g, '');
                      assetDictElement.defaultData = foundItem.itemValue;
                    } else {
                      assetDictElement.defaultData = foundItem.itemValue;
                    }
                  }
                }
              }
            }
          }
          let relatesString = await this.customizeViewComponent.getRelateListString();
          const dealMap = await this.customizeViewComponent.dealData();
          // 資産種類
          dealMap['資産種類'] = JSON.parse(this.assetText)['資産種類'];
          // 更新日を現在の日時に設定する ASCHK-26883 票号中 后台指出 这个字段可以去掉
          // dealMap['更新日'] = getFormatDateTime(new Date());
          let assetText = JSON.stringify(dealMap);
          let assetId = this.assetId;
          let assetTypeId = this.assetTypeId;
          let modifiedDate = this.modifiedDate;
          let barcode = JSON.parse(this.assetText)['identityCode'];
          this.assetDetailService
            .saveAssetdata(assetTypeId, assetId, assetText, modifiedDate, relatesString, barcode)
            .then(async (data) => {
              console.log(data);
              this.toastr.success('アップデート完了しました', '', {
                positionClass: 'toast-center-center',
                timeOut: 500,
              });
              if (this.fromScanPage === 'assetScan') {
                this.assetDataBefore = JSON.stringify(await this.customizeViewComponent.dealData()); //保存成功後、assetDataBefore更新必要
                let result = await this.assetDetailService.getFindById(this.assetId);
                this.modifiedDate = result.arrayAsset[0].modifiedDate;
              }

              if (this.fromScanPage === 'scan' || this.insideFromScanPage === 'scan') {
                const params = this.activatedRoute.snapshot.queryParams;
                const isTabScan = params['isTabScan'];
                if (isTabScan !== null && isTabScan !== undefined) {
                  if (
                    this.appurtenancesInformationTypeInfo !== null &&
                    this.appurtenancesInformationTypeInfo !== undefined
                  ) {
                    const jsonObj = JSON.parse(this.appurtenancesInformationTypeInfo);
                    const element = jsonObj[0];
                    if (element !== null && element !== undefined) {
                      const appurtenancesInformationTypeId = element['appurtenancesInformationTypeId'];
                      const appurtenancesInformationTypeName = element['appurtenancesInformationTypeName'];
                      const dataList = element['dataList'];
                      const appurtenancesInformationMobileLayoutList =
                        await this.getMobileColumnList(appurtenancesInformationTypeId);
                      let tempLayoutSettingList: AssetTypeItem[] = [];
                      const userInfo = await this.getinfoService.getUserInfo();
                      let user = userInfo.data;
                      let firstName = user.firstName;
                      let lastName = user.lastName;
                      //データ設定
                      for (const mobileLayout of appurtenancesInformationMobileLayoutList) {
                        // mobileLayout.defaultData = '';
                        if (mobileLayout.itemName === 'ユーザー名') {
                          mobileLayout.defaultData = lastName + ' ' + firstName;
                        }
                        if (mobileLayout.itemName === 'ロケーション') {
                          mobileLayout.defaultData = await StorageUtils.get(StorageUtils.KEY_LOCATION);
                          mobileLayout.itemType = 'textarea';
                        }
                        mobileLayout.optionObject = JSON.parse(mobileLayout.option);
                        tempLayoutSettingList.push(mobileLayout);
                      }

                      let navigationExtras: NavigationExtras = {
                        queryParams: {
                          layoutSettingList: tempLayoutSettingList,
                          assetId: this.assetId,
                          appurtenancesInformationTypeId: appurtenancesInformationTypeId,
                          isEditPermissions: '1',
                          fromPage: 'detail',
                          jsString: this.jsString,
                          dataInfo: dataList,
                          callback: async () => {
                            await this.backToRegistedList(assetText, assetTypeId, assetId);
                          },
                        },
                      };
                      this.navController.navigateForward('/asset/relate/list/add', navigationExtras);
                    }
                  } else {
                    this.gotoSubmitSuccessPage();
                  }
                } else {
                  this.gotoSubmitSuccessPage();
                }
              } else {
                this.gotoSubmitSuccessPage();
              }
            });
        },
        headerText: ['QRコードをスキャンしました。', '内容をご確認の上、', '「次へ」をタップしてください。'],
      },
    };
    this.navController.navigateForward('/action/list/view', navigationExtras);
  }

  private async gotoSubmitSuccessPage() {
    const dealMap = await this.customizeViewComponent.dealData();
    // 資産種類
    dealMap['資産種類'] = JSON.parse(this.assetText)['資産種類'];
    // 更新日を現在の日時に設定する ASCHK-26883 票号中 后台指出 这个字段可以去掉
    // dealMap['更新日'] = getFormatDateTime(new Date());
    const params = this.activatedRoute.snapshot.queryParams;
    const navParams: NavigationOptions = {
      queryParams: {
        titleTxt: '実行完了',
        headerTxt: `${this.assetActionModel.assetActionName}の実行が完了しました。`,
        secondButtonTxt: `実行完了した処理を確認`,
        assetActionModel: this.assetActionModel,
        processType: 0,
        assetData: [this.arrayAsset],
        isAction: this.isAction,
        fromListPage: params['fromListPage'],
        callback: () => {},
        fromPage: 'preview-confirm',
      },
    };
    this.navController.navigateForward('/tabs/asset/submit-complete', navParams);
  }

  showScanActionQrCodeSucToast() {
    this.toastr.success('スキャンした資産情報を入力しました', '', {
      positionClass: 'toast-center-center',
      timeOut: 1500,
    });
  }

  showScanActionQrCodeFaiToast() {
    this.toastr.error('このQRコードは入力できません', '', {
      positionClass: 'toast-center-center',
      timeOut: 1500,
    });
  }

  async getMasterDefaultValue(masterTypeId, masterId, itemName) {
    var masterListDefaultValue = '';
    if (masterTypeId && masterId && itemName) {
      // 同じmasterTypeIdだったら、２度とAPIを呼び出さない
      if (this.masterResult && !(masterTypeId in this.masterResult)) {
        const result = await this.actionService.getMasterInfoById(masterTypeId, false);
        if (result.code == 0) {
          this.masterResult[masterTypeId] = result;
        }
      }
      let masterDetail: [] = this.masterResult[masterTypeId].masterDetail;
      masterDetail.forEach((master) => {
        if (masterId && itemName) {
          if (master['masterId'] == masterId) {
            let masterText = JSON.parse(master['masterText']);
            masterListDefaultValue = masterText[itemName];
          }
        }
      });
    }
    return masterListDefaultValue;
  }

  async saveChangedData(callback: () => void) {
    let relatesString = await this.customizeViewComponent.getRelateListString();
    // 保存時のカスタマイズロジックを取得

    let customsLogicResult = await this.addAssetService.getCustomsLogicJSSetting(this.assetTypeId);
    let jsSaveAsset = '';
    // 如果js从后台取得后不为空那么才把commonJS拼接到js
    if (!_.isEmpty(customsLogicResult?.jsSaveAsset)) {
      const cjs = _.isEmpty(customsLogicResult['commonJS']) ? '' : customsLogicResult['commonJS'];
      jsSaveAsset = cjs + customsLogicResult.jsSaveAsset;
    }
    // カスタマイズロジック実行、（資産の新規作成、アップデート、取得）
    let result = await this.customizeViewComponent.runJavascriptTogetAssetOrUpdateAssetOrInsertAsset(
      jsSaveAsset,
      false,
      '',
    );
    console.log('result098 ++++======+++++++', result);
    if (result === false) {
      return;
    }

    const dealMap = await this.customizeViewComponent.dealData();
    // 資産種類
    dealMap['資産種類'] = JSON.parse(this.assetText)['資産種類'];
    // 更新日を現在の日時に設定する ASCHK-26883 票号中 后台指出 这个字段可以去掉
    // dealMap['更新日'] = getFormatDateTime(new Date());
    let assetText = JSON.stringify(dealMap);
    let assetId = this.assetId;
    let assetTypeId = this.assetTypeId;
    let modifiedDate = this.modifiedDate;
    let barcode = JSON.parse(this.assetText)['identityCode'];
    this.assetDetailService
      .saveAssetdata(assetTypeId, assetId, assetText, modifiedDate, relatesString, barcode)
      .then(async (data) => {
        console.log('asset-force/src/app/asset/detail/detail.page.ts ===> 714', data);
        this.queryParams['assetDict'] = JSON.stringify(this.assetDict);
        this.toastr.success('アップデート完了しました', '', {
          positionClass: 'toast-center-center',
          timeOut: 500,
        });
        callback();
      });
  }
}
