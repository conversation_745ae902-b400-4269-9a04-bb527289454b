import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute, ParamMap } from '@angular/router';
import { NavController } from '@ionic/angular';
import { Subscription, Subject } from 'rxjs';
import { ToastrService, ActiveToast } from 'ngx-toastr';
import {
  WorkflowGetApplicationWorkflowListService,
  Progress,
} from 'src/app/services/workflow/get-applicationWorkflowList/workflow-getApplicationWorkflowList.service';
import { WorkflowEngineTask } from 'src/app/models/workflow/WorkflowEngineTask';
import { WorkflowSearchCondition } from 'src/app/models/workflow/workflowSearchCondition';
import { Asset } from 'src/app/models/asset';
import { Throttle } from 'src/app/utils/throttle.decorator';

@Component({
  selector: 'app-detail',
  templateUrl: './detail.page.html',
  styleUrls: ['./detail.page.scss'],
})
export class DetailPage implements OnInit {
  assetList: Asset[];
  [x: string]: any;
  order: 'descend' | 'ascend' = 'descend'; // 並び順
  orderSubject: Subject<'ascend' | 'descend'> = new Subject();
  orderState = this.orderSubject.asObservable();
  items: WorkflowEngineTask[] = []; // アイテム
  search: string = ''; // 検索テキスト
  progress: Progress = 'not-yet'; // 進捗の絞り込み
  activeToast: ActiveToast<number>; // 表示中のtoastr
  routeObserver: Subscription; // 遷移を監視するオブジェクト
  unaapveCount: number; // 未承認のWF数
  searchConditionDataList: any;
  defaultDoneSearchConditionDataList: WorkflowSearchCondition[] = []; // default申請済
  defaultAllSearchConditionDataList: WorkflowSearchCondition[] = []; //　default申請の全て
  showAssetLocation: boolean = false; // 資産情報表示画面で「場所」が表示されるか
  isFromSearchCondition: string;
  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private toastr: ToastrService,
    private workflowGetApplicationWorkflowListService: WorkflowGetApplicationWorkflowListService,
    private navController: NavController,
  ) {
    // 資産一覧を検索用のフォーマットに揃えておく
    this.itemsForSearch = this.items.map((item) => {
      return Object.assign(item, {
        name: this.formatSearch(item.workflowName),
      });
    });
    // 並び順が変えたらソートする（監視）
    // this.orderState.subscribe(() => {
    //   this.sortByDeadlineDate(this.items);
    // });
  }

  ngOnInit() {
    this.activatedRoute.queryParamMap.subscribe(async (params: ParamMap) => {
      const name = params.get('name');
      const progress = params.get('progress');
      this.isFromSearchCondition = params.get('isFromSearchCondition');
      //デフォルト検索条件設定
      if (this.isFromSearchCondition !== 'true') {
        this.defaultForDoneSearchCondition();
        this.defaultForAllSearchCondition();
      }
      // 絞り込みを設定する
      switch (progress) {
        case 'all':
          this.progress = 'all';
          break;
        case 'done':
          this.progress = 'done';
          break;
        case 'not-yet':
          this.progress = 'not-yet';
          break;
        default:
          break;
      }
      // this.reloadData()
      this.showAssetLocation = await this.workflowActionsService.showTenantAssetLocation();

      if (name || name == '') {
        // 通知を表示する
        this.activeToast = this.toastr.success(`${name}しました。`, '', {
          positionClass: 'toast-center-center',
          timeOut: 1000,
        });

        this.clearPosParam(progress);
      }
    });
  }

  /**
   * ワークフローを絞り込み
   */
  get filterItems(): WorkflowEngineTask[] {
    if (this.items === null) {
      return null;
    }
    return this.filter(this.search, this.progress);
  }
  /**
   * 検索結果なし
   */
  get isEmptyResult(): boolean {
    if (this.items === null) {
      return true;
    }
    return this.filterItems.length <= 0;
  }

  @Throttle()
  public openNotYet({ event }) {
    this.progress = 'not-yet';
    this.reloadData();
  } // 申請済み
  @Throttle()
  public openDone({ event }) {
    this.progress = 'done';
    this.reloadData();
  }
}
