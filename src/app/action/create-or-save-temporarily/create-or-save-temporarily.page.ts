/*
 * @Author: your name
 * @Date: 2020-06-12 10:52:12
 * @,@LastEditTime: ,: 2021-08-20 15:57:38
 * @,@LastEditors: ,: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: asset-force/src/app/action/create-or-save-temporarily/create-or-save-temporarily.page.ts
 */

import { ChangeDetectorRef, Component, ViewChild } from '@angular/core';
import { NavigationExtras, Router } from '@angular/router';
import type { IonInput } from '@ionic/angular';
import { AlertController, IonContent, NavController, Platform } from '@ionic/angular';
import * as _ from 'lodash';
import { AMOUNT_TYPE } from 'src/app/models/assetAction';
import { ActionService } from 'src/app/services/action/action-service.service';
import { AssetService } from 'src/app/services/asset/asset.service';
import { AssetDetailService } from 'src/app/services/assetdetail/asset-detail.service';
import { PrinterService } from 'src/app/services/printer/printer.service';
import { HttpUtilsService } from 'src/app/utils/http-utils.service';
import { StorageUtils } from 'src/app/utils/storage-utils';
import { Throttle } from 'src/app/utils/throttle.decorator';
import { TabsPage } from 'src/app/tabs/tabs.page';
import {
  NewAndSaveActionAddState,
  SetNewAndSaveActionAssetInfoADD,
  SetNewAndSaveActionAssetListADD,
  SetNewAndSaveActionBasicPropertiesADD,
} from 'src/app/services/action/action-service.actions';
import { Store } from '@ngxs/store';
import { GlobalVariableService } from '../../utils/globl-variable.service';
import { AddAssetScanStartServiceService } from 'src/app/services/add/add-asset-scan-start-service.service';
import { MemCache } from 'src/app/utils/MemCache';

@Component({
  selector: 'app-create-or-save-temporarily',
  templateUrl: './create-or-save-temporarily.page.html',
  styleUrls: ['./create-or-save-temporarily.page.scss'],
})
export class CreateOrSaveTemporarilyPage {
  @ViewChild('ionInputEl', { static: true }) ionInputEl!: IonInput;
  @ViewChild('content') content: IonContent;

  // 检索输入内容
  inputKey: string = '';

  retryEntry = [];
  private actionData;
  amountData: {
    isAmountType: boolean;
    amountTextList: string[];
    amountType: string;
  };
  // 是否可以打印
  isPrintAvailable: boolean = false;

  // 显示资产列表内容数据
  registeredAssetList: any[] = [];
  /** 展开关闭显示更多项目item */
  more: { [key: string]: boolean } = {};
  private isFromHome: boolean = false;
  private isFromNewAction: boolean;
  // 处理设定名称
  private actionLabel: string;
  // 1所有处理一览，2处理设定组
  private actionListType: number;
  // 资产种类ID
  private assetTypeId: number;
  // 资产种类名称
  assetTypeName: string = '';
  // 0一时保存 1新规处理设定
  private processType = 0;

  dynamicItemDic: Map<string, string>;
  assetActionModel: any;
  isNewAction: boolean;
  isFromTakeMiddleButtonPage: boolean;

  isShowItemsMax = false; // 是否已经显示了XXX件
  maxitems = 200; // 最大显示件数
  assectsMax = 16; // 每页最大取得件数
  pageNum = 1; // 当前页码
  assetTotalCount = 0; // 总共资产数量
  isNextPage: boolean; // 是否还有下一页
  searchNum: number; // 检索后剩余多少个资产
  searchKey: string; // 处理设定一览搜索内容
  displayCount = 0;; // 显示查询结果数量

  constructor(
    private printerService: PrinterService,
    private http: HttpUtilsService,
    private alertController: AlertController,
    private navController: NavController,
    private assetService: AssetService,
    private actionService: ActionService,
    private assetDetailService: AssetDetailService,
    private store: Store,
    private ref: ChangeDetectorRef,
    public globalVar: GlobalVariableService,
    private assetScanServer: AddAssetScanStartServiceService,
    private platfrom: Platform,
    private pageRouter: Router
  ) {}

  /**
   * @description: ngOnInit
   * @param {*}
   * @return {*}
   */
  async ionViewWillEnter() {


    // this.store.dispatch(new SetNewAndSaveActionBasicPropertiesADD(dtyul));

    this.pageNum = 1;
    this.initActionInfo();
  }

  async initActionInfo() {
    if (StorageUtils.platform.is('desktop')) {
      // this.testData.prepareTestData();
    }
    const getactionData = await this.store.selectSnapshot(NewAndSaveActionAddState.getNewAndSaveActionBasicProperties);
    console.log('🚀 ~ CreateOrSaveTemporarilyPage ~ initActionInfo ~ getactionData:', getactionData);
    this.actionData = _.cloneDeep(getactionData);
    this.assetActionModel = this.actionData['assetActionModel'];
    if (!_.isEmpty(this.assetActionModel)) {
      this.assetTypeName = this.assetActionModel['assetTypeName'];
      this.assetTypeId = this.assetActionModel['assetTypeId'];
      const amountType = this.assetActionModel['amountType'];
      this.amountData = {
        isAmountType: _.isEmpty(amountType) ? false : true,
        amountTextList: this.getAcountText(amountType),
        amountType: amountType,
      };
    }
    this.dynamicItemDic = this.actionData['dynamicItemDic'];
    this.isNewAction = this.actionData['isNewAction'];
    this.processType = this.isNewAction ? 1 : 0;
    this.isFromNewAction = _.isEmpty(this.actionData['isNewAction']) ? false : this.actionData['isNewAction'];
    if (_.isEmpty(this.actionData['isFromHome'])) {
      this.isFromHome = false;
    } else {
      if (_.isBoolean(this.actionData['isFromHome'])) {
        this.isFromHome = this.actionData['isFromHome'];
      }
      if (_.isString(this.actionData['isFromHome'])) {
        this.isFromHome = this.actionData['isFromHome'] === 'true' ? true : false;
      }
    }
    this.isFromTakeMiddleButtonPage = this.actionData['isFromTakeMiddleButtonPage'];
    this.actionLabel = this.actionData['actionLabel'];
    this.actionListType = this.actionData['actionListType'];
    this.searchKey = this.actionData['searchKey'];
    await this.checkPrintAvailable();

    // const assetListMbdata = await this.getHomeImageFun(this.actionData['registeredAssetList']);
    const assetListMbdata = _.cloneDeep(this.actionData['registeredAssetList']);
    await this.store.dispatch(new SetNewAndSaveActionAssetListADD(assetListMbdata));
    await this.getAssetList(false);
  }

  /**
   * 资产列表取得
   * @param isClickNextPage 是否点击了下一页
   */
  async getAssetList(isClickNextPage: boolean) {
    if (this.pageNum === 1) {
      this.registeredAssetList = [];
    }

    // 清除过滤.
    MemCache.instance.remove(MemCache.KEY_ACTION_ASSETID_FILTER);
    const simulatePage = {
      pageNum: this.pageNum,
      piecesNum: this.assectsMax,
      search: this.inputKey,
      maxitems: this.maxitems,
    };
    await this.store.dispatch(new SetNewAndSaveActionAssetListADD({ simulatePage: simulatePage }));
    let { assetList, totalAssetNum, isNextPage, searchNum, showAssetList } = await this.store.selectSnapshot(
      NewAndSaveActionAddState.getSimulatePageNewAndSaveActionAssetList,
    );
    if (!_.isEmpty(this.inputKey)) {
      let assetTypeId = null;
      let idList = showAssetList.map((p) => {
        assetTypeId = p.assetTypeId;
        return _.toNumber(p.assetId);
      });
      let filterResult = await this.actionService.findAssetsByKeyword(
        assetTypeId,
        this.inputKey,
        0,
        this.maxitems,
        idList,
        true,
      );

      if (filterResult) {
        let filterdIds = filterResult.assets || [];
        MemCache.instance.set(MemCache.KEY_ACTION_ASSETID_FILTER, filterdIds);

        await this.store.dispatch(new SetNewAndSaveActionAssetListADD({ simulatePage: simulatePage }));
        // 获取真实的数据.
        const {
          assetList: assetList2,
          totalAssetNum: totalAssetNum2,
          isNextPage: isNextPage2,
          searchNum: searchNum2,
        } = await this.store.selectSnapshot(NewAndSaveActionAddState.getSimulatePageNewAndSaveActionAssetList);

        assetList = assetList2 ?? [];
        totalAssetNum = totalAssetNum2 ?? 0;
        isNextPage = isNextPage2;
        searchNum = searchNum2;
      }
    }

    if (!this.registeredAssetList) {
      this.registeredAssetList = [];
    }

    // 选择要复制的源列表
    const sourceList = _.isEmpty(this.inputKey) ? showAssetList : assetList;    

    // 深拷贝源列表并追加到 registeredAssetList
    this.registeredAssetList.push(..._.cloneDeep(sourceList));

    // 去重操作：根据某个唯一标识符字段去重
    this.registeredAssetList = _.uniqBy(this.registeredAssetList, 'assetId'); // 使用 'id' 替换为唯一标识符字段

    this.isShowItemsMax = false;
    this.isNextPage = isNextPage;

    if (!isClickNextPage) {
      this.assetTotalCount = totalAssetNum;
    }
    if (this.maxitems < this.registeredAssetList.length) {
      this.registeredAssetList = this.registeredAssetList.slice(0, this.maxitems);
      this.isShowItemsMax = true;
      this.isNextPage = true;
    }
    if (_.isEmpty(this.inputKey)) {
      this.searchNum = totalAssetNum;
    } else {
      this.searchNum = searchNum;
    }
     // 设置 displayCount
     this.displayCount = this.assetTotalCount < this.maxitems ? this.assetTotalCount : this.maxitems;
  
  }

  // /**
  //  * home画像取得
  //  * @param {Asset[]} assetItemList
  //  * @param {AssetMobileSetting} assetTypeeSetting
  //  * @returns {Asset[]}
  //  */
  // async getHomeImageFun(assetItemList: any[]): Promise<any[]> {
  //   const assetItemResult = await this.actionService.getItemByAssetType(this.assetTypeId);
  //   const assetTypeeSetting: AssetMobileSetting = assetItemResult;
  //   const { assetItemList: assetItemTypeSettingList } = assetTypeeSetting;

  //   const assetList = [];
  //   for (let index = 0; index < assetItemList.length; index++) {
  //     const asset = assetItemList[index];
  //     // 此处 会将内部的 assettext 转成 object, 然后 对里面的 homeimage 做过滤,  内部有是否有显示的权限判断.
  //     const assetClod = await this.actionService.getHomeImageFun(asset, assetItemTypeSettingList);
  //     assetList.push(assetClod);
  //   }
  //   return assetList;
  // }

  async onCancelClick() {
    // 如果从home页面点进
    if (this.isFromHome) {
      this.navController.navigateBack('tabs/my-home');
      return;
    }
    // 如果从中间按钮到处理设定
    if (this.isFromTakeMiddleButtonPage) {
      const lastTa = TabsPage.lastTabHomePath;
      this.navController.navigateRoot('tabs/' + lastTa);
      return;
    }
    // 新规
    if (this.processType === 1) {
      let url = '/tabs/action/list/list/';
      const navigationExtras: NavigationExtras = {
        queryParams: {
          actionLabel: this.actionListType === 1 ? '処理一覧' : this.actionLabel,
          actionListType: this.actionListType, // 1所有处理一览，2处理设定组
          fromUrl: url,
          searchKey: this.searchKey,
        },
      };
      this.navController.navigateBack(url, navigationExtras);
      return;
    }
    // 一时保存
    if (this.processType === 0) {
      const alert = await this.alertController.create({
        cssClass: 'common-alert',
        header: 'キャンセルします',
        message: 'スキャンしたデータは破棄されますが、よろしいですか？',
        buttons: [
          {
            text: 'キャンセル',
            role: 'cancel',
            cssClass: 'common-alert-text-primary',
            handler: () => {},
          },
          {
            text: 'OK',
            role: 'confirm',
            cssClass: 'common-alert-text-primary',
            handler: () => {
              setTimeout(() => {
                this.navController.navigateBack('/tabs/action/progress/list');
              }, 500);
            },
          },
        ],
        backdropDismiss: false,
      });
      await alert.present();
    }
  }

  @Throttle(1500)
  async toScan() {
    const getactionData = await this.store.selectSnapshot(NewAndSaveActionAddState.getNewAndSaveActionBasicProperties);
    let nativeData = MemCache.instance.get(MemCache.KEY_NATIVE_TO_INOIC_DATA);
    if (_.isArray(nativeData) && nativeData.length > 0) {
      nativeData = _.cloneDeep(nativeData);
      // 将count 数据同步过去
      let registeredAssetList = getactionData['registeredAssetList'];

      nativeData = _.filter(nativeData, (assetData) => {
        let data = _.find(registeredAssetList, (asset) => {
          return _.isEqual(_.toNumber(assetData.assetId), _.toNumber(asset.assetId));
        });

        if (!!data) {
          assetData['count'] = data.assetScanedCount;
          return true;
        }

        return false;
      });

      MemCache.instance.set(MemCache.KEY_NATIVE_TO_INOIC_DATA, nativeData);
    }

    const clickNextStepData = {
      data: this.assetActionModel,
      registeredAssetList: nativeData,
      unRegisteredAssetList: [],
      dynamicItemDic: this.dynamicItemDic,
      actionLabel: this.actionLabel,
      tempActionListType: this.actionListType,
      isNewAction: this.isNewAction,
      isFromHome: false,
      isFromLocationSettingPage: false,
      isFromTakeMiddleButtonPage: false,
      isNeedJump: false,
      searchKey: this.searchKey,
      pagePath: this.pageRouter.url
    };
    this.assetScanServer.scanStartNextStep(clickNextStepData, () => {
      this.initActionInfo();
    });
  }

  isNoneData(): boolean {
    return _.isEmpty(this.registeredAssetList);
  }

  /**
   * 检索框输入onChange
   */
  async onChangeSearchInputSearchInput(value) {
    this.inputKey = _.trim(value);
  }

  @Throttle(1000)
  async doSearch() {
    // this.inputKey = _.trim(this.inputKey);
    this.pageNum = 1;
    this.retryEntry = [];
    await this.getAssetList(false);
  }

  /** 清除搜索 */
  async clearInputKey() {
    this.inputKey = '';
    this.pageNum = 1;
    await this.getAssetList(false);
  }

  /** 下一页 */
  async onClicklistLoadData(ev) {
    this.pageNum = this.pageNum + 1;
    await this.getAssetList(true);
    this.ref.detectChanges();
    ev.target.complete();
  }

  /**
   * 打印功能
   * @description: スキャンリストに登録されている全資産のシールを一括発行
   * @param {type}
   * @return:
   */
  @Throttle()
  async printLabel() {
    console.log('========== scan to print', this.registeredAssetList);

    const dataList = this.registeredAssetList.reduce((acc, item) => {
      const index = acc.findIndex((r) => r.assetTypeId === item.assetTypeId);
      if (index === -1) {
        acc.push({
          assetTypeId: item.assetTypeId,
          asset: [
            {
              assetId: item.assetId,
              barcode: item.barcode,
              identityCode: item.identityCode,
            },
          ],
        });
      } else {
        acc[index].asset.push({
          assetId: item.assetId,
          identityCode: item.identityCode,
          barcode: item.barcode,
        });
      }
      return acc;
    }, []);

    console.log('🚀 ~ CreateOrSaveTemporarilyPage ~ printLabel ~ to native paramter result:', dataList);
    await this.printerService.printLabelList(dataList, ['']);
  }

  /**
   * 資産印刷可能確認
   */
  async checkPrintAvailable() {
    this.isPrintAvailable = await this.printerService.checkPrintAvailable([`${this.assetTypeId}`], true);
  }

  /**
   * 展开&收缩
   * @param item ItemInterface
   * @param b boolean
   * @returns boolean
   */
  // @Throttle() 无网络请求将此注解去掉
  clickMore(assetId, b: boolean) {
    console.log('clickMore', b ? 'open' : 'closure', assetId);
    this.more[assetId] = b;
    setTimeout(() => {
      this.content.scrollByPoint(0, -1, 100);
    });
    return false;
  }

  checkInput(): boolean {
    // 个体的时候不check，直接返回。只有个数的时候进行check
    if (!this.amountData.isAmountType) {
      return true;
    }

    for (let asset of this.registeredAssetList) {
      // 检查扫描数量是否合法，档amountType==4的时候为棚卸，棚卸情况允许数量为0
      if (
        asset.assetScanedCount === null ||
        asset.assetScanedCount === undefined ||
        (this.amountData.amountType == '4' && asset.assetScanedCount < 0)
      ) {
        return false;
      }
    }
    return true;
  }

  /**
   * 下一个迁移页面
   * @returns void
   */
  @Throttle()
  async goActionNextStep() {
    if (!this.checkInput()) {
      const alert = await this.alertController.create({
        header: '内容に不備があります',
        message: '表示されたメッセージをご確認の上、もう一度登録してください。',
        buttons: [
          {
            text: 'OK',
            cssClass: 'font-weight-bold',
            handler: () => {},
          },
        ],
        backdropDismiss: false,
      });
      await alert.present();
      return;
    }

    if (this.actionData !== null && this.actionData !== undefined) {
      const getactionData = await this.store.selectSnapshot(
        NewAndSaveActionAddState.getNewAndSaveActionBasicProperties,
      );
      this.actionData.registeredAssetList = _.cloneDeep(getactionData['registeredAssetList']);
      await StorageUtils.set(StorageUtils.KEY_ASSET_ACTION_MODEL, this.actionData.assetActionModel);
      const callback = () => {
        let navigationExtras: NavigationExtras = {
          queryParams: {
            dynamicItemDic: this.actionData.dynamicItemDic,
            isFromScanType: this.actionData.isFromScanType,
            isFromHome: this.isFromHome,
            registeredAssetList: this.actionData.registeredAssetList,
            assetActionModel: this.actionData.assetActionModel,
            processType: this.processType,
            isFromActionPage: 'true',
            isFromNewAction: this.isNewAction,
            fromPageRink: '/action/create-or-save-temporarily',
            updateScanedAssetAction: undefined,
            assetActionItemList: undefined,
            isAction: true,
            headerText: [
              this.amountData?.amountTextList[0] === ''
                ? '処理を選択しました。'
                : this.amountData?.amountTextList[2] + 'を設定しました。',
              '内容をご確認の上、',
              '「次へ」をタップしてください。',
            ],
            actionLabel: this.actionLabel,
            actionListType: this.actionListType,
            isNewAction: this.isNewAction,
            searchKey: this.searchKey,
          },
        };
        this.navController.navigateForward('/action/list/view', navigationExtras);
      };

      // 遍历this.actionData.registeredAssetList，如果有assetScanedCount为0的情况存在，弹出提示框
      let isZero = false;
      for (let asset of this.actionData.registeredAssetList) {
        if (asset.assetScanedCount === 0) {
          isZero = true;
          break;
        }
      }
      if (isZero) {
        const alert = await this.alertController.create({
          header: '処理を実行します',
          message: `数量が0になる資産がありますが、よろしいですか？`,
          buttons: [
            {
              text: 'キャンセル',
              role: 'cancel',
              cssClass: 'font-weight-bold',
            },
            {
              text: 'OK',
              cssClass: 'font-weight-bold',
              handler: () => {
                callback();
              },
            },
          ],
          backdropDismiss: false,
        });
        await alert.present();
        return;
      } else {
        callback();
      }
    }
  }

  private getAcountText(amountType: string): string[] {
    if (amountType === AMOUNT_TYPE.INCREASE_AMOUNT_TYPE) {
      //純増
      return ['増加数', '増加数量', '増加数'];
    }
    if (amountType === AMOUNT_TYPE.DECREASE_AMOUNT_TYPE) {
      // 純減
      return ['減少数', '減少数量', '減少数'];
    }
    if (amountType === AMOUNT_TYPE.MOVE_AMOUNT_TYPE) {
      // 移動
      return ['移動させる資産数', '移動数量', '移動数'];
    }
    if (amountType === AMOUNT_TYPE.INVENTORY_AMOUNT_TYPE) {
      // 棚卸し
      return ['実際の数量', '実際数量', '実際数'];
    }
    return ['', '', ''];
  }

  /**
   * @description: 詳細ページに遷移する
   * @param {type}
   * @return:
   */
  @Throttle()
  async toDetailPage(pAsset: Asset) {
    // registeredAssetList 中的item.
    try {
      // 判断権限
      const result1 = await this.assetService.getAssetAuthority(
        _.toString(pAsset.assetId),
        _.toString(this.assetTypeId),
      );

      if (result1.count === 1) {
        let level = Date.now();
        const navigationExtras: NavigationExtras = {
          queryParams: {
            // assetText: pAsset.assetText,// 未使用
            assetId: pAsset.assetId,
            assetTypeId: pAsset.assetTypeId,
            barcode: pAsset['barcode'],
            fromListPage: 'fromActionScan',
            backUrl: '/action/create-or-save-temporarily',
            fromScanPage: 'fromActionScan',
            isTabScan: true,
            isAction: true,
            level: level,
          },
        };
        this.navController.navigateForward('/asset/detail/' + level, navigationExtras);
      } else {
        const alert = await this.alertController.create({
          message:
            '資産が削除された又は権限が変更された可能性があるので、処理できません。画面をリフレッシュしてください。',
          cssClass: 'search-alert-title-controller',
          buttons: [
            {
              text: 'OK',
              role: 'ok',
            },
          ],
        });
        await alert.present();
      }
    } catch (error) {
      console.error('toDetailPage => error', error);
    }
  }

  /**
   * 個別削除の確認ダイアログ
   * @param id アイテムID
   * @param name アイテム名
   */
  @Throttle()
  async presentDeleteConfirm(asset: Asset) {
    console.log('*******new/W1-asset-list************', asset);
    const alert = await this.alertController.create({
      message: `${asset.assetName}を削除しますか？`,
      buttons: [
        {
          text: 'いいえ',
          role: 'cancel',
          cssClass: 'font-weight-bold',
        },
        {
          text: 'はい',
          cssClass: 'text-danger font-weight-normal',
          handler: async () => {
            this.registeredAssetList = _.filter(this.registeredAssetList, (assetData: Asset) => {
              return !_.isEqual(assetData.assetId, asset.assetId);
            });
            await this.store.dispatch(new SetNewAndSaveActionAssetInfoADD(asset, true));
            this.searchNum = this.searchNum - 1;
            this.assetTotalCount = this.assetTotalCount - 1;
          },
        },
      ],
    });
    await alert.present();
  }

  //  差分の数量を算出
  getDifferenceCount(asset) {
    // registeredAssetList 中的item.
    return Number(asset.quantity) - Number(asset.assetScanedCount);
  }

  async combinedCalculation(asset) {
    this.registeredAssetList = this.registeredAssetList.map((regAsset) => {
      if (regAsset['assetId'] === asset['assetId']) {
        regAsset = asset;
      }
      return regAsset;
    });
    await this.store.dispatch(new SetNewAndSaveActionAssetInfoADD(asset, false));
  }

  // 数量を算出
  getQuantity(asset) {
    return Number(asset.quantity);
  }

  async minusClick(item: any) {
    this.actionService.minusClick(item, this.amountData.amountType == '4');
    this.combinedCalculation(item);
  }

  async plusClick(item: any) {
    this.actionService.plusClick(item);
    this.combinedCalculation(item);
  }

  async updateCount(item: any, $event) {
    if ($event.target.value === '') {
      item.assetScanedCount = this.amountData.amountType == '4' ? 0 : 1;
      this.combinedCalculation(item);
      return;
    }
    this.actionService.updateCount(item, $event, this.amountData.amountType == '4');
    this.combinedCalculation(item);
  }

  // 判断是会否有home画像
  hasHomeImage(item) {
    if (item.homeImage instanceof Object) {
      return !_.isEmpty(item.homeImage['url']);
    }
    return false;
  }

  async errorImgLoad(item) {
    // 第一次是没有 turl 的, 所以先行判断.
    const turl = item.homeImage['turl'];
    if (!turl) {
      item.homeImage.turl = await this.getTurl(item.homeImage['url'], false);
      return;
    }

    if (this.retryEntry[item.homeImage['url']]) {
      if (this.retryEntry[item.homeImage['url']] < 4) {
        this.retryEntry[item.homeImage['url']]++;
      } else {
        this.retryEntry[item.homeImage['url']] = 4;
      }
    } else {
      this.retryEntry[item.homeImage['url']] = 1;
    }
    if (this.retryEntry[item.homeImage['url']] == 4) {
      item.homeImage.turl = '';
      item.homeImgLoaded = true;
      return;
    }

    item.homeImage.turl = await this.getTurl(item.homeImage['url'], false);
  }

  // 通过url获取turl用于展示
  async getTurl(filePath, needLoading = false) {
    const resultData = await this.assetDetailService.getTurl(filePath, needLoading);
    return resultData.data.getUrl;
  }

  /**
   * 只可以输入数字，并且零以上
   * @param ev
   */
  onInput(item: any, ev) {
    const value = ev.target!.value;
    const filteredValue = value.replace(/[^0-9]+/g, '');
    this.ionInputEl.value = item.assetScanedCount = filteredValue;
  }

  private lastInputElement: HTMLInputElement;

  numInputFocus(asset) {
    asset.tempAssetScanedCount = asset.assetScanedCount;
    this.lastInputElement = document.activeElement as HTMLInputElement;
  }

  isAutoBlur = false;
  numInputBlur(event, asset) {
    console.log('numInputBlur====》当前input框失去焦点');
    event.stopPropagation();
    if (this.isAutoBlur) {
      this.isAutoBlur = false;
      return;
    }
    if (!asset.assetScanedCount) {
      if (this.amountData && this.amountData.amountType === '4') {
        asset.assetScanedCount = 0;
      } else {
        asset.assetScanedCount = asset.tempAssetScanedCount;
      }
    }
    if (this.platfrom.is('android')) {
      // 如果输入框有焦点并且有文本内容
      if (this.lastInputElement) {
        // 全选文本
        this.lastInputElement.select();

        // 执行命令（例如复制文本到剪贴板）
        document.execCommand('insertText', false, asset.assetScanedCount);
        this.isAutoBlur = true;
        this.lastInputElement.blur();
        this.lastInputElement = undefined;
      }
    }
  }

  isShowErrorMsg(asset): boolean {
    if (this.amountData && this.amountData.amountType === '4') {
      return asset.assetScanedCount < 0;
    } else {
      return asset.assetScanedCount === null || asset.assetScanedCount === undefined || asset.assetScanedCount < 1;
    }
  }

  genErrMsg() {
    if (this.amountData && this.amountData.amountType === '4') {
      return '0以上の数量を指定してください。';
    } else {
      return '1以上の数量を指定してください。';
    }
  }
}
