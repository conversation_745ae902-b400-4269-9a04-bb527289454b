declare namespace Stimulsoft.Report.Events {
    import IStiSeries = Stimulsoft.Report.Chart.IStiSeries;
    import EventArgs = Stimulsoft.System.EventArgs;
    class StiGetTitleEventArgs extends EventArgs {
        private valueObject;
        get value(): string;
        set value(value: string);
        index: number;
        series: IStiSeries;
    }
}
declare namespace Stimulsoft.Report.Events {
    import EventArgs = Stimulsoft.System.EventArgs;
    import IStiSeries = Stimulsoft.Report.Chart.IStiSeries;
    class StiNewAutoSeriesEventArgs extends EventArgs {
        seriesIndex: number;
        color: any;
        series: IStiSeries;
        constructor(seriesIndex: number, series: IStiSeries, color: any);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiChart = Stimulsoft.Report.Components.StiChart;
    import Color = Stimulsoft.System.Drawing.Color;
    import TimeSpan = Stimulsoft.System.TimeSpan;
    import PointD = Stimulsoft.System.Drawing.Point;
    import IStiSeries = Stimulsoft.Report.Chart.IStiSeries;
    class StiChartHelper {
        static globalDurationElement: TimeSpan;
        static globalBeginTimeElement: TimeSpan;
        static fillSeriesData(series: StiSeries, items: StiDataItem[]): void;
        static getFilterData(report: StiReport, filter: StiChartFilter, filterMethodName: string): any;
        static getFilterResult(filter: StiChartFilter, itemArgument: any, itemValue: any, itemValueEnd: any, itemValueOpen: any, itemValueClose: any, itemValueLow: any, itemValueHigh: any, data: any): boolean;
        static convertStringToColor(colorStr: string): Color;
        static createChart(masterChart: StiChart, chartComp: StiChart): void;
        static getShorterListPoints(series: StiSeries): PointD[];
        private static checkParetoValues;
        private static checkValueNaN;
        private static checkArgumentsDateTimeStep;
        static checkWaterfallTotals(chart: IStiChart): void;
        private static createTopN;
        private static createValuesTopN;
        private static createValuesWeightsTopN;
        private static getNextDate;
        private static getKey;
        private static sortArray;
        private static findIndex;
        private static getValueForDate;
        private static getTotalTimeSpans;
        private static isArgumentsDateTime;
        private static maximumDate;
        private static minimumDate;
        private static getAutoSeriesColorFromautoSeriesColorDataColumn;
        private static getAutoSeriesTitleFromAutoSeriesTitleDataColumn;
        private static getAutoSeriesKeysFromAutoSeriesKeyDataColumn;
        private static setTitle;
        private static setCutPieList;
        private static getArguments;
        private static getArgumentsFromArgumentExpression;
        private static getArgumentsFromArgumentDataColumn;
        private static getArgumentsFromListOfArguments;
        private static getValues;
        private static getValuesFromValueExpression;
        private static getValuesFromValueDataColumn;
        private static getValuesFromListOfValues;
        private static getValuesEnd;
        private static getValuesEndFromValueEndExpression;
        private static getValuesEndFromValueDataColumnEnd;
        private static getValuesEndFromListOfValuesEnd;
        private static getValuesOpen;
        private static getValuesOpenFromValuesOpenExpression;
        private static getValuesOpenFromValueDataColumnOpen;
        private static getValuesOpenFromListOfValuesOpen;
        private static getValuesClose;
        private static getValuesCloseFromValuesCloseExpression;
        private static getValuesCloseFromValueDataColumnClose;
        private static getValuesCloseFromListOfValuesClose;
        private static getValuesHigh;
        private static getValuesHighFromValuesHighExpression;
        private static getValuesHighFromValueDataColumnHigh;
        private static getValuesHighFromListOfValuesHigh;
        private static getValuesLow;
        private static getValuesLowFromValuesLowExpression;
        private static getValuesLowFromValueDataColumnLow;
        private static getValuesLowFromListOfValuesLow;
        private static getWeights;
        private static getWeightsWeightExpression;
        private static getWeightsFromWeightDataColumn;
        private static getWeightsFromListOfWeights;
        private static getHyperlinks;
        private static getHyperlinksFromHyperlinkExpression;
        private static getHyperlinksFromHyperlinkDataColumn;
        private static getHyperlinksFromListOfHyperlinks;
        private static getTags;
        private static getTagsFromTagExpression;
        private static getTagsFromTagDataColumn;
        private static getTagsFromListOfTags;
        private static getToolTips;
        private static getToolTipsFromToolTipExpression;
        private static getToolTipsFromToolTipDataColumn;
        private static getToolTipsFromListOfToolTips;
        static getAnimationСompatibilitySeries(series1: IStiSeries, series2: IStiSeries): boolean;
        private static getAnimationSeriesType;
    }
}
declare namespace Stimulsoft.Report.Events {
    class StiProcessChartEvent extends StiEvent {
        toString(): string;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiService = Stimulsoft.Base.Services.StiService;
    class StiArea extends StiService implements IStiJsonReportObject, IStiArea, ICloneable {
        private static implementsStiArea;
        implements(): any[];
        meta(): StiMeta[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        static loadFromJsonObjectInternal(jObject: StiJson): IStiArea;
        static loadAreaFromXml(xmlNode: XmlNode, chart: Stimulsoft.Report.Components.StiChart): StiArea;
        get componentId(): StiComponentId;
        get propName(): string;
        clone(): StiArea;
        createNew(): StiArea;
        toString(): string;
        getDefaultSeriesType(): Stimulsoft.System.Type;
        getSeriesTypes(): Stimulsoft.System.Type[];
        getDefaultSeriesLabelsType(): Stimulsoft.System.Type;
        getSeriesLabelsTypes(): Stimulsoft.System.Type[];
        get serviceCategory(): string;
        get serviceType(): Stimulsoft.System.Type;
        get isDefaultSeriesTypeFullStackedColumnSeries(): boolean;
        get isDefaultSeriesTypeFullStackedBarSeries(): boolean;
        core: StiAreaCoreXF;
        chart: IStiChart;
        private _allowApplyStyle;
        get allowApplyStyle(): boolean;
        set allowApplyStyle(value: boolean);
        colorEach: boolean;
        showShadow: boolean;
        borderColor: Color;
        borderThickness: number;
        brush: StiBrush;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiRadarArea extends StiArea implements IStiJsonReportObject, IStiRadarArea, IStiArea, ICloneable {
        private static implementsStiRadarArea;
        implements(): any[];
        meta(): StiMeta[];
        clone(): StiRadarArea;
        getDefaultSeriesLabelsType(): Stimulsoft.System.Type;
        getSeriesLabelsTypes(): Stimulsoft.System.Type[];
        private _interlacingHor;
        get interlacingHor(): IStiInterlacingHor;
        set interlacingHor(value: IStiInterlacingHor);
        private _interlacingVert;
        get interlacingVert(): IStiInterlacingVert;
        set interlacingVert(value: IStiInterlacingVert);
        private _gridLinesHor;
        get gridLinesHor(): IStiRadarGridLinesHor;
        set gridLinesHor(value: IStiRadarGridLinesHor);
        private _gridLinesVert;
        get gridLinesVert(): IStiRadarGridLinesVert;
        set gridLinesVert(value: IStiRadarGridLinesVert);
        radarStyle: StiRadarStyle;
        private _xAxis;
        get xAxis(): IStiXRadarAxis;
        set xAxis(value: IStiXRadarAxis);
        private _yAxis;
        get yAxis(): IStiYRadarAxis;
        set yAxis(value: IStiYRadarAxis);
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiChartCoreXF implements ICloneable, IStiApplyStyle, IStiChartCoreXF {
        private static implementsStiChartCoreXF;
        implements(): any[];
        clone(): any;
        applyStyle(style: IStiChartStyle): void;
        render(context: StiContext, rect: RectangleD, useMargins: boolean): StiCellGeom;
        private setLegendRect;
        chart: IStiChart;
        fullRectangle: RectangleD;
        constructor(chart: IStiChart);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import Color = Stimulsoft.System.Drawing.Color;
    import Font = Stimulsoft.System.Drawing.Font;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiChartTable implements IStiJsonReportObject, IStiChartTable, ICloneable {
        private static implementsStiChartTable;
        implements(): any[];
        protected _hash: StiMeta[];
        meta(): StiMeta[];
        saveToJsonObject(m: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        get componentId(): StiComponentId;
        get propName(): string;
        clone(): StiChartTable;
        get font(): Font;
        set font(value: Font);
        visible: boolean;
        allowApplyStyle: boolean;
        markerVisible: boolean;
        gridLineColor: Color;
        get textColor(): Color;
        set textColor(value: Color);
        gridLinesHor: boolean;
        gridLinesVert: boolean;
        gridOutline: boolean;
        format: string;
        header: IStiChartTableHeader;
        core: StiChartTableCoreXF;
        dataCells: StiChartTableDataCells;
        chart: IStiChart;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StringAlignment = Stimulsoft.System.Drawing.StringAlignment;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Font = Stimulsoft.System.Drawing.Font;
    class StiChartTitle implements IStiChartTitle, ICloneable, IStiJsonReportObject {
        private static implementsStiChartTitle;
        implements(): any[];
        protected _hash: StiMeta[];
        meta(): StiMeta[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        get componentId(): StiComponentId;
        get propName(): string;
        clone(): StiChartTitle;
        private _allowApplyStyle;
        get allowApplyStyle(): boolean;
        set allowApplyStyle(value: boolean);
        core: StiChartTitleCoreXF;
        font: Font;
        text: string;
        brush: StiBrush;
        antialiasing: boolean;
        alignment: StringAlignment;
        dock: StiChartTitleDock;
        spacing: number;
        visible: boolean;
        chart: IStiChart;
        constructor(font?: Font, text?: string, brush?: StiBrush, antialiasing?: boolean, alignment?: StringAlignment, dock?: StiChartTitleDock, spacing?: number, visible?: boolean, allowApplyStyle?: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiChart = Stimulsoft.Report.Components.StiChart;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import CollectionBase = Stimulsoft.System.Collections.CollectionBase;
    class StiStripsCollection extends CollectionBase<IStiStrips> implements IStiJsonReportObject, IStiApplyStyle, IStiStripsCollection {
        private static implementsStiStripsCollection;
        implements(): any[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(jObject: StiJson): void;
        loadFromXml(xmlNode: XmlNode): void;
        applyStyle(style: IStiChartStyle): void;
        private getStripsTitle;
        add(value: IStiStrips): void;
        chart: StiChart;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiChart = Stimulsoft.Report.Components.StiChart;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import CollectionBase = Stimulsoft.System.Collections.CollectionBase;
    class StiConstantLinesCollection extends CollectionBase<IStiConstantLines> implements IStiJsonReportObject, IStiApplyStyle, IStiConstantLinesCollection {
        private static implementsStiConstantLinesCollection;
        implements(): any[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(jObject: StiJson): void;
        loadFromXml(xmlNode: XmlNode): void;
        applyStyle(style: IStiChartStyle): void;
        private getConstantLineTitle;
        add(value: IStiConstantLines): void;
        chart: StiChart;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import SizeD = Stimulsoft.System.Drawing.Size;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiService = Stimulsoft.Base.Services.StiService;
    import Font = Stimulsoft.System.Drawing.Font;
    import StiFormatService = Stimulsoft.Report.Components.TextFormats.StiFormatService;
    class StiSeriesLabels extends StiService implements IStiJsonReportObject, IStiSeriesLabels, ICloneable {
        private static implementsStiSeriesLabels;
        implements(): any[];
        meta(): StiMeta[];
        saveToJsonObject(m: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        static loadFromJsonObjectInternal(jObject: StiJson, chart: IStiChart): IStiSeriesLabels;
        static loadLabelsFromXml(xmlNode: XmlNode, chart: IStiChart): StiSeriesLabels;
        get componentId(): StiComponentId;
        get propName(): string;
        clone(): StiSeriesLabels;
        get serviceName(): string;
        get serviceCategory(): string;
        get serviceType(): Stimulsoft.System.Type;
        preventIntersection: boolean;
        core: StiSeriesLabelsCoreXF;
        get axisCore(): StiAxisSeriesLabelsCoreXF;
        get pieCore(): StiPieSeriesLabelsCoreXF;
        private _allowApplyStyle;
        get allowApplyStyle(): boolean;
        set allowApplyStyle(value: boolean);
        get conditions(): StiChartConditionsCollection;
        set conditions(value: StiChartConditionsCollection);
        get showOnZeroValues(): boolean;
        set showOnZeroValues(value: boolean);
        showZeros: boolean;
        showNulls: boolean;
        markerVisible: boolean;
        markerSize: SizeD;
        markerAlignment: StiMarkerAlignment;
        step: number;
        valueType: StiSeriesLabelsValueType;
        valueTypeSeparator: string;
        legendValueType: StiSeriesLabelsValueType;
        textBefore: string;
        textAfter: string;
        angle: number;
        format: string;
        antialiasing: boolean;
        visible: boolean;
        drawBorder: boolean;
        useSeriesColor: boolean;
        labelColor: Color;
        borderColor: Color;
        brush: StiBrush;
        font: Font;
        chart: IStiChart;
        wordWrap: boolean;
        width: number;
        formatService: StiFormatService;
        toString(): string;
        createNew(): StiSeriesLabels;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiAxisSeriesLabels extends StiSeriesLabels implements IStiSeriesLabels, ICloneable, IStiAxisSeriesLabels {
        private static implementsStiAxisSeriesLabels;
        implements(): any[];
        meta(): StiMeta[];
        private _showInPercent;
        get showInPercent(): boolean;
        set showInPercent(value: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiCenterAxisLabels extends StiAxisSeriesLabels implements IStiJsonReportObject, IStiSeriesLabels, IStiCenterAxisLabels, IStiAxisSeriesLabels, ICloneable {
        private static implementsStiCenterAxisLabels;
        implements(): any[];
        get componentId(): StiComponentId;
        createNew(): StiSeriesLabels;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiPenStyle = Stimulsoft.Base.Drawing.StiPenStyle;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiAxis implements IStiAxis, IStiJsonReportObject {
        private static implementsStiAxis;
        implements(): any[];
        is<T>(type: (new (...args: any[]) => T) | Stimulsoft.System.Interface<T>): this is T;
        is2<T>(type: (new (...args: any[]) => T) | Stimulsoft.System.Interface<T>): boolean;
        as<T>(type: (new (...args: any[]) => T) | Stimulsoft.System.Interface<T>): T;
        protected _hash: StiMeta[];
        meta(): StiMeta[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        clone(): StiAxis;
        private _allowApplyStyle;
        get allowApplyStyle(): boolean;
        set allowApplyStyle(value: boolean);
        get step(): number;
        set step(value: number);
        private _title;
        get title(): IStiAxisTitle;
        set title(value: IStiAxisTitle);
        get titleDirection(): StiLegendDirection;
        set titleDirection(value: StiLegendDirection);
        logarithmicScale: boolean;
        core: StiAxisCoreXF;
        startFromZero: boolean;
        interaction: IStiAxisInteraction;
        labels: IStiAxisLabels;
        range: IStiAxisRange;
        ticks: IStiAxisTicks;
        arrowStyle: StiArrowStyle;
        lineStyle: StiPenStyle;
        lineColor: Color;
        lineWidth: number;
        visible: boolean;
        area: IStiAxisArea;
        info: StiAxisInfoXF;
        constructor(labels?: IStiAxisLabels, range?: IStiAxisRange, title?: IStiAxisTitle, ticks?: IStiAxisTicks, interaction?: IStiAxisInteraction, arrowStyle?: StiArrowStyle, lineStyle?: StiPenStyle, lineColor?: Color, lineWidth?: number, visible?: boolean, startFromZero?: boolean, allowApplyStyle?: boolean, logarithmicScale?: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiPenStyle = Stimulsoft.Base.Drawing.StiPenStyle;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiYAxis extends StiAxis implements IStiJsonReportObject, IStiYAxis, ICloneable, IStiAxis {
        private static implementsStiYAxis;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        get propName(): string;
        showYAxis: StiShowYAxis;
        constructor(labels?: IStiAxisLabels, range?: IStiAxisRange, title?: IStiAxisTitle, ticks?: IStiAxisTicks, interaction?: IStiAxisInteraction, arrowStyle?: StiArrowStyle, lineStyle?: StiPenStyle, lineColor?: Color, lineWidth?: number, visible?: boolean, startFromZero?: boolean, showYAxis?: StiShowYAxis, allowApplyStyle?: boolean, logarithmicScale?: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiPenStyle = Stimulsoft.Base.Drawing.StiPenStyle;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiYRightAxis extends StiYAxis implements IStiJsonReportObject, IStiYAxis, ICloneable, IStiAxis, IStiYRightAxis {
        private static implementsStiYRightAxis;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        constructor(labels?: IStiAxisLabels, range?: IStiAxisRange, title?: IStiAxisTitle, ticks?: IStiAxisTicks, interaction?: IStiAxisInteraction, arrowStyle?: StiArrowStyle, lineStyle?: StiPenStyle, lineColor?: Color, lineWidth?: number, visible?: boolean, startFromZero?: boolean, allowApplyStyle?: boolean, logarithmicScale?: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiPenStyle = Stimulsoft.Base.Drawing.StiPenStyle;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiXAxis extends StiAxis implements IStiJsonReportObject, IStiAxis, IStiXAxis, ICloneable {
        private static implementsStiXAxis;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        get propName(): string;
        showEdgeValues: boolean;
        showXAxis: StiShowXAxis;
        dateTimeStep: IStiAxisDateTimeStep;
        constructor(labels?: IStiAxisLabels, range?: IStiAxisRange, title?: IStiAxisTitle, ticks?: IStiAxisTicks, interaction?: IStiAxisInteraction, arrowStyle?: StiArrowStyle, lineStyle?: StiPenStyle, lineColor?: Color, lineWidth?: number, visible?: boolean, startFromZero?: boolean, showXAxis?: StiShowXAxis, showEdgeValues?: boolean, allowApplyStyle?: boolean, dateTimeStep?: IStiAxisDateTimeStep, logarithmicScale?: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiPenStyle = Stimulsoft.Base.Drawing.StiPenStyle;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiXTopAxis extends StiXAxis implements IStiXTopAxis, ICloneable, IStiAxis, IStiXAxis, IStiJsonReportObject {
        private static implementsStiXTopAxis;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        constructor(labels?: IStiAxisLabels, range?: IStiAxisRange, title?: IStiAxisTitle, ticks?: IStiAxisTicks, interaction?: IStiAxisInteraction, arrowStyle?: StiArrowStyle, lineStyle?: StiPenStyle, lineColor?: Color, lineWidth?: number, visible?: boolean, startFromZero?: boolean, showXAxis?: StiShowXAxis, showEdgeValues?: boolean, allowApplyStyle?: boolean, logarithmicScale?: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiPenStyle = Stimulsoft.Base.Drawing.StiPenStyle;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiYLeftAxis extends StiYAxis implements IStiJsonReportObject, IStiYAxis, ICloneable, IStiAxis, IStiYLeftAxis {
        private static implementsStiYLeftAxis;
        implements(): any[];
        constructor(labels?: IStiAxisLabels, range?: IStiAxisRange, title?: IStiAxisTitle, ticks?: IStiAxisTicks, interaction?: IStiAxisInteraction, arrowStyle?: StiArrowStyle, lineStyle?: StiPenStyle, lineColor?: Color, lineWidth?: number, visible?: boolean, startFromZero?: boolean, showYAxis?: StiShowYAxis, allowApplyStyle?: boolean, logarithmicScale?: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import StiFormatService = Stimulsoft.Report.Components.TextFormats.StiFormatService;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiHorAlignment = Stimulsoft.Base.Drawing.StiHorAlignment;
    import Color = Stimulsoft.System.Drawing.Color;
    import Font = Stimulsoft.System.Drawing.Font;
    class StiAxisLabels implements IStiJsonReportObject, IStiAxisLabels, ICloneable {
        private static implementsStiAxisLabels;
        implements(): any[];
        protected _hash: StiMeta[];
        meta(): StiMeta[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        clone(): StiAxisLabels;
        core: StiAxisLabelsCoreXF;
        allowApplyStyle: boolean;
        format: string;
        angle: number;
        width: number;
        textBefore: string;
        textAfter: string;
        font: Font;
        antialiasing: boolean;
        placement: StiLabelsPlacement;
        color: Color;
        textAlignment: StiHorAlignment;
        step: number;
        wordWrap: boolean;
        calculatedStep: number;
        formatService: StiFormatService;
        constructor(format?: string, textBefore?: string, textAfter?: string, angle?: number, font?: Font, antialiasing?: boolean, placement?: StiLabelsPlacement, color?: Color, width?: number, textAlignment?: StiHorAlignment, step?: number, allowApplyStyle?: boolean, wordWrap?: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiAxisRange implements IStiJsonReportObject, ICloneable, IStiAxisRange {
        private static implementsStiAxisRange;
        implements(): any[];
        protected _hash: StiMeta[];
        meta(): StiMeta[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        clone(): StiAxisRange;
        minimum: number;
        maximum: number;
        auto: boolean;
        constructor(auto?: boolean, minimum?: number, maximum?: number);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiAxisTicks implements IStiJsonReportObject, IStiAxisTicks, ICloneable {
        private static implementsStiAxisTicks;
        implements(): any[];
        protected _hash: StiMeta[];
        meta(): StiMeta[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        clone(): StiAxisTicks;
        private _lengthUnderLabels;
        get lengthUnderLabels(): number;
        set lengthUnderLabels(value: number);
        private _length;
        get length(): number;
        set length(value: number);
        private _minorLength;
        get minorLength(): number;
        set minorLength(value: number);
        private _minorCount;
        get minorCount(): number;
        set minorCount(value: number);
        private _step;
        get step(): number;
        set step(value: number);
        minorVisible: boolean;
        visible: boolean;
        constructor(visible?: boolean, length?: number, minorVisible?: boolean, minorLength?: number, minorCount?: number, step?: number, lengthUnderLabels?: number);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiAxisInteraction implements IStiJsonReportObject, IStiAxisInteraction, ICloneable {
        private static implementsStiAxisInteraction;
        implements(): any[];
        protected _hash: StiMeta[];
        meta(): StiMeta[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        clone(): StiAxisInteraction;
        showScrollBar: boolean;
        rangeScrollEnabled: boolean;
        constructor(showScrollBar?: boolean, rangeScrollEnabled?: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    class StiAxisDateTimeStep implements IStiAxisDateTimeStep, IStiJsonReportObject {
        private static implementsStiAxisDateTimeStep;
        implements(): any[];
        protected _hash: StiMeta[];
        meta(): StiMeta[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        clone(): StiAxisDateTimeStep;
        step: StiTimeDateStep;
        numberOfValues: number;
        interpolation: boolean;
        constructor(step?: StiTimeDateStep, numberOfValues?: number, interpolation?: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiPenStyle = Stimulsoft.Base.Drawing.StiPenStyle;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiXBottomAxis extends StiXAxis implements IStiJsonReportObject, IStiXAxis, ICloneable, IStiXBottomAxis, IStiAxis {
        private static implementsStiXBottomAxis;
        implements(): any[];
        constructor(labels?: IStiAxisLabels, range?: IStiAxisRange, title?: IStiAxisTitle, ticks?: IStiAxisTicks, interaction?: IStiAxisInteraction, arrowStyle?: StiArrowStyle, lineStyle?: StiPenStyle, lineColor?: Color, lineWidth?: number, visible?: boolean, startFromZero?: boolean, showXAxis?: StiShowXAxis, showEdgeValues?: boolean, allowApplyStyle?: boolean, dateTimeStep?: IStiAxisDateTimeStep, logarithmicScale?: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiPenStyle = Stimulsoft.Base.Drawing.StiPenStyle;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiGridLines implements IStiJsonReportObject, IStiGridLines, ICloneable {
        private static implementsStiGridLines;
        implements(): any[];
        protected _hash: StiMeta[];
        meta(): StiMeta[];
        needSetAreaJsonPropertyInternal: boolean;
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        get componentId(): StiComponentId;
        get propName(): string;
        clone(): StiGridLines;
        private _allowApplyStyle;
        get allowApplyStyle(): boolean;
        set allowApplyStyle(value: boolean);
        core: StiGridLinesCoreXF;
        color: Color;
        minorColor: Color;
        style: StiPenStyle;
        minorStyle: StiPenStyle;
        visible: boolean;
        minorVisible: boolean;
        area: IStiArea;
        private _minorCount;
        get minorCount(): number;
        set minorCount(value: number);
        constructor(color?: Color, style?: StiPenStyle, visible?: boolean, minorColor?: Color, minorStyle?: StiPenStyle, minorVisible?: boolean, minorCount?: number, allowApplyStyle?: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiPenStyle = Stimulsoft.Base.Drawing.StiPenStyle;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiGridLinesVert extends StiGridLines implements IStiJsonReportObject, IStiGridLines, ICloneable, IStiGridLinesVert {
        private static implementsStiGridLinesVert;
        implements(): any[];
        constructor(color?: Color, style?: StiPenStyle, visible?: boolean, minorColor?: Color, minorStyle?: StiPenStyle, minorVisible?: boolean, minorCount?: number, allowApplyStyle?: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiPenStyle = Stimulsoft.Base.Drawing.StiPenStyle;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiGridLinesHor extends StiGridLines implements IStiJsonReportObject, IStiGridLines, IStiGridLinesHor, ICloneable {
        private static implementsStiGridLinesHor;
        implements(): any[];
        constructor(color?: Color, style?: StiPenStyle, visible?: boolean, minorColor?: Color, minorStyle?: StiPenStyle, minorVisible?: boolean, minorCount?: number, allowApplyStyle?: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    class StiInterlacing implements IStiInterlacing, ICloneable, IStiJsonReportObject {
        private static implementsStiInterlacing;
        implements(): any[];
        is<T>(type: (new (...args: any[]) => T) | Stimulsoft.System.Interface<T>): this is T;
        is2<T>(type: (new (...args: any[]) => T) | Stimulsoft.System.Interface<T>): boolean;
        as<T>(type: (new (...args: any[]) => T) | Stimulsoft.System.Interface<T>): T;
        protected _hash: StiMeta[];
        meta(): StiMeta[];
        needSetAreaJsonPropertyInternal: boolean;
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        get componentId(): StiComponentId;
        get propName(): string;
        clone(): StiInterlacing;
        private _allowApplyStyle;
        get allowApplyStyle(): boolean;
        set allowApplyStyle(value: boolean);
        core: StiInterlacingCoreXF;
        interlacedBrush: StiBrush;
        visible: boolean;
        area: IStiArea;
        constructor(interlacedBrush?: StiBrush, visible?: boolean, allowApplyStyle?: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    class StiInterlacingVert extends StiInterlacing implements IStiInterlacingVert {
        private static implementsStiInterlacingVert;
        implements(): any[];
        constructor(interlacedBrush?: StiBrush, visible?: boolean, allowApplyStyle?: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    class StiInterlacingHor extends StiInterlacing implements IStiInterlacing, IStiInterlacingHor, IStiJsonReportObject, ICloneable {
        private static implementsStiInterlacingHor;
        implements(): any[];
        constructor(interlacedBrush?: StiBrush, visible?: boolean, allowApplyStyle?: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiAxisArea extends StiArea implements IStiJsonReportObject, IStiAxisArea, IStiArea, ICloneable {
        private static implementsStiAxisArea;
        implements(): any[];
        meta(): StiMeta[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        clone(): StiAxisArea;
        get axisCore(): StiAxisAreaCoreXF;
        private _interlacingHor;
        get interlacingHor(): IStiInterlacingHor;
        set interlacingHor(value: IStiInterlacingHor);
        private _interlacingVert;
        get interlacingVert(): IStiInterlacingVert;
        set interlacingVert(value: IStiInterlacingVert);
        private _gridLinesHor;
        get gridLinesHor(): IStiGridLinesHor;
        set gridLinesHor(value: IStiGridLinesHor);
        private _gridLinesHorRight;
        get gridLinesHorRight(): IStiGridLinesHor;
        set gridLinesHorRight(value: IStiGridLinesHor);
        private _gridLinesVert;
        get gridLinesVert(): IStiGridLinesVert;
        set gridLinesVert(value: IStiGridLinesVert);
        private _yAxis;
        get yAxis(): IStiYAxis;
        set yAxis(value: IStiYAxis);
        private _yRightAxis;
        get yRightAxis(): IStiYAxis;
        set yRightAxis(value: IStiYAxis);
        private _xAxis;
        get xAxis(): IStiXAxis;
        set xAxis(value: IStiXAxis);
        private _xTopAxis;
        get xTopAxis(): IStiXAxis;
        set xTopAxis(value: IStiXAxis);
        reverseHor: boolean;
        reverseVert: boolean;
        getDefaultSeriesLabelsType(): Stimulsoft.System.Type;
        getSeriesLabelsTypes(): Stimulsoft.System.Type[];
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiClusteredColumnArea extends StiAxisArea implements IStiJsonReportObject, IStiClusteredColumnArea, IStiAxisArea, IStiRoundValuesArea, ICloneable, IStiArea {
        private static implementsStiClusteredColumnArea;
        implements(): any[];
        meta(): StiMeta[];
        roundValues: boolean;
        sideBySide: boolean;
        get componentId(): StiComponentId;
        getDefaultSeriesType(): Stimulsoft.System.Type;
        getSeriesTypes(): Stimulsoft.System.Type[];
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiChart = Stimulsoft.Report.Components.StiChart;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import CollectionBase = Stimulsoft.System.Collections.CollectionBase;
    class StiSeriesCollection extends CollectionBase<IStiSeries> implements IStiJsonReportObject, IStiApplyStyle, IStiSeriesCollection {
        private static implementsStiSeriesCollection;
        implements(): any[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(jObject: StiJson): void;
        loadFromXml(xmlNode: XmlNode, chart: StiChart): void;
        applyStyle(style: IStiChartStyle): void;
        private getSeriesTitle;
        add(value: IStiSeries): void;
        insert(index: number, value: IStiSeries): void;
        remove(item: IStiSeries): void;
        removeAt(index: number): void;
        getByName(name: string): IStiSeries;
        setByName(name: string, value: IStiSeries): void;
        seriesAdded: Function;
        private invokeSeriesAdded;
        seriesRemoved: Function;
        private invokeSeriesRemoved;
        chart: StiChart;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiFilterItem = Stimulsoft.Report.Components.StiFilterItem;
    import StiFilterDataType = Stimulsoft.Report.Components.StiFilterDataType;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiFilterCondition = Stimulsoft.Report.Components.StiFilterCondition;
    class StiChartFilter implements IStiJsonReportObject, IStiChartFilter, ICloneable {
        private static implementsStiChartFilter;
        implements(): any[];
        protected _hash: StiMeta[];
        meta(): StiMeta[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        clone(): StiChartFilter;
        get index(): number;
        condition: StiFilterCondition;
        dataType: StiFilterDataType;
        item: StiFilterItem;
        private _valueObj;
        get value(): string;
        set value(value: string);
        toString(): string;
        filters: StiChartFiltersCollection;
        constructor(item?: StiFilterItem, dataType?: StiFilterDataType, condition?: StiFilterCondition, value?: string);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiFilterCondition = Stimulsoft.Report.Components.StiFilterCondition;
    import StiFilterDataType = Stimulsoft.Report.Components.StiFilterDataType;
    import StiFilterItem = Stimulsoft.Report.Components.StiFilterItem;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiChartCondition extends StiChartFilter implements IStiChartCondition, IStiChartFilter, IStiJsonReportObject {
        private static implementsStiChartCondition;
        implements(): any[];
        meta(): StiMeta[];
        clone(): StiChartCondition;
        color: Color;
        markerType: StiMarkerType;
        markerAngle: number;
        conditions: StiChartConditionsCollection;
        constructor(color?: Color, item?: StiFilterItem, dataType?: StiFilterDataType, condition?: StiFilterCondition, value?: string, markerType?: StiMarkerType, markerAngle?: number);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import CollectionBase = Stimulsoft.System.Collections.CollectionBase;
    class StiChartConditionsCollection extends CollectionBase<StiChartCondition> implements IStiJsonReportObject, ICloneable, IStiChartConditionsCollection {
        private static implementsStiChartConditionsCollection;
        implements(): any[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(jObject: StiJson): void;
        loadFromXml(xmlNode: XmlNode): void;
        clone(): StiChartConditionsCollection;
        add(condition: StiChartCondition): void;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiNewAutoSeriesEventArgs = Stimulsoft.Report.Events.StiNewAutoSeriesEventArgs;
    import StiGetTitleEventArgs = Stimulsoft.Report.Events.StiGetTitleEventArgs;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiGetValueEventArgs = Stimulsoft.Report.Events.StiGetValueEventArgs;
    import StiValueEventArgs = Stimulsoft.Report.Events.StiValueEventArgs;
    import StiFilterMode = Stimulsoft.Report.Components.StiFilterMode;
    import StiPage = Stimulsoft.Report.Components.StiPage;
    import StiComponent = Stimulsoft.Report.Components.StiComponent;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiService = Stimulsoft.Base.Services.StiService;
    class StiSeries extends StiService implements IStiJsonReportObject, ICloneable, IStiSeries, IStiJsonReportObject {
        private static implementsStiSeries;
        implements(): any[];
        meta(): StiMeta[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        get componentId(): StiComponentId;
        get propName(): string;
        clone(): StiSeries;
        private static storedCulture;
        baseTransform(): void;
        get parent(): StiComponent;
        get serviceName(): string;
        get serviceCategory(): string;
        get serviceType(): Stimulsoft.System.Type;
        allowApplyStyle: boolean;
        core: StiSeriesCoreXF;
        format: string;
        sortBy: StiSeriesSortType;
        sortDirection: StiSeriesSortDirection;
        showInLegend: boolean;
        get showLabels(): boolean;
        set showLabels(value: boolean);
        private _showSeriesLabels;
        get showSeriesLabels(): StiShowSeriesLabels;
        set showSeriesLabels(value: StiShowSeriesLabels);
        showShadow: boolean;
        filterMode: StiFilterMode;
        filters: StiChartFiltersCollection;
        conditions: StiChartConditionsCollection;
        topN: IStiSeriesTopN;
        yAxis: StiSeriesYAxis;
        private _seriesLabels;
        get seriesLabels(): IStiSeriesLabels;
        set seriesLabels(value: IStiSeriesLabels);
        get trendLine(): IStiTrendLine;
        set trendLine(value: IStiTrendLine);
        private _chart;
        get chart(): IStiChart;
        set chart(value: IStiChart);
        trendLines: StiTrendLinesCollection;
        isTotalLabel: boolean;
        valuesStart: number[];
        private _values;
        get values(): number[];
        set values(value: number[]);
        valueDataColumn: string;
        get valuesString(): string;
        set valuesString(value: string);
        originalArguments: any[];
        private _arguments;
        get arguments(): any[];
        set arguments(value: any[]);
        protected getArguments(): any[];
        protected setArguments(value: any[]): void;
        argumentDataColumn: string;
        get argumentsString(): string;
        set argumentsString(value: string);
        autoSeriesTitleDataColumn: string;
        autoSeriesKeyDataColumn: string;
        autoSeriesColorDataColumn: string;
        private _toolTips;
        get toolTips(): string[];
        set toolTips(value: string[]);
        toolTipDataColumn: string;
        get toolTipsString(): string;
        set toolTipsString(value: string);
        private _tags;
        get tags(): any[];
        set tags(value: any[]);
        tagDataColumn: string;
        get tagString(): string;
        set tagString(value: string);
        private _hyperlinks;
        get hyperlinks(): string[];
        set hyperlinks(value: string[]);
        hyperlinkDataColumn: string;
        get hyperlinkString(): string;
        set hyperlinkString(value: string);
        drillDownEnabled: boolean;
        drillDownReport: string;
        get drillDownPage(): StiPage;
        set drillDownPage(value: StiPage);
        drillDownPageGuid: string;
        allowSeries: boolean;
        allowSeriesElements: boolean;
        get coreTitle(): string;
        set coreTitle(value: string);
        isDashboard: boolean;
        legendColor: Color;
        private _interaction;
        get interaction(): IStiSeriesInteraction;
        set interaction(value: IStiSeriesInteraction);
        processSeriesColors(pointIndex: number, seriesColor: Color): Color;
        processSeriesMarkerType(pointIndex: number, markerType: StiMarkerType): StiMarkerType;
        processSeriesMarkerAngle(pointIndex: number, markerAngle: number): number;
        processSeriesMarkerVisible(pointIndex: number): boolean;
        processSeriesBrushes(pointIndex: number, seriesBrush: StiBrush): StiBrush;
        private getConditionResult;
        toString(): string;
        static tryParseValue(value: string, culture: string, refResult: {
            ref: number;
        }): boolean;
        static getNullableValuesFromString(series: StiSeries, list: string): number[];
        static getValuesFromString(list: string): number[];
        static getStringsFromString(list: string): string[];
        static getArgumentsFromString(list: string): any[];
        protected getOffsetForValues(): number;
        createNew(): StiSeries;
        getDefaultAreaType(): Stimulsoft.System.Type;
        newAutoSeries: Function;
        invokeNewAutoSeries(e: StiNewAutoSeriesEventArgs): void;
        getValue: Function;
        protected onGetValue(e: StiGetValueEventArgs): void;
        invokeGetValue(sender: StiComponent, e: StiGetValueEventArgs): void;
        getListOfValues: Function;
        protected onGetListOfValues(e: StiGetValueEventArgs): void;
        invokeGetListOfValues(sender: StiComponent, e: StiGetValueEventArgs, series: StiSeries): void;
        getArgument: Function;
        protected onGetArgument(e: StiValueEventArgs): void;
        invokeGetArgument(sender: StiComponent, e: StiValueEventArgs): void;
        getListOfArguments: Function;
        protected onGetListOfArguments(e: StiGetValueEventArgs): void;
        invokeGetListOfArguments(sender: StiComponent, e: StiGetValueEventArgs): void;
        getTitle: Function;
        protected onGetTitle(e: StiGetTitleEventArgs): void;
        invokeGetTitle(sender: StiComponent, e: StiGetTitleEventArgs): void;
        getToolTip: Function;
        protected onGetToolTip(e: StiValueEventArgs): void;
        invokeGetToolTip(sender: any, e: StiValueEventArgs): void;
        getListOfToolTips: Function;
        protected onGetListOfToolTips(e: StiGetValueEventArgs): void;
        invokeGetListOfToolTips(sender: StiComponent, e: StiGetValueEventArgs): void;
        getTag: Function;
        protected onGetTag(e: StiValueEventArgs): void;
        invokeGetTag(sender: any, e: StiValueEventArgs): void;
        getListOfTags: Function;
        protected onGetListOfTags(e: StiGetValueEventArgs): void;
        invokeGetListOfTags(sender: StiComponent, e: StiGetValueEventArgs): void;
        getHyperlink: Function;
        protected onGetHyperlink(e: StiValueEventArgs): void;
        invokeGetHyperlink(sender: any, e: StiValueEventArgs): void;
        getListOfHyperlinks: Function;
        protected onGetListOfHyperlinks(e: StiGetValueEventArgs): void;
        invokeGetListOfHyperlinks(sender: StiComponent, e: StiGetValueEventArgs): void;
        private valueObj;
        get value(): string;
        set value(value: string);
        listOfValues: string;
        argument: string;
        listOfArguments: string;
        titleValue: string;
        title: string;
        toolTip: string;
        listOfToolTips: string;
        tag: string;
        listOfTags: string;
        hyperlink: string;
        listOfHyperlinks: string;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiChart = Stimulsoft.Report.Components.StiChart;
    import StiComponentInfo = Stimulsoft.Report.Engine.StiComponentInfo;
    import StiText = Stimulsoft.Report.Components.StiText;
    class StiChartInfo extends StiComponentInfo implements IStiChartInfo {
        private static implementsStiChartInfo;
        implements(): any[];
        storedForProcessAtEndChart: StiChart;
        interactiveComps: StiText[];
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiBaseStyle = Stimulsoft.Report.Styles.StiBaseStyle;
    import StiElementStyleIdent = Stimulsoft.Report.Dashboard.StiElementStyleIdent;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiChartStyle extends StiBaseStyle implements IStiJsonReportObject, IStiChartStyle, ICloneable {
        private static implementsStiChartStyle;
        implements(): any[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(jObject: StiJson): void;
        static loadFromXml(xmlNode: XmlNode): StiChartStyle;
        static loadFromJsonObjectInternal(jObject: StiJson): StiChartStyle;
        get serviceName(): string;
        get serviceCategory(): string;
        get serviceType(): Stimulsoft.System.Type;
        isOffice2015Style: boolean;
        allowDashboard: boolean;
        styleIdent: StiElementStyleIdent;
        core: StiStyleCoreXF;
        toString(): string;
        compareChartStyle(style: StiChartStyle): boolean;
        createNew(): StiChartStyle;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiElementStyleIdent = Stimulsoft.Report.Dashboard.StiElementStyleIdent;
    class StiStyle25 extends StiChartStyle {
        allowDashboard: boolean;
        get dashboardName(): string;
        styleIdent: StiElementStyleIdent;
        isOffice2015Style: boolean;
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiElementStyleIdent = Stimulsoft.Report.Dashboard.StiElementStyleIdent;
    class StiStyle29 extends StiChartStyle {
        allowDashboard: boolean;
        get dashboardName(): string;
        styleIdent: StiElementStyleIdent;
        isOffice2015Style: boolean;
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import SizeD = Stimulsoft.System.Drawing.Size;
    import Font = Stimulsoft.System.Drawing.Font;
    class StiLegend implements IStiJsonReportObject, ICloneable, IStiLegend {
        private static implementsStiLegend;
        implements(): any[];
        protected _hash: StiMeta[];
        meta(): StiMeta[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        get componentId(): StiComponentId;
        get propName(): string;
        clone(): StiLegend;
        private _allowApplyStyle;
        get allowApplyStyle(): boolean;
        set allowApplyStyle(value: boolean);
        core: StiLegendCoreXF;
        chart: IStiChart;
        hideSeriesWithEmptyTitle: boolean;
        showShadow: boolean;
        borderColor: Color;
        brush: StiBrush;
        titleColor: Color;
        labelsColor: Color;
        direction: StiLegendDirection;
        horAlignment: StiLegendHorAlignment;
        vertAlignment: StiLegendVertAlignment;
        titleFont: Font;
        font: Font;
        visible: boolean;
        markerVisible: boolean;
        markerBorder: boolean;
        markerSize: SizeD;
        markerAlignment: StiMarkerAlignment;
        horSpacing: number;
        vertSpacing: number;
        size: SizeD;
        title: string;
        columnWidth: number;
        private _columns;
        get columns(): number;
        set columns(value: number);
        constructor();
    }
}
declare namespace Stimulsoft.Report.Components {
    import Font = Stimulsoft.System.Drawing.Font;
    import StiChartEditorType = Stimulsoft.Report.Chart.StiChartEditorType;
    import IStiGetFonts = Stimulsoft.Base.IStiGetFonts;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiProcessChartEvent = Stimulsoft.Report.Events.StiProcessChartEvent;
    import EventArgs = Stimulsoft.System.EventArgs;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiChartConditionsCollection = Stimulsoft.Report.Chart.StiChartConditionsCollection;
    import IStiArea = Stimulsoft.Report.Chart.IStiArea;
    import IStiChart = Stimulsoft.Report.Chart.IStiChart;
    import IStiChartTable = Stimulsoft.Report.Chart.IStiChartTable;
    import IStiChartTitle = Stimulsoft.Report.Chart.IStiChartTitle;
    import IStiLegend = Stimulsoft.Report.Chart.IStiLegend;
    import IStiSeriesLabels = Stimulsoft.Report.Chart.IStiSeriesLabels;
    import StiChartCoreXF = Stimulsoft.Report.Chart.StiChartCoreXF;
    import StiChartInfo = Stimulsoft.Report.Chart.StiChartInfo;
    import StiConstantLinesCollection = Stimulsoft.Report.Chart.StiConstantLinesCollection;
    import IStiChartStyle = Stimulsoft.Report.Chart.IStiChartStyle;
    import StiStripsCollection = Stimulsoft.Report.Chart.StiStripsCollection;
    import StiSeriesCollection = Stimulsoft.Report.Chart.StiSeriesCollection;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import StiImageRotation = Stimulsoft.Report.Components.StiImageRotation;
    import StiComponentType = Stimulsoft.Report.Components.StiComponentType;
    import StiDataRelation = Stimulsoft.Report.Dictionary.StiDataRelation;
    import StiBusinessObject = Stimulsoft.Report.Dictionary.StiBusinessObject;
    import StiDataSource = Stimulsoft.Report.Dictionary.StiDataSource;
    import StiBorder = Stimulsoft.Base.Drawing.StiBorder;
    import StiFiltersCollection = Stimulsoft.Report.Components.StiFiltersCollection;
    import StiFilterMode = Stimulsoft.Report.Components.StiFilterMode;
    import StiUnit = Stimulsoft.Report.Units.StiUnit;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import StiComponent = Stimulsoft.Report.Components.StiComponent;
    import Image = Stimulsoft.System.Drawing.Image;
    import StiAnimation = Stimulsoft.Base.Context.Animation.StiAnimation;
    import List = Stimulsoft.System.Collections.List;
    class StiChart extends StiComponent implements IStiBorder, IStiBusinessObject, IStiBrush, IStiDataSource, IStiDataRelation, IStiMasterComponent, IStiSort, IStiFilter, IStiExportImage, IStiExportImageExtended, IStiIgnoryStyle, IStiGlobalizationProvider, IStiChart, IStiJsonReportObject, IStiGetFonts {
        private static implementsStiChart;
        implements(): any[];
        jsonMasterComponentTemp: string;
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(jObject: StiJson): void;
        loadFromXml(xmlNode: XmlNode): void;
        get componentId(): StiComponentId;
        convert(oldUnit: StiUnit, newUnit: StiUnit, isReportSnapshot?: boolean): void;
        convertToHInches(value: number): number;
        setString(propertyName: string, value: string): void;
        getString(propertyName: string): string;
        getAllStrings(): string[];
        clone(): StiChart;
        saveState(stateName: string): void;
        restoreState(stateName: string): void;
        getImage(REFzoom: any, format?: StiExportFormat): Image;
        isExportAsImage(format: StiExportFormat): boolean;
        filterMethodHandler: Function;
        filterMode: StiFilterMode;
        filters: StiFiltersCollection;
        get filter(): string;
        set filter(value: string);
        filterOn: boolean;
        border: StiBorder;
        brush: StiBrush;
        private _sort;
        get sort(): string[];
        set sort(value: string[]);
        get dataSource(): StiDataSource;
        private _dataSourceName;
        get dataSourceName(): string;
        set dataSourceName(value: string);
        get isDataSourceEmpty(): boolean;
        get isBusinessObjectEmpty(): boolean;
        get businessObject(): StiBusinessObject;
        private _businessObjectGuid;
        get businessObjectGuid(): string;
        set businessObjectGuid(value: string);
        masterComponent: StiComponent;
        countData: number;
        first(): void;
        prior(): void;
        next(): void;
        last(): void;
        isEofValue: boolean;
        get isEof(): boolean;
        set isEof(value: boolean);
        isBofValue: boolean;
        get isBof(): boolean;
        set isBof(value: boolean);
        get isEmpty(): boolean;
        positionValue: number;
        get position(): number;
        set position(value: number);
        get count(): number;
        private isCacheValues;
        private cachedCount;
        private cachedIsBusinessObjectEmpty;
        private cachedIsDataSourceEmpty;
        private cachedDataSource;
        private cachedBusinessObject;
        cacheValues(cache: boolean): void;
        get dataRelation(): StiDataRelation;
        dataRelationName: string;
        processAtEnd: boolean;
        getFonts(): Font[];
        get priority(): number;
        get localizedCategory(): string;
        defaultClientRectangle: RectangleD;
        get componentType(): StiComponentType;
        get localizedName(): string;
        invokeEvents(): void;
        protected onProcessChart(e: EventArgs): void;
        invokeProcessChart(sender: any, e: EventArgs): void;
        processChartEvent: StiProcessChartEvent;
        private series_SeriesAdded;
        private series_SeriesRemoved;
        seriesLabelsConditions: StiChartConditionsCollection;
        get chartType(): IStiArea;
        set chartType(value: IStiArea);
        isDashboard: boolean;
        createNew(): StiComponent;
        applyStyle(): void;
        simplifyValues(): void;
        core: StiChartCoreXF;
        rotation: StiImageRotation;
        editorType: StiChartEditorType;
        private _series;
        get series(): StiSeriesCollection;
        set series(value: StiSeriesCollection);
        private _area;
        get area(): IStiArea;
        set area(value: IStiArea);
        private _table;
        get table(): IStiChartTable;
        set table(value: IStiChartTable);
        private _style;
        get style(): IStiChartStyle;
        set style(value: IStiChartStyle);
        private _allowApplyStyle;
        get allowApplyStyle(): boolean;
        set allowApplyStyle(value: boolean);
        customStyleName: string;
        private _horSpacing;
        get horSpacing(): number;
        set horSpacing(value: number);
        private _vertSpacing;
        get vertSpacing(): number;
        set vertSpacing(value: number);
        private _seriesLabels;
        get seriesLabels(): IStiSeriesLabels;
        set seriesLabels(value: IStiSeriesLabels);
        get labels(): IStiSeriesLabels;
        set labels(value: IStiSeriesLabels);
        private _legend;
        get legend(): IStiLegend;
        set legend(value: IStiLegend);
        private _title;
        get title(): IStiChartTitle;
        set title(value: IStiChartTitle);
        private _strips;
        get strips(): StiStripsCollection;
        set strips(value: StiStripsCollection);
        private _constantLines;
        get constantLines(): StiConstantLinesCollection;
        set constantLines(value: StiConstantLinesCollection);
        isAnimation: boolean;
        isAnimationChangingValues: boolean;
        chartInfo: StiChartInfo;
        previousAnimations: List<StiAnimation>;
        sortAnimation: boolean;
        constructor(rect?: RectangleD);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiChartOptions {
        private static _oldChartPercentMode;
        static get oldChartPercentMode(): boolean;
        static set oldChartPercentMode(value: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import TimeSpan = Stimulsoft.System.TimeSpan;
    import PointD = Stimulsoft.System.Drawing.Point;
    import SizeD = Stimulsoft.System.Drawing.Size;
    import StiSeriesInteractionData = Stimulsoft.Base.Context.StiSeriesInteractionData;
    class StiInteractionOptions {
        updateContext: boolean;
        recallEvent: boolean;
        recallTime: TimeSpan;
        isRecalled: boolean;
        mousePoint: PointD;
        dragEnabled: boolean;
        dragDelta: SizeD;
        interactionToolTip: string;
        interactionHyperlink: string;
        seriesInteractionData: StiSeriesInteractionData;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import PointD = Stimulsoft.System.Drawing.Point;
    class StiPointHelper {
        private static getPointClassify;
        static isPointInTriangle(p: PointD, a: PointD, b: PointD, c: PointD): boolean;
        static isPointInPolygon(p: PointD, points: PointD[]): boolean;
        static getLineOffsetRectangle(point1: PointD, point2: PointD, offset: number): PointD[];
        static isLineContainsPoint(startPoint: PointD, endPoint: PointD, offset: number, point: PointD): boolean;
        static optimizePoints(points: PointD[]): PointD[];
    }
}
declare namespace Stimulsoft.Report.Chart {
    import PointD = Stimulsoft.System.Drawing.Point;
    class StiSimplifyHelper {
        private static getSquareDistance;
        private static getSquareSegmentDistance;
        private static simplifyRadialDistance;
        private static simplifyDouglasPeucker;
        static simplify(points: PointD[], tolerance: number, highestQuality: boolean): PointD[];
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiBoxAndWhiskerHelper {
        static checkArgument(chart: IStiChart): void;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiFormatService = Stimulsoft.Report.Components.TextFormats.StiFormatService;
    class StiHistogramHelper {
        static checkValuesAndArguments(series: IStiSeries, formatService: StiFormatService): void;
        private static roundToSignificantDigits;
        private static getStandardDeviation;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Color = Stimulsoft.System.Drawing.Color;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    class StiPie3dHelper {
        static readonly brightnessEnhancementFactor1: number;
        static createColorWithCorrectedLightness(color: Color, correctionFactor: number): Color;
        static getActualAngle(rect: Rectangle, transformedAngle: number): number;
        static transformAngle(rect: Rectangle, angle: number): number;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiFontGeom = Stimulsoft.Base.Context.StiFontGeom;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import Font = Stimulsoft.System.Drawing.Font;
    class StiTextContentHelper {
        static getMeasureText(context: StiContext, text: string, font: Font, maxWidth: number): string;
        static getMeasureText2(context: StiContext, text: string, font: StiFontGeom, maxWidth: number): string;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IAsIs = Stimulsoft.System.IAsIs;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiAreaCoreXF implements ICloneable, IStiApplyStyle, IStiAreaCoreXF, IAsIs {
        private static implementsStiAreaCoreXF;
        implements(): any[];
        is<T>(type: (new (...args: any[]) => T) | Stimulsoft.System.Interface<T>): this is T;
        is2<T>(type: (new (...args: any[]) => T) | Stimulsoft.System.Interface<T>): boolean;
        as<T>(type: (new (...args: any[]) => T) | Stimulsoft.System.Interface<T>): T;
        clone(): StiAreaCoreXF;
        applyStyle(style: IStiChartStyle): void;
        render(context: StiContext, rect: RectangleD): StiCellGeom;
        protected prepareInfo(rect: RectangleD): void;
        checkInLabelsTypes(typeForCheck: Stimulsoft.System.Type): boolean;
        getSeries(): IStiSeries[];
        isAcceptableSeries(seriesType: Stimulsoft.System.Type): boolean;
        isAcceptableSeriesLabels(seriesLabelsType: Stimulsoft.System.Type): boolean;
        area: IStiArea;
        get localizedName(): string;
        get seriesOrientation(): StiChartSeriesOrientation;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiPenStyle = Stimulsoft.Base.Drawing.StiPenStyle;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiShowEmptyCellsAs = Stimulsoft.Report.Chart.StiShowEmptyCellsAs;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiBaseLineSeries extends StiSeries implements IStiJsonReportObject, IStiBaseLineSeries, ICloneable, IStiSeries, IStiAllowApplyColorNegative, IStiShowNullsSeries, IStiShowZerosSeries {
        private static implementsStiBaseLineSeries;
        implements(): any[];
        meta(): StiMeta[];
        clone(): StiBaseLineSeries;
        showNulls: boolean;
        showZeros: boolean;
        get showMarker(): boolean;
        set showMarker(value: boolean);
        get markerColor(): Color;
        set markerColor(value: Color);
        get markerSize(): number;
        set markerSize(value: number);
        get markerType(): StiMarkerType;
        set markerType(value: StiMarkerType);
        marker: IStiMarker;
        lineMarker: IStiLineMarker;
        private _lineColor;
        get lineColor(): Color;
        set lineColor(value: Color);
        getLineColor(): Color;
        setLineColor(value: Color): void;
        lineStyle: StiPenStyle;
        lighting: boolean;
        private _lineWidth;
        get lineWidth(): number;
        set lineWidth(value: number);
        labelsOffset: number;
        lineColorNegative: Color;
        allowApplyColorNegative: boolean;
        showNullsAs: StiShowEmptyCellsAs;
        showZerosAs: StiShowEmptyCellsAs;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import ICloneable = Stimulsoft.System.ICloneable;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiScatterSeries extends StiBaseLineSeries implements IStiBaseLineSeries, IStiSeries, ICloneable, IStiScatterSeries, IStiAllowApplyColorNegative {
        private static implementsStiScatterSeries;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        clone(): StiScatterSeries;
        getDefaultAreaType(): Stimulsoft.System.Type;
        getLineColor(): Color;
        setLineColor(value: Color): void;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiScatterLineSeries extends StiScatterSeries implements IStiScatterLineSeries, IStiBaseLineSeries, IStiScatterSeries, IStiJsonReportObject, IStiSeries, ICloneable, IStiAllowApplyColorNegative {
        private static implementsStiScatterLineSeries;
        implements(): any[];
        get componentId(): StiComponentId;
        clone(): StiScatterLineSeries;
        getDefaultAreaType(): Stimulsoft.System.Type;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiAxisAreaCoreXF extends StiAreaCoreXF implements IStiAxisAreaCoreXF {
        private static implementsStiAxisAreaCoreXF;
        implements(): any[];
        applyStyle(style: IStiChartStyle): void;
        render(context: StiContext, rect: RectangleD): StiCellGeom;
        private calculateScrollValuesX;
        private calculateScrollValuesY;
        protected prepareInfo(rect: RectangleD): void;
        private renderSeries;
        isAutoRangeXAxis(axis: IStiAxis): boolean;
        isAutoRangeYAxis(axis: IStiAxis): boolean;
        calculateMinimumAndMaximumXAxis(axis: IStiAxis): void;
        calculateMinimumAndMaximumYAxisLog(axis: IStiAxis): void;
        calculateMinimumAndMaximumYAxis(axis: IStiAxis): void;
        getArgumentLabel(line: StiStripLineXF, series: IStiSeries): string;
        switchOff(): void;
        private swap;
        protected prepareRange(specXAxis: IStiAxis, specXTopAxis: IStiAxis, specYAxis: IStiAxis, specYRightAxis: IStiAxis): void;
        protected createStripLinesXAxis(axis: IStiAxis): void;
        protected checkShowEdgeValues(axis: IStiAxis): void;
        protected createStripLinesYAxis(axis: IStiAxis, isDateTimeValues: boolean): void;
        protected checkStripLinesAndMaximumMinimumXAxis(axis: IStiAxis): void;
        protected checkStripLinesAndMaximumMinimumYAxis(axis: IStiAxis): void;
        private calculateStepX;
        private calculateStepY;
        private checkStartFromZeroYAxis;
        calculatePositions(axis: IStiAxis, REFcollection: any, step: number, calculationForTicks: boolean): void;
        private calculateDivider;
        private static rotateStripLines;
        getDividerX(): number;
        getDividerTopX(): number;
        getDividerY(): number;
        getDividerRightY(): number;
        valuesCount: number;
        get scrollDistanceX(): number;
        get scrollDistanceY(): number;
        private _scrollRangeX;
        get scrollRangeX(): number;
        private _scrollRangeY;
        get scrollRangeY(): number;
        private _scrollViewX;
        get scrollViewX(): number;
        private _scrollViewY;
        get scrollViewY(): number;
        blockScrollValueX: boolean;
        blockScrollValueY: boolean;
        scrollValueX: number;
        scrollValueY: number;
        private _scrollDpiX;
        get scrollDpiX(): number;
        private _scrollDpiY;
        get scrollDpiY(): number;
        scrollDragStartValue: number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiClusteredColumnAreaCoreXF extends StiAxisAreaCoreXF {
        protected prepareRange(specXAxis: IStiAxis, specXTopAxis: IStiAxis, specYAxis: IStiAxis, specYRightAxis: IStiAxis): void;
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiArea = Stimulsoft.Report.Chart.IStiArea;
    class StiBoxAndWhiskerAreaCoreXF extends StiClusteredColumnAreaCoreXF {
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiScatterAreaCoreXF extends StiClusteredColumnAreaCoreXF {
        private isArgumentDateTime;
        private isXAxisAutoRange;
        protected prepareRange(specXAxis: IStiAxis, specXTopAxis: IStiAxis, specYAxis: IStiAxis, specYRightAxis: IStiAxis): void;
        protected checkStripLinesAndMaximumMinimumXAxis(axis: IStiAxis): void;
        protected createStripLinesXAxis(axis: IStiAxis): void;
        protected createStripLinesYAxis(axis: IStiAxis, isDateTimeValues: boolean): void;
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiArea = Stimulsoft.Report.Chart.IStiArea;
    class StiBubbleAreaCoreXF extends StiScatterAreaCoreXF {
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiCandlestickAreaCoreXF extends StiClusteredColumnAreaCoreXF {
        get localizedName(): string;
        get position(): number;
        protected createStripLinesXAxis(axis: IStiAxis): void;
        protected prepareRange(specXAxis: IStiAxis, specXTopAxis: IStiAxis, specYAxis: IStiAxis, specYRightAxis: IStiAxis): void;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiClusteredBarAreaCoreXF extends StiClusteredColumnAreaCoreXF {
        get localizedName(): string;
        get seriesOrientation(): StiChartSeriesOrientation;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiAreaAreaCoreXF extends StiClusteredColumnAreaCoreXF {
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiHistogramAreaCoreXF extends StiClusteredColumnAreaCoreXF {
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiLineAreaCoreXF extends StiClusteredColumnAreaCoreXF {
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiParetoAreaCoreXF extends StiClusteredColumnAreaCoreXF {
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiSplineAreaAreaCoreXF extends StiClusteredColumnAreaCoreXF {
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiSplineAreaCoreXF extends StiClusteredColumnAreaCoreXF {
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiSteppedAreaAreaCoreXF extends StiClusteredColumnAreaCoreXF {
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiSteppedLineAreaCoreXF extends StiClusteredColumnAreaCoreXF {
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiWaterfallAreaCoreXF extends StiAxisAreaCoreXF {
        protected prepareRange(specXAxis: IStiAxis, specXTopAxis: IStiAxis, specYAxis: IStiAxis, specYRightAxis: IStiAxis): void;
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiPieAreaCoreXF extends StiAreaCoreXF {
        valuesCount: number;
        render(context: StiContext, rect: RectangleD): StiCellGeom;
        renderSeries(context: StiContext, rect: RectangleD, geom: StiAreaGeom, seriesCollection: IStiSeries[]): void;
        protected prepareInfo(rect: RectangleD): void;
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiDoughnutAreaCoreXF extends StiPieAreaCoreXF {
        applyStyle(style: IStiChartStyle): void;
        render(context: StiContext, rect: RectangleD): StiCellGeom;
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiStackedBarAreaCoreXF extends StiClusteredBarAreaCoreXF {
        private prepareSeriesRange;
        protected prepareRange(specXAxis: IStiAxis, specXTopAxis: IStiAxis, specYAxis: IStiAxis, specYRightAxis: IStiAxis): void;
        get localizedName(): string;
        get seriesOrientation(): StiChartSeriesOrientation;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiFullStackedBarAreaCoreXF extends StiStackedBarAreaCoreXF {
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiStackedColumnAreaCoreXF extends StiAxisAreaCoreXF {
        private prepareSeriesRange;
        protected prepareRange(specXAxis: IStiAxis, specXTopAxis: IStiAxis, specYAxis: IStiAxis, specYRightAxis: IStiAxis): void;
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiFullStackedColumnAreaCoreXF extends StiStackedColumnAreaCoreXF {
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiFullStackedAreaAreaCoreXF extends StiFullStackedColumnAreaCoreXF {
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiFullStackedLineAreaCoreXF extends StiFullStackedColumnAreaCoreXF {
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiFullStackedSplineAreaAreaCoreXF extends StiFullStackedColumnAreaCoreXF {
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiFullStackedSplineAreaCoreXF extends StiFullStackedColumnAreaCoreXF {
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiFunnelAreaCoreXF extends StiAreaCoreXF {
        get localizedName(): string;
        get position(): number;
        render(context: StiContext, rect: RectangleD): StiCellGeom;
        renderSeries(context: StiContext, rect: RectangleD, geom: StiAreaGeom, seriesCollection: IStiSeries[]): void;
        protected prepareInfo(rect: RectangleD): void;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiGanttAreaCoreXF extends StiClusteredBarAreaCoreXF {
        protected createStripLinesXAxis(axis: IStiAxis): void;
        protected prepareRange(specXAxis: IStiAxis, specXTopAxis: IStiAxis, specYAxis: IStiAxis, specYRightAxis: IStiAxis): void;
        get localizedName(): string;
        get seriesOrientation(): StiChartSeriesOrientation;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiPictorialAreaCoreXF extends StiAreaCoreXF {
        render(context: StiContext, rect: RectangleD): StiCellGeom;
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiPictorialStackedAreaCoreXF extends StiAreaCoreXF {
        render(context: StiContext, rect: RectangleD): StiCellGeom;
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    class StiPie3dAreaCoreXF extends StiPieAreaCoreXF {
        render(context: StiContext, rect: Rectangle): StiCellGeom;
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import PointD = Stimulsoft.System.Drawing.Point;
    class StiRadarAreaCoreXF extends StiAreaCoreXF {
        applyStyle(style: IStiChartStyle): void;
        valuesCount: number;
        points: PointD[];
        arguments: any[];
        centerPoint: PointD;
        render(context: StiContext, areaRect: RectangleD): StiCellGeom;
        private static centerArea;
        measureLabels(context: StiContext, rect: RectangleD): RectangleD;
        renderArguments(context: StiContext, geom: StiRadarAreaGeom, series: IStiSeries): void;
        renderSeries(context: StiContext, rect: RectangleD, geom: StiAreaGeom, seriesCollection: IStiSeries[]): void;
        protected prepareInfo(rect: RectangleD): void;
        protected createStripLinesAxis(axis: IStiYRadarAxis, minimum: number, maximum: number): void;
        private calculateStep;
        calculatePositions(axis: IStiYRadarAxis, REFcollection: any, step: number, calculationForTicks: boolean): void;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiRadarAreaAreaCoreXF extends StiRadarAreaCoreXF {
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiRadarLineAreaCoreXF extends StiRadarAreaCoreXF {
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiRadarPointAreaCoreXF extends StiRadarAreaCoreXF {
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiRangeAreaCoreXF extends StiClusteredColumnAreaCoreXF {
        get localizedName(): string;
        get position(): number;
        protected prepareRange(specXAxis: IStiAxis, specXTopAxis: IStiAxis, specYAxis: IStiAxis, specYRightAxis: IStiAxis): void;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiRangeBarAreaCoreXF extends StiClusteredColumnAreaCoreXF {
        protected createStripLinesXAxis(axis: IStiAxis): void;
        protected prepareRange(specXAxis: IStiAxis, specXTopAxis: IStiAxis, specYAxis: IStiAxis, specYRightAxis: IStiAxis): void;
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiSplineRangeAreaCoreXF extends StiClusteredColumnAreaCoreXF {
        get localizedName(): string;
        get position(): number;
        protected prepareRange(specXAxis: IStiAxis, specXTopAxis: IStiAxis, specYAxis: IStiAxis, specYRightAxis: IStiAxis): void;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiSteppedRangeAreaCoreXF extends StiClusteredColumnAreaCoreXF {
        get localizedName(): string;
        get position(): number;
        protected prepareRange(specXAxis: IStiAxis, specXTopAxis: IStiAxis, specYAxis: IStiAxis, specYRightAxis: IStiAxis): void;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiStackedAreaAreaCoreXF extends StiStackedColumnAreaCoreXF {
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiStackedLineAreaCoreXF extends StiStackedColumnAreaCoreXF {
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiStackedSplineAreaAreaCoreXF extends StiStackedColumnAreaCoreXF {
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiStackedSplineAreaCoreXF extends StiStackedColumnAreaCoreXF {
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiStockAreaCoreXF extends StiCandlestickAreaCoreXF {
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    class StiSunburstAreaCoreXF extends StiAreaCoreXF {
        render(context: StiContext, rect: Rectangle): StiCellGeom;
        protected prepareInfo(rect: Rectangle): void;
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiTreemapAreaCoreXF extends StiAreaCoreXF {
        render(context: StiContext, rect: RectangleD): StiCellGeom;
        renderSeries(context: StiContext, boxes: RectangleD[], boxRoot: RectangleD, geom: StiAreaGeom, seriesCollection: IStiSeries[]): void;
        private cutArea;
        squarify(data: number[], currentrow: number[], container: RectangleD, stack: RectangleD[]): RectangleD[];
        private improvesRatio;
        private calculateRatio;
        normalizeDataForArea(data: number[], area: number): number[];
        private getCoordinates;
        prepareInfo(rect: RectangleD): void;
        get localizedName(): string;
        get position(): number;
        constructor(area: IStiArea);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IAsIs = Stimulsoft.System.IAsIs;
    import StiStringFormatGeom = Stimulsoft.Base.Context.StiStringFormatGeom;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiFontGeom = Stimulsoft.Base.Context.StiFontGeom;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import StiHorAlignment = Stimulsoft.Base.Drawing.StiHorAlignment;
    import SizeD = Stimulsoft.System.Drawing.Size;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiAxisCoreXF implements ICloneable, IStiApplyStyle, IStiAxisCoreXF, IAsIs {
        private static implementsStiAxisCoreXF;
        implements(): any[];
        is<T>(type: (new (...args: any[]) => T) | Stimulsoft.System.Interface<T>): this is T;
        is2<T>(type: (new (...args: any[]) => T) | Stimulsoft.System.Interface<T>): boolean;
        as<T>(type: (new (...args: any[]) => T) | Stimulsoft.System.Interface<T>): T;
        clone(): StiAxisCoreXF;
        applyStyle(style: IStiChartStyle): void;
        getStartFromZero(): boolean;
        render(context: StiContext, rect: RectangleD): StiCellGeom;
        renderView(context: StiContext, rect: RectangleD): StiCellGeom;
        calculateStripPositions(topPosition: number, bottomPosition: number): void;
        getTicksMaxLength(context: StiContext): number;
        getArrowHeight(context: StiContext): number;
        getLabelsSpaceAxis(context: StiContext): number;
        getLabelsTwoLinesDestination(context: StiContext): number;
        getFontGeom(context: StiContext): StiFontGeom;
        getTextAlignment(): StiHorAlignment;
        getStringFormatGeom(context: StiContext): StiStringFormatGeom;
        protected getAxisTitleSize(context: StiContext): SizeD;
        protected getAngleTitle(): number;
        protected getCorrectionFontSize(axisRect: Rectangle, titleRect: Rectangle, currentFontSize: number): number;
        protected checkUseMaxWidth(axisRect: Rectangle, titleRect: Rectangle, RefMaxWidth: any): boolean;
        static defaultScrollBarSize: number;
        static defaultScrollBarSmallFactor: number;
        static defaultScrollBarFirstRecallTime: number;
        static defaultScrollBarOtherRecallTime: number;
        get ticksMaxLength(): number;
        get arrowWidth(): number;
        get arrowHeight(): number;
        axis: IStiAxis;
        get info(): StiAxisInfoXF;
        set info(value: StiAxisInfoXF);
        constructor(axis: IStiAxis);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiAxisInfoXF implements ICloneable, IStiAxisInfoXF {
        private static implementsStiAxisInfoXF;
        implements(): any[];
        clone(): StiAxisInfoXF;
        dpi: number;
        step: number;
        get range(): number;
        stripLines: StiStripLinesXF;
        stripPositions: number[];
        ticksCollection: StiStripPositionXF[];
        labelsCollection: StiStripPositionXF[];
        minimum: number;
        maximum: number;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiRotationMode = Stimulsoft.Base.Drawing.StiRotationMode;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import PointD = Stimulsoft.System.Drawing.Point;
    class StiAxisLabelInfoXF {
        clientRectangle: RectangleD;
        textPoint: PointD;
        angle: number;
        rotationMode: StiRotationMode;
        text: string;
        stripLine: StiStripLineXF;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiAxisLabelsCoreXF implements IStiApplyStyle, ICloneable, IStiAxisLabelsCoreXF {
        private static implementsStiAxisLabelsCoreXF;
        implements(): any[];
        clone(): StiAxisLabelsCoreXF;
        applyStyle(style: IStiChartStyle): void;
        labels: IStiAxisLabels;
        constructor(labels: IStiAxisLabels);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiAxisTitleCoreXF implements IStiApplyStyle, ICloneable, IStiAxisTitleCoreXF {
        private static implementsStiAxisTitleCoreXF;
        implements(): any[];
        clone(): StiAxisTitleCoreXF;
        applyStyle(style: IStiChartStyle): void;
        title: IStiAxisTitle;
        constructor(title: IStiAxisTitle);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiXAxisCoreXF extends StiAxisCoreXF {
        private storedCulture;
        getStartFromZero(): boolean;
        render(context: StiContext, rect: RectangleD): StiCellGeom;
        renderView(context: StiContext, rect: RectangleD): StiCellGeom;
        renderScrollBar(context: StiContext, axisRect: RectangleD, axisGeom: StiXAxisViewGeom): void;
        renderCenter(context: StiContext, rect: RectangleD): StiCellGeom;
        renderCenterView(context: StiContext, rect: RectangleD): StiCellGeom;
        getLabelText(line: StiStripLineXF, series: IStiSeries): string;
        getLabelText2(objectValue: any, value: number, series: IStiSeries): string;
        private get isLabelsAngleByWidth();
        private checkAutoAngleLabels;
        private measureStripLines;
        getCenterAxisRect(context: StiContext, rect: RectangleD, includeAxisArrow: boolean, includeLabelsHeight: boolean, isDrawing: boolean): RectangleD;
        getAxisRect(context: StiContext, rect: RectangleD, includeAxisArrow: boolean, includeLabelsWidth: boolean, isDrawing: boolean, includeScrollBar: boolean): RectangleD;
        private renderLabels;
        private renderTitle;
        private isArgumentDateTime1;
        private isArgumentDateTime2;
        get dock(): StiXAxisDock;
        get isTopSide(): boolean;
        get isBottomSide(): boolean;
        constructor(axis: IStiAxis);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiXBottomAxisCoreXF extends StiXAxisCoreXF {
        get dock(): StiXAxisDock;
        constructor(axis: IStiAxis);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiXTopAxisCoreXF extends StiXAxisCoreXF {
        get dock(): StiXAxisDock;
        constructor(axis: IStiAxis);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiYAxisCoreXF extends StiAxisCoreXF {
        private storedCulture;
        render(context: StiContext, rect: RectangleD): StiCellGeom;
        renderView(context: StiContext, rect: RectangleD): StiCellGeom;
        renderScrollBar(context: StiContext, axisRect: RectangleD, axisGeom: StiYAxisViewGeom): void;
        renderCenter(context: StiContext, rect: RectangleD): StiCellGeom;
        renderCenterView(context: StiContext, rect: RectangleD): StiCellGeom;
        setTotalNumberCapacity(): void;
        getLabelText(line: StiStripLineXF, series: IStiSeries): string;
        private measureStripLines;
        getCenterAxisRect(context: StiContext, rect: RectangleD, includeAxisArrow: boolean, includeLabelsHeight: boolean, isDrawing: boolean): RectangleD;
        getAxisRect(context: StiContext, rect: RectangleD, includeAxisArrow: boolean, includeLabelsHeight: boolean, isDrawing: boolean, includeScrollBar: boolean): RectangleD;
        private renderLabels;
        private renderTitle;
        get dock(): StiYAxisDock;
        get isLeftSide(): boolean;
        get isRightSide(): boolean;
        constructor(axis: IStiAxis);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiYLeftAxisCoreXF extends StiYAxisCoreXF {
        get dock(): StiYAxisDock;
        constructor(axis: IStiAxis);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiYRightAxisCoreXF extends StiYAxisCoreXF {
        get dock(): StiYAxisDock;
        getStartFromZero(): boolean;
        constructor(axis: IStiAxis);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiStripLineCalculatorXF {
        private static getInterval1;
        static getInterval(minValue: number, maxValue: number, num: number): number;
        static getStripLines(minValue: number, maxValue: number, step: number, asDateTimeValue: boolean): StiStripLinesXF;
        private static getCountAfterComma;
        static getStripLinesLogScale(minValue: number, maxValue: number): StiStripLinesXF;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiStripLineXF implements ICloneable, IStiStripLineXF {
        private static implementsStiStripLineXF;
        implements(): any[];
        clone(): StiStripLineXF;
        valueObject: any;
        private valueObj;
        get value(): number;
        set value(value: number);
        constructor(valueObject: any, value: number);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import ICloneable = Stimulsoft.System.ICloneable;
    import CollectionBase = Stimulsoft.System.Collections.CollectionBase;
    class StiStripLinesXF extends CollectionBase<StiStripLineXF> implements ICloneable, IStiStripLinesXF {
        private static implementsStiStripLinesXF;
        implements(): any[];
        clone(): StiStripLinesXF;
        add2(valueObject: any, value: number): void;
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiStripPositionXF implements IStiStripPositionXF {
        private static implementsStiStripPositionXF;
        implements(): any[];
        position: number;
        stripLine: StiStripLineXF;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import ICloneable = Stimulsoft.System.ICloneable;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiChartTitleCoreXF implements ICloneable, IStiApplyStyle, IStiChartTitleCoreXF {
        private static implementsStiChartTitleCoreXF;
        implements(): any[];
        clone(): StiChartTitleCoreXF;
        applyStyle(style: IStiChartStyle): void;
        render(context: StiContext, chartTitle: IStiChartTitle, rect: RectangleD): StiCellGeom;
        chartTitle: IStiChartTitle;
        constructor(chartTitle: IStiChartTitle);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiConstantLinesCoreXF implements IStiApplyStyle, ICloneable, IStiConstantLinesCoreXF {
        private static implementsStiConstantLinesCoreXF;
        implements(): any[];
        clone(): any;
        applyStyle(style: IStiChartStyle): void;
        renderXConstantLines(geom: StiAxisAreaGeom, rect: RectangleD): void;
        renderYConstantLines(geom: StiAxisAreaGeom, rect: RectangleD): void;
        render(context: StiContext, geom: StiAxisAreaGeom, rect: RectangleD): void;
        constantLines: IStiConstantLines;
        constructor(constantLines: IStiConstantLines);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiGridLinesCoreXF implements IStiApplyStyle, ICloneable, IStiGridLinesCoreXF {
        private static implementsStiGridLinesCoreXF;
        implements(): any[];
        clone(): StiGridLinesCoreXF;
        applyStyle(style: IStiChartStyle): void;
        gridLines: IStiGridLines;
        constructor(gridLines: IStiGridLines);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiRadarGridLinesCoreXF implements IStiApplyStyle, ICloneable, IStiRadarGridLinesCoreXF {
        private static implementsStiRadarGridLinesCoreXF;
        implements(): any[];
        clone(): StiRadarGridLinesCoreXF;
        applyStyle(style: IStiChartStyle): void;
        gridLines: IStiRadarGridLines;
        constructor(gridLines: IStiRadarGridLines);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiInterlacingCoreXF implements IStiApplyStyle, ICloneable, IStiInterlacingCoreXF {
        private static implementsStiInterlacingCoreXF;
        implements(): any[];
        clone(): StiInterlacingCoreXF;
        applyStyle(style: IStiChartStyle): void;
        interlacing: IStiInterlacing;
        constructor(interlacing: IStiInterlacing);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import SizeD = Stimulsoft.System.Drawing.Size;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import RectangleF = Stimulsoft.System.Drawing.Rectangle;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiLegendCoreXF implements ICloneable, IStiApplyStyle, IStiLegendCoreXF {
        private static implementsStiLegendCoreXF;
        implements(): any[];
        applyStyle(style: IStiChartStyle): void;
        clone(): StiLegendCoreXF;
        render(context: StiContext, rect: RectangleD): StiCellGeom;
        getMatrixIndexItem(countColumns: number, countRows: number, countItems: number): number[][];
        getArgumentText(series: IStiSeries, index: number): string;
        getLegendItemColumn(seriesItems: StiLegendItemCoreXF[], seriesItem: StiLegendItemCoreXF): number;
        getTitleSize(context: StiContext): SizeD;
        getItemSize1(context: StiContext, seriesItems: StiLegendItemCoreXF[], seriesIndex: number): SizeD;
        getItemSize2(context: StiContext, seriesItems: StiLegendItemCoreXF[], seriesItem: StiLegendItemCoreXF): SizeD;
        getItemRealSize(context: StiContext, seriesItem: StiLegendItemCoreXF): SizeD;
        getItemsSize(context: StiContext, seriesItems: StiLegendItemCoreXF[]): SizeD;
        getItemsAutoSize(context: StiContext, seriesItems: StiLegendItemCoreXF[], rect: RectangleF, countColumns: any, countRows: any): SizeD;
        getSeriesSize(context: StiContext, rect: RectangleF, countColumns: any, countRows: any): SizeD;
        getLegendSize(context: StiContext, rect: RectangleF, countColumns: any, countRows: any): SizeD;
        getLegendItems(REFcount: any): StiLegendItemCoreXF[];
        legend: IStiLegend;
        constructor(legend: IStiLegend);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Size = Stimulsoft.System.Drawing.Size;
    import StiFontGeom = Stimulsoft.Base.Context.StiFontGeom;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    class StiLegendItemCoreXF {
        text: string;
        series: IStiSeries;
        index: number;
        colorIndex: number;
        getText(context: StiContext, font: StiFontGeom): string;
        measureString(context: StiContext, font: StiFontGeom): Size;
        constructor(text: string, series: IStiSeries, index: number, colorIndex: number);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiPenGeom = Stimulsoft.Base.Context.StiPenGeom;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import PointD = Stimulsoft.System.Drawing.Point;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import StiInteractionDataGeom = Stimulsoft.Base.Context.StiInteractionDataGeom;
    import StiFontIcons = Stimulsoft.Report.Helpers.StiFontIcons;
    class StiMarkerCoreXF implements ICloneable, IStiMarkerCoreXF {
        private static implementsStiMarkerCoreXF;
        implements(): any[];
        clone(): StiMarkerCoreXF;
        drawMarkers(context: StiContext, points: PointD[], showShadow: boolean): void;
        static getMarkerRect(position: PointD, markerSize: number, zoom: number): RectangleD;
        draw(context: StiContext, marker: IStiMarker, position: PointD, zoom: number, showShadow: boolean, isMouseOver: boolean, isTooltipMode: boolean, isAnimation: boolean, toolTip: string, tag: any, interaction: StiInteractionDataGeom): void;
        drawLine(context: StiContext, x1: number, y1: number, x2: number, y2: number, scale: number, brushMarker: StiBrush, penMarker: StiPenGeom, markerType: StiMarkerType, markerStep: number, markerSize: number, angle: number): void;
        drawLines(context: StiContext, points: PointD[], scale: number, brushMarker: any, penMarker: StiPenGeom, markerType: StiMarkerType, markerStep: number, markerSize: number, angle: number): void;
        drawPoint(context: StiContext, x: number, y: number, scale: number, brush: any, pen: StiPenGeom, markerType: StiMarkerType, icon: StiFontIcons, markerSize: number, angle: number, isMouseOver: boolean, isAnimation: boolean, toolTip: string, tag: any, interaction: StiInteractionDataGeom): void;
        private drawPolygon;
        marker: IStiMarker;
        constructor(marker: IStiMarker);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiRadarAxisCoreXF implements ICloneable, IStiApplyStyle, IStiRadarAxisCoreXF {
        private static implementsStiRadarAxisCoreXF;
        implements(): any[];
        clone(): StiRadarAxisCoreXF;
        applyStyle(style: IStiChartStyle): void;
        axis: IStiRadarAxis;
        constructor(axis: IStiRadarAxis);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiRadarAxisLabelsCoreXF implements IStiApplyStyle, ICloneable, IStiRadarAxisLabelsCoreXF {
        private static implementsStiRadarAxisLabelsCoreXF;
        implements(): any[];
        clone(): StiRadarAxisLabelsCoreXF;
        applyStyle(style: IStiChartStyle): void;
        labels: IStiRadarAxisLabels;
        constructor(labels: IStiRadarAxisLabels);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import PointD = Stimulsoft.System.Drawing.Point;
    class StiXRadarAxisCoreXF extends StiRadarAxisCoreXF implements IStiXRadarAxisCoreXF {
        private static implementsStiXRadarAxisCoreXF;
        implements(): any[];
        applyStyle(style: IStiChartStyle): void;
        renderLabel(context: StiContext, series: IStiSeries, point: PointD, argument: any, angle: number, colorIndex: number, colorCount: number): StiXRadarAxisLabelGeom;
        getLabelText(value: any): string;
        getLabelRect(context: StiContext, point: PointD, text: string, angle: number): RectangleD;
        get xAxis(): IStiXRadarAxis;
        constructor(axis: IStiRadarAxis);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiStringFormatGeom = Stimulsoft.Base.Context.StiStringFormatGeom;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiFontGeom = Stimulsoft.Base.Context.StiFontGeom;
    import StiHorAlignment = Stimulsoft.Base.Drawing.StiHorAlignment;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiYRadarAxisCoreXF extends StiRadarAxisCoreXF implements IStiYRadarAxisCoreXF {
        private static implementsStiYRadarAxisCoreXF;
        implements(): any[];
        applyStyle(style: IStiChartStyle): void;
        render(context: StiContext, rect: RectangleD): StiCellGeom;
        private measureStripLines;
        private renderLabels;
        calculateStripPositions(topPosition: number, bottomPosition: number): void;
        getAxisRect(context: StiContext, rect: RectangleD): RectangleD;
        getTicksMaxLength(context: StiContext): number;
        getLabelsSpaceAxis(context: StiContext): number;
        getLabelsTwoLinesDestination(context: StiContext): number;
        getTextAlignment(): StiHorAlignment;
        setTotalNumberCapacity(): void;
        getLabelText(line: StiStripLineXF): string;
        getStringFormatGeom(context: StiContext): StiStringFormatGeom;
        getFontGeom(context: StiContext): StiFontGeom;
        get yAxis(): IStiYRadarAxis;
        get info(): StiAxisInfoXF;
        set info(value: StiAxisInfoXF);
        get ticksMaxLength(): number;
        constructor(axis: IStiRadarAxis);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IAsIs = Stimulsoft.System.IAsIs;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import ICloneable = Stimulsoft.System.ICloneable;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    class StiSeriesCoreXF implements ICloneable, IStiApplyStyleSeries, IStiSeriesCoreXF, IAsIs {
        private static implementsStiSeriesCoreXF;
        implements(): any[];
        is<T>(type: (new (...args: any[]) => T) | Stimulsoft.System.Interface<T>): this is T;
        is2<T>(type: (new (...args: any[]) => T) | Stimulsoft.System.Interface<T>): boolean;
        as<T>(type: (new (...args: any[]) => T) | Stimulsoft.System.Interface<T>): T;
        clone(): StiSeriesCoreXF;
        applyStyle(style: IStiChartStyle, color: Color): void;
        checkLabelsRect(labels: IStiSeriesLabels, geom: StiAreaGeom, labelsRect: RectangleD): RectangleD;
        checkLabelsRect2(labels: IStiSeriesLabels, rect: RectangleD, labelsRect: RectangleD): RectangleD;
        private getDrawRectangle;
        private rotatePoint;
        checkIntersectionLabels(geom: StiAreaGeom): void;
        private getLabelRectangle;
        renderSeries(context: StiContext, rect: RectangleD, geom: StiAreaGeom, seriesArray: IStiSeries[]): void;
        getSeriesBrush(colorIndex: number, colorCount: number): StiBrush;
        getSeriesBorderColor(colorIndex: number, colorCount: number): Color;
        getSeriesLabels(): IStiAxisSeriesLabels;
        getTag(tagIndex: number): string;
        private static falseObject;
        private static trueObject;
        get localizedName(): string;
        seriesColors: Color[];
        isDateTimeValues: boolean;
        isDateTimeArguments: boolean;
        series: IStiSeries;
        get interaction(): IStiSeriesInteraction;
        set interaction(value: IStiSeriesInteraction);
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiBoxAndWhiskerSeriesCoreXF extends StiSeriesCoreXF {
        applyStyle(style: IStiChartStyle, color: Color): void;
        renderSeries(context: StiContext, rect: Rectangle, geom: StiAreaGeom, series: IStiSeries[]): void;
        private getFirstValues;
        private getSecondValues;
        private getThirdQuartile;
        private getFirstQuartile;
        private getMedian;
        private getMedianIndices;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import PointD = Stimulsoft.System.Drawing.Point;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import StiSeriesInteractionData = Stimulsoft.Base.Context.StiSeriesInteractionData;
    class StiBaseLineSeriesCoreXF extends StiSeriesCoreXF implements IStiApplyStyleSeries {
        private static implementsStiBaseLineSeriesCoreXF;
        implements(): any[];
        applyStyle(style: IStiChartStyle, color: Color): void;
        protected clipLinePoints(context: StiContext, geom: StiAreaGeom, points: PointD[], REFstartIndex: any, REFendIndex: any): PointD[];
        renderMarkers(context: StiContext, geom: StiAreaGeom, points: PointD[]): void;
        getInteractions(context: StiContext, geom: StiAreaGeom, points: PointD[]): StiSeriesInteractionData[];
        renderLines(context: StiContext, geom: StiAreaGeom, pointsInfo: StiSeriesPointsInfo): void;
        renderAreas(context: StiContext, geom: StiAreaGeom, pointsInfo: StiSeriesPointsInfo): void;
        renderSeries(context: StiContext, rect: RectangleD, geom: StiAreaGeom, series: IStiSeries[]): void;
        private getPointsZeroConnect;
        private getPointsNullConnect;
        private getPointConnect;
        private getPointValue2;
        private getPointValue;
        private getPointValue1;
        private isTopmostLine;
        correctPoint(point: PointD, rect: RectangleD, correctY: number): PointD;
        getSeriesBrush(colorIndex: number, colorCount: number): StiBrush;
        getSeriesBorderColor(colorIndex: number, colorCount: number): Color;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Color = Stimulsoft.System.Drawing.Color;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiScatterSeriesCoreXF extends StiBaseLineSeriesCoreXF {
        applyStyle(style: IStiChartStyle, color: Color): void;
        renderLines(context: StiContext, geom: StiAreaGeom, pointsInfo: StiSeriesPointsInfo): void;
        renderSeries(context: StiContext, rect: RectangleD, geom: StiAreaGeom, seriesArray: IStiSeries[]): void;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import PointD = Stimulsoft.System.Drawing.Point;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiBubbleSeriesCoreXF extends StiScatterSeriesCoreXF {
        applyStyle(style: IStiChartStyle, color: Color): void;
        renderLines(context: StiContext, geom: StiAreaGeom, pointsInfo: StiSeriesPointsInfo): void;
        renderBubbles(context: StiContext, geom: StiAreaGeom, series: IStiBubbleSeries, points: PointD[], weights: number[]): void;
        renderSeries(context: StiContext, rect: RectangleD, geom: StiAreaGeom, seriesArray: IStiSeries[]): void;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import PointD = Stimulsoft.System.Drawing.Point;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    class StiClusteredColumnSeriesCoreXF extends StiSeriesCoreXF {
        applyStyle(style: IStiChartStyle, color: Color): void;
        renderSeries(context: StiContext, rect: RectangleD, geom: StiAreaGeom, series: IStiSeries[]): void;
        protected getPointEnd(currentSeries: IStiClusteredColumnSeries, value: number, seriesLeftPos: number, seriesWidth: number): PointD;
        protected getColumnRect(context: StiContext, currentSeries: IStiClusteredColumnSeries, value: number, seriesLeftPos: number, seriesWidth: number): RectangleD;
        protected correctPoint(point: PointD, rect: RectangleD): PointD;
        getSeriesBrush(colorIndex: number, colorCount: number): StiBrush;
        getSeriesBorderColor(colorIndex: number, colorCount: number): Color;
        private measureLeftPosition;
        private getSeriesCurrentValue;
        private getSeriesValueStart;
        private getSeriesBrush2;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import PointD = Stimulsoft.System.Drawing.Point;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiClusteredBarSeriesCoreXF extends StiClusteredColumnSeriesCoreXF implements IStiApplyStyleSeries {
        private static implementsStiClusteredBarSeriesCoreXF;
        implements(): any[];
        applyStyle(style: IStiChartStyle, color: Color): void;
        renderSeries(context: StiContext, rect: RectangleD, geom: StiAreaGeom, series: IStiSeries[]): void;
        private getBarRect;
        protected correctPoint(point: PointD, rect: RectangleD): PointD;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    class StiLineSeriesCoreXF extends StiBaseLineSeriesCoreXF {
        renderLines(context: StiContext, geom: StiAreaGeom, pointsInfo: StiSeriesPointsInfo): void;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    class StiAreaSeriesCoreXF extends StiLineSeriesCoreXF {
        applyStyle(style: IStiChartStyle, color: Color): void;
        renderAreas(context: StiContext, geom: StiAreaGeom, pointsInfo: StiSeriesPointsInfo): void;
        getSeriesBrush(colorIndex: number, colorCount: number): StiBrush;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiHistogramSeriesCoreXF extends StiClusteredColumnSeriesCoreXF {
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiParetoSeriesCoreXF extends StiClusteredColumnSeriesCoreXF {
        applyStyle(style: IStiChartStyle, color: Color): void;
        renderSeries(context: StiContext, rect: RectangleD, geom: StiAreaGeom, series: IStiSeries[]): void;
        private renderLinePareto;
        private renderLines;
        private getParetoValues;
        private getPointValue;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    class StiSplineSeriesCoreXF extends StiBaseLineSeriesCoreXF {
        renderLines(context: StiContext, geom: StiAreaGeom, pointsInfo: StiSeriesPointsInfo): void;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    class StiSplineAreaSeriesCoreXF extends StiSplineSeriesCoreXF {
        applyStyle(style: IStiChartStyle, color: Color): void;
        renderAreas(context: StiContext, geom: StiAreaGeom, pointsInfo: StiSeriesPointsInfo): void;
        getSeriesBrush(colorIndex: number, colorCount: number): StiBrush;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    class StiSteppedLineSeriesCoreXF extends StiBaseLineSeriesCoreXF {
        renderLines(context: StiContext, geom: StiAreaGeom, pointsInfo: StiSeriesPointsInfo): void;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    class StiSteppedAreaSeriesCoreXF extends StiSteppedLineSeriesCoreXF {
        applyStyle(style: IStiChartStyle, color: Color): void;
        renderAreas(context: StiContext, geom: StiAreaGeom, pointsInfo: StiSeriesPointsInfo): void;
        getSeriesBrush(colorIndex: number, colorCount: number): StiBrush;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import PointD = Stimulsoft.System.Drawing.Point;
    class StiWaterfallSeriesCoreXF extends StiClusteredColumnSeriesCoreXF {
        renderSeries(context: StiContext, rect: Rectangle, geom: StiAreaGeom, series: IStiSeries[]): void;
        private getSumSeriesWidth;
        private getDividerYSeries;
        private renderColumns;
        protected getPointEnd(currentSeries: IStiClusteredColumnSeries, value: number, seriesLeftPos: number, seriesWidth: number, posY?: number): PointD;
        protected getColumnRect(context: StiContext, currentSeries: IStiClusteredColumnSeries, value: number, seriesLeftPos: number, seriesWidth: number, REFposY?: any): Rectangle;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import PointD = Stimulsoft.System.Drawing.Point;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiPieSeriesCoreXF extends StiSeriesCoreXF {
        applyStyle(style: IStiChartStyle, color: Color): void;
        private correctBrush;
        private renderPieElement;
        private renderPieElementShadow;
        private measurePieElement;
        private measurePieElementCore;
        protected checkNonZerovalue(seriesArray: IStiSeries[], REFnonZeroValuesCount: {
            ref: number;
        }, REFfirstNonZeroValueIndex: {
            ref: number;
        }, REFfirstNonZeroSeries: {
            ref: IStiSeries;
        }, isForValueFrom?: boolean): number;
        protected isNotNullValues(seriesArray: IStiSeries[]): boolean;
        renderSeries(context: StiContext, rect: RectangleD, geom: StiAreaGeom, seriesArray: IStiSeries[]): void;
        getColorCount(seriesArray: IStiSeries[], isForValueFrom?: boolean): number;
        private isIntersectionLabels;
        private getPieLabelGeoms;
        getPieSeriesLabels(): IStiPieSeriesLabels;
        private getPieElementGeoms;
        private checkIntersectionOutLabels;
        private checkIntersectionTwoColumnsLabels;
        checkIntersectionLabels(geom: StiAreaGeom): void;
        private checkLabelPosition;
        protected getGradPerValue(series: IStiSeries[], isForValueFrom?: boolean): number;
        getPercentPerValue(series: IStiSeries[], isForValueFrom?: boolean): number;
        protected getPointCenter(rect: RectangleD): PointD;
        protected getRadius(context: StiContext, rect: RectangleD): number;
        protected getPoint(centerPie: PointD, radius: number, angle: number): PointD;
        protected getArgumentText(series: IStiSeries, index: number): string;
        private getPieDistance;
        private getPieDistance2;
        getSeriesBrush(colorIndex: number, colorCount: number): StiBrush;
        getSeriesBorderColor(colorIndex: number, colorCount: number): Color;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiDoughnutSeriesCoreXF extends StiPieSeriesCoreXF {
        private renderDoughnutElement;
        isNotNullValues(seriesArray: IStiSeries[]): boolean;
        renderSeries(context: StiContext, rect: RectangleD, geom: StiAreaGeom, seriesArray: IStiSeries[]): void;
        protected checkNonZerovalue(seriesArray: IStiSeries[], REFnonZeroValuesCount: any, REFfirstNonZeroValueIndex: any, REFfirstNonZeroSeries: any, isForValueFrom?: boolean): number;
        protected getGradPerValue(series: IStiSeries[]): number;
        getPercentPerValue(series: IStiSeries[]): number;
        protected getArgumentText(series: IStiSeries, index: number): string;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiCandlestickSeriesCoreXF extends StiSeriesCoreXF {
        applyStyle(style: IStiChartStyle, color: Color): void;
        renderSeries(context: StiContext, rect: RectangleD, geom: StiAreaGeom, series: IStiSeries[]): void;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiStockSeriesCoreXF extends StiSeriesCoreXF {
        applyStyle(style: IStiChartStyle, color: Color): void;
        renderSeries(context: StiContext, rect: RectangleD, geom: StiAreaGeom, series: IStiSeries[]): void;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    class StiStackedBarSeriesCoreXF extends StiSeriesCoreXF {
        applyStyle(style: IStiChartStyle, color: Color): void;
        renderSeries(context: StiContext, rect: RectangleD, geom: StiAreaGeom, series: IStiSeries[]): void;
        private calculateTotalWidth;
        private correctRect;
        getSeriesBrush(colorIndex: number, colorCount: number): StiBrush;
        getSeriesBorderColor(colorIndex: number, colorCount: number): Color;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiFullStackedBarSeriesCoreXF extends StiStackedBarSeriesCoreXF {
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import PointD = Stimulsoft.System.Drawing.Point;
    class StiStackedBaseLineSeriesCoreXF extends StiSeriesCoreXF {
        applyStyle(style: IStiChartStyle, color: Color): void;
        clipLinePoints(context: StiContext, geom: StiAreaGeom, startPoints: PointD[], endPoints: PointD[], REFnewStartPoints: any, REFnewEndPoints: any, REFstartIndex: any, REFendIndex: any): void;
        renderMarkers(context: StiContext, geom: StiAreaGeom, points: PointD[]): void;
        renderLines(context: StiContext, geom: StiAreaGeom, pointsInfo: StiSeriesPointsInfo): void;
        renderAreas(context: StiContext, geom: StiAreaGeom, pointsInfo: StiSeriesPointsInfo): void;
        renderSeries(context: StiContext, rect: RectangleD, geom: StiAreaGeom, series: IStiSeries[]): void;
        private calculateTotalHeight;
        private correctPoint;
        getSeriesBrush(colorIndex: number, colorCount: number): StiBrush;
        getSeriesBorderColor(colorIndex: number, colorCount: number): Color;
        get isFullStacked(): boolean;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    class StiStackedLineSeriesCoreXF extends StiStackedBaseLineSeriesCoreXF {
        renderLines(context: StiContext, geom: StiAreaGeom, pointsInfo: StiSeriesPointsInfo): void;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    class StiStackedAreaSeriesCoreXF extends StiStackedLineSeriesCoreXF {
        applyStyle(style: IStiChartStyle, color: Color): void;
        renderAreas(context: StiContext, geom: StiAreaGeom, pointsInfo: StiSeriesPointsInfo): void;
        getSeriesBrush(colorIndex: number, colorCount: number): StiBrush;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiFullStackedAreaSeriesCoreXF extends StiStackedAreaSeriesCoreXF {
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    class StiStackedColumnSeriesCoreXF extends StiSeriesCoreXF {
        applyStyle(style: IStiChartStyle, color: Color): void;
        renderSeries(context: StiContext, rect: RectangleD, geom: StiAreaGeom, series: IStiSeries[]): void;
        private calculateTotalHeight;
        private correctRect;
        getSeriesBrush(colorIndex: number, colorCount: number): StiBrush;
        getSeriesBorderColor(colorIndex: number, colorCount: number): Color;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiFullStackedColumnSeriesCoreXF extends StiStackedColumnSeriesCoreXF {
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiFullStackedLineSeriesCoreXF extends StiStackedLineSeriesCoreXF {
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    class StiStackedSplineSeriesCoreXF extends StiStackedBaseLineSeriesCoreXF {
        renderLines(context: StiContext, geom: StiAreaGeom, pointsInfo: StiSeriesPointsInfo): void;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    class StiStackedSplineAreaSeriesCoreXF extends StiStackedSplineSeriesCoreXF {
        applyStyle(style: IStiChartStyle, color: Color): void;
        renderAreas(context: StiContext, geom: StiAreaGeom, pointsInfo: StiSeriesPointsInfo): void;
        getSeriesBrush(colorIndex: number, colorCount: number): StiBrush;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiFullStackedSplineAreaSeriesCoreXF extends StiStackedSplineAreaSeriesCoreXF {
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiFullStackedSplineSeriesCoreXF extends StiStackedSplineSeriesCoreXF {
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    class StiFunnelSeriesCoreXF extends StiSeriesCoreXF {
        private labels;
        applyStyle(style: IStiChartStyle, color: Color): void;
        isNotNullValues(seriesArray: IStiSeries[]): boolean;
        renderSeries(context: StiContext, rect: RectangleD, geom: StiAreaGeom, seriesArray: IStiSeries[]): void;
        private checkNonZerovalue;
        getSeriesBorderColor(colorIndex: number, colorCount: number): Color;
        getSeriesBrush(colorIndex: number, colorCount: number): StiBrush;
        getCurrentValue(funnelSeries: IStiFunnelSeries, index: number, values: number[]): number;
        getNextCurrentValue(funnelSeries: IStiFunnelSeries, index: number, values: number[]): number;
        getAllValues(funnelSeries: IStiFunnelSeries[]): number[];
        getAllTrueValues(funnelSeries: IStiFunnelSeries[]): number[];
        private getValues;
        private getArgumentText;
        private renderFunnelEmpty;
        private renderFunnelElement;
        private getSingleValueHeight;
        private getSingleValueWidth;
        private measureFunnelElementCore;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    class StiFunnelWeightedSlicesSeriesCoreXF extends StiSeriesCoreXF {
        private labels;
        applyStyle(style: IStiChartStyle, color: Color): void;
        isNotNullValues(seriesArray: IStiSeries[]): boolean;
        renderSeries(context: StiContext, rect: RectangleD, geom: StiAreaGeom, seriesArray: IStiSeries[]): void;
        private checkNonZerovalue;
        getSeriesBorderColor(colorIndex: number, colorCount: number): Color;
        getSeriesBrush(colorIndex: number, colorCount: number): StiBrush;
        getAllValues(funnelSeries: IStiFunnelSeries[]): number[];
        getAllTrueValues(funnelSeries: IStiFunnelSeries[]): number[];
        private getValues;
        private getArgumentText;
        private renderFunnelEmpty;
        private getPathFunnelEmpty;
        private renderFunnelElement;
        private getSumValues;
        private getSumLastValues;
        private measureFunnelElementCore;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiGanttSeriesCoreXF extends StiClusteredBarSeriesCoreXF {
        renderSeries(context: StiContext, rect: RectangleD, geom: StiAreaGeom, series: IStiSeries[]): void;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import SizeD = Stimulsoft.System.Drawing.Size;
    class DataPictorial {
        value: number;
        series: StiPictorialSeries;
        index: number;
        constructor(value: number, series: StiPictorialSeries, index: number);
    }
    class StiPictorialSeriesCoreXF extends StiSeriesCoreXF {
        applyStyle(style: IStiChartStyle, color: Color): void;
        get localizedName(): string;
        private singleSizeConst;
        getSingleSize(context: StiContext): SizeD;
        renderSeries(context: StiContext, rect: RectangleD, geom: StiAreaGeom, seriesArray: IStiSeries[]): void;
        roundPictirialValue(currentFactorValue: number, deltaValue: number): number;
        getSeriesBrush(colorIndex: number, colorCount: number): StiBrush;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiPictorialStackedSeriesCoreXF extends StiSeriesCoreXF {
        private correctionAlfa;
        applyStyle(style: IStiChartStyle, color: Color): void;
        renderSeries(context: StiContext, rect: Rectangle, geom: StiAreaGeom, seriesCollection: IStiSeries[]): void;
        private correctionMainPoint;
        private getArgumentText;
        private getRectangle;
        private getStringFormatGeom;
        private measureFontSize;
        private getSumValues;
        getAllTrueValues(funnelSeries: IStiSeries[]): number[];
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiSeriesInteractionData = Stimulsoft.Base.Context.StiSeriesInteractionData;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import PointF = Stimulsoft.System.Drawing.Point;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    class StiPie3dSlice {
        private rectPieSlilce;
        private pieHeight;
        private seriesBrush;
        private borderColor;
        private series;
        private area;
        colorCount: number;
        startAngle: number;
        sweepAngle: number;
        get endAngle(): number;
        value: number;
        argumentText: string;
        tag: string;
        index: number;
        colorIndex: number;
        textPosition: PointF;
        interaction: StiSeriesInteractionData;
        startSideExists: boolean;
        endSideExists: boolean;
        drawLabels(areaGeom: StiAreaGeom, context: StiContext): void;
        drawTopPieSliceGeom(areaGeom: StiAreaGeom): void;
        drawBottomPieSliceGeom(areaGeom: StiAreaGeom): void;
        drawSides(areaGeom: StiAreaGeom): void;
        split(splitAngle: number): StiPie3dSlice[];
        initTextPosition(areaGeom: StiAreaGeom): void;
        private getNewModified;
        constructor(area: IStiArea, value: number, argumentText: string, tag: string, index: number, series: IStiPie3dSeries, rectPieSlilce: Rectangle, pieHeight: number, startAngle: number, sweepAngle: number, seriesBrush: StiBrush, borderColor: Color, colorIndex: number, colorCount: number);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Size = Stimulsoft.System.Drawing.Size;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    class StiPie3dSeriesCoreXF extends StiPieSeriesCoreXF {
        private mPieSlices;
        private pieHeight;
        renderSeries(context: StiContext, rect: Rectangle, geom: StiAreaGeom, seriesArray: IStiSeries[]): void;
        private drawLabels;
        private drawTops;
        private drawSliceSides;
        private drawBottoms;
        private measureBoundingRect;
        protected initializePieSlices(area: IStiArea, mainRect: Rectangle, seriesArray: IStiSeries[], zoom: number): void;
        protected initializeEmptyPieSlices(area: IStiArea, mainRect: Rectangle, seriesArray: IStiSeries[], zoom: number): void;
        private getTopEllipseSize;
        protected getSliceDisplacement(angle: number, xDisplacement: number, yDisplacement: number): Size;
        private getLargestDisplacement;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import Color = Stimulsoft.System.Drawing.Color;
    import PointD = Stimulsoft.System.Drawing.Point;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    class StiRadarSeriesCoreXF extends StiSeriesCoreXF {
        applyStyle(style: IStiChartStyle, color: Color): void;
        private getPointsList;
        renderSeries(context: StiContext, rect: RectangleD, geom: StiAreaGeom, seriesArray: IStiSeries[]): void;
        renderAreas(context: StiContext, series: IStiRadarSeries, pointsInfo: StiSeriesPointsInfo, geom: StiAreaGeom): void;
        renderLines(context: StiContext, series: IStiRadarSeries, pointsInfo: StiSeriesPointsInfo, geom: StiAreaGeom): void;
        renderPoints(context: StiContext, series: IStiRadarSeries, points: PointD[], geom: StiAreaGeom): void;
        private getArgument;
        getSeriesBrush(colorIndex: number, colorCount: number): StiBrush;
        getSeriesBorderColor(colorIndex: number, colorCount: number): Color;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiRadarAreaSeriesCoreXF extends StiRadarSeriesCoreXF {
        applyStyle(style: IStiChartStyle, color: Color): void;
        get localizedName(): string;
        renderLines(context: StiContext, series: IStiRadarSeries, pointsInfo: StiSeriesPointsInfo, geom: StiAreaGeom): void;
        renderAreas(context: StiContext, series: IStiRadarSeries, pointsInfo: StiSeriesPointsInfo, geom: StiAreaGeom): void;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiRadarLineSeriesCoreXF extends StiRadarSeriesCoreXF {
        applyStyle(style: IStiChartStyle, color: Color): void;
        get localizedName(): string;
        renderLines(context: StiContext, series: IStiRadarSeries, pointsInfo: StiSeriesPointsInfo, geom: StiAreaGeom): void;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Color = Stimulsoft.System.Drawing.Color;
    class StiRadarPointSeriesCoreXF extends StiRadarSeriesCoreXF {
        applyStyle(style: IStiChartStyle, color: Color): void;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiRangeBarSeriesCoreXF extends StiClusteredColumnSeriesCoreXF {
        renderSeries(context: StiContext, rect: RectangleD, geom: StiAreaGeom, series: IStiSeries[]): void;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiRangeSeriesCoreXF extends StiLineSeriesCoreXF {
        applyStyle(style: IStiChartStyle, color: Color): void;
        renderSeries(context: StiContext, rect: RectangleD, geom: StiAreaGeom, series: IStiSeries[]): void;
        private renderLines2;
        private renderMarkers2;
        private getYPoint;
        private renderAreas2;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiSplineRangeSeriesCoreXF extends StiSplineSeriesCoreXF {
        applyStyle(style: IStiChartStyle, color: Color): void;
        renderSeries(context: StiContext, rect: RectangleD, geom: StiAreaGeom, series: IStiSeries[]): void;
        private getYPoint;
        private renderAreas2;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiSteppedRangeSeriesCoreXF extends StiSteppedLineSeriesCoreXF {
        applyStyle(style: IStiChartStyle, color: Color): void;
        renderSeries(context: StiContext, rect: RectangleD, geom: StiAreaGeom, series: IStiSeries[]): void;
        private renderAreas2;
        private renderLines2;
        private renderMarkers2;
        private getYPoint;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    class StiScatterLineSeriesCoreXF extends StiScatterSeriesCoreXF {
        renderLines(context: StiContext, geom: StiAreaGeom, pointsInfo: StiSeriesPointsInfo): void;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    class StiScatterSplineSeriesCoreXF extends StiScatterSeriesCoreXF {
        renderLines(context: StiContext, geom: StiAreaGeom, pointsInfo: StiSeriesPointsInfo): void;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    class StiRibbonSeriesCoreXF extends StiSeriesCoreXF {
        applyStyle(style: IStiChartStyle, color: Color): void;
        renderSeries(context: StiContext, rect: RectangleD, geom: StiAreaGeom, series: IStiSeries[]): void;
        private static getValueFromArray;
        private correctRect;
        getSeriesBrush(colorIndex: number, colorCount: number): StiBrush;
        getSeriesBorderColor(colorIndex: number, colorCount: number): any;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Color = Stimulsoft.System.Drawing.Color;
    import PointF = Stimulsoft.System.Drawing.Point;
    class StiSunburstSeriesCoreXF extends StiSeriesCoreXF {
        applyStyle(style: IStiChartStyle, color: Color): void;
        renderSeries(context: StiContext, rect: Rectangle, geom: StiAreaGeom, seriesCollection: IStiSeries[]): void;
        private renderComputeSeries;
        private renderLevelSeries;
        private renderLevelSeriesLebels;
        private renderSunburstElement;
        protected getPoint(centerPie: PointF, radius: number, angle: number): PointF;
        private getDataTable;
        private getCountRow;
        private getGradPerValue;
        protected getRadius(context: StiContext, rect: Rectangle): number;
        protected getPointCenter(rect: Rectangle): PointF;
        private getSumColumn;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiTreemapSeriesCoreXF extends StiSeriesCoreXF {
        applyStyle(style: IStiChartStyle, color: Color): void;
        renderSeries(context: StiContext, rect: RectangleD, geom: StiAreaGeom, seriesArray: IStiSeries[]): void;
        getArgumentText(series: IStiSeries, index: number): string;
        getSeriesBrush(colorIndex: number, colorCount: number): StiBrush;
        getSeriesBorderColor(colorIndex: number, colorCount: number): Color;
        get localizedName(): string;
        constructor(series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IAsIs = Stimulsoft.System.IAsIs;
    import StiStringFormatGeom = Stimulsoft.Base.Context.StiStringFormatGeom;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import ICloneable = Stimulsoft.System.ICloneable;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    class StiSeriesLabelsCoreXF implements ICloneable, IStiApplyStyle, IStiSeriesLabelsCoreXF, IAsIs {
        private static implementsStiSeriesLabelsCoreXF;
        implements(): any[];
        is<T>(type: (new (...args: any[]) => T) | Stimulsoft.System.Interface<T>): this is T;
        is2<T>(type: (new (...args: any[]) => T) | Stimulsoft.System.Interface<T>): boolean;
        as<T>(type: (new (...args: any[]) => T) | Stimulsoft.System.Interface<T>): T;
        clone(): StiSeriesLabelsCoreXF;
        applyStyle(style: IStiChartStyle): void;
        private storedCulture;
        get position(): number;
        get seriesLabelsType(): StiSeriesLabelsType;
        seriesLabels: IStiSeriesLabels;
        get localizedName(): string;
        processSeriesColors(pointIndex: number, brush: StiBrush, series: IStiSeries): StiBrush;
        getSeriesLabelColor(series: IStiSeries, colorIndex: number, colorCount: number): Color;
        getBorderColor(series: IStiSeries, colorIndex: number, colorCount: number): Color;
        getLabelColor(series: IStiSeries, colorIndex: number, colorCount: number): Color;
        recalcValue(value: number, signs: number): number;
        getLabelText(series: IStiSeries, value: number, argument: string, tag: string, seriesName: string, useLegendValueType?: boolean): string;
        getLabelText2(series: IStiSeries, value: number, argument: string, tag: string, seriesName: string, weight: number, useLegendValueType: boolean): string;
        private getArgument;
        private getFormatted;
        getFormattedValue(series: IStiSeries, value: number): string;
        getStringFormatGeom(context: StiContext): StiStringFormatGeom;
        constructor(seriesLabels: IStiSeriesLabels);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiNoneLabelsCoreXF extends StiSeriesLabelsCoreXF {
        get seriesLabelsType(): StiSeriesLabelsType;
        get position(): number;
        get localizedName(): string;
        constructor(seriesLabels: IStiSeriesLabels);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import PointD = Stimulsoft.System.Drawing.Point;
    import StiAnimation = Stimulsoft.Base.Context.Animation.StiAnimation;
    class StiAxisSeriesLabelsCoreXF extends StiSeriesLabelsCoreXF {
        renderLabel(series: IStiSeries, context: StiContext, endPoint: PointD, startPoint: PointD, pointIndex: number, value: number, labelValue: number, argumentText: string, tag: string, colorIndex: number, colorCount: number, rect: RectangleD, animation?: StiAnimation): StiSeriesLabelsGeom;
        renderLabel2(series: IStiSeries, context: StiContext, endPoint: PointD, startPoint: PointD, pointIndex: number, value: number, labelValue: number, argumentText: string, tag: string, weight: number, colorIndex: number, colorCount: number, rect: RectangleD, animation?: StiAnimation): StiSeriesLabelsGeom;
        recalcValue(value: number, signs: number): number;
        get seriesLabelsType(): StiSeriesLabelsType;
        currentIndex: number;
        constructor(seriesLabels: IStiSeriesLabels);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiStringFormatGeom = Stimulsoft.Base.Context.StiStringFormatGeom;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiFontGeom = Stimulsoft.Base.Context.StiFontGeom;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import PointD = Stimulsoft.System.Drawing.Point;
    import StiAnimation = Stimulsoft.Base.Context.Animation.StiAnimation;
    class StiCenterAxisLabelsCoreXF extends StiAxisSeriesLabelsCoreXF {
        renderLabel(series: IStiSeries, context: StiContext, endPoint: PointD, startPoint: PointD, pointIndex: number, value: number, labelValue: number, argumentText: string, tag: string, colorIndex: number, colorCount: number, rect: RectangleD, animation?: StiAnimation): StiSeriesLabelsGeom;
        renderLabel2(series: IStiSeries, context: StiContext, endPoint: PointD, startPoint: PointD, pointIndex: number, value: number, labelValue: number, argumentText: string, tag: string, weight: number, colorIndex: number, colorCount: number, rect: RectangleD, animation?: StiAnimation): StiSeriesLabelsGeom;
        getLabelRect(context: StiContext, endPoint: PointD, startPoint: PointD, value: number, labelText: string, checkHeight: boolean, font: StiFontGeom, sf: StiStringFormatGeom): RectangleD;
        get position(): number;
        get localizedName(): string;
        constructor(seriesLabels: IStiSeriesLabels);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiStringFormatGeom = Stimulsoft.Base.Context.StiStringFormatGeom;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiFontGeom = Stimulsoft.Base.Context.StiFontGeom;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import PointD = Stimulsoft.System.Drawing.Point;
    class StiInsideBaseAxisLabelsCoreXF extends StiCenterAxisLabelsCoreXF {
        get localizedName(): string;
        get position(): number;
        getLabelRect(context: StiContext, endPoint: PointD, startPoint: PointD, value: number, labelText: string, checkHeight: boolean, font: StiFontGeom, sf: StiStringFormatGeom): RectangleD;
        constructor(seriesLabels: IStiSeriesLabels);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiStringFormatGeom = Stimulsoft.Base.Context.StiStringFormatGeom;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiFontGeom = Stimulsoft.Base.Context.StiFontGeom;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import PointD = Stimulsoft.System.Drawing.Point;
    class StiInsideEndAxisLabelsCoreXF extends StiCenterAxisLabelsCoreXF {
        get position(): number;
        get localizedName(): string;
        getLabelRect(context: StiContext, endPoint: PointD, startPoint: PointD, value: number, labelText: string, checkHeight: boolean, font: StiFontGeom, sf: StiStringFormatGeom): RectangleD;
        constructor(seriesLabels: IStiSeriesLabels);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiStringFormatGeom = Stimulsoft.Base.Context.StiStringFormatGeom;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiFontGeom = Stimulsoft.Base.Context.StiFontGeom;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import PointD = Stimulsoft.System.Drawing.Point;
    class StiLeftAxisLabelsCoreXF extends StiCenterAxisLabelsCoreXF {
        get localizedName(): string;
        get position(): number;
        getLabelRect(context: StiContext, endPoint: PointD, startPoint: PointD, value: number, labelText: string, checkHeight: boolean, font: StiFontGeom, sf: StiStringFormatGeom): RectangleD;
        constructor(seriesLabels: IStiSeriesLabels);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import PointD = Stimulsoft.System.Drawing.Point;
    class StiOutsideAxisLabelsCoreXF extends StiAxisSeriesLabelsCoreXF {
        renderLabel(series: IStiSeries, context: StiContext, endPoint: PointD, startPoint: PointD, pointIndex: number, value: number, labelValue: number, argumentText: string, tag: string, colorIndex: number, colorCount: number, rect: RectangleD): StiSeriesLabelsGeom;
        get position(): number;
        get localizedName(): string;
        constructor(seriesLabels: IStiSeriesLabels);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiStringFormatGeom = Stimulsoft.Base.Context.StiStringFormatGeom;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiFontGeom = Stimulsoft.Base.Context.StiFontGeom;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import PointD = Stimulsoft.System.Drawing.Point;
    class StiOutsideBaseAxisLabelsCoreXF extends StiCenterAxisLabelsCoreXF {
        get localizedName(): string;
        get position(): number;
        getLabelRect(context: StiContext, endPoint: PointD, startPoint: PointD, value: number, labelText: string, checkHeight: boolean, font: StiFontGeom, sf: StiStringFormatGeom): RectangleD;
        constructor(seriesLabels: IStiSeriesLabels);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiStringFormatGeom = Stimulsoft.Base.Context.StiStringFormatGeom;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiFontGeom = Stimulsoft.Base.Context.StiFontGeom;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import PointD = Stimulsoft.System.Drawing.Point;
    class StiOutsideEndAxisLabelsCoreXF extends StiCenterAxisLabelsCoreXF {
        get localizedName(): string;
        get position(): number;
        getLabelRect(context: StiContext, endPoint: PointD, startPoint: PointD, value: number, labelText: string, checkHeight: boolean, font: StiFontGeom, sf: StiStringFormatGeom): RectangleD;
        constructor(seriesLabels: IStiSeriesLabels);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiStringFormatGeom = Stimulsoft.Base.Context.StiStringFormatGeom;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiFontGeom = Stimulsoft.Base.Context.StiFontGeom;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import PointD = Stimulsoft.System.Drawing.Point;
    class StiRightAxisLabelsCoreXF extends StiCenterAxisLabelsCoreXF {
        get localizedName(): string;
        get position(): number;
        getLabelRect(context: StiContext, endPoint: PointD, startPoint: PointD, value: number, labelText: string, checkHeight: boolean, font: StiFontGeom, sf: StiStringFormatGeom): RectangleD;
        constructor(seriesLabels: IStiSeriesLabels);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiStringFormatGeom = Stimulsoft.Base.Context.StiStringFormatGeom;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiFontGeom = Stimulsoft.Base.Context.StiFontGeom;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import PointD = Stimulsoft.System.Drawing.Point;
    class StiValueAxisLabelsCoreXF extends StiCenterAxisLabelsCoreXF {
        get localizedName(): string;
        get position(): number;
        getLabelRect(context: StiContext, endPoint: PointD, startPoint: PointD, value: number, labelText: string, checkHeight: boolean, font: StiFontGeom, sf: StiStringFormatGeom): RectangleD;
        constructor(seriesLabels: IStiSeriesLabels);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiFunnelSeriesLabelsCoreXF extends StiSeriesLabelsCoreXF {
        renderLabel(series: IStiSeries, context: StiContext, pointIndex: number, value: number, valueNext: number, argumentText: string, tag: string, colorIndex: number, colorCount: number, rect: RectangleD, singleValueHeight: number, singleValueWidth: number, centerAxis: number, REFmeasureRect: any): StiSeriesLabelsGeom;
        get seriesLabelsType(): StiSeriesLabelsType;
        constructor(seriesLabels: IStiSeriesLabels);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiCenterFunnelLabelsCoreXF extends StiFunnelSeriesLabelsCoreXF {
        renderLabel(series: IStiSeries, context: StiContext, pointIndex: number, value: number, valueNext: number, argumentText: string, tag: string, colorIndex: number, colorCount: number, rect: RectangleD, singleValueHeight: number, singleValueWidth: number, centerAxis: number, REFmeasureRect: any): StiSeriesLabelsGeom;
        private getSumLastValues;
        get seriesLabelsType(): StiSeriesLabelsType;
        get position(): number;
        get localizedName(): string;
        constructor(seriesLabels: IStiSeriesLabels);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiOutsideLeftFunnelLabelsCoreXF extends StiFunnelSeriesLabelsCoreXF {
        renderLabel(series: IStiSeries, context: StiContext, pointIndex: number, value: number, valueNext: number, argumentText: string, tag: string, colorIndex: number, colorCount: number, rect: RectangleD, singleValueHeight: number, singleValueWidth: number, centerAxis: number, REFmeasureRect: any): StiSeriesLabelsGeom;
        get seriesLabelsType(): StiSeriesLabelsType;
        get position(): number;
        get localizedName(): string;
        constructor(seriesLabels: IStiSeriesLabels);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiOutsideRightFunnelLabelsCoreXF extends StiFunnelSeriesLabelsCoreXF {
        renderLabel(series: IStiSeries, context: StiContext, pointIndex: number, value: number, valueNext: number, argumentText: string, tag: string, colorIndex: number, colorCount: number, rect: RectangleD, singleValueHeight: number, singleValueWidth: number, centerAxis: number, REFmeasureRect: any): StiSeriesLabelsGeom;
        get seriesLabelsType(): StiSeriesLabelsType;
        get position(): number;
        get localizedName(): string;
        constructor(seriesLabels: IStiSeriesLabels);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiPictorialStackedLabelsCoreXF extends StiSeriesLabelsCoreXF {
        renderLabel(series: IStiSeries, context: StiContext, pointIndex: number, value: number, argumentText: string, tag: string, colorIndex: number, colorCount: number, lineLength: number, rect: RectangleD): StiSeriesLabelsGeom;
        get seriesLabelsType(): StiSeriesLabelsType;
        constructor(seriesLabels: IStiSeriesLabels);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    class StiCenterPictorialStackedLabelsCoreXF extends StiPictorialStackedLabelsCoreXF {
        renderLabel(series: IStiSeries, context: StiContext, pointIndex: number, value: number, argumentText: string, tag: string, colorIndex: number, colorCount: number, lineLength: number, rect: Rectangle): StiSeriesLabelsGeom;
        get seriesLabelsType(): StiSeriesLabelsType;
        get position(): number;
        get localizedName(): string;
        constructor(seriesLabels: IStiSeriesLabels);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    class StiOutsideLeftPictorialStackedLabelsCoreXF extends StiPictorialStackedLabelsCoreXF {
        applyStyle(style: IStiChartStyle): void;
        renderLabel(series: IStiSeries, context: StiContext, pointIndex: number, value: number, argumentText: string, tag: string, colorIndex: number, colorCount: number, lineLength: number, rect: Rectangle): StiSeriesLabelsGeom;
        get seriesLabelsType(): StiSeriesLabelsType;
        get position(): number;
        get localizedName(): string;
        constructor(seriesLabels: IStiSeriesLabels);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    class StiOutsideRightPictorialStackedLabelsCoreXF extends StiPictorialStackedLabelsCoreXF {
        applyStyle(style: IStiChartStyle): void;
        renderLabel(series: IStiSeries, context: StiContext, pointIndex: number, value: number, argumentText: string, tag: string, colorIndex: number, colorCount: number, lineLength: number, rect: Rectangle): StiSeriesLabelsGeom;
        get seriesLabelsType(): StiSeriesLabelsType;
        get position(): number;
        get localizedName(): string;
        constructor(seriesLabels: IStiSeriesLabels);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiStringFormatGeom = Stimulsoft.Base.Context.StiStringFormatGeom;
    import StiFontGeom = Stimulsoft.Base.Context.StiFontGeom;
    import Point = Stimulsoft.System.Drawing.Point;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    class StiCenterPie3dLabelsCoreXF extends StiSeriesLabelsCoreXF {
        percentPerValue: number;
        get seriesLabelsType(): StiSeriesLabelsType;
        get position(): number;
        get localizedName(): string;
        renderLabel2(series: IStiSeries, context: StiContext, pie3dSlice: StiPie3dSlice): StiSeriesLabelsGeom;
        getLabelRect(context: StiContext, labelPoint: Point, labelText: string, font: StiFontGeom, sf: StiStringFormatGeom): Rectangle;
        recalcValue(value: number, signs: number): number;
        constructor(seriesLabels: IStiSeriesLabels);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import PointD = Stimulsoft.System.Drawing.Point;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiPieSeriesLabelsCoreXF extends StiSeriesLabelsCoreXF {
        renderLabel(series: IStiSeries, context: StiContext, centerPie: PointD, radius: number, radius2: number, pieAngle: number, pointIndex: number, value: number, labelValue: number, argumentText: string, tag: string, measure: boolean, colorIndex: number, colorCount: number, percentPerValue: number, REFmeasureRect: {
            ref: RectangleD;
        }, drawValue: boolean, deltaY: number): StiSeriesLabelsGeom;
        recalcValue(value: number, signs: number): number;
        get seriesLabelsType(): StiSeriesLabelsType;
        percentPerValue: number;
        constructor(seriesLabels: IStiSeriesLabels);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiStringFormatGeom = Stimulsoft.Base.Context.StiStringFormatGeom;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiFontGeom = Stimulsoft.Base.Context.StiFontGeom;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import PointD = Stimulsoft.System.Drawing.Point;
    class StiCenterPieLabelsCoreXF extends StiPieSeriesLabelsCoreXF {
        renderLabel(series: IStiSeries, context: StiContext, centerPie: PointD, radius: number, radius2: number, pieAngle: number, pointIndex: number, value: number, labelValue: number, argumentText: string, tag: string, measure: boolean, colorIndex: number, colorCount: number, percentPerValue: number, REFmeasureRect: {
            ref: RectangleD;
        }, drawValue: boolean, deltaY: number): StiSeriesLabelsGeom;
        getLabelPoint(centerPie: PointD, radius: number, angleRad: number): PointD;
        getLabelRect(context: StiContext, labelPoint: PointD, labelText: string, font: StiFontGeom, sf: StiStringFormatGeom): RectangleD;
        get seriesLabelsType(): StiSeriesLabelsType;
        get position(): number;
        get localizedName(): string;
        constructor(seriesLabels: IStiSeriesLabels);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiInsideEndPieLabelsCoreXF extends StiCenterPieLabelsCoreXF {
        get localizedName(): string;
        get position(): number;
        constructor(seriesLabels: IStiSeriesLabels);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Color = Stimulsoft.System.Drawing.Color;
    class StiOutsidePieLabelsCoreXF extends StiCenterPieLabelsCoreXF {
        applyStyle(style: IStiChartStyle): void;
        get position(): number;
        get localizedName(): string;
        getLineColor(series: IStiSeries, colorIndex: number, colorCount: number): Color;
        constructor(seriesLabels: IStiSeriesLabels);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import PointD = Stimulsoft.System.Drawing.Point;
    class StiTwoColumnsPieLabelsCoreXF extends StiOutsidePieLabelsCoreXF {
        renderLabel(series: IStiSeries, context: StiContext, centerPie: PointD, radius: number, radius2: number, pieAngle: number, pointIndex: number, value: number, labelValue: number, argumentText: string, tag: string, measure: boolean, colorIndex: number, colorCount: number, percentPerValue: number, REFmeasureRect: any, drawValue: boolean, deltaY: number): StiSeriesLabelsGeom;
        get seriesLabelsType(): StiSeriesLabelsType;
        get position(): number;
        get localizedName(): string;
        constructor(seriesLabels: IStiSeriesLabels);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiStringFormatGeom = Stimulsoft.Base.Context.StiStringFormatGeom;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiFontGeom = Stimulsoft.Base.Context.StiFontGeom;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import StiAnimation = Stimulsoft.Base.Context.Animation.StiAnimation;
    class StiCenterTreemapLabelsCoreXF extends StiSeriesLabelsCoreXF {
        renderLabel(series: IStiSeries, context: StiContext, pointIndex: number, value: number, argumentText: string, tag: string, colorIndex: number, colorCount: number, rect: RectangleD, animation?: StiAnimation): StiSeriesLabelsGeom;
        getLabelRect(context: StiContext, rect: RectangleD, value: number, labelText: string, checkHeight: boolean, font: StiFontGeom, sf: StiStringFormatGeom): RectangleD;
        get position(): number;
        get localizedName(): string;
        get seriesLabelsType(): StiSeriesLabelsType;
        constructor(seriesLabels: IStiSeriesLabels);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiStripsCoreXF implements IStiApplyStyle, ICloneable, IStiStripsCoreXF {
        private static implementsStiStripsCoreXF;
        implements(): any[];
        clone(): any;
        applyStyle(style: IStiChartStyle): void;
        renderXStrips(context: StiContext, geom: StiAxisAreaGeom, rect: RectangleD): void;
        private calculateXValue;
        renderYStrips(context: StiContext, geom: StiAxisAreaGeom, rect: RectangleD): void;
        private calculateYValue;
        render(context: StiContext, geom: StiAxisAreaGeom, rect: RectangleD): void;
        strips: IStiStrips;
        constructor(strips: IStiStrips);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiSimpleBorder = Stimulsoft.Base.Drawing.StiSimpleBorder;
    import StiCornerRadius = Stimulsoft.Base.Drawing.StiCornerRadius;
    import Font = Stimulsoft.System.Drawing.Font;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import StiInteractionDataGeom = Stimulsoft.Base.Context.StiInteractionDataGeom;
    class StiStyleCoreXF implements IStiStyleCoreXF {
        private static implementsStiStyleCoreXF;
        implements(): any[];
        get localizedName(): string;
        get styleId(): StiChartStyleId;
        get chartBrush(): StiBrush;
        get chartAreaBrush(): StiBrush;
        get chartAreaBorderColor(): Color;
        get chartAreaBorderThickness(): number;
        chart: IStiChart;
        get chartAreaShowShadow(): boolean;
        get seriesLabelsBrush(): StiBrush;
        get seriesLabelsColor(): Color;
        get seriesLabelsBorderColor(): Color;
        get seriesLabelsLineColor(): Color;
        get seriesLabelsFont(): Font;
        get toolTipBrush(): StiBrush;
        get toolTipTextBrush(): StiBrush;
        get toolTipCornerRadius(): StiCornerRadius;
        get toolTipBorder(): StiSimpleBorder;
        get trendLineColor(): Color;
        get trendLineShowShadow(): boolean;
        get legendBrush(): StiBrush;
        get legendLabelsColor(): Color;
        get legendBorderColor(): Color;
        get legendTitleColor(): Color;
        get legendShowShadow(): boolean;
        get legendFont(): Font;
        get axisTitleColor(): Color;
        get axisLineColor(): Color;
        get axisLabelsColor(): Color;
        get interlacingHorBrush(): StiBrush;
        get interlacingVertBrush(): StiBrush;
        get gridLinesHorColor(): Color;
        get gridLinesVertColor(): Color;
        get seriesLighting(): boolean;
        get seriesShowShadow(): boolean;
        get seriesShowBorder(): boolean;
        get seriesBorderColor(): Color;
        get seriesBorderThickness(): number;
        get seriesCornerRadius(): StiCornerRadius;
        private _markerVisible;
        get markerVisible(): boolean;
        set markerVisible(value: boolean);
        get firstStyleColor(): Color;
        get lastStyleColor(): Color;
        get styleColors(): Color[];
        get basicStyleColor(): Color;
        fillColumn(context: StiContext, rect: RectangleD, brush: StiBrush, interaction: StiInteractionDataGeom): void;
        fillCicledColumn(context: StiContext, rect: RectangleD, cornerRadius: StiCornerRadius, brush: StiBrush, interaction: StiInteractionDataGeom): void;
        getAreaBrush(color: Color): StiBrush;
        getColumnBrush(color: Color): StiBrush;
        getColumnBorder(color: Color): Color;
        getColors(seriesCount: number, seriesColors: Color[]): Color[];
        getColorByIndex(index: number, count: number, seriesColors: Color[]): Color;
        getColorBySeries(series: IStiSeries, seriesColors: Color[]): Color;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiStyleCoreXF01 extends StiStyleCoreXF {
        get localizedName(): string;
        protected _styleColor: Color[];
        get styleColors(): Color[];
        get basicStyleColor(): Color;
        get styleId(): StiChartStyleId;
        getColumnBrush(color: Color): StiBrush;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiSimpleBorder = Stimulsoft.Base.Drawing.StiSimpleBorder;
    import StiCornerRadius = Stimulsoft.Base.Drawing.StiCornerRadius;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiCustomStyleCoreXF extends StiStyleCoreXF01 {
        private _base;
        get localizedName(): string;
        reportChartStyle: Stimulsoft.Report.Styles.StiChartStyle;
        get chartBrush(): StiBrush;
        get chartAreaBrush(): StiBrush;
        get chartAreaBorderColor(): Color;
        get chartAreaBorderThickness(): number;
        get chartAreaShowShadow(): boolean;
        get toolTipBrush(): StiBrush;
        get toolTipTextBrush(): StiBrush;
        get toolTipCornerRadius(): StiCornerRadius;
        get toolTipBorder(): StiSimpleBorder;
        get seriesLighting(): boolean;
        get seriesShowShadow(): boolean;
        get seriesShowBorder(): boolean;
        get seriesBorderColor(): Color;
        get seriesLabelsBrush(): StiBrush;
        get seriesLabelsColor(): Color;
        get seriesLabelsBorderColor(): Color;
        get seriesLabelsLineColor(): Color;
        get seriesBorderThickness(): number;
        get seriesCornerRadius(): StiCornerRadius;
        get trendLineColor(): Color;
        get trendLineShowShadow(): boolean;
        get legendBrush(): StiBrush;
        get legendLabelsColor(): Color;
        get legendBorderColor(): Color;
        get legendTitleColor(): Color;
        get markerVisible(): boolean;
        set markerVisible(value: boolean);
        get axisTitleColor(): Color;
        get axisLineColor(): Color;
        get axisLabelsColor(): Color;
        get interlacingHorBrush(): StiBrush;
        get interlacingVertBrush(): StiBrush;
        get gridLinesHorColor(): Color;
        get gridLinesVertColor(): Color;
        get styleColors(): Color[];
        get basicStyleColor(): Color;
        reportStyleName: string;
        get reportStyle(): Stimulsoft.Report.Styles.StiChartStyle;
        private _customStyle;
        get customStyle(): StiCustomStyle;
        getColumnBrush(color: Color): StiBrush;
        constructor(customStyle: StiCustomStyle);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiStyleCoreXF02 extends StiStyleCoreXF {
        get localizedName(): string;
        protected _styleColor: Color[];
        get basicStyleColor(): Color;
        get styleColors(): Color[];
        get styleId(): StiChartStyleId;
        getColumnBrush(color: Color): StiBrush;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiStyleCoreXF03 extends StiStyleCoreXF {
        get localizedName(): string;
        protected _styleColor: Color[];
        get styleColors(): Color[];
        get basicStyleColor(): Color;
        get styleId(): StiChartStyleId;
        getColumnBrush(color: Color): StiBrush;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Color = Stimulsoft.System.Drawing.Color;
    class StiStyleCoreXF04 extends StiStyleCoreXF {
        get localizedName(): string;
        protected _styleColor: Color[];
        get basicStyleColor(): Color;
        get styleColors(): Color[];
        get styleId(): StiChartStyleId;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Color = Stimulsoft.System.Drawing.Color;
    class StiStyleCoreXF05 extends StiStyleCoreXF {
        get localizedName(): string;
        protected _styleColor: Color[];
        get styleColors(): Color[];
        get basicStyleColor(): Color;
        get styleId(): StiChartStyleId;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiStyleCoreXF06 extends StiStyleCoreXF {
        get localizedName(): string;
        protected _styleColor: Color[];
        get styleColors(): Color[];
        get basicStyleColor(): Color;
        get styleId(): StiChartStyleId;
        getColumnBrush(color: Color): StiBrush;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiStyleCoreXF07 extends StiStyleCoreXF {
        get localizedName(): string;
        protected _styleColor: Color[];
        get styleColors(): Color[];
        get basicStyleColor(): Color;
        get styleId(): StiChartStyleId;
        getColumnBrush(color: Color): StiBrush;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiStyleCoreXF08 extends StiStyleCoreXF03 {
        get localizedName(): string;
        protected _styleColor: Color[];
        get styleColors(): Color[];
        get basicStyleColor(): Color;
        get styleId(): StiChartStyleId;
        getColumnBrush(color: Color): StiBrush;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Color = Stimulsoft.System.Drawing.Color;
    class StiStyleCoreXF09 extends StiStyleCoreXF {
        get localizedName(): string;
        protected _styleColor: Color[];
        get styleColors(): Color[];
        get basicStyleColor(): Color;
        get styleId(): StiChartStyleId;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Color = Stimulsoft.System.Drawing.Color;
    class StiStyleCoreXF10 extends StiStyleCoreXF {
        get localizedName(): string;
        protected _styleColor: Color[];
        get styleColors(): Color[];
        get basicStyleColor(): Color;
        get styleId(): StiChartStyleId;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Color = Stimulsoft.System.Drawing.Color;
    class StiStyleCoreXF11 extends StiStyleCoreXF {
        get localizedName(): string;
        protected _styleColor: Color[];
        get styleColors(): Color[];
        get basicStyleColor(): Color;
        get styleId(): StiChartStyleId;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Color = Stimulsoft.System.Drawing.Color;
    class StiStyleCoreXF12 extends StiStyleCoreXF {
        get localizedName(): string;
        protected _styleColor: Color[];
        get styleColors(): Color[];
        get basicStyleColor(): Color;
        get styleId(): StiChartStyleId;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Color = Stimulsoft.System.Drawing.Color;
    class StiStyleCoreXF13 extends StiStyleCoreXF {
        get localizedName(): string;
        protected _styleColor: Color[];
        get styleColors(): Color[];
        get basicStyleColor(): Color;
        get styleId(): StiChartStyleId;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Color = Stimulsoft.System.Drawing.Color;
    class StiStyleCoreXF14 extends StiStyleCoreXF {
        get localizedName(): string;
        protected _styleColor: Color[];
        get styleColors(): Color[];
        get basicStyleColor(): Color;
        get styleId(): StiChartStyleId;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Color = Stimulsoft.System.Drawing.Color;
    class StiStyleCoreXF15 extends StiStyleCoreXF {
        get localizedName(): string;
        protected _styleColor: Color[];
        get styleColors(): Color[];
        get basicStyleColor(): Color;
        get styleId(): StiChartStyleId;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Color = Stimulsoft.System.Drawing.Color;
    class StiStyleCoreXF16 extends StiStyleCoreXF {
        get localizedName(): string;
        protected _styleColor: Color[];
        get styleColors(): Color[];
        get basicStyleColor(): Color;
        get styleId(): StiChartStyleId;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Color = Stimulsoft.System.Drawing.Color;
    class StiStyleCoreXF17 extends StiStyleCoreXF {
        get localizedName(): string;
        protected _styleColor: Color[];
        get styleColors(): Color[];
        get basicStyleColor(): Color;
        get styleId(): StiChartStyleId;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiInteractionDataGeom = Stimulsoft.Base.Context.StiInteractionDataGeom;
    class StiStyleCoreXF18 extends StiStyleCoreXF {
        get localizedName(): string;
        fillColumn(context: StiContext, rect: RectangleD, brush: StiBrush, interaction: StiInteractionDataGeom): void;
        getColumnBrush(color: Color): StiBrush;
        protected _styleColor: Color[];
        get styleColors(): Color[];
        get basicStyleColor(): Color;
        get styleId(): StiChartStyleId;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiStyleCoreXF19 extends StiStyleCoreXF {
        get localizedName(): string;
        protected _styleColor: Color[];
        get styleColors(): Color[];
        get basicStyleColor(): Color;
        get interlacingHorBrush(): StiBrush;
        get interlacingVertBrush(): StiBrush;
        get chartAreaBrush(): StiBrush;
        get chartBrush(): StiBrush;
        get styleId(): StiChartStyleId;
        getColumnBrush(color: Color): StiBrush;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiStyleCoreXF20 extends StiStyleCoreXF {
        get localizedName(): string;
        protected _styleColor: Color[];
        get styleColors(): Color[];
        get basicStyleColor(): Color;
        get axisLineColor(): Color;
        get chartAreaBorderColor(): Color;
        get styleId(): StiChartStyleId;
        getColumnBrush(color: Color): StiBrush;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiStyleCoreXF21 extends StiStyleCoreXF {
        get localizedName(): string;
        get chartBrush(): StiBrush;
        get chartAreaBrush(): StiBrush;
        get chartAreaBorderColor(): Color;
        get seriesLabelsBrush(): StiBrush;
        get seriesLabelsColor(): Color;
        get seriesLabelsBorderColor(): Color;
        get legendBrush(): StiBrush;
        get legendLabelsColor(): Color;
        get axisTitleColor(): Color;
        get axisLineColor(): Color;
        get axisLabelsColor(): Color;
        protected _styleColor: Color[];
        get styleColors(): Color[];
        get basicStyleColor(): Color;
        get styleId(): StiChartStyleId;
        getColumnBrush(color: Color): StiBrush;
        getColumnBorder(color: Color): Color;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiSimpleBorder = Stimulsoft.Base.Drawing.StiSimpleBorder;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiStyleCoreXF22 extends StiStyleCoreXF {
        get localizedName(): string;
        get chartBrush(): StiBrush;
        get chartAreaBrush(): StiBrush;
        get chartAreaBorderColor(): Color;
        get seriesLabelsBrush(): StiBrush;
        get seriesLabelsColor(): Color;
        get seriesLabelsBorderColor(): Color;
        get legendBrush(): StiBrush;
        get legendLabelsColor(): Color;
        get axisTitleColor(): Color;
        get axisLineColor(): Color;
        get axisLabelsColor(): Color;
        protected _styleColor: Color[];
        get styleColors(): Color[];
        get basicStyleColor(): Color;
        get toolTipBrush(): StiBrush;
        get toolTipTextBrush(): StiBrush;
        get toolTipBorder(): StiSimpleBorder;
        get styleId(): StiChartStyleId;
        getColumnBrush(color: Color): StiBrush;
        getColumnBorder(color: Color): Color;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Color = Stimulsoft.System.Drawing.Color;
    class StiStyleCoreXF23 extends StiStyleCoreXF22 {
        get localizedName(): string;
        protected _styleColor: Color[];
        get styleColors(): Color[];
        get styleId(): StiChartStyleId;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Color = Stimulsoft.System.Drawing.Color;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    class StiStyleCoreXF24 extends StiStyleCoreXF22 {
        get localizedName(): string;
        protected _styleColor: Color[];
        get styleColors(): Color[];
        get styleId(): StiChartStyleId;
        get chartAreaBorderColor(): Color;
        get legendBorderColor(): Color;
        get seriesLabelsBorderColor(): Color;
        get seriesLabelsBrush(): StiBrush;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Color = Stimulsoft.System.Drawing.Color;
    import Font = Stimulsoft.System.Drawing.Font;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    class StiStyleCoreXF25 extends StiStyleCoreXF22 {
        get localizedName(): string;
        protected _styleColor: Color[];
        get styleColors(): Color[];
        get styleId(): StiChartStyleId;
        get chartAreaBorderColor(): Color;
        get legendShowShadow(): boolean;
        get legendBorderColor(): Color;
        get seriesLabelsBorderColor(): Color;
        get seriesLabelsBrush(): StiBrush;
        get seriesLabelsColor(): Color;
        get seriesLabelsFont(): Font;
        get seriesLighting(): boolean;
        get seriesShowShadow(): boolean;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Font = Stimulsoft.System.Drawing.Font;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    class StiStyleCoreXF26 extends StiStyleCoreXF22 {
        get localizedName(): string;
        protected _styleColor: Color[];
        get styleColors(): Color[];
        get chartAreaBorderColor(): Color;
        get chartAreaBrush(): StiBrush;
        get legendShowShadow(): boolean;
        get legendBorderColor(): Color;
        get seriesLabelsColor(): Color;
        get seriesLabelsBorderColor(): Color;
        get seriesLabelsBrush(): StiBrush;
        get seriesLabelsFont(): Font;
        get seriesLighting(): boolean;
        get seriesShowShadow(): boolean;
        get markerVisible(): boolean;
        set markerVisible(value: boolean);
        get styleId(): StiChartStyleId;
        getColumnBorder(color: Color): Color;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiSimpleBorder = Stimulsoft.Base.Drawing.StiSimpleBorder;
    import Font = Stimulsoft.System.Drawing.Font;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    class StiStyleCoreXF27 extends StiStyleCoreXF22 {
        get localizedName(): string;
        protected _styleColor: Color[];
        get styleColors(): Color[];
        get chartBrush(): StiBrush;
        get chartAreaBrush(): StiBrush;
        get chartAreaBorderColor(): Color;
        get seriesLabelsBorderColor(): Color;
        get seriesLabelsBrush(): StiBrush;
        get seriesLabelsColor(): Color;
        get seriesLabelsFont(): Font;
        get legendBrush(): StiBrush;
        get legendLabelsColor(): Color;
        get legendBorderColor(): Color;
        get legendTitleColor(): Color;
        get legendShowShadow(): boolean;
        get legendFont(): Font;
        get seriesLighting(): boolean;
        getColumnBorder(color: Color): Color;
        get toolTipBrush(): StiBrush;
        get toolTipTextBrush(): StiBrush;
        get toolTipBorder(): StiSimpleBorder;
        get styleId(): StiChartStyleId;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiSimpleBorder = Stimulsoft.Base.Drawing.StiSimpleBorder;
    import Font = Stimulsoft.System.Drawing.Font;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    class StiStyleCoreXF28 extends StiStyleCoreXF26 {
        get localizedName(): string;
        protected _styleColor: Color[];
        get chartBrush(): StiBrush;
        get chartAreaBrush(): StiBrush;
        get chartAreaBorderColor(): Color;
        get axisTitleColor(): Color;
        get axisLineColor(): Color;
        get axisLabelsColor(): Color;
        get legendBrush(): StiBrush;
        get seriesLabelsBorderColor(): Color;
        get seriesLabelsBrush(): StiBrush;
        get legendBorderColor(): Color;
        get legendTitleColor(): Color;
        get legendShowShadow(): boolean;
        get legendFont(): Font;
        get toolTipBrush(): StiBrush;
        get toolTipTextBrush(): StiBrush;
        get toolTipBorder(): StiSimpleBorder;
        get styleId(): StiChartStyleId;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Color = Stimulsoft.System.Drawing.Color;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    class StiStyleCoreXF29 extends StiStyleCoreXF26 {
        get localizedName(): string;
        _styleColor: Color[];
        get styleColors(): Color[];
        get styleId(): StiChartStyleId;
        get chartAreaBorderColor(): Color;
        get legendShowShadow(): boolean;
        get legendBorderColor(): Color;
        get seriesLabelsColor(): Color;
        get seriesLabelsBorderColor(): Color;
        get seriesLabelsBrush(): StiBrush;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Color = Stimulsoft.System.Drawing.Color;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Font = Stimulsoft.System.Drawing.Font;
    class StiStyleCoreXF30 extends StiStyleCoreXF22 {
        get localizedName(): string;
        _styleColor: Color[];
        get styleColors(): Color[];
        get chartBrush(): StiBrush;
        get chartAreaBrush(): StiBrush;
        get chartAreaBorderColor(): Color;
        get seriesLabelsBorderColor(): Color;
        get seriesLabelsBrush(): StiBrush;
        get seriesLabelsColor(): Color;
        get seriesLabelsFont(): Font;
        get legendBrush(): StiBrush;
        get legendLabelsColor(): Color;
        get legendTitleColor(): Color;
        get legendShowShadow(): boolean;
        get legendBorderColor(): Color;
        get legendFont(): Font;
        get seriesLighting(): boolean;
        getColumnBorder(color: Color): Color;
        get styleId(): StiChartStyleId;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiSimpleBorder = Stimulsoft.Base.Drawing.StiSimpleBorder;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Font = Stimulsoft.System.Drawing.Font;
    class StiStyleCoreXF31 extends StiStyleCoreXF22 {
        get localizedName(): string;
        _styleColor: Color[];
        get styleColors(): Color[];
        get chartBrush(): StiBrush;
        get chartAreaBrush(): StiBrush;
        get chartAreaBorderColor(): Color;
        get seriesLabelsBorderColor(): Color;
        get seriesLabelsBrush(): StiBrush;
        get seriesLabelsColor(): Color;
        get seriesLabelsFont(): Font;
        get legendBrush(): StiBrush;
        get legendLabelsColor(): Color;
        get legendTitleColor(): Color;
        get legendShowShadow(): boolean;
        get legendBorderColor(): Color;
        get legendFont(): Font;
        get toolTipBrush(): StiBrush;
        get toolTipTextBrush(): StiBrush;
        get toolTipBorder(): StiSimpleBorder;
        get seriesLighting(): boolean;
        getColumnBorder(color: Color): Color;
        get styleId(): StiChartStyleId;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Color = Stimulsoft.System.Drawing.Color;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Font = Stimulsoft.System.Drawing.Font;
    class StiStyleCoreXF32 extends StiStyleCoreXF22 {
        get localizedName(): string;
        _styleColor: Color[];
        get styleColors(): Color[];
        get basicStyleColor(): Color;
        get chartBrush(): StiBrush;
        get chartAreaBrush(): StiBrush;
        get legendBrush(): StiBrush;
        get legendLabelsColor(): Color;
        get legendBorderColor(): Color;
        get legendTitleColor(): Color;
        get legendShowShadow(): boolean;
        get legendFont(): Font;
        get seriesLabelsBorderColor(): Color;
        get seriesLabelsBrush(): StiBrush;
        get seriesLabelsColor(): Color;
        get seriesLabelsLineColor(): Color;
        get seriesLabelsFont(): Font;
        get axisTitleColor(): Color;
        get axisLineColor(): Color;
        get axisLabelsColor(): Color;
        get gridLinesHorColor(): Color;
        get gridLinesVertColor(): Color;
        get toolTipBrush(): StiBrush;
        get seriesLighting(): boolean;
        get styleId(): StiChartStyleId;
        getColumnBorder(color: Color): Color;
        getColumnBrush(color: Color): StiBrush;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Color = Stimulsoft.System.Drawing.Color;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Font = Stimulsoft.System.Drawing.Font;
    class StiStyleCoreXF33 extends StiStyleCoreXF {
        get localizedName(): string;
        _styleColor: Color[];
        get styleColors(): Color[];
        get basicStyleColor(): Color;
        get chartBrush(): StiBrush;
        get chartAreaBrush(): StiBrush;
        get legendBrush(): StiBrush;
        get legendLabelsColor(): Color;
        get legendBorderColor(): Color;
        get legendTitleColor(): Color;
        get legendShowShadow(): boolean;
        get legendFont(): Font;
        get seriesLabelsBorderColor(): Color;
        get seriesLabelsBrush(): StiBrush;
        get seriesLabelsColor(): Color;
        get seriesLabelsLineColor(): Color;
        get seriesLabelsFont(): Font;
        get axisTitleColor(): Color;
        get axisLineColor(): Color;
        get axisLabelsColor(): Color;
        get gridLinesHorColor(): Color;
        get gridLinesVertColor(): Color;
        get seriesLighting(): boolean;
        get styleId(): StiChartStyleId;
        getColumnBorder(color: Color): Color;
        getColumnBrush(color: Color): StiBrush;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiSimpleBorder = Stimulsoft.Base.Drawing.StiSimpleBorder;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Font = Stimulsoft.System.Drawing.Font;
    class StiStyleCoreXF34 extends StiStyleCoreXF22 {
        get localizedName(): string;
        _styleColor: Color[];
        get styleColors(): Color[];
        get chartBrush(): StiBrush;
        get chartAreaBrush(): StiBrush;
        get chartAreaBorderColor(): Color;
        get legendBrush(): StiBrush;
        get legendLabelsColor(): Color;
        get legendBorderColor(): Color;
        get legendTitleColor(): Color;
        get legendShowShadow(): boolean;
        get legendFont(): Font;
        get toolTipBrush(): StiBrush;
        get toolTipTextBrush(): StiBrush;
        get toolTipBorder(): StiSimpleBorder;
        get seriesLabelsBorderColor(): Color;
        get seriesLabelsBrush(): StiBrush;
        get seriesLabelsColor(): Color;
        get seriesLabelsFont(): Font;
        get seriesLighting(): boolean;
        get styleId(): StiChartStyleId;
        getColumnBorder(color: Color): Color;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiSimpleBorder = Stimulsoft.Base.Drawing.StiSimpleBorder;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Font = Stimulsoft.System.Drawing.Font;
    class StiStyleCoreXF35 extends StiStyleCoreXF22 {
        get localizedName(): string;
        _styleColor: Color[];
        get styleColors(): Color[];
        get chartBrush(): StiBrush;
        get chartAreaBrush(): StiBrush;
        get chartAreaBorderColor(): Color;
        get legendBrush(): StiBrush;
        get legendLabelsColor(): Color;
        get legendBorderColor(): Color;
        get legendTitleColor(): Color;
        get legendShowShadow(): boolean;
        get legendFont(): Font;
        get seriesLabelsBorderColor(): Color;
        get seriesLabelsBrush(): StiBrush;
        get seriesLabelsColor(): Color;
        get seriesLabelsFont(): Font;
        get toolTipBrush(): StiBrush;
        get toolTipTextBrush(): StiBrush;
        get toolTipBorder(): StiSimpleBorder;
        get seriesLighting(): boolean;
        get styleId(): StiChartStyleId;
        getColumnBorder(color: Color): Color;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiChartTableCoreXF implements ICloneable, IStiApplyStyle, IStiChartTableCoreXF {
        private static implementsStiChartTableCoreXF;
        implements(): any[];
        applyStyle(style: IStiChartStyle): void;
        clone(): StiChartTableCoreXF;
        private storedCulture;
        chartTable: IStiChartTable;
        showTable(): boolean;
        getHeightTable(context: StiContext, widthTable: number): number;
        getHeightHeaderTable(context: StiContext, widthTable: number): number;
        getWidthCellLegend(context: StiContext): number;
        render(context: StiContext, rect: RectangleD): StiCellGeom;
        private getMaxCountValues;
        private getArguments;
        getLabelText(objectValue: any, series: IStiSeries): string;
        private getTableValues;
        constructor(table: IStiChartTable);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import PointD = Stimulsoft.System.Drawing.Point;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiTrendLineCoreXF implements ICloneable, IStiTrendLineCoreXF {
        private static implementsStiTrendLineCoreXF;
        implements(): any[];
        clone(): StiTrendLineCoreXF;
        get localizedName(): string;
        trendLine: IStiTrendLine;
        renderTrendLine(geom: StiAreaGeom, points: PointD[], posY: number): void;
        sum(values: number[]): number;
        sumSqr(values: number[]): number;
        sumProductions(valuesX: number[], valuesY: number[]): number;
        sumProductionsXLogY(valuesX: number[], valuesY: number[]): number;
        sumLn(values: number[]): number;
        constructor(trendLine: IStiTrendLine);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import PointD = Stimulsoft.System.Drawing.Point;
    class StiTrendLineExponentialCoreXF extends StiTrendLineCoreXF {
        get localizedName(): string;
        renderTrendLine(geom: StiAreaGeom, points: PointD[], posY: number): void;
        constructor(trendLine: IStiTrendLine);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import PointD = Stimulsoft.System.Drawing.Point;
    class StiTrendLineLinearCoreXF extends StiTrendLineCoreXF {
        get localizedName(): string;
        renderTrendLine(geom: StiAreaGeom, points: PointD[], posY: number): void;
        constructor(trendLine: IStiTrendLine);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import PointD = Stimulsoft.System.Drawing.Point;
    class StiTrendLineLogarithmicCoreXF extends StiTrendLineCoreXF {
        get localizedName(): string;
        renderTrendLine(geom: StiAreaGeom, points: PointD[], posY: number): void;
        constructor(trendLine: IStiTrendLine);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiTrendLineNoneCoreXF extends StiTrendLineCoreXF {
        get localizedName(): string;
        constructor(trendLine: IStiTrendLine);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import StiGeom = Stimulsoft.Base.Context.StiGeom;
    import StiGeomType = Stimulsoft.Base.Context.StiGeomType;
    class StiCellGeom extends StiGeom implements IStiCellGeom {
        private static implementsStiCellGeom;
        implements(): any[];
        get invisible(): boolean;
        type: StiGeomType;
        childGeoms: StiCellGeom[];
        clientRectangle: RectangleD;
        dispose(): void;
        contains(x: number, y: number): boolean;
        getGeomAt(parent: StiCellGeom, x: number, y: number): StiCellGeom;
        getSeriesGeoms(): StiCellGeom[];
        getSeriesElementGeoms(): StiCellGeom[];
        getRect(geom: StiGeom): RectangleD;
        createChildGeoms(): void;
        draw(context: StiContext): void;
        drawGeom(context: StiContext): void;
        drawChildGeoms(context: StiContext): void;
        protected allowChildDrawing(cellGeom: StiCellGeom): boolean;
        constructor(clientRectangle: RectangleD);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiChartGeom extends StiCellGeom {
        draw(context: StiContext): void;
        constructor(clientRectangle: RectangleD);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiPenGeom = Stimulsoft.Base.Context.StiPenGeom;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import PointD = Stimulsoft.System.Drawing.Point;
    import StiAnimation = Stimulsoft.Base.Context.Animation.StiAnimation;
    class StiNullableDrawing {
        static drawLines(context: StiContext, penGeom: StiPenGeom, points: PointD[], animation?: StiAnimation): void;
        static drawLines2(context: StiContext, penGeom: StiPenGeom, pointsStart: PointD[], points: PointD[], animation?: StiAnimation): void;
        static drawCurve(context: StiContext, penGeom: StiPenGeom, points: PointD[], tension: number, animation?: StiAnimation): void;
        static getPointsList(points: PointD[]): PointD[][];
        static getNullablePointsList(points: PointD[]): PointD[][];
        static getPointsList2(points1: PointD[], points2: PointD[], REFlist1: any, REFlist2: any): void;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiAreaGeom extends StiCellGeom {
        area: IStiArea;
        draw(context: StiContext): void;
        constructor(area: IStiArea, clientRectangle: RectangleD);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiPenGeom = Stimulsoft.Base.Context.StiPenGeom;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import List = Stimulsoft.System.Collections.List;
    class StiLineF {
        x1: number;
        y1: number;
        x2: number;
        y2: number;
        constructor(x1: number, y1: number, x2: number, y2: number);
    }
    class StiAxisAreaGeom extends StiAreaGeom {
        view: StiAxisAreaViewGeom;
        private minWidth;
        private drawInterlacingHor;
        private drawInterlacingVer;
        private getGridLinesHorMajor;
        private getGridLinesHorMinor;
        private getGridLinesVerMajor;
        private getGridLinesVerMinor;
        protected allowChildDrawing(cellGeom: StiCellGeom): boolean;
        isChildVisibleInView(cellGeom: StiCellGeom): boolean;
        draw(context: StiContext): void;
        drawLines(context: StiContext, lines: List<StiLineF>, pen: StiPenGeom): void;
        constructor(view: StiAxisAreaViewGeom, area: IStiArea, clientRectangle: RectangleD);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiAxisAreaViewGeom extends StiAreaGeom {
        draw(context: StiContext): void;
        drawGeom(context: StiContext): void;
        drawChildGeoms(context: StiContext): void;
        private drawBorder;
        constructor(area: IStiArea, clientRectangle: RectangleD);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiPieAreaGeom extends StiAreaGeom {
        draw(context: StiContext): void;
        constructor(area: IStiArea, clientRectangle: RectangleD);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiDoughnutAreaGeom extends StiPieAreaGeom {
        constructor(area: IStiArea, clientRectangle: RectangleD);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Font = Stimulsoft.System.Drawing.Font;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    class StiDoughnutAreaIndicatorGeom extends StiCellGeom {
        indicator: StiDoughnutAreaIndicator;
        valueText: string;
        titleText: string;
        draw(context: StiContext): void;
        drawText(context: StiContext, text: string, color: Color, rect: Rectangle, font: Font): void;
        static getFontSize(context: StiContext, rect: Rectangle, text: string, font: Font): number;
        constructor(clientRectangle: Rectangle, valueText: string, titleText: string, indicator: StiDoughnutAreaIndicator);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiPictorialAreaGeom extends StiAreaGeom {
        draw(context: StiContext): void;
        drawGeom(context: StiContext): void;
        private drawBorder;
        constructor(area: IStiArea, clientRectangle: RectangleD);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiRadarAreaGeom extends StiAreaGeom {
        valuesCount: number;
        private drawHor;
        private drawVert;
        private drawBackground;
        draw(context: StiContext): void;
        constructor(area: IStiArea, clientRectangle: RectangleD, valuesCount: number);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiSunburstAreaGeom extends StiPieAreaGeom {
        constructor(area: IStiArea, clientRectangle: RectangleD);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiTreemapAreaGeom extends StiAreaGeom {
        draw(context: StiContext): void;
        constructor(area: IStiArea, clientRectangle: RectangleD);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiRotationMode = Stimulsoft.Base.Drawing.StiRotationMode;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import PointD = Stimulsoft.System.Drawing.Point;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiAxisLabelGeom extends StiCellGeom {
        rotationMode: StiRotationMode;
        textPoint: PointD;
        angle: number;
        axis: IStiAxis;
        text: string;
        stripLine: StiStripLineXF;
        draw(context: StiContext): void;
        constructor(axis: IStiAxis, clientRectangle: RectangleD, textPoint: PointD, text: string, stripLine: StiStripLineXF, angle: number, rotationMode: StiRotationMode);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StringAlignment = Stimulsoft.System.Drawing.StringAlignment;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiFontGeom = Stimulsoft.Base.Context.StiFontGeom;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiAxisTitleGeom extends StiCellGeom {
        axis: IStiAxis;
        angle: number;
        font: StiFontGeom;
        draw(context: StiContext): void;
        constructor(axis: IStiAxis, clientRectangle: RectangleD, angle: number, stringAlignment: StringAlignment, font: StiFontGeom);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiXAxisGeom extends StiCellGeom {
        axis: IStiXAxis;
        isCenterAxis: boolean;
        view: StiXAxisViewGeom;
        drawArrow(context: StiContext, rect: RectangleD): void;
        private drawAxisLine;
        private drawMinorTicks;
        private drawTicks;
        private isArgumentDateTime;
        private drawAxis;
        private getViewclipRect;
        allowChildDrawing(cellGeom: StiCellGeom): boolean;
        draw(context: StiContext): void;
        constructor(axis: IStiXAxis, clientRectangle: RectangleD, isCenterAxis: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiXAxisViewGeom extends StiXAxisGeom {
        drawChildGeoms(context: StiContext): void;
        draw(context: StiContext): void;
        constructor(axis: IStiXAxis, clientRectangle: RectangleD, isCenterAxis: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiYAxisGeom extends StiCellGeom {
        axis: IStiYAxis;
        isCenterAxis: boolean;
        view: StiYAxisViewGeom;
        drawArrow(context: StiContext, rect: RectangleD): void;
        private drawAxisLine;
        private drawMinorTicks;
        private drawTicks;
        private drawAxis;
        private getViewclipRect;
        allowChildDrawing(cellGeom: StiCellGeom): boolean;
        draw(context: StiContext): void;
        constructor(axis: IStiYAxis, clientRectangle: RectangleD, isCenterAxis: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiYAxisViewGeom extends StiYAxisGeom {
        drawChildGeoms(context: StiContext): void;
        draw(context: StiContext): void;
        constructor(axis: IStiYAxis, clientRectangle: RectangleD, isCenterAxis: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiDownButtonGeom extends StiCellGeom {
        private axis;
        draw(context: StiContext): void;
        constructor(axis: IStiYAxis, clientRectangle: RectangleD);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiHorzScrollBarGeom extends StiCellGeom {
        draw(context: StiContext): void;
        axis: IStiXAxis;
        constructor(axis: IStiXAxis, clientRectangle: RectangleD);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiHorzTrackBarGeom extends StiCellGeom {
        draw(context: StiContext): void;
        axis: IStiXAxis;
        scrollBar: StiHorzScrollBarGeom;
        constructor(axis: IStiXAxis, clientRectangle: RectangleD, scrollBar: StiHorzScrollBarGeom);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiLeftButtonGeom extends StiCellGeom {
        axis: IStiXAxis;
        draw(context: StiContext): void;
        constructor(axis: IStiXAxis, clientRectangle: RectangleD);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiRightButtonGeom extends StiCellGeom {
        axis: IStiXAxis;
        draw(context: StiContext): void;
        constructor(axis: IStiXAxis, clientRectangle: RectangleD);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiUpButtonGeom extends StiCellGeom {
        axis: IStiYAxis;
        draw(context: StiContext): void;
        constructor(axis: IStiYAxis, clientRectangle: RectangleD);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiVertScrollBarGeom extends StiCellGeom {
        draw(context: StiContext): void;
        private axis;
        constructor(axis: IStiYAxis, clientRectangle: RectangleD);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiVertTrackBarGeom extends StiCellGeom {
        draw(context: StiContext): void;
        axis: IStiYAxis;
        scrollBar: StiVertScrollBarGeom;
        constructor(axis: IStiYAxis, clientRectangle: RectangleD, scrollBar: StiVertScrollBarGeom);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiChartTitleGeom extends StiCellGeom {
        title: IStiChartTitle;
        draw(context: StiContext): void;
        constructor(title: IStiChartTitle, clientRectangle: RectangleD);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiRotationMode = Stimulsoft.Base.Drawing.StiRotationMode;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import PointD = Stimulsoft.System.Drawing.Point;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiConstantLinesVerticalGeom extends StiCellGeom {
        line: IStiConstantLines;
        point: PointD;
        mode: StiRotationMode;
        draw(context: StiContext): void;
        constructor(line: IStiConstantLines, clientRectangle: RectangleD, point: PointD, mode: StiRotationMode);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiRotationMode = Stimulsoft.Base.Drawing.StiRotationMode;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import PointD = Stimulsoft.System.Drawing.Point;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiConstantLinesYGeom extends StiCellGeom {
        line: IStiConstantLines;
        point: PointD;
        mode: StiRotationMode;
        draw(context: StiContext): void;
        constructor(line: IStiConstantLines, clientRectangle: RectangleD, point: PointD, mode: StiRotationMode);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiLegendGeom extends StiCellGeom {
        legend: IStiLegend;
        seriesItems: StiLegendItemCoreXF[];
        legendTitleGeom: StiLegendTitleGeom;
        dispose(): void;
        draw(context: StiContext): void;
        constructor(legend: IStiLegend, clientRectangle: RectangleD, seriesItems: StiLegendItemCoreXF[]);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiLegendItemGeom extends StiCellGeom {
        legend: IStiLegend;
        item: StiLegendItemCoreXF;
        colorIndex: number;
        legendItemsCount: number;
        private legendItemIndex;
        draw(context: StiContext): void;
        constructor(legend: IStiLegend, item: StiLegendItemCoreXF, clientRectangle: RectangleD, colorIndex: number, legendItemsCount: number, legendItemIndex: number);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiLegendTitleGeom extends StiCellGeom {
        legend: IStiLegend;
        draw(context: StiContext): void;
        constructor(legend: IStiLegend, clientRectangle: RectangleD);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiLegendAreaMarker implements IStiLegendMarker {
        private static implementsStiLegendAreaMarker;
        implements(): any[];
        draw(context: StiContext, serie: IStiSeries, rect: RectangleD, colorIndex: number, colorCount: number, index: number): void;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiLegendCandelstickMarker implements IStiLegendMarker {
        private static implementsStiLegendCandelstickMarker;
        implements(): any[];
        draw(context: StiContext, serie: IStiSeries, rect: RectangleD, colorIndex: number, colorCount: number, index: number): void;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiLegendColumnMarker implements IStiLegendMarker {
        private static implementsStiLegendColumnMarker;
        implements(): any[];
        draw(context: StiContext, series: IStiSeries, rect: RectangleD, colorIndex: number, colorCount: number, index: number): void;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiLegendDoughnutMarker implements IStiLegendMarker {
        private static implementsStiLegendDoughnutMarker;
        implements(): any[];
        draw(context: StiContext, serie: IStiSeries, rect: RectangleD, colorIndex: number, colorCount: number, index: number): void;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    class StiLegendFontIconMarker implements IStiLegendMarker {
        draw(context: StiContext, series: IStiSeries, rect: Rectangle, colorIndex: number, colorCount: number, index: number): void;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiLegendFunnelMarker implements IStiLegendMarker {
        private static implementsStiLegendFunnelMarker;
        implements(): any[];
        draw(context: StiContext, serie: IStiSeries, rect: RectangleD, colorIndex: number, colorCount: number, index: number): void;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiLegendLineMarker implements IStiLegendMarker {
        private static implementsStiLegendLineMarker;
        implements(): any[];
        draw(context: StiContext, serie: IStiSeries, rect: RectangleD, colorIndex: number, colorCount: number, index: number): void;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import StiSegmentGeom = Stimulsoft.Base.Context.StiSegmentGeom;
    import PointD = Stimulsoft.System.Drawing.Point;
    class StiLegendMarkerHelper {
        static getSteppedMarkerPath(rect: RectangleD): StiSegmentGeom[];
        static getAreaMarkerPath(rect: RectangleD): StiSegmentGeom[];
        static getAreaMarkerLinePoints(rect: RectangleD): PointD[];
        static getSplineAreaMarkerPath(rect: RectangleD): StiSegmentGeom[];
        static getSplineAreaMarkerLinePoints(rect: RectangleD): PointD[];
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiLegendPictorialMarker implements IStiLegendMarker {
        private static implementsStiLegendPictorialMarker;
        implements(): any[];
        draw(context: StiContext, series: IStiSeries, rect: RectangleD, colorIndex: number, colorCount: number, index: number): void;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiLegendPieMarker implements IStiLegendMarker {
        private static implementsStiLegendPieMarker;
        implements(): any[];
        draw(context: StiContext, serie: IStiSeries, rect: RectangleD, colorIndex: number, colorCount: number, index: number): void;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiLegendRangeMarker implements IStiLegendMarker {
        private static implementsStiLegendRangeMarker;
        implements(): any[];
        draw(context: StiContext, serie: IStiSeries, rect: RectangleD, colorIndex: number, colorCount: number, index: number): void;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiLegendSplineAreaMarker implements IStiLegendMarker {
        private static implementsStiLegendSplineAreaMarker;
        implements(): any[];
        draw(context: StiContext, serie: IStiSeries, rect: RectangleD, colorIndex: number, colorCount: number, index: number): void;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiLegendSplineRangeMarker implements IStiLegendMarker {
        private static implementsStiLegendSplineRangeMarker;
        implements(): any[];
        draw(context: StiContext, serie: IStiSeries, rect: RectangleD, colorIndex: number, colorCount: number, index: number): void;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiLegendStackedAreaMarker implements IStiLegendMarker {
        private static implementsStiLegendStackedAreaMarker;
        implements(): any[];
        draw(context: StiContext, serie: IStiSeries, rect: RectangleD, colorIndex: number, colorCount: number, index: number): void;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiLegendStackedSplineAreaMarker implements IStiLegendMarker {
        private static implementsStiLegendStackedSplineAreaMarker;
        implements(): any[];
        draw(context: StiContext, serie: IStiSeries, rect: RectangleD, colorIndex: number, colorCount: number, index: number): void;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiLegendSteppedAreaMarker implements IStiLegendMarker {
        private static implementsStiLegendSteppedAreaMarker;
        implements(): any[];
        draw(context: StiContext, serie: IStiSeries, rect: RectangleD, colorIndex: number, colorCount: number, index: number): void;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiLegendSteppedRangeMarker implements IStiLegendMarker {
        private static implementsStiSteppedRangeSeries;
        implements(): any[];
        draw(context: StiContext, serie: IStiSeries, rect: RectangleD, colorIndex: number, colorCount: number, index: number): void;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiLegendStockMarker implements IStiLegendMarker {
        private static implementsStiLegendStockMarker;
        implements(): any[];
        draw(context: StiContext, serie: IStiSeries, rect: RectangleD, colorIndex: number, colorCount: number, index: number): void;
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiMarkerLegendFactory {
        static createMarker(series: IStiSeries): IStiLegendMarker;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import PointD = Stimulsoft.System.Drawing.Point;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import IStiSeriesElement = Stimulsoft.Report.Chart.IStiSeriesElement;
    import StiSeriesInteractionData = Stimulsoft.Base.Context.StiSeriesInteractionData;
    class StiMarkerGeom extends StiCellGeom implements IStiSeriesElement {
        private static implementsStiMarkerGeom;
        implements(): any[];
        private getValueIndex;
        getHyperlink(): string;
        private getHyperlink2;
        getToolTip(): string;
        private getToolTip2;
        interaction: StiSeriesInteractionData;
        index: number;
        point: PointD;
        marker: IStiMarker;
        value: number;
        showShadow: boolean;
        series: IStiSeries;
        elementIndex: string;
        isTooltipMode: boolean;
        contains(x: number, y: number): boolean;
        getMouseOverRect(): RectangleD;
        draw(context: StiContext): void;
        constructor(series: IStiSeries, index: number, value: number, point: PointD, marker: IStiMarker, showShadow: boolean, zoom: number, isTooltipMode: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiRadarAxisGeom extends StiCellGeom {
        axis: IStiYRadarAxis;
        private drawAxisLine;
        private drawMinorTicks;
        private drawTicks;
        private drawAxis;
        draw(context: StiContext): void;
        constructor(axis: IStiYRadarAxis, clientRectangle: RectangleD);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import PointD = Stimulsoft.System.Drawing.Point;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiXRadarAxisLabelGeom extends StiCellGeom {
        borderColor: Color;
        labelBrush: StiBrush;
        text: string;
        angle: number;
        point: PointD;
        labelRect: RectangleD;
        axis: IStiXRadarAxis;
        draw(context: StiContext): void;
        constructor(axis: IStiXRadarAxis, text: string, labelBrush: StiBrush, borderColor: Color, angle: number, clientRectangle: RectangleD, labelRect: RectangleD, point: PointD);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiRotationMode = Stimulsoft.Base.Drawing.StiRotationMode;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import PointD = Stimulsoft.System.Drawing.Point;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiYRadarAxisLabelGeom extends StiCellGeom {
        rotationMode: StiRotationMode;
        textPoint: PointD;
        angle: number;
        axis: IStiYRadarAxis;
        text: string;
        stripLine: StiStripLineXF;
        draw(context: StiContext): void;
        constructor(axis: IStiYRadarAxis, clientRectangle: RectangleD, textPoint: PointD, text: string, stripLine: StiStripLineXF, angle: number, rotationMode: StiRotationMode);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Color = Stimulsoft.System.Drawing.Color;
    class StiMouseOverHelper {
        static getMouseOverColor(): Color;
        static getLineMouseOverColor(): Color;
        static mouseOverLineDistance: number;
        static mouseOverSplineDistance: number;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiCornerRadius = Stimulsoft.Base.Drawing.StiCornerRadius;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import StiInteractionDataGeom = Stimulsoft.Base.Context.StiInteractionDataGeom;
    import IStiSeriesElement = Stimulsoft.Report.Chart.IStiSeriesElement;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import StiSeriesInteractionData = Stimulsoft.Base.Context.StiSeriesInteractionData;
    class StiSeriesElementGeom extends StiCellGeom implements IStiSeriesElement {
        private static implementsStiSeriesElementGeom;
        implements(): any[];
        protected getValueIndex(): number;
        getHyperlink(): string;
        private getHyperlink2;
        getToolTip(): string;
        private getToolTip2;
        seriesBrush: StiBrush;
        value: number;
        index: number;
        series: IStiSeries;
        areaGeom: StiAreaGeom;
        elementIndex: string;
        _interaction: StiSeriesInteractionData;
        get interaction(): StiSeriesInteractionData;
        set interaction(value: StiSeriesInteractionData);
        draw(context: StiContext): void;
        getInteractionData(): StiInteractionDataGeom;
        getSeriesBorderThickness(zoom: number): number;
        protected getCornerRadius(): StiCornerRadius;
        constructor(areaGeom: StiAreaGeom, value: number, index: number, series: IStiSeries, clientRectangle: RectangleD, brush: StiBrush);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import StiSeriesInteractionData = Stimulsoft.Base.Context.StiSeriesInteractionData;
    class StiSeriesGeom extends StiCellGeom {
        series: IStiSeries;
        interactions: StiSeriesInteractionData[];
        areaGeom: StiAreaGeom;
        draw(context: StiContext): void;
        constructor(areaGeom: StiAreaGeom, series: IStiSeries, clientRectangle: RectangleD);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import TimeSpan = Stimulsoft.System.TimeSpan;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiBoxAndWhiskerSeriesElementGeom extends StiCellGeom {
        brush: StiBrush;
        borderColor: Color;
        beginTime: TimeSpan;
        areaGeom: StiAreaGeom;
        mean: number;
        maximum: number;
        minimum: number;
        median: number;
        firstQuartile: number;
        thirdQuartile: number;
        positionX: number;
        values: number[];
        series: IStiSeries;
        draw(context: StiContext): void;
        getSeriesBorderThickness(zoom: number): number;
        constructor(areaGeom: StiAreaGeom, series: IStiSeries, positionX: number, minimum: number, maximim: number, firstQuartile: number, thirdQuartile: number, median: number, values: number[], mean: number, clientRectangle: Rectangle, brush: StiBrush, borderColor: Color, beginTime: TimeSpan);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import TimeSpan = Stimulsoft.System.TimeSpan;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiBubbleSeriesElementGeom extends StiSeriesElementGeom {
        seriesBrush: StiBrush;
        seriesBorderColor: Color;
        beginTime: TimeSpan;
        contains(x: number, y: number): boolean;
        draw(context: StiContext): void;
        constructor(areaGeom: StiAreaGeom, value: number, index: number, seriesBrush: StiBrush, seriesBorderColor: Color, series: IStiSeries, clientRectangle: RectangleD, beginTime: TimeSpan);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import StiAnimation = Stimulsoft.Base.Context.Animation.StiAnimation;
    class StiClusteredBarSeriesElementGeom extends StiSeriesElementGeom {
        seriesBorderColor: Color;
        valueStart: number;
        columnRectStart: RectangleD;
        draw(context: StiContext): void;
        private getBorderPath;
        protected getAnimation(): StiAnimation;
        constructor(areaGeom: StiAreaGeom, valueStart: number, value: number, index: number, seriesBrush: StiBrush, seriesBorderColor: Color, series: IStiSeries, columnRectStart: RectangleD, columnRect: RectangleD);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import PointD = Stimulsoft.System.Drawing.Point;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import StiAnimation = Stimulsoft.Base.Context.Animation.StiAnimation;
    class StiBaseLineSeriesGeom extends StiSeriesGeom {
        additionalSeriesId: string;
        points: PointD[];
        pointsFrom: PointD[];
        pointsIds: string[];
        get allowMouseOver(): boolean;
        static getClientRectangle(points: PointD[], lineWidth: number): RectangleD;
        draw(context: StiContext): void;
        protected getAnimation(points?: PointD[]): StiAnimation;
        protected getAnimationConnect(points?: PointD[]): StiAnimation;
        protected getAnimation2(pointsFrom: PointD[], points: PointD[], pointsIds: string[]): StiAnimation;
        constructor(areaGeom: StiAreaGeom, pointsInfo: StiSeriesPointsInfo, series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import PointD = Stimulsoft.System.Drawing.Point;
    class StiLineSeriesGeom extends StiBaseLineSeriesGeom {
        pointsZeroConnect: PointD[];
        pointsNullConnect: PointD[];
        contains(x: number, y: number): boolean;
        draw(context: StiContext): void;
        private getPointCross;
        constructor(areaGeom: StiAreaGeom, pointsInfo: StiSeriesPointsInfo, series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    class StiAreaSeriesGeom extends StiLineSeriesGeom {
        contains(x: number, y: number): boolean;
        draw(context: StiContext): void;
        constructor(areaGeom: StiAreaGeom, pointsInfo: StiSeriesPointsInfo, series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import StiAnimation = Stimulsoft.Base.Context.Animation.StiAnimation;
    class StiClusteredColumnSeriesElementGeom extends StiSeriesElementGeom {
        seriesBorderColor: Color;
        columnRectStart: RectangleD;
        draw(context: StiContext): void;
        private getBorderPath;
        protected getAnimation(): StiAnimation;
        constructor(areaGeom: StiAreaGeom, value: number, index: number, seriesBrush: StiBrush, seriesBorderColor: Color, series: IStiSeries, columnRect: RectangleD, columnRectStart: RectangleD);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import PointD = Stimulsoft.System.Drawing.Point;
    class StiSplineSeriesGeom extends StiBaseLineSeriesGeom {
        pointsZeroConnect: PointD[];
        pointsNullConnect: PointD[];
        contains(x: number, y: number): boolean;
        draw(context: StiContext): void;
        constructor(areaGeom: StiAreaGeom, pointsInfo: StiSeriesPointsInfo, series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    class StiSplineAreaSeriesGeom extends StiSplineSeriesGeom {
        contains(x: number, y: number): boolean;
        draw(context: StiContext): void;
        constructor(areaGeom: StiAreaGeom, pointsInfo: StiSeriesPointsInfo, series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import PointD = Stimulsoft.System.Drawing.Point;
    class StiSteppedLineSeriesGeom extends StiBaseLineSeriesGeom {
        getSteppedPoints(points: PointD[]): PointD[];
        protected getSteppedPointsIds(ids: string[]): string[];
        contains(x: number, y: number): boolean;
        draw(context: StiContext): void;
        private intersectionAxis;
        constructor(areaGeom: StiAreaGeom, pointsInfo: StiSeriesPointsInfo, series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    class StiSteppedAreaSeriesGeom extends StiSteppedLineSeriesGeom {
        draw(context: StiContext): void;
        constructor(areaGeom: StiAreaGeom, pointsInfo: StiSeriesPointsInfo, series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import PointF = Stimulsoft.System.Drawing.Point;
    import StiPenGeom = Stimulsoft.Base.Context.StiPenGeom;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    class StiWaterfallLineGeom extends StiCellGeom {
        pen: StiPenGeom;
        pointStart: PointF;
        pointEnd: PointF;
        animation: boolean;
        draw(context: StiContext): void;
        constructor(pointStart: PointF, pointEnd: PointF, pen: StiPenGeom, clientRectangle: Rectangle, animation: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    class StiDoughnutEmptySeriesElementGeom extends StiCellGeom {
        draw(context: StiContext): void;
        constructor(clientRectangle: Rectangle);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiSegmentGeom = Stimulsoft.Base.Context.StiSegmentGeom;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import TimeSpan = Stimulsoft.System.TimeSpan;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiDoughnutSeriesElementGeom extends StiSeriesElementGeom {
        clientRectangleDt: RectangleD;
        path: StiSegmentGeom[];
        pathLight: StiSegmentGeom[];
        pathDark: StiSegmentGeom[];
        borderColor: Color;
        brush: StiBrush;
        brushLight: StiBrush;
        brushDark: StiBrush;
        startAngle: number;
        endAngle: number;
        radiusFrom: number;
        radiusTo: number;
        beginTime: TimeSpan;
        contains(x: number, y: number): boolean;
        draw(context: StiContext): void;
        private getAnimation;
        constructor(areaGeom: StiAreaGeom, value: number, index: number, series: IStiDoughnutSeries, clientRectangle: RectangleD, clientRectangleDt: RectangleD, path: StiSegmentGeom[], pathLight: StiSegmentGeom[], pathDark: StiSegmentGeom[], borderColor: Color, brush: StiBrush, brushLight: StiBrush, brushDark: StiBrush, startAngle: number, endAngle: number, radiusFrom: number, radiusTo: number, beginTime: TimeSpan);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import StiSeriesInteractionData = Stimulsoft.Base.Context.StiSeriesInteractionData;
    class StiFinancialSeriesElementGeom extends StiCellGeom {
        series: IStiSeries;
        interaction: StiSeriesInteractionData;
        open: number;
        close: number;
        high: number;
        low: number;
        positionX: number;
        areaGeom: StiAreaGeom;
        index: number;
        draw(context: StiContext): void;
        constructor(areaGeom: StiAreaGeom, series: IStiSeries, clientRectangle: RectangleD, open: number, close: number, high: number, low: number, positionX: number, index: number);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import TimeSpan = Stimulsoft.System.TimeSpan;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiCandlestickSeriesElementGeom extends StiFinancialSeriesElementGeom {
        brush: StiBrush;
        borderColor: Color;
        beginTime: TimeSpan;
        draw(context: StiContext): void;
        constructor(areaGeom: StiAreaGeom, series: IStiSeries, clientRectangle: RectangleD, bodyStart: number, bodyEnd: number, high: number, low: number, positionX: number, index: number, brush: StiBrush, borderColor: Color, beginTime: TimeSpan);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import TimeSpan = Stimulsoft.System.TimeSpan;
    import Color = Stimulsoft.System.Drawing.Color;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiStockSeriesElementGeom extends StiFinancialSeriesElementGeom {
        color: Color;
        beginTime: TimeSpan;
        draw(context: StiContext): void;
        constructor(areaGeom: StiAreaGeom, series: IStiSeries, clientRectangle: RectangleD, open: number, close: number, high: number, low: number, positionX: number, index: number, color: Color, beginTime: TimeSpan);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiSegmentGeom = Stimulsoft.Base.Context.StiSegmentGeom;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import List = Stimulsoft.System.Collections.List;
    class StiFunnelEmptySeriesElementGeom extends StiCellGeom {
        path: List<StiSegmentGeom>;
        draw(context: StiContext): void;
        constructor(clientRectangle: Rectangle, path: List<StiSegmentGeom>);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiSegmentGeom = Stimulsoft.Base.Context.StiSegmentGeom;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import TimeSpan = Stimulsoft.System.TimeSpan;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiFunnelSeriesElementGeom extends StiSeriesElementGeom {
        path: StiSegmentGeom[];
        borderColor: Color;
        brush: StiBrush;
        beginTime: TimeSpan;
        draw(context: StiContext): void;
        constructor(areaGeom: StiAreaGeom, value: number, index: number, series: IStiSeries, clientRectangle: RectangleD, brush: StiBrush, borderColor: Color, path: StiSegmentGeom[], beginTime: TimeSpan);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import StiAnimation = Stimulsoft.Base.Context.Animation.StiAnimation;
    class StiGanttSeriesElementGeom extends StiSeriesElementGeom {
        rectFrom: RectangleD;
        draw(context: StiContext): void;
        protected getAnimation(): StiAnimation;
        constructor(areaGeom: StiAreaGeom, value: number, index: number, series: IStiSeries, clientRectangle: RectangleD, rectFrom: RectangleD, brush: StiBrush);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import StiAnimation = Stimulsoft.Base.Context.Animation.StiAnimation;
    import StiStringFormatGeom = Stimulsoft.Base.Context.StiStringFormatGeom;
    import StiFontIcons = Stimulsoft.Report.Helpers.StiFontIcons;
    class StiPictorialSeriesElementGeom extends StiSeriesElementGeom {
        icon: StiFontIcons;
        drawRectangles: RectangleD[];
        clipRectangles: RectangleD[];
        seriesBrush: StiBrush;
        animation: StiAnimation;
        contains(x: number, y: number): boolean;
        draw(context: StiContext): void;
        getStringFormatGeom(context: StiContext): StiStringFormatGeom;
        constructor(areaGeom: StiAreaGeom, value: number, index: number, seriesBrush: StiBrush, series: IStiSeries, icon: StiFontIcons, drawRectangles: RectangleD[], clipRectangles: RectangleD[], clientRectangle: RectangleD, animation: StiAnimation);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import StiAnimation = Stimulsoft.Base.Context.Animation.StiAnimation;
    import StiStringFormatGeom = Stimulsoft.Base.Context.StiStringFormatGeom;
    import StiFontIcons = Stimulsoft.Report.Helpers.StiFontIcons;
    class StiPictorialStackedSeriesElementGeom extends StiSeriesElementGeom {
        icon: StiFontIcons;
        clipRectangle: RectangleD;
        animation: StiAnimation;
        contains(x: number, y: number): boolean;
        draw(context: StiContext): void;
        private getFontGeom;
        private measureFontSize;
        getStringFormatGeom(context: StiContext): StiStringFormatGeom;
        constructor(areaGeom: StiAreaGeom, value: number, index: number, seriesBrush: StiBrush, series: IStiSeries, icon: StiFontIcons, clientRectangle: RectangleD, clipRectangle: RectangleD, animation: StiAnimation);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiSeriesInteractionData = Stimulsoft.Base.Context.StiSeriesInteractionData;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiPie3dMainSideSeriesElementGeom extends StiSeriesElementGeom {
        borderColor: Color;
        startAngle: number;
        sweepAngle: number;
        realStartAngle: number;
        realSweepAngle: number;
        count: number;
        get interaction(): StiSeriesInteractionData;
        pie3DSlice: StiPie3dSlice;
        draw(context: StiContext): void;
        private getAnimation;
        constructor(pie3DSlice: StiPie3dSlice, areaGeom: StiAreaGeom, value: number, index: number, count: number, series: IStiPieSeries, clientRectangle: RectangleD, borderColor: Color, brush: StiBrush, startAngle: number, sweepAngle: number);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Color = Stimulsoft.System.Drawing.Color;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiSeriesInteractionData = Stimulsoft.Base.Context.StiSeriesInteractionData;
    import Point = Stimulsoft.System.Drawing.Point;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import StiPenGeom = Stimulsoft.Base.Context.StiPenGeom;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    class StiPie3dSidesSeriesElementGeom extends StiSeriesElementGeom {
        s_shadowAngle: number;
        pen: StiPenGeom;
        startnAngle: number;
        sweepAngle: number;
        m_boundingRectangle: Rectangle;
        m_sliceHeight: number;
        m_startAngle: number;
        m_sweepAngle: number;
        m_center: Point;
        m_centerBelow: Point;
        m_pointStart: Point;
        m_pointStartBelow: Point;
        m_pointEnd: Point;
        m_pointEndBelow: Point;
        m_brushStartSide: StiBrush;
        m_brushEndSide: StiBrush;
        m_brushPeripherySurface: StiBrush;
        m_brushSurface: StiBrush;
        m_startSide: StiPie3dQuadrilateral;
        m_endSide: StiPie3dQuadrilateral;
        count: number;
        startSideExists: boolean;
        endSideExists: boolean;
        get StartAngle(): number;
        get EndAngle(): number;
        get Interaction(): StiSeriesInteractionData;
        pie3DSlice: StiPie3dSlice;
        draw(context: StiContext): void;
        private drawStartSide;
        private drawEndSide;
        private drawVisiblePeriphery;
        private drawHiddenPeriphery;
        private getHiddenPeripherySurfaceBounds;
        private getVisiblePeripherySurfaceBounds;
        protected drawCylinderSurfaceSection(context: StiContext, pen: StiPenGeom, brush: StiBrush, peripherySurfaceBounds: StiPie3dPeripherySurfaceBounds): void;
        getTextPosition(): Point;
        protected getActualAngle(transformedAngle: number): number;
        private createPathForCylinderSurfaceSection;
        private initializePieSlice;
        private initializeSides;
        protected createSurfaceBrushes(shadowStyle: StiPie3dLightingStyle): void;
        protected createBrushForSide(color: Color, angle: number): StiBrush;
        protected createBrushForPeriphery(color: Color): StiBrush;
        static createColorWithCorrectedLightness(color: Color, correctionFactor: number): Color;
        protected peripheralPoint(xCenter: number, yCenter: number, semiMajor: number, semiMinor: number, angleDegrees: number): Point;
        protected transformAngle(angle: number): number;
        private getAnimation;
        constructor(pie3DSlice: StiPie3dSlice, areaGeom: StiAreaGeom, value: number, index: number, count: number, series: IStiPie3dSeries, clientRectangle: Rectangle, borderColor: Color, brush: StiBrush, startAngle: number, sweepAngle: number, pieHeight: number, startSideExists: boolean, endSideExists: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    class StiPieEmptySeriesElementGeom extends StiCellGeom {
        draw(context: StiContext): void;
        constructor(clientRectangle: Rectangle);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiSegmentGeom = Stimulsoft.Base.Context.StiSegmentGeom;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import TimeSpan = Stimulsoft.System.TimeSpan;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiPieSeriesElementGeom extends StiSeriesElementGeom {
        path: StiSegmentGeom[];
        pathLight: StiSegmentGeom[];
        borderColor: Color;
        brush: StiBrush;
        startAngle: number;
        endAngle: number;
        radius: number;
        beginTime: TimeSpan;
        count: number;
        contains(x: number, y: number): boolean;
        draw(context: StiContext): void;
        private getAnimation;
        constructor(areaGeom: StiAreaGeom, value: number, index: number, series: IStiPieSeries, clientRectangle: RectangleD, path: StiSegmentGeom[], pathLight: StiSegmentGeom[], borderColor: Color, brush: StiBrush, startAngle: number, endAngle: number, radius: number);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiPieSeriesFullElementGeom extends StiSeriesElementGeom {
        brush: StiBrush;
        borderColor: Color;
        draw(context: StiContext): void;
        constructor(areaGeom: StiAreaGeom, value: number, index: number, series: IStiPieSeries, clientRectangle: RectangleD, brush: StiBrush, borderColor: Color);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import TimeSpan = Stimulsoft.System.TimeSpan;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiPieSeriesShadowElementGeom extends StiCellGeom {
        get invisible(): boolean;
        series: IStiPieSeries;
        shadowContext: StiContext;
        radius: number;
        duration: TimeSpan;
        beginTime: TimeSpan;
        isAnimation: boolean;
        draw(context: StiContext): void;
        constructor(series: IStiPieSeries, clientRectangle: RectangleD, radius: number, shadowContext: StiContext, duration: TimeSpan, beginTime: TimeSpan);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import PointD = Stimulsoft.System.Drawing.Point;
    class StiRadarAreaSeriesGeom extends StiCellGeom {
        series: IStiSeries;
        pointsFrom: PointD[];
        points: PointD[];
        pointsIds: string[];
        contains(x: number, y: number): boolean;
        draw(context: StiContext): void;
        constructor(series: IStiSeries, pointsInfo: StiSeriesPointsInfo);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import PointD = Stimulsoft.System.Drawing.Point;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    class StiRadarPointSeriesElementGeom extends StiSeriesElementGeom {
        protected getValueIndex(): number;
        point: PointD;
        contains(x: number, y: number): boolean;
        getMouseOverRect(): RectangleD;
        draw(context: StiContext): void;
        constructor(areaGeom: StiAreaGeom, value: number, index: number, brush: StiBrush, series: IStiRadarSeries, point: PointD, zoom: number);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import StiAnimation = Stimulsoft.Base.Context.Animation.StiAnimation;
    class StiRangeBarElementGeom extends StiSeriesElementGeom {
        rectFrom: RectangleD;
        draw(context: StiContext): void;
        protected getAnimation(): StiAnimation;
        constructor(areaGeom: StiAreaGeom, value: number, index: number, series: IStiSeries, brush: StiBrush, clientRectangle: RectangleD, rectFrom: RectangleD);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import PointD = Stimulsoft.System.Drawing.Point;
    class StiRangeSeriesGeom extends StiLineSeriesGeom {
        pointsEnd: PointD[];
        draw(context: StiContext): void;
        private isPointsEqual;
        private getBrush;
        private fillPath;
        private intersection;
        private getPointCross2;
        constructor(areaGeom: StiAreaGeom, pointsInfo: StiSeriesPointsInfo, series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import PointD = Stimulsoft.System.Drawing.Point;
    class StiSplineRangeSeriesGeom extends StiSplineSeriesGeom {
        pointsEnd: PointD[];
        draw(context: StiContext): void;
        private fillPath;
        constructor(areaGeom: StiAreaGeom, pointsInfo: StiSeriesPointsInfo, series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import PointD = Stimulsoft.System.Drawing.Point;
    class StiSteppedRangeSeriesGeom extends StiSteppedLineSeriesGeom {
        pointsEnd: PointD[];
        draw(context: StiContext): void;
        private getBrush;
        private fillPath;
        private intersection;
        constructor(areaGeom: StiAreaGeom, pointsInfo: StiSeriesPointsInfo, series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    class StiScatterSplineSeriesGeom extends StiBaseLineSeriesGeom {
        contains(x: number, y: number): boolean;
        draw(context: StiContext): void;
        constructor(areaGeom: StiAreaGeom, pointsInfo: StiSeriesPointsInfo, series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import StiAnimation = Stimulsoft.Base.Context.Animation.StiAnimation;
    class StiStackedBarSeriesElementGeom extends StiSeriesElementGeom {
        seriesBorderColor: Color;
        columnRectStart: RectangleD;
        draw(context: StiContext): void;
        private getBorderPath;
        protected getAnimation(): StiAnimation;
        constructor(areaGeom: StiAreaGeom, value: number, index: number, seriesBrush: StiBrush, seriesBorderColor: Color, series: IStiSeries, clientRectangle: RectangleD, columnRectStart: RectangleD);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiStackedBarSeriesShadowElementGeom extends StiCellGeom {
        get invisible(): boolean;
        series: IStiSeries;
        isLeftShadow: boolean;
        isRightShadow: boolean;
        draw(context: StiContext): void;
        constructor(series: IStiSeries, clientRectangle: RectangleD, isLeftShadow: boolean, isRightShadow: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import List = Stimulsoft.System.Collections.List;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import TimeSpan = Stimulsoft.System.TimeSpan;
    class StiRibbonSeriesGeom extends StiSeriesGeom {
        metadata: StiRibbonSeriesMetadata;
        beginTime: TimeSpan;
        static getClientRectangle(rectangles: List<Rectangle>): Rectangle;
        draw(context: StiContext): void;
        constructor(areaGeom: StiAreaGeom, metadata: StiRibbonSeriesMetadata, series: IStiSeries, beginTime: TimeSpan);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import PointD = Stimulsoft.System.Drawing.Point;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiStackedAreaSeriesGeom extends StiSeriesGeom {
        startPoints: PointD[];
        endPoints: PointD[];
        pointsIds: string[];
        contains(x: number, y: number): boolean;
        static getClientRectangle(startPoints: PointD[], endPoints: PointD[]): RectangleD;
        draw(context: StiContext): void;
        constructor(areaGeom: StiAreaGeom, pointsInfo: StiSeriesPointsInfo, series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import PointD = Stimulsoft.System.Drawing.Point;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiStackedBaseLineSeriesGeom extends StiSeriesGeom {
        points: PointD[];
        static getClientRectangle(points: PointD[]): RectangleD;
        draw(context: StiContext): void;
        constructor(areaGeom: StiAreaGeom, points: PointD[], series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import StiAnimation = Stimulsoft.Base.Context.Animation.StiAnimation;
    class StiStackedColumnSeriesElementGeom extends StiSeriesElementGeom {
        seriesBorderColor: Color;
        columnRectStart: RectangleD;
        draw(context: StiContext): void;
        private getBorderPath;
        protected getAnimation(): StiAnimation;
        constructor(areaGeom: StiAreaGeom, value: number, index: number, seriesBrush: StiBrush, seriesBorderColor: Color, series: IStiSeries, clientRectangle: RectangleD, columnRectStart: RectangleD);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiStackedColumnSeriesShadowElementGeom extends StiCellGeom {
        get invisible(): boolean;
        private series;
        private isTopShadow;
        private isBottomShadow;
        draw(context: StiContext): void;
        constructor(series: IStiSeries, clientRectangle: RectangleD, isTopShadow: boolean, isBottomShadow: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    class StiStackedLineSeriesGeom extends StiBaseLineSeriesGeom {
        contains(x: number, y: number): boolean;
        draw(context: StiContext): void;
        private getPointCross;
        constructor(areaGeom: StiAreaGeom, pointsInfo: StiSeriesPointsInfo, series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import PointD = Stimulsoft.System.Drawing.Point;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiStackedSplineAreaSeriesGeom extends StiSeriesGeom {
        startPoints: PointD[];
        endPoints: PointD[];
        pointsIds: string[];
        contains(x: number, y: number): boolean;
        static getClientRectangle(startPoints: PointD[], endPoints: PointD[]): RectangleD;
        draw(context: StiContext): void;
        constructor(areaGeom: StiAreaGeom, pointsInfo: StiSeriesPointsInfo, series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    class StiStackedSplineSeriesGeom extends StiBaseLineSeriesGeom {
        contains(x: number, y: number): boolean;
        draw(context: StiContext): void;
        constructor(areaGeom: StiAreaGeom, pointsInfo: StiSeriesPointsInfo, series: IStiSeries);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiSegmentGeom = Stimulsoft.Base.Context.StiSegmentGeom;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import TimeSpan = Stimulsoft.System.TimeSpan;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import List = Stimulsoft.System.Collections.List;
    class StiSunburstSeriesElementGeom extends StiSeriesElementGeom {
        private index2;
        private index3;
        clientRectangleDt: RectangleD;
        startAngle: number;
        endAngle: number;
        radiusFrom: number;
        path: List<StiSegmentGeom>;
        borderColor: Color;
        brush: StiBrush;
        radiusTo: number;
        beginTime: TimeSpan;
        contains(x: number, y: number): boolean;
        draw(context: StiContext): void;
        private getAnimation;
        constructor(areaGeom: StiAreaGeom, value: number, index1: number, index2: number, index3: number, series: IStiSeries, clientRectangle: RectangleD, clientRectangleDt: RectangleD, path: List<StiSegmentGeom>, borderColor: Color, brush: StiBrush, startAngle: number, endAngle: number, radiusFrom: number, radiusTo: number, beginTime: TimeSpan);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import StiAnimation = Stimulsoft.Base.Context.Animation.StiAnimation;
    class StiTreemapSeriesElementGeom extends StiSeriesElementGeom {
        seriesBrush: StiBrush;
        seriesBorderColor: Color;
        animation: StiAnimation;
        draw(context: StiContext): void;
        constructor(areaGeom: StiAreaGeom, value: number, index: number, seriesBrush: StiBrush, seriesBorderColor: Color, series: IStiSeries, clientRectangle: RectangleD, animation: StiAnimation);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import TimeSpan = Stimulsoft.System.TimeSpan;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiSeriesLabelsGeom extends StiCellGeom {
        private getValueIndex;
        private getHyperlink;
        private getToolTip;
        value: number;
        index: number;
        series: IStiSeries;
        seriesLabels: IStiSeriesLabels;
        beginTime: TimeSpan;
        duration: TimeSpan;
        drawMarker(context: StiContext, itemRect: Rectangle, markerColor: any, markerBrush: StiBrush): void;
        draw(context: StiContext): void;
        constructor(seriesLabels: IStiSeriesLabels, series: IStiSeries, index: number, value: number, clientRectangle: RectangleD);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiFontGeom = Stimulsoft.Base.Context.StiFontGeom;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import StiAnimation = Stimulsoft.Base.Context.Animation.StiAnimation;
    class StiCenterAxisLabelsGeom extends StiSeriesLabelsGeom {
        labelColor: Color;
        labelBorderColor: Color;
        seriesBrush: StiBrush;
        seriesLabelsBrush: StiBrush;
        seriesBorderColor: Color;
        font: StiFontGeom;
        text: string;
        animation: StiAnimation;
        draw(context: StiContext): void;
        private drawLabelArea;
        private drawLabelText;
        constructor(seriesLabels: IStiSeriesLabels, series: IStiSeries, index: number, value: number, clientRectangle: RectangleD, text: string, labelColor: Color, labelBorderColor: Color, seriesBrush: StiBrush, seriesLabelsBrush: StiBrush, seriesBorderColor: Color, font: StiFontGeom, animation: StiAnimation);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiAnimation = Stimulsoft.Base.Context.Animation.StiAnimation;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiFontGeom = Stimulsoft.Base.Context.StiFontGeom;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import PointD = Stimulsoft.System.Drawing.Point;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiOutsideAxisLabelsGeom extends StiSeriesLabelsGeom {
        labelColor: Color;
        labelBorderColor: Color;
        seriesBrush: StiBrush;
        seriesBorderColor: Color;
        font: StiFontGeom;
        text: string;
        startPoint: PointD;
        endPoint: PointD;
        animation: StiAnimation;
        draw(context: StiContext): void;
        private drawLabelArea;
        private drawLines;
        private drawLabelText;
        constructor(seriesLabels: IStiSeriesLabels, series: IStiSeries, index: number, value: number, clientRectangle: RectangleD, text: string, labelColor: Color, labelBorderColor: Color, seriesBrush: StiBrush, seriesBorderColor: Color, font: StiFontGeom, startPoint: PointD, endPoint: PointD, animation: StiAnimation);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import StiAnimation = Stimulsoft.Base.Context.Animation.StiAnimation;
    class StiCenterFunnelLabelsGeom extends StiSeriesLabelsGeom {
        seriesBrush: StiBrush;
        borderColor: Color;
        seriesBorderColor: Color;
        labelBrush: StiBrush;
        text: string;
        labelRect: RectangleD;
        animation: StiAnimation;
        draw(context: StiContext): void;
        private drawLabelArea;
        private drawLabelText;
        constructor(seriesLabels: IStiSeriesLabels, series: IStiSeries, index: number, value: number, clientRectangle: RectangleD, text: string, seriesBrush: StiBrush, labelBrush: StiBrush, borderColor: Color, seriesBorderColor: Color, labelRect: RectangleD, animation: StiAnimation);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import PointD = Stimulsoft.System.Drawing.Point;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiOutsideFunnelLabelsGeom extends StiCenterFunnelLabelsGeom {
        startPointLine: PointD;
        endPointLine: PointD;
        draw(context: StiContext): void;
        constructor(seriesLabels: IStiSeriesLabels, series: IStiSeries, index: number, value: number, clientRectangle: RectangleD, text: string, seriesBrush: StiBrush, labelBrush: StiBrush, borderColor: Color, seriesBorderColor: Color, labelRect: RectangleD, startPointLine: PointD, endPointLine: PointD);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import StiAnimation = Stimulsoft.Base.Context.Animation.StiAnimation;
    class StiOutsidePictorialStackedLabelGeom extends StiSeriesLabelsGeom {
        seriesBrush: StiBrush;
        borderColor: Color;
        seriesBorderColor: Color;
        labelLineColor: Color;
        labelBrush: StiBrush;
        text: string;
        labelRect: RectangleD;
        lineRect: RectangleD;
        animation: StiAnimation;
        draw(context: StiContext): void;
        private drawLineLabel;
        private drawLabelArea;
        private drawLabelText;
        constructor(seriesLabels: IStiSeriesLabels, series: IStiSeries, index: number, value: number, clientRectangle: RectangleD, text: string, seriesBrush: StiBrush, labelBrush: StiBrush, labelLineColor: Color, borderColor: Color, seriesBorderColor: Color, labelRect: RectangleD, lineRect: RectangleD, animation: StiAnimation);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiRotationMode = Stimulsoft.Base.Drawing.StiRotationMode;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import StiAnimation = Stimulsoft.Base.Context.Animation.StiAnimation;
    class StiCenterPieLabelsGeom extends StiSeriesLabelsGeom {
        seriesBrush: StiBrush;
        borderColor: Color;
        seriesBorderColor: Color;
        seriesLabelsBrush: StiBrush;
        labelBrush: StiBrush;
        text: string;
        rotationMode: StiRotationMode;
        labelRect: RectangleD;
        angleToUse: number;
        animation: StiAnimation;
        draw(context: StiContext): void;
        private drawLabelArea;
        private drawLabelText;
        constructor(seriesLabels: IStiSeriesLabels, series: IStiSeries, index: number, value: number, clientRectangle: RectangleD, text: string, seriesBrush: StiBrush, labelBrush: StiBrush, seriesLabelsBrush: StiBrush, borderColor: Color, seriesBorderColor: Color, rotationMode: StiRotationMode, labelRect: RectangleD, angleToUse: number, animation: StiAnimation);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiRotationMode = Stimulsoft.Base.Drawing.StiRotationMode;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import PointD = Stimulsoft.System.Drawing.Point;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiOutsidePieLabelsGeom extends StiCenterPieLabelsGeom {
        lineColor: Color;
        labelPoint: PointD;
        startPoint: PointD;
        draw(context: StiContext): void;
        constructor(seriesLabels: IStiSeriesLabels, series: IStiSeries, index: number, value: number, clientRectangle: RectangleD, text: string, seriesBrush: StiBrush, labelBrush: StiBrush, seriesLabelsBrush: StiBrush, borderColor: Color, seriesBorderColor: Color, rotationMode: StiRotationMode, labelRect: RectangleD, angleToUse: number, lineColor: Color, labelPoint: PointD, startPoint: PointD);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiAnimation = Stimulsoft.Base.Context.Animation.StiAnimation;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import PointD = Stimulsoft.System.Drawing.Point;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiTwoColumnsPieLabelsGeom extends StiSeriesLabelsGeom {
        seriesBrush: StiBrush;
        borderColor: Color;
        seriesBorderColor: Color;
        labelBrush: StiBrush;
        seriesLabelsBrush: StiBrush;
        text: string;
        labelRect: RectangleD;
        lineColor: Color;
        startPoint: PointD;
        endPoint: PointD;
        arcPoint: PointD;
        centerPie: PointD;
        animation: StiAnimation;
        draw(context: StiContext): void;
        private drawLines;
        drawMarker(context: StiContext, itemRect: Rectangle, markerColor: any, markerBrush: StiBrush): void;
        private drawLabelArea;
        private drawLabelText;
        constructor(seriesLabels: IStiSeriesLabels, series: IStiSeries, index: number, value: number, clientRectangle: RectangleD, text: string, seriesBrush: StiBrush, labelBrush: StiBrush, seriesLabelsBrush: StiBrush, borderColor: Color, seriesBorderColor: Color, labelRect: RectangleD, lineColor: Color, startPoint: PointD, endPoint: PointD, arcPoint: PointD, centerPie: PointD, animation: StiAnimation);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Color = Stimulsoft.System.Drawing.Color;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import StiFontGeom = Stimulsoft.Base.Context.StiFontGeom;
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    import StiAnimation = Stimulsoft.Base.Context.Animation.StiAnimation;
    class StiCenterTreemapLabelsGeom extends StiSeriesLabelsGeom {
        labelColor: Color;
        labelBorderColor: Color;
        seriesBrush: StiBrush;
        seriesLabelsBrush: StiBrush;
        seriesBorderColor: Color;
        font: StiFontGeom;
        text: string;
        animation: StiAnimation;
        draw(context: StiContext): void;
        private drawLabelArea;
        private drawLabelText;
        constructor(seriesLabels: IStiSeriesLabels, series: IStiSeries, index: number, value: number, clientRectangle: RectangleD, text: string, labelColor: Color, labelBorderColor: Color, seriesBrush: StiBrush, seriesLabelsBrush: StiBrush, seriesBorderColor: Color, font: StiFontGeom, animation: StiAnimation);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiStripsXGeom extends StiCellGeom {
        strip: IStiStrips;
        draw(context: StiContext): void;
        constructor(strip: IStiStrips, clientRectangle: RectangleD);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiStripsYGeom extends StiCellGeom {
        strip: IStiStrips;
        draw(context: StiContext): void;
        constructor(strip: IStiStrips, clientRectangle: RectangleD);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import RectangleD = Stimulsoft.System.Drawing.Rectangle;
    class StiChartTableGeom extends StiCellGeom {
        constructor(clientRectangle: RectangleD, table: string[][], widthCellLegendTableChart: number, heightCellHeader: number, chartTable: IStiChartTable);
        private table;
        private widthCellLegendTableChart;
        private heightCellHeader;
        private chartTable;
        private pen;
        private font;
        private fontHeader;
        private labelBrush;
        private sf;
        private sfHeader;
        private labelHeaderBrush;
        draw(context: StiContext): void;
        private drawHeaderArgument;
        private drawTitleLegend;
        private drawRootTable;
        private checkFontSize;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import PointD = Stimulsoft.System.Drawing.Point;
    class StiTrendCurveGeom extends StiCellGeom {
        points: PointD[];
        trendLine: IStiTrendLine;
        draw(context: StiContext): void;
        constructor(points: PointD[], trendLine: IStiTrendLine);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import PointD = Stimulsoft.System.Drawing.Point;
    class StiTrendLineGeom extends StiCellGeom {
        private trendLine;
        private pointStart;
        private pointEnd;
        draw(context: StiContext): void;
        private static getArray;
        constructor(pointStart: PointD, pointEnd: PointD, trendLine: IStiTrendLine);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import StiJson = Stimulsoft.Base.StiJson;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    class Sti3dOptions implements ISti3dOptions {
        protected _hash: StiMeta[];
        meta(): StiMeta[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        isDefault(): boolean;
        get componentId(): StiComponentId;
        get propName(): string;
        private opacity_;
        get opacity(): number;
        set opacity(value: number);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiPie3dOptions extends Sti3dOptions implements IStiPie3dOptions {
        meta(): StiMeta[];
        lighting: StiPie3dLightingStyle;
        private height_;
        get height(): number;
        set height(value: number);
        private distance_;
        get distance(): number;
        set distance(value: number);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiBoxAndWhiskerArea extends StiAxisArea implements IStiBoxAndWhiskerArea {
        private static implementsStiBoxAndWhiskerArea;
        implements(): any[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        getSeriesTypes(): Stimulsoft.System.Type[];
        getSeriesLabelsTypes(): Stimulsoft.System.Type[];
        get componentId(): StiComponentId;
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiScatterArea extends StiClusteredColumnArea implements IStiScatterArea, IStiArea, IStiClusteredColumnArea, IStiAxisArea, IStiJsonReportObject, ICloneable {
        private static implementsStiScatterArea;
        implements(): any[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        getSeriesTypes(): Stimulsoft.System.Type[];
        get componentId(): StiComponentId;
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiBubbleArea extends StiScatterArea implements IStiScatterArea, IStiClusteredColumnArea, IStiArea, IStiAxisArea, IStiJsonReportObject, ICloneable, IStiBubbleArea {
        private static implementsStiBubbleArea;
        implements(): any[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        getSeriesTypes(): Stimulsoft.System.Type[];
        get componentId(): StiComponentId;
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiCandlestickArea extends StiClusteredColumnArea implements IStiCandlestickArea, IStiClusteredColumnArea, IStiAxisArea, IStiJsonReportObject, IStiArea, ICloneable {
        private static implementsStiCandlestickArea;
        implements(): any[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        getSeriesTypes(): Stimulsoft.System.Type[];
        getSeriesLabelsTypes(): Stimulsoft.System.Type[];
        get componentId(): StiComponentId;
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiClusteredBarArea extends StiClusteredColumnArea implements IStiArea, IStiClusteredBarArea, IStiClusteredColumnArea, IStiAxisArea, IStiJsonReportObject, ICloneable {
        private static implementsStiClusteredBarArea;
        implements(): any[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        getSeriesTypes(): Stimulsoft.System.Type[];
        get componentId(): StiComponentId;
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiAreaArea extends StiClusteredColumnArea implements IStiArea, IStiClusteredColumnArea, IStiAxisArea, IStiAreaArea, IStiJsonReportObject, ICloneable {
        private static implementsStiAreaArea;
        implements(): any[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        get componentId(): StiComponentId;
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiHistogramArea extends StiAxisArea {
        get componentId(): StiComponentId;
        getDefaultSeriesType(): Stimulsoft.System.Type;
        getSeriesTypes(): Stimulsoft.System.Type[];
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiLineArea extends StiClusteredColumnArea implements IStiArea, IStiLineArea, IStiClusteredColumnArea, IStiAxisArea, IStiJsonReportObject, ICloneable {
        private static implementsStiLineArea;
        implements(): any[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        get componentId(): StiComponentId;
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiParetoArea extends StiClusteredColumnArea implements IStiArea, IStiParetoArea, IStiClusteredColumnArea, IStiAxisArea, IStiJsonReportObject, ICloneable {
        private static implementsStiParetoArea;
        implements(): any[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        getSeriesTypes(): Stimulsoft.System.Type[];
        get componentId(): StiComponentId;
        createNew(): StiParetoArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiSplineArea extends StiClusteredColumnArea implements IStiArea, IStiSplineArea, IStiClusteredColumnArea, IStiAxisArea, IStiJsonReportObject, ICloneable {
        private static implementsStiSplineArea;
        implements(): any[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        get componentId(): StiComponentId;
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiSplineAreaArea extends StiClusteredColumnArea implements IStiArea, IStiSplineAreaArea, IStiClusteredColumnArea, IStiAxisArea, IStiJsonReportObject, ICloneable {
        private static implementsStiSplineAreaArea;
        implements(): any[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        get componentId(): StiComponentId;
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiSteppedAreaArea extends StiClusteredColumnArea implements IStiArea, IStiSteppedAreaArea, IStiClusteredColumnArea, IStiAxisArea, IStiJsonReportObject, ICloneable {
        private static implementsStiSteppedAreaArea;
        implements(): any[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        get componentId(): StiComponentId;
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiSteppedLineArea extends StiClusteredColumnArea implements IStiArea, IStiClusteredColumnArea, IStiAxisArea, IStiSteppedLineArea, IStiJsonReportObject, ICloneable {
        private static implementsStiSteppedLineArea;
        implements(): any[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        get componentId(): StiComponentId;
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiWaterfallArea extends StiAxisArea implements IStiWaterfallArea, IStiRoundValuesArea {
        private static implementsStiWaterfallArea;
        implements(): any[];
        meta(): StiMeta[];
        roundValues: boolean;
        get componentId(): StiComponentId;
        getDefaultSeriesType(): Stimulsoft.System.Type;
        getSeriesTypes(): Stimulsoft.System.Type[];
        getSeriesLabelsTypes(): Stimulsoft.System.Type[];
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiPieArea extends StiArea implements IStiJsonReportObject, IStiPieArea, IStiArea, ICloneable {
        private static implementsStiPieArea;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        getDefaultSeriesLabelsType(): Stimulsoft.System.Type;
        getSeriesLabelsTypes(): Stimulsoft.System.Type[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        getSeriesTypes(): Stimulsoft.System.Type[];
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiDoughnutArea extends StiPieArea implements IStiJsonReportObject, IStiPieArea, IStiArea, ICloneable, IStiDoughnutArea {
        private static implementsStiDoughnutArea;
        implements(): any[];
        meta(): StiMeta[];
        getDefaultSeriesLabelsType(): Stimulsoft.System.Type;
        getSeriesLabelsTypes(): Stimulsoft.System.Type[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        getSeriesTypes(): Stimulsoft.System.Type[];
        get componentId(): StiComponentId;
        createNew(): StiArea;
        clone(): StiDoughnutArea;
        indicator: StiDoughnutAreaIndicator;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import StiJson = Stimulsoft.Base.StiJson;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiDoughnutAreaIndicator implements IStiJsonReportObject {
        private static implementsStiDoughnutAreaIndicator;
        implements(): any[];
        protected _hash: StiMeta[];
        meta(): StiMeta[];
        saveToJsonObject(m: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        clone(): StiDoughnutAreaIndicator;
        visible: boolean;
        value: StiDoughnutAreaIndicatorValue;
        title: StiDoughnutAreaIndicatorTitle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import ICloneable = Stimulsoft.System.ICloneable;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import StiJson = Stimulsoft.Base.StiJson;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiDoughnutAreaIndicatorTitle implements IStiJsonReportObject, ICloneable {
        private static implementsStiDoughnutAreaIndicatorTitle;
        implements(): any[];
        protected _hash: StiMeta[];
        meta(): StiMeta[];
        saveToJsonObject(m: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        clone(): StiDoughnutAreaIndicatorTitle;
        getColor(): Color;
        styleColor: Color;
        text: string;
        color: Color;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import ICloneable = Stimulsoft.System.ICloneable;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import StiJson = Stimulsoft.Base.StiJson;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiDoughnutAreaIndicatorValue implements IStiJsonReportObject, ICloneable {
        private static implementsStiDoughnutAreaIndicatorValue;
        implements(): any[];
        protected _hash: StiMeta[];
        meta(): StiMeta[];
        saveToJsonObject(m: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        clone(): StiDoughnutAreaIndicatorValue;
        getColor(): Color;
        styleColor: Color;
        value: string;
        color: Color;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiStackedBarArea extends StiClusteredBarArea implements IStiClusteredBarArea, IStiClusteredColumnArea, IStiArea, IStiAxisArea, IStiJsonReportObject, IStiStackedBarArea, ICloneable {
        private static implementsStiStackedBarArea;
        implements(): any[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        getSeriesTypes(): Stimulsoft.System.Type[];
        get componentId(): StiComponentId;
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiFullStackedBarArea extends StiStackedBarArea implements IStiClusteredBarArea, IStiClusteredColumnArea, IStiArea, IStiAxisArea, IStiFullStackedBarArea, IStiStackedBarArea, IStiJsonReportObject, ICloneable {
        private static implementsStiFullStackedBarArea;
        implements(): any[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        getSeriesTypes(): Stimulsoft.System.Type[];
        get componentId(): StiComponentId;
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiStackedColumnArea extends StiAxisArea implements IStiJsonReportObject, IStiStackedColumnArea, IStiAxisArea, ICloneable, IStiArea {
        private static implementsStiStackedColumnArea;
        implements(): any[];
        get componentId(): StiComponentId;
        getDefaultSeriesType(): Stimulsoft.System.Type;
        getSeriesTypes(): Stimulsoft.System.Type[];
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiFullStackedColumnArea extends StiStackedColumnArea implements IStiStackedColumnArea, IStiArea, IStiAxisArea, IStiFullStackedColumnArea, IStiJsonReportObject, ICloneable {
        private static implementsStiFullStackedColumnArea;
        implements(): any[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        getSeriesTypes(): Stimulsoft.System.Type[];
        get componentId(): StiComponentId;
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiFullStackedAreaArea extends StiFullStackedColumnArea implements IStiStackedColumnArea, IStiArea, IStiAxisArea, IStiFullStackedColumnArea, IStiJsonReportObject, ICloneable, IStiFullStackedAreaArea {
        private static implementsStiFullStackedAreaArea;
        implements(): any[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        get componentId(): StiComponentId;
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiFullStackedLineArea extends StiFullStackedColumnArea implements IStiStackedColumnArea, IStiArea, IStiAxisArea, IStiFullStackedColumnArea, IStiJsonReportObject, ICloneable, IStiFullStackedLineArea {
        private static implementsStiFullStackedLineArea;
        implements(): any[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        get componentId(): StiComponentId;
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiFullStackedSplineArea extends StiFullStackedColumnArea implements IStiFullStackedSplineArea, IStiStackedColumnArea, IStiArea, IStiAxisArea, IStiFullStackedColumnArea, IStiJsonReportObject, ICloneable {
        private static implementsStiFullStackedSplineArea;
        implements(): any[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        get componentId(): StiComponentId;
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiFullStackedSplineAreaArea extends StiFullStackedColumnArea implements IStiStackedColumnArea, IStiArea, IStiAxisArea, IStiFullStackedColumnArea, IStiFullStackedSplineAreaArea, IStiJsonReportObject, ICloneable {
        private static implementsStiFullStackedSplineAreaArea;
        implements(): any[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        get componentId(): StiComponentId;
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiFunnelArea extends StiArea implements IStiJsonReportObject, IStiArea, ICloneable, IStiFunnelArea {
        private static implementsStiFunnelArea;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        getDefaultSeriesType(): Stimulsoft.System.Type;
        getSeriesTypes(): Stimulsoft.System.Type[];
        getDefaultSeriesLabelsType(): Stimulsoft.System.Type;
        getSeriesLabelsTypes(): Stimulsoft.System.Type[];
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiFunnelWeightedSlicesArea extends StiFunnelArea {
        get componentId(): StiComponentId;
        getDefaultSeriesType(): Stimulsoft.System.Type;
        getSeriesTypes(): Stimulsoft.System.Type[];
        getDefaultSeriesLabelsType(): Stimulsoft.System.Type;
        getSeriesLabelsTypes(): Stimulsoft.System.Type[];
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiGanttArea extends StiClusteredBarArea implements IStiClusteredBarArea, IStiClusteredColumnArea, IStiArea, IStiAxisArea, IStiJsonReportObject, IStiGanttArea, ICloneable {
        private static implementsStiGanttArea;
        implements(): any[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        getSeriesTypes(): Stimulsoft.System.Type[];
        getSeriesLabelsTypes(): Stimulsoft.System.Type[];
        get componentId(): StiComponentId;
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiPictorialArea extends StiArea implements IStiJsonReportObject, IStiPictorialArea, IStiRoundValuesArea, IStiArea, ICloneable {
        private static implementsStiPictorialArea;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        roundValues: boolean;
        actual: boolean;
        getDefaultSeriesLabelsType(): Stimulsoft.System.Type;
        getSeriesLabelsTypes(): Stimulsoft.System.Type[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        getSeriesTypes(): Stimulsoft.System.Type[];
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiPictorialStackedArea extends StiArea implements IStiJsonReportObject, IStiArea, ICloneable {
        private static implementsStiPictorialStackedArea;
        implements(): any[];
        get componentId(): StiComponentId;
        getDefaultSeriesLabelsType(): Stimulsoft.System.Type;
        getSeriesLabelsTypes(): Stimulsoft.System.Type[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        getSeriesTypes(): Stimulsoft.System.Type[];
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiPie3dArea extends StiPieArea {
        meta(): StiMeta[];
        getDefaultSeriesLabelsType(): Stimulsoft.System.Type;
        getSeriesLabelsTypes(): Stimulsoft.System.Type[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        getSeriesTypes(): Stimulsoft.System.Type[];
        get componentId(): StiComponentId;
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiRadarAreaArea extends StiRadarArea implements IStiJsonReportObject, IStiRadarArea, IStiArea, IStiRadarAreaArea, ICloneable {
        private static implementsStiRadarAreaArea;
        implements(): any[];
        get componentId(): StiComponentId;
        getDefaultSeriesType(): Stimulsoft.System.Type;
        getSeriesTypes(): Stimulsoft.System.Type[];
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiRadarLineArea extends StiRadarArea implements IStiJsonReportObject, IStiRadarArea, IStiArea, ICloneable, IStiRadarLineArea {
        private static implementsStiRadarLineArea;
        implements(): any[];
        get componentId(): StiComponentId;
        getDefaultSeriesType(): Stimulsoft.System.Type;
        getSeriesTypes(): Stimulsoft.System.Type[];
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiRadarPointArea extends StiRadarArea implements IStiJsonReportObject, IStiRadarPointArea, IStiRadarArea, IStiArea, ICloneable {
        private static implementsStiRadarPointArea;
        implements(): any[];
        get componentId(): StiComponentId;
        getDefaultSeriesType(): Stimulsoft.System.Type;
        getSeriesTypes(): Stimulsoft.System.Type[];
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiRangeArea extends StiClusteredColumnArea implements IStiArea, IStiRangeArea, IStiClusteredColumnArea, IStiAxisArea, IStiJsonReportObject, ICloneable {
        private static implementsStiRangeArea;
        implements(): any[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        getSeriesTypes(): Stimulsoft.System.Type[];
        get componentId(): StiComponentId;
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiRangeBarArea extends StiClusteredColumnArea implements IStiArea, IStiRangeBarArea, IStiClusteredColumnArea, IStiAxisArea, IStiJsonReportObject, ICloneable {
        private static implementsStiRangeBarArea;
        implements(): any[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        getSeriesTypes(): Stimulsoft.System.Type[];
        getSeriesLabelsTypes(): Stimulsoft.System.Type[];
        get componentId(): StiComponentId;
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiSplineRangeArea extends StiClusteredColumnArea implements IStiArea, IStiAxisArea, IStiClusteredColumnArea, IStiSplineRangeArea, IStiJsonReportObject, ICloneable {
        private static implementsStiSplineRangeArea;
        implements(): any[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        getSeriesTypes(): Stimulsoft.System.Type[];
        get componentId(): StiComponentId;
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiSteppedRangeArea extends StiClusteredColumnArea implements IStiArea, IStiClusteredColumnArea, IStiSteppedRangeArea, IStiAxisArea, IStiJsonReportObject, ICloneable {
        private static implementsStiSteppedRangeArea;
        implements(): any[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        getSeriesTypes(): Stimulsoft.System.Type[];
        get componentId(): StiComponentId;
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiRibbonArea extends StiAxisArea implements IStiJsonReportObject, IStiRibbonArea, IStiAxisArea, ICloneable, IStiArea {
        private static implementsStiRibbonArea;
        implements(): any[];
        get componentId(): StiComponentId;
        getDefaultSeriesType(): Stimulsoft.System.Type;
        getSeriesTypes(): Stimulsoft.System.Type[];
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiStackedAreaArea extends StiStackedColumnArea implements IStiStackedColumnArea, IStiArea, IStiAxisArea, IStiStackedAreaArea, IStiJsonReportObject, ICloneable {
        private static implementsStiStackedAreaArea;
        implements(): any[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        get componentId(): StiComponentId;
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiStackedLineArea extends StiStackedColumnArea implements IStiStackedLineArea, IStiArea, IStiAxisArea, IStiStackedColumnArea, IStiJsonReportObject, ICloneable {
        private static implementsStiStackedLineArea;
        implements(): any[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        get componentId(): StiComponentId;
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiStackedSplineArea extends StiStackedColumnArea implements IStiStackedSplineArea, IStiStackedColumnArea, IStiArea, IStiAxisArea, IStiJsonReportObject, ICloneable {
        private static implementsStiStackedSplineArea;
        implements(): any[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        get componentId(): StiComponentId;
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiStackedSplineAreaArea extends StiStackedColumnArea implements IStiAxisArea, IStiStackedColumnArea, IStiArea, IStiStackedSplineAreaArea, IStiJsonReportObject, ICloneable {
        private static implementsStiStackedSplineAreaArea;
        implements(): any[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        get componentId(): StiComponentId;
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiStockArea extends StiClusteredColumnArea implements IStiArea, IStiStockArea, IStiClusteredColumnArea, IStiAxisArea, IStiJsonReportObject, ICloneable {
        private static implementsStiStockArea;
        implements(): any[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        getSeriesTypes(): Stimulsoft.System.Type[];
        getSeriesLabelsTypes(): Stimulsoft.System.Type[];
        get componentId(): StiComponentId;
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiSunburstArea extends StiArea implements IStiJsonReportObject, IStiSunburstArea, ICloneable {
        private static implementsStiSunburstArea;
        implements(): any[];
        get componentId(): StiComponentId;
        getDefaultSeriesLabelsType(): Stimulsoft.System.Type;
        getSeriesLabelsTypes(): Stimulsoft.System.Type[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        getSeriesTypes(): Stimulsoft.System.Type[];
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiTreemapArea extends StiArea implements IStiJsonReportObject, IStiTreemapArea, ICloneable {
        private static implementsStiTreemapArea;
        implements(): any[];
        get componentId(): StiComponentId;
        getDefaultSeriesLabelsType(): Stimulsoft.System.Type;
        getSeriesLabelsTypes(): Stimulsoft.System.Type[];
        getDefaultSeriesType(): Stimulsoft.System.Type;
        getSeriesTypes(): Stimulsoft.System.Type[];
        createNew(): StiArea;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StringAlignment = Stimulsoft.System.Drawing.StringAlignment;
    import Color = Stimulsoft.System.Drawing.Color;
    import Font = Stimulsoft.System.Drawing.Font;
    class StiAxisTitle implements IStiAxisTitle, ICloneable, IStiJsonReportObject {
        private static implementsStiAxisTitle;
        implements(): any[];
        protected _hash: StiMeta[];
        meta(): StiMeta[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        clone(): StiAxisTitle;
        core: StiAxisTitleCoreXF;
        allowApplyStyle: boolean;
        font: Font;
        text: string;
        color: Color;
        antialiasing: boolean;
        alignment: StringAlignment;
        position: StiTitlePosition;
        direction: StiDirection;
        constructor(font?: Font, text?: string, color?: Color, antialiasing?: boolean, alignment?: StringAlignment, direction?: StiDirection, allowApplyStyle?: boolean, position?: StiTitlePosition);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiPenStyle = Stimulsoft.Base.Drawing.StiPenStyle;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiService = Stimulsoft.Base.Services.StiService;
    import Font = Stimulsoft.System.Drawing.Font;
    class StiConstantLines extends StiService implements IStiConstantLines, ICloneable, IStiJsonReportObject {
        private static implementsStiConstantLines;
        implements(): any[];
        meta(): StiMeta[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        get componentId(): StiComponentId;
        get propName(): string;
        clone(): StiConstantLines;
        get serviceCategory(): string;
        get ServiceType(): Stimulsoft.System.Type;
        private _allowApplyStyle;
        get allowApplyStyle(): boolean;
        set allowApplyStyle(value: boolean);
        core: StiConstantLinesCoreXF;
        antialiasing: boolean;
        position: StiConstantLines_StiTextPosition;
        font: Font;
        text: string;
        titleVisible: boolean;
        orientation: StiConstantLines_StiOrientation;
        lineWidth: number;
        lineStyle: StiPenStyle;
        lineColor: Color;
        showInLegend: boolean;
        showBehind: boolean;
        axisValue: string;
        visible: boolean;
        chart: IStiChart;
        toString(): string;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import CollectionBase = Stimulsoft.System.Collections.CollectionBase;
    class StiChartFiltersCollection extends CollectionBase<IStiChartFilter> implements IStiJsonReportObject, ICloneable {
        private static implementsStiChartFiltersCollection;
        implements(): any[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(jObject: StiJson): void;
        loadFromXml(xmlNode: XmlNode): void;
        clone(): StiChartFiltersCollection;
        add(filter: StiChartFilter): void;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IComparer = Stimulsoft.System.Collections.IComparer;
    class StiDataItem {
        index: number;
        argument: any;
        value: any;
        valueEnd: any;
        weight: any;
        valueOpen: any;
        valueClose: any;
        valueLow: any;
        valueHigh: any;
        title: any;
        key: any;
        color: any;
        toolTip: any;
        tag: any;
        constructor(index: number, argument: any, value: any, valueEnd: any, weight: any, valueOpen: any, valueClose: any, valueLow: any, valueHight: any, title: any, key: any, color: any, toolTip: any, tag: any);
    }
    class StiDataItemComparer implements IComparer<StiDataItem> {
        compare(x: StiDataItem, y: StiDataItem): number;
        private directionFactor;
        private sortType;
        constructor(sortType: StiSeriesSortType, sortDirection: StiSeriesSortDirection);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiPenStyle = Stimulsoft.Base.Drawing.StiPenStyle;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiRadarGridLines implements IStiJsonReportObject, IStiRadarGridLines, ICloneable {
        private static implementsStiRadarGridLines;
        implements(): any[];
        protected _hash: StiMeta[];
        meta(): StiMeta[];
        needSetAreaJsonPropertyInternal: boolean;
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        get componentId(): StiComponentId;
        get propName(): string;
        clone(): StiRadarGridLines;
        private _allowApplyStyle;
        get allowApplyStyle(): boolean;
        set allowApplyStyle(value: boolean);
        core: StiRadarGridLinesCoreXF;
        color: Color;
        style: StiPenStyle;
        visible: boolean;
        area: IStiArea;
        constructor(color?: Color, style?: StiPenStyle, visible?: boolean, allowApplyStyle?: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiPenStyle = Stimulsoft.Base.Drawing.StiPenStyle;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiRadarGridLinesHor extends StiRadarGridLines implements IStiJsonReportObject, IStiRadarGridLines, IStiRadarGridLinesHor, ICloneable {
        private static implementsStiRadarGridLinesHor;
        implements(): any[];
        constructor(color?: Color, style?: StiPenStyle, visible?: boolean, allowApplyStyle?: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiPenStyle = Stimulsoft.Base.Drawing.StiPenStyle;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiRadarGridLinesVert extends StiRadarGridLines implements IStiJsonReportObject, IStiRadarGridLines, IStiRadarGridLinesVert, ICloneable {
        private static implementsStiRadarGridLinesVert;
        implements(): any[];
        constructor(color?: Color, style?: StiPenStyle, visible?: boolean, allowApplyStyle?: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiFontIcons = Stimulsoft.Report.Helpers.StiFontIcons;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiMarker implements IStiJsonReportObject, IStiMarker, ICloneable {
        private static implementsStiMarker;
        implements(): any[];
        is<T>(type: (new (...args: any[]) => T) | Stimulsoft.System.Interface<T>): this is T;
        is2<T>(type: (new (...args: any[]) => T) | Stimulsoft.System.Interface<T>): boolean;
        as<T>(type: (new (...args: any[]) => T) | Stimulsoft.System.Interface<T>): T;
        protected _hash: StiMeta[];
        meta(): StiMeta[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        get componentId(): StiComponentId;
        get propName(): string;
        clone(): StiMarker;
        core: StiMarkerCoreXF;
        showInLegend: boolean;
        visible: boolean;
        extendedVisible: StiExtendedStyleBool;
        brush: StiBrush;
        borderColor: Color;
        size: number;
        angle: number;
        type: StiMarkerType;
        icon: StiFontIcons;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiFontIcons = Stimulsoft.Report.Helpers.StiFontIcons;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiLineMarker extends StiMarker implements IStiJsonReportObject, IStiLineMarker, IStiMarker, ICloneable {
        private static implementsStiLineMarker;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        step: number;
        icon: StiFontIcons;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiRadarAxis implements IStiJsonReportObject, IStiRadarAxis, ICloneable {
        private static implementsStiRadarAxis;
        implements(): any[];
        protected _hash: StiMeta[];
        meta(): StiMeta[];
        jsonLoadFromJsonObjectArea: boolean;
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        clone(): StiRadarAxis;
        private _allowApplyStyle;
        get allowApplyStyle(): boolean;
        set allowApplyStyle(value: boolean);
        core: StiRadarAxisCoreXF;
        visible: boolean;
        area: IStiRadarArea;
        range: IStiAxisRange;
        constructor(range?: IStiAxisRange, visible?: boolean, allowApplyStyle?: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import Font = Stimulsoft.System.Drawing.Font;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiRadarAxisLabels implements IStiJsonReportObject, IStiRadarAxisLabels, ICloneable {
        private static implementsStiRadarAxisLabels;
        implements(): any[];
        protected _hash: StiMeta[];
        meta(): StiMeta[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        clone(): StiRadarAxisLabels;
        allowApplyStyle: boolean;
        core: StiRadarAxisLabelsCoreXF;
        rotationLabels: boolean;
        textBefore: string;
        textAfter: string;
        drawBorder: boolean;
        format: string;
        font: Font;
        antialiasing: boolean;
        color: Color;
        borderColor: Color;
        brush: StiBrush;
        width: number;
        wordWrap: boolean;
        constructor(format?: string, font?: Font, antialiasing?: boolean, drawBorder?: boolean, color?: Color, borderColor?: Color, brush?: StiBrush, allowApplyStyle?: boolean, rotationLabels?: boolean, width?: number, wordWrap?: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiXRadarAxis extends StiRadarAxis implements IStiXRadarAxis, IStiRadarAxis, ICloneable, IStiJsonReportObject {
        private static implementsStiXRadarAxis;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        get propName(): string;
        clone(): StiXRadarAxis;
        get xCore(): StiXRadarAxisCoreXF;
        labels: IStiRadarAxisLabels;
        constructor(labels?: IStiRadarAxisLabels, range?: IStiAxisRange, visible?: boolean, allowApplyStyle?: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiPenStyle = Stimulsoft.Base.Drawing.StiPenStyle;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiYRadarAxis extends StiRadarAxis implements IStiYRadarAxis, IStiRadarAxis, ICloneable, IStiJsonReportObject {
        private static implementsStiYRadarAxis;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        get propName(): string;
        clone(): StiYRadarAxis;
        get yCore(): StiYRadarAxisCoreXF;
        labels: IStiAxisLabels;
        ticks: IStiAxisTicks;
        lineStyle: StiPenStyle;
        lineColor: Color;
        lineWidth: number;
        info: StiAxisInfoXF;
        constructor(labels?: IStiAxisLabels, range?: IStiAxisRange, ticks?: IStiAxisTicks, lineStyle?: StiPenStyle, lineColor?: Color, lineWidth?: number, visible?: boolean, allowApplyStyle?: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiPage = Stimulsoft.Report.Components.StiPage;
    import StiComponent = Stimulsoft.Report.Components.StiComponent;
    class StiSeriesInteraction implements IStiSeriesInteraction, IStiJsonReportObject, ICloneable {
        private static implementsStiSeriesInteraction;
        implements(): any[];
        protected _hash: StiMeta[];
        meta(): StiMeta[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        get componentId(): StiComponentId;
        get propName(): string;
        getReport(): any;
        clone(): StiSeriesInteraction;
        isDefault(): boolean;
        get hyperlink(): string;
        set hyperlink(value: string);
        get tag(): string;
        set tag(value: string);
        get toolTip(): string;
        set toolTip(value: string);
        get hyperlinkDataColumn(): string;
        set hyperlinkDataColumn(value: string);
        get tagDataColumn(): string;
        set tagDataColumn(value: string);
        get toolTipDataColumn(): string;
        set toolTipDataColumn(value: string);
        get listOfHyperlinks(): string;
        set listOfHyperlinks(value: string);
        get listOfTags(): string;
        set listOfTags(value: string);
        get listOfToolTips(): string;
        set listOfToolTips(value: string);
        get allowSeries(): boolean;
        set allowSeries(value: boolean);
        get allowSeriesElements(): boolean;
        set allowSeriesElements(value: boolean);
        get drillDownEnabled(): boolean;
        set drillDownEnabled(value: boolean);
        get drillDownReport(): string;
        set drillDownReport(value: string);
        get drillDownPage(): StiPage;
        set drillDownPage(value: StiPage);
        get drillDownPageGuid(): string;
        set drillDownPageGuid(value: string);
        get parentComponent(): StiComponent;
        parentSeries: StiSeries;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import PointD = Stimulsoft.System.Drawing.Point;
    class StiSeriesPointsInfo {
        points: PointD[];
        pointsFrom: PointD[];
        pointsStart: PointD[];
        pointsEnd: PointD[];
        pointsZeroConnect: PointD[];
        pointsNullConnect: PointD[];
        pointsIds: string[];
        additionalSeriesId: string;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    class StiBoxAndWhiskerSeries extends StiSeries implements IStiBoxAndWhiskerSeries, IStiSeriesBorderThickness {
        private static implementsStiBoxAndWhiskerSeries;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        clone(): StiBoxAndWhiskerSeries;
        getDefaultAreaType(): Stimulsoft.System.Type;
        brush: StiBrush;
        allowApplyBrush: boolean;
        showInnerPoints: boolean;
        showMeanMarkers: boolean;
        borderThickness: number;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiGetValueEventArgs = Stimulsoft.Report.Events.StiGetValueEventArgs;
    import StiComponent = Stimulsoft.Report.Components.StiComponent;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiFontIcons = Stimulsoft.Report.Helpers.StiFontIcons;
    class StiBubbleSeries extends StiScatterSeries implements IStiBaseLineSeries, IStiBubbleSeries, IStiFontIconsSeries, IStiScatterSeries, IStiSeriesBorderThickness, IStiJsonReportObject, IStiSeries, ICloneable {
        private static implementsStiBubbleSeries;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        clone(): StiBubbleSeries;
        getDefaultAreaType(): Stimulsoft.System.Type;
        borderColor: Color;
        brush: StiBrush;
        private _weights;
        get weights(): number[];
        set weights(value: number[]);
        get weightsString(): string;
        set weightsString(value: string);
        weightDataColumn: string;
        getWeight: Function;
        onGetWeight(e: StiGetValueEventArgs): void;
        invokeGetWeight(sender: StiComponent, e: StiGetValueEventArgs): void;
        getListOfWeights: Function;
        onGetListOfWeights(e: StiGetValueEventArgs): void;
        invokeGetListOfWeights(sender: StiComponent, e: StiGetValueEventArgs, series: StiBubbleSeries): void;
        weight: string;
        listOfWeights: string;
        icon: StiFontIcons;
        borderThickness: number;
        private _bubbleScale;
        get bubbleScale(): number;
        set bubbleScale(value: number);
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiCornerRadius = Stimulsoft.Report.Components.IStiCornerRadius;
    import StiCornerRadius = Stimulsoft.Base.Drawing.StiCornerRadius;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiFontIcons = Stimulsoft.Report.Helpers.StiFontIcons;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiClusteredColumnSeries extends StiSeries implements IStiJsonReportObject, IStiClusteredColumnSeries, IStiFontIconsSeries, IStiSeriesBorderThickness, IStiCornerRadius, ICloneable, IStiSeries, IStiAllowApplyBrushNegative, IStiShowZerosSeries {
        private static implementsStiClusteredColumnSeries;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        clone(): StiClusteredColumnSeries;
        getDefaultAreaType(): Stimulsoft.System.Type;
        createNew(): StiSeries;
        showZeros: boolean;
        cornerRadius: StiCornerRadius;
        private _width;
        get width(): number;
        set width(value: number);
        borderColor: Color;
        brush: StiBrush;
        brushNegative: StiBrush;
        allowApplyBrushNegative: boolean;
        icon: StiFontIcons;
        borderThickness: number;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiClusteredBarSeries extends StiClusteredColumnSeries implements IStiJsonReportObject, IStiClusteredColumnSeries, IStiSeries, ICloneable, IStiClusteredBarSeries, IStiAllowApplyBrushNegative {
        private static implementsStiClusteredBarSeries;
        implements(): any[];
        get componentId(): StiComponentId;
        get xAxis(): StiSeriesXAxis;
        set xAxis(value: StiSeriesXAxis);
        getDefaultAreaType(): Stimulsoft.System.Type;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiLineSeries extends StiBaseLineSeries implements IStiJsonReportObject, IStiBaseLineSeries, IStiLineSeries, ICloneable, IStiSeries, IStiAllowApplyColorNegative {
        private static implementsStiLineSeries;
        implements(): any[];
        get componentId(): StiComponentId;
        getDefaultAreaType(): Stimulsoft.System.Type;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiAreaSeries extends StiLineSeries implements IStiLineSeries, IStiBaseLineSeries, IStiAreaSeries, IStiJsonReportObject, IStiSeries, ICloneable, IStiAllowApplyBrushNegative {
        private static implementsStiAreaSeries;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        clone(): StiAreaSeries;
        topmostLine: boolean;
        brush: StiBrush;
        brushNegative: StiBrush;
        allowApplyBrushNegative: boolean;
        getDefaultAreaType(): Stimulsoft.System.Type;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiHistogramSeries extends StiClusteredColumnSeries implements IStiHistogramSeries {
        private static implementsStiHistogramSeries;
        implements(): any[];
        get componentId(): StiComponentId;
        getDefaultAreaType(): Stimulsoft.System.Type;
        createNew(): StiSeries;
        private _width1;
        get width(): number;
        set width(value: number);
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiCornerRadius = Stimulsoft.Report.Components.IStiCornerRadius;
    import StiCornerRadius = Stimulsoft.Base.Drawing.StiCornerRadius;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiPenStyle = Stimulsoft.Base.Drawing.StiPenStyle;
    import StiFontIcons = Stimulsoft.Report.Helpers.StiFontIcons;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiParetoSeries extends StiSeries implements IStiJsonReportObject, IStiParetoSeries, IStiSeriesBorderThickness, IStiCornerRadius, IStiBaseLineSeries, IStiClusteredColumnSeries, ICloneable, IStiSeries, IStiAllowApplyBrushNegative, IStiShowNullsSeries, IStiShowZerosSeries {
        private static implementsStiParetoSeries;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        clone(): StiParetoSeries;
        getDefaultAreaType(): Stimulsoft.System.Type;
        createNew(): StiSeries;
        showZeros: boolean;
        private _width;
        get width(): number;
        set width(value: number);
        borderColor: Color;
        borderThickness: number;
        brush: StiBrush;
        brushNegative: StiBrush;
        allowApplyBrushNegative: boolean;
        showNulls: boolean;
        cornerRadius: StiCornerRadius;
        get showMarker(): boolean;
        set showMarker(value: boolean);
        get markerColor(): Color;
        set markerColor(value: Color);
        get markerSize(): number;
        set markerSize(value: number);
        get markerType(): StiMarkerType;
        set markerType(value: StiMarkerType);
        marker: IStiMarker;
        lineMarker: IStiLineMarker;
        private _lineColor;
        get lineColor(): Color;
        set lineColor(value: Color);
        getLineColor(): Color;
        setLineColor(value: Color): void;
        lineStyle: StiPenStyle;
        lighting: boolean;
        private _lineWidth;
        get lineWidth(): number;
        set lineWidth(value: number);
        labelsOffset: number;
        lineColorNegative: Color;
        allowApplyColorNegative: boolean;
        allowApplyLineColor: boolean;
        showNullsAs: StiShowEmptyCellsAs;
        showZerosAs: StiShowEmptyCellsAs;
        icon: StiFontIcons;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiSplineSeries extends StiBaseLineSeries implements IStiJsonReportObject, IStiBaseLineSeries, ICloneable, IStiSeries, IStiSplineSeries, IStiAllowApplyColorNegative {
        private static implementsStiSplineSeries;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        getDefaultAreaType(): Stimulsoft.System.Type;
        tension: number;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    class StiSplineAreaSeries extends StiSplineSeries implements IStiSplineSeries, IStiBaseLineSeries, IStiJsonReportObject, IStiSeries, ICloneable, IStiSplineAreaSeries, IStiAllowApplyColorNegative {
        private static implementsStiSplineAreaSeries;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        clone(): StiSplineAreaSeries;
        getDefaultAreaType(): Stimulsoft.System.Type;
        topmostLine: boolean;
        brush: StiBrush;
        brushNegative: StiBrush;
        allowApplyBrushNegative: boolean;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiSteppedLineSeries extends StiBaseLineSeries implements IStiJsonReportObject, IStiBaseLineSeries, IStiSeries, ICloneable, IStiSteppedLineSeries, IStiAllowApplyColorNegative {
        private static implementsStiSteppedLineSeries;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        getDefaultAreaType(): Stimulsoft.System.Type;
        pointAtCenter: boolean;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiSteppedAreaSeries extends StiSteppedLineSeries implements IStiSteppedLineSeries, IStiBaseLineSeries, IStiJsonReportObject, IStiSteppedAreaSeries, IStiSeries, ICloneable, IStiAllowApplyColorNegative {
        private static implementsStiSteppedAreaSeries;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        clone(): StiSteppedAreaSeries;
        getDefaultAreaType(): Stimulsoft.System.Type;
        topmostLine: boolean;
        brush: StiBrush;
        brushNegative: StiBrush;
        allowApplyBrushNegative: boolean;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiWaterfallSeries extends StiClusteredColumnSeries implements IStiWaterfallSeries {
        private static implementsStiWaterfallSeries;
        implements(): any[];
        meta(): StiMeta[];
        clone(): StiWaterfallSeries;
        getDefaultAreaType(): Stimulsoft.System.Type;
        get componentId(): StiComponentId;
        createNew(): StiSeries;
        connectorLine: IStiWaterfallConnectorLine;
        total: IStiWaterfallTotal;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Components.Design {
    class StiSeriesInteractionConverter {
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiGetValueEventArgs = Stimulsoft.Report.Events.StiGetValueEventArgs;
    import StiComponent = Stimulsoft.Report.Components.StiComponent;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiFontIcons = Stimulsoft.Report.Helpers.StiFontIcons;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiPieSeries extends StiSeries implements IStiPieSeries, IStiFontIconsSeries, ICloneable, IStiSeries, IStiJsonReportObject, IStiAllowApplyBorderColor, IStiSeriesBorderThickness, IStiAllowApplyBrush {
        private static implementsStiPieSeries;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        clone(): StiPieSeries;
        getDefaultAreaType(): Stimulsoft.System.Type;
        showZeros: boolean;
        allowApplyBrush: boolean;
        allowApplyBorderColor: boolean;
        getArguments(): any[];
        setArguments(value: any[]): void;
        startAngle: number;
        borderColor: Color;
        borderThickness: number;
        brush: StiBrush;
        lighting: boolean;
        private _diameter;
        get diameter(): number;
        set diameter(value: number);
        private _distance;
        get distance(): number;
        set distance(value: number);
        private _cutPieListValues;
        get cutPieListValues(): number[];
        set cutPieListValues(value: number[]);
        get cuttedPieList(): string;
        set cuttedPieList(value: string);
        cutPieList: string;
        icon: StiFontIcons;
        getCutPieList: Function;
        onGetCutPieList(e: StiGetValueEventArgs): void;
        invokeGetCutPieList(sender: StiComponent, e: StiGetValueEventArgs): void;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiDoughnutSeries extends StiPieSeries implements IStiPieSeries, IStiSeries, ICloneable, IStiDoughnutSeries, IStiJsonReportObject, IStiAllowApplyBorderColor, IStiAllowApplyBrush {
        private static implementsStiDoughnutSeries;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        getDefaultAreaType(): Stimulsoft.System.Type;
        createNew(): StiSeries;
        width: number;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiGetValueEventArgs = Stimulsoft.Report.Events.StiGetValueEventArgs;
    import StiComponent = Stimulsoft.Report.Components.StiComponent;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiCandlestickSeries extends StiSeries implements IStiJsonReportObject, IStiSeries, IStiFinancialSeries, ICloneable, IStiCandlestickSeries {
        private static implementsStiCandlestickSeries;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        clone(): StiCandlestickSeries;
        getDefaultAreaType(): Stimulsoft.System.Type;
        private _valuesOpen;
        get valuesOpen(): number[];
        set valuesOpen(value: number[]);
        private _valuesClose;
        get valuesClose(): number[];
        set valuesClose(value: number[]);
        get valuesStringOpen(): string;
        set valuesStringOpen(value: string);
        get valuesStringClose(): string;
        set valuesStringClose(value: string);
        get valuesStringHigh(): string;
        set valuesStringHigh(value: string);
        get valuesStringLow(): string;
        set valuesStringLow(value: string);
        private _valuesHigh;
        get valuesHigh(): number[];
        set valuesHigh(value: number[]);
        private _valuesLow;
        get valuesLow(): number[];
        set valuesLow(value: number[]);
        valueDataColumnOpen: string;
        valueDataColumnClose: string;
        valueDataColumnHigh: string;
        valueDataColumnLow: string;
        borderColor: Color;
        borderColorNegative: Color;
        private _borderWidth;
        get borderWidth(): number;
        set borderWidth(value: number);
        brush: StiBrush;
        brushNegative: StiBrush;
        getValueOpen: Function;
        protected onGetValueOpen(e: StiGetValueEventArgs): void;
        invokeGetValueOpen(sender: StiComponent, e: StiGetValueEventArgs): void;
        getListOfValuesOpen: Function;
        protected onGetListOfValuesOpen(e: StiGetValueEventArgs): void;
        invokeGetListOfValuesOpen(sender: StiComponent, e: StiGetValueEventArgs): void;
        getValueClose: Function;
        protected onGetValueClose(e: StiGetValueEventArgs): void;
        invokeGetValueClose(sender: StiComponent, e: StiGetValueEventArgs): void;
        getListOfValuesClose: Function;
        protected onGetListOfValuesClose(e: StiGetValueEventArgs): void;
        invokeGetListOfValuesClose(sender: StiComponent, e: StiGetValueEventArgs): void;
        getValueHigh: Function;
        protected onGetValueHigh(e: StiGetValueEventArgs): void;
        invokeGetValueHigh(sender: StiComponent, e: StiGetValueEventArgs): void;
        getListOfValuesHigh: Function;
        protected onGetListOfValuesHigh(e: StiGetValueEventArgs): void;
        invokeGetListOfValuesHigh(sender: StiComponent, e: StiGetValueEventArgs): void;
        getValueLow: Function;
        protected onGetValueLow(e: StiGetValueEventArgs): void;
        invokeGetValueLow(sender: StiComponent, e: StiGetValueEventArgs): void;
        getListOfValuesLow: Function;
        protected onGetListOfValuesLow(e: StiGetValueEventArgs): void;
        invokeGetListOfValuesLow(sender: StiComponent, e: StiGetValueEventArgs): void;
        private valueObjOpen;
        get valueOpen(): string;
        set valueOpen(value: string);
        listOfValuesOpen: string;
        private valueObjClose;
        get valueClose(): string;
        set valueClose(value: string);
        listOfValuesClose: string;
        private valueObjHigh;
        get valueHigh(): string;
        set valueHigh(value: string);
        listOfValuesHigh: string;
        private valueObjLow;
        get valueLow(): string;
        set valueLow(value: string);
        listOfValuesLow: string;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiPenStyle = Stimulsoft.Base.Drawing.StiPenStyle;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiStockSeries extends StiCandlestickSeries implements IStiJsonReportObject, IStiStockSeries, IStiFinancialSeries, ICloneable, IStiSeries, IStiAllowApplyColorNegative {
        private static implementsStiStockSeries;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        clone(): StiStockSeries;
        getDefaultAreaType(): Stimulsoft.System.Type;
        lineColor: Color;
        lineStyle: StiPenStyle;
        private _lineWidth;
        get lineWidth(): number;
        set lineWidth(value: number);
        lineColorNegative: Color;
        allowApplyColorNegative: boolean;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiFontIconsSeries = Stimulsoft.Report.Chart.IStiFontIconsSeries;
    import IStiCornerRadius = Stimulsoft.Report.Components.IStiCornerRadius;
    import StiCornerRadius = Stimulsoft.Base.Drawing.StiCornerRadius;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiFontIcons = Stimulsoft.Report.Helpers.StiFontIcons;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiStackedBarSeries extends StiSeries implements IStiJsonReportObject, IStiStackedBarSeries, IStiSeriesBorderThickness, IStiFontIconsSeries, IStiCornerRadius, ICloneable, IStiSeries, IStiAllowApplyBrushNegative, IStiShowZerosSeries {
        private static implementsStiStackedBarSeries;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        clone(): StiStackedBarSeries;
        getDefaultAreaType(): Stimulsoft.System.Type;
        showZeros: boolean;
        private _width;
        get width(): number;
        set width(value: number);
        borderColor: Color;
        borderThickness: number;
        brush: StiBrush;
        brushNegative: StiBrush;
        allowApplyBrushNegative: boolean;
        cornerRadius: StiCornerRadius;
        get xAxis(): StiSeriesXAxis;
        set xAxis(value: StiSeriesXAxis);
        createNew(): StiSeries;
        icon: StiFontIcons;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiFullStackedBarSeries extends StiStackedBarSeries implements IStiJsonReportObject, IStiStackedBarSeries, IStiFontIconsSeries, ICloneable, IStiSeries, IStiFullStackedBarSeries {
        private static implementsStiFullStackedBarSeries;
        implements(): any[];
        get componentId(): StiComponentId;
        getDefaultAreaType(): Stimulsoft.System.Type;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiPenStyle = Stimulsoft.Base.Drawing.StiPenStyle;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiStackedBaseLineSeries extends StiSeries implements IStiJsonReportObject, IStiStackedBaseLineSeries, ICloneable, IStiSeries {
        private static implementsStiStackedBaseLineSeries;
        implements(): any[];
        meta(): StiMeta[];
        clone(): StiStackedBaseLineSeries;
        showNulls: boolean;
        get showMarker(): boolean;
        set showMarker(value: boolean);
        get markerColor(): Color;
        set markerColor(value: Color);
        get markerSize(): number;
        set markerSize(value: number);
        get markerType(): StiMarkerType;
        set markerType(value: StiMarkerType);
        marker: IStiMarker;
        lineMarker: IStiLineMarker;
        lighting: boolean;
        lineColor: Color;
        private _lineWidth;
        get lineWidth(): number;
        set lineWidth(value: number);
        lineStyle: StiPenStyle;
        lineColorNegative: Color;
        allowApplyColorNegative: boolean;
        getDefaultAreaType(): Stimulsoft.System.Type;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiStackedLineSeries extends StiStackedBaseLineSeries implements IStiJsonReportObject, IStiStackedBaseLineSeries, IStiStackedLineSeries, IStiSeries, ICloneable, IStiShowNullsSeries {
        private static implementsStiStackedLineSeries;
        implements(): any[];
        get componentId(): StiComponentId;
        getDefaultAreaType(): Stimulsoft.System.Type;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiStackedAreaSeries extends StiStackedLineSeries implements ICloneable, IStiStackedBaseLineSeries, IStiStackedLineSeries, IStiJsonReportObject, IStiSeries, IStiStackedAreaSeries, IStiAllowApplyBrushNegative {
        private static implementsStiStackedAreaSeries;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        clone(): StiStackedAreaSeries;
        getDefaultAreaType(): Stimulsoft.System.Type;
        get coreBrush(): StiBrush;
        set coreBrush(value: StiBrush);
        brush: StiBrush;
        brushNegative: StiBrush;
        allowApplyBrushNegative: boolean;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiFullStackedAreaSeries extends StiStackedAreaSeries implements IStiStackedAreaSeries, IStiStackedBaseLineSeries, IStiSeries, IStiJsonReportObject, IStiFullStackedAreaSeries, IStiStackedLineSeries, ICloneable, IStiAllowApplyBrushNegative {
        private static implementsStiFullStackedAreaSeries;
        implements(): any[];
        get componentId(): StiComponentId;
        getDefaultAreaType(): Stimulsoft.System.Type;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiCornerRadius = Stimulsoft.Report.Components.IStiCornerRadius;
    import StiCornerRadius = Stimulsoft.Base.Drawing.StiCornerRadius;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiFontIcons = Stimulsoft.Report.Helpers.StiFontIcons;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiStackedColumnSeries extends StiSeries implements IStiJsonReportObject, IStiStackedColumnSeries, IStiFontIconsSeries, ICloneable, IStiSeries, IStiAllowApplyBrushNegative, IStiCornerRadius, IStiSeriesBorderThickness, IStiShowZerosSeries {
        private static implementsStiStackedColumnSeries;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        clone(): StiStackedColumnSeries;
        showZeros: boolean;
        private _width;
        get width(): number;
        set width(value: number);
        borderColor: Color;
        borderThickness: number;
        brush: StiBrush;
        brushNegative: StiBrush;
        allowApplyBrushNegative: boolean;
        icon: StiFontIcons;
        cornerRadius: StiCornerRadius;
        getDefaultAreaType(): Stimulsoft.System.Type;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiFullStackedColumnSeries extends StiStackedColumnSeries implements IStiFullStackedColumnSeries, IStiStackedColumnSeries, ICloneable, IStiSeries, IStiJsonReportObject, IStiAllowApplyBrushNegative {
        private static implementsStiFullStackedColumnSeries;
        implements(): any[];
        get componentId(): StiComponentId;
        getDefaultAreaType(): Stimulsoft.System.Type;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiFullStackedLineSeries extends StiStackedLineSeries implements IStiJsonReportObject, IStiStackedBaseLineSeries, IStiStackedLineSeries, IStiSeries, ICloneable {
        private static implementsStiFullStackedLineSeries;
        implements(): any[];
        get componentId(): StiComponentId;
        getDefaultAreaType(): Stimulsoft.System.Type;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiStackedSplineSeries extends StiStackedBaseLineSeries implements IStiJsonReportObject, IStiStackedBaseLineSeries, ICloneable, IStiSeries, IStiStackedSplineSeries, IStiAllowApplyColorNegative {
        private static implementsStiStackedSplineSeries;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        tension: number;
        getDefaultAreaType(): Stimulsoft.System.Type;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiStackedSplineAreaSeries extends StiStackedSplineSeries implements IStiStackedSplineSeries, IStiStackedBaseLineSeries, IStiStackedSplineAreaSeries, IStiJsonReportObject, IStiSeries, ICloneable, IStiAllowApplyBrushNegative {
        private static implementsStiStackedSplineAreaSeries;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        clone(): StiStackedSplineAreaSeries;
        brush: StiBrush;
        brushNegative: StiBrush;
        allowApplyBrushNegative: boolean;
        getDefaultAreaType(): Stimulsoft.System.Type;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiFullStackedSplineAreaSeries extends StiStackedSplineAreaSeries implements IStiStackedSplineSeries, IStiFullStackedSplineAreaSeries, IStiStackedBaseLineSeries, IStiStackedSplineAreaSeries, IStiJsonReportObject, IStiSeries, ICloneable, IStiAllowApplyBrushNegative {
        private static implementsStiFullStackedSplineAreaSeries;
        implements(): any[];
        get componentId(): StiComponentId;
        getDefaultAreaType(): Stimulsoft.System.Type;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiFullStackedSplineSeries extends StiStackedSplineSeries implements IStiStackedSplineSeries, IStiStackedBaseLineSeries, IStiFullStackedSplineSeries, IStiJsonReportObject, IStiSeries, ICloneable, IStiAllowApplyColorNegative {
        private static implementsStiFullStackedSplineSeries;
        implements(): any[];
        get componentId(): StiComponentId;
        getDefaultAreaType(): Stimulsoft.System.Type;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiFontIcons = Stimulsoft.Report.Helpers.StiFontIcons;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiFunnelSeries extends StiSeries implements IStiJsonReportObject, IStiFunnelSeries, IStiFontIconsSeries, IStiSeriesBorderThickness, IStiSeries, ICloneable, IStiShowZerosSeries {
        private static implementsStiFunnelSeries;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        clone(): StiFunnelSeries;
        getDefaultAreaType(): Stimulsoft.System.Type;
        showZeros: boolean;
        allowApplyBrush: boolean;
        allowApplyBorderColor: boolean;
        brush: StiBrush;
        borderColor: Color;
        borderThickness: number;
        icon: StiFontIcons;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiFunnelWeightedSlicesSeries extends StiFunnelSeries implements IStiJsonReportObject, IStiFunnelSeries, IStiFunnelWeightedSlicesSeries, IStiSeries, ICloneable {
        private static implementsStiFunnelWeightedSlicesSeries;
        implements(): any[];
        get componentId(): StiComponentId;
        clone(): StiFunnelWeightedSlicesSeries;
        getDefaultAreaType(): Stimulsoft.System.Type;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiGetValueEventArgs = Stimulsoft.Report.Events.StiGetValueEventArgs;
    import StiComponent = Stimulsoft.Report.Components.StiComponent;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiGanttSeries extends StiClusteredBarSeries implements IStiClusteredColumnSeries, IStiClusteredBarSeries, IStiRangeSeries, IStiJsonReportObject, IStiSeries, IStiGanttSeries, ICloneable, IStiAllowApplyBrushNegative {
        private static implementsStiGanttSeries;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        clone(): StiGanttSeries;
        getDefaultAreaType(): Stimulsoft.System.Type;
        private _valuesEnd;
        get valuesEnd(): number[];
        set valuesEnd(value: number[]);
        get valuesStringEnd(): string;
        set valuesStringEnd(value: string);
        valueDataColumnEnd: string;
        getValueEnd: Function;
        protected onGetValueEnd(e: StiGetValueEventArgs): void;
        invokeGetValueEnd(sender: StiComponent, e: StiGetValueEventArgs): void;
        getListOfValuesEnd: Function;
        protected onGetListOfValuesEnd(e: StiGetValueEventArgs): void;
        invokeGetListOfValuesEnd(sender: StiComponent, e: StiGetValueEventArgs, series: StiGanttSeries): void;
        private valueObjEnd;
        get valueEnd(): string;
        set valueEnd(value: string);
        listOfValuesEnd: string;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiFontIcons = Stimulsoft.Report.Helpers.StiFontIcons;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiPictorialSeries extends StiSeries implements IStiPictorialSeries, IStiFontIconsSeries, ICloneable, IStiSeries, IStiJsonReportObject {
        private static implementsStiPictorialSeries;
        implements(): any[];
        meta(): StiMeta[];
        brush: StiBrush;
        icon: StiFontIcons;
        get componentId(): StiComponentId;
        clone(): StiPictorialSeries;
        getDefaultAreaType(): Stimulsoft.System.Type;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiPictorialStackedSeries = Stimulsoft.Report.Chart.IStiPictorialStackedSeries;
    import StiFontIcons = Stimulsoft.Report.Helpers.StiFontIcons;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiPictorialStackedSeries extends StiSeries implements IStiFontIconsSeries, ICloneable, IStiSeries, IStiPictorialStackedSeries, IStiJsonReportObject {
        private static implementsStiPictorialStackedSeries;
        implements(): any[];
        meta(): StiMeta[];
        brush: StiBrush;
        icon: StiFontIcons;
        get componentId(): StiComponentId;
        clone(): StiPictorialStackedSeries;
        getDefaultAreaType(): Stimulsoft.System.Type;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Point = Stimulsoft.System.Drawing.Point;
    class StiPie3dPeripherySurfaceBounds {
        startAngle: number;
        endAngle: number;
        startPoint: Point;
        endPoint: Point;
        realStartAngle: number;
        realEndAngle: number;
        constructor(startAngle: number, endAngle: number, startPoint: Point, endPoint: Point, realStartAngle: number, realEndAngle: number);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiContext = Stimulsoft.Base.Context.StiContext;
    import StiPenGeom = Stimulsoft.Base.Context.StiPenGeom;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Point = Stimulsoft.System.Drawing.Point;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import StiAnimation = Stimulsoft.Base.Context.Animation.StiAnimation;
    class StiPie3dQuadrilateral {
        animation: StiAnimation;
        rectangle: Rectangle;
        point1: Point;
        point2: Point;
        point3: Point;
        point4: Point;
        toClose: boolean;
        draw(context: StiContext, pen: StiPenGeom, brush: StiBrush): void;
        static empty(): StiPie3dQuadrilateral;
        constructor(rectangle: Rectangle, point1: Point, point2: Point, point3: Point, point4: Point, toClose: boolean, animation: StiAnimation);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiPie3dSeries extends StiPieSeries implements IStiPie3dSeries {
        private static implementsStiPie3dSeries;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        clone(): StiPieSeries;
        getDefaultAreaType(): Stimulsoft.System.Type;
        createNew(): StiSeries;
        options3D: StiPie3dOptions;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiRadarSeries extends StiSeries implements IStiJsonReportObject, ICloneable, IStiSeries, IStiRadarSeries, IStiShowNullsSeries {
        private static implementsStiRadarSeries;
        implements(): any[];
        meta(): StiMeta[];
        clone(): StiRadarSeries;
        showNulls: boolean;
        marker: IStiMarker;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiPenStyle = Stimulsoft.Base.Drawing.StiPenStyle;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiRadarAreaSeries extends StiRadarSeries implements IStiRadarSeries, IStiRadarLineSeries, IStiJsonReportObject, IStiSeries, IStiRadarAreaSeries, ICloneable {
        private static implementsStiRadarAreaSeries;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        lineColor: Color;
        lineStyle: StiPenStyle;
        lighting: boolean;
        private _lineWidth;
        get lineWidth(): number;
        set lineWidth(value: number);
        brush: StiBrush;
        getDefaultAreaType(): Stimulsoft.System.Type;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiPenStyle = Stimulsoft.Base.Drawing.StiPenStyle;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiRadarLineSeries extends StiRadarSeries implements IStiJsonReportObject, IStiRadarLineSeries, ICloneable, IStiSeries, IStiRadarSeries {
        private static implementsStiRadarLineSeries;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        lineColor: Color;
        lineStyle: StiPenStyle;
        lighting: boolean;
        private _lineWidth;
        get lineWidth(): number;
        set lineWidth(value: number);
        getDefaultAreaType(): Stimulsoft.System.Type;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiRadarPointSeries extends StiRadarSeries implements IStiJsonReportObject, IStiRadarPointSeries, ICloneable, IStiSeries, IStiRadarSeries {
        private static implementsStiRadarPointSeries;
        implements(): any[];
        get componentId(): StiComponentId;
        getDefaultAreaType(): Stimulsoft.System.Type;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiGetValueEventArgs = Stimulsoft.Report.Events.StiGetValueEventArgs;
    import StiComponent = Stimulsoft.Report.Components.StiComponent;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiRangeBarSeries extends StiClusteredColumnSeries implements IStiRangeBarSeries, IStiClusteredColumnSeries, IStiRangeSeries, IStiJsonReportObject, IStiSeries, ICloneable, IStiAllowApplyBrushNegative {
        private static implementsStiRangeBarSeries;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        clone(): StiRangeBarSeries;
        getDefaultAreaType(): Stimulsoft.System.Type;
        private _valuesEnd;
        get valuesEnd(): number[];
        set valuesEnd(value: number[]);
        get valuesStringEnd(): string;
        set valuesStringEnd(value: string);
        valueDataColumnEnd: string;
        getValueEnd: Function;
        protected onGetValueEnd(e: StiGetValueEventArgs): void;
        invokeGetValueEnd(sender: StiComponent, e: StiGetValueEventArgs): void;
        getListOfValuesEnd: Function;
        protected onGetListOfValuesEnd(e: StiGetValueEventArgs): void;
        invokeGetListOfValuesEnd(sender: StiComponent, e: StiGetValueEventArgs, series: StiRangeBarSeries): void;
        private valueObjEnd;
        get valueEnd(): string;
        set valueEnd(value: string);
        listOfValuesEnd: string;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiGetValueEventArgs = Stimulsoft.Report.Events.StiGetValueEventArgs;
    import StiComponent = Stimulsoft.Report.Components.StiComponent;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiRangeSeries extends StiLineSeries implements IStiLineSeries, IStiLineRangeSeries, IStiBaseLineSeries, IStiRangeSeries, IStiJsonReportObject, IStiSeries, ICloneable, IStiAllowApplyColorNegative {
        private static implementsStiRangeSeries;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        clone(): StiRangeSeries;
        getDefaultAreaType(): Stimulsoft.System.Type;
        brush: StiBrush;
        private _valuesEnd;
        get valuesEnd(): number[];
        set valuesEnd(value: number[]);
        get valuesStringEnd(): string;
        set valuesStringEnd(value: string);
        valueDataColumnEnd: string;
        brushNegative: StiBrush;
        allowApplyBrushNegative: boolean;
        getValueEnd: Function;
        onGetValueEnd(e: StiGetValueEventArgs): void;
        invokeGetValueEnd(sender: StiComponent, e: StiGetValueEventArgs): void;
        getListOfValuesEnd: Function;
        onGetListOfValuesEnd(e: StiGetValueEventArgs): void;
        invokeGetListOfValuesEnd(sender: StiComponent, e: StiGetValueEventArgs, series: StiRangeSeries): void;
        private valueObjEnd;
        get valueEnd(): string;
        set valueEnd(value: string);
        listOfValuesEnd: string;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiGetValueEventArgs = Stimulsoft.Report.Events.StiGetValueEventArgs;
    import StiComponent = Stimulsoft.Report.Components.StiComponent;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiSplineRangeSeries extends StiSplineSeries implements IStiSplineSeries, IStiSplineRangeSeries, IStiBaseLineSeries, IStiRangeSeries, IStiJsonReportObject, IStiSeries, ICloneable, IStiAllowApplyColorNegative {
        private static implementsStiSplineRangeSeries;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        clone(): StiSplineRangeSeries;
        getDefaultAreaType(): Stimulsoft.System.Type;
        brush: StiBrush;
        private _valuesEnd;
        get valuesEnd(): number[];
        set valuesEnd(value: number[]);
        get valuesStringEnd(): string;
        set valuesStringEnd(value: string);
        valueDataColumnEnd: string;
        getValueEnd: Function;
        onGetValueEnd(e: StiGetValueEventArgs): void;
        invokeGetValueEnd(sender: StiComponent, e: StiGetValueEventArgs): void;
        getListOfValuesEnd: Function;
        onGetListOfValuesEnd(e: StiGetValueEventArgs): void;
        invokeGetListOfValuesEnd(sender: StiComponent, e: StiGetValueEventArgs, series: StiSplineRangeSeries): void;
        private valueObjEnd;
        get valueEnd(): string;
        set valueEnd(value: string);
        listOfValuesEnd: string;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiGetValueEventArgs = Stimulsoft.Report.Events.StiGetValueEventArgs;
    import StiComponent = Stimulsoft.Report.Components.StiComponent;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiSteppedRangeSeries extends StiSteppedLineSeries implements IStiSteppedLineSeries, IStiBaseLineSeries, IStiRangeSeries, IStiSteppedRangeSeries, IStiJsonReportObject, IStiSeries, ICloneable, IStiAllowApplyColorNegative {
        private static implementsStiSteppedRangeSeries;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        clone(): StiSteppedRangeSeries;
        getDefaultAreaType(): Stimulsoft.System.Type;
        brush: StiBrush;
        private _valuesEnd;
        get valuesEnd(): number[];
        set valuesEnd(value: number[]);
        get valuesStringEnd(): string;
        set valuesStringEnd(value: string);
        valueDataColumnEnd: string;
        brushNegative: StiBrush;
        allowApplyBrushNegative: boolean;
        getValueEnd: Function;
        onGetValueEnd(e: StiGetValueEventArgs): void;
        invokeGetValueEnd(sender: StiComponent, e: StiGetValueEventArgs): void;
        getListOfValuesEnd: Function;
        onGetListOfValuesEnd(e: StiGetValueEventArgs): void;
        invokeGetListOfValuesEnd(sender: StiComponent, e: StiGetValueEventArgs, series: StiSteppedRangeSeries): void;
        private valueObjEnd;
        get valueEnd(): string;
        set valueEnd(value: string);
        listOfValuesEnd: string;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiScatterSplineSeries extends StiScatterSeries implements ICloneable, IStiScatterLineSeries, IStiBaseLineSeries, IStiScatterSplineSeries, IStiJsonReportObject, IStiSeries, IStiScatterSeries, IStiAllowApplyColorNegative {
        private static implementsStiScatterSplineSeries;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        clone(): StiScatterSplineSeries;
        getDefaultAreaType(): Stimulsoft.System.Type;
        tension: number;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiCornerRadius = Stimulsoft.Report.Components.IStiCornerRadius;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import StiCornerRadius = Stimulsoft.Base.Drawing.StiCornerRadius;
    class StiRibbonSeries extends StiSeries implements IStiJsonReportObject, IStiRibbonSeries, IStiCornerRadius, IStiSeriesBorderThickness {
        private static implementsStiRibbonSeries;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        clone(): StiRibbonSeries;
        private _width;
        get width(): number;
        set width(value: number);
        borderColor: Color;
        borderThickness: number;
        brush: StiBrush;
        cornerRadius: StiCornerRadius;
        getDefaultAreaType(): Stimulsoft.System.Type;
        createNew(): StiSeries;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import List = Stimulsoft.System.Collections.List;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    class StiRibbonSeriesMetadata {
        borderColor: Color;
        brush: StiBrush;
        rectangles: List<Rectangle>;
        constructor(brush: StiBrush, borderColor: Color);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiSunburstSeries extends StiSeries implements IStiJsonReportObject, IStiSunburstSeries, IStiSeriesBorderThickness, ICloneable, IStiSeries {
        private static implementsStiSunburstSeries;
        implements(): any[];
        meta(): StiMeta[];
        clone(): StiSunburstSeries;
        getDefaultAreaType(): Stimulsoft.System.Type;
        get componentId(): StiComponentId;
        createNew(): StiSeries;
        borderColor: Color;
        borderThickness: number;
        brush: StiBrush;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiCornerRadius = Stimulsoft.Report.Components.IStiCornerRadius;
    import StiCornerRadius = Stimulsoft.Base.Drawing.StiCornerRadius;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiFontIcons = Stimulsoft.Report.Helpers.StiFontIcons;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiTreemapSeries extends StiSeries implements IStiJsonReportObject, IStiTreemapSeries, IStiSeriesBorderThickness, IStiCornerRadius, IStiFontIconsSeries, ICloneable, IStiSeries {
        private static implementsStiTreemapSeries;
        implements(): any[];
        meta(): StiMeta[];
        clone(): StiTreemapSeries;
        getDefaultAreaType(): Stimulsoft.System.Type;
        get componentId(): StiComponentId;
        createNew(): StiSeries;
        borderColor: Color;
        borderThickness: number;
        cornerRadius: StiCornerRadius;
        brush: StiBrush;
        icon: StiFontIcons;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiNoneLabels extends StiSeriesLabels implements IStiNoneLabels, IStiSeriesLabels, ICloneable, IStiJsonReportObject {
        private static implementsStiNoneLabels;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        createNew(): StiSeriesLabels;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiInsideBaseAxisLabels extends StiCenterAxisLabels implements IStiInsideBaseAxisLabels, IStiSeriesLabels, ICloneable, IStiAxisSeriesLabels, IStiJsonReportObject {
        private static implementsStiInsideBaseAxisLabels;
        implements(): any[];
        get componentId(): StiComponentId;
        createNew(): StiSeriesLabels;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiInsideEndAxisLabels extends StiCenterAxisLabels implements IStiCenterAxisLabels, IStiAxisSeriesLabels, IStiSeriesLabels, IStiJsonReportObject, IStiInsideEndAxisLabels, ICloneable {
        private static implementsStiInsideEndAxisLabels;
        implements(): any[];
        get componentId(): StiComponentId;
        createNew(): StiSeriesLabels;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiLeftAxisLabels extends StiCenterAxisLabels implements IStiCenterAxisLabels, IStiLeftAxisLabels, IStiAxisSeriesLabels, IStiJsonReportObject, IStiSeriesLabels, ICloneable {
        private static implementsStiLeftAxisLabels;
        implements(): any[];
        get componentId(): StiComponentId;
        createNew(): StiSeriesLabels;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiOutsideAxisLabels extends StiAxisSeriesLabels implements IStiOutsideAxisLabels, IStiSeriesLabels, ICloneable, IStiAxisSeriesLabels, IStiJsonReportObject {
        private static implementsStiOutsideAxisLabels;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        lineLength: number;
        createNew(): StiSeriesLabels;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiOutsideBaseAxisLabels extends StiCenterAxisLabels implements IStiCenterAxisLabels, IStiAxisSeriesLabels, IStiJsonReportObject, IStiOutsideBaseAxisLabels, IStiSeriesLabels, ICloneable {
        private static implementsStiOutsideBaseAxisLabels;
        implements(): any[];
        get componentId(): StiComponentId;
        createNew(): StiSeriesLabels;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiOutsideEndAxisLabels extends StiCenterAxisLabels implements IStiOutsideEndAxisLabels, IStiCenterAxisLabels, IStiAxisSeriesLabels, IStiJsonReportObject, IStiSeriesLabels, ICloneable {
        private static implementsStiOutsideEndAxisLabels;
        implements(): any[];
        get componentId(): StiComponentId;
        createNew(): StiSeriesLabels;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiRightAxisLabels extends StiCenterAxisLabels implements IStiCenterAxisLabels, IStiAxisSeriesLabels, IStiRightAxisLabels, IStiJsonReportObject, IStiSeriesLabels, ICloneable {
        private static implementsStiRightAxisLabels;
        implements(): any[];
        get componentId(): StiComponentId;
        createNew(): StiSeriesLabels;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiValueAxisLabels extends StiCenterAxisLabels implements IStiValueAxisLabels, IStiCenterAxisLabels, IStiAxisSeriesLabels, IStiJsonReportObject, IStiSeriesLabels, ICloneable {
        private static implementsStiValueAxisLabels;
        implements(): any[];
        get componentId(): StiComponentId;
        createNew(): StiSeriesLabels;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiFunnelSeriesLabels extends StiSeriesLabels implements IStiJsonReportObject, IStiFunnelSeriesLabels, ICloneable, IStiSeriesLabels {
        private static implementsStiFunnelSeriesLabels;
        implements(): any[];
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiCenterFunnelLabels extends StiFunnelSeriesLabels implements IStiJsonReportObject, IStiSeriesLabels, IStiFunnelSeriesLabels, ICloneable, IStiCenterFunnelLabels {
        private static implementsStiCenterFunnelLabels;
        implements(): any[];
        get componentId(): StiComponentId;
        createNew(): StiSeriesLabels;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiOutsideLeftFunnelLabels extends StiFunnelSeriesLabels implements IStiCenterFunnelLabels, IStiOutsideLeftFunnelLabels, IStiJsonReportObject, IStiSeriesLabels, IStiFunnelSeriesLabels, ICloneable {
        private static implementsStiOutsideLeftFunnelLabels;
        implements(): any[];
        get componentId(): StiComponentId;
        createNew(): StiSeriesLabels;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiOutsideRightFunnelLabels extends StiFunnelSeriesLabels implements IStiOutsideRightFunnelLabels, IStiCenterFunnelLabels, IStiJsonReportObject, IStiSeriesLabels, IStiFunnelSeriesLabels, ICloneable {
        private static implementsStiOutsideRightFunnelLabels;
        implements(): any[];
        get componentId(): StiComponentId;
        createNew(): StiSeriesLabels;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiCenterPictorialStackedLabels extends StiFunnelSeriesLabels implements IStiJsonReportObject, IStiSeriesLabels, ICloneable {
        private static implementsStiCenterPictorialStackedLabels;
        implements(): any[];
        get componentId(): StiComponentId;
        createNew(): StiSeriesLabels;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import Color = Stimulsoft.System.Drawing.Color;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiOutsideLeftPictorialStackedLabels extends StiCenterPictorialStackedLabels implements IStiJsonReportObject, IStiSeriesLabels, ICloneable {
        private static implementsStiOutsideLeftFunnelLabels;
        implements(): any[];
        meta(): StiMeta[];
        lineColor: Color;
        get componentId(): StiComponentId;
        createNew(): StiSeriesLabels;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiOutsideRightPictorialStackedLabels extends StiCenterPictorialStackedLabels implements IStiJsonReportObject, IStiSeriesLabels, ICloneable {
        private static implementsStiOutsideRightPictorialStackedLabels;
        implements(): any[];
        meta(): StiMeta[];
        lineColor: Color;
        get componentId(): StiComponentId;
        createNew(): StiSeriesLabels;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiPictorialStackedLabels extends StiSeriesLabels implements IStiJsonReportObject, ICloneable, IStiSeriesLabels {
        private static implementsStiPictorialStackedLabels;
        implements(): any[];
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiPieSeriesLabels extends StiSeriesLabels implements IStiJsonReportObject, IStiPieSeriesLabels, IStiSeriesLabels, ICloneable {
        private static implementsStiPieSeriesLabels;
        implements(): any[];
        meta(): StiMeta[];
        private _showInPercent;
        get showInPercent(): boolean;
        set showInPercent(value: boolean);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiCenterPie3dLabels extends StiPieSeriesLabels {
        get componentId(): StiComponentId;
        createNew(): StiSeriesLabels;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiCenterPieLabels extends StiPieSeriesLabels implements IStiJsonReportObject, IStiPieSeriesLabels, IStiSeriesLabels, IStiCenterPieLabels, ICloneable {
        private static implementsStiCenterPieLabels;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        autoRotate: boolean;
        createNew(): StiSeriesLabels;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiInsideEndPieLabels extends StiCenterPieLabels implements IStiCenterPieLabels, IStiSeriesLabels, IStiPieSeriesLabels, IStiInsideEndPieLabels, IStiJsonReportObject, ICloneable {
        private static implementsStiInsideEndPieLabels;
        implements(): any[];
        get componentId(): StiComponentId;
        createNew(): StiSeriesLabels;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiOutsidePieLabels extends StiCenterPieLabels implements IStiOutsidePieLabels, IStiCenterPieLabels, IStiPieSeriesLabels, IStiSeriesLabels, IStiJsonReportObject, ICloneable {
        private static implementsStiOutsidePieLabels;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        showValue: boolean;
        lineLength: number;
        lineColor: Color;
        createNew(): StiSeriesLabels;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiTwoColumnsPieLabels extends StiOutsidePieLabels implements IStiTwoColumnsPieLabels, IStiOutsidePieLabels, IStiCenterPieLabels, IStiPieSeriesLabels, IStiSeriesLabels, IStiJsonReportObject, ICloneable {
        private static implementsStiTwoColumnsPieLabels;
        implements(): any[];
        meta(): StiMeta[];
        get componentId(): StiComponentId;
        createNew(): StiSeriesLabels;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiCenterTreemapLabels extends StiAxisSeriesLabels implements IStiCenterAxisLabels {
        get componentId(): StiComponentId;
        createNew(): StiSeriesLabels;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiService = Stimulsoft.Base.Services.StiService;
    import Font = Stimulsoft.System.Drawing.Font;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiStrips extends StiService implements IStiJsonReportObject, IStiStrips, ICloneable {
        private static implementsStiStrips;
        implements(): any[];
        meta(): StiMeta[];
        saveToJsonObject(m: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        get componentId(): StiComponentId;
        get propName(): string;
        clone(): StiStrips;
        get serviceCategory(): string;
        get serviceType(): Stimulsoft.System.Type;
        core: StiStripsCoreXF;
        private _allowApplyStyle;
        get allowApplyStyle(): boolean;
        set allowApplyStyle(value: boolean);
        showBehind: boolean;
        stripBrush: StiBrush;
        antialiasing: boolean;
        font: Font;
        text: string;
        titleVisible: boolean;
        titleColor: Color;
        orientation: StiStrips_StiOrientation;
        showInLegend: boolean;
        maxValue: string;
        minValue: string;
        visible: boolean;
        chart: IStiChart;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiStyle01 extends StiChartStyle {
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiCustomStyle extends StiStyle01 implements IStiCustomStyle {
        private static implementsStiCustomStyle;
        implements(): any[];
        get serviceName(): string;
        get customCore(): StiCustomStyleCoreXF;
        constructor(reportStyleName?: string);
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiStyle02 extends StiChartStyle {
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiStyle03 extends StiChartStyle {
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiStyle04 extends StiChartStyle {
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiStyle05 extends StiChartStyle {
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiStyle06 extends StiChartStyle {
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiStyle07 extends StiChartStyle {
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiStyle08 extends StiStyle03 {
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiStyle09 extends StiChartStyle {
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiStyle10 extends StiChartStyle {
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiStyle11 extends StiChartStyle {
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiStyle12 extends StiChartStyle {
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiStyle13 extends StiChartStyle {
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiStyle14 extends StiChartStyle {
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiStyle15 extends StiChartStyle {
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiStyle16 extends StiChartStyle {
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiStyle17 extends StiChartStyle {
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiStyle18 extends StiChartStyle {
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiStyle19 extends StiChartStyle {
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiStyle20 extends StiChartStyle {
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiStyle21 extends StiChartStyle {
        isOffice2015Style: boolean;
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiStyle22 extends StiChartStyle {
        isOffice2015Style: boolean;
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    class StiStyle23 extends StiChartStyle {
        isOffice2015Style: boolean;
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiElementStyleIdent = Stimulsoft.Report.Dashboard.StiElementStyleIdent;
    class StiStyle24 extends StiChartStyle {
        allowDashboard: boolean;
        get dashboardName(): string;
        styleIdent: StiElementStyleIdent;
        isOffice2015Style: boolean;
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiElementStyleIdent = Stimulsoft.Report.Dashboard.StiElementStyleIdent;
    class StiStyle26 extends StiChartStyle {
        allowDashboard: boolean;
        get dashboardName(): string;
        styleIdent: StiElementStyleIdent;
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiElementStyleIdent = Stimulsoft.Report.Dashboard.StiElementStyleIdent;
    class StiStyle27 extends StiChartStyle {
        allowDashboard: boolean;
        get dashboardName(): string;
        styleIdent: StiElementStyleIdent;
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiElementStyleIdent = Stimulsoft.Report.Dashboard.StiElementStyleIdent;
    class StiStyle28 extends StiChartStyle {
        allowDashboard: boolean;
        get dashboardName(): string;
        styleIdent: StiElementStyleIdent;
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiElementStyleIdent = Stimulsoft.Report.Dashboard.StiElementStyleIdent;
    class StiStyle30 extends StiChartStyle {
        allowDashboard: boolean;
        get dashboardName(): string;
        styleIdent: StiElementStyleIdent;
        isOffice2015Style: boolean;
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiElementStyleIdent = Stimulsoft.Report.Dashboard.StiElementStyleIdent;
    class StiStyle31 extends StiChartStyle {
        allowDashboard: boolean;
        get dashboardName(): string;
        styleIdent: StiElementStyleIdent;
        isOffice2015Style: boolean;
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiElementStyleIdent = Stimulsoft.Report.Dashboard.StiElementStyleIdent;
    class StiStyle32 extends StiChartStyle {
        allowDashboard: boolean;
        get dashboardName(): string;
        styleIdent: StiElementStyleIdent;
        isOffice2015Style: boolean;
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiElementStyleIdent = Stimulsoft.Report.Dashboard.StiElementStyleIdent;
    class StiStyle33 extends StiChartStyle {
        allowDashboard: boolean;
        get dashboardName(): string;
        styleIdent: StiElementStyleIdent;
        isOffice2015Style: boolean;
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiElementStyleIdent = Stimulsoft.Report.Dashboard.StiElementStyleIdent;
    class StiStyle34 extends StiChartStyle {
        allowDashboard: boolean;
        get dashboardName(): string;
        styleIdent: StiElementStyleIdent;
        isOffice2015Style: boolean;
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiElementStyleIdent = Stimulsoft.Report.Dashboard.StiElementStyleIdent;
    class StiStyle35 extends StiChartStyle {
        allowDashboard: boolean;
        get dashboardName(): string;
        styleIdent: StiElementStyleIdent;
        isOffice2015Style: boolean;
        createNew(): StiChartStyle;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import Color = Stimulsoft.System.Drawing.Color;
    import Font = Stimulsoft.System.Drawing.Font;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiChartTableDataCells implements IStiChartTableDataCells, IStiJsonReportObject {
        protected _hash: StiMeta[];
        meta(): StiMeta[];
        saveToJsonObject(m: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        clone(): StiChartTableDataCells;
        font: Font;
        textColor: Color;
        shrinkFontToFit: boolean;
        shrinkFontToFitMinimumSize: number;
        constructor(shrinkFontToFit?: boolean, shrinkFontToFitMinimumSize?: number, font?: Font, textColor?: Color);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import Font = Stimulsoft.System.Drawing.Font;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiChartTableHeader implements IStiJsonReportObject, IStiChartTableHeader, ICloneable {
        private static implementsStiChartTableHeader;
        implements(): any[];
        protected _hash: StiMeta[];
        meta(): StiMeta[];
        saveToJsonObject(m: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        clone(): StiChartTableHeader;
        brush: StiBrush;
        font: Font;
        textColor: Color;
        wordWrap: boolean;
        textAfter: string;
        format: string;
        constructor(textAfter?: string, brush?: StiBrush, font?: Font, textColor?: Color, wordWrap?: boolean, format?: string);
    }
}
declare namespace Stimulsoft.Report.Chart {
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiSeriesTopN implements IStiJsonReportObject, IStiSeriesTopN, ICloneable {
        private static implementsStiSeriesTopN;
        implements(): any[];
        protected _hash: StiMeta[];
        meta(): StiMeta[];
        saveToJsonObject(m: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        get componentId(): StiComponentId;
        get propName(): string;
        clone(): StiSeriesTopN;
        mode: StiTopNMode;
        count: number;
        showOthers: boolean;
        othersText: string;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiPenStyle = Stimulsoft.Base.Drawing.StiPenStyle;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiService = Stimulsoft.Base.Services.StiService;
    import Font = Stimulsoft.System.Drawing.Font;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiTrendLine extends StiService implements IStiTrendLine, ICloneable, IStiJsonReportObject {
        private static implementsStiTrendLine;
        implements(): any[];
        meta(): StiMeta[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        static loadFromJsonObjectInternal(jObject: StiJson): IStiTrendLine;
        loadFromXml(xn: XmlNode): void;
        static loadTrendLineFromXml(xmlNode: XmlNode): StiTrendLine;
        get componentId(): StiComponentId;
        get propName(): string;
        clone(): StiTrendLine;
        get serviceName(): string;
        get serviceCategory(): string;
        get serviceType(): Stimulsoft.System.Type;
        core: StiTrendLineCoreXF;
        lineColor: Color;
        lineWidth: number;
        lineStyle: StiPenStyle;
        showShadow: boolean;
        allowApplyStyle: boolean;
        position: StiTrendLine_StiTextPosition;
        font: Font;
        text: string;
        titleVisible: boolean;
        createNew(): StiTrendLine;
        toString(): string;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiTrendLineExponential extends StiTrendLine implements IStiTrendLine, ICloneable, IStiJsonReportObject, IStiTrendLineExponential {
        private static implementsStiTrendLineExponential;
        implements(): any[];
        get componentId(): StiComponentId;
        createNew(): StiTrendLine;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiTrendLineLinear extends StiTrendLine implements IStiTrendLine, IStiTrendLineLinear, ICloneable, IStiJsonReportObject {
        private static implementsStiTrendLineLinear;
        implements(): any[];
        get componentId(): StiComponentId;
        createNew(): StiTrendLine;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiTrendLineLogarithmic extends StiTrendLine implements IStiTrendLine, IStiTrendLineLogarithmic, ICloneable, IStiJsonReportObject {
        private static implementsStiTrendLineLogarithmic;
        implements(): any[];
        get componentId(): StiComponentId;
        createNew(): StiTrendLine;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiTrendLineNone extends StiTrendLine implements IStiTrendLine, IStiTrendLineNone, IStiJsonReportObject, ICloneable {
        private static implementsStiTrendLineNone;
        implements(): any[];
        get componentId(): StiComponentId;
        createNew(): StiTrendLine;
        constructor();
    }
}
declare namespace Stimulsoft.Report.Chart {
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import CollectionBase = Stimulsoft.System.Collections.CollectionBase;
    import ICloneable = Stimulsoft.System.ICloneable;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    class StiTrendLinesCollection extends CollectionBase<StiTrendLine> implements IStiTrendLinesCollection, ICloneable, IStiJsonReportObject {
        implements(): any[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(jObject: StiJson): void;
        loadFromXml(xmlNode: XmlNode): void;
        clone(): object;
        add(line: StiTrendLine): void;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiPenStyle = Stimulsoft.Base.Drawing.StiPenStyle;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiWaterfallConnectorLine implements IStiWaterfallConnectorLine, ICloneable, IStiJsonReportObject {
        private static implementsStiWaterfallConnectorLine;
        implements(): any[];
        clone(): StiWaterfallConnectorLine;
        protected _hash: StiMeta[];
        meta(): StiMeta[];
        saveToJsonObject(m: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        get componentId(): StiComponentId;
        lineColor: Color;
        lineStyle: StiPenStyle;
        lineWidth: number;
        visible: boolean;
    }
}
declare namespace Stimulsoft.Report.Chart {
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    class StiWaterfallTotal implements IStiWaterfallTotal, ICloneable, IStiJsonReportObject {
        private static implementsStiWaterfallTotal;
        implements(): any[];
        clone(): StiWaterfallTotal;
        protected _hash: StiMeta[];
        meta(): StiMeta[];
        saveToJsonObject(m: StiJsonSaveMode): StiJson;
        loadFromJsonObject(j: StiJson): void;
        loadFromXml(xn: XmlNode): void;
        get componentId(): StiComponentId;
        text: string;
        visible: boolean;
    }
}
