Stimulsoft.Report.Check.StiLocalizationExt.English = Stimulsoft.System.decompressLiteral({"@language":"English","@description":"English","@cultureName":"en","CheckActions":{"ApplyEngineV2Long":"Set the EngineVersion property of a page to EngineV2 to get access to all reporting tool features.","Change":"Change","ChangeReportToInterpretationMode":"Change the report to the interpretation mode","Code":"Code","Convert":"Convert","Delete":"Delete","Edit":"Edit","Fix":"Fix","GotoCodeLong":"Go to code.","Hide":"Hide these messages.","NewName":"New name","Off":"Off","On":"On","SetGrowToHeightToFalse":"Set the GrowToHeight property to false","SetGrowToHeightToTrue":"Set the GrowToHeight property to true","StiAllowHtmlTagsInTextActionLong":"Set the value of the AllowHtmlTags property of the component to true, for correct showing the HTML tags.","StiAllowOnDoublePassActionLong":"Set the NumberOfPass property to DoublePass for correct calculation of variables.","StiApplyGeneralTextFormatLong":"Set the 'General' format for the '{0}' component.","StiApplyGeneralTextFormatShort":"Apply General","StiCanBreakComponentInContainerActionLong":"Set the value of the CanBreak property of the component to true.","StiCanGrowComponentInContainerActionLong":"Set the value of the CanGrow property of the container to true.","StiCanGrowGrowToHeightComponentInContainerLong":"The GrowToHeight property will be set to true for all components in container. This operation does not guarantee the 100% correct fix!","StiCanGrowWordWrapTextAndWysiwygActionLong":"Set the value of the TextQuality property of the component to WYSIWYG.","StiColumnsWidthGreaterContainerWidthActionLong":"Automatically change the column width depending on the width of the components.","StiComponentStyleIsNotFoundOnComponentActionLong":"The value of the ComponentStyle property will be cleared.","StiDeleteComponentActionLong":"Delete the component from the report.","StiDeleteConnectionActionLong":"Delete the connection from the report.","StiDeleteDataRelationActionLong":"Delete the relation from the report.","StiDeleteDataSourceActionLong":"Delete the Data Source from the report.","StiDeleteLostPointsActionLong":"Delete all lost points on a page","StiDeletePageActionLong":"Delete the page from the report.","StiFixCrossLinePrimitiveActionLong":"Create new points instead of the lost ones. This operation does not guarantee the 100% correct fix!","StiGenerateNewNameComponentActionLong":"Generate a new name for the component.","StiGenerateNewNameDataSourceActionLong":"Generate a new name for the Data Source.","StiGenerateNewNamePageActionLong":"Generate a new name for the page.","StiGenerateNewNameRelationActionLong":"Generate a new name for the relation.","StiGrowToHeightOverlappingLong":"Set the value of the GrowToHeight property of the component to false.","StiInsufficientTextHeightForOneLineLong":"The height of the component will increase at least to the height of one line of text or to the next cell of the report grid.","StiInsufficientTextHeightForOneLineShort":"Fix height","StiLargeHeightAtPageActionLong":"For handy viewing and editing components, the LargeHeight property of the page will be set to true.","StiMinRowsInColumnsActionLong":"Set the MinRowsInColumns property to zero.","StiMoveComponentToPageAreaActionLong":"The component will be placed so that it is entirely placed on the page. In this case the page fields are not taken into consideration.","StiMoveComponentToPageAreaActionShort":"Move to page","StiMoveComponentToPrintablePageAreaActionLong":"Move the component to the page printing area. Page margins will be taken into consideration.","StiMoveComponentToPrintablePageAreaActionShort":"To Print Area","StiNegativeSizesOfComponentsActionLong":"Fix the size of the component to eliminate problems when rendering a report. This operation does not guarantee the 100% correct fix!","StiOrientationPageToLandscapeActionLong":"Switch the page to the landscape. Page size will not be changed.","StiOrientationPageToLandscapeActionShort":"Landscape layout","StiOrientationPageToPortraitActionLong":"Change the page layout to portrait. Page size will not be changed.","StiOrientationPageToPortraitActionShort":"Portrait layout","StiPrintHeadersFootersFromPreviousPageLong":"Set the PrintHeadersFootersFromPreviousPage property of a page to false.","StiPrintOnPreviousPageLong":"Set the PrintOnPreviousPage property of a page to false.","StiPropertiesOnlyEngineV1ActionLong":"Disable properties of the '{0}' component which are not supported in the EngineV2.","StiPropertiesOnlyEngineV2ActionLong":"Disable properties of the component which are not supported in the EngineV1.","StiResetPageNumberActionLong":"Set the ResetPageNumber property of a page to false.","StiSwitchToParametersVerticalOrientationAction":"Switch the value of the 'Parameters Orientation' report property to 'Vertical'.","StiSwitchWidthAndHeightOfPageActionLong":"Rearrange the height and width of the page.","StiVerySmallSizesOfComponentsLong":"The size of a component will be increased to the GridSize value of a report. This operation does not guarantee the 100% correct fix!","StiVerySmallSizesOfComponentsShort":"Increase size","StiWordWrapCanGrowTextDoesNotFitActionLong":"Set the value of the CanGrow property of the component to true.","Zero":"Zero"},"CheckComponent":{"StiAllowHtmlTagsInTextCheckLong":"HTML tags are used in the text. But the AllowHtmlTags property is set to false. The content of the {0} component will be printed incorrectly.","StiAllowHtmlTagsInTextCheckShort":"The AllowHtmlTags property is not set","StiCanBreakComponentInContainerCheckLong":"The CanBreak property of the '{0}' container is set to false. Some nested components have the CanBreak property set to true.","StiCanBreakComponentInContainerCheckShort":"The CanBreak property of the container is set to false","StiCanGrowComponentInContainerCheckLong":"The CanGrow property of the '{0}' component is set to false. Some nested components have the CanGrow property set to true. This may cause to incorrect report rendering.","StiCanGrowComponentInContainerCheckShort":"The CanGrow property of the container is set to false","StiCanGrowGrowToHeightComponentInContainerLong":"The CanGrow properties of some components of the '{0}' container are set to true. But the GrowToHeight property is not set to true for all components. When rendering a report these components may be aligned with the bottom margin of the container.","StiCanGrowGrowToHeightComponentInContainerShort":"It is possible an incorrect showing of components","StiCanGrowWordWrapTextAndWysiwygCheckLong":"When the TextQuality property is set to Standard or Typographic. Text preview, in different zoom modes and when printing may differ due to errors of rounding of the GDI+ library.","StiCanGrowWordWrapTextAndWysiwygCheckShort":"Possibly there will be the incorrect text wrap","StiChartSeriesValueCheckLong":"The Value property is specified for series from the chart \"{0}\" but the chart does not have the Data Source property set.","StiColumnsWidthGreaterContainerWidthCheckLong":"The total column width is greater then the width of the '{0}' component. This may cause to incorrect report rendering. Also it may cause some problems with exporting a report to other formats.","StiColumnsWidthGreaterContainerWidthCheckShort":"The total column width is greater then the component width","StiColumnsWidthGreaterPageWidthCheckLong":"The total width of columns of the '{0}' page is greater than the page width. This may cause to incorrect report rendering. Also it may cause some problems with exporting a report to other formats.","StiColumnsWidthGreaterPageWidthCheckShort":"The total column width is greater than page width","StiComponentBoundsAreOutOfBandLong":"The '{0}' component is completely or partially outside the band. This component will not be printed correctly. But, in some cases, such position of the component is acceptable. Also it may cause some problems with exporting a report to other formats.","StiComponentBoundsAreOutOfBandShort":"The component is partially outside the band.","StiComponentExpressionCheckLong":"Expression in {0} property of '{1}' can't be evaluated!","StiComponentExpressionCheckShort":"Expression can't be evaluated!","StiComponentResourceCheckLong":"The resource '{0}' specified for component '{1}' was not found in the report dictionary.","StiComponentResourceCheckShort":"The specified resource was not found!","StiComponentStyleIsNotFoundCheckAtPageLong":"The style specified for the '{0}' page does not exist.","StiComponentStyleIsNotFoundCheckLong":"The style specified for the '{0}' component does not exist.","StiComponentStyleIsNotFoundCheckShort":"The specified style does not exist","StiContainerInEngineV2CheckLong":"The '{0}' container is found in the report where the EngineV2 is used. In the EngineV2 the Panel component is used instead of the Container component. The Panel component has more capabilities in compare with the Container.","StiContainerInEngineV2CheckShort":"The container in EngineV2","StiContourTextObsoleteCheckLong":"The '{0}' component has the StiContourText type. The StiContourText component is obsolete and no longer supported, so it is not recommended to use it.","StiContourTextObsoleteCheckShort":"The component is obsolete","StiCorruptedCrossLinePrimitiveCheckLong":"Start and end points of the '{0}' primitive are absent. This primitive either will not be shown or will be shown incorrectly.","StiCorruptedCrossLinePrimitiveCheckShort":"Corrupted primitive","StiCountDataDataSourceAtDataBandLong":"The CountData, DataSource and BusinessObject properties are not set for the '{0}' components. In some cases it is acceptable.","StiCountDataDataSourceAtDataBandShort":"May be you forgot to set values of properties","StiCrossGroupHeaderNotEqualToCrossGroupFooterOnPageLong":"The number of CrossGroupFooter does not correspond to the number of CrossGroupHeader on the '{0}' page. Odd components will not be rendered.","StiCrossGroupHeaderNotEqualToGroupCrossFooterOnContainerLong":"The number of CrossGroupFooter does not correspond to the number of CrossGroupHeader in the '{0}' container. Odd components will not be rendered.","StiDataSourcesForImageCheckLong":"Several data sources are set for the '{0}' component.","StiDataSourcesForImageCheckShort":"Several data sources are set","StiEventsAtInterpretationCheckLong":"The script in the '{0}' event in the '{1}' component doesn't work in the interpretation mode.","StiEventsAtInterpretationCheckShort":"Events in Interpretation","StiExpressionElementCheckLong":"The expression '{0}' in the '{1}' component works incorrectly. The following message was received from the parser: '{2}'.","StiExpressionElementCheckShort":"An expression works incorrectly!","StiFilterCircularDependencyElementCheckLong":"The circular dependency of the filter is found at the element '{0}'. Please check the 'Parent Key' property of the {0} 'element!","StiFilterCircularDependencyElementCheckShort":"Circular filter dependency","StiFilterValueCheckLong":"A filter value of '{0}' is not specified. This filter can't be processed.","StiFilterValueCheckShort":"A filter Value is not specified","StiFontMissingCheckLong":"The font '{0}' specified for the component '{1}' is not found.","StiFontMissingCheckShort":"The font is not found.","StiFunctionsOnlyForEngineV2CheckLong":"System variables which are not available in the EngineV1 are used in the report. Do you want to use EngineV2.","StiFunctionsOnlyForEngineV2CheckShort":"Variables unavailable in the EngineV1 are used.","StiGroupHeaderNotEqualToGroupFooterOnContainerLong":"The number of GroupFooter does not correspond to the number of GroupHeader in the '{0}' container. Odd components will not be rendered.","StiGroupHeaderNotEqualToGroupFooterOnPageLong":"The number of GroupFooter does not correspond to the number of GroupHeader on the '{0}' page. Odd components will not be rendered.","StiGroupHeaderNotEqualToGroupFooterShort":"Some components will not be rendered","StiGroupHeaderSummaryExpressionCheckLong":"The Summary Expression for the group header {0} contains an aggregate function, but this function is set in a separate property. Report generation is not possible.","StiGroupHeaderSummaryExpressionCheckShort":"Invalid function in expression","StiGrowToHeightOverlappingLong":"The GrowToHeight property of the '{0}' component is set to true. When rendering a report, this component can overlap one or more underlying components.","StiGrowToHeightOverlappingShort":"It is possible to overlap components.","StiInsufficientTextHeightForOneLineCheckLong":"The height of the text component {0} is too small, even for one line of text. This can cause issues with exporting to some formats, as well as with printing on the Web. It is recommended to increase the height or set the CanGrow property to true.","StiInsufficientTextHeightForOneLineCheckShort":"Insufficient Height for one line.","StiIsFirstPageIsLastPageDoublePassCheckLong":"The IsFirstPage, IsFirstPageThrough, IsLastPage, IsLastPageThrough system variables provide incorrect results when one pass. It is required to set the NumberOfPass property to DoublePass.","StiIsFirstPageIsLastPageDoublePassCheckShort":"System variables which require double pass are used in the report","StiIsFirstPassIsSecondPassCheckLong":"For correct work of IsFirstPass, IsSecondPass functions double pass of the report rendering is required. maybe you forgot to set the NumberOfPass property of the report to DoublePass.","StiIsFirstPassIsSecondPassCheckShort":"IsFirstPass, IsSecondPass functions require double pass when rendering a report","StiLargeHeightAtPageCheckLong":"Lack of free space on the '{0}' page. This may impede further editing the components and preview. It is recommended to set the LargeHeight property to true.","StiLargeHeightAtPageCheckShort":"Lack of free space on the page","StiLocationOutsidePageCheckLong":"The '{0}' component is partially outside of the page. This component will not be printed correctly. Also it may cause some problems with exporting a report to other formats.","StiLocationOutsidePageCheckShort":"The component is partially outside of the page","StiLocationOutsidePrintableAreaCheckLong":"The '{0}' component is partially or completely outside of the printing area. This component will not be printed correctly. But, in some cases, such position of the component is acceptable. Also it may cause some problems with exporting a report to other formats.","StiLocationOutsidePrintableAreaCheckShort":"The component is partially out of the printing area","StiMinRowsInColumnsCheckLong":"The MinRowsInColumns property of the band is not set to 0. But the value of the Columns property is set to 0. This is senseless.","StiMinRowsInColumnsCheckShort":"Maybe the MinRowsInColumns property is set incorrectly","StiNegativeSizesOfComponentsCheckLong":"The Width and Height of the '{0}' component have negative values. This may cause to incorrect report rendering.","StiNegativeSizesOfComponentsCheckShort":"The Width and Height of the component have negative values.","StiNoConditionAtGroupCheckLong":"A condition is not set for the '{0}' component. Grouping will not be done without it","StiNoConditionAtGroupCheckShort":"A condition for the group is not set","StiNoNameComponentCheckLong":"The component has no name. If to specify the name of the component then the report engine will not be able to compile the report.","StiNoNameComponentCheckShort":"The component has no name","StiNoNamePageCheckLong":"The name of the page with the '{0}' index is not specified. If the name of the page is not specified then the report engine will not compile the report.","StiNoNamePageCheckShort":"A page has no name","StiPanelInEngineV1CheckLong":"The '{0}' panel is found in the report where the EngineV1 is used. This component is not available in the EngineV1. So the reporting tool attempts to render it as a Container component. But the Panel component has more capabilities in compare with the Container and this may cause the incorrect report rendering.","StiPanelInEngineV1CheckShort":"The panel in EngineV1","StiPrintHeadersAndFootersFromPreviousPageLong":"The PrintHeadersAndFootersFromPreviousPage property of the '{0}' page is set to true. But for the first and the only page it has no sense.","StiPrintHeadersAndFootersFromPreviousPageShort":"May be the PrintHeadersAndFootersFromPreviousPage property is set incorrectly","StiPrintOnDoublePassCheckLong":"The PrintOn property of the '{0}' component is not set to AllPages. For correct report rendering double pass should be enabled.","StiPrintOnDoublePassCheckShort":"The PrintOn property is not set to AllPages","StiPrintOnPreviousPageCheck2Long":"The PrintOnPreviousPage property of the '{0}' page is set to true. All components (except bands), which lie on this page will be printed only from the next new page, or may not be printed at all if there will be no new pages.","StiPrintOnPreviousPageCheck2Short":"Some components may not be printed.","StiPrintOnPreviousPageCheckLong":"The PrintOnPreviousPage property of the '{0}' page is set to true. But for the first and the only page it has no sense.","StiPrintOnPreviousPageCheckShort":"Maybe the PrintOnPreviousPage property is set incorrectly","StiPropertiesOnlyEngineV1CheckLong":"The StartNewPage property of the '{0}' component is supported in the EngineV1 only.","StiPropertiesOnlyEngineV1CheckShort":"Some properties are set which are not supported in the EngineV2","StiPropertiesOnlyEngineV2CheckLong":"One of the following properties is used for the '{0}' component: NewPageBefore, NewPageAfter, NewColumnBefore, NewColumnAfter, SkipFirst. They are not supported by EngineV2.","StiPropertiesOnlyEngineV2CheckShort":"Some properties are set which are not supported in the EngineV1","StiResetPageNumberCheckLong":"The ResetPageNumber property of the '{0}' page is set to true. But for the first and the only page it has no sense.","StiResetPageNumberCheckShort":"Maybe the ResetPageNumber property is set incorrectly","StiShowInsteadNullValuesCheckLong":"The NullValues property is set for the '{0}' component. But the DataColumn property is not set. This is senseless.","StiShowInsteadNullValuesCheckShort":"The NullValues property is set","StiSubReportPageZeroCheckLong":"If the SubReportPage property of the SubReport component is not set then the report cannot be rendered.","StiSubReportPageZeroCheckShort":"The SubReportPage property is not set","StiSystemTextObsoleteCheckLong":"The '{0}' component has the StiSystemText type. The StiSystemText component is obsolete and no longer supported, so it is not recommended to use it.","StiSystemTextObsoleteCheckShort":"The component is obsolete","StiTextColorEqualToBackColorCheckLong":"Possible reason is that the values of the TextBrush and Brush properties of the '{0}' component were set incorrectly. The text will not be visible.","StiTextColorEqualToBackColorCheckShort":"The TextBrush property is equal to the Brush property","StiTextTextFormatCheckLong":"Selected text format for the '{0}' component can`t be applied, because the result of the expression is text string.","StiTextTextFormatCheckShort":"The format can`t be applied.","StiTotalPageCountDoublePassCheckLong":"The TotalPageCount, TotalPageCountThrough system variables in scripts and conditions provide incorrect results when one pass. It is required to set the NumberOfPass property to DoublePass.","StiUndefinedComponentCheckLong":"Undefined '{0}' component is found in the report. The reporting tool cannot identify this component. Maybe this is a custom component. The report cannot be rendered!","StiUndefinedComponentCheckShort":"Undefined component","StiVerySmallSizesOfComponentsCheckLong":"The values of the Width of Height of the '{0}' component are less than the values of the GridSize of the report. Maybe you incorrectly set its size. This may cause problems when exporting to other formats.","StiVerySmallSizesOfComponentsCheckShort":"Very small size of the component","StiWidthHeightZeroComponentCheckLongHeight":"The height of the '{0}' component is zero. This may cause problems with showing this component on a page. It also may cause some problems when exporting the report to other formats.","StiWidthHeightZeroComponentCheckLongWidth":"The width of the '{0}' component is zero. There may be some problems with showing this component on a page. It also may cause some problems when exporting the report to other formats.","StiWidthHeightZeroComponentCheckLongWidthHeight":"The height and width of the '{0}' component are equal to 0. There may be some problems with showing this component on a page. It also may cause some problems when exporting the report to other formats.","StiWidthHeightZeroComponentCheckShortHeight":"The height of the '{0}' component are equal to 0","StiWidthHeightZeroComponentCheckShortWidth":"The width of the '{0}' component are equal to 0","StiWidthHeightZeroComponentCheckShortWidthHeight":"The height and width of the '{0}' component are equal to 0","StiWordWrapCanGrowTextDoesNotFitLong":"If the WordWrap property is set to true, and the CanGrow property is set to false, then the text of the {0} component cannot be shown completely. It is recommended to set the CanGrow property to true.","StiWordWrapCanGrowTextDoesNotFitShort":"The text may not fit"},"CheckConnection":{"StiUndefinedConnectionCheckLong":"Undefined '{0}' connection is found. The reporting tool cannot correctly process such type of connection with the database! May be the customer types of connections are used in the report. This report cannot be rendered!","StiUndefinedConnectionCheckShort":"Undefined type of connection","StiUnsupportedConnectionCheckLong":"The report contains the connection '{0}' that is not supported in the selected platform. Please change the options for this connection.","StiUnsupportedConnectionCheckShort":"Unsupported connection type"},"CheckDataRelation":{"StiDifferentAmountOfKeysInDataRelationCheckLong":"The number of parent keys of the '{0}' relation does not correspond to the number of child keys. The following relation cannot be created when rendering a report!","StiDifferentAmountOfKeysInDataRelationCheckShort":"Incorrect number of keys","StiKeysInAbsentDataRelationCheckLong":"Keys of the '{0}' relation are not specified. Such a relation will not be created when report rendering!","StiKeysInAbsentDataRelationCheckShort":"No keys","StiKeysNotFoundRelationCheckLong":"The following columns: '{1}' which were specified as keys of the '{0}', were not found in the Parent or Child data sources of the relation. This relation cannot be created when report rendering!","StiKeysNotFoundRelationCheckShort":"The key of the relation not found","StiKeysTypesMismatchDataRelationCheckLong":"'{1}' column types which were specified as keys for the '{0}' relation does not correspond! Such a relation cannot be created when report rendering!","StiKeysTypesMismatchDataRelationCheckShort":"The key types of the relation does not correspond","StiNoNameDataRelationCheckLong":"The Name of the relation (created between data sources '{0}') is not specified. This name is used for creating the class in the report code of this relation. Without the name the reporting tool will not be able to create the report code of the report and the report will not be compiled!","StiNoNameDataRelationCheckShort":"There is no name for the relation","StiNoNameInSourceDataRelationCheckLong":"NameInSource field is not filled for the '{0}' relation. Without this field the reporting tool will not be able to find the relation in the database and the report will not be rendered!","StiNoNameInSourceDataRelationCheckShort":"NameInSource field is not filled for the relation","StiSourcesInAbsentDataRelationCheckLong":"The parent data source and/or the child data source is not specified for the '{0}' relation. It is necessary to specify both data sources!","StiSourcesInAbsentDataRelationCheckShort":"There are no Data Sources"},"CheckDataSource":{"StiCalculatedColumnRecursionCheckLong":"The '{0}' calculated column of the '{1}' datasource has a expression with recursion. It's impossible to refer to the Calculated Column in its own expression.","StiCalculatedColumnRecursionCheckShort":"Recursive Expression","StiNoNameDataSourceCheckLong":"The datasource name is not specified. This name is used for creating a class in the report code of the data source. Without indicating the name, the reporting tool cannot create the report code and the report will not be compiled!","StiNoNameDataSourceCheckShort":"There is no name for the Data Source","StiNoNameInSourceDataSourceCheckLong":"NameInSource field is not filled for the '{0}' data sources. Without filling this field, the reporting tool will not be able to find the data for the data source in the database and the report will not be rendered!","StiNoNameInSourceDataSourceCheckShort":"NameInSource of the data source field is not filled","StiUndefinedDataSourceCheckLong":"The undefined '{0}' data source is found in the report. The reporting tool cannot correctly process such type of the data source! Maybe custom data sources are used in the report. This report cannot be rendered!","StiUndefinedDataSourceCheckShort":"Undefined Data Source"},"CheckGlobal":{"Error":"Error! ","Information":"Information. ","RenderingMessage":"Report Rendering Message. ","Warning":"Warning! "},"CheckLicense":{"StiLicenseTrialCheckLong":"You are using the trial version of Stimulsoft Reports and Dashboards. To use the software in production you should purchase a license.","StiValidSubscriptioRequiredCheckLong":"Valid Subscription Required. Please update your subscription to use the fully functional version of Stimulsoft Designer."},"CheckPage":{"StiLostPointsOnPageCheckLong":"Lost points are found on the '{0}' page: {1}. Points are invisible and used in the following primitives: vertical line, rectangle and rounded rectangle. If the point does not match to any components then it can be deleted.","StiLostPointsOnPageCheckShort":"Lost points","StiOrientationPageCheckLongLandscape":"The '{0}' page orientation does not correspond to the specified page size. Usually, for Landscape orientation, the page width should be greater than the page height. The width of this page is lesser than the height. This may cause to problems when printing this page.","StiOrientationPageCheckLongPortrait":"The '{0}' page orientation does not correspond to the specified page size. Usually, for Portrait orientation, the page width should be less than the page height. The width of this page is greater than the height. This may cause to problems when printing this page.","StiOrientationPageCheckShort":"Incorrect '{0}' page orientation."},"CheckReport":{"StiCategoryRequestFromUserCheckLong":"The '{0}' variable's categories can't be shown on the parameters panel of the viewer because it is possible only when the value of the 'ParametersOrientation' report property is set to 'Vertical'.","StiCategoryRequestFromUserCheckShort":"Showing categories on the parameters panel","StiCloudCompilationModeCheckLong":"Stimulsoft Cloud cannot preview this report in the compilation mode. Change the report to the interpretation mode.","StiCloudCompilationModeCheckShort":"This report is in the compilation mode","StiCompilationErrorAssemblyCheckLong":"Could not load file or assembly '{0}' or one of its dependencies.","StiCompilationErrorCheck2Long":"The error of compilation is found in the '{1}' property of the '{2}' component:","StiCompilationErrorCheck3Long":"The error of compilation is found in the '{1}' event of the '{2}' component:","StiCompilationErrorCheckLong":"The error of compilation is found in the '{0}' report:","StiCompilationErrorCheckShort":"The error of compilation","StiDataSourceLoopingCheckLong":"Data band '{0}' and child data band '{1}' use the same data source '{2}'. To avoid looping, the child data band will not be shown.","StiDataSourceLoopingCheckShort":"Data looping is possible","StiDuplicatedName2CheckLong":"There are several data sources with the '{0}' name.","StiDuplicatedName3CheckLong":"There are several data sources with the name '{0}' which differ in case.","StiDuplicatedNameCheckLong":"There are several components with the '{0}' name.","StiDuplicatedNameCheckShort":"The components with duplicate names are found","StiDuplicatedNameInSourceCheckShort":"Duplicates in the data source","StiDuplicatedNameInSourceInDataRelationReportCheckLong":"The '{0}' relations are found in the report. They have equal ('{1}') values in the NameInSource. All values in the NameInSource should be unique in the range of one relation to one database! It is necessary to correct these fields of relations.","StiDuplicatedNameInSourceInDataRelationReportCheckShort":"Duplicates in relations","StiDuplicatedReportName2CheckLong":"The report name '{0}' is the same as the data source name.","StiDuplicatedReportNameCheckLong":"The report name '{0}' is the same as the component name.","StiNetCoreCompilationModeCheckLong":"Stimulsoft .NET Core engine cannot render this report in the compilation mode. Change the report to the interpretation mode.","StiNetCoreCompilationModeCheckShort":"This report is in the compilation mode"},"CheckVariable":{"StiVariableInitializationCheckLong":"The '{0}' variable has the wrong default value. It is impossible to initialize the variable with a specified value.","StiVariableInitializationCheckShort":"Initialization value","StiVariableRecursionCheckLong":"The '{0}' variable has a expression with recursion. It's impossible to refer to the Variable in its own expression.","StiVariableRecursionCheckShort":"Recursive Expression","StiVariableTypeCheckLong":"For the dependent variables to work correctly, their types and the types of their columns must match.","StiVariableTypeCheckShort":"Variable types do not match."},"Font":{"Bold":"A value that indicates whether this Font is bold.","Italic":"A value that indicates whether this Font is italic.","Name":"The face name of this Font.","Size":"Size of this Font.","Strikeout":"A value that indicates whether this Font specifies a horizontal line through the font.","Underline":"A value that indicates whether this Font is underlined."},"Publish":{"ActionDesign":"Design","ActionExport":"Export","ActionShow":"Show","AddNewConnection":"Add New Connection","Cancel":"Cancel","ChangeExportSettings":"Change export settings","ChangeExportSettingsToolTip":"The code for creating the settings class of the selected export format will be added. In the project, you can change the export settings.","Close":"Close","ConnectionsFromReport":"Use Connection from Report","ConnectionsFromReportToolTip":"Connections saved in the report template will be used without any modifications.","ConnectionsRegData":"Register Data from Code","ConnectionsRegDataToolTip":"You will be offered to connect data from code in the form of XML or JSON data, or, if possible, in the form of business objects.","ConnectionsReplace":"Replace Connection String","ConnectionsReplaceToolTip":"You will be offered to change the connection from the code, depending on its type - file path, database connection string.","Copy":"Copy","CopyingLibraries":"Copying project files...","DataAdapterPlatform":"Data Adapter Platform","DeployReportPlatform":"Deploy Report to Platform:","DownloadingLibraries":"Downloading libraries...","EmbedAllDataToResources":"The report contains data connections that are not supported in the selected platform. Do you want to embed available data in the report as resources? In this case, all connections will be replaced with XML resources.","EmbedAllDataToResourcesToolTip":"All data will be loaded and added to the report template as resources. This allows you to view the report without having to query the database.","ExportFormat":"Export Report to:","ExportFormatData":"Data","ExportFormatDataType":"Data Type:","ExportFormatImage":"Image","ExportFormatImageType":"Image Type:","FrameworkType":"Framework Type","FrameworkVersion":"Framework Version","FullScreenViewer":"Display the Viewer in Full Browser Window","FullScreenViewerToolTip":"The viewer with the report will be displayed in the entire available space of the browser window.","GetLibrariesFrom":"Get Stimulsoft Libraries from {0}","GetLibrariesFromToolTip":"All necessary libraries and scripts will be connected from the repository. Otherwise, they will be copied into the project and connected directly.","GroupAddons":"Addons","GroupConnections":"Data Connections","GroupParameters":"Report Parameters","HideOptions":"Hide Options","IncludeFonts":"Include Fonts","IncludeFontsToolTip":"The fonts necessary for correct calculation of the sizes of report components will be copied and connected to the project.","IncludeLibrariesToStandalone":"Include Stimulsoft Libraries to the Executable File","IncludeLibrariesToStandaloneToolTip":"All necessary libraries and files will be included in the executable file. Otherwise, a ZIP archive will be created.","IncludeLicenseKey":"Include License Key","IncludeLicenseKeyToolTip":"The license key from the account of the currently authorized user will be integrated into the project.","IncludeLocalization":"Include Localization","IncludeLocalizationToolTip":"The selected localization will be copied and connected to the project.","IncludeReportPackedStringToCode":"Include Report as Packed String to Code","IncludeReportPackedStringToCodeToolTip":"The report will be saved as a Base64 string right in the project code. This allows you to get rid of additional files.","IncludeUITheme":"Include UI Theme","IncludeUIThemeToolTip":"The selected user interface theme will be set in the component settings.","JavaScriptFramework":"Framework Type:","LicenseKeyTypeFile":"File","LicenseKeyTypeString":"String","LoadReport":"Load Report from:","LoadReportAssembly":"Assembly","LoadReportByteArray":"Byte Array","LoadReportClass":"Class","LoadReportFile":"File","LoadReportHyperlink":"Hyperlink","LoadReportResource":"Resource","LoadReportStream":"Stream","LoadReportString":"String","ParametersFromReport":"Use Value from Report","ParametersFromReportToolTip":"The values ​​of the parameters set in the report template will be used.","ParametersReplace":"Replace Value from {0}Code","ParametersReplacePhpInfo":"The parameter will be removed from the report dictionary and can be used only in the SQL query.","ParametersReplaceToolTip":"You will be offered to change the values ​​of the report parameters from the code.","ParametersRequestFromUser":"Request from User","ParametersRequestFromUserToolTip":"The report parameter will be requested from the user when viewing the report in the viewer window.","ProjectType":"Project Type:","Publish":"Publish","PublishType":"Report Publish Type:","PublishTypeProject":"Project","PublishTypeProjectToolTip":"A ZIP archive will be created with the finished project and all the necessary files for further compilation and launch.","PublishTypeStandalone":"Standalone","PublishTypeStandaloneToolTip":"An already compiled application for viewing the report will be created.","ReadMore":"Read More","RegDataOnlyForPreview":"Use Only for Report Preview","RegDataOnlyForPreviewToolTip":"The data will be connected to the report only at the moment of the preview.","RegDataSynchronize":"Synchronize Report Dictionary","RegDataSynchronizeToolTip":"After connecting the data to the report, the data dictionary will be synchronized - data sources, columns and their types, data links.","ReplaceConnectionString":"Replace Connection String","ReplacePathToData":"Replace Path to the Data","ReportAction":"What to do with Report:","ReportErrors":"Unfortunately, due to errors, this report cannot be published!","SaveExportedPagesToArchive":"Save all pages in one ZIP archive","SaveExportedPagesToArchiveToolTip":"Each report page will be exported to the selected image format, all pages will be added into one ZIP archive.","SaveProjectPackage":"Save Project Package","SaveStandalone":"Save Standalone","SaveStandalonePackage":"Save Standalone Package","SearchLibraries":"Search for libraries...","ShowMore":"Show More","ShowOptions":"Show Options","ThemeBackground":"Background:","ThemeStyle":"Theme Style:","TrialVersion":"Trial Version","Use":"Use","UseCompilationCache":"Use Compilation Cache","UseCompilationCacheToolTip":"The rendered report will be saved in a temporary directory, which allows you to speed up its rendering and reduce memory usage.","UseCompressedScripts":"Use Compressed Scripts","UseCompressedScriptsToolTip":"Packed scripts will be used instead of regular ones. This allows you to reduce their size, but it takes some time to unpack when running.","UseLatestVersion":"Use the latest version of libraries","UseLatestVersionToolTip":"When connecting libraries and scripts, the latest available official version of the product will be used instead of the currently installed version.","UseRenderedReport":"Use Rendered Report","UseRenderedReportToolTip":"The report will be built and saved in document format. You will no longer need a data connection to view the report, but any interactivity and report parameters will not be available.","UseWpfDesignerV2":"Use the Designer V2","UseWpfDesignerV2ToolTip":"The updated WPF report designer will be used. It has a new UI, increased speed, and other optimizations."},"ReportComparer":{"Change":"Change","Copy":"Copy","CopyAll":"Copy All","Delete":"Delete","SaveChangesInReports":"Do you want to save changes in reports?","StiBusinessObjectDifferentColumnCompareComment":"Shows what columns of identical Business Objects are only in one report.","StiBusinessObjectDifferentColumnCompareLong":"The column '{0}' of the Business Object '{1}' of the report '{2}' not present in the Business Object '{3}' of the report '{4}'.","StiBusinessObjectDifferentColumnCompareShort":"No column in the business object.","StiBusinessObjectPropertiesCompareComment":"Shows different properties values of identical Business Objects.","StiBusinessObjectPropertiesCompareLong":"In the report '{0}' the '{2}' property of the Business Object '{1}' differs from the values of the '{5}' property of the Business Object '{4}' of the report '{3}'.","StiBusinessObjectPropertiesCompareShort":"Different properties values of Business Objects.","StiChangeInReportPropertyActionDescription":"Equate both properties values to '{0}'","StiComponentPropertiesCompareComment":"Shows different properties values of identical components.","StiComponentPropertiesCompareLong":"The '{2}' property of the component '{1}' in the report '{0}' differs from the value of the '{5}' property of the component '{4}' in the report '{3}'.","StiComponentPropertiesCompareShort":"Different values of component properties.","StiCopyAllActionDescription":"Copy everything to the report '{0}'","StiCopyBusinessDataColumnActionsDescription":"Copy the column to the Business Object","StiCopyBusinessObjectActionDescription":"Copy Business Object","StiCopyComponentActionDescription":"Copy the component","StiCopyDataColumnActionDescription":"Copy the column to the data source","StiCopyDataRelationActionDescription":"Copy Data Relation","StiCopyDataSourceActionDescription":"Copy data source","StiCopyStyleActionDescription":"Copy Style","StiCopyVariableActionDescription":"Copy variable","StiDataRelationPropertiesCompareComment":"Shows different properties values of identical relations.","StiDataRelationPropertiesCompareLong":"The '{2}' property of the relation '{1}' of the report '{0}' differs from the value of the '{5}' property of the relation '{4}' of the report '{3}'.","StiDataRelationPropertiesCompareShort":"Different properties values of relations.","StiDataSourceDifferentColumnCompareComment":"Shows what columns of identical data sources are only in one report.","StiDataSourceDifferentColumnCompareLong":"The column '{0}' from the data source '{1}' of the report '{2}' not present in the data source '{3}' of the report '{4}'.","StiDataSourceDifferentColumnCompareShort":"No column in the data source.","StiDataSourcePropertiesCompareComment":"Shows different properties values of identical data sources.","StiDataSourcePropertiesCompareLong":"The '{2}' property of the data source '{1}' of the report '{0}' differs from the value of the '{5}' property of the data source '{4}' of the report '{3}'.","StiDataSourcePropertiesCompareShort":"Different values of the data sources properties.","StiDeleteBusinessDataColumnActionsDescription":"Delete the column from Business Objects","StiDeleteBusinessObjectActionDescription":"Delete Business Object","StiDeleteComponentActionDescription":"Delete the component from the report","StiDeleteDataColumnActionDescription":"Delete the column from the data source","StiDeleteDataRelationActionDescription":"Delete the relation between data sources","StiDeleteDataSourceActionDescription":"Delete data source","StiDeleteStyleActionDescription":"Delete Style","StiDeleteVariableActionDescription":"Delete the variable from the Report ","StiReportDifferentBusinessObjectCompareComment":"Shows what Business Object present in one report only.","StiReportDifferentBusinessObjectCompareLong":"The Business Object '{0}' not present in the report '{1}'.","StiReportDifferentBusinessObjectCompareShort":"No Business Object.","StiReportDifferentComponentsCompareComment":"Shows what component present in one report only.","StiReportDifferentComponentsCompareLong":"The component '{0}' not present in the report '{1}'.","StiReportDifferentComponentsCompareMessag1":"There is no component '{1}' for the Container property of the StiClone '{0}'.","StiReportDifferentComponentsCompareMessag2":"There is no component '{1}' for the Master Component property of the StiDataBand '{0}'.","StiReportDifferentComponentsCompareShort":"No component.","StiReportDifferentDataRelationCompareComment":"Shows what relation between the data sources is only in one report.","StiReportDifferentDataRelationCompareLong":"The relation '{0}' between '{1}' and '{2}' is not present in the report '{3}'.","StiReportDifferentDataRelationCompareShort":"No relation.","StiReportDifferentDataSourceCompareComment":"Shows what data source present in one report only.","StiReportDifferentDataSourceCompareLong":"The data source '{0}' not present in the report '{1}'.","StiReportDifferentDataSourceCompareShort":"No data source.","StiReportDifferentStyleCompareComment":"Shows what style present in one report only.","StiReportDifferentStyleCompareLong":"The style '{0}' is not present in the report '{1}'.","StiReportDifferentStyleCompareShort":"No style.","StiReportDifferentVariableCompareComment":"Shows what variable present in one report only.","StiReportDifferentVariableCompareLong":"The variable '{0}' not present in the report '{1}'.","StiReportDifferentVariableCompareShort":"No variable.","StiReportPropertiesCompareComment":"Shows various values of report properties.","StiReportPropertiesCompareLong":"In the report '{0}' the '{1}' property does not correspond to the value of the '{3}' property of the report '{2}'.","StiReportPropertiesCompareShort":"Different values of report properties.","StiStylePropertiesCompareComment":"Shows different properties values with identical styles.","StiStylePropertiesCompareLong":"The '{2}' property of the style '{1}' of the report '{0}' differs from the value of the '{5}' property of the style '{4}' of the report '{3 }'.","StiStylePropertiesCompareShort":"Different values of properties in the styles.","StiVariablePropertiesCompareComment":"Shows different values of the properties of identical variables.","StiVariablePropertiesCompareLong":"The '{2}' property of the variable '{1}' of the report '{0}' differs from the value of the '{5}' property of the variable '{4}' of the report '{3}'.","StiVariablePropertiesCompareShort":"Different properties values of variables."},"ReportComparerViewer":{"Browse":"Browse...","BusinessObjects":"Business Objects","BusinessObjectsDataColumns":"Business Objects Data Columns","Cancel":"Cancel","Compare":"Compare","CompareList":"Compare List","CompareReports":"Compare Reports","Compares":"Compares","Components":"Components","DataRelations":"Data Relations","DataSources":"Data Sources","DataSourcesDataColumns":"Data Sources Data Columns","EnterPassword":"Enter the password for opening of it a file","FirstReport":"First Report","LongMessage":"Long Message","Next":"Next","OK":"OK","OpenReports":"Open Reports","Previous":"Previous","Report":"Report","ReportToCompare":"Report to Compare","SaveReports":"Save Reports","SecondReport":"Second Report","SelectReports":"Select Reports","StatusHigh":"High","StatusLow":"Low","StatusMiddle":"Middle","Styles":"Styles","Variables":"Variables","WarningFile1EqualFile2":"Warning! File 'First Report' equal 'Second Report'.","WarningFirstAndSecondReportNotFound":"Warning! Files 'First Report' and 'Second Report' not found.","WarningFirstReportNotFound":"Warning! File 'First Report' not found.","WarningSecondReportNotFound":"Warning! File 'Second Report' not found."},"StiAdvancedBorder":{"BottomSide":"A frame of the bottom side.","LeftSide":"A frame of the left side.","RightSide":"A frame of the right side.","TopSide":"A frame of the top side."},"StiArea":{"BorderColor":"A border color of this area.","Brush":"A brush to fill a area.","ColorEach":"A value indicates that each series is drawn by its own colour.","GridLinesHor":"Horizontal grid lines on left axis.","GridLinesHorRight":"Horizontal grid lines on right axis.","GridLinesVert":"Grid lines on vertical axis.","InterlacingHor":"Interlacing settings on horizontal axis.","InterlacingVert":"Interlacing settings on vertical axis.","RadarStyle":"A style of radar area.","ReverseHor":"A value that indicate that all values on horizontal axis is reverse.","ReverseVert":"A value that indicate that all values on vertical axis is reverse.","ShowShadow":"A value that indicates necessary draw shadod or no.","XAxis":"Settings of XAxis.","XTopAxis":"Settings of XTopAxis.","YAxis":"Settings of YAxis.","YRightAxis":"Settings of YRightAxis."},"StiAreaSeries":{"Brush":"A brush that will be used to fill area."},"StiArrowShapeType":{"ArrowHeight":"The arrow height factor.","ArrowWidth":"The arrow width factor."},"StiAustraliaPost4StateBarCodeType":{"Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code."},"StiAxis":{"ArrowStyle":"Style of axis arrow.","Interaction":"Interaction options of this component.","Labels":"Axis labels settings.","LineColor":"Line color which used to draw axis.","LineStyle":"Line style of axis.","LineWidth":"Line width which used to draw axis.","LogarithmicScale":"A value indicates that logarithmic scale will be used.","Range":"Axis range settings.","RangeScrollEnabled":"A value which indicates whether the range of axis can be scrolled.","ShowEdgeValues":"A value indicates that first and last arguments on axis will be shown anyway.","ShowScrollBar":"A value which indicates that scroll bar will be shown.","ShowXAxis":"Type of drawing X axis.","ShowYAxis":"Type of drawing Y axis.","StartFromZero":"A value indicates that all arguments will be shows from zero.","Step":"A value that indicates with what steps do labels be shown on axis.","Ticks":"Ticks settings.","Title":"Axis title settings.","Visible":"Visibility of axis."},"StiAxisDateTimeStep":{"Interpolation":"The value indicates that the values ​​will be interpolated.","NumberOfValues":"The value indicates how many values to display in a time step.","Step":"The value indicates with what the time step values will be shown."},"StiAxisInteraction":{"RangeScrollEnabled":"A value which indicates whether the range of axis can be scrolled."},"StiAxisLabels":{"Angle":"An angle of label rotation.","Antialiasing":"A value that control antialiasing drawing mode.","Color":"A color of labels drawing.","Font":"a font which will be used for axis label drawing.","Format":"Format string which is used for formating argument values.","Placement":"A mode of labels placement on axis.","Step":"A value which indicates with what steps do labels be shown on axis.","TextAfter":"A string which will be output after argument string representation.","TextAlignment":"A label text alignment.","TextBefore":"A string which will be output before argument string representation.","Width":"Fixed width of axis labels.","WordWrap":"Word wrap."},"StiAxisRange":{"Auto":"A value indicates that minimum and maximum values will be calculated automatically.","Maximum":"Maximum value of axis range.","Minimum":"Minimum value of axis range."},"StiAxisTicks":{"Length":"Length of one major tick.","LengthUnderLabels":"A length of one major tick under labels.","MinorCount":"Count of minor ticks between two major ticks.","MinorLength":"Length of one minor tick.","MinorVisible":"Visibility of minor ticks.","Step":"A value that indicates on which steps major ticks will be displayed.","Visible":"Visility of major ticks."},"StiAxisTitle":{"Alignment":"A title text alignment.","Antialiasing":"A value that control antialiasing drawing mode.","Color":"A color which will be used for title drawing.","Direction":"A text direction for axis title drawing.","Font":"A font which will be used for axis title drawing.","Position":"A title text position.","Text":"A title text."},"StiBand":{"MaxHeight":"Maximal height of band.","MinHeight":"Minimal height of band.","PrintOnEvenOddPages":"A value indicates that the component is printed on even-odd pages.","ResetPageNumber":"Allows to reset page number on this band.","StartNewPage":"A value indicates that it is necessary to print every new string on a new page.","StartNewPageIfLessThan":"A value that indicates how much free space on a page (in percentage terms) should be reserved for formation of a new page. The value should be set in the range from 0 to 100. If the value is 100 then, in any case, a new page will be formed. This property is used together with the StartNewPage property."},"StiBandInteraction":{"Collapsed":"A boolean expression that indicates whether a group, when rendering, should be collapsed or not.","CollapseGroupFooter":"A value that indicates whether it is necessary GroupFooter collapsing.","CollapsingEnabled":"A value that indicates whether it is allowed or not data collapsing in the report viewer.","SelectionEnabled":"A value which indicates whether it is allowed to select one data row which is output by this DataBand."},"StiBarCode":{"Angle":"An angle of a bar-code rotation.","AutoScale":"A value that indicates how bar-code will scale its size.","BackColor":"A background color of bar-code.","BarCodeType":"A type of the bar-code.","Code":"An expression to fill a code of bar-code.","Font":"A font of bar-code.","ForeColor":"A bar-code color.","GetBarCodeEvent":"Occurs when getting the code of bar-code.","ShowLabelText":"A value that indicates will this bar-code show a label text or not.","ShowQuietZones":"A value which indicates will this bar code show quiet zones or no.","Zoom":"A value to multiply by it bar-code size."},"StiBarCodeTypeService":{"AddClearZone":"A value that indicates will the Clear Zone be shown.","AspectRatio":"A value that set aspect ratio between horizontal and vertical sides of bar-code.","AutoDataColumns":"A value indicates that amount of columns will be calculated automatically.","AutoDataRows":"A value indicates that amount of rows will be calculated automatically.","Checksum":"A mode of checksum.","CheckSum":"A mode of checksum.","CheckSum1":"A mode of CheckSum1.","CheckSum2":"A mode of CheckSum2.","DataColumns":"The amount of data columns.","DataRows":"The amount of data rows.","EncodingMode":"A mode of encoding.","EncodingType":"A type of encoding.","ErrorsCorrectionLevel":"Errors correction level. The higher level is, the more information is added to bar-code for restoring.","Height":"Height factor of the bar-code.","MatrixSize":"Matrix size.","Module":"Width of the most fine element of the bar-code.","PrintVerticalBars":"A value that indicates whether to print or not vertical sections.","Ratio":"A value that indicates the WideToNarrow ratio.","RatioY":"Vertical ratio of bar-code. Value must between 2 and 5.","ShowQuietZoneIndicator":"A value that indicates will show Quiet Zone Indicator or no.","Space":"Space between elements of bar-code.","SupplementCode":"A component supplement bar-code.","SupplementType":"A type of supplement code.","UseRectangularSymbols":"A value that indicates will RectangularSymbols be used or not."},"StiBaseStyle":{"AllowUseBackColor":"A value which indicates whether a report engine can use BackColor for dialog controls.","AllowUseBorderFormatting":"A value which indicates whether a report engine can use Border formatting or not.","AllowUseBorderSides":"A value which indicates whether a report engine can use Border Sides or not.","AllowUseBorderSidesFromLocation":"A value which indicates whether a report engine can set border sides of a component depending on the component location.","AllowUseBrush":"A value which indicates whether a report engine can use Brush formatting or not.","AllowUseFont":"A value which indicates whether a report engine can use Font formatting or not.","AllowUseForeColor":"A value which indicates whether a report engine can use ForeColor for dialog controls.","AllowUseHorAlignment":"A value which indicates whether a report engine can use HorAlignment formatting or not.","AllowUseImage":"A value which indicates whether a report engine can use Image formatting or not.","AllowUseTextBrush":"A value which indicates whether a report engine can use TextBrush formatting or not.","AllowUseTextOptions":"A value which indicates whether a report engine can use TextOptions formatting or not.","AllowUseVertAlignment":"A value which indicates whether a report engine can use VertAlignment formatting or not.","AxisLabelsColor":"A color of labels drawing.","AxisLineColor":"A line color which used to draw axis.","AxisTitleColor":"A color which will be used for title drawing.","BackColor":"A background color for drawing this style.","BasicStyleColor":"A base color for drawing this style.","Border":"The appearance and behavior of the component border.","Brush":"The brush, which is used to draw background.","BrushType":"A value which indicates which type of brush will be used to draw this style.","ChartAreaBorderColor":"A border color of area.","ChartAreaBrush":"A brush to fill a area.","CollectionName":"A name of the styles collection.","Color":"A color of the style.","Conditions":"A collection of the style conditions.","Description":"A style description.","Font":"A font for drawing this style.","ForeColor":"A foreground color for drawing this style.","GridLinesHorColor":"A color which will be used for drawing horizontal grid lines.","GridLinesVertColor":"A color which will be used for drawing vertical grid lines.","HorAlignment":"A horizontal alignment of the style.","Image":"An image to fill the Image property of the Image component.","InterlacingHorBrush":"A brush that is used for drawing horizontal interlacing bar.","InterlacingVertBrush":"A brush that is used for drawing vertical interlacing bar.","LegendBorderColor":"A border color.","LegendBrush":"A background brush of legend.","LegendLabelsColor":"A color of the labels.","LegendTitleColor":"A title color of legend.","Name":"A style name.","SeriesLabelsBorderColor":"A border color of series labels.","SeriesLabelsBrush":"A brush that will be used to fill area of series labels.","SeriesLabelsColor":"A foreground color of series labels.","StyleColors":"A list of colors which will be used for drawing chart series.","TextBrush":"A brush of the style, which is used to display text.","VertAlignment":"A vertical alignment of the style."},"StiBorder":{"Color":"A border color.","DropShadow":"A value that indicates whether drop shadow is shown.","ShadowBrush":"A border shadow brush.","ShadowSize":"Shadow Size.","Side":"Frame borders.","Size":"A border size.","Style":"A border style.","Topmost":"A value which indicates that border sides will be drawn on top of all components."},"StiBorderSide":{"Color":"A border color.","Size":"A border size.","Style":"A border style."},"StiBusinessObject":{"Alias":"An alias of the business object.","Category":"A category name of the business object.","Columns":"A column collection of the business object.","Name":"A name of the business object."},"StiButtonControl":{"Cancel":"A value that indicates which button is clicked when the user presses the ESCAPE key.","Default":"A value that indicates which button is clicked when the user hits the ENTER key.","DialogResult":"A value that is returned to the parent form when the button is clicked.","Image":"An image that is displayed on a button control.","ImageAlign":"An alignment of the image on the button control.","Text":"A text associated with this control.","TextAlign":"An alignment of the text on the button control."},"StiCandlestickSeries":{"ListOfValuesClose":"An expression to fill a list of close values. Example: 1;2;3","ListOfValuesHigh":"An expression to fill a list of high values. Example: 1;2;3","ListOfValuesLow":"An expression to fill a list of low values. Example: 1;2;3","ListOfValuesOpen":"An expression to fill a list of open values. Example: 1;2;3","ValueClose":"The close value expression. Example: {Order.Value}","ValueDataColumnClose":"A column name column that contains the close value.","ValueDataColumnHigh":"A column name that contains the high value.","ValueDataColumnLow":"A column name that contains the low value.","ValueDataColumnOpen":"A column name that contains the open value.","ValueHigh":"The high value expression. Example: {Order.Value}","ValueLow":"The low value expression. Example: {Order.Value}","ValueOpen":"The open value expression. Example: {Order.Value}"},"StiCap":{"Color":"A cap color.","Fill":"Fill mode of the cap.","Height":"Height of the cap.","Style":"A cap style.","Width":"Width of the cap."},"StiCardsStyle":{"LineColor":"The property defines the color which will be used to draw borders of the cards."},"StiChart":{"Area":"An area of the chart.","ChartType":"A type of the chart.","ConstantLines":"Constant lines settings of the chart.","HorSpacing":"Horizontal space between border of the chart and the chart.","Legend":"Legend settings.","ProcessAtEnd":"A value indicates that a chart is processed at the end of the report execution.","ProcessChartEvent":"Occurs when getting the ProcessChart.","Rotation":"A value which indicates how to rotate an chart before output.","Series":"A list of series.","SeriesLabels":"Series labels.","Strips":"Strips settings of the chart.","Style":"A style of the chart.","Table":"Chart table settings.","Title":"Chart title settings.","VertSpacing":"Vertical space between border of the chart and the chart."},"StiChartArea":{"ColorEach":"A value indicates that each series is drawn by its own colour.","GridLinesHor":"Horizontal grid lines on left axis.","GridLinesVert":"Grid lines on vertical axis.","InterlacingHor":"Interlacing settings on horizontal axis.","InterlacingVert":"Interlacing settings on vertical axis.","ReverseHor":"A value that indicate that all values on horizontal axis is reverse.","ReverseVert":"A value that indicate that all values on vertical axis is reverse.","SideBySide":"Sets the layout mode of the graphic elements of the chart for each argument - side-by-side or along the Z axis.","XAxis":"Settings of XAxis.","XTopAxis":"Settings of XTopAxis.","YAxis":"Settings of YAxis.","YRightAxis":"Settings of YRightAxis."},"StiChartAxis":{"Visible":"Visibility of axis."},"StiChartAxisLabels":{"Angle":"An angle of label rotation.","Color":"A color of labels drawing.","Font":"a font which will be used for axis label drawing.","Placement":"A mode of labels placement on axis.","Step":"A value which indicates with what steps do labels be shown on axis.","TextAfter":"A string which will be output after argument string representation.","TextAlignment":"A label text alignment.","TextBefore":"A string which will be output before argument string representation."},"StiChartAxisRange":{"Auto":"A value indicates that minimum and maximum values will be calculated automatically.","Maximum":"Maximum value of axis range.","Minimum":"Minimum value of axis range."},"StiChartAxisTitle":{"Alignment":"A title text alignment.","Color":"A color which will be used for title drawing.","Font":"A font which will be used for axis title drawing.","Position":"A title text position.","Text":"A title text.","Visible":"Title visibility."},"StiChartElement":{"Area":"An area of the chart.","Arguments":"Chart arguments.","CloseValues":"Close values of a chart.","ColorEach":"","ConstantLines":"Constant lines settings of the chart.","EndValues":"End values of a chart.","Group":"","HighValues":"High values of a chart.","Labels":"Series labels.","Legend":"Legend settings.","LowValues":"Low values of a chart.","Series":"Chart series.","Style":"A style of a chart.","TextFormat":"The text format.","Title":"Chart title.","TrendLines":"Trend line settings.","Values":"Chart values.","Weights":"Weight values of a chart."},"StiChartGridLines":{"Color":"Color which will be used for drawing major grid lines."},"StiChartInterlacing":{"Color":"Color which will be used for drawing interlaced bars.","Visible":"Visibility of interlaced bars."},"StiChartLabels":{"AutoRotate":"A value that enables or disables auto rotate mode drawing of series labels.","Font":"A font that will be used to draw series labels.","ForeColor":"A color of labels drawing.","Position":"A position of the labels.","Style":"A style of the labels.","TextAfter":"A text that will be shown after label text.","TextBefore":"A text that will be shown before label text."},"StiChartLegend":{"Columns":"Amount of columns.","Direction":"Direction which used for series drawing in legend.","HorAlignment":"Horizontal alignment of legend placement.","VertAlignment":"Vertical alignment of legend placement.","Visible":"Visibility of chart legend."},"StiChartLegendLabels":{"Color":"A color of the labels.","Font":"A font which used for series title drawing in chart legend."},"StiChartLegendTitle":{"Color":"A title color of legend.","Font":"A title font of the chart legend.","Text":"A title text of legend."},"StiChartStyle":{"ChartAreaShowShadow":"The property defines the area shadow will be displayed or not.","MarkerVisible":"The property defines the marker will be displayed or not.","SeriesBorderThickness":"The property defines the series border width.","SeriesLabelsLineColor":"The property defines the line color of series labels.","SeriesLighting":"The property defines the pie lighting will be displayed or not.","SeriesShowBorder":"The property defines the series border will be displayed or not.","SeriesShowShadow":"The property defines the series shadow will be displayed or not.","TrendLineColor":"The property defines the color of trend line.","TrendLineShowShadow":"The property defines the trend line shadow will be displayed or not."},"StiChartTable":{"Font":"Font of the chart table.","GridLineColor":"A color of the grid lines.","GridLinesHor":"Visibility of grid lines horizontal.","GridLinesVert":"Visibility of grid lines vertical.","GridOutline":"Visibility of grid outline","MarkerVisible":"Visibility of markers.","NegativeSeriesColors":"A list of colors which will be used for drawing series with negative values.","SeriesColors":"A list of colors which will be used for drawing series.","Visible":"Visibility of chart table."},"StiChartTitle":{"Alignment":"Alignment of chart title.","Antialiasing":"A value that control antialiasing drawing mode of chart title.","Brush":"A text brush of the chart title.","Dock":"Docking ot chart title.","Font":"Font of the chart title.","Spacing":"Spacing between chart title and chart area.","Text":"A text of the chart title.","Visible":"Visibility of chart title."},"StiCheckBox":{"Checked":"An expression that is used to calculate check state.","CheckStyleForFalse":"Check style for the false value.","CheckStyleForTrue":"Check style for the true value.","ContourColor":"A contour color.","ExcelValue":"An expression that is used to export data to Excel.","GetCheckedEvent":"Occurs when state is being checked.","Size":"A contour size.","Values":"A string that describes true and false values."},"StiCheckBoxControl":{"Checked":"A value indicates whether the check box is in the checked state.","CheckedBinding":"Data bindings for the checked.","CheckedChangedEvent":"A script of the CheckedChanged event.","Text":"A text associated with this control.","TextBinding":"Data bindings for the text."},"StiCheckedListBoxControl":{"CheckOnClick":"A value indicates whether the check box should be toggled when an item is selected.","ItemHeight":"Height of the item area.","Items":"A collection of items in this CheckedListBox.","ItemsBinding":"A data bindings for the items.","SelectedIndexBinding":"Data bindings for the selected index.","SelectedIndexChangedEvent":"Occurs when the SelectedIndex property has been changed.","SelectedItemBinding":"Data bindings for the selected item.","SelectedValueBinding":"Data bindings for the selected value.","SelectionMode":"A value specifying the selection mode.","Sorted":"A value indicates whether the items in the ListBox are sorted alphabetically."},"StiChildBand":{"KeepChildTogether":"A value indicates that childs are to be kept together.","PrintIfParentDisabled":"Gets or sets value which indicates that if the parent band is disabled then the child band will be printed anyway."},"StiClone":{"Container":"A clone container.","ScaleHor":"A value indicates that contents of the container will be shrunk or grown."},"StiClusteredColumnArea3D":{"SideBySide":"Sets the layout mode of the graphic elements of the chart for each argument - side-by-side or along the Z axis."},"StiClusteredColumnSeries":{"Width":"Width factor of one bar series. Value 1 is equal to 100%."},"StiCodabarBarCodeType":{"Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code.","Ratio":"Get or sets value, which indicates WideToNarrow ratio."},"StiCode11BarCodeType":{"Checksum":"Gets or sets mode of checksum.","Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code."},"StiCode128aBarCodeType":{"Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code."},"StiCode128AutoBarCodeType":{"Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code."},"StiCode128BarCodeType":{"Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code."},"StiCode128bBarCodeType":{"Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code."},"StiCode128cBarCodeType":{"Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code."},"StiCode39BarCodeType":{"CheckSum":"Gets or sets a mode of the checksum.","Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code.","Ratio":"Get or sets value, which indicates WideToNarrow ratio."},"StiCode39ExtBarCodeType":{"CheckSum":"Gets or sets a mode of the checksum.","Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code.","Ratio":"Get or sets value, which indicates WideToNarrow ratio."},"StiCode93BarCodeType":{"Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code.","Ratio":"Get or sets value, which indicates WideToNarrow ratio."},"StiCode93ExtBarCodeType":{"Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code.","Ratio":"Get or sets value, which indicates WideToNarrow ratio."},"StiColumnSize":{"MaxWidth":"Gets or sets a maximum width of the table column.","MinWidth":"Gets or sets a minimum width of the table column.","Width":"Gets or sets the specified width for the table column. The width will be calculated automatically by the table element if a value equal to zero."},"StiComboBoxControl":{"DropDownStyle":"A value specifying the style.","DropDownWidth":"Width of the of the drop-down portion.","ItemHeight":"Height of an item.","Items":"An object representing the collection of the items.","ItemsBinding":"Data bindings for the items.","MaxDropDownItems":"Maximum number.","MaxLength":"Maximum number of characters allowed.","SelectedIndexChangedEvent":"A script of the SelectedIndexChanged event.","SelectedItemBinding":"Data bindings for the selected item.","SelectedValueBinding":"Data bindings for the selected value.","Sorted":"A value indicates whether the items are sorted.","Text":"A text associated with this control.","TextBinding":"Data bindings for the text."},"StiComponent":{"AfterPrintEvent":"Occurs after the component printing.","Alias":"Indicates the text that will be shown instead of the component name. If the text is not indicated, then the component name is shown.","Anchor":"Gets or sets a value which indicates the mode of linking component location to the parent component size..","BackColor":"The background color of the component.","BeforePrintEvent":"Occurs before printing the component.","Bookmark":"An expression to fill the component Bookmark.","Border":"The appearance and behavior of the component border.","Brush":"The brush, which is used to draw background.","BusinessObject":"Business Object that is used for getting data.","CanBreak":"A value that indicates whether the component can or cannot break its contents on several pages.","CanGrow":"A value indicates that this object can grow.","CanShrink":"A value indicates that this object can shrink.","CellDockStyle":"A type of the cell docking.","CellType":"A type of the cell content.","CheckAlignment":"Determines the location of the check inside the component.","CheckBrush":"The check brush of the component.","Checked":"Indicates whether the component is in the checked state.","CheckedChangedEvent":"Occurs when the Checked state is changed.","CheckSize":"The check size of the component.","ClickEvent":"Occurs when a user clicks the component in the window of the viewer.","ComponentStyle":"A style of a component.","Conditions":"A collection of the conditions.","CornerRadius":"Represents the value to which the corners are rounded.","CountData":"A count of rows for virtual data.","CrossFiltering":"Gets or sets a value which controls applying cross-filters from the current page to that element.","DataRelation":"Link that is used for master-detail reports rendering.","DataSource":"Data source that is used for getting data.","Disabled":"The non-interactive state of the component.","DockStyle":"A type of the component docking.","DoubleClickEvent":"Occurs when a user double clicks the component in the report viewer.","Editable":"A value indicates that a component can be edited in the window of viewer.","Enabled":"Indicates that this component will be available or not.","ExceedMargins":"Gets or sets a value to exceed margins of the component background.","FilterMode":"A logical operation of combining filters for the current band.","FilterOn":"A value that indicates, that filter is turned on.","Filters":"A collection of the data filters.","FixedWidth":"A value which indicates that the cell have fixed width.","Font":"The font used to display text in control.","ForeColor":"The foreground color of this component, which is used to display text.","GetBookmarkEvent":"Occurs when getting the Bookmark for the component.","GetDrillDownReportEvent":"Occurs when it is required to get a report for the Drill-Down operation.","GetHyperlinkEvent":"Occurs when getting the Hyperlink for the component.","GetTagEvent":"Occurs when getting the Tag for a component.","GetToolTipEvent":"Occurs when getting the ToolTip for the component.","GrowToHeight":"A value indicates that the height of this component increases/decreases to the bottom of a container.","Height":"Height of the component.","HorAlignment":"The horizontal alignment of the text that will be displayed on the component.","Hover":"The hover state when a user has placed a cursor above the component.","Hyperlink":"An expression to fill the component Hyperlink.","Icon":"The icon used for the component.","IconAlignment":"The alignment of the icon that will be displayed on the component.","IconBrush":"The icon brush of the component.","IconSet":"The set of icons used in the component.","Interaction":"Interaction options of this component.","Left":"Left position of the component.","Linked":"A value indicates that the object snap to the container is turned on.","Locked":"A value indicates that moving is locked.","Margin":"Specifies space between this component and another component's margin.","MasterComponent":"The master-component.","MaxSize":"Maximal size.","MinSize":"Minimal size.","MouseEnterEvent":"Occurs when a user enters the mouse into the area of the component in the report viewer.","MouseLeaveEvent":"Occurs when a user leaves the mouse out of the area of the component in the report viewer.","Name":"Indicates the name which is used to identify the component.","Padding":"Specifies the interior spacing of the component.","Pressed":"The pressed state when a user pressed on the component.","Printable":"A value that indicates whether a component is printable or not.","PrintOn":"A value that indicates on which pages will a component be printed.","Restrictions":"A value that indicates the restrictions of a component.","Shadow":"Shadow settings of the component.","ShapeType":"Indicates which shape is used to draw the form of the component.","ShiftMode":"A value that indicates the shift mode of a component.","Sort":"The array of strings that describes rules of sorting.","Stretch":"Describes how the component is resized to fill its allocated space.","Tag":"An expression to fill the component tag.","Text":"The text is associated with the component.","TextBrush":"The brush of the component, which is used to display text.","ThreeStates":"Indicates that control supports two or three states.","ToolTip":"An expression to fill the component ToolTip.","Top":"Top position of the component.","TotalLabel":"Gets or sets a value that is displayed as a label for a total.","Type":"Describes the type of that component behaviour.","UseParentStyles":"A value indicates that this component must use styles from the parent component.","VertAlignment":"The vertical alignment of the text that will be displayed on the component.","VisualStates":"Manages the different visual states of the component.","Watermark":"The watermark used for the component.","Width":"Width of the component.","WordWrap":"Indicates that the multiline text wraps words or not."},"StiConstantLines":{"Antialiasing":"A value that control antialiasing drawing mode.","AxisValue":"Axis value through what the constant line is drawn. Sample: 1, yyyy.MM.dd","Font":"A font that is used for drawing constant line text.","LineColor":"A color which will be used for drawing constant line.","LineStyle":"Constant line style.","LineWidth":"Constant line width.","Orientation":"Horizontal or vertical orientation of constant line.","Position":"A text position at constant line.","ShowBehind":"A value indicates that constant lines will be shown behind chart series or in front of chart series.","ShowInLegend":"Constant lines in chart legend.","Text":"A constant line text.","TitleVisible":"Visibility of constant lines title.","Visible":"Visibility of constant line."},"StiCornerRadius":{"BottomLeft":"The value that specifies the radius of the bottom-left corner.","BottomRight":"The value that specifies the radius of the bottom-right corner.","TopLeft":"The value that specifies the radius of the top-left corner.","TopRight":"The value that specifies the radius of the top-right corner."},"StiCrossDataBand":{"MaxWidth":"Maximal width of a band.","MinWidth":"Minimal width of a band."},"StiCrossField":{"DisplayValue":"An expression that is used for calculation of a cell value which will be output in the table.","EnumeratorSeparator":"The enumerator separator.","EnumeratorType":"The enumerator type.","GetCrossValueEvent":"A script of the event GetValueEvent.","GetDisplayCrossValueEvent":"A script of the event GetDisplayCrossValueEvent.","HideZeros":"A value indicates that no need show zeroes.","KeepMergedCellsTogether":"A value indicates that merged cells will not separate to next page.","MergeHeaders":"A value indicates that all equal values of header will be merged into one.","PrintOnAllPages":"A value indicates that the component is printed on all pages.","ShowPercents":"A value indicates that value in cell must be shown as percents.","ShowTotal":"A value that indicates whether it is necessary to output totals or not.","SortDirection":"The sorting direction.","SortType":"A type of sorting.","Summary":"A type of values summation.","SummaryValues":"A type of zeros and nulls values summation.","UseStyleOfSummaryInColumnTotal":"A value indicates that style of summary cell will be used in column total.","UseStyleOfSummaryInRowTotal":"A value indicates that style of summary cell will be used in row total.","Value":"An expression that is used for calculation of a cell value."},"StiCrossFooterBand":{"MaxWidth":"Maximal width of a band.","MinWidth":"Minimal width of a band."},"StiCrossGroupFooterBand":{"MaxWidth":"Maximal width of a band.","MinWidth":"Minimal width of a band."},"StiCrossGroupHeaderBand":{"MaxWidth":"Maximal width of a band.","MinWidth":"Minimal width of a band."},"StiCrossHeaderBand":{"MaxWidth":"Maximal width of a band.","MinWidth":"Minimal width of a band."},"StiCrossTab":{"EmptyValue":"A string value that is used to show the cross-tab in empty cells.","HorAlignment":"Horizontal alignment of an Cross-Tab.","KeepCrossTabTogether":"A value indicates that the CrossTab must to be kept together with DataBand on what it is placed.","PrintIfEmpty":"A value indicates that the cross-tab is printed if data is not present.","RightToLeft":"Horizontal CrossTab direction.","Wrap":"A value indicates that the cross-tab is to be output in one column. At the same time, everything that do not fit by the width is output below. For using this property it is necessary to enable the CanBreak property of the band on which the cross-tab is placed (if it is placed on the band).","WrapGap":"Space between two parts of a wrapped cross-tab. The property is used jointly with the property Wrap."},"StiCrossTabStyle":{"AlternatingCellBackColor":"The property defines the color which will be used to fill the background of the even rows.","AlternatingCellForeColor":"The property defines the color which will be used for drawing of the values in the even rows.","CellBackColor":"The property defines the color which will be used to fill the background of the cells.","CellForeColor":"The property defines the color which will be used for drawing of the values in the cells.","ColumnHeaderBackColor":"The property defines the color which will be used to fill the background of the column header cells.","ColumnHeaderForeColor":"The property defines the color which will be used for drawing of the text in the column header cells.","HotColumnHeaderBackColor":"The property defines the color which will be used to fill the background of the column header cell when the cursor is hovered on it.","HotRowHeaderBackColor":"The property defines the color which will be used to fill the background of the row header cell when the cursor is hovered on it.","LineColor":"The property defines the color which will be used for drawing cell borders.","RowHeaderBackColor":"The property defines the color which will be used to fill the background of the row header cells.","RowHeaderForeColor":"The property defines the color which will be used for drawing of the text in the row header cells.","SelectedCellBackColor":"The property defines the color which will be used to fill the background of the selected cell.","SelectedCellForeColor":"The property defines the color which will be used for drawing of the values in the selected cells.","TotalCellColumnBackColor":"The property defines the color which will be used to fill the background of the total cell column.","TotalCellColumnForeColor":"The property defines the color which will be used for drawing of the values in the total cell column.","TotalCellRowBackColor":"The property defines the color which will be used to fill the background of the total cell row.","TotalCellRowForeColor":"The property defines the color which will be used for drawing of the values in the total cell row."},"StiDataBand":{"BeginRenderEvent":"Occurs when band begin render.","CalcInvisible":"A value indicates that, when aggregate functions calculation, it is nesessary to take into consideration invisible data bands.","ColumnDirection":"Direction of the rendeting columns.","ColumnGaps":"Distance between two columns.","Columns":"Number of columns.","ColumnWidth":"Width of column.","EndRenderEvent":"Occurs when band rendering is finished.","EvenStyle":"A value that indicates style of even lines.","FilterEngine":"A value which indicates how a filter will be applied to data - be means of the report generator or by means of changing the SQL query.","FilterMode":"A filter mode.","GapAfterLastColumn":"The property allows to add gap after the last column.","GetCollapsedEvent":"Occurs when the Collapsed value is calculated.","KeepChildTogether":"A value indicates that childs are to be kept together.","KeepDetailsTogether":"A value indicates that details are to be kept together with this DataBand.","KeepFooterTogether":"A value indicates that the footer is printed with data.","KeepGroupTogether":"A value indicates that the group is to be kept together.","KeepHeaderTogether":"A value indicates that the header is printed with data together.","MinRowsInColumn":"Minimum number of rows in one column.","MultipleInitialization":"Initialize the data source for each container and detail section. For example, Filters will be applied for each detail section even if Relation is not assigned.","OddStyle":"A value that indicates style of odd lines.","PrintIfDetailEmpty":"A value indicates that if detail is empty then the master data must be printed anyway.","PrintOnAllPages":"A value indicates that the component is printed on all pages.","RenderingEvent":"Occurs when a data row rendering.","ResetDataSource":"A value that indicates to reset Data Source position to begin when preparation for rendering.","RightToLeft":"Horizontal column direction."},"StiDatabase":{"Alias":"An alias of the database.","ConnectedEvent":"Occurs when connection is activated.","ConnectingEvent":"Occurs when connection is being activated.","ConnectionString":"A connection string which contains SQL connection parameters.","DisconnectedEvent":"Occurs when connection is deactivated.","DisconnectingEvent":"Occurs when connection is being deactivated.","Name":"A name of the database.","PathData":"A path to the XML data.","PathSchema":"A path to the XML schema.","PromptUserNameAndPassword":"A value which indicates that UserName and Password parameters should be requested from user."},"StiDataColumn":{"Alias":"An alias of column data.","Expression":"An expression of the calculated column.","Name":"A column name which will be used in report.","NameInSource":"A column original name in database.","Type":"A type of column data."},"StiDataMatrixBarCodeType":{"EncodingType":"Gets or sets type of encoding type.","Height":"Gets os sets height factor of the bar code.","MatrixSize":"Gets or sets matrix size.","Module":"Gets or sets width of the most fine element of the bar code.","UseRectangularSymbols":"Gets or sets value which indicates will RectangularSymbols be used or not."},"StiDataParameter":{"Alias":"An alias of the data parameter.","Expression":"An expression of the calculated column.","Name":"A name of the data parameter.","Size":"A size of the parameter.","Type":"A type of the data parameter."},"StiDataRelation":{"Alias":"An alias of relation.","ChildColumns":"A collection of child column names.","ChildSource":"A Child data source.","Name":"A relation name which will be used in report.","NameInSource":"A name of relation in database.","ParentColumns":"A collection of parent column names.","ParentSource":"A Parent data source."},"StiDataSource":{"Alias":"A Data Source alias.","AllowExpressions":"A value which indicates that sql query of the data source can contain script expressions or no.","CodePage":"A code page.","Columns":"A column collection.","CommandTimeout":"A number of seconds to wait while attempting to execute a command, before canceling the attempt and generate an error. Default is 30.","ConnectionOrder":"A value that indicates in what order that data source will be connect to the data.","ConnectOnStart":"A value indicates that data source is not connected to the data automatically.","Name":"A Data Source name which will be used in report.","NameInSource":"A name of the Data Source in database.","Parameters":"The parameter collection of the SQL query.","Path":"A path to the data file.","ReconnectOnEachRow":"A value which indicates that data source reconnect on each master row in master-detail reports.","SqlCommand":"A SQL statement to execute at the Data Source.","Type":"A value which indicates type of sql data source."},"StiDateTimePickerControl":{"CustomFormat":"A custom date-time format string.","DropDownAlign":"An alignment of the drop-down calendar on the date-time picker control.","Format":"A format of the date and time displayed in the control.","MaxDate":"Maximum date and time that can be selected in the control.","MaxDateBinding":"Data bindings for the max date.","MinDate":"Minimum date and time that can be selected in the control.","MinDateBinding":"Data bindings for the min date.","ShowUpDown":"A value indicates whether an up-down control is used to adjust the date-time value.","Today":"A value that indicates the date is set equal current date.","Value":"A date-time value assigned to the control.","ValueBinding":"Data bindings for the value.","ValueChangedEvent":"A script of the event ValueChanged."},"StiDialogStyle":{"GlyphColor":"A glyph color of the style.","HotBackColor":"A background color of a hot of the style.","HotForeColor":"A foreground color of a hot item of the style.","HotGlyphColor":"A glyph color of a hot item of the style.","HotSelectedBackColor":"A background color of a hot selected item of the style.","HotSelectedForeColor":"A foreground color of a hot selected item of the style.","HotSelectedGlyphColor":"A glyph color of a hot selected item of the style.","SelectedBackColor":"A background color of a selected item of the style.","SelectedForeColor":"A foreground color of a selected item of the style.","SelectedGlyphColor":"A glyph color of a selected item of the style.","SeparatorColor":"A color for drawing of the style."},"StiDoughnutSeries":{"Diameter":"Fixed size of diameter of doughnut series."},"StiDrillDownParameter":{"Expression":"An expression to fill the drill-down parameter.","Name":"A name of drill-down parameter."},"StiDutchKIXBarCodeType":{"Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code."},"StiDynamicBand":{"BreakIfLessThan":"A value that indicates how much free space is on a page (in per cent) should be reserved for formation of a new page or a new column. The value should be set in the range from 0 to 100. If the value is 100 then, in any case, a new page or a new column will be formed. This property is used together with NewPageBefore, NewPageAfter, NewColumnBefore, NewColumnAfter properties.","NewColumnAfter":"If the value of this property is true, then, after output of a band, a new column will be generated.","NewColumnBefore":"If the value of this property is true, then, before output of a band, a new column will be generated.","NewPageAfter":"If the value of this property is true, then, after output of a band, a new page will be generated.","NewPageBefore":"If the value of this property is true, then, before output of a band, a new page will be generated. Output of a band will be continued on the next page.","PrintAtBottom":"A value indicates that the footer is printed at bottom of page.","SkipFirst":"If the value of this property is true, then, a new page/column will be generated only starting from the second case."},"StiEAN128aBarCodeType":{"Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code."},"StiEAN128AutoBarCodeType":{"Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code."},"StiEAN128bBarCodeType":{"Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code."},"StiEAN128cBarCodeType":{"Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code."},"StiEAN13BarCodeType":{"Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code.","ShowQuietZoneIndicator":"Gets or sets value which indicates will show Quiet Zone Indicator or no.","SupplementCode":"Gets or sets the component supplement bar code.","SupplementType":"Gets or sets type of supplement code."},"StiEAN8BarCodeType":{"Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code.","ShowQuietZoneIndicator":"Gets or sets value which indicates will show Quiet Zone Indicator or no.","SupplementCode":"Gets or sets the component supplement bar code.","SupplementType":"Gets or sets type of supplement code."},"StiEmptyBand":{"BeginRenderEvent":"Occurs when band begin render.","EndRenderEvent":"Occurs when band rendering is finished.","EvenStyle":"A value that indicates style of even lines.","OddStyle":"A value that indicates style of odd lines.","RenderingEvent":"Occurs when data row rendering.","SizeMode":"This property allows to indicate how to change the size of the last row on a page."},"StiFIMBarCodeType":{"AddClearZone":"Gets or sets value which indicates will show Clear Zone or no.","Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code."},"StiFooterBand":{"KeepFooterTogether":"A value indicates that the footer is printed with data.","PrintIfEmpty":"A value indicates that the footer if data not present.","PrintOnAllPages":"A value indicates that the component is printed on all pages."},"StiForm":{"BackColor":"A background color for the control.","ClickEvent":"A script of the Click event.","ClosedFormEvent":"A script of the ClosedForm event.","ClosingFormEvent":"A script of the ClosingForm event.","Font":"A font of the control.","LoadFormEvent":"A script of the LoadForm event.","Location":"The coordinates of the upper-left corner of the form.","RightToLeft":"A value indicates whether control's elements are aligned to support locales using right-to-left fonts.","Size":"Size of the form.","StartMode":"A value that indicates time when form appears.","StartPosition":"The starting position of the form at run time.","Text":"A text associated with this control.","Visible":"A value indicates whether the control is displayed.","WindowState":"The form's window state."},"StiGanttSeries":{"GetListOfValuesEndEvent":"Occurs when getting the list of end values.","GetValueEndEvent":"Occurs when getting the property Value.","ListOfValues":"An expression to fill a list of start values. Example: 1;2;3","ListOfValuesEnd":"An expression to fill a list of end values. Example: 1;2;3","Value":"The start value expression. Example: {Order.Value}","ValueDataColumn":"A name of the column that contains the start value.","ValueDataColumnEnd":"A name of the column that contains the end value.","ValueEnd":"The end value expression. Example: {Order.Value}"},"StiGaugeElement":{"Font":"Font.","ForeColor":"Fore color.","Series":"Gauge Series.","Style":"Gauge Style.","Title":"Gauge Title."},"StiGaugeStyle":{"BorderColor":"Gets or sets border color.","BorderWidth":"A border size.","Brush":"Gets or sets a brush to fill the style.","ForeColor":"Gets or sets a brush to draw the text.","LinearBarBorderBrush":"Gets or sets a border of the LinearBar.","LinearBarBrush":"Gets or sets a brush LinearBar.","LinearBarEmptyBorderBrush":"Gets or sets a empty border of the LinearBar.","LinearBarEmptyBrush":"Gets or sets a empty brush LinearBar.","LinearScaleBrush":"Gets or sets a brush LinearScale.","MarkerBrush":"Gets or sets a brush marker.","NeedleBorderBrush":"Gets or sets a border of the Needle.","NeedleBorderWidth":"A border size.","NeedleBrush":"Gets or sets a brush Needle.","NeedleCapBorderBrush":"Gets or sets a border of the cap.","NeedleCapBrush":"Gets or sets a brush to fill the cap.","RadialBarBorderBrush":"Gets or sets a border of the RadialBar.","RadialBarBrush":"Gets or sets a brush RadialBar.","RadialBarEmptyBorderBrush":"Gets or sets a empty border of the RadialBar.","RadialBarEmptyBrush":"Gets or sets a empty brush RadialBar.","TargetColor":"Gets or sets target color.","TickLabelMajorFont":"Gets or sets a font for drawing TickLabelMajor.","TickLabelMajorTextBrush":"Gets or sets a brush to draw the text.","TickLabelMinorFont":"Gets or sets a font for drawing TickLabelMinor.","TickLabelMinorTextBrush":"Gets or sets a brush to draw the text.","TickMarkMajorBorder":"Gets or sets a border of the TickMarkMajor.","TickMarkMajorBorderWidth":"A border size.","TickMarkMajorBrush":"Gets or sets a brush to fill the TickMarkMajor.","TickMarkMinorBorder":"Gets or sets a border of the TickMarkMinor.","TickMarkMinorBorderWidth":"A border size.","TickMarkMinorBrush":"Gets or sets a brush to fill the TickMarkMinor."},"StiGlareBrush":{"Angle":"The angle, measured in degrees clockwise from the x-axis, of the gradient's orientation line.","EndColor":"The end color for the gradient.","Focus":"A value from 0 through 1 that specifies the center of the gradient (the point where the gradient is composed of only the ending color).","Scale":"A value from 0 through 1 that specifies how fast the colors falloff from the focus.","StartColor":"The start color for the gradient."},"StiGlassBrush":{"Blend":"A blend factor.","Color":"The color of the brush.","DrawHatch":"A value that indicates draw hatch at background or not."},"StiGradientBrush":{"Angle":"The angle, measured in degrees clockwise from the x-axis, of the gradient's orientation line.","EndColor":"The end color for the gradient.","StartColor":"The start color for the gradient."},"StiGridColumn":{"Alignment":"Horizontal alignment of column content.","DataTextField":"A name of data column to be shown.","HeaderText":"Header text of column.","NullText":"A text that will be shown instead null values.","Visible":"Visiblity of column.","Width":"Width of column."},"StiGridControl":{"AlternatingBackColor":"A background color of alternating rows for a ledger appearance.","BackColor":"A background color of the grid.","BackgroundColor":"A color of the non-row area of the grid.","ColumnHeadersVisible":"A value indicates whether the column headers a table are visible.","Columns":"A column collection.","Filter":"A filter string.","ForeColor":"A foreground color (typically the color of the text) property.","GridLineColor":"A color of the grid lines.","GridLineStyle":"A line style of the grid.","HeaderBackColor":"A background color of all row and column headers.","HeaderFont":"A font used for column headers.","HeaderForeColor":"A foreground color of headers.","PositionChangedEvent":"A script of the event PositionChanged.","PreferredColumnWidth":"Default width of the grid columns in pixels.","PreferredRowHeight":"The preferred row height.","RowHeadersVisible":"A value that specifies whether row headers are visible.","RowHeaderWidth":"Width of row headers.","SelectionBackColor":"A background color of selected rows.","SelectionForeColor":"A foreground color of selected rows."},"StiGridLines":{"Color":"Color which will be used for drawing major grid lines.","MinorColor":"Color which will be used for drawing minor grid lines.","MinorCount":"Count of minor grid lines per each major grid line.","MinorStyle":"Style which will be used for drawing minor grid lines.","MinorVisible":"Visibility of minor grid lines.","Style":"Style which will be used for drawing major grid lines.","Visible":"Visibility of major grid lines."},"StiGroupBoxControl":{"Text":"A text associated with this control.","TextBinding":"Data bindings for the text."},"StiGroupFooterBand":{"KeepGroupFooterTogether":"A value indicates that the group footer is printed with data."},"StiGroupHeaderBand":{"BeginRenderEvent":"Occurs when band begins to render.","Condition":"Grouping condition.","EndRenderEvent":"Occurs when ends rendering band.","GetCollapsedEvent":"Occurs when the Collapsed value is calculated.","GetSummaryExpressionEvent":"Occurs when when group summary expression is calculated.","GetValueEvent":"Occurs when condition of the group is checked.","KeepGroupHeaderTogether":"A value indicates that group header is printed together with data.","KeepGroupTogether":"A value indicates that a group is to be kept together.","PrintOnAllPages":"A value indicates that the component is printed on all pages.","RenderingEvent":"Occurs when occurs rendering one line of data.","SortDirection":"Sorting direction of grouped data.","SummaryExpression":"Gets or sets summary expression which is used for group summary totals calculation.","SummarySortDirection":"Gets or sets function of calculating group totals for its sorting by totals.","SummaryType":"Gets or sets the sorting direction of grouped data."},"StiHatchBrush":{"BackColor":"The color of spaces between the hatch lines drawn by this brush.","ForeColor":"The color of hatch lines drawn by this brush.","Style":"The hatch style of this brush."},"StiHeaderBand":{"KeepHeaderTogether":"A value indicates that header is printed with data together.","PrintIfEmpty":"A value indicates that the header if data not present.","PrintOnAllPages":"A value indicates that the component is printed on all pages."},"StiHeatmapStyleData":{"Color":"This property defines the color which will be used to fill the geographic object Map, which contains the max value.","Mode":"The property defines the mode which will be used to form the colors collection of the heatmap.","ZeroColor":"This property defines the color which will be used to fill the geographic object Map, which contains the zero value."},"StiHeatmapWithGroupStyleData":{"Colors":"The property allows the creation of a collection of colors. Each color of the collection will be used as the primary color of the specific group.","Mode":"The property defines the mode which will be used to form the colors collection of the heatmap.","ZeroColor":"This property defines the color which will be used to fill the geographic object Map, which contains the zero value."},"StiHierarchicalBand":{"Footers":"A list of footers for the hierarchical band.","Headers":"A list of headers for the hierarchical band.","Indent":"Indent from the left side of band for offset of data levels.","KeyDataColumn":"A column that contains data key.","MasterKeyDataColumn":"A column that contains data master key.","ParentValue":"A column that contains parent value which identifies parent rows."},"StiHorChartGridLines":{"Visible":"Visibility of major grid lines."},"StiImage":{"DataColumn":"A name of the column that contains an image.","File":"The path to the file that contains an image.","GetImageDataEvent":"Occurs when getting image for the component.","GetImageURLEvent":"Occurs when getting image url for the component.","GlobalizedName":"A value that is used as a key for the Globalization Manager.","Image":"The image.","ImageData":"The expression to fill a component image property.","ImageRotation":"A value that indicates how to rotate an image before output.","ImageURL":"The expression to fill a component image URL.","ProcessingDuplicates":"A value that indicates how the report engine processes duplicated images."},"StiIndicatorStyle":{"GlyphColor":"The property defines the color which will be used to draw icon.","HotBackColor":"The property defines the color which will be used to fill indicator background when cursor is hovered.","NegativeColor":"The property defines the color which will be used for variation value when it is less than zero.","PositiveColor":"The property defines the color which will be used for variation value when it is more than zero."},"StiInteraction":{"AllowSeries":"A value that indicates whether the Drill-Down operation can be executed for Series.","AllowSeriesElements":"A value that indicates whether the Drill-Down operation can be executed for Series Elements.","Bookmark":"An expression to fill a component bookmark.","DrillDownEnabled":"A value that indicates whether the Drill-Down operation can be executed.","DrillDownMode":"Mode of opening Drill-Down page.","DrillDownPage":"A page for the Drill-Down operation.","DrillDownParameter1":"The first Drill-Down parameter.","DrillDownParameter2":"The second Drill-Down parameter.","DrillDownParameter3":"The third Drill-Down parameter.","DrillDownParameter4":"The fourth Drill-Down parameter.","DrillDownParameter5":"The fifth Drill-Down parameter.","DrillDownReport":"A path to a report for the Drill-Down operation.","Hyperlink":"An expression to fill a component hyperlink.","SortingColumn":"A column by what data should be re-sorted in the report viewer.","SortingEnabled":"A value that indicates whether it is allowed or not, using given component, data re-sorting in the report viewer.","Tag":"An expression to fill a component tag.","ToolTip":"An expression to fill a component ToolTip."},"StiInterlacing":{"InterlacedBrush":"A brush that is used for drawing interlacing bar.","Visible":"Visibility of interlaced bars."},"StiInterleaved2of5BarCodeType":{"Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code.","Ratio":"Get or sets value, which indicates WideToNarrow ratio."},"StiIsbn10BarCodeType":{"Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code.","ShowQuietZoneIndicator":"Gets or sets value which indicates will show Quiet Zone Indicator or no.","SupplementCode":"Gets or sets the component supplement bar code.","SupplementType":"Gets or sets type of supplement code."},"StiIsbn13BarCodeType":{"Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code.","ShowQuietZoneIndicator":"Gets or sets value which indicates will show Quiet Zone Indicator or no.","SupplementCode":"Gets or sets the component supplement bar code.","SupplementType":"Gets or sets type of supplement code."},"StiITF14BarCodeType":{"Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code.","PrintVerticalBars":"Get or sets value, which indicates, print or not vertical sections.","Ratio":"Get or sets value, which indicates WideToNarrow ratio."},"StiJan13BarCodeType":{"Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code.","ShowQuietZoneIndicator":"Gets or sets value which indicates will show Quiet Zone Indicator or no.","SupplementCode":"Gets or sets the component supplement bar code.","SupplementType":"Gets or sets type of supplement code."},"StiJan8BarCodeType":{"Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code.","ShowQuietZoneIndicator":"Gets or sets value which indicates will show Quiet Zone Indicator or no.","SupplementCode":"Gets or sets the component supplement bar code.","SupplementType":"Gets or sets type of supplement code."},"StiLabelControl":{"Text":"A text associated with this control.","TextAlign":"An alignment of text in the label.","TextBinding":"Data bindings for the text."},"StiLegend":{"BorderColor":"A border color.","Brush":"A background brush of legend.","Columns":"Amount of columns.","Direction":"Direction which used for series drawing in legend.","Font":"A font which used for series title drawing in chart legend.","HideSeriesWithEmptyTitle":"A value which shows/hides series with empty title.","HorAlignment":"Horizontal alignment of legend placement.","HorSpacing":"Horizontal spacing between items in legend.","LabelsColor":"A color of the labels.","MarkerAlignment":"Alignment of markers related to series title.","MarkerBorder":"A value which indicates that marker border will be shown.","MarkerSize":"Marker size.","MarkerVisible":"Visibility of markers.","ShowShadow":"A value that indicates draw shadow or no.","Size":"Size of legend.","Title":"A title of the legend.","TitleColor":"Title color of legend.","TitleFont":"A title font of the chart legend.","VertAlignment":"Vertical alignment of legend placement.","VertSpacing":"Vertical spacing between items in legend.","Visible":"Visibility of chart legend."},"StiLinePrimitive":{"Color":"A line color.","EndCap":"The end cap settings.","Size":"Size of the line.","StartCap":"The end cap settings.","Style":"A pen style."},"StiListBoxControl":{"ItemHeight":"Height of an item in the ListBox.","Items":"Items of the ListBox.","ItemsBinding":"Data bindings for the items.","SelectedIndexBinding":"Data bindings for the selected index.","SelectedIndexChangedEvent":"A script of the event SelectedIndexChanged.","SelectedItemBinding":"Data bindings for the selected item.","SelectedValueBinding":"Data bindings for the selected value.","SelectionMode":"A method in what items are selected in the ListBox.","Sorted":"A value indicates whether the items in the ListBox are sorted alphabetically."},"StiListViewControl":{"SelectedIndexChangedEvent":"Occurs when the SelectedIndex property has changed."},"StiLookUpBoxControl":{"Keys":"An object representing the collection of the keys.","KeysBinding":"Data bindings for the keys.","SelectedKeyBinding":"Data bindings for the selected key."},"StiMapStyle":{"BackColor":"The property defines the color which will be used to fill map background.","BorderColor":"The property defines the color which will be used to draw borders of the map geographic objects.","BorderSize":"The property defines the border width which will be used for the map geographic objects.","BubbleBackColor":"The property defines the color which will be used to fill map bubbles.","BubbleBorderColor":"The property defines the color which will be used to draw borders of the map bubbles.","Colors":"The property allows to create colors set which will be used to fill the map geographic objects when color each mode is used.","DefaultColor":"The property defines the color by default which will be used to fill the map geographic objects when the map type is setted any type except individual.","Heatmap":"The properties set define the settings which will be used for heatmap.","HeatmapWithGroup":"The properties set define the settings which will be used for heatmap with group.","IndividualColor":"The property defines the color which will be used to fill the map geographic objects.","LabelForeground":"The property defines the color which will be used to draw labels of the map geographic objects.","LabelShadowForeground":"The property defines the color which will be used to draw label shadow of the map geographic objects."},"StiMargin":{"Bottom":"The value sets a margin from the bottom edge of the element.","Left":"The value sets a margin from the left edge of the element.","Right":"The value sets a margin from the right edge of the element.","Top":"The value sets a margin from the top edge of the element."},"StiMarker":{"Angle":"A rotation angle of the marker.","BorderColor":"A border color of marker.","Brush":"A brush that will be used to fill marker area.","ShowInLegend":"A value indicates that marker will be visible in legend marker.","Size":"Size of the marker.","Step":"Step of the line marker.","Type":"A type of the marker.","Visible":"Visibility of marker."},"StiMsiBarCodeType":{"CheckSum1":"Gets or sets mode of CheckSum1.","CheckSum2":"Gets or sets mode of CheckSum2.","Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code."},"StiNumericUpDownControl":{"Increment":"A value to increment or decrement the up-down control when the up or down buttons are clicked.","Maximum":"Maximum value for the up-down control.","MaximumBinding":"Data bindings for the maximum.","Minimum":"Minimum allowed value for the up-down control.","MinimumBinding":"Data bindings for the minimum.","Value":"A value assigned to the up-down control.","ValueBinding":"Data bindings for the value.","ValueChangedEvent":"A script of the event ValueChanged."},"StiOutsidePieLabels":{"LineLength":"A line length between border of series labels and border of pie series."},"StiPadding":{"Bottom":"The value sets the padding inside the element from the bottom border and its contents.","Left":"The value sets the padding inside the element from the left border and its contents.","Right":"The value sets the padding inside the element from the right border and its contents.","Top":"The value sets the padding inside the element from the top border and its contents."},"StiPage":{"BeginRenderEvent":"Occurs when when a page begins to render.","ColumnBeginRenderEvent":"Occurs when begin rendering a column.","ColumnEndRenderEvent":"Occurs when column rendering ends.","EndRenderEvent":"Occurs when a page render ends.","ExcelSheet":"An expression that is used for generation name of excel sheet.","GetExcelSheetEvent":"Occurs when the ExcelSheet is calculated.","Icon":"A page icon.","LargeHeight":"A value indicates that this page has large height in the designer.","LargeHeightFactor":"Large height factor for the LargeHeight property of this page.","Margins":"Page margins.","MirrorMargins":"A value which mirrors the margins of a page depending on whether the page is an odd or even.","NumberOfCopies":"A value of number of copies of the current page.","Orientation":"Page orientation.","PageHeight":"The total height of the page.","PageWidth":"The total width of the page.","PaperSize":"The page size.","PaperSourceOfFirstPage":"The paper source for the first page. Some printers does not support this feature.","PaperSourceOfOtherPages":"The paper source for the first page. Some printers does not support this feature.","PrintHeadersFootersFromPreviousPage":"A value indicates that, on this page, it is necessary to print headers and footers of the previous page.","PrintOnPreviousPage":"A value indicates that the page will start to be rendered on the free space of the previous page.","RenderingEvent":"Occurs when page rendering.","ResetPageNumber":"Allows resetting page number on this page.","SegmentPerHeight":"The number of segments per height.","SegmentPerWidth":"The number of segments per width.","StopBeforePrint":"The page number. When it is reached then stop rendering. If the property is 0 then rendering of the report will be stopped.","StretchToPrintArea":"A value indicates that, when printing, a page stretches to print area.","TitleBeforeHeader":"A value indicates that it is necessary to put the report title before the page header.","UnlimitedBreakable":"A value indicates that, when printing multiple-sheet cross-reports, columns and strings are to be broken.","UnlimitedHeight":"A value indicates that the page has an unlimited height.","UnlimitedWidth":"A value indicates that the page has an unlimited width."},"StiPanel":{"ColumnGaps":"Distance between two columns.","Columns":"Number of columns.","ColumnWidth":"Width of a column.","RightToLeft":"Horizontal column direction."},"StiPanelControl":{"BorderStyle":"Indicates the border style for the control."},"StiPdf417BarCodeType":{"AspectRatio":"Gets or sets value, which set aspect ratio between horizontal and vertical sides of bar code.","AutoDataColumns":"Gets or sets value which indicates that amount of columns will be calculated automatically.","AutoDataRows":"Gets or sets value which indicates that amount of rows will be calculated automatically.","DataColumns":"Gets or sets amount of data columns.","DataRows":"Gets or sets amound of data rows.","EncodingMode":"Gets or sets type of encoding type.","ErrorsCorrectionLevel":"Gets or sets errors correction level. The higher level is the more information is added to bar code for restoring.","Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code.","RatioY":"Gets or sets vertical ratio of bar code. Value must between 2 and 5."},"StiPharmacodeBarCodeType":{"Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code."},"StiPictureBoxControl":{"BorderStyle":"Indicates the border style for the control.","Image":"An image that the PictureBox displays.","SizeMode":"Indicates how the image is displayed.","TransparentColor":"A transparent color for the image."},"StiPieSeries":{"AllowApplyBorderColor":"A value that allow to use border color from series settings.","AllowApplyBrush":"A value that allows using a brush from series settings.","CutPieList":"An expression to fill a list of cut pie segments. Example: 1;4;6","Diameter":"Fixed size of diameter of pie series.","Distance":"Distance between the center of series and the center of each segment.","GetCutPieListEvent":"Occurs when getting the cut pie list.","StartAngle":"A rotation angle of series."},"StiPlesseyBarCodeType":{"CheckSum1":"Gets or sets mode of CheckSum1.","CheckSum2":"Gets or sets mode of CheckSum2.","Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code."},"StiPostnetBarCodeType":{"Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code.","Space":"Gets or sets space between elements of bar code."},"StiPrinterSettings":{"Collate":"A value indicates that collate mode of printing will be used or not.","Copies":"A number of copies of a report for printing.","Duplex":"A mode of duplex printing.","PrinterName":"A name of printer which will be used for report printing.","ShowDialog":"A value indicates that print dialog will be shown or not."},"StiProgressStyle":{"BandColor":"The property defines the color which will be used to fill the area graphics of the progress value.","SeriesColors":"The property allows to create color set for drawing the progress series.","TrackColor":"The property defines the color which will be used to fill the progress track."},"StiQRCodeBarCodeType":{"ErrorCorrectionLevel":"Gets or sets the error correction level.","Height":"Gets os sets height factor of the bar code.","MatrixSize":"Gets or sets matrix size.","Module":"Gets or sets width of the most fine element of the bar code."},"StiRadarAxis":{"Visible":"Visibility of axis."},"StiRadarAxisLabels":{"Brush":"A brush which will used to fill label area.","DrawBorder":"A value which indicates that label border will be shown."},"StiRadarGridLines":{"Color":"Color which will be used for drawing major grid lines.","MinorColor":"Color which will be used for drawing minor grid lines.","MinorCount":"Count of minor grid lines per each major grid line.","MinorStyle":"Style which will be used for drawing minor grid lines.","MinorVisible":"Visibility of minor grid lines.","Style":"Style which will be used for drawing major grid lines.","Visible":"Visibility of major grid lines."},"StiRadioButtonControl":{"Checked":"A value indicates whether the control is checked.","CheckedBinding":"Data bindings for the checked.","CheckedChangedEvent":"Gets","Text":"A text associated with this control.","TextBinding":"Data bindings for the text."},"StiRectanglePrimitive":{"BottomSide":"A property that indicates indicates to draw bottom side of the rectangle or no.","LeftSide":"A property that indicates indicates to draw left side of the rectangle or no.","RightSide":"A property that indicates indicates to draw fright side of the rectangle or no.","TopSide":"A property that indicates to draw top side of the rectangle or no."},"StiReport":{"AutoLocalizeReportOnRun":"A property that allows automatic localization of the report when running starts.","BeginRenderEvent":"Occurs when the report rendering starts.","CacheAllData":"A value that indicates whether it is necessary to cache all data of the report in one DataSet.","CacheTotals":"The property includes caching the totals of the report which are used with the Totals prefix. This property accelerates the report rendering in case of using several totals with the same input parameters on one band.","CalculationMode":"A method of an expression calculation in the report rendering.","Collate":"A value that can be used for pages collating. The value of the property cannot be less then 1.","ConvertNulls":"A value that shows whether it is necessary to convert null or DBNull.","EndRenderEvent":"Occurs when the report rendering is finished.","EngineVersion":"The current report engine.","ExportedEvent":"Occurs when report ends exporting.","ExportingEvent":"Occurs when report starts exporting.","GlobalizationStrings":"A collection which consists of globalization strings.","NumberOfPass":"Number of passes which the report generator makes while report rendering.","ParametersOrientation":"A value which indicates parameters panel orientation.","ParameterWidth":"Gets or sets a width in pixels of a parameter in the viewer. The default value is used if a zero value is specified.","PreviewMode":"The preview mode of a report.","PreviewSettings":"Controls which will be shown in the Preview Window.","PrintedEvent":"Occurs when report ends printing.","PrinterSettings":"Printer settings.","PrintingEvent":"Occurs when report starts printing.","ReferencedAssemblies":"An array of referenced assemblies.","RenderingEvent":"Occurs when a page is rendered.","ReportAlias":"A report alias.","ReportAuthor":"A report author.","ReportCacheMode":"A value that indicates how the report engine uses the report cache.","ReportDescription":"A report description.","ReportIcon":"A report icon.","ReportImage":"A report image.","ReportName":"A report name.","ReportUnit":"The current unit of the report.","RequestParameters":"Gets or sets value which indicates whether to request parameters from a user before report rendering or render a report with the default value of parameters.","ScriptLanguage":"The current script language.","StopBeforePage":"A page number. When this page is reached then the report rendering is stopped.","StoreImagesInResources":"A value indicates that images from report will be stored as assembly resources.","Styles":"A collection which consists of styles."},"StiReportControl":{"BackColor":"A background color for the control.","ClickEvent":"A script of the Click event.","DataBindings":"Data bindings for the control.","DoubleClickEvent":"A script of the DoubleClick event.","Enabled":"A value indicates whether the control can respond to user interaction.","EnterEvent":"A script of the Enter event.","Font":"A font of the text displayed by the control.","ForeColor":"A foreground color of the control.","GetTagEvent":"A script of the GetTag event.","GetToolTipEvent":"A script of the GetToolTip event.","LeaveEvent":"A script of the Leave event.","Location":"The coordinates of the upper-left corner of the control relative to the upper-left corner of its container.","MouseDownEvent":"A script of the MouseDown event.","MouseEnterEvent":"A script of the MouseEnter event.","MouseLeaveEvent":"A script of the MouseLeave event.","MouseMoveEvent":"A script of the MouseMove event.","MouseUpEvent":"A script of the MouseUp event.","RightToLeft":"A value indicates whether control's elements are aligned to support locales using right-to-left fonts.","Size":"Height and width of the control.","TagValueBinding":"Data bindings for the tag value.","Visible":"A value indicates whether the control is displayed."},"StiReportSummaryBand":{"KeepReportSummaryTogether":"A value indicates that the report summary is printed with data.","PrintIfEmpty":"A value indicates that the footer is printed at the bottom of a page."},"StiReportTitleBand":{"PrintIfEmpty":"A value indicates that the report title band is printed if data is not present."},"StiRichText":{"BackColor":"A back color.","DataColumn":"A name of the column that contains the RTF text.","DataUrl":"The expression to fill a component RTF text.","DetectUrls":"Detection of urls.","FullConvertExpression":"A value indicates that it is necessary to fully convert the expression to the RTF format. Full convertion of expressions slows down the report rendering.","Margins":"Text margins.","WordWrap":"Word wrap.","Wysiwyg":"A value which indicates that it is necessary to use the Wysiwyg mode of the rendering."},"StiRichTextBoxControl":{"Text":"A current text in the rich text box."},"StiRoundedRectanglePrimitive":{"Round":"The factor of rounding."},"StiRoundedRectangleShapeType":{"Round":"The factor of rounding."},"StiRoyalMail4StateBarCodeType":{"CheckSum":"Gets or sets mode of checksum.","Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code."},"StiSeries":{"AllowApplyBrushNegative":"The values indicates that the specific brush for filling negative values will be used.","AllowApplyColorNegative":"This value indicates that the specific color for negative values will be used.","Argument":"An argument expression. Example: {Order.Argument}","ArgumentDataColumn":"A name of the column that contains the argument value.","AutoSeriesColorDataColumn":"A name of the column that contains the color of auto created series. Color must be presented as string.","AutoSeriesKeyDataColumn":"A name of the column that contains the key of auto created series.","AutoSeriesTitleDataColumn":"A name of the column that contains the title of auto created series.","BorderColor":"A border color of series.","Brush":"A brush which will used to fill series.","BrushNegative":"A brush to fill negative values.","Conditions":"A collection of conditions which can be used to change behavior of series.","FilterMode":"The filter mode of series.","Filters":"The filters that are used to filter series values.","Format":"String that is used to format series values.","GetArgumentEvent":"Occurs when getting the property Argument.","GetHyperlinkEvent":"Occurs when getting the Hyperlink for the series.","GetListOfArgumentsEvent":"Occurs when getting the list of arguments.","GetListOfHyperlinksEvent":"Occurs when getting the list of hyperlinks.","GetListOfTagsEvent":"Occurs when getting the list of tags.","GetListOfToolTipsEvent":"Occurs when getting the list of tool tips.","GetListOfValuesEvent":"Occurs when getting the list of values.","GetListOfWeightsEvent":"Occurs when getting the list of weights.","GetTagEvent":"Occurs when getting the Tag for a series.","GetTitleEvent":"Occurs when getting the property Title.","GetToolTipEvent":"Occurs when getting the ToolTip for the series.","GetValueEvent":"Occurs when getting the property Value.","GetWeightEvent":"Occurs when getting the property Weight.","Interaction":"Interaction options of this component.","LabelsOffset":"Vertical labels offset.","Lighting":"A value indicates that light effect will be shown.","LineColor":"Line color of series.","LineColorNegative":"Line color of series for negative values.","LineMarker":"Line marker settings.","LineStyle":"A line style of series.","LineWidth":"Line width of series.","ListOfArguments":"An expression to fill a list of arguments.  Example: 1;2;3","ListOfValues":"An expression to fill a list of values. Example: 1;2;3","ListOfWeights":"An expression to fill a list of values. Example: 1;2;3","Marker":"Marker settings.","NewAutoSeriesEvent":"Occurs when new auto series is created.","PointAtCenter":"A value that indicates where to place center of series segment.","SeriesLabels":"Series labels settings.","ShowInLegend":"A value indicates that series must be shown in legend.","ShowNulls":"A value that indicates whether it is necessary to show the series element, if the value is null.","ShowSeriesLabels":"Series labels output mode.","ShowShadow":"A value that indicates draw shadow or no.","ShowZeros":"A value that indicates whether it is necessary to show the series element, if the value is 0.","SortBy":"A mode of series values sorting.","SortDirection":"Sort direction.","Tension":"A tension factor of series.","Title":"A title of series.","TopmostLine":"A value that indicates whether the line be displayed as a topmost.","TopN":"Parameters of displaying top results.","TrendLine":"Trend line settings.","Value":"A point value expression. Example: {Order.Value}","ValueDataColumn":"A name of the column that contains the value.","Weight":"A weight value expression. Example: {Order.Value}","WeightDataColumn":"A name of the column that contains the weight value.","YAxis":"Y Axis for series on which will output string representation of arguments."},"StiSeriesInteraction":{"AllowSeries":"A value which indicates that the Drill-Down operation can be executed for Series.","AllowSeriesElements":"A value which indicates that the Drill-Down operation can be executed for Series Elements.","DrillDownEnabled":"A value that indicates whether the Drill-Down operation can be executed.","DrillDownPage":"A page for the Drill-Down operation.","DrillDownReport":"A path to a report for the Drill-Down operation.","Hyperlink":"An expression to fill a series hyperlink.","HyperlinkDataColumn":"A name of the column that contains the hyperlink value.","ListOfHyperlinks":"An expression to fill a list of hyperlinks. Example: 1;2;3","ListOfTags":"An expression to fill a list of tags. Example: 1;2;3","ListOfToolTips":"An expression to fill a list of tool tips. Example: 1;2;3","Tag":"An expression to fill a series tag.","TagDataColumn":"A name of the column that contains the tag value.","ToolTip":"An expression to fill a series ToolTip.","ToolTipDataColumn":"A name of the column that contains the tool tip value."},"StiSeriesLabels":{"Angle":"A text rotation angle.","Antialiasing":"A value that control antialiasing drawing mode of a chart title.","AutoRotate":"A value that enables or disables auto rotate mode drawing of series labels.","BorderColor":"A border color of series labels.","Brush":"A brush that will be used to fill area of series labels.","Conditions":"A collection of conditions which can be used to change behavior of series labels.","DrawBorder":"A value that incates that border will be drawn or not.","Font":"A font that will be used to draw series labels.","Format":"Format string that is used for formating series values (if applicable).","LabelColor":"A foreground color of series labels.","LegendValueType":"What type of information will be shown in the legend.","LineColor":"A color of the line.","LineLength":"Length of line between border of series labels and border of series element.","MarkerAlignment":"Marker alignment related to the label text.","MarkerSize":"Marker size.","MarkerVisible":"Vibility of marker.","PreventIntersection":"A value indicates that whether it is necessary to avoid intersections between border of series labels and border of series.","ShowInPercent":"A value indicates that values from series must be shown as percents.","ShowNulls":"A value that indicates whether it is necessary to show the series label, if the value is null.","ShowValue":"A value indicates that values from series will be shown in series labels.","ShowZeros":"A value that indicates whether it is necessary to show the series label, if the value is 0.","Step":"A value that indicates with what steps will labels be shown.","TextAfter":"A text that will be shown after label text.","TextBefore":"A text that will be shown before label text.","UseSeriesColor":"A value indicates that series colors must be used.","ValueType":"What type of information will be shown in series labels.","ValueTypeSeparator":"A string that contain separator for value information (if applicated).","Visible":"Visiblity of series labels.","Width":"Fixed width of series labels.","WordWrap":"Word wrap."},"StiSeriesTopN":{"Count":"The number of output values.","Mode":"Output values mode","OtherText":"Signature for other values.","ShowOther":"The value sets whether to display different values."},"StiShape":{"BorderColor":"A border color.","ShapeType":"A type of the shape.","Size":"Size of the border.","Style":"A pen style."},"StiShapeTypeService":{"Direction":"The arrow direction."},"StiSimpleBorder":{"Color":"A border color.","Side":"Frame borders.","Size":"A border size.","Style":"A border style."},"StiSimpleText":{"GetValueEvent":"Occurs when the text is being prepared for rendering.","GlobalizedName":"A value that is used as a key for the Globalization Manager.","HideZeros":"A value indicates that there is no need to show zeroes.","LinesOfUnderline":"A value indicates that it is necessary underline lines.","MaxNumberOfLines":"The maximum number of lines which specify the limit of the height stretch.","OnlyText":"A value indicates that the text expression contains a text only.","ProcessAt":"A value indicates whether to process a text expression of this component at the end of the page rendering or at the end of the report rendering.","ProcessAtEnd":"A value indicates that a text is processed at the end of the report execution.","ProcessingDuplicates":"A value that indicates how the report engine processes duplicated values.","Text":"A text expression."},"StiSolidBrush":{"Color":"The color of this brush."},"StiSSCC18BarCodeType":{"CompanyPrefix":"Gets or sets the GS1 Company Prefix (7-10 digits).","ExtensionDigit":"Gets or sets the extension digit.","Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code.","SerialNumber":"Gets or sets the Serial Reference Numbers (6-9 digits)."},"StiStandard2of5BarCodeType":{"Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code.","Ratio":"Get or sets value, which indicates WideToNarrow ratio."},"StiStrips":{"Antialiasing":"A value that control antialiasing drawing mode.","Font":"A font of title text.","MaxValue":"Maximal range of strips.","MinValue":"Minimal range of strips.","Orientation":"Horizontal or vertical orientation of strips.","ShowBehind":"A value indicates that strips will be shown behind chart series or in front of chart series.","ShowInLegend":"A value indicates that strips will be shown in legend of chart.","StripBrush":"A brush which will be used for drawing strips.","Text":"A title text.","TitleColor":"A foreground color of title.","TitleVisible":"A visibility of title.","Visible":"Visibility of strips."},"StiSubReport":{"KeepSubReportTogether":"A value indicates that the sub-report must to be kept together with the DataBand on what it is placed.","SubReportPage":"A page that contains sub-report.","UseExternalReport":"A value indicates that this sub-report requires the external report."},"StiTable":{"AutoWidth":"Which range use table component for adjusting columns width.","AutoWidthType":"How to adjust columns width to the table component.","ColumnCount":"A number of columns in the table.","DockableTable":"A value which indicates that the table will be adjusted to the top of the parent component area.","FooterCanBreak":"A value indicates that this footer can break its content.","FooterCanGrow":"A value indicates that the footer of a table can grow its height.","FooterCanShrink":"A value indicates that this footer can shrink its height.","FooterPrintAtBottom":"A value indicates that the footer of a table will be printed at the bottom of page.","FooterPrintIfEmpty":"A value indicates that the footer will be printed if data not present in table.","FooterPrintOn":"A value that indicates how the footer of a table will be printed on the pages.","FooterPrintOnAllPages":"A value indicates that the footer of a table will be printed on all pages.","FooterPrintOnEvenOddPages":"A value that indicates how the footer of a table will be printed on even-odd pages.","FooterRowsCount":"A number of footer rows in the table.","HeaderCanBreak":"A value indicates that this header can break its content.","HeaderCanGrow":"A value indicates that header of table can grow its height.","HeaderCanShrink":"A value indicates that this header can shrink its height.","HeaderPrintAtBottom":"A value indicates that the header of a table will be printed at the bottom of a page.","HeaderPrintIfEmpty":"A value indicates that the header will be print if data is not present in table.","HeaderPrintOn":"A value that indicates how header of table will be print on pages.","HeaderPrintOnAllPages":"A value indicates that the header of a table will be printed on all pages.","HeaderPrintOnEvenOddPages":"A value that indicates how the header of a table will be printed on even-odd pages.","HeaderRowsCount":"A number of header rows in the table.","RowCount":"A number of rows in the table."},"StiTableElement":{"Columns":"Columns displayed in the table","Group":"","SizeMode":"","Style":"Table style.","Title":"Table title."},"StiTableOfContents":{"RightToLeft":"Gets or sets horizontal output direction."},"StiTableStyle":{"AlternatingDataColor":"The property defines the color which will be used to fill the background of the alternating rows.","AlternatingDataForeground":"The property defines the color which will be used for drawing the value in the alternating rows.","DataColor":"The property defines the color which will be used to fill the background of the rows.","DataForeground":"The property defines the color which will be used for drawing the value in the rows.","FooterColor":"The property defines the color which will be used to fill the background of the footer row.","FooterForeground":"The property defines the color which will be used for drawing the values in the footer row.","GridColor":"The property defines the color which will be used for drawing the cell borders.","HeaderColor":"The property defines the color which will be used to fill the background of the header row.","HeaderForeground":"The property defines the color which will be used for drawing the values in the header row.","HotHeaderColor":"The property defines the color which will be used to fill the background of the header row when the cursor is hovered on it.","SelectedDataColor":"The property defines the color which will be used to fill the background of the selected cell.","SelectedDataForeground":"The property defines the color which will be used for drawing the value in the selected cell."},"StiText":{"AllowHtmlTags":"A value indicates that this component allows HTML tags in a text.","Angle":"An angle of a text rotation.","AutoWidth":"A value indicates that this object can change width automatically.","ExcelValue":"An expression that is used to export data to Excel. Only for numeric values.","ExportAsImage":"A value that indicates how content of a text will be exported as an image or as a text.","Font":"A font of component.","GetExcelValueEvent":"Occurs when the ExcelValue is calculated.","HorAlignment":"The text horizontal alignment.","Margins":"Text margins.","RenderTo":"In the property specify the Text component in what the text that is out of the current Text component bound will be continued to be output.","ShrinkFontToFit":"A value indicates that this component descreases font size to fit contents of a component.","ShrinkFontToFitMinimumSize":"A value that indicates minimum font size for ShrinkFontToFit operation.","TextFormat":"The format of the text.","TextOptions":"Options to control of the text showing.","TextQuality":"A value that indicates text quality.","WordWrap":"Word wrap."},"StiTextBoxControl":{"AcceptsReturn":"A value indicates whether pressing ENTER in a multiline TextBox control creates a new line of text in the control or activates the default button for the form.","AcceptsTab":"A value indicates whether pressing the TAB key in a multiline text box control types a TAB character in the control instead of moving the focus to the next control in the tab order.","MaxLength":"Maximum number of characters the user can type into the text box control.","Multiline":"A value indicates whether this is a multiline text box control.","PasswordChar":"A character used to mask characters of a password in a single-line TextBox control.","Text":"A current text in the text box.","TextBinding":"Data bindings for the text.","WordWrap":"Indicates whether a multiline text box control automatically wraps words to the beginning of the next line when necessary."},"StiTextElement":{"SizeMode":"Specifies how the text output in the component area depends on the text size."},"StiTextInCells":{"CellHeight":"Height of the cell.","CellWidth":"Width of the cell.","ContinuousText":"Continuous text flag.","HorSpacing":"Horizontal spacing between cells.","RightToLeft":"Horizontal output direction.","VertSpacing":"Vertical spacing between cells.","WordWrap":"Word wrap."},"StiTextOptions":{"Angle":"An angle of a text rotation.","DistanceBetweenTabs":"Distance between tabs.","FirstTabOffset":"The first tab offset.","HotkeyPrefix":"A type of drawing hot keys.","LineLimit":"A value that shows compleleted lines only.","RightToLeft":"Horizontal output direction.","Trimming":"A type to trim the end of a line.","WordWrap":"Word wrap."},"StiTitle":{"BackColor":"Background color for a title.","Font":"Font of a title.","ForeColor":"Fore color for a title.","HorAlignment":"Horizontal alignment of text.","Text":"Text of a title.","Visible":"Title visibility."},"StiTreeViewControl":{"AfterSelectEvent":"Occurs when the AfterSelect property has changed."},"StiTrendLine":{"LineColor":"A color which will be used for drawing trend line.","LineStyle":"Trend line style.","LineWidth":"Trend line width.","ShowShadow":"A value that indicates draw shadow or no."},"StiUpcABarCodeType":{"Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code.","ShowQuietZoneIndicator":"Gets or sets value which indicates will show Quiet Zone Indicator or no.","SupplementCode":"Gets or sets the component supplement bar code.","SupplementType":"Gets or sets type of supplement code."},"StiUpcEBarCodeType":{"Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code.","SupplementCode":"Gets or sets the component supplement bar code.","SupplementType":"Gets or sets type of supplement code."},"StiUpcSup2BarCodeType":{"Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code.","ShowQuietZoneIndicator":"Gets or sets value which indicates will show Quiet Zone Indicator or no."},"StiUpcSup5BarCodeType":{"Height":"Gets os sets height factor of the bar code.","Module":"Gets or sets width of the most fine element of the bar code.","ShowQuietZoneIndicator":"Gets or sets value which indicates will show Quiet Zone Indicator or no."},"StiVariable":{"Alias":"An alias of the variable.","Category":"A category of the variable.","Description":"A description of the variable.","Name":"A name of the variable."},"StiVertChartGridLines":{"Visible":"Visibility of major grid lines."},"StiView":{"AspectRatio":"A value indicates that an image will save its aspect ratio.","MultipleFactor":"A value to multiply by it an image size.","Smoothing":"The smoothing mode for drawing an image.","Stretch":"A value indicates that this component will stretch the image till an image will get size equal in its size on a page."},"StiWatermark":{"Angle":"An angle of Watermark.","AspectRatio":"A value indicates that this watermark's image will save its aspect ratio.","Enabled":"A value that indicates whether the Watermark should be drawn or not.","Font":"The font of a Watermark.","Image":"A value of the watermark's image.","ImageAlignment":"The watermark's image alignment.","ImageMultipleFactor":"A value to multiply by its image size.","ImageStretch":"A value indicates that this watermark's image will be stretched on the page.","ImageTiling":"The watermark's image should be tiled.","ImageTransparency":"Transparency of the watermark's image.","RightToLeft":"Watermark's output direction.","ShowBehind":"A value that indicates whether Watermark should be drawn behind or in front of page.","ShowImageBehind":"A value that indicates whether the Watermark image should be drawn behind or in front of a page.","Text":"A text of a Watermark.","TextBrush":"The brush of the watermark, which is used to display text."},"StiWinControl":{"BackColor":"The background color for the control.","Font":"The font of the text displayed by the control.","ForeColor":"The foreground color of the control.","Text":"The text associated with this control.","TypeName":"A type name."},"StiXAxis":{"DateTimeStep":"Date time step settings."},"StiXChartAxis":{"ShowEdgeValues":"A value indicates that first and last arguments on axis will be shown anyway.","StartFromZero":"A value indicates that all arguments will be shows from zero.","Title":"Axis title settings."},"StiXChartAxisTitle":{"Direction":"A text direction for axis title drawing."},"StiYChartAxis":{"StartFromZero":"A value indicates that all arguments will be shows from zero."},"StiYChartAxisTitle":{"Direction":"A text direction for axis title drawing."},"StiZipCode":{"Code":"An expression to fill a code of zip code.","ForeColor":"A fore color.","GetZipCodeEvent":"Occurs when getting the code of zip code.","Ratio":"A value that indicates width and height ratio.","Size":"A contour size."},"Universal":{"AllowApplyStyle":"A value indicates that chart style will be used.","Key":"A Key which will be used in variables when user select associated with it Value in GUI.","Label":"A Label which will be displayed instead of Key in GUI.","Value":"A Value which will be displayed instead of the Key value in GUI."}});