var Stimulsoft;
(function (Stimulsoft) {
    var ExternalLibrary;
    (function (ExternalLibrary) {
        //#region constant.js
        const C = {};
        C.SECONDS_A_MINUTE = 60;
        C.SECONDS_A_HOUR = C.SECONDS_A_MINUTE * 60;
        C.SECONDS_A_DAY = C.SECONDS_A_HOUR * 24;
        C.SECONDS_A_WEEK = C.SECONDS_A_DAY * 7;

        C.MILLISECONDS_A_SECOND = 1e3;
        C.MILLISECONDS_A_MINUTE = C.SECONDS_A_MINUTE * C.MILLISECONDS_A_SECOND;
        C.MILLISECONDS_A_HOUR = C.SECONDS_A_HOUR * C.MILLISECONDS_A_SECOND;
        C.MILLISECONDS_A_DAY = C.SECONDS_A_DAY * C.MILLISECONDS_A_SECOND;
        C.MILLISECONDS_A_WEEK = C.SECONDS_A_WEEK * C.MILLISECONDS_A_SECOND;

        // English locales
        C.MS = 'millisecond';
        C.S = 'second';
        C.MIN = 'minute';
        C.H = 'hour';
        C.D = 'day';
        C.W = 'week';
        C.M = 'month';
        C.Q = 'quarter';
        C.Y = 'year';
        C.DATE = 'date';

        C.FORMAT_DEFAULT = 'YYYY-MM-DDTHH:mm:ssZ';

        C.INVALID_DATE_STRING = 'Invalid Date';

        // regex
        C.REGEX_PARSE = /^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/;
        C.REGEX_FORMAT = /'([^']*)'|"([^"]*)"|\\(.)|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|S{1,7}/g;
        //#endregion

        //#region locale/en.js
        // English [en]
        // We don't need weekdaysShort, weekdaysMin, monthsShort in en.js locale
        const en = {
            name: 'en',
            weekdays: 'Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday'.split('_'),
            months: 'January_February_March_April_May_June_July_August_September_October_November_December'.split('_')
        };
        //#endregion

        //#region utils.js
        const padStart = (string, length, pad) => {
            const s = String(string)
            if (!s || s.length >= length) return string
            return `${Array((length + 1) - s.length).join(pad)}${string}`
        }

        const padZoneStr = (instance) => {
            const negMinutes = -instance.utcOffset()
            const minutes = Math.abs(negMinutes)
            const hourOffset = Math.floor(minutes / 60)
            const minuteOffset = minutes % 60
            return `${negMinutes <= 0 ? '+' : '-'}${padStart(hourOffset, 2, '0')}:${padStart(minuteOffset, 2, '0')}`
        }

        const monthDiff = (a, b) => {
            // function from moment.js in order to keep the same result
            if (a.date() < b.date()) return -monthDiff(b, a)
            const wholeMonthDiff = ((b.year() - a.year()) * 12) + (b.month() - a.month())
            const anchor = a.clone().add(wholeMonthDiff, C.M)
            const c = b - anchor < 0
            const anchor2 = a.clone().add(wholeMonthDiff + (c ? -1 : 1), C.M)
            return +(-(wholeMonthDiff + ((b - anchor) / (c ? (anchor - anchor2) :
                (anchor2 - anchor)))) || 0)
        }

        const absFloor = n => (n < 0 ? Math.ceil(n) || 0 : Math.floor(n))

        const prettyUnit = (u) => {
            const special = {
                M: C.M,
                y: C.Y,
                w: C.W,
                d: C.D,
                D: C.DATE,
                h: C.H,
                m: C.MIN,
                s: C.S,
                ms: C.MS,
                Q: C.Q
            }
            return special[u] || String(u || '').toLowerCase().replace(/s$/, '')
        }

        const isUndefined = s => s === undefined

        const U = {
            s: padStart,
            z: padZoneStr,
            m: monthDiff,
            a: absFloor,
            p: prettyUnit,
            u: isUndefined
        }
        //#endregion

        //#region index.js
        let L = 'en'; // global locale
        const Ls = {}; // global loaded locale
        Ls[L] = en;

        const isDayjs = d => d instanceof Dayjs; // eslint-disable-line no-use-before-define

        const parseLocale = (preset, object, isLocal) => {
            let l
            if (!preset) return L
            if (typeof preset === 'string') {
                if (Ls[preset]) {
                    l = preset
                }
                if (object) {
                    Ls[preset] = object
                    l = preset
                }
            } else {
                const { name } = preset
                Ls[name] = preset
                l = name
            }
            if (!isLocal && l) L = l
            return l || (!isLocal && L)
        };

        const dayjs = function (date, c) {
            if (isDayjs(date)) {
                return date.clone()
            }
            // eslint-disable-next-line no-nested-ternary
            const cfg = typeof c === 'object' ? c : {}
            cfg.date = date
            cfg.args = arguments// eslint-disable-line prefer-rest-params
            return new Dayjs(cfg) // eslint-disable-line no-use-before-define
        };

        const wrapper = (date, instance) =>
            dayjs(date, {
                locale: instance.$L,
                utc: instance.$u,
                x: instance.$x,
                $offset: instance.$offset // todo: refactor; do not use this.$offset in you code
            });

        const Utils = U; // for plugin use
        Utils.l = parseLocale;
        Utils.i = isDayjs;
        Utils.w = wrapper;

        const parseDate = (cfg) => {
            const { date, utc } = cfg
            if (date === null) return new Date(NaN) // null is invalid
            if (Utils.u(date)) return new Date() // today
            if (date instanceof Date) return new Date(date)
            if (typeof date === 'string' && !/Z$/i.test(date)) {
                const d = date.match(C.REGEX_PARSE)
                if (d) {
                    const m = d[2] - 1 || 0
                    const ms = (d[7] || '0').substring(0, 3)
                    if (utc) {
                        return new Date(Date.UTC(d[1], m, d[3]
                            || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms))
                    }
                    return new Date(d[1], m, d[3]
                        || 1, d[4] || 0, d[5] || 0, d[6] || 0, ms)
                }
            }

            return new Date(date) // everything else
        };

        const meridiemFunc = (meridiem, hour, minute, isLowercase) => {
            const m = (hour < 12 ? meridiem.AM || 'AM' : meridiem.PM || 'PM')
            return isLowercase ? m.toLowerCase() : m
        }

        class Dayjs {
            constructor(cfg) {
                this.$L = parseLocale(cfg.locale, null, true)
                this.parse(cfg) // for plugin
            }

            parse(cfg) {
                this.$d = parseDate(cfg)
                this.$x = cfg.x || {}
                this.init()
            }

            init() {
                const { $d } = this
                this.$y = $d.getFullYear()
                this.$M = $d.getMonth()
                this.$D = $d.getDate()
                this.$W = $d.getDay()
                this.$H = $d.getHours()
                this.$m = $d.getMinutes()
                this.$s = $d.getSeconds()
                this.$ms = $d.getMilliseconds()
            }

            // eslint-disable-next-line class-methods-use-this
            $utils() {
                return Utils
            }

            isValid() {
                return this.$d instanceof Date && !isNaN(this.$d);
                //return !(this.$d.toString() === C.INVALID_DATE_STRING)
            }

            isSame(that, units) {
                const other = dayjs(that)
                return this.startOf(units) <= other && other <= this.endOf(units)
            }

            isAfter(that, units) {
                return dayjs(that) < this.startOf(units)
            }

            isBefore(that, units) {
                return this.endOf(units) < dayjs(that)
            }

            $g(input, get, set) {
                if (Utils.u(input)) return this[get]
                return this.set(set, input)
            }

            unix() {
                return Math.floor(this.valueOf() / 1000)
            }

            valueOf() {
                // timezone(hour) * 60 * 60 * 1000 => ms
                return this.$d.getTime()
            }

            startOf(units, startOf) { // startOf -> endOf
                const isStartOf = !Utils.u(startOf) ? startOf : true
                const unit = Utils.p(units)
                const instanceFactory = (d, m) => {
                    const ins = Utils.w(this.$u ?
                        Date.UTC(this.$y, m, d) : new Date(this.$y, m, d), this)
                    return isStartOf ? ins : ins.endOf(C.D)
                }
                const instanceFactorySet = (method, slice) => {
                    const argumentStart = [0, 0, 0, 0]
                    const argumentEnd = [23, 59, 59, 999]
                    return Utils.w(this.toDate()[method].apply( // eslint-disable-line prefer-spread
                        this.toDate('s'),
                        (isStartOf ? argumentStart : argumentEnd).slice(slice)
                    ), this)
                }
                const { $W, $M, $D } = this
                const utcPad = `set${this.$u ? 'UTC' : ''}`
                switch (unit) {
                    case C.Y:
                        return isStartOf ? instanceFactory(1, 0) :
                            instanceFactory(31, 11)
                    case C.M:
                        return isStartOf ? instanceFactory(1, $M) :
                            instanceFactory(0, $M + 1)
                    case C.W: {
                        const weekStart = this.$locale().weekStart || 0
                        const gap = ($W < weekStart ? $W + 7 : $W) - weekStart
                        return instanceFactory(isStartOf ? $D - gap : $D + (6 - gap), $M)
                    }
                    case C.D:
                    case C.DATE:
                        return instanceFactorySet(`${utcPad}Hours`, 0)
                    case C.H:
                        return instanceFactorySet(`${utcPad}Minutes`, 1)
                    case C.MIN:
                        return instanceFactorySet(`${utcPad}Seconds`, 2)
                    case C.S:
                        return instanceFactorySet(`${utcPad}Milliseconds`, 3)
                    default:
                        return this.clone()
                }
            }

            endOf(arg) {
                return this.startOf(arg, false)
            }

            $set(units, int) { // private set
                const unit = Utils.p(units)
                const utcPad = `set${this.$u ? 'UTC' : ''}`
                const name = {
                    [C.D]: `${utcPad}Date`,
                    [C.DATE]: `${utcPad}Date`,
                    [C.M]: `${utcPad}Month`,
                    [C.Y]: `${utcPad}FullYear`,
                    [C.H]: `${utcPad}Hours`,
                    [C.MIN]: `${utcPad}Minutes`,
                    [C.S]: `${utcPad}Seconds`,
                    [C.MS]: `${utcPad}Milliseconds`
                }[unit]
                const arg = unit === C.D ? this.$D + (int - this.$W) : int

                if (unit === C.M || unit === C.Y) {
                    // clone is for badMutable plugin
                    const date = this.clone().set(C.DATE, 1)
                    date.$d[name](arg)
                    date.init()
                    this.$d = date.set(C.DATE, Math.min(this.$D, date.daysInMonth())).$d
                } else if (name) this.$d[name](arg)

                this.init()
                return this
            }

            set(string, int) {
                return this.clone().$set(string, int)
            }

            get(unit) {
                return this[Utils.p(unit)]()
            }

            add(number, units) {
                number = Number(number) // eslint-disable-line no-param-reassign
                const unit = Utils.p(units)
                const instanceFactorySet = (n) => {
                    const d = dayjs(this)
                    return Utils.w(d.date(d.date() + Math.round(n * number)), this)
                }
                if (unit === C.M) {
                    return this.set(C.M, this.$M + number)
                }
                if (unit === C.Y) {
                    return this.set(C.Y, this.$y + number)
                }
                if (unit === C.D) {
                    return instanceFactorySet(1)
                }
                if (unit === C.W) {
                    return instanceFactorySet(7)
                }
                const step = {
                    [C.MIN]: C.MILLISECONDS_A_MINUTE,
                    [C.H]: C.MILLISECONDS_A_HOUR,
                    [C.S]: C.MILLISECONDS_A_SECOND
                }[unit] || 1 // ms

                const nextTimeStamp = this.$d.getTime() + (number * step)
                return Utils.w(nextTimeStamp, this)
            }

            subtract(number, string) {
                return this.add(number * -1, string)
            }

            format(formatStr) {
                const locale = this.$locale()

                if (!this.isValid()) return locale.invalidDate || C.INVALID_DATE_STRING

                const str = formatStr || C.FORMAT_DEFAULT
                const zoneStr = Utils.z(this)
                const { $H, $m, $M } = this
                const {
                    weekdays, months, meridiem = { AM: 'AM', PM: 'PM' }
                } = locale
                const getShort = (arr, index, full, length) => (
                    (arr && (arr[index] || arr(this, str))) || full[index].substr(0, length)
                )
                const get$H = num => (
                    Utils.s($H % 12 || 12, num, '0')
                )

                const matches = {
                    Y: String(this.$y).slice(-1),
                    YY: String(this.$y).slice(-2),
                    YYYY: this.$y,
                    M: $M + 1,
                    MM: Utils.s($M + 1, 2, '0'),
                    MMM: getShort(locale.monthsShort, $M, months, 3),
                    MMMM: getShort(months, $M),
                    D: this.$D,
                    DD: Utils.s(this.$D, 2, '0'),
                    d: String(this.$W),
                    dd: getShort(locale.weekdaysMin, this.$W, weekdays, 2),
                    ddd: getShort(locale.weekdaysShort, this.$W, weekdays, 3),
                    dddd: weekdays[this.$W],
                    H: String($H),
                    HH: Utils.s($H, 2, '0'),
                    h: get$H(1),
                    hh: get$H(2),
                    a: meridiemFunc(meridiem, $H, $m, true),
                    A: meridiemFunc(meridiem, $H, $m, false),
                    m: String($m),
                    mm: Utils.s($m, 2, '0'),
                    s: String(this.$s),
                    ss: Utils.s(this.$s, 2, '0'),
                    S: String(Utils.s(this.$ms, 3, '0'))[0],
                    SS: String(Utils.s(this.$ms, 3, '0')).substring(0, 2),
                    SSS: Utils.s(this.$ms, 3, '0'),
                    SSSS: Utils.s(this.$ms, 3, '0') + '0',
                    SSSSS: Utils.s(this.$ms, 3, '0') + '00',
                    SSSSSS: Utils.s(this.$ms, 3, '0') + '000',
                    SSSSSSS: Utils.s(this.$ms, 3, '0') + '0000',
                    Z: zoneStr // 'ZZ' logic below
                }

                return str.replace(C.REGEX_FORMAT, (match, $1, $2, $3) => $1 || $2 || $3 || matches[match] || zoneStr.replace(':', '')) // 'ZZ'
            }

            utcOffset() {
                // Because a bug at FF24, we're rounding the timezone offset around 15 minutes
                // https://github.com/moment/moment/pull/1871
                return -Math.round(this.$d.getTimezoneOffset() / 15) * 15
            }

            diff(input, units, float) {
                const unit = Utils.p(units)
                const that = dayjs(input)
                const zoneDelta = (that.utcOffset() - this.utcOffset()) * C.MILLISECONDS_A_MINUTE
                const diff = this - that
                let result = Utils.m(this, that)

                result = {
                    [C.Y]: result / 12,
                    [C.M]: result,
                    [C.Q]: result / 3,
                    [C.W]: (diff - zoneDelta) / C.MILLISECONDS_A_WEEK,
                    [C.D]: (diff - zoneDelta) / C.MILLISECONDS_A_DAY,
                    [C.H]: diff / C.MILLISECONDS_A_HOUR,
                    [C.MIN]: diff / C.MILLISECONDS_A_MINUTE,
                    [C.S]: diff / C.MILLISECONDS_A_SECOND
                }[unit] || diff // milliseconds

                return float ? result : Utils.a(result)
            }

            daysInMonth() {
                return this.endOf(C.M).$D
            }

            $locale() { // get locale object
                return Ls[this.$L]
            }

            locale(preset, object) {
                if (!preset) return this.$L
                const that = this.clone()
                const nextLocaleName = parseLocale(preset, object, true)
                if (nextLocaleName) that.$L = nextLocaleName
                return that
            }

            clone() {
                return Utils.w(this.$d, this)
            }

            toDate() {
                return new Date(this.valueOf())
            }

            toJSON() {
                return this.isValid() ? this.toISOString() : null
            }

            toISOString() {
                // ie 8 return
                // new Dayjs(this.valueOf() + this.$d.getTimezoneOffset() * 60000)
                // .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
                return this.$d.toISOString()
            }

            toString() {
                return this.$d.toUTCString()
            }
        }

        {
            const proto = Dayjs.prototype
            dayjs.prototype = proto;
            [
                ['$ms', C.MS],
                ['$s', C.S],
                ['$m', C.MIN],
                ['$H', C.H],
                ['$W', C.D],
                ['$M', C.M],
                ['$y', C.Y],
                ['$D', C.DATE]
            ].forEach((g) => {
                proto[g[1]] = function (input) {
                    return this.$g(input, g[0], g[1])
                }
            })
        }

        dayjs.extend = (plugin, option) => {
            if (!plugin.$i) { // install plugin only once
                plugin(option, Dayjs, dayjs)
                plugin.$i = true
            }
            return dayjs
        }

        dayjs.locale = parseLocale

        dayjs.isDayjs = isDayjs

        dayjs.unix = timestamp => (
            dayjs(timestamp * 1e3)
        )

        dayjs.en = Ls[L]
        dayjs.Ls = Ls
        dayjs.p = {}
        ExternalLibrary.dayjs = dayjs;
        //#endregion

        //#region QuarterOfYear
        {
            const quarterOfYear = (o, c) => {
                const proto = c.prototype
                proto.quarter = function (quarter) {
                    if (!this.$utils().u(quarter)) {
                        return this.month((this.month() % 3) + ((quarter - 1) * 3))
                    }
                    return Math.ceil((this.month() + 1) / 3)
                }

                const oldAdd = proto.add
                proto.add = function (number, units) {
                    number = Number(number) // eslint-disable-line no-param-reassign
                    const unit = this.$utils().p(units)
                    if (unit === C.Q) {
                        return this.add(number * 3, C.M)
                    }
                    return oldAdd.bind(this)(number, units)
                }

                const oldStartOf = proto.startOf
                proto.startOf = function (units, startOf) {
                    const utils = this.$utils()
                    const isStartOf = !utils.u(startOf) ? startOf : true
                    const unit = utils.p(units)
                    if (unit === C.Q) {
                        const quarter = this.quarter() - 1
                        return isStartOf ? this.month(quarter * 3)
                            .startOf(C.M).startOf(C.D) :
                            this.month((quarter * 3) + 2).endOf(C.M).endOf(C.D)
                    }
                    return oldStartOf.bind(this)(units, startOf)
                }
            }
            dayjs.extend(quarterOfYear);
        }
        //#endregion

        //#region CustomParseFormat
        {
            //#region plugin/localizedFormat/utils.js
            const englishFormats = {
                LTS: 'h:mm:ss A',
                LT: 'h:mm A',
                L: 'MM/DD/YYYY',
                LL: 'MMMM D, YYYY',
                LLL: 'MMMM D, YYYY h:mm A',
                LLLL: 'dddd, MMMM D, YYYY h:mm A'
            };

            const u = (formatStr, formats) => formatStr.replace(/(LTS?|l{1,4}|L{1,4})/g, (_, b) => {
                const B = b.toUpperCase()
                return formats[b] || englishFormats[b]
            });
            //#endregion

            //#region plugin/localizedFormat/index.js
            const localizedFormat = (o, c, d) => {
                const proto = c.prototype
                const oldFormat = proto.format

                d.en.formats = englishFormats
                proto.format = function (formatStr = C.FORMAT_DEFAULT) {
                    const { formats = {} } = this.$locale()
                    const result = u(formatStr, formats)
                    return oldFormat.call(this, result)
                }
            }
            dayjs.extend(localizedFormat);
            //#endregion

            //#region plugin/customParseFormat/index.js
            const formattingTokens = /'[^']*'|"[^"]*"|\\.|([-:/.()\sTt]+)|(A|a|YYYY|YY?|MM?M?M?|Do|DD?|hh?|HH?|mm?|ss?|S{1,7}|z|ZZ?)/g

            const match1 = /^\d/ // 0 - 9
            const match2 = /^\d\d/ // 00 - 99
            const match3 = /^\d{3}/ // 000 - 999
            const match4 = /^\d{4}/ // 0000 - 9999
            const match5 = /^\d{5}/
            const match6 = /^\d{6}/
            const match7 = /^\d{7}/
            const match1to2 = /^\d\d?/ // 0 - 99
            const matchSigned = /^[+-]?\d+/ // -inf - inf
            const matchOffset = /^[+-]\d\d:?(\d\d)?|Z/ // +00:00 -00:00 +0000 or -0000 +00 or Z
            const matchWord = /^\d*[^\s\d-_:/()]+/ // Word

            let locale = {}

            let parseTwoDigitYear = function (input) {
                input = +input
                return input + (input > 68 ? 1900 : 2000)
            }

            const offsetFromString = (string) => {
                if (!string) return 0
                if (string === 'Z') return 0
                const parts = string.match(/([+-]|\d\d)/g)
                const minutes = +(parts[1] * 60) + (+parts[2] || 0)
                return minutes === 0 ? 0 : parts[0] === '+' ? -minutes : minutes // eslint-disable-line no-nested-ternary
            }

            const addInput = function (property) {
                return function (input) {
                    this[property] = +input
                }
            }

            const zoneExpressions = [matchOffset, function (input) {
                const zone = this.zone || (this.zone = {})
                zone.offset = offsetFromString(input)
            }]

            const getLocalePart = (name) => {
                const part = locale[name]
                return part && (
                    part.indexOf ? part : part.s.concat(part.f)
                )
            }
            const meridiemMatch = (input) => {
                let isAfternoon = false;
                const { meridiem } = locale
                if (!meridiem) {
                    isAfternoon = (input === 'pm' || input === 'PM');
                } else {
                    if (input.indexOf(meridiemFunc(meridiem, 13, 0, false)) > -1 || input.indexOf(meridiemFunc(meridiem, 13, 0, true)) > -1) {
                        isAfternoon = true;
                    }
                }
                return isAfternoon;
            }

            const expressions = {
                A: [matchWord, function (input) {
                    this.afternoon = meridiemMatch(input);
                }],
                a: [matchWord, function (input) {
                    this.afternoon = meridiemMatch(input);
                }],
                S: [match1, function (input) {
                    this.milliseconds = +input * 100
                }],
                SS: [match2, function (input) {
                    this.milliseconds = +input * 10
                }],
                SSS: [match3, function (input) {
                    this.milliseconds = +input
                }],
                SSSS: [match4, function (input) {
                    this.milliseconds = (+input) / 10
                }],
                SSSSS: [match5, function (input) {
                    this.milliseconds = (+input) / 100
                }],
                SSSSSS: [match6, function (input) {
                    this.milliseconds = (+input) / 1000
                }],
                SSSSSSS: [match7, function (input) {
                    this.milliseconds = (+input) / 10000
                }],
                s: [match1to2, addInput('seconds')],
                ss: [match2, addInput('seconds')],
                m: [match1to2, addInput('minutes')],
                mm: [match2, addInput('minutes')],
                H: [match1to2, addInput('hours')],
                h: [match1to2, addInput('hours')],
                HH: [match2, addInput('hours')],
                hh: [match2, addInput('hours')],
                D: [match1to2, addInput('day')],
                DD: [match2, addInput('day')],
                Do: [matchWord, function (input) {
                    const { ordinal } = locale;
                    [this.day] = input.match(/\d+/)
                    if (!ordinal) return
                    for (let i = 1; i <= 31; i += 1) {
                        if (ordinal(i).replace(/\[|\]/g, '') === input) {
                            this.day = i
                        }
                    }
                }],
                M: [match1to2, addInput('month')],
                MM: [match2, addInput('month')],
                MMM: [matchWord, function (input) {
                    const months = getLocalePart('months')
                    const monthsShort = getLocalePart('monthsShort')
                    const matchIndex = (monthsShort || months.map(_ => _.substr(0, 3))).indexOf(input) + 1
                    if (matchIndex < 1) {
                        throw new Error()
                    }
                    this.month = (matchIndex % 12) || matchIndex
                }],
                MMMM: [matchWord, function (input) {
                    const months = getLocalePart('months')
                    const matchIndex = months.indexOf(input) + 1
                    if (matchIndex < 1) {
                        throw new Error()
                    }
                    this.month = (matchIndex % 12) || matchIndex
                }],
                Y: [matchSigned, addInput('year')],
                YY: [match2, function (input) {
                    this.year = parseTwoDigitYear(input)
                }],
                YYYY: [match4, addInput('year')],
                Z: zoneExpressions,
                ZZ: zoneExpressions
            }

            const correctHours = (time) => {
                const { afternoon, hours } = time;
                if (afternoon !== undefined) {
                    if (afternoon) {
                        if (hours < 12) time.hours += 12;
                    } else if (hours === 12) {
                        time.hours = 0;
                    }
                    delete time.afternoon;
                }
            }

            const makeParser = (format) => {
                format = u(format, locale && locale.formats)
                const array = format.match(formattingTokens)
                const { length } = array
                for (let i = 0; i < length; i += 1) {
                    const token = array[i]
                    const parseTo = expressions[token]
                    const regex = parseTo && parseTo[0]
                    const parser = parseTo && parseTo[1]
                    if (parser) {
                        array[i] = { regex, parser, token }
                    } else {
                        array[i] = token
                    }
                }
                return function (input) {
                    const time = {}
                    for (let i = 0; i < length; i += 1) {
                        const token = array[i]
                        if (typeof token === 'string') {
                            if (!input.startsWith(token)) {
                                time.hasErrors = true;
                                break;
                            }
                            input = input.substr(token.length)
                        } else {
                            const { regex, parser } = token
                            const match = regex.exec(input)
                            if (!match || match.index) {
                                time.hasErrors = true;
                                break;
                            }
                            const value = match[0]
                            parser.call(time, value)
                            input = input.substr(value.length)
                        }
                    }
                    if (input) {
                        time.hasErrors = true
                    }
                    correctHours(time)
                    return time
                }
            }

            const parseFormattedInput = (input, format, utc) => {
                try {
                    if (['x', 'X'].indexOf(format) > -1) return new Date((format === 'X' ? 1000 : 1) * input)
                    const parser = makeParser(format)
                    const {
                        year, month, day, hours, minutes, seconds, milliseconds, zone, hasErrors
                    } = parser(input)
                    const now = new Date()
                    const d = day || ((!year && !month) ? now.getDate() : 1)
                    const y = year || now.getFullYear()
                    let M = 0
                    if (!(year && !month)) {
                        M = month > 0 ? month - 1 : now.getMonth()
                    }
                    const h = hours || 0
                    const m = minutes || 0
                    const s = seconds || 0
                    const ms = milliseconds || 0
                    if (zone) {
                        return Object.assign(new Date(Date.UTC(y, M, d, h, m, s, ms + (zone.offset * 60 * 1000))), { hasErrors })
                    }
                    if (utc) {
                        return Object.assign(new Date(Date.UTC(y, M, d, h, m, s, ms)), { hasErrors })
                    }
                    let resultDate = new Date();
                    resultDate.setFullYear(y, M, d);
                    resultDate.setHours(h, m, s, ms);
                    return Object.assign(resultDate, { hasErrors })
                } catch (e) {
                    return new Date('') // Invalid Date
                }
            }

            const customParseFormat = (o, C, d) => {
                d.p.customParseFormat = true
                if (o && o.parseTwoDigitYear) {
                    ({ parseTwoDigitYear } = o)
                }
                const proto = C.prototype;
                const oldParse = proto.parse;
                proto.parse = function (cfg) {
                    const { date, utc, args } = cfg;
                    this.$u = utc;
                    const format = args[1];
                    if (typeof format === 'string') {
                        const isStrictWithoutLocale = args[2] === true
                        const isStrictWithLocale = args[3] === true
                        const isStrict = isStrictWithoutLocale || isStrictWithLocale
                        let pl = args[2]
                        if (isStrictWithLocale) [, , pl] = args
                        locale = this.$locale()
                        if (!isStrictWithoutLocale && pl) {
                            locale = d.Ls[pl]
                        }
                        this.$d = parseFormattedInput(date, format, utc)
                        this.init()
                        if (pl && pl !== true) this.$L = this.locale(pl).$L
                        // use != to treat
                        // input number 1410715640579 and format string '1410715640579' equal
                        // eslint-disable-next-line eqeqeq
                        if (isStrict && this.$d.hasErrors) {
                            this.$d = new Date('')
                        }
                        delete this.$d.hasErrors;
                        // reset global locale to make parallel unit test
                        locale = {}
                    } else if (format instanceof Array) {
                        const len = format.length
                        for (let i = 1; i <= len; i += 1) {
                            args[1] = format[i - 1]
                            const result = d.apply(this, args)
                            if (result.isValid()) {
                                this.$d = result.$d
                                this.$L = result.$L
                                this.init()
                                break
                            }
                            if (i === len) this.$d = new Date('')
                        }
                    } else {
                        oldParse.call(this, cfg)
                    }
                }
            }
            dayjs.extend(customParseFormat);
            //#endregion
        }
        //#endregion
    })(ExternalLibrary = Stimulsoft.ExternalLibrary || (Stimulsoft.ExternalLibrary = {}));
})(Stimulsoft || (Stimulsoft = {}));
