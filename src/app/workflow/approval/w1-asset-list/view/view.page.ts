import { ChangeDetector<PERSON><PERSON>, Component, OnInit, ViewChild } from '@angular/core';
import { ActionSheetButton, ActionSheetController, AlertController, NavController } from '@ionic/angular';
import { ActivatedRoute, ParamMap, NavigationExtras } from '@angular/router';
import { WorkflowEngineTask } from 'src/app/models/workflow/WorkflowEngineTask';
import { AssetTypeItem } from 'src/app/models/assetTypeItem';
import { Asset } from 'src/app/models/asset';
import { WorkflowGetApplicationWorkflowListService } from 'src/app/services/workflow/get-applicationWorkflowList/workflow-getApplicationWorkflowList.service';
import { AssetAction } from 'src/app/models/assetAction';
import { PerviewComponent } from 'src/app/component/perview/perview.component';
import { WorkflowActionsService } from 'src/app/services/workflow/workflow-actions.service';
import { ApprovalViewPageService } from 'src/app/services/workflow/workflow-service/approval/view/approval-view-page.service';
import { WfPageWidgetService } from 'src/app/services/workflow/workflow-service/other/wf-page-widget.service';
import { StorageUtils } from 'src/app/utils/storage-utils';
import { environment } from '../../../../../environments/environment';
import { GetAuthorityService } from '../../../../services/getAuthority/get-authority.service';
import { Throttle } from 'src/app/utils/throttle.decorator';
import {
  ChosenAsseDataInterface,
  WorkflowexecDataInterface,
} from 'src/app/models/workflow/AssetsByKeywordInWfAssetListForAssignScan';
import * as _ from 'lodash';
import { HttpUtilsService } from 'src/app/utils/http-utils.service';
import { LoginService } from 'src/app/services/login/login.service';
import { Overlay } from 'src/app/live/overlay.component';
import { FilterTalkParameter } from 'src/app/models/talkFilter';
import { validateNumericAndCurrency, validateNumericAndCurrencyField } from '../../../../utils/utils';

@Component({
  selector: 'app-view',
  templateUrl: './view.page.html',
  styleUrls: ['./view.page.scss'],
})
export class ViewPage implements OnInit {
  @ViewChild(PerviewComponent, { static: false }) perviewComponent: PerviewComponent;
  expand: boolean[]; // 項目の開閉状況
  more: boolean[]; // もっと見るの開閉状況
  isScanComplete: boolean = false; // すべてのスキャンが完了した
  assetActionData: AssetAction; //　資産アクション
  processInstanceId: string;
  taskId: string;
  editAssetList = []; //为了重置资产信息,把编辑过的资产放到一个list里
  // フォーム情報
  wfModel: WorkflowEngineTask; // ワークフロー承認情報
  firstLoad: boolean = true; // 画面初期ロードフラグ
  workflowName: string; // ワークフロー名前
  stepName: string; // ステップ名
  wfDict: { [key: string]: AssetTypeItem[] }; // ワークフローDic
  permissions: { [key: string]: boolean };
  wfList: String[]; // ワークフローリスト
  formData: any; // フォームデータ
  actions: any; // アクション
  showSendBackButton: boolean = false; // 差し戻しボタンを表示されるか
  showRejectButton: boolean = false; // 拒否ボタンを表示されるか
  showClamin: boolean = false; // 受取ボタンを表示されるか
  showUnclamin: boolean = false; // アサイン解除ボタンを表示されるか
  isPermission: boolean = false; // 権限か
  backDestination: string = ''; //　差し戻し先
  comments = []; // コメント
  rawFormData; // オリジナルフォームデータ
  buttonStatus = {}; // ボタン状態
  isUserTask;
  rawComment: string; // オリジナルコメント
  rawAssetList: string; // オリジナル資産リスト
  workflowScript = ''; // WF customLogic
  jsString = ''; // WF customizlogic javascript
  // 資産情報
  assetCount: number = 0; //資産件数
  finishedScanedAssetCount: number = 0; //スキャン済資産件数
  assetTypeName: string = ''; // 資産種類名
  assetList: Asset[] = []; // 資産一覧
  noScanningAssetList: Asset[] = []; // 未スキャン資産一覧
  scannedAssetList: Asset[] = []; // スキャン済み資産一覧

  scanAssetIdList = []; // スキャン資産IDリスト
  needFreshAssetListAction; // 資産リスト更新する。

  liveTalkListUrl: String;
  isAllowTalk = false;
  assetTypeId; // 資産種類I D

  backPageUrl = '/workflow/approval/w1-asset-list/view'; // 他の画面から戻るURL
  taskType: string; // タスクタイプ
  showButton: boolean = true; // ボタン表示されるか
  isAllScaned; // 全てスキャン必要か
  pageAction: ApprovalViewPageService; // 画面ロジック
  isCountingType = false; // 個数管理場合

  // 存在动态担当者选择时使用到的，用于保存选择的担当者个人或担当者组
  notifyInfo = [];
  dynamicTaskInfo: any[] = [];
  isHasTantoushaF = false;
  dynamicTantousha = [];

  isFirstWfWithAssetList: boolean;
  inputAssetListFlag: string;
  isDisabled = false;
  // 参照 asset-force/src/app/workflow/approval/w1-asset-list/list/list.page.ts，注释说明
  private chosenAssetMap: Map<string, ChosenAsseDataInterface> = new Map();
  /** 是否有权限一时保存 */
  isPermissionWF = false;
  workflowexecData: WorkflowexecDataInterface = {
    totalAssets: 0,
    numberOfUnscanned: 0,
    totalSavedAssetAmountCount: 0,
    allAssetCountInKeywordSearch: 0,
    noScanningAssetCountInKeywordSearch: 0,
    scannedAssetCount: 0
  };
  isRentalWorkflow = false; // 是否为Rental
  rawWfDict: string;
  assetListId: any;
  // wf新规提出按钮数组，isCustomizedButtonName是否为定制的按钮名称
  workflowButtonName: {
    // 提出/承认
    raiseButtonList: { isCustomizedButtonName: boolean; nameRaise: string }[];
    // 差戻し
    turndBackButtonDic: { isCustomizedButtonName: boolean; nameTurndBack: string };
    // 否决
    vetoButtonDic: { isCustomizedButtonName: boolean; nameVeto: string };
  };
  // 是否为大字体
  isBigFont: boolean = false;
  constructor(
    private navController: NavController,
    private route: ActivatedRoute,
    private authorityService: GetAuthorityService,
    private workflowGetApplicationWorkflowListService: WorkflowGetApplicationWorkflowListService,
    private workflowActionsService: WorkflowActionsService,
    private ref: ChangeDetectorRef,
    private alertController: AlertController,
    private http: HttpUtilsService,
    private loginService: LoginService,
    private actionSheetCtrl: ActionSheetController,
  ) {
    // 初始化数据用于保存选择的动态担当者信息
    this.notifyInfo['info'] = {
      user: new Map(),
      role: new Map(),
    };
  }

  /**
   *　初期化
   * @param {type} なし
   */
  ngOnInit() {
    console.log('======= w1 + assets view');

    // 項目の開閉状態
    this.expand = [true, true, true];

    // もっと見るの開閉状況
    this.more = [false];

    this.route.queryParams.subscribe(async (params) => {
      if (this.firstLoad) {
        //リスト画面から
        let rawModel = params['workFlowData'];
        this.needFreshAssetListAction = params['needFresh'];
        this.assetActionData = rawModel['assetActionData'];
        console.log(params);

        // 入力情報
        this.workflowName = params['workFlowData']['workflowName'];
        this.wfModel = params['workFlowData'];
        this.stepName = this.wfModel['name'];
        this.processInstanceId = this.wfModel.processInstanceId;
        this.taskId = this.wfModel.id;
        this.assetTypeId = params['workFlowData']['assetTypeIdWithFirstWf'];

        // overlayのfilter条件を更新
        const parameter: FilterTalkParameter = {
          workflowId: String(this.processInstanceId),
          workflowName: String(this.wfModel.wfName),
        };
        Overlay.filterTalkObservable.next(parameter);
        const type = await StorageUtils.get(StorageUtils.KEY_FONT_SIZE);
        this.isBigFont = _.isEmpty(type) ? false : type === 'big';
        await this.loadData(this.wfModel.processInstanceId, this.wfModel.id);
        // 資産情報
        this.assetTypeName = rawModel['firstWorkflowAssetTypeName'];
        this.pageAction = new ApprovalViewPageService(this.rawFormData);

        this.liveTalkListUrl = `${environment.liveBaseUrl}/af-integrate/room-list/workflow/${
          this.processInstanceId
        }?accessToken=${await StorageUtils.get<string>(StorageUtils.KEY_TOKEN)}&zoneId=${await StorageUtils.get<string>(
          StorageUtils.KEY_ZONE_ID,
        )}`;
        this.isAllowTalk = await this.authorityService
          .hasFunctionPermission(GetAuthorityService.TALK_FUNCTION_PERMISSION_ID)
          .catch(() => false);

        this.firstLoad = false;
      }
      if (params['assetCount']) {
        this.assetCount = params['assetCount'];
      }
      if (params['editAssetList']) {
        this.editAssetList = params['editAssetList'];
      }
      if (params['isFromEditPage']) {
        this.perviewComponent.doJavascript(this.jsString);
      }
      // this.updateFinishedScanAssetsCcount();
      if (!this.firstLoad && this.actions != undefined && this.actions['isMultiScanTask'] === true) {
        this.rrefreshButtons(this.wfModel.processInstanceId, this.wfModel.id);
      }
      this.initAssetShowData();

      setTimeout(() => {
        this.ref.detectChanges();
      }, 1000);
    });
  }

  /**
   * 画面表示するとき
   * @param {type} なし
   */
  ionViewDidEnter() {
    // クエリーパラメーターによってスキャン完了にする
    this.route.queryParamMap.subscribe((params: ParamMap) => {
      const isScanComplete = params.get('scan-complete');
      const goToListPage: {processInstanceId: string, taskId: string} = params['goToListPage'];

      if (isScanComplete === 'true' || goToListPage) {
        console.log('🐒 ~ ViewPage ~ this.route.queryParamMap.subscribe ~ params: ', params);
        if (goToListPage) {
          const {processInstanceId, taskId} = goToListPage;
          this.wfModel.processInstanceId = processInstanceId;
          this.wfModel.id = taskId;
        }
        this.loadData(this.wfModel.processInstanceId, this.wfModel.id);
      }
    });
  }

  /**
   * 実行
   */
  start() {
    this.navController.navigateForward('/workflow/approval/w1-asset-list/scan');
  }

  /**
   * 項目の開閉
   * @param index インデックス
   */
  @Throttle()
  toggleExpand(index: number) {
    this.expand[index] = !this.expand[index];

    setTimeout(() => {
      this.ref.detectChanges();
    }, 300);
  }

  /**
   * もっと見る
   * @param index
   */
  seeMore(index: number) {
    this.more[index] = true;
  }

  /**
   * 表示を減らす
   */
  seeLess(index: number) {
    this.more[index] = false;
  }

  /**
   * 資産一覧画面に遷移する
   * @param name
   */
  @Throttle()
  async toAssetList(shouldClaim: boolean) {
    if (shouldClaim) {
      const assetTypeList: AssetTypeItem[] = [];
      let itemDataDictCopy = JSON.parse(JSON.stringify(this.wfDict));
      for (const sectionName in itemDataDictCopy) {
        assetTypeList.push(...this.wfDict[sectionName]);
      }
      for (const assetType of assetTypeList) {
        //非表示になっているプライベートセクション内の項目
        var isView = true;
        let optionObj = JSON.parse(assetType.option);
        let sectionPrivateGroups = optionObj.sectionPrivateGroups;
        if (sectionPrivateGroups) {
          isView = await this.loginService.visiblePermissionsCheck(sectionPrivateGroups);
        }
        if (!isView) {
          //没有权限预览
          continue;
        }
        let optionObject = assetType['optionObject'];
        if (_.isEmpty(optionObject)) {
          optionObject = optionObj;
        }
        // true 不可以编辑（只读），false可以编辑
        const isReadonly = _.isEmpty(_.toString(optionObject['readonly']))
          ? false
          : _.toString(optionObject['readonly']) === '1'
            ? true
            : false;
        if (isReadonly) {
          // 对于通过 customizeLogic setValue 进来的 通货、数字不可编辑内容
          // 需要判断是否不符合规则，如果不符合的情况则弹出 error 信息并拦截下一步操作
          // 数字或通货类型输入位数检查
          if (!validateNumericAndCurrencyField(assetType)) {
            return;
          }
          continue;
        }
        // 必須チェック
        if (assetType.inputFlg === '1') {
          if (
            assetType.defaultData == '' ||
            assetType.defaultData == undefined ||
            assetType.defaultData == null ||
            Object.keys(assetType.defaultData).length === 0 ||
            String(assetType.defaultData).trim() === '' ||
            assetType.defaultData == '[]'
          ) {
            alert(assetType.itemName + 'を設定してください。');
            return;
          }
        }

        if (assetType.inputFlg === '1' && assetType.itemType === 'checkbox') {
          if (assetType.defaultData) {
            if (typeof assetType.defaultData === 'string') {
              if (assetType.defaultData !== '1') {
                let cbName = assetType.itemName ?? assetType.itemDisplayName;
                alert(cbName + 'を設定してください。');
                return;
              }
            }
          }
        }

        if (assetType.isShowMessageTS) {
          alert('入力エラーがあるのでご確認ください。');
          return;
        }
        if (assetType.defaultData) {
          // 桁数チェック
          if (assetType.optionObject.maxlength && typeof assetType.defaultData === 'string') {
            if (assetType.defaultData.length > assetType.optionObject.maxlength) {
              alert(`${assetType.itemName}を${assetType.optionObject.maxlength}文字以内に入力してください。`);
              return;
            }
          }

          // 校验数字和通货类型
          if (!validateNumericAndCurrencyField(assetType)) {
            return;
          }

          // メールアドレスの有効性チェック
          if (assetType.itemType === 'email') {
            const reg = /^(\w+|[-+.])*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
            if (!reg.test(assetType.defaultData)) {
              alert(`${assetType.itemName}のフォーマットが間違っています。`);
              return;
            }
          }
        }
      }
    }

    if (this.isAssetListEditable()) {
      if (this.isPermission) {
        let navigationExtras: NavigationExtras = {
          queryParams: {
            formData: this.wfDict,
            permissions: this.permissions,
            sectionArray: this.wfList,
            assetTypeName: this.assetTypeName,
            workflowTypeCode: this.wfModel.workflowTypeCode,
            processDefinitionId: this.wfModel.processDefinitionId,
            processInstanceId: this.processInstanceId,
            workflowName: this.workflowName,
            registeredAssetList: JSON.parse(JSON.stringify(this.assetList ?? [])),
            assetTypeId: this.wfModel.assetTypeIdWithFirstWf,
            isNew: false,
            isUserTask: this.isUserTask || this.isGroupTask,
            taskId: this.taskId,
            taskType: this.taskType,
            comment: this.comments,
            isCountingType: this.isCountingType,
            fromPage: '/workflow/approval/w1-asset-list/view',
            assignDynamicData: this.dynamicTaskInfo,
            stepName: this.stepName,
            workflowScript: this.workflowScript,
            editAssetList: this.editAssetList,
            onConfirm: async (assetCount) => {
              this.assetCount = assetCount;
              this.ref.detectChanges();
            },
          },
        };
        this.navController.navigateForward('/workflow/new/w1-asset-list/scan-list', navigationExtras);
      } else {
        let navigationExtras: NavigationExtras = {
          queryParams: {
            simplyMode: true,
            allMustScan: this.allMustScan,
            rawWorkFlowModel: this.wfModel,
            shouldClaim: shouldClaim,
            comments: this.comments,
            confirmData: this.confirmData,
            rawFormData: this.rawFormData,
            scannedAssetList: this.scannedAssetList,
            noScanningAssetList: this.noScanningAssetList,
            wfDict: this.wfDict,
            formPostVariables: this.generateFormPostVariables,
            workflowScript: this.workflowScript,
            stepName: this.stepName,
            dynamicTaskInfo: this.dynamicTaskInfo,
            dynamicTantousha: this.dynamicTantousha,
            permissions: this.permissions,
          },
        };
        this.navController.navigateForward('/workflow/approval/w1-asset-list/list', navigationExtras);
      }
      return;
    }
    if (this.rawFormData['taskType'] !== 'SCAN') {
      if (this.assetCount < 1) {
        alert('参照可能な資産がございません。');
        return;
      }
    }
    const navigationExtras: NavigationExtras = {
      queryParams: {
        allMustScan: this.allMustScan,
        rawWorkFlowModel: this.wfModel,
        shouldClaim: shouldClaim,
        comments: this.comments,
        confirmData: this.confirmData,
        rawFormData: this.rawFormData,
        scannedAssetList: this.scannedAssetList,
        noScanningAssetList: this.noScanningAssetList,
        wfDict: this.wfDict,
        permissions: this.permissions,
        formPostVariables: this.generateFormPostVariables,
        workflowScript: this.workflowScript,
        stepName: this.stepName,
        updateIDAction: (processInstanceId: string, taskId: string) => {
          // データを更新する
          console.log('====== view page update ID');
          this.wfModel.processInstanceId = processInstanceId;
          this.wfModel.id = taskId;
          if (this.workFlowType.isNewW3) {
            this.taskId = taskId;
          }
        },
        updateAssetAction: async (processInstanceId: string, taskId: string) => {
          // データを更新する
          if (this.workFlowType.isNewW3) {
            this.taskId = taskId;
          }
          await this.loadData(processInstanceId, taskId);
        },
        // 个体和个数（非复数人）已经弃用
        updateScanStateAction: async (assetId: any) => {
          // データを更新する
          if (this.rawFormData !== undefined) {
            this.rawFormData['assetListDatas'].forEach((value) => {
              if (assetId === value.assetId) {
                value['scanState'] = '1';
              }
            });
          }
        },
        // 个体和个数（非复数人）已经弃用
        updateScannedCountAction: async (assetId: any, scannedCount: number) => {
          if (this.rawFormData !== undefined) {
            this.rawFormData.assetListDatas.forEach((value) => {
              if (assetId === value.assetId) {
                value.savedAssetAmount = scannedCount;
                value.isScanned = true;
              }
            });
          }
        },
        // list页面已经被操作了的资产数量回调，主要给返回按钮用, 暂时个体和个数（非复数人）专用
        setHasBeenChangedAsset: async (chosenAssetMap: Map<string, ChosenAsseDataInterface>) => {
          this.chosenAssetMap = chosenAssetMap;
          await this.loadData(this.wfModel.processInstanceId, this.wfModel.id);
        },
        dynamicTaskInfo: this.dynamicTaskInfo,
        dynamicTantousha: this.dynamicTantousha,
      },
    };
    if (this.actions !== undefined && this.actions['isMultiScanTask'] === true) {
      navigationExtras.queryParams['assetList'] = this.rawFormData['assetListDatas'];
      this.navController.navigateForward('/workflow/asset-list-no-claim/asset-list', navigationExtras);
    } else {
      this.navController.navigateForward('/workflow/approval/w1-asset-list/list', navigationExtras);
    }
  }

  /**
   * データを読み込む
   * @param {type} processInstanceId: string
   * @param {type} taskId: string
   */
  async loadData(processInstanceId: string, taskId: string) {
    try {
      await this.http.addLoadingQueue();
      console.log('======== start load data');
      const result = await this.workflowGetApplicationWorkflowListService.getWorkflowTaskForm(
        processInstanceId,
        taskId,
        '1',
      );
      console.log('======== end');
      if (!_.isEmpty(this.formData?.secitonDic)) {
        result['secitonDic'] = this.formData.secitonDic;
      }
      if (!_.isEmpty(this.formData?.resultData)) {
        const rd = this.formData.resultData;
        if (!_.isEmpty(rd?.form)) {
          result['resultData'].form = rd.form;
        }
      }
      if (!_.isEmpty(this.comments)) {
        try {
          result['commentData'] = JSON.stringify(this.comments);
        } catch (error) {
          console.error('🐒 ~ ViewPage ~ loadData ~ error: ', error)
          result['commentData'] = '[]';
        }
      }
      this.formData = result;
      this.wfDict = result['secitonDic'];
      this.permissions = result['permissions'];
      this.wfList = result['secitonList'];
      this.actions = result['resultData']['actions'];
      this.comments = JSON.parse(result['commentData'] ?? '[]');
      this.rawFormData = result['resultData'];
      const cjs = _.isEmpty(result['resultData']?.commonJS) ? '' : result['resultData']?.commonJS;
      // 如果js从后台取得后不为空那么才把commonJS拼接到js
      if (!_.isEmpty(result['resultData']?.workflowLogicScript)) {
        this.jsString = cjs + result['resultData'].workflowLogicScript;
      }
      // 如果js从后台取得后不为空那么才把commonJS拼接到js
      if (!_.isEmpty(result['resultData']?.workflowScript)) {
        this.workflowScript = cjs + result['resultData'].workflowScript;
      }
      this.isCountingType = this.rawFormData.updateAmountShow;
      this.assetListId = this.rawFormData.assetListId;
      this.isPermission = this.actions['isPermission'] ?? false;
      this.assetCount = this.rawFormData['allAssetCount'];
      this.isHasTantoushaF =
        this.rawFormData['assignDynamicFlag'] === true &&
        this.rawFormData['wfAssignDynamicTasks'] &&
        this.rawFormData['wfAssignDynamicTasks'].length > 0 &&
        this.isPermission;
      this.dynamicTaskInfo = this.rawFormData['wfAssignDynamicTasks'];
      // 重置担当者选择数据用于初始化页面
      this.dynamicTantousha = [];
      if (this.dynamicTaskInfo !== null && this.dynamicTaskInfo) {
        for (const dynamicTaskInfoElement of this.dynamicTaskInfo) {
          // 若存在即存的动态担当者选择，则判断task类型后创建对象存入数组
          if (dynamicTaskInfoElement['authorizerId'] !== null) {
            if (dynamicTaskInfoElement['assignDynamicType'] === 'user') {
              //  user
              this.dynamicTantousha.push({
                userId: dynamicTaskInfoElement['authorizerId'],
                lastName: dynamicTaskInfoElement['authorizerName'],
                firstName: '',
              });
            } else {
              //  group
              this.dynamicTantousha.push({
                roleId: dynamicTaskInfoElement['authorizerId'],
                roleName: dynamicTaskInfoElement['authorizerName'],
              });
            }
          }
        }
      }

      this.isFirstWfWithAssetList = result['resultData']['isFirstWfWithAssetList'];
      this.inputAssetListFlag = result['resultData']['inputAssetListFlag'];

      this.showClamin = this.actions['possibleOfClaim'] ?? false;
      if (result['resultData']['taskType'] == 'SCAN') {
        this.showUnclamin = this.isPermission && !this.isMultiScanTask;
      }
      if (this.actions['rejectionStatus'] === '1') {
        this.showRejectButton = true && (this.actions['isPermission'] ?? false);
      }

      if (this.actions['sendBackStatus'] === '1' || this.actions['sendBackStatus'] === '2') {
        this.showSendBackButton = true && (this.actions['isPermission'] ?? false);
      }

      if (this.actions['sendBackDestination'] != null) {
        this.backDestination = this.actions['sendBackDestination'];
      }

      if (this.rawFormData['taskType'] != null) {
        this.isUserTask = this.rawFormData['taskType'] === 'USER' ? true : false;
      }

      // 資産情報
      result['resultData']['assetListDatas'] = result['resultData']['assetListDatas'] ?? [];

      if (this.isAssetListEditable()) {
        result['resultData']['assetListDatas'].forEach((item) => {
          if (this.isCountingType) {
            item['assetScanedCount'] = item['assetAmount'];
          } else {
            item['assetScanedCount'] = 1;
          }
        });
      }

      this.updateFinishedScanAssetsCcount();
      if (this.allMustScan) {
        this.isAllScaned = this.workflowexecData.numberOfUnscanned === 0 ? true : false;
      } else {
        this.isAllScaned = this.workflowexecData.totalAssets > this.workflowexecData.numberOfUnscanned ? true : false;
      }
      this.buttonStatus['footer'] =
        this.enableConfrimButton ||
        this.showRejectButton ||
        this.showSendBackButton ||
        this.showClamin ||
        this.showUnclamin;

      const status = this.perviewComponent.getButtonStatus(result);

      this.showButton = status; // ture : show button, false: hidden
      this.isRentalWorkflow = result['resultData']['isRentalWorkflow'];

      this.saveRawForm(this.comments, this.assetList, _.cloneDeep(result['secitonDic']));
      const workflowButtonNameDic = result?.resultData?.workflowButtonName;
      this.workflowButtonName =
        this.workflowGetApplicationWorkflowListService.extractWFApprovalOrApplicationRaiseButtons(
          workflowButtonNameDic,
          true,
        );
    } finally {
      await this.http.removeLoadingQueue();

      setTimeout(() => {
        this.ref.detectChanges();
      }, 1000);
    }
  }

  get isGroupTask() {
    if (this.rawFormData) return this.rawFormData['taskType'] === 'GROUP';
    return false;
  }

  /**
   * 資産リスト編集できるか
   */
  isAssetListEditable() {
    // group task with form input assetlist also support edit.
    return this.isFirstWfWithAssetList && this.inputAssetListFlag === '1' && (this.isUserTask || this.isGroupTask);
  }

  /**
   * ボタンを更新する
   * @param {type} processInstanceId: string
   * @param {type} taskId: string
   */
  async rrefreshButtons(processInstanceId: string, taskId: string) {
    try {
      await this.http.addLoadingQueue();

      console.log('======== start load data');
      const result = await this.workflowGetApplicationWorkflowListService.getWorkflowTaskForm(
        processInstanceId,
        taskId,
        '1',
      );
      console.log('======== end');
      this.actions = result['resultData']['actions'];
      this.rawFormData = result['resultData'];

      this.isPermission = this.actions['isPermission'] ?? false;

      this.showClamin = this.actions['possibleOfClaim'] ?? false;
      if (result['resultData']['taskType'] == 'SCAN') {
        this.showUnclamin = this.isPermission && !this.isMultiScanTask;
      }
      if (this.actions['rejectionStatus'] === '1') {
        this.showRejectButton = true && (this.actions['isPermission'] ?? false);
      }

      if (this.actions['sendBackStatus'] === '1' || this.actions['sendBackStatus'] === '2') {
        this.showSendBackButton = true && (this.actions['isPermission'] ?? false);
      }

      if (this.actions['sendBackDestination'] != null) {
        this.backDestination = this.actions['sendBackDestination'];
      }

      if (this.rawFormData['taskType'] != null) {
        this.isUserTask = this.rawFormData['taskType'] === 'USER' ? true : false;
      }

      this.buttonStatus['footer'] =
        this.enableConfrimButton ||
        this.showRejectButton ||
        this.showSendBackButton ||
        this.showClamin ||
        this.showUnclamin;

      const status = this.perviewComponent.getButtonStatus(result);
      this.showButton = status; // ture : show button, false: hidden
    } finally {
      await this.http.removeLoadingQueue();
    }
  }

  /**
   * スキャンした資産個数を更新する
   * @param {type} なし
   */
  updateFinishedScanAssetsCcount() {
    this.workflowexecData = {
      totalAssets: this.rawFormData['allAssetCount'] ?? 0,
      numberOfUnscanned: this.rawFormData['noScanningAssetCount'] ?? 0,
      totalSavedAssetAmountCount: this.rawFormData['totalSavedAssetAmountCount'] ?? 0,
      allAssetCountInKeywordSearch: this.rawFormData['allAssetCountInKeywordSearch'] ?? 0,
      noScanningAssetCountInKeywordSearch: this.rawFormData['noScanningAssetCountInKeywordSearch'] ?? 0,
      scannedAssetCount: this.rawFormData['scannedAssetCount'] ?? 0
    };
    // 完成的资产数
    const finishScanningAssetCount = this.workflowexecData.totalAssets - this.workflowexecData.numberOfUnscanned;
    if (!this.isCountingType) {
      this.workflowexecData.totalSavedAssetAmountCount = finishScanningAssetCount;
    }
    // if (this.rawFormData != undefined) {
    //   if (this.rawFormData['assetListDatas']) {
    //     this.rawFormData['assetListDatas'].forEach(value => {
    //       if (nonSubProcess) {
    //         if (value['processState'] == 'COMPLETED' || value['scanState'] == '1') {
    //           finishedScanAssetsAccount += 1;
    //           this.scanAssetIdList.push(value['assetId']);
    //         }
    //       } else {
    //         if (value['processState'] == 'COMPLETED') {
    //           finishedScanAssetsAccount += 1;
    //           this.scanAssetIdList.push(value['assetId']);
    //         }
    //       }
    //     });
    //   }
    // }

    //this.assetCount = this.assetList.length ?? 0;
    this.finishedScanedAssetCount = finishScanningAssetCount;
  }

  // 提出用なデータを組み合わせ
  get generateFormPostVariables() {
    let assetTypeList: AssetTypeItem[] = [];
    var variables = {};

    for (var key in this.wfDict) {
      if (key !== 'null' && key !== null) {
        assetTypeList.push(...this.wfDict[key]);
      }
    }

    console.log(assetTypeList);
    assetTypeList.forEach((assetType) => {
      if (assetType.itemIdStr != null) {
        variables[assetType.itemIdStr] = assetType.defaultData;
      }
    });
    assetTypeList.forEach((assetType) => {
      if (assetType.itemName == 'WF期限') {
        if (assetType.defaultData == 'null' || assetType.defaultData == null) {
          variables[assetType.itemIdStr] = '';
        } else {
          variables[assetType.itemIdStr] = assetType.defaultData;
        }
      } else if (
        assetType.itemType == 'master' ||
        assetType.itemType == 'file' ||
        assetType.itemType == 'digitalSign'
      ) {
        //マスター
        variables[assetType.itemIdStr] = JSON.stringify(assetType.defaultData);
      } else {
        variables[assetType.itemIdStr] = assetType.defaultData;
      }
    });
    variables['commentItem'] = JSON.stringify(this.comments);

    return variables;
  }

  /**
   * 承認ボタンを更新する
   * @param {type}
   */
  get enableConfrimButton() {
    if (this.actions != undefined && this.actions['isMultiScanTask'] === true) {
      if (this.rawFormData != null) {
        if ((this.isUserTask || this.isAllScaned) && (this.rawFormData['actions']['isPermission'] ?? false)) {
          return true;
        } else {
          return false;
        }
      }
    } else {
      if (this.rawFormData == null) {
        return this.isPermission;
      }

      let type = this.rawFormData['taskType'];
      if (type == 'USER') {
        return this.isPermission;
      }
      if (this.assetCount === 0) {
        return this.isPermission;
      }

      if (this.allMustScan) {
        return this.isPermission && this.finishedScanedAssetCount == this.assetCount;
      } else {
        const { totalAssets, numberOfUnscanned } = this.workflowexecData;
        if (this.isRentalWorkflow) {
          const isWFStatus = totalAssets > numberOfUnscanned;
          return this.isPermission && isWFStatus;
        } else {
          // 与web保持一致，当没有扫描资产时候也可以承认·
          return this.isPermission && this.finishedScanedAssetCount >= 0;
        }
      }
    }
  }

  /**
   * 現物確認完了
   */
  @Throttle(1500)
  scanTasktoFinish() {
    let that = this;
    this.workflowActionsService.updateScanTaskLock(
      this.taskId,
      'LOCK',
      '現物確認完了に変更',
      'not-yet',
      this.approvalBtnDisabled,
      that,
      this.comments,
      this.wfDict,
      this.processInstanceId,
    );
  }

  /**
   * 現物確認を解除
   */
  @Throttle(1500)
  restartScantask() {
    let that = this;
    this.workflowActionsService.updateScanTaskLock(
      this.taskId,
      '',
      '完了状態を解除',
      'not-yet',
      this.approvalBtnDisabled,
      that,
      this.comments,
      this.wfDict,
      this.processInstanceId,
    );
  }

  /**
   * 差し戻し
   * @description: sendBack
   * @param {type}
   * @return {type}
   */
  @Throttle(1500)
  async sendBack(buttonName: string) {
    try {
      await this.http.addLoadingQueue();
      let isCheck: boolean = await this.workflowActionsService.checkAndGetData(this.wfDict, false);
      if (isCheck == false) {
        return;
      }
    } finally {
      await this.http.removeLoadingQueue();
    }
    let that = this;
    this.workflowActionsService.sendBackFromApproval({
      assetDict: this.wfDict,
      comments: this.comments,
      sendBackDestination: this.backDestination,
      taskId: this.taskId,
      progress: 'not-yet',
      perviewComponent: this.perviewComponent,
      workflowScript: this.workflowScript,
      stepName: this.stepName,
      callBack: this.approvalBtnDisabled,
      that: that,
      isFromAssetListPage: false,
      buttonName: buttonName,
    });
  }

  /**
   * @description: 普通
   * @param {type}
   * @return {type}
   */
  @Throttle(1500)
  async rejection(buttonName: string) {
    try {
      await this.http.addLoadingQueue();
      let isCheck: boolean = await this.workflowActionsService.checkAndGetData(this.wfDict, false);
      if (isCheck == false) {
        return;
      }
    } finally {
      await this.http.removeLoadingQueue();
    }
    let that = this;
    this.workflowActionsService.comfrimWithAssets({
      assetDict: this.wfDict,
      comments: this.comments,
      processInstanceId: this.processInstanceId,
      assetList: this.confirmData.assetList,
      assetListDatas: this.confirmData.assetListDatas,
      assetListTitle: this.confirmData.assetListTitle,
      assetTypeIdWithFirstWf: this.wfModel.assetTypeIdWithFirstWf,
      taskId: this.taskId,
      progress: 'not-yet',
      perviewComponent: this.perviewComponent,
      workflowScript: this.workflowScript,
      stepName: this.stepName,
      callBack: this.approvalBtnDisabled,
      that: that,
      isFromAssetListPage: false,
      isApproval: false,
      backPage: null,
      buttonName: buttonName,
    });
  }

  approvalBtnDisabled(that, result) {
    that.isDisabled = result;
  }
  /**
   * @description: 普通
   * @param {type}
   * @return {type}
   */
  @Throttle(1500)
  async approval(raiseDic: { isCustomizedButtonName: boolean; nameRaise: string }) {
    try {
      await this.http.addLoadingQueue();
      let isCheck: boolean = await this.workflowActionsService.checkAndGetData(this.wfDict, false);
      if (isCheck == false) {
        return;
      }
    } finally {
      await this.http.removeLoadingQueue();
    }

    // 提交时用来判断
    if (!this.checkDynamicTaskCanSubmit()) {
      const alert = await this.alertController.create({
        message: '各ステップの承認者を選択してください。',
        buttons: [
          {
            text: 'はい',
            role: 'cancel',
            cssClass: 'font-weight-bold',
            handler: async () => {},
          },
        ],
      });
      await alert.present();
      return;
    } else {
      if (!_.isEmpty(this.dynamicTaskInfo) && !_.isEmpty(this.dynamicTantousha)) {
        for (let i = 0; i < this.dynamicTaskInfo.length; i++) {
          const element = this.dynamicTaskInfo[i];
          if (element['assignDynamicType'] === 'group') {
            element['authorizerId'] = this.dynamicTantousha[i]['roleId'];
            element['authorizerName'] = this.dynamicTantousha[i]['roleName'];
            console.log('新增Group', element);
          } else {
            element['authorizerId'] = this.dynamicTantousha[i]['userId'];
            element['authorizerName'] =
              this.dynamicTantousha[i]['lastName'] + ' ' + this.dynamicTantousha[i]['firstName'];
            console.log('新增User', element);
          }
        }
      }
    }
    let that = this;
    if (this.actions !== undefined && this.actions['isMultiScanTask'] === true) {
      const confirmDataClone = _.cloneDeep(this.confirmData);
      confirmDataClone['allMustScan'] = this.allMustScan;
      confirmDataClone['totalAssetAmountCount'] = this.rawFormData['totalAssetAmountCount'];
      confirmDataClone['totalSavedAssetAmountCount'] = this.rawFormData['totalSavedAssetAmountCount'];
      const taskDefKey = this.actions['taskDefKey'];
      await this.workflowActionsService.comfrimWithMultiScanCompleteNoSubprocess({
        assetDict: this.wfDict,
        comments: this.comments,
        processInstanceId: this.processInstanceId,
        taskId: this.taskId,
        progress: 'done',
        isApproval: true,
        backPage: this.workflowActionsService.approvalURL,
        assignDynamicData: this.dynamicTaskInfo,
        perviewComponent: this.perviewComponent,
        workflowScript: this.workflowScript,
        confirmData: confirmDataClone,
        stepName: this.stepName,
        callBack: this.approvalBtnDisabled,
        that: that,
        isFromAssetListPage: false,
        taskDefKey: taskDefKey,
        buttonName: raiseDic?.nameRaise,
      });
    } else {
      if (this.nonSubProcess && this.rawFormData['taskType'] === 'SCAN') {
        const isExecutionMarker = await this.workflowActionsService.comfrimWithScanCompleteNoSubprocessWithAllMust({
          assetDict: this.wfDict,
          comments: this.comments,
          processInstanceId: this.processInstanceId,
          taskId: this.taskId,
          progress: 'done',
          isApproval: true,
          backPage: this.workflowActionsService.approvalURL,
          assignDynamicData: this.dynamicTaskInfo,
          perviewComponent: this.perviewComponent,
          workflowScript: this.workflowScript,
          confirmData: this.confirmData,
          stepName: this.stepName,
          isFromAssetListPage: false,
          workflowexecData: this.workflowexecData,
          isAllMustScan: this.allMustScan,
          buttonName: raiseDic?.nameRaise,
        });
        this.approvalBtnDisabled(this, isExecutionMarker);
      } else {
        await this.workflowActionsService.comfrimWithScanComplete({
          isAssetListEditable: this.isAssetListEditable(),
          isCountingType: this.isCountingType,
          assetDict: this.wfDict,
          comments: this.comments,
          processInstanceId: this.processInstanceId,
          assetList: this.confirmData.assetList,
          assetListDatas: this.confirmData.assetListDatas,
          assetListTitle: this.confirmData.assetListTitle,
          assetTypeIdWithFirstWf: this.wfModel.assetTypeIdWithFirstWf,
          taskId: this.taskId,
          progress: 'done',
          isApproval: true,
          backPage: this.workflowActionsService.approvalURL,
          assignDynamicData: this.dynamicTaskInfo,
          perviewComponent: this.perviewComponent,
          workflowScript: this.workflowScript,
          confirmData: this.confirmData,
          stepName: this.stepName,
          callBack: this.approvalBtnDisabled,
          that: that,
          isFromAssetListPage: false,
          buttonName: raiseDic?.nameRaise,
        });
      }
    }
  }

  /**
   * @description:unclaim
   * @param {type}
   * @return {type}
   */
  @Throttle()
  async unclaim() {
    try {
      await this.http.addLoadingQueue();
      let isCheck: boolean = await this.workflowActionsService.checkAndGetData(this.wfDict, false);
      if (isCheck == false) {
        return;
      }
    } finally {
      await this.http.removeLoadingQueue();
    }
    let that = this;
    this.workflowActionsService.unclaimFormApproval(
      this.wfDict,
      this.comments,
      this.processInstanceId,
      this.taskId,
      'not-yet',
      this.approvalBtnDisabled,
      that,
    );
  }

  /**
   * @description: 入力項目（フォーム値、スキャン実績）を保存
   * @param {type}
   * @return {type}
   */
  async saveTemporaryFromScanTask() {
    if (!_.isEmpty(this.dynamicTaskInfo) && !_.isEmpty(this.dynamicTantousha)) {
      for (let i = 0; i < this.dynamicTaskInfo.length; i++) {
        const element = this.dynamicTaskInfo[i];
        if (element['assignDynamicType'] === 'group') {
          element['authorizerId'] = this.dynamicTantousha[i]['roleId'];
          element['authorizerName'] = this.dynamicTantousha[i]['roleName'];
          console.log('新增Group', element);
        } else {
          element['authorizerId'] = this.dynamicTantousha[i]['userId'];
          element['authorizerName'] =
            this.dynamicTantousha[i]['lastName'] + ' ' + this.dynamicTantousha[i]['firstName'];
          console.log('新增User', element);
        }
      }
    }

    await this.workflowActionsService.saveTemporaryFromScanTask(
      this.isCountingType,
      this.wfDict,
      this.comments,
      this.processInstanceId,
      this.assetListInfo.assetListId,
      this.confirmData.taskId,
      'not-yet',
      this.workflowActionsService.approvalURL,
      this.dynamicTaskInfo,
      this.actions !== undefined && this.actions['isMultiScanTask'] === true,
      this.rawFormData['taskType'] === 'USER' || this.rawFormData['taskType'] === 'GROUP',
    );
  }

  /**
   * @description: 入力項目（フォーム値）を保存
   * @param {type}
   * @return {type}
   */
  // async saveTemporaryFormData() {
  //   this.workflowActionsService.saveTemporaryFormData(
  //     this.wfDict,
  //     this.comments,
  //     this.processInstanceId,
  //     this.confirmData.taskId,
  //     'not-yet',
  //     this.workflowActionsService.approvalURL,
  //     this.dynamicTaskInfo,
  //     this.confirmData.assetList,
  //     this.confirmData.assetListDatas,
  //     this.isCountingType,
  //     this.wfModel.assetTypeIdWithFirstWf,
  //   );
  // }

  /**
   * 提出用なデータ
   * @param {type}
   */
  get confirmData() {
    return {
      assetDict: this.wfDict,
      comments: this.comments,
      processInstanceId: this.processInstanceId,
      assetList: this.assetList,
      assetListDatas: this.formData['resultData']['assetListDatas'],
      assetListTitle: this.wfModel.assetListTitle,
      assetTypeIdWithFirstWf: this.wfModel.assetTypeIdWithFirstWf,
      taskId: this.taskId,
      assetListInfo: this.assetListInfo,
    };
  }

  // スキャン資産タイプが一時保存か
  get showSaveTemporaryFromScanTaskBtn() {
    return this.rawFormData['actions']['saveTemporaryButtonWithScanTask'] ?? false;
  }

  // 資産IDリストを取得
  get assetIds() {
    var assetIdList = [];
    this.assetList.forEach((asset) => {
      assetIdList.push(asset.assetId);
    });
    return assetIdList;
  }

  // 承認時に利用する資産リストデータ
  get assetListInfo() {
    let assetListId = this.formData['resultData']['assetListId'];
    return {
      assetTypeId: this.assetTypeId,
      assetIds: this.assetIds,
      assetListId: assetListId,
    };
  }

  // 全て資産スキャン必要か
  get allMustScan() {
    return this.rawFormData['actions']['allMustScan'] ?? false;
  }

  // scanTaskIsLocked
  get scanTaskIsLocked() {
    return this.rawFormData['actions']['scanTaskIsLocked'] ?? false;
  }

  // サブプロセスなしか
  get nonSubProcess() {
    if (this.rawFormData != undefined) {
      return this.rawFormData['nonSubProcess'] ?? false;
    }
    return false;
  }

  // 複数人スキャンか
  get isMultiScanTask() {
    return this.rawFormData['actions']['isMultiScanTask'] ?? false;
  }

  // タスクタイプstring
  get taskTypeStr() {
    return this.rawFormData['taskType'];
  }

  // フォーム一時保存か
  get shouldSaveForm() {
    return this.didFormChanged && this.isPermission;
  }

  /**
   * オリジナルフォームデータを保存する
   * @param {type} comments
   */
  saveRawForm(comments: any, assetList: any, secitonDic: any) {
    this.rawComment = JSON.stringify(comments);
    this.rawAssetList = JSON.stringify(assetList);
    this.rawWfDict = JSON.stringify(secitonDic);
  }

  /**
   * フォームを変更されるか
   * @param {type}
   */
  get didFormChanged() {
    if (_.isEmpty(this.wfDict)) {
      return false;
    }
    let originalForm = this.workflowActionsService.getOriginalForm(this.wfDict, this.comments);
    let rawItemData = this.workflowActionsService.getOriginalForm(
      JSON.parse(this.rawWfDict),
      JSON.parse(this.rawComment),
    );
    let isFormChanged = !_.isEqual(rawItemData, originalForm);
    let hasDynamic = false;
    if (this.isHasTantoushaF === true) {
      if (!_.isEmpty(this.dynamicTantousha)) {
        if (!_.isEmpty(this.dynamicTaskInfo)) {
          for (let i = 0; i < this.dynamicTaskInfo.length; i++) {
            const element = this.dynamicTaskInfo[i];
            if (element['assignDynamicType'] === 'group') {
              element['authorizerId'] = this.dynamicTantousha[i]['roleId'];
              element['authorizerName'] = this.dynamicTantousha[i]['roleName'];
              console.log('新增Group', element);
            } else {
              element['authorizerId'] = this.dynamicTantousha[i]['userId'];
              element['authorizerName'] =
                this.dynamicTantousha[i]['lastName'] + ' ' + this.dynamicTantousha[i]['firstName'];
              console.log('新增User', element);
            }
          }
          hasDynamic = true;
        }
      }
    }

    return isFormChanged || hasDynamic;
  }

  // 資産をスキャンしたか
  get didAssetScaned() {
    return this.rawAssetList != JSON.stringify(this.confirmData.assetList);
  }

  initAssetShowData() {
    if (this.workFlowType.isNewW3) {
      this.assetTypeName = this.pageAction.assetData.assetTypeName;
      this.assetCount = this.pageAction.assetData.assetCount;
    }

    if (
      this.rawFormData['hasSubProcess'] &&
      this.rawFormData['productAssetTypeName'] &&
      this.rawFormData['productAssetTypeName'] != ''
    ) {
      this.assetTypeName = this.rawFormData['productAssetTypeName'];
    }
  }

  get workFlowType() {
    return {
      isNewW3: this.pageAction?.workFlowType == WfPageWidgetService.APPROVAL_ASSETS_TYPE_NEW_W3,
      isDefault: this.pageAction?.workFlowType == WfPageWidgetService.APPROVAL_ASSETS_TYPE_DEFAULT,
    };
  }

  // 选择担当者
  @Throttle()
  chooseTantousha(i: number) {
    if (this.dynamicTaskInfo) {
      const navigationExtras: NavigationExtras = {
        queryParams: {
          index: 0,
          itemType: this.dynamicTaskInfo[i]['assignDynamicType'],
          progress: '',
          dynamicLeader: {
            procDefId: this.dynamicTaskInfo[i]['procDefId'],
            taskDefKey: this.dynamicTaskInfo[i]['taskDefKey'],
          },
          isFromPage: 'choose-tantousha-view',
          backToPage: 'asset/schedule-alert',
          searchData: this.notifyInfo,
          searchConditionDataList: [],
          // バックメソッド
          callback: (data) => {
            if (data) {
              const tantousha = data['info'];
              const assignDynamicType: string = this.dynamicTaskInfo[i]['assignDynamicType'];
              this.dynamicTaskInfo[i] = {
                ...this.dynamicTaskInfo[i],
                authorizerId: assignDynamicType == 'group' ? tantousha['roleId'] : tantousha['userId'],
              };
              this.dynamicTantousha[i] = data['info'];
              this.ref.detectChanges();
            }
          },
        },
      };
      this.navController.navigateForward('/edit-item/staff', navigationExtras);
    }
  }

  // 判断是否符合提交条件
  // 返回true时满足条件可以提交
  private checkDynamicTaskCanSubmit() {
    if (this.dynamicTaskInfo && this.dynamicTaskInfo.length > 0) {
      if (this.dynamicTantousha && this.dynamicTantousha.length > 0) {
        return this.dynamicTaskInfo.length === this.dynamicTantousha.length;
      } else {
        return false;
      }
    } else {
      return true;
    }
  }

  // 获取担当者文本用于html页面显示
  getTantoushaInfoWithIndex(index: number) {
    if (this.dynamicTaskInfo) {
      const chooseType = this.dynamicTaskInfo[index]['assignDynamicType'];
      if (this.dynamicTantousha) {
        const datum = this.dynamicTantousha[index];
        if (datum) {
          if (chooseType === 'group') {
            return datum['roleName'];
          } else {
            return datum['lastName'] + ' ' + datum['firstName'];
          }
        } else {
          return '未選択';
        }
      } else {
        return '未選択';
      }
    }
  }

  @Throttle()
  async backButton() {
    // 修改过的资产汇总数组
    const assetListDataArray = [];
    for (let [key, value] of this.chosenAssetMap) {
      // initScanCount初期资产数量回数（初期代表着刚一进入此页面显示的回数）
      if (!_.isEmpty(_.toString(value['initScanCount']))) {
        assetListDataArray.push({ assetId: _.toNumber(key), initSavedAssetAmount: value.initScanCount });
      }
    }
    let taskDefKey = undefined;
    /** 是否为复数人判断 */
    let isPluralPeople = undefined;
    if (!_.isEmpty(this.actions)) {
      // 取到wf当前状态 1.InputTask  2.UserTask  3.ScanTask
      taskDefKey = this.actions['taskDefKey'];
      // 是否为复数人判断
      isPluralPeople = this.actions['isMultiScanTask'] === true;
    }

    const isRestoreAllAssets = await this.isRestoreAllAssets();
    const isChangedAsset = this.isChangedAsset();
    const isAssetListDataArray = assetListDataArray.length > 0;
    const isAllListCho = isRestoreAllAssets || isChangedAsset || isAssetListDataArray;
    // 是否为差し戻し
    const isSendBack = _.isEmpty(this.wfModel) ? undefined : this.wfModel['isMyselfInputTask'] === true;

    const alert = await this.alertController.create({
      message: '保存せずに終了しますか？入力したデータ・及びスキャンデータは破棄されます',
      cssClass: 'alert-title-primary',
      buttons: [
        {
          text: 'キャンセル',
          role: 'cancel',
          cssClass: 'common-alert-text-primary',
          handler: async () => {
            // await backNoFun()
          },
        },
        {
          text: 'OK',
          role: 'ok',
          cssClass: 'common-alert-text-primary font-weight-bold',
          handler: async () => {
            alert.dismiss();
            if (this.workFlowType.isNewW3 || isPluralPeople) {
              this.navController.navigateBack('/tabs/workflow/approval');
              return;
            }
            // 判断当前WF状态（taskDefKey）或 所有数组都为空（资产列表里原始状态没有资产）
            if (_.isEmpty(taskDefKey) || !isAllListCho) {
              this.navController.navigateBack('/tabs/workflow/approval');
              return;
            }
            // InputTask 也可以为 新规WF时候，但是回滚必须为差し戻し的InputTask时候才可以
            if (
              taskDefKey.startsWith('UserTask') ||
              taskDefKey.startsWith('GroupTask') ||
              (taskDefKey.startsWith('InputTask') && isSendBack)
            ) {
              await backOkayFunAtUserTask();
              return;
            }
            if (taskDefKey.startsWith('ScanTask')) {
              await backOkayFun();
              return;
            }
            this.navController.navigateBack('/tabs/workflow/approval');
          },
        },
      ],
    });
    // 在scan task的时候，确认按钮
    const backOkayFun = async () => {
      try {
        await this.http.addLoadingQueue();
        // 当用户操作了或者扫描了资产才去调用重置API，否则直接返回到上一个页面
        const formData = new FormData();
        formData.append('processInstanceId', _.toString(this.processInstanceId));
        formData.append('taskId', _.toString(this.taskId));
        formData.append('assetListDataArrayJSON', JSON.stringify(assetListDataArray));
        await this.workflowGetApplicationWorkflowListService.restoreAssetScanStateForAssignScan(formData);
      } finally {
        await this.http.removeLoadingQueue();
        if (this.needFreshAssetListAction) {
          this.needFreshAssetListAction(true);
        }
        this.navController.navigateBack('/tabs/workflow/approval');
      }
    };

    // 在user task的时候，确认按钮
    const backOkayFunAtUserTask = async () => {
      try {
        await this.http.addLoadingQueue();
        // 当用户操作了或者扫描了资产才去调用重置API，否则直接返回到上一个页面
        const formData = new FormData();
        formData.append('processInstanceId', _.toString(this.processInstanceId));
        formData.append('taskId', _.toString(this.taskId));
        if (this.editAssetList) {
          formData.append('assetListDataJson', JSON.stringify(this.editAssetList));
        } else {
          formData.append('assetListDataJson', JSON.stringify([]));
        }
        if (isRestoreAllAssets) {
          let assetList = await StorageUtils.get(StorageUtils.TEMP_EDIT_ASSET_LIST);
          formData.append('clearedAssetListDataJson', assetList);
        } else {
          formData.append('clearedAssetListDataJson', JSON.stringify([]));
        }
        try {
          await this.workflowGetApplicationWorkflowListService.restoreAssetScanState(formData);
          this.navController.navigateBack('/tabs/workflow/approval');
        } catch (error) {
          this.navController.navigateBack('/tabs/workflow/approval');
        }
      } finally {
        await this.http.removeLoadingQueue();
      }
    };

    if (isAllListCho || this.shouldSaveForm) {
      await alert.present();
    } else {
      this.navController.navigateBack('/tabs/workflow/approval');
    }
  }

  /**
   * 对editAssetList进行check
   * @returns
   */
  isChangedAsset() {
    return this.editAssetList && this.editAssetList.length > 0 ? true : false;
  }

  /**
   * 确认是否进行了一扩删除动作
   */
  async isRestoreAllAssets() {
    let assetList = await StorageUtils.get(StorageUtils.TEMP_EDIT_ASSET_LIST);
    return assetList && assetList != '[]' ? true : false;
  }

  get thisQueryPar() {
    return this.route.snapshot.queryParams;
  }

  otherButtonSwitchOpenModal = async () => {
    const buList = _.initial(this.workflowButtonName?.raiseButtonList).map((rb) => {
      const bu: ActionSheetButton<{ text: string; role: string }> = {
        text: rb.nameRaise,
        role: JSON.stringify(rb),
      };
      return bu;
    });
    buList.push({
      text: 'キャンセル',
      role: 'cancel',
    });
    const actionSheet = await this.actionSheetCtrl.create({
      header: 'その他',
      subHeader: 'その他の実行処理を選択してください',
      cssClass: 'wf-application-alert-text-primary',
      translucent: true,
      backdropDismiss: false,
      buttons: buList,
    });

    actionSheet.present();

    const { role } = await actionSheet.onWillDismiss();
    if (role == 'cancel') {
      return;
    }
    const roleObj: {
      isCustomizedButtonName: boolean;
      nameRaise: string;
    } = JSON.parse(role);
    this.approval(roleObj);
  };

  /**
   * 底部按钮总数
   */
  public get numberButtonsFooterSection(): number {
    if (_.isEmpty(this.workflowButtonName)) {
      // 正常情况下不可能为空
      return 0;
    }
    let allButtonNum = 0;
    // 默认第一个是一时保存
    if (this.isPermission) {
      allButtonNum = allButtonNum + 1;
    }
    const { raiseButtonList, turndBackButtonDic, vetoButtonDic } = this.workflowButtonName;
    if (!_.isEmpty(turndBackButtonDic) && this.showSendBackButton) {
      // 差し戻し
      allButtonNum = allButtonNum + 1;
    }
    if (!_.isEmpty(vetoButtonDic) && this.showRejectButton) {
      // 否决
      allButtonNum = allButtonNum + 1;
    }
    // 承认或者提出按钮总数
    allButtonNum = allButtonNum + raiseButtonList.length;
    return allButtonNum;
  }
}