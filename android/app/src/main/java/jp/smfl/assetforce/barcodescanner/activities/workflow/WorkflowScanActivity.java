package jp.smfl.assetforce.barcodescanner.activities.workflow;

import android.content.Intent;
import android.graphics.Paint;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextUtils;
import android.text.style.ImageSpan;
import android.util.TypedValue;
import android.view.View;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.lifecycle.ViewModelProvider;
import androidx.viewpager.widget.ViewPager;
import androidx.viewpager2.widget.ViewPager2;

import com.fasterxml.jackson.core.JsonProcessingException;

import jp.co.smfl.AssetForce.R;
import jp.smfl.assetforce.barcodescanner.activities.BaseScanActivity;
import jp.smfl.assetforce.barcodescanner.converters.WorkflowPayloadConverter;
import jp.smfl.assetforce.barcodescanner.factories.viewmodels.WorkflowScanViewModelFactory;
import jp.smfl.assetforce.barcodescanner.fragments.RFIDFragment;
import jp.smfl.assetforce.barcodescanner.viewmodels.workflow.WorkflowScanViewModel;
import jp.smfl.assetforce.plugins.ActivityResultCode;
import jp.smfl.assetforce.plugins.barcodescanner.payloads.ScanPluginPayload;
import jp.smfl.assetforce.utilities.BigDataTransportHelper;
import jp.smfl.assetforce.utilities.CenterImageSpan;
import jp.smfl.assetforce.utilities.FileUtils;
import jp.smfl.assetforce.utilities.SharedPrefManager;
import jp.smfl.assetforce.utilities.helpers.JsonHelpers;

public class WorkflowScanActivity extends BaseScanActivity<WorkflowScanViewModel> {

  private WorkflowScanViewModel viewModel;
  private TextView countLabel;
  private Button nextBtn;
  private ImageButton locationBtn;
  private ViewPager2 fragmentPager;
  private ImageButton ibLeftSwitch;
  private ImageButton ibRightSwitch;
  private ViewPager mWheelView;
  private LinearLayout wheelViewContainer;
  private LinearLayout buttonMenu;
  private ImageButton ibBack;
  private LinearLayout llSubTitle1;
  private LinearLayout llSubTitle2;
  private TextView tvMainTitle;
  private TextView tvSubTitle1;
  private TextView tvSubTitle2;
  private TextView tvDefaultTitle;
  private LinearLayout clBlackBg;

  @Override
  protected void onCreate(Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    setContentView(R.layout.activity_asset_scan);
    ibBack = findViewById(R.id.btnCancel);
    ibBack.setOnClickListener(v -> {
        if (viewModel.isFromNew()) {
          alertWhenBackBtnPress(false, null);
        } else {
          toNextPage(true);
        }
      }
    );
    ibLeftSwitch = findViewById(R.id.ibLeftSwitch);
    ibRightSwitch = findViewById(R.id.ibRightSwitch);
    mWheelView = findViewById(R.id.wheelView);
    wheelViewContainer = findViewById(R.id.llScanModeSwitch);
    mWheelView.setPageMargin(10);
    fragmentPager = findViewById(R.id.pagerContainer);
    buttonMenu = findViewById(R.id.buttonMenu);

    countLabel = findViewById(R.id.count_label);
    nextBtn = findViewById(R.id.go_next_button);
    nextBtn.setOnClickListener(v -> toNextPage());
    locationBtn = findViewById(R.id.locationBtn);
    locationBtn.setOnClickListener(v -> toLocationPage());

    scanInfoGradientView = findViewById(R.id.scan_info_gradient_view);
    llSubTitle1 = findViewById(R.id.llSubTitle1);
    llSubTitle2 = findViewById(R.id.llSubTitle2);
    tvMainTitle = findViewById(R.id.tvMainTitle);
    tvSubTitle1 = findViewById(R.id.tvSubTitle1);
    tvSubTitle2 = findViewById(R.id.tvSubTitle2);
    tvDefaultTitle = findViewById(R.id.tvDefaultTitle);
    clBlackBg = findViewById(R.id.clBlackBg);

    if ("big".equals(SharedPrefManager.read(SharedPrefManager.Key.FONT_SIZE, "normal"))) {
      nextBtn.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 20);
      countLabel.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 18);
      tvMainTitle.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 22.5F);
      tvDefaultTitle.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 15);
      tvSubTitle1.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 15);
      tvSubTitle2.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 15);
    } else {
      nextBtn.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16);
      countLabel.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14);
      tvMainTitle.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 18);
      tvDefaultTitle.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12);
      tvSubTitle1.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12);
      tvSubTitle2.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 12);
    }

    Intent intent = getIntent();
    String dataFileName = intent.getStringExtra("data");
    ScanPluginPayload payload = null;
    try {
      payload = JsonHelpers.parseJsonString(FileUtils.getStringFromFile(this, dataFileName), ScanPluginPayload.class);
    } catch (JsonProcessingException e) {
      e.printStackTrace();
    } finally {
      this.deleteFile(dataFileName);
    }
    viewModel = new ViewModelProvider(this,
      new WorkflowScanViewModelFactory(
        this.getApplication(),
        payload.data
      )).get(WorkflowScanViewModel.class);

    setupScanInfoGradientView(payload);
    setupScannerFragment(savedInstanceState, WorkflowScanViewModel.class);
    setupFragmentViewPager(fragmentPager, ibLeftSwitch, ibRightSwitch, mWheelView, wheelViewContainer, buttonMenu, ibBack, clBlackBg);
    setupTorchButton(findViewById(R.id.torch_switch));
    setupManualInputButton(findViewById(R.id.inputBtn), getScannerFragment());
    setupObservation();

  }

  public void toLocationPage() {
    Intent intent = new Intent();
    WorkflowPayloadConverter converter = new WorkflowPayloadConverter(viewModel.getScannedList());
    intent.putStringArrayListExtra(intentTargetAssets, converter.getRawJsonStrTargetAssetList());
    intent.putExtra(intentAssetListId, viewModel.getTargetAssetListId());
    intent.putExtra(intentEditAsset, JsonHelpers.castObjectToJsonString(viewModel.mEditAssetList));
    BigDataTransportHelper.put(ActivityResultCode.ToLocationPage, intent);
    setResult(ActivityResultCode.ToLocationPage, null);
    finish();
  }

  @Override
  protected void setupRFIDFragment(RFIDFragment<WorkflowScanViewModel> workflowScanViewModelRFIDFragment) {
    workflowScanViewModelRFIDFragment.setArguments(RFIDFragment.genArguments(false, true, false));
  }

  private void setupObservation() {
    viewModel.toastText.observe(this, s -> {
      showToast(s);
    });

    viewModel.count.observe(this, count -> {
      if (count > 0) {
        countLabel.setVisibility(View.VISIBLE);
        nextBtn.setEnabled(true);
        attachAnimation(countLabel, String.valueOf(count), nextBtn);
      } else {
        countLabel.setText(String.valueOf(count));
        countLabel.setVisibility(View.INVISIBLE);
        nextBtn.setEnabled(false);
      }
    });

    viewModel.hasMultipleLocation.observe(this, hasMultipleLocation -> {
      if (hasMultipleLocation) {
        locationBtn.setVisibility(View.VISIBLE);
        llSubTitle2.setVisibility(View.VISIBLE);
      }
    });
  }

  public void toNextPage() {
    toNextPage(false);
  }

    @Override
    protected void clearData() {
      if (viewModel != null) {
        viewModel.clearData();
      }
    }

  public void toNextPage(boolean isGoBack) {
    WorkflowPayloadConverter converter = new WorkflowPayloadConverter(viewModel.getScannedList());
    Intent intent = new Intent();
    intent.putStringArrayListExtra(intentTargetAssets, converter.getRawJsonStrTargetAssetList());
    intent.putExtra(intentAssetListId, viewModel.getTargetAssetListId());
    intent.putExtra("isFromNew", viewModel.isFromNew());
    if (isGoBack) {
      intent.putExtra(intentIsFromCloseBtn, "1");
    }
    intent.putExtra(intentEditAsset, JsonHelpers.castObjectToJsonString(viewModel.mEditAssetList));
    BigDataTransportHelper.put(ActivityResultCode.ToNextPage, intent);
    setResult(ActivityResultCode.ToNextPage, null);
    finish();
  }

  private void setupScanInfoGradientView(ScanPluginPayload payload) {
    String mainTitle = String.format(getString(R.string.scan_workflow_name), payload.workflowName, payload.taskName);
    String subTitle1 = payload.assetTypeName;
    String subTitle2 = viewModel.locationInfo;

    if (TextUtils.isEmpty(mainTitle) || mainTitle.equals("null")) {
      tvMainTitle.setVisibility(View.GONE);
    } else {
      tvMainTitle.setVisibility(View.VISIBLE);
      String text = "[icon] " + mainTitle;
      int fontsizeId = SharedPrefManager.read(SharedPrefManager.Key.FONT_SIZE, "normal").equals("big") ? R.dimen.scan_default_title_font_height_big : R.dimen.scan_default_title_font_height_normal;
      Paint paint = new Paint();
      paint.setTextSize(getResources().getDimensionPixelSize(fontsizeId));
      Paint.FontMetrics fm = paint.getFontMetrics();
      int fontH = (int)Math.ceil((fm.descent - fm.top) * 0.60);
      Drawable drawable = getResources().getDrawable(R.drawable.scan_wf);
      drawable.setBounds(0, 0, fontH, fontH);
      SpannableString spannable = new SpannableString(text);
      ImageSpan imageSpan = new ImageSpan(drawable);
      spannable.setSpan(new CenterImageSpan(drawable), 0, 6, Spannable.SPAN_INCLUSIVE_EXCLUSIVE);
      tvMainTitle.setText(spannable);
    }

    if (TextUtils.isEmpty(subTitle1) || subTitle1.equals("null")) {
      llSubTitle1.setVisibility(View.GONE);
    } else {
      llSubTitle1.setVisibility(View.VISIBLE);
      tvSubTitle1.setText(subTitle1);
    }

    llSubTitle2.setVisibility(View.GONE);
    if (TextUtils.isEmpty(subTitle2) || subTitle2.equals("null")) {
      tvSubTitle2.setText(getString(R.string.scan_no_location));
    } else {
      tvSubTitle2.setText(subTitle2);
    }

    scanInfoGradientView.setVisibility(View.VISIBLE);
  }
}
