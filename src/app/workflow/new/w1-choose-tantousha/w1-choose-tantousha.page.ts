import {ChangeDetectorRef, Component, <PERSON>Zone, OnInit, ViewChild} from '@angular/core';
import {AlertController, NavController} from '@ionic/angular';
import {ActivatedRoute, NavigationExtras} from '@angular/router';
import {AssetTypeItem} from 'src/app/models/assetTypeItem';
import {ScanCondition} from 'src/app/models/assetAction';
import {PerviewComponent} from 'src/app/component/perview/perview.component';
import {WorkflowActionsService} from 'src/app/services/workflow/workflow-actions.service';
import {Throttle} from 'src/app/utils/throttle.decorator';
import {HttpUtilsService} from 'src/app/utils/http-utils.service';
import {VListPage} from '../../../v-list/v-list.page';
import _ from 'lodash';

@Component({
  selector: 'app-w1-choose-tantousha',
  templateUrl: './w1-choose-tantousha.page.html',
  styleUrls: ['./w1-choose-tantousha.page.scss'],
})
export class W1ChooseTantoushaPage extends VListPage<any> implements OnInit {
  @ViewChild(PerviewComponent, { static: false }) perviewComponent: PerviewComponent;
  more: boolean[]; // もっと見るの開閉状況
  formData: any; // フォームデータ
  sectionArray: string[]; // セクション配列
  assetActionItemListDict: { [key: string]: AssetTypeItem[] }; // 資産情報のレイアウト表示部品へ引渡す変数
  scanConditionList: ScanCondition[] = []; // スキャン条件リスト
  assetActionItemList: AssetTypeItem[]; // 資産処理項目リスト
  registeredAssetList: [{}]; // 登録済み資産リスト
  amountAssetList: [{}]; // 資産数量リスト
  assetTypeName: string; // 資産種類名前
  assetTypeId: string; // 資産種類ID
  assetActionName: string; // 資産処理名前
  assetActionId: string; // 資産処理ID
  processType: string; // プロセス種類
  customerActionName: string; // カスタマイズ処理名前
  processDefinitionId: string;
  isNew: boolean; // 新しか
  processInstanceId: string;
  isUpdateAmount: string; //　数量更新するか
  state: string; // 状態
  taskId: string; // タスクID
  comments: []; // コメント
  assetListId: string; // 資産リストID
  inputAssetListFlag: string; // 資産入力できるか
  progress: string; // 進捗
  backPageUrl: string; // 他の画面から戻るURL
  datasCount: string; // データ数量
  firstLoad: boolean = true; // 画面初期ロードフラグ
  showButton: boolean = true; // ボタンを表示されるか
  isCountingType: boolean = false; // 個数管理か
  isHasTantoushaF = false;
  dynamicTaskInfo: Array<any> = []; // 动态担当者选择任务信息
  dynamicTantousha: Array<any> = [];
  notifyInfo = [];
  workflowName: string;
  workflowTypeCode: string; // ワークフロー種類コード
  assetDict: { [key: string]: AssetTypeItem[] }; // 資産DIC
  isFromNew; // 新规WF或者一时保存后提出
  editAssetList = []; //为了重置资产信息,把编辑过的资产放到一个list里
  autoFetchSearchId; // 用于获取过滤资产抽出条件用的参数
  workflowId; // 用于扫描获取资产信息
  engineId; // 用于扫描获取资产信息
  private isNoAsset = false;
  private isApplication = false;
  private stepName: string;
  private newBackUrl: string;
  private workflowScript: string; // javascript on saved
  private jsString: string; // customizlogic javascript
  private isMyselfInputTask: boolean;
  permissions: { [key: string]: boolean };
  // wf新规提出按钮数组，isCustomizedButtonName是否为定制的按钮名称
  workflowRaiseButtonNameList: { isCustomizedButtonName: boolean; nameRaise: string; }[];

  constructor(
    private alertController: AlertController,
    navController: NavController,
    route: ActivatedRoute,
    private workflowActionsService: WorkflowActionsService,
    private ref: ChangeDetectorRef,
    private http: HttpUtilsService,
    ngZone: NgZone,
  ) {
    super(navController, route, ngZone);
    this.activatedRoute.queryParams.subscribe((params) => {
      if (this.firstLoad) {
        this.isNoAsset = params['isNoAsset'];
        this.newBackUrl = params['newBackUrl'];
        this.inputAssetListFlag = params['inputAssetListFlag'];
        this.workflowScript = params['workflowScript'];
        this.jsString = params['jsString'];
        this.autoFetchSearchId = params['autoFetchSearchId'];
        this.workflowId = params['workflowId'];
        this.engineId = params['engineId'];
        this.isMyselfInputTask = params['isMyselfInputTask'];
        this.permissions = params['permissions'];
        this.workflowRaiseButtonNameList = params['workflowRaiseButtonNameList'];
        if (this.isNoAsset === true) {
          // 当被退回到form入力的时候需要额外判断isApplication==true的情况
          // true时需要额外接收额外参数
          this.isApplication = params['isApplication'];
          if (this.isApplication === true) {
            this.workflowName = params['workflowName'];
            this.stepName = params['stepName'];
            this.processInstanceId = params['processInstanceId'];
            this.taskId = params['taskId'];
            this.state = params['state'];
            this.progress = params['progress'];
            this.workflowName = params['workflowName'];
            this.workflowName = params['workflowName'];
          }
          this.assetDict = params['assetDict'];
          this.comments = params['comments'];
          this.processDefinitionId = params['processDefinitionId'];
          this.dynamicTaskInfo = params['dynamicTaskInfo'];
        } else {
          this.dynamicTaskInfo = params['assignDynamicData'];
          this.sectionArray = params['sectionArray'];
          this.formData = params['formData'];
          this.assetListId = params['assetListId'];
          this.scanConditionList = params['scanConditionList'];
          this.registeredAssetList = params['registeredAssetList'];
          if (params['assetCount']) {
            this.datasCount = params['assetCount'];
          }
          if (params['editAssetList']) {
            this.editAssetList = params['editAssetList'];
          }
          // 差し戻し之后this.registeredAssetList会变为空故在此判断
          if (this.registeredAssetList) {
            this.datasCount = String(this.registeredAssetList.length);
          }
          this.assetTypeName = params['assetTypeName'];
          this.assetTypeId = params['assetTypeId'];
          this.workflowName = params['workflowName'];
          this.workflowTypeCode = params['workflowTypeCode'];
          this.processType = params['workflowTypeCode'];
          this.processDefinitionId = params['processDefinitionId'];
          this.processInstanceId = params['processInstanceId'];
          this.isNew = params['isNew'];
          this.taskId = params['taskId'];
          this.state = params['state'];
          this.comments = params['comment'];
          this.isUpdateAmount = params['isUpdateAmount'];
          this.progress = params['progress'];
          this.assetListId = params['assetListId'];
          this.backPageUrl = '/workflow/new/w1-asset-list/submit';
          this.isCountingType = params['isCountingType'];
          this.stepName = params['stepName'];
          if (this.state != undefined) {
            this.isFromNew = this.state == '作成中';
          }
          this.autoFetchSearchId = params['autoFetchSearchId'];
        }
        this.initDynamicTaskInfo();
        this.itemsList = this.dynamicTaskInfo ?? [];
        this.doAfterRequest(0);
        this.firstLoad = false;
      }
      if (params['isFromNew']) {
        this.isFromNew = params['isFromNew'];
      }
    });
    this.notifyInfo['info'] = {
      user: new Map(),
      role: new Map(),
    };
  }

  private initDynamicTaskInfo() {
    // 如果存在已选中的担当者信息则初始化用于显示的对象
    this.dynamicTantousha = [];
    if (this.dynamicTaskInfo !== undefined && this.dynamicTaskInfo !== null && this.dynamicTaskInfo.length > 0) {
      for (const dynamicTaskInfoElement of this.dynamicTaskInfo) {
        // 若存在即存的动态担当者选择，则判断task类型后创建对象存入数组
        if (dynamicTaskInfoElement['authorizerId'] !== null) {
          if (dynamicTaskInfoElement['assignDynamicType'] === 'user') {
            //  user
            this.dynamicTantousha.push({
              userId: dynamicTaskInfoElement['authorizerId'],
              lastName: dynamicTaskInfoElement['authorizerName'],
              firstName: '',
            });
          } else {
            //  group
            this.dynamicTantousha.push({
              roleId: dynamicTaskInfoElement['authorizerId'],
              roleName: dynamicTaskInfoElement['authorizerName'],
            });
          }
        }
      }
    }
  }

  /**
   * 初期化
   * @param {type}　なし
   */
  ngOnInit() {
    super.ngOnInit()
  }

  ionViewDidEnter() {
    if (_.isEmpty(this.itemsList)) {
      this.getDynamicTaskInfo();
    }
  }

  /**
   * @description:　提出
   * @param {type}
   * @return {type}
   */
  @Throttle()
  async apply() {
    // 提交时用来判断
    if (!this.checkDynamicTaskCanSubmit()) {
      const alert = await this.alertController.create({
        message: '各ステップの承認者を選択してください。',
        buttons: [
          {
            text: 'はい',
            role: 'cancel',
            cssClass: 'font-weight-bold',
            handler: async () => {},
          },
        ],
      });
      await alert.present();
      return;
    } else {
      if (!_.isEmpty(this.dynamicTaskInfo) && !_.isEmpty(this.dynamicTantousha)) {
        for (let i = 0; i < this.dynamicTaskInfo.length; i++) {
          const element = this.dynamicTaskInfo[i];
          if (element['assignDynamicType'] === 'group') {
            element['authorizerId'] = this.dynamicTantousha[i]['roleId'];
            element['authorizerName'] = this.dynamicTantousha[i]['roleName'];
          } else {
            element['authorizerId'] = this.dynamicTantousha[i]['userId'];
            element['authorizerName'] =
              this.dynamicTantousha[i]['lastName'] + ' ' + this.dynamicTantousha[i]['firstName'];
          }
        }
      }
    }
    if (this.isNoAsset === true) {
      if (this.isApplication === true) {
        const navigationExtras: NavigationExtras = {
          queryParams: {
            isSubmit: true,
            workflowName: this.workflowName,
            stepName: this.stepName,
            processInstanceId: this.processInstanceId,
            taskId: this.taskId,
            state: this.state,
            progress: this.progress,
            assetDict: this.assetDict,
            comments: this.comments,
            processDefinitionId: this.processDefinitionId,
            dynamicTaskInfo: this.dynamicTaskInfo,
            dynamicTantousha: this.dynamicTantousha,
            permissions: this.permissions,
            workflowRaiseButtonNameList: this.workflowRaiseButtonNameList,
          },
        };
        this.navController.navigateForward('/workflow/application/w1/view', navigationExtras);
      } else {
        const navigationExtras: NavigationExtras = {
          queryParams: {
            isSubmit: true,
            assetDict: this.assetDict,
            comments: this.comments,
            processDefinitionId: this.processDefinitionId,
            dynamicTaskInfo: this.dynamicTaskInfo,
            dynamicTantousha: this.dynamicTantousha,
            backUrl: this.newBackUrl,
            permissions: this.permissions,
            workflowRaiseButtonNameList: this.workflowRaiseButtonNameList,
          },
        };
        this.navController.navigateForward('/workflow/new/w1/form', navigationExtras);
      }
    } else {
      // 跳转提交一览页面
      const navigationExtras: NavigationExtras = {
        queryParams: {
          isSubmit: true,
          registeredAssetList: this.registeredAssetList,
          formData: this.formData,
          sectionArray: this.sectionArray,
          scanConditionList: this.scanConditionList,
          assetTypeName: this.assetTypeName,
          assetTypeId: this.assetTypeId,
          inputAssetListFlag: this.inputAssetListFlag,
          workflowTypeCode: this.workflowTypeCode,
          processDefinitionId: this.processDefinitionId,
          processInstanceId: this.processInstanceId,
          workflowName: this.workflowName,
          isNew: this.isNew,
          state: this.state,
          taskId: this.taskId,
          comment: this.comments,
          isUpdateAmount: this.isUpdateAmount,
          progress: this.progress,
          backUrl: '/workflow/new/w1-choose-tantousha',
          assetListId: this.assetListId,
          isCountingType: this.isCountingType,
          dynamicTaskInfo: this.dynamicTaskInfo,
          dynamicTantousha: this.dynamicTantousha,
          newBackUrl: this.newBackUrl,
          stepName: this.stepName,
          isFromNew: this.isFromNew,
          workflowScript: this.workflowScript,
          jsString: this.jsString,
          editAssetList: this.editAssetList,
          assetCount: this.datasCount,
          autoFetchSearchId: this.autoFetchSearchId,
          workflowId: this.workflowId,
          engineId: this.engineId,
          newPageInstance: true,
          isMyselfInputTask: this.isMyselfInputTask,
          permissions: this.permissions,
          workflowRaiseButtonNameList: this.workflowRaiseButtonNameList
        },
      };
      this.navController.navigateForward('/workflow/new/w1-asset-list/submit', navigationExtras);
    }
  }

  /**
   * 戻る
   */
  @Throttle()
  async back() {
    this.navController.back();
  }

  @Throttle()
  chooseTantousha(i: number) {
    if (this.dynamicTaskInfo) {
      const navigationExtras: NavigationExtras = {
        queryParams: {
          index: 0,
          itemType: this.dynamicTaskInfo[i]['assignDynamicType'],
          progress: '',
          dynamicLeader: {
            procDefId: this.dynamicTaskInfo[i]['procDefId'],
            taskDefKey: this.dynamicTaskInfo[i]['taskDefKey'],
          },
          isFromPage: 'choose-tantousha-view',
          backToPage: 'asset/schedule-alert',
          searchData: this.notifyInfo,
          autoFetchSearchId: this.autoFetchSearchId,
          permissions: this.permissions,
          searchConditionDataList: [],
          // バックメソッド
          callback: (data) => {
            console.log(data);
            if (data) {
              this.dynamicTantousha[i] = data['info'];
              this.ref.detectChanges();
            }
          },
        },
      };
      this.navController.navigateForward('/edit-item/staff', navigationExtras);
    }
  }

  // 获取担当者文本用于html页面显示
  getTantoushaInfoWithIndex(index: number) {
    if (this.dynamicTaskInfo) {
      const chooseType = this.dynamicTaskInfo[index]['assignDynamicType'];
      const datum = this.dynamicTantousha[index];
      if (datum) {
        if (chooseType === 'group') {
          return datum['roleName'];
        } else {
          return datum['lastName'] + ' ' + datum['firstName'];
        }
      } else {
        return '未選択';
      }
    }
  }

  // 获取动态担当者选择task的信息
  private async getDynamicTaskInfo() {
    await this.http.addLoadingQueue();
    this.workflowActionsService
      .getWorkflowAssignDynamicTaskListByTaskId(this.processDefinitionId, 'InputTask-0001')
      .then((data) => {
        console.log(data);
        this.dynamicTaskInfo = data.taskList;
        this.itemsList = this.dynamicTaskInfo ?? [];
        this.initDynamicTaskInfo();
        this.doAfterRequest(0);
      })
      .finally(async () => {
        await this.http.removeLoadingQueue();
      });
  }

  // 判断是否符合提交条件
  // 返回true时满足条件可以提交
  private checkDynamicTaskCanSubmit() {
    if (this.dynamicTaskInfo && this.dynamicTaskInfo.length > 0) {
      if (this.dynamicTantousha && this.dynamicTantousha.length > 0) {
        return this.dynamicTaskInfo.length === this.dynamicTantousha.length;
      } else {
        return false;
      }
    } else {
      return true;
    }
  }
}
