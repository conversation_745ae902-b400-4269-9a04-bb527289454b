<div class="global-bg"> </div>
<ion-header
  translucent="true"
  class="header"
>
  <ion-toolbar
    class="title-toolbar"
    appTitleCenter
  >
    <ion-buttons slot="start">
      <ion-button
        class="back-button"
        (click)="close()"
      >
        <ion-icon
          slot="icon-only"
          name="chevron-back"
        ></ion-icon>
      </ion-button>
    </ion-buttons>
    <div
      id="titleContainer"
      class="title-container"
    >
      <div class="title">{{ title }}</div>
    </div>
  </ion-toolbar>
</ion-header>

<ion-content
  fullscreen="true"
  class="offset-bottom-0"
>
  <div class="container layout-default">
    <app-af-customize-view
      #customizeView
      [backPath]="'/workflow/approval/w1-asset-list/subform'"
      [itemData]="assetDict"
      [isResumeInformation]="true"
    ></app-af-customize-view>
  </div>
</ion-content>

<ion-footer
  class="page-footer ion-no-border"
  *ngIf="!readOnly"
>
  <ion-toolbar>
    <div class="footer-buttons flex-row-reverse">
      <ion-button
        color="blue-default"
        size="small"
        (click)="confirm()"
        class="footer-button"
        >承認</ion-button
      >
    </div>
  </ion-toolbar>
</ion-footer>
