package jp.smfl.assetforce.ocr.util;

import android.content.Context;
import android.hardware.Sensor;
import android.hardware.SensorEvent;
import android.hardware.SensorEventListener;
import android.hardware.SensorManager;

public class OrientationProvider implements SensorEventListener {

    private SensorManager sensorManager;
    private Sensor accelerometer;
    private Context context;
    private OrientationInfo tmp;
    private boolean blockFlg = false;
    private float[] g;

    public OrientationProvider(Context context) throws Exception {
        this.context = context;

        sensorManager = (SensorManager) this.context.getSystemService(Context.SENSOR_SERVICE);
        if (sensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER) != null) {
            accelerometer = sensorManager.getDefaultSensor((Sensor.TYPE_ACCELEROMETER));
            sensorManager.registerListener(this, accelerometer, SensorManager.SENSOR_DELAY_GAME);
        } else {
            throw new Exception("can not find available sensor");
        }
    }

    public OrientationInfo getOrientationInfo() {
        this.tmp = new OrientationInfo();
        this.blockFlg = true;

        double norm = Math.sqrt(g[0] * g[0] + g[1] * g[1] + g[2] * g[2]);
        int inclination = (int) Math.round(Math.toDegrees(Math.acos(g[2] / norm)));

        this.tmp.x = this.g[0];
        this.tmp.y = this.g[1];
        this.tmp.z = this.g[2];

        if (inclination < 15 || inclination > 165) {
            //flat(face top or face bottom)
            this.tmp.orientation = 0;
        } else {
            //not flat
            int degrees = (int) Math.round(Math.toDegrees(Math.atan2(g[0] / norm, g[1] / norm)));
            if (-45 <= degrees && degrees <= 45) {
                //home bottom
                this.tmp.orientation = 0;
            } else if (-135 <= degrees && degrees < -45) {
                //home left
                this.tmp.orientation = 1;
            } else if (45 < degrees && degrees <= 135) {
                //home right
                this.tmp.orientation = 3;
            } else if ((-180 <= degrees && degrees < -135) ||
                       (135 < degrees && degrees <= 180)) {
                //home top
                this.tmp.orientation = 2;
            }
        }

        this.blockFlg = false;

        return this.tmp;
    }

    @Override
    public void onSensorChanged(SensorEvent event) {
        if (this.blockFlg) {
            return;
        }
        this.g = event.values.clone();
    }

    @Override
    public void onAccuracyChanged(Sensor sensor, int accuracy) {
        //doing nothing
    }

    public class OrientationInfo {
        private int orientation;
        private float x;
        private float y;
        private float z;

        public int getOrientation() {
            return orientation;
        }

        public void setOrientationInfo(int orientationInfo) {
            this.orientation = orientationInfo;
        }

        public float getX() {
            return x;
        }

        public void setX(float x) {
            this.x = x;
        }

        public float getY() {
            return y;
        }

        public void setY(float y) {
            this.y = y;
        }

        public float getZ() {
            return z;
        }

        public void setZ(float z) {
            this.z = z;
        }
    }
}
