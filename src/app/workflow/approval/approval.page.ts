import { ChangeDetector<PERSON><PERSON>, Component, ElementRef, NgZone, OnInit, Renderer2 } from '@angular/core';
import { Router, NavigationStart, ActivatedRoute, ParamMap, NavigationExtras } from '@angular/router';
import { <PERSON><PERSON><PERSON>roller, NavController } from '@ionic/angular';
import { Subscription, Subject } from 'rxjs';
import { ToastrService, ActiveToast } from 'ngx-toastr';
import {
  WorkflowGetApplicationWorkflowListService,
  Progress,
} from 'src/app/services/workflow/get-applicationWorkflowList/workflow-getApplicationWorkflowList.service';
import { WorkflowEngineSearchTaskCondition } from 'src/app/models/workflow/workflowEngineSearchTaskCondition';
import { WorkflowEngineTask } from 'src/app/models/workflow/WorkflowEngineTask';
import { StorageUtils } from 'src/app/utils/storage-utils';
import { WorkflowSearchCondition } from 'src/app/models/workflow/workflowSearchCondition';
import { clearLocalData, nativeBlockAfterNavigateForward, toggleElementsVisibility } from '../../utils/utils';
import { Throttle } from 'src/app/utils/throttle.decorator';
import { HttpUtilsService } from '../../utils/http-utils.service';
import { ScanService } from 'src/app/services/scan/scan.service';
import _ from 'lodash';
import {NavigationOptions} from '@ionic/angular/common/providers/nav-controller'

@Component({
  selector: 'app-approval',
  templateUrl: './approval.page.html',
  styleUrls: ['./approval.page.scss'],
})
export class ApprovalPage implements OnInit {
  [x: string]: any;
  order: 'descend' | 'ascend' = 'descend'; // 並び順
  orderSubject: Subject<'ascend' | 'descend'> = new Subject();
  orderState = this.orderSubject.asObservable();
  items: WorkflowEngineTask[] = []; // アイテム
  search: string = ''; // 検索テキスト
  progress: Progress = 'not-yet'; // 進捗の絞り込み
  activeToast: ActiveToast<number>; // 表示中のtoastr
  routeObserver: Subscription; // 遷移を監視するオブジェクト
  unaapveCount: number; // 未承認のWF数
  searchConditionDataList: any;
  defaultDoneSearchConditionDataList: WorkflowSearchCondition[] = []; // default申請済
  defaultAllSearchConditionDataList: WorkflowSearchCondition[] = []; //　default申請の全て
  isFromSearchCondition: string; // 検索からか
  needRefresh: boolean = false; // refresh必要か

  backUrl = '/tabs/temp-task'; // 戻るURL

  constructor(
    private http: HttpUtilsService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private toastr: ToastrService,
    private workflowGetApplicationWorkflowListService: WorkflowGetApplicationWorkflowListService,
    private navController: NavController,
    private scanService: ScanService,
    private ref: ChangeDetectorRef,
    private ngZone: NgZone,
    private element: ElementRef,
    private renderer: Renderer2,
    private domCtrl: DomController,
  ) {
    // 資産一覧を検索用のフォーマットに揃えておく
    this.itemsForSearch = this.items.map((item) => {
      return Object.assign(item, {
        name: this.formatSearch(item.workflowName),
      });
    });
    // 並び順が変えたらソートする（監視）
    this.orderState.subscribe(() => {
      this.sortByDeadlineDate(this.items);
    });
  }
  /**
   * 初期化
   * @param {type} なし
   */
  ngOnInit() {
    this.activatedRoute.queryParamMap.subscribe(async (params: ParamMap) => {
      const name = params.get('name');
      const progress = params.get('progress');
      this.isFromSearchCondition = params.get('isFromSearchCondition');

      if (params.get('backUrl')) {
        this.backUrl = params.get('backUrl');
      }

      if (params.get('unaapveCount')) {
        //検索画面から戻る場合、未承認のWF数を表示する
        let tmpCount = params.get('unaapveCount');
        if (!isNaN(Number(tmpCount))) {
          this.unaapveCount = Number(tmpCount);
        }
      }

      //デフォルト検索条件設定
      if (this.isFromSearchCondition !== 'true') {
        this.defaultForDoneSearchCondition();
        this.defaultForAllSearchCondition();
      }
      // 絞り込みを設定する
      switch (progress) {
        case 'all':
          this.progress = 'all';
          break;
        case 'done':
          this.progress = 'done';
          break;
        case 'not-yet':
          this.progress = 'not-yet';
          break;
        default:
          break;
      }

      if (name || name == '') {
        // 通知を表示する
        this.activeToast = this.toastr.success(`${name}しました。`, '', {
          positionClass: 'toast-center-center',
          timeOut: 1000,
        });
        await this.reloadData();
        this.clearPosParam(progress);
      }

      if (this.needRefresh && !(name || name == '')) {
        await this.reloadData();
      }

      this.needRefresh = false;
    });
  }

  /**
   * 画面を表示される時
   * @param {type} なし
   */
  ionViewWillEnter() {
    this.reloadData();
  }

  ionViewWillLeave() {
    if (this.activeToast) {
      this.toastr.clear(this.activeToast.toastId);
      this.activeToast = null;
    }
  }

  async loadData(progress: string) {
    try {
      await this.http.addLoadingQueue();
      let workflowEngineSearchTaskCondition: WorkflowEngineSearchTaskCondition =
        new WorkflowEngineSearchTaskCondition();
      workflowEngineSearchTaskCondition.from = '1';
      var state: string;
      //　作成中
      if (progress === 'not-yet') {
        workflowEngineSearchTaskCondition.processStateCondition = 'UNAPPROVE';
        state = 'UNAPPROVE';
        // 申請済み
      } else if (progress === 'done') {
        workflowEngineSearchTaskCondition.processStateCondition = 'APPROVED';
        state = 'APPROVED';
        // 全て
      } else {
        workflowEngineSearchTaskCondition.processStateCondition = 'ALL';
        state = 'ALL';
      }

      let formData = new FormData();
      formData.append('processStateCondition', state);
      formData.append('searchConditions', this.searchConditionDataList);
      formData.append('from', '1');
      var result: any;

      if (progress === 'not-yet') {
        result = await this.workflowGetApplicationWorkflowListService.getApplicationWorkflowList(
          workflowEngineSearchTaskCondition,
        );
      } else {
        result =
          await this.workflowGetApplicationWorkflowListService.getApplicationWorkflowListBySearchCondition(formData);
      }
      console.log('====== approve list');
      console.log(result);
      this.sortByDeadlineDate(result.tasks);
      this.itemsForSearch = this.items = result.tasks;
      if (progress === 'not-yet') {
        this.unaapveCount = this.items.length;
      }
    } finally {
      await this.http.removeLoadingQueue();

      this.ref.detectChanges();
    }
  }

  /**
   * 承認済検索条件のデフォルト値設定
   */
  async defaultForDoneSearchCondition() {
    let searchConditionDataList = await StorageUtils.get(StorageUtils.KEY_SEARCH_CONDITION_LIST + 'approval');
    if (
      searchConditionDataList === null ||
      searchConditionDataList === undefined ||
      searchConditionDataList === '' ||
      searchConditionDataList === '[]'
    ) {
      const defaultForDoneSearchCondition = new WorkflowSearchCondition();
      defaultForDoneSearchCondition.name = 'state';
      defaultForDoneSearchCondition.nameIsNull = false;
      defaultForDoneSearchCondition.method = 'listInclude';
      defaultForDoneSearchCondition.methodIsNull = false;
      defaultForDoneSearchCondition.value = ['ACTIVE'];
      defaultForDoneSearchCondition.valueIsNull = false;
      defaultForDoneSearchCondition.itemType = 'multiSelect';
      defaultForDoneSearchCondition.isShow = true;
      defaultForDoneSearchCondition.valueForShow = 'ACTIVE';
      this.defaultDoneSearchConditionDataList.push(defaultForDoneSearchCondition);
      StorageUtils.set(
        StorageUtils.KEY_SEARCH_CONDITION_LIST + 'approval',
        JSON.stringify(this.defaultDoneSearchConditionDataList),
      );
    }
  }

  /**
   * 承認の全て検索条件のデフォルト値設定
   */
  async defaultForAllSearchCondition() {
    let searchConditionDataList = await StorageUtils.get(StorageUtils.KEY_SEARCH_CONDITION_LIST + 'approval-all');
    if (
      searchConditionDataList === null ||
      searchConditionDataList === undefined ||
      searchConditionDataList === '' ||
      searchConditionDataList === '[]'
    ) {
      const defaultForDoneSearchCondition = new WorkflowSearchCondition();
      defaultForDoneSearchCondition.name = 'appliedDate';
      defaultForDoneSearchCondition.nameIsNull = false;
      defaultForDoneSearchCondition.method = 'gtOrEq';
      defaultForDoneSearchCondition.methodIsNull = false;
      defaultForDoneSearchCondition.value = this.getDay(-7);
      defaultForDoneSearchCondition.valueIsNull = false;
      defaultForDoneSearchCondition.itemType = 'date';
      defaultForDoneSearchCondition.isShow = true;
      this.defaultAllSearchConditionDataList.push(defaultForDoneSearchCondition);
      StorageUtils.set(
        StorageUtils.KEY_SEARCH_CONDITION_LIST + 'approval-all',
        JSON.stringify(this.defaultAllSearchConditionDataList),
      );
    }
  }

  /**
   * 指定の日付を取る　例：getDay(−７)　→　一週間前の日付
   * @param day
   */
  getDay(day) {
    var days = new Date();
    var gettimes = days.getTime() + 1000 * 60 * 60 * 24 * day;
    days.setTime(gettimes);
    var year = days.getFullYear();
    var month = days.getMonth() + 1;
    var monthStr: string;
    var todayStr: string;
    if (month < 10) {
      monthStr = '0' + month;
    } else {
      monthStr = String(month);
    }
    var today = days.getDate();
    if (today < 10) {
      todayStr = '0' + today;
    } else {
      todayStr = String(today);
    }
    return year + '-' + monthStr + '-' + todayStr;
  }

  /**
   * @description: 詳細ページに遷移する
   * @param {type}
   * @return:
   */
  @Throttle()
  toDetailPage(item: WorkflowEngineTask) {
    // 当是一下手机无法处理的task的时候给出一个提示
    if (item.taskDefKey && item.taskDefKey.indexOf('-') != -1) {
      const taskName = item.taskDefKey.split('-')[0];
      if (
        taskName == 'ActionTask' ||
        taskName == 'RPATask' ||
        taskName == 'ServiceTask' ||
        taskName == 'AmountActionTask' ||
        taskName == 'JavaDelegateServiceTask'
      ) {
        alert('システム実行中につき、時間をおいてから再度ご参照ください。');
        return;
      }
    }
    //重置flag和备份用的资产list
    clearLocalData();
    this.setWorkFlowInfoOnScan(item);
    var link = '/workflow/approval/w1/view';
    switch (item.workflowTypeCode) {
      case '1':
        if (item.assetTypeIdWithFirstWf != 0 || item.assetTypeIdWithFirstWf == null) {
          // W1+W3
          console.log('======= W1+W3 =======');
          link = '/workflow/approval/w1-asset-list/view';
        } else {
          // w1
          console.log('======= w1 =======');
          link = '/workflow/approval/w1/view';
        }
        break;
      case '2':
        console.log('======= w2 =======');
        link = '/workflow/approval/w2/view';
        break;
      default:
        console.log('======= w1 =======');
        link = '/workflow/approval/w1/view';
        break;
    }

    let navigationExtras: NavigationExtras = {
      queryParams: {
        workFlowData: item,
        needFresh: (value) => {
          this.needRefresh = true;
        },
      },
    };
    this.navController.navigateForward(link, navigationExtras);

    nativeBlockAfterNavigateForward();
  }

  /**
   * クリアパラメータ
   * @param {type} progress
   */
  clearPosParam(progress) {
    this.navController.navigateBack('/tabs/workflow/approval', {
      queryParams: {
        progress: progress,
      },
    });
  }

  /**
   * 更新する
   * @param {type} event
   */
  doRefresh(event: { target: { complete: () => void } }) {
    // リフレッシュを完了させる（仮）
    this.reloadData();
    setTimeout(() => {
      event.target.complete();
    }, 2000);
  }

  // 未承認
  @Throttle()
  public openNotYet({ event }) {
    this.progress = 'not-yet';
    this.reloadData();
  }
  // 申請済み
  @Throttle()
  public openDone({ event }) {
    this.progress = 'done';
    this.reloadData();
  }
  // 全て
  @Throttle()
  public openAll({ event }) {
    this.progress = 'all';
    this.reloadData();
  }

  /**
   * データを読み込む
   * @param {type} なし
   */
  async reloadData() {
    try {
      await this.http.addLoadingQueue();
      let isFromPage = this.isFromPage();
      let searchConditionDataList = await StorageUtils.get(StorageUtils.KEY_SEARCH_CONDITION_LIST + isFromPage);
      if (searchConditionDataList !== null && searchConditionDataList !== '') {
        this.searchConditionDataList = searchConditionDataList;
      } else {
        this.searchConditionDataList = '[]';
      }
      await this.loadData(this.progress);
    } finally {
      await this.http.removeLoadingQueue();
    }
  }
  /**
   * 並び順を切り替え
   */
  toggleSortOrder() {
    if (this.order === 'ascend') {
      this.order = 'descend';
    } else {
      this.order = 'ascend';
    }
    this.orderSubject.next(this.order);
  }

  /**
   * 期限日付順で配列をソートする
   */
  sortByDeadlineDate(items: any[]) {
    items.sort((a, b) => {
      let retVal = 0;
      if (this.chageStringToDate(a.createdDate) > this.chageStringToDate(b.createdDate)) {
        retVal = 1;
      } else if (this.chageStringToDate(a.createdDate) < this.chageStringToDate(b.createdDate)) {
        retVal = -1;
      }
      return this.order === 'descend' ? retVal * -1 : retVal;
    });

  }

  /**
   * テキストと日付に変換
   */
  chageStringToDate(str: string | number | Date): Date {
    return new Date(str);
  }

  valueChange(value: string) {
    this.search = value;
  }

  /**
   * ワークフローを絞り込み
   */
  get filterItems(): WorkflowEngineTask[] {
    if (this.items === null) {
      return null;
    }
    return this.filter(this.search, this.progress);
  }

  /**
   * 検索結果なし
   */
  get isEmptyResult(): boolean {
    if (this.items === null) {
      return true;
    }
    return this.filterItems.length <= 0;
  }

  /**
   * 名前検索（簡易的な文字列一致）と進捗で絞り込み
   * @param search 検索するワード
   * @param progress 進捗
   */
  filter(search: string, progress: Progress = 'all'): WorkflowEngineTask[] {
    const formatted = this.formatSearch(search); // 検索ワード
    let retVal = this.itemsForSearch;

    // 検索
    if (formatted.length > 0) {
      let find = []; // 見つかった資産

      // 検索ワードに該当するインデックスを取得
      this.itemsForSearch.forEach((item, index) => {
        if (
          (item.wfName != null && this.formatSearch(item.wfName.toLowerCase()).indexOf(formatted.toLowerCase()) >= 0) ||
          // (item.id != null && this.formatSearch(item.id.toLowerCase()).indexOf(formatted.toLowerCase()) >= 0) ||
          (item.processInstanceId != null &&
            this.formatSearch(item.processInstanceId.toLowerCase()).indexOf(formatted.toLowerCase()) >= 0) ||
          (item.workflowType != null &&
            this.formatSearch(item.workflowType.toLowerCase()).indexOf(formatted.toLowerCase()) >= 0) ||
          (item.deadlineDate != null &&
            this.formatSearch(item.deadlineDate.toLowerCase()).indexOf(formatted.toLowerCase()) >= 0) ||
          (item.state != null && this.formatSearch(item.state.toLowerCase()).indexOf(formatted.toLowerCase()) >= 0)
        ) {
          find.push(this.itemsForSearch[index]);
        }
      });

      retVal = find;
      if (find.length > 0) {
        this.items = find;
      }
    } else {
      this.items = this.itemsForSearch;
    }
    this.sortByDeadlineDate(this.items);
    return retVal;
  }

  /**
   * 検索用にフォーマット
   * @param str
   */
  public formatSearch(str: string): string {
    return this.hiraToKana(this.zenToHan(str));
  }

  /**
   * ひらがな→カタカナ
   * @param str
   */
  private hiraToKana(str: string): string {
    return str.replace(/[\u3041-\u3096]/g, (match) => {
      const chr = match.charCodeAt(0) + 0x60;
      return String.fromCharCode(chr);
    });
  }

  /**
   * 英数の全角→半角
   * @param str
   */
  private zenToHan(str: string): string {
    return str.replace(/[A-Za-z0-9]/g, (s) => {
      return String.fromCharCode(s.charCodeAt(0) + 65248);
    });
  }

  /**
   * 検索画面に遷移する
   */
  @Throttle()
  toSearchCondition() {
    toggleElementsVisibility(this.renderer,this.domCtrl, false);
    let isFromPage = this.isFromPage();
    let option: NavigationOptions = {
      queryParams: {
        progress: this.progress,
        isFromPage: isFromPage,
        backToPage: '/tabs/workflow/approval',
        unaapveCount: this.unaapveCount,
      },
    };
    this.navController.navigateBack('/workflow/search-condition', option);
  }

  /**
   * どの画面から遷移
   * @param {type}　なし
   */
  isFromPage() {
    var isFromPage: string;
    if (this.progress === 'done') {
      isFromPage = 'approval';
    } else if (this.progress === 'all') {
      isFromPage = 'approval-all';
    }
    return isFromPage;
  }

  setWorkFlowInfoOnScan(item: WorkflowEngineTask) {
    if (_.isEmpty(item)) {
      return;
    }

    this.scanService.setWorkflowName(item.wfName);
    this.scanService.setAssetTypeName(item.firstWorkflowAssetTypeName);
    this.scanService.setTaskName(item.name);
  }
}
