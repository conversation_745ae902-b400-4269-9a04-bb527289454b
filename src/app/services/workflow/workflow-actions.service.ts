/*
 * @Author: your name
 * @Date: 2020-10-12 11:04:37
 * @,@LastEditTime: ,: 2021-12-22 10:27:40
 * @,@LastEditors: ,: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /asset-force-mobile/asset-force/src/app/services/workflow/workflow-actions.service.ts
 */
import { Injectable } from '@angular/core';
import { AlertController, NavController } from '@ionic/angular';
import { ToastrService } from 'ngx-toastr';
import { AfCustomizeViewComponent } from 'src/app/component/af-customize-view/af-customize-view.component';
import { AssetTypeItem } from 'src/app/models/assetTypeItem';
import { WorkflowGetApplicationWorkflowListService } from './get-applicationWorkflowList/workflow-getApplicationWorkflowList.service';
import { AssetAction, ScanCondition } from 'src/app/models/assetAction';
import { Asset } from 'src/app/models/asset';
import { ActionService } from '../action/action-service.service';
import { StorageUtils } from 'src/app/utils/storage-utils';
import { LoginService } from '../login/login.service';
import { AssetDetailService } from '../assetdetail/asset-detail.service';
import { GlobalVariableService } from 'src/app/utils/globl-variable.service';
import { PerviewComponent } from 'src/app/component/perview/perview.component';
import { WorkflowexecDataInterface } from 'src/app/models/workflow/AssetsByKeywordInWfAssetListForAssignScan';
import * as _ from 'lodash';
import {
  clearLocalData,
  isValEmpty,
  getNumbersCommaRemoved,
  validateNumericAndCurrency,
  deleteFileFromS3,
  processNumericAndCurrency,
  validateNumericAndCurrencyField, dealNumber, setPercentageUnit, toFixed, checkIsNumber, validateAndProcessNumericItem
} from 'src/app/utils/utils';
import { HttpUtilsService } from '../../utils/http-utils.service';
import { getFormatDateTime } from 'src/app/utils/time-utils';
import { AssetService } from 'src/app/services/asset/asset.service';

@Injectable({
  providedIn: 'root',
})
export class WorkflowActionsService {
  approvalURL: string = '/tabs/workflow/approval';
  newURL: string = '/tabs/workflow/new';
  applicationURL: string = '/tabs/workflow/application';
  dynamicSettingNamedic = {
    today: '今日',
    now: '現在',
    valueBlank: '<空白>',
    currentUserName: 'ログインユーザーのユーザー名',
    currentUserEmail: 'ログインユーザーのEmail',
    currentUserInput: '',
  };
  assetTypeItemList: Array<AssetTypeItem> = [];
  assetList;
  stepName;
  actionName: string = '';
  alertMsgIsShow: boolean = false;
  itemDataDict;
  processInstanceId;
  taskId;

  constructor(
    private alertController: AlertController,
    private navController: NavController,
    private toastr: ToastrService,
    private actionService: ActionService,
    private workflowGetApplicationWorkflowListService: WorkflowGetApplicationWorkflowListService,
    private assetDetailService: AssetDetailService,
    private loginService: LoginService,
    private globalVariable: GlobalVariableService,
    private http: HttpUtilsService,
    public assetService: AssetService,
  ) {}

  /**
   * @description:基本メソット
   * @param {type}
   * @return {type}
   */
  async baseAction(
    alertText: string,
    customizeViewComponent: AfCustomizeViewComponent,
    url: string,
    isNewFlag: boolean,
    isSaveAction: boolean,
    callback: () => void,
    noAction: () => void = null,
  ): Promise<boolean> {
    if (customizeViewComponent != null && isSaveAction !== true) {
      let map = customizeViewComponent.checkAndGetData();
      if (!map) {
        return false;
      }
    }

    const alert = await this.alertController.create({
      header: alertText,
      message: '選択した処理を実行しますか？',
      buttons: [
        {
          text: 'キャンセル',
          role: 'cancel',
          cssClass: 'common-alert-text-primary',
          handler: () => {
            if (noAction) {
              noAction();
            }
          }
        },
        {
          text: 'OK',
          role: 'ok',
          cssClass: 'common-alert-text-primary font-weight-bold',
          handler: () => {
            // 执行异步操作
            (async () => {
              if (callback) {
                await this.http.addLoadingQueue();
                try {
                  await callback();
                  await alert.dismiss(true,'ok');
                } catch (error) {
                  console.error('Error in callback:', error);
                  await alert.dismiss(false,'ok');
                } finally {
                  await this.http.removeLoadingQueue();
                }
              } else {
                await alert.dismiss(true,'ok');
              }
            })();
            return false; // 返回 false 防止 alert 自动关闭，让我们手动控制关闭时机
          }
        }
      ]
    });
    
    await alert.present();
    const { role, data } = await alert.onDidDismiss();
    return role === 'ok' && data === true;
  }

  /**
   * @description: 共通のフォームデータを作成する
   * @param {type}
   * @return {type}
   */
  getFormData(assetDict: { [key: string]: AssetTypeItem[] }, comments: any, fromOriginCompare = false): any {
    assetDict = JSON.parse(JSON.stringify(assetDict));
    let assetTypeList: AssetTypeItem[] = [];
    const formData = new FormData();
    for (var key in assetDict) {
      if (key !== 'null' && key !== null) {
        assetTypeList.push(...assetDict[key]);
      }
    }
    assetTypeList.forEach((assetType) => {
      if (assetType.defaultData instanceof Array) {
        assetType.defaultData.forEach((item) => {
          if (item['turl'] != undefined && item['turl'] != '') {
            delete item['turl'];
          }

          delete item['loaded'];
        });
      }
      if (assetType.itemName == 'WF期限') {
        if (assetType.defaultData == 'null' || assetType.defaultData == null) {
          formData.append(assetType.itemIdStr, '');
        } else {
          formData.append(assetType.itemIdStr, assetType.defaultData);
        }
      } else if (assetType.itemType == 'file') {
        if (isValEmpty(assetType.defaultData, assetType.itemType)) {
          formData.append(assetType.itemIdStr, JSON.stringify([]));
        } else {
          if (assetType.defaultData) {
            formData.append(assetType.itemIdStr, JSON.stringify(assetType.defaultData));
          } else {
            formData.append(assetType.itemIdStr, JSON.stringify([]));
          }
        }
      } else if (
        assetType.itemType == 'master' ||
        assetType.itemType == 'digitalSign' ||
        assetType.itemType == 'image'
      ) {
        if (isValEmpty(assetType.defaultData, assetType.itemType)) {
          formData.append(assetType.itemIdStr, JSON.stringify([]));
        } else {
          formData.append(assetType.itemIdStr, JSON.stringify(assetType.defaultData));
        }
      } else if (assetType.itemType == 'checkbox') {
        if (assetType.optionObject.checkboxMultiFlg == '1') {
          //複数
          if (typeof assetType.defaultData === 'string') {
            formData.append(assetType.itemIdStr, assetType.defaultData);
          } else {
            formData.append(assetType.itemIdStr, JSON.stringify(assetType.defaultData));
          }
        } else {
          formData.append(assetType.itemIdStr, assetType.defaultData);
        }
      } else if (assetType.itemType == 'number' || assetType.itemType == 'currency') {
        if (assetType.defaultData) {
          formData.append(assetType.itemIdStr, _.toString(assetType.defaultData).replace(/,/g, ''));
        } else {
          formData.append(assetType.itemIdStr, '');
        }
      } else if (assetType.itemType == 'userSelect') {
        formData.append(assetType.itemIdStr, JSON.stringify(assetType.defaultData));
      } else if (assetType.itemType === 'groupSelect') {
        let data = '[]';

        if (!fromOriginCompare) {
          const defaultData = assetType.defaultData;
          
          if (_.isEmpty(defaultData)) {
            data = '[]';
          } else if (typeof defaultData === 'string') {
            data = defaultData;
          } else {
            data = JSON.stringify(defaultData);
          }
        } else {
          assetType.valueForShow = '[]';
        }

        formData.append(assetType.itemIdStr, data);
      } else {
        formData.append(assetType.itemIdStr, assetType.defaultData);
      }
    });
    //コメント
    formData.append('commentItem', JSON.stringify(comments));
    return formData;
  }

  /**
   *
   * @param assetDict フォーム情報
   * @param comments
   */
  getVeriables(assetDict: { [key: string]: AssetTypeItem[] }, comments: any): string {
    let assetTypeList: AssetTypeItem[] = [];
    var formDic = {};
    for (var key in assetDict) {
      assetTypeList.push(...assetDict[key]);
    }
    assetTypeList.forEach((assetType) => {
      if (assetType.defaultData instanceof Array) {
        assetType.defaultData.forEach((item) => {
          if (item['turl'] != undefined && item['turl'] != '') {
            delete item['turl'];
          }
        });
      }
      if (assetType.itemName == 'WF期限') {
        if (assetType.defaultData == 'null' || assetType.defaultData == null) {
          formDic[assetType.itemIdStr] = '';
        } else {
          formDic[assetType.itemIdStr] = assetType.itemValue;
        }
      } else if (
        assetType.itemType == 'master' ||
        assetType.itemType == 'file' ||
        assetType.itemType == 'digitalSign' ||
        assetType.itemType == 'image'
      ) {
        if (isValEmpty(assetType.defaultData, assetType.itemType)) {
          formDic[assetType.itemIdStr] = JSON.stringify([]);
        } else {
          //マスター
          formDic[assetType.itemIdStr] = JSON.stringify(assetType.defaultData);
        }
      } else if (assetType.itemType == 'checkbox') {
        if (assetType.optionObject.checkboxMultiFlg == '1') {
          //複数
          formDic[assetType.itemIdStr] = JSON.stringify(assetType.defaultData);
        } else {
          formDic[assetType.itemIdStr] = assetType.defaultData;
        }
      } else if (assetType.itemType == 'userSelect') {
        formDic[assetType.itemIdStr] = JSON.stringify(assetType.defaultData);
      } else if (assetType.itemType == 'number' || assetType.itemType == 'currency') {
        formDic[assetType.itemIdStr] = _.toString(assetType.defaultData).replace(/,/g, '');
      } else {
        formDic[assetType.itemIdStr] = assetType.defaultData;
      }
    });
    //コメント
    formDic['commentItem'] = JSON.stringify(comments);
    return JSON.stringify(formDic);
  }

  /**
   * @description: 共通のフォームデータを作成する
   * @param {type}
   * @return {type}
   */
  getMobileVariableData(assetDict: { [key: string]: AssetTypeItem[] }, comments: any): any {
    let assetTypeList: AssetTypeItem[] = [];
    var variables = {};

    for (var key in assetDict) {
      assetTypeList.push(...assetDict[key]);
    }

    console.log(assetTypeList);
    assetTypeList.forEach((assetType) => {
      if (assetType.defaultData instanceof Array) {
        assetType.defaultData.forEach((item) => {
          if (item['turl'] != undefined && item['turl'] != '') {
            delete item['turl'];
          }
        });
      }
      if (assetType.itemName == 'WF期限') {
        if (assetType.defaultData == 'null' || assetType.defaultData == null) {
          variables[assetType.itemIdStr] = '';
        } else {
          variables[assetType.itemIdStr] = assetType.itemValue ? assetType.itemValue : assetType.defaultData;
        }
      } else if (
        assetType.itemType == 'master' ||
        assetType.itemType == 'file' ||
        assetType.itemType == 'digitalSign' ||
        assetType.itemType == 'image'
      ) {
        if (isValEmpty(assetType.itemValue, assetType.itemType)) {
          variables[assetType.itemIdStr] = JSON.stringify([]);
        } else {
          //マスター
          variables[assetType.itemIdStr] = JSON.stringify(assetType.defaultData);
        }
      } else if (assetType.itemType == 'checkbox') {
        if (assetType.optionObject.checkboxMultiFlg == '1') {
          //複数
          variables[assetType.itemIdStr] = JSON.stringify(assetType.defaultData);
        } else {
          variables[assetType.itemIdStr] = assetType.defaultData;
        }
      } else if (assetType.itemType == 'userSelect') {
        variables[assetType.itemIdStr] = JSON.stringify(assetType.defaultData);
      } else if (assetType.itemType == 'number' || assetType.itemType == 'currency') {
        variables[assetType.itemIdStr] = _.toString(assetType.defaultData).replace(/,/g, '');
      } else {
        variables[assetType.itemIdStr] = assetType.defaultData;
      }
    });
    variables['commentItem'] = JSON.stringify(comments);

    return variables;
  }

  /**
   * @description: 申請
   * @return {type}
   * @param assetDict
   * @param comments
   * @param processDefinitionId
   * @param customizeViewComponent
   * @param assignDynamicData
   */
  async apply(applyWithDic: {
    assetDict: { [key: string]: AssetTypeItem[] };
    comments: any;
    processDefinitionId: string;
    customizeViewComponent: AfCustomizeViewComponent;
    assignDynamicData: any;
    backUrl: string;
    workflowScript;
    raiseDic: { isCustomizedButtonName: boolean; nameRaise: string };
  }) {
    const {
      assetDict,
      comments,
      processDefinitionId,
      customizeViewComponent,
      assignDynamicData,
      backUrl,
      workflowScript,
      raiseDic,
    } = applyWithDic;
    const nameRaise = _.isEmpty(raiseDic?.nameRaise) ? '提出' : raiseDic?.nameRaise;
    this.baseAction(nameRaise, customizeViewComponent, this.newURL, true, false, async () => {
      try {
        await this.http.addLoadingQueue();
        // カスタマイズロジック実行、（資産の新規作成、アップデート、取得）
        let resultCus = await customizeViewComponent.runJavascriptTogetAssetOrUpdateAssetOrInsertAsset(
          workflowScript,
          false,
          'startNew',
        );
        console.log('result987654 ++++======+++++++', resultCus);
        if (resultCus === false) {
          return;
        }
        const formData = this.getFormData(assetDict, comments); //共通
        formData.append('processDefinitionId', processDefinitionId);
        if (assignDynamicData !== null && assignDynamicData !== undefined && assignDynamicData.length > 0) {
          formData.append('assignDynamicData', JSON.stringify(assignDynamicData));
        }
        // 后台新增字段，主要为了区分开点击的提出名字传给后台，即使是默认的提出两个字也要传给后台
        formData.append('buttonName', nameRaise);
        const result = await this.workflowGetApplicationWorkflowListService.newWorkFlowStart(formData);
        // 点击保存按钮后删除S3文件
        deleteFileFromS3(this.http);
        this.actionWhenFinish('提出', _.isEmpty(backUrl) ? this.newURL : backUrl);
        console.log(result);
      } finally {
        await this.http.removeLoadingQueue();
      }
    });
  }

  async getWorkflowAssignDynamicTaskListByTaskId(processDefinitionId: string, taskDefKey: string) {
    const result = await this.workflowGetApplicationWorkflowListService.getWorkflowAssignDynamicTaskListByTaskId(
      processDefinitionId,
      taskDefKey,
    );
    return result;
  }

  async getWorkFlowSearchByGroupIds(
    processDefinitionId: string,
    taskDefKey: string,
    keyword: string = '',
    needLoading = true,
    delay = true,
  ) {
    const result = await this.workflowGetApplicationWorkflowListService.getWorkFlowSearchByGroupIds(
      processDefinitionId,
      taskDefKey,
      keyword,
      needLoading,
      delay,
    );
    return result;
  }

  async getWorkFlowGetAssignDynamicGroupList(processDefinitionId: string, taskDefKey: string, keyword: string = '') {
    const result = await this.workflowGetApplicationWorkflowListService.getWorkFlowGetAssignDynamicGroupList(
      processDefinitionId,
      taskDefKey,
      keyword,
    );
    return result;
  }

  /**
   * @description:
   * @param {type}
   * @return {type}
   */
  async applyWithAction(
    assetDict: { [key: string]: AssetTypeItem[] },
    comments: any,
    processDefinitionId: string,
    registeredAssetList: [{}],
    assetActionItemList: AssetTypeItem[],
    assetActionId: string,
    appurtenancesInformationTextDic: [Map<string, any>],
    assetTypeId: string,
    processType: string,
    assetActionModel: AssetAction,
    backUrl = this.newURL,
  ) {
    let actionItemList: Array<any> = [];
    assetActionItemList.forEach((item) => {
      if (item.itemType == 'number' || item.itemType == 'currency') {
        item.itemValue = _.toString(item.itemValue).replace(/,/g, '');
        item.defaultData = _.toString(item.defaultData).replace(/,/g, '');
      }
      if (item.itemValue == '<空白>' || item.defaultData == '<空白>') {
        item.itemValue = '';
        item.defaultData = '';
      }
      if (item.valueForShow == '今日' && item.itemValue == 'today') {
        item.defaultData = '今日';
      }
      if (item.valueForShow == '現在' && item.itemValue == 'now') {
        item.defaultData = '現在';
      }
      if (item.itemType == 'date') {
        let countItem: any = {
          method: item.itemName == 'location' ? 1 : item.method,
          option: item.optionObject,
          editable: true,
          itemName: item.itemName,
          itemDisplayName: item.itemDisplayName,
          itemType: item.itemType,
          itemValue: String(item.itemValue).includes('assetItemName:')
            ? item.itemValue
            : item.defaultData == '今日'
              ? 'today'
              : item.defaultData == '現在'
                ? 'now'
                : item.defaultData,
          itemId: item.itemId,
        };
        actionItemList.push(countItem);
      } else {
        let countItem: any = {
          method:
            String(item.itemValue).includes('assetItemName:') ||
            String(item.itemValue).includes('currentUserItem_') ||
            String(item.itemValue).includes('currentUserName') ||
            String(item.itemValue).includes('currentUserEmail')
              ? 2
              : 1,
          option: item.optionObject,
          editable: true,
          itemName: item.itemName,
          itemDisplayName: item.itemDisplayName,
          itemType: item.itemType,
          itemValue: String(item.itemValue).includes('assetItemName:') ? item.itemValue : item.defaultData,
          itemId: item.itemId,
        };
        actionItemList.push(countItem);
      }
    });

    this.baseAction('提出', null, this.newURL, true, false, async () => {
      let variables = this.getVeriables(assetDict, comments); //共通
      let assetIdList: string[] = [];
      if (registeredAssetList) {
        registeredAssetList.forEach((item) => {
          assetIdList.push(item['assetId']);
        });
      }
      assetActionModel.customerActionName = assetActionModel.assetActionName;
      var appurtenancesInformationTypeInfo = {};
      appurtenancesInformationTypeInfo['dataList'] = JSON.stringify(appurtenancesInformationTextDic);
      appurtenancesInformationTypeInfo['appurtenancesInformationTypeId'] =
        assetActionModel.appurtenancesInformationTypeId;
      appurtenancesInformationTypeInfo['appurtenancesInformationTypeName'] =
        assetActionModel.appurtenancesInformationTypeName;

      const dict = {
        assetList: JSON.stringify(assetIdList),
        assetActionId: assetActionId,
        assetTypeId: assetTypeId,
        processType: processType,
        assetActionItem: JSON.stringify(actionItemList),
        customerActionName: assetActionModel.customerActionName,
        processDefinitionId: processDefinitionId,
        appurtenancesInformationTypeInfo:
          appurtenancesInformationTextDic === undefined ? '[]' : JSON.stringify([appurtenancesInformationTypeInfo]),
        variables: variables,
      };
      console.log('🚀------>', dict);
      const result = await this.workflowGetApplicationWorkflowListService.newWorkFlowStartSecond(
        processDefinitionId,
        dict,
      );
      // 点击保存按钮后删除S3文件
      deleteFileFromS3(this.http);
      this.actionWhenFinish('提出', backUrl);
      console.log(result);
    });
  }

  /**
   * @description:
   * @param {type}
   * @return {type}
   */
  async approvalWithAction(
    assetDict: { [key: string]: AssetTypeItem[] },
    comments: any,
    processInstanceId: string,
    processId: string,
    taskId: string,
    progress: string,
  ) {
    this.baseAction('承認', null, null, null, false, async () => {
      let formData = new FormData();
      let variables = this.getMobileVariableData(assetDict, comments);
      formData.append('processInstanceId', processInstanceId);
      formData.append('processId', processId);
      formData.append('taskId', taskId);
      formData.append('mode', '0');
      formData.append('variables', JSON.stringify(variables));
      const result = await this.workflowGetApplicationWorkflowListService.newWorkFlowTransitSecond(formData);
      // 点击保存按钮后删除S3文件
      deleteFileFromS3(this.http);
      console.log(result);
      setTimeout(() => {
        this.setNavigateBack('承認', progress, this.approvalURL);
      }, 500);
    });
  }

  /**
   * @description: setNavigateBack
   * @param {type}
   * @return {type}
   */
  async setNavigateBack(alertText: string, progress: string, url: string) {
    this.navController.navigateBack(url, {
      queryParams: {
        name: alertText,
        progress: progress,
      },
    });
  }

  /**
   * @description: setNavigateBack
   * @param {type}
   * @return {type}
   */
  async setNavigateBackWithoutText(url: string) {
    await this.http.clearLoadingQueue(true);
    this.navController.navigateBack(url, {
      queryParams: {},
    });
  }

  /**
   * @description: agginApply
   * @return {type}
   * @param assetDict
   * @param comments
   * @param processInstanceId
   * @param taskId
   * @param progress
   * @param assignDynamicData
   */
  async aginApply(applyWithDic: {
    assetDict: { [key: string]: AssetTypeItem[] };
    comments: any;
    processInstanceId: string;
    taskId: string;
    progress: string;
    perviewComponent: PerviewComponent;
    workflowScript: string;
    stepName: string;
    assignDynamicData: any[];
    raiseDic?: { isCustomizedButtonName: boolean; nameRaise: string };
  }) {
    const {
      assetDict,
      comments,
      processInstanceId,
      taskId,
      progress,
      perviewComponent,
      workflowScript,
      stepName,
      assignDynamicData,
      raiseDic,
    } = applyWithDic;
    const nameRaise = _.isEmpty(raiseDic?.nameRaise) ? '提出' : raiseDic.nameRaise;
    this.baseAction(nameRaise, null, null, false, false, async () => {
      // run javaScript
      console.log('109809800333666- restart');
      if (perviewComponent) {
        let resultCus = await perviewComponent.runJavaScripInWF(workflowScript, [], stepName, 'restart');
        if (resultCus === false) {
          return;
        }
      }
      let formData = this.getFormData(assetDict, comments); //共通
      if (assignDynamicData !== null && assignDynamicData !== undefined && assignDynamicData.length > 0) {
        formData.append('assignDynamicData', JSON.stringify(assignDynamicData));
      }
      formData.append('processInstanceId', processInstanceId);
      formData.append('taskId', taskId);
      formData.append('mode', '2');
      formData.append('buttonName', nameRaise);
      const result = await this.workflowGetApplicationWorkflowListService.transitFlowStart(formData);
      // 点击保存按钮后删除S3文件
      deleteFileFromS3(this.http);
      console.log(result);
      setTimeout(() => {
        this.setNavigateBack('提出', progress, this.applicationURL);
      }, 500);
    });
  }

  /**
   * @description: activeApplyWithout
   * @return {type}
   * @param assetDict
   * @param comments
   * @param processInstanceId
   * @param progress
   * @param assignDynamicData
   */
  async activeApplyWithout(applyWithDic: {
    assetDict: { [key: string]: AssetTypeItem[] };
    comments: any;
    processInstanceId: string;
    progress: string;
    assignDynamicData: any[];
    perviewComponent: PerviewComponent;
    workflowScript: string;
    stepName: string;
    state: string;
    isMyselfInputTask: boolean;
    raiseDic?: { isCustomizedButtonName: boolean; nameRaise: string };
  }) {
    const {
      assetDict,
      comments,
      processInstanceId,
      progress,
      assignDynamicData,
      perviewComponent,
      workflowScript,
      stepName,
      state,
      isMyselfInputTask,
      raiseDic,
    } = applyWithDic;
    const nameRaise = _.isEmpty(raiseDic?.nameRaise) ? '提出' : raiseDic.nameRaise;
    this.baseAction(nameRaise, null, null, false, false, async () => {
      if (perviewComponent) {
        var WFActionType;
        if (isMyselfInputTask) {
          WFActionType = 'restart';
        } else {
          WFActionType = 'startNew';
        }
        console.log('4847678347834===', WFActionType);
        // run javaScript
        let resultCus = await perviewComponent.runJavaScripInWF(workflowScript, [], stepName, WFActionType);
        if (resultCus === false) {
          return;
        }
      }
      let formData = this.getFormData(assetDict, comments); //共通
      formData.append('processInstanceId', processInstanceId);
      formData.append('status', 'active');
      if (assignDynamicData !== null && assignDynamicData !== undefined && assignDynamicData.length > 0) {
        formData.append('assignDynamicData', JSON.stringify(assignDynamicData));
      }
      formData.append('buttonName', nameRaise);
      const result = await this.workflowGetApplicationWorkflowListService.workflowStartSaveTemporary(formData);
      // 点击保存按钮后删除S3文件
      deleteFileFromS3(this.http);
      console.log(result);
      setTimeout(() => {
        this.setNavigateBack('提出', progress, this.applicationURL);
      }, 500);
    });
  }

  /**
   * @description: activeApply
   * @param {type}
   * @return {type}
   */
  async activeApply(
    assetDict: { [key: string]: AssetTypeItem[] },
    comments: any,
    processDefinitionId: string,
    registeredAssetList: [{}],
    assetActionItemList: AssetTypeItem[],
    assetActionId: string,
    appurtenancesInformationTextDic: [Map<string, any>],
    assetTypeId: string,
    processType: string,
    processId: string,
    assetActionModel: AssetAction,
    customerActionName: string,
    progress: string,
    processInstanceId: string,
  ) {
    let actionItemList: Array<any> = [];
    assetActionItemList.forEach((item) => {
      if (item.itemType == 'number' || item.itemType == 'currency') {
        item.itemValue = _.toString(item.itemValue).replace(/,/g, '');
        item.defaultData = _.toString(item.defaultData).replace(/,/g, '');
      }
      if (item.itemValue == '<空白>' || item.defaultData == '<空白>') {
        item.itemValue = '';
        item.defaultData = '';
      }
      if (item.valueForShow == '今日' && item.itemValue == 'today') {
        item.defaultData = '今日';
      }
      if (item.valueForShow == '現在' && item.itemValue == 'now') {
        item.defaultData = '現在';
      }
      if (item.itemType == 'date') {
        let countItem: any = {
          method: item.itemName == 'location' ? 1 : item.method,
          option: item.optionObject,
          editable: true,
          itemName: item.itemName,
          itemDisplayName: item.itemDisplayName,
          itemType: item.itemType,
          itemValue: String(item.itemValue).includes('assetItemName:')
            ? item.itemValue
            : item.defaultData == '今日'
              ? 'today'
              : item.defaultData == '現在'
                ? 'now'
                : item.defaultData,
          itemId: item.itemId,
        };
        actionItemList.push(countItem);
      } else {
        let countItem: any = {
          method:
            String(item.itemValue).includes('assetItemName:') ||
            String(item.itemValue).includes('currentUserItem_') ||
            String(item.itemValue).includes('currentUserName') ||
            String(item.itemValue).includes('currentUserEmail')
              ? 2
              : 1,
          option: item.optionObject,
          editable: true,
          itemName: item.itemName,
          itemDisplayName: item.itemDisplayName,
          itemType: item.itemType,
          itemValue: String(item.itemValue).includes('assetItemName:') ? item.itemValue : item.defaultData,
          itemId: item.itemId,
        };
        actionItemList.push(countItem);
      }
    });
    this.baseAction('提出', null, null, false, false, async () => {
      // let formData = this.getFormData(assetDict, comments); //共通
      let variables = this.getMobileVariableData(assetDict, comments); //共通
      let assetIdList: string[] = [];
      if (registeredAssetList) {
        registeredAssetList.forEach((item) => {
          assetIdList.push(item['assetId']);
        });
      }

      assetActionModel.customerActionName = assetActionModel.assetActionName;
      var appurtenancesInformationTypeInfo = {};
      appurtenancesInformationTypeInfo['dataList'] = JSON.stringify(appurtenancesInformationTextDic);
      appurtenancesInformationTypeInfo['appurtenancesInformationTypeId'] =
        assetActionModel.appurtenancesInformationTypeId;
      appurtenancesInformationTypeInfo['appurtenancesInformationTypeName'] =
        assetActionModel.appurtenancesInformationTypeName;

      const dict = {
        processId: processId,
        assetList: JSON.stringify(assetIdList),
        assetActionId: assetActionId,
        assetTypeId: assetTypeId,
        processType: processType,
        assetActionItem: JSON.stringify(actionItemList),
        customerActionName: customerActionName,
        processDefinitionId: processDefinitionId,
        appurtenancesInformationTypeInfo:
          appurtenancesInformationTextDic === undefined ? '[]' : JSON.stringify([appurtenancesInformationTypeInfo]),
        variables: JSON.stringify(variables),
        progress: progress,
        processInstanceId: processInstanceId,
      };
      console.log('🍑-------->', dict);
      const result = await this.workflowGetApplicationWorkflowListService.workflowStartSaveMobileTemporary(dict);
      // 点击保存按钮后删除S3文件
      deleteFileFromS3(this.http);
      console.log(result);
      setTimeout(() => {
        this.setNavigateBack('提出', progress, this.applicationURL);
      }, 500);
    });
  }

  /**
   * @description:一時保存
   * @param {type}
   * @return {type}
   */
  async save(
    assetDict: { [key: string]: AssetTypeItem[] },
    comments: any,
    processDefinitionId: string,
    customizeViewComponent: AfCustomizeViewComponent,
    isSaveAction: boolean,
    backUrl: string = this.newURL,
  ) {
    var tempAssetDict;
    tempAssetDict = assetDict;
    for (const sectionName of Object.keys(tempAssetDict)) {
      tempAssetDict[sectionName].forEach((item) => {
        if (item.itemName === 'WF名' && !item.defaultData) {
          item.defaultData = '';
        }
      });
    }
    this.baseAction('一時保存', customizeViewComponent, backUrl, true, isSaveAction, async () => {
      try {
        await this.http.addLoadingQueue();
        let formData = this.getFormData(tempAssetDict, comments); //共通
        formData.append('processDefinitionId', processDefinitionId);
        const result = await this.workflowGetApplicationWorkflowListService.saveTemporaryWorkFlow(formData);
        // 点击保存按钮后删除S3文件
        deleteFileFromS3(this.http);
        this.actionWhenFinish('一時保存', backUrl);
        console.log(result);
      } finally {
        await this.http.removeLoadingQueue();
      }
    });
  }

  /**
   * @description:startSaveTemporary
   * @param {type}
   * @return {type}
   */
  async startSaveTemporary(
    assetDict: { [key: string]: AssetTypeItem[] },
    comments: any,
    processInstanceId: string,
    progress: string,
  ) {
    this.baseAction('一時保存', null, null, false, false, async () => {
      try {
        await this.http.addLoadingQueue();
        let formData = this.getFormData(assetDict, comments); //共通
        formData.append('processInstanceId', processInstanceId);
        formData.append('status', 'update');
        const result = await this.workflowGetApplicationWorkflowListService.workflowStartSaveTemporary(formData);
        // 点击保存按钮后删除S3文件
        deleteFileFromS3(this.http);
        console.log(result);
        //重置flag和备份用的资产list
        clearLocalData();
      } finally {
        await this.http.removeLoadingQueue(true);
      }
      setTimeout(() => {
        this.setNavigateBack('一時保存', progress, this.applicationURL);
      }, 500);
    });
  }

  /**
   * @description:
   * @param {type}
   * @return {type}
   */
  async saveWithAction(
    assetDict: { [key: string]: AssetTypeItem[] },
    comments: any,
    processDefinitionId: string,
    customizeViewComponent: AfCustomizeViewComponent,
    assetAction: AssetAction,
    workflowTypeCode: string,
    backUrl: string = this.newURL,
  ) {
    this.baseAction('一時保存', customizeViewComponent, backUrl, true, false, async () => {
      try {
        await this.http.addLoadingQueue();
        let formData = this.getFormData(assetDict, comments); //共通
        formData.append('processDefinitionId', processDefinitionId);
        let mapAction = new Map();
        mapAction['assetList'] = [];
        mapAction['assetActionId'] = assetAction.assetActionId;
        mapAction['assetTypeId'] = assetAction.assetTypeId;
        mapAction['processType'] = workflowTypeCode;
        mapAction['customerActionName'] = '';
        formData.append('assetActionData', JSON.stringify(mapAction));
        const result = await this.workflowGetApplicationWorkflowListService.saveTemporaryWorkFlow(formData);
        // 点击保存按钮后删除S3文件
        deleteFileFromS3(this.http);
        this.actionWhenFinish('一時保存', backUrl);
        console.log(result);
      } finally {
        await this.http.removeLoadingQueue();
      }
    });
  }

  /**
   * キャンセルの確認ダイアログ
   */
  async presentCancelConfirm(
    url: string,
    processInstanceId: any = null,
    taskId: any = null,
    editAssetList: any = null,
    params: any = null,
  ) {
    const alert = await this.alertController.create({
      message: '保存せずに終了しますか？入力したデータ・及びスキャンデータは破棄されます。',
      buttons: [
        {
          text: 'キャンセル',
          role: 'cancel',
          cssClass: 'common-alert-text-primary',
        },
        {
          text: 'OK',
          role: 'ok',
          cssClass: 'common-alert-text-primary font-weight-bold',
          handler: async () => {
            if (processInstanceId && taskId) {
              await backOkayFunAtUserTask();
            } else {
              if (params) {
                this.navController.navigateBack(url, params);
              } else {
                this.navController.navigateBack(url);
              }
            }
          },
        },
      ],
    });
    // 在user task的时候，确认按钮
    const backOkayFunAtUserTask = async () => {
      await this.http.addLoadingQueue();
      try {
        // 当用户操作了或者扫描了资产才去调用重置API，否则直接返回到上一个页面
        const formData = new FormData();
        formData.append('processInstanceId', _.toString(processInstanceId));
        formData.append('taskId', _.toString(taskId));
        if (editAssetList) {
          formData.append('assetListDataJson', JSON.stringify(editAssetList));
        } else {
          formData.append('assetListDataJson', JSON.stringify([]));
        }
        let result = await this.isRestoreAllAssets();
        if (result) {
          let assetList = await StorageUtils.get(StorageUtils.TEMP_EDIT_ASSET_LIST);
          formData.append('clearedAssetListDataJson', assetList);
        } else {
          formData.append('clearedAssetListDataJson', JSON.stringify([]));
        }

        let estoreResult = await this.workflowGetApplicationWorkflowListService.restoreAssetScanState(formData);
        // 点击保存按钮后删除S3文件
        deleteFileFromS3(this.http);
        if (params) {
          this.navController.navigateBack(url, params);
        } else {
          this.navController.navigateBack(url);
        }
      } finally {
        await this.http.removeLoadingQueue();
      }
    };
    await alert.present();
  }

  /**
   * 确认是否进行了一扩删除动作
   */
  async isRestoreAllAssets() {
    let assetList = await StorageUtils.get(StorageUtils.TEMP_EDIT_ASSET_LIST);
    return assetList && assetList != '[]' ? true : false;
  }

  async applyWithList(applyWithDic: {
    assetDict: { [key: string]: AssetTypeItem[] };
    comments: any;
    processDefinitionId: string;
    processInstanceId: string;
    registeredAssetList: any[];
    workflowName: string;
    assetTypeId: string;
    isNew: boolean;
    state: string;
    taskId: string;
    progress: string;
    assetListId: string;
    updateAmountShow: boolean;
    assignDynamicData: Array<any>;
    newBackUrl: string;
    isFromNew: boolean;
    isMyselfInputTask: boolean;
    raiseDic: { isCustomizedButtonName: boolean; nameRaise: string };
    perviewComponent: PerviewComponent | AfCustomizeViewComponent;
    actionType: string;
    stepName: string;
    workflowScript: any;
  }) {
    const {
      assetDict,
      comments,
      processDefinitionId,
      processInstanceId,
      registeredAssetList,
      workflowName,
      assetTypeId,
      isNew,
      state,
      taskId,
      progress,
      assetListId,
      updateAmountShow,
      assignDynamicData,
      newBackUrl,
      isFromNew,
      isMyselfInputTask,
      raiseDic,
      perviewComponent,
      actionType,
      stepName,
      workflowScript,
    } = applyWithDic;
    const nameRaise = _.isEmpty(raiseDic?.nameRaise) ? '提出' : raiseDic?.nameRaise;
    this.baseAction(nameRaise, null, null, false, false, async () => {
      if (perviewComponent instanceof PerviewComponent) {
        const result = await perviewComponent.runJavaScripInWF(
          workflowScript,
          registeredAssetList,
          stepName,
          actionType,
        );
        if (result === false) {
          return;
        }
      }
      if (perviewComponent instanceof AfCustomizeViewComponent) {
        const resultCus = await perviewComponent.runJavascriptTogetAssetOrUpdateAssetOrInsertAsset(
          workflowScript,
          false,
          actionType,
        );
        if (resultCus === false) {
          return;
        }
      }

      let formData = this.getFormData(assetDict, comments); //共通
      let assetIdList: string[] = [];
      let eachAssetList = [];
      // 只有新规或者一时保存之后的提出时候用
      if (isFromNew) {
        if (registeredAssetList) {
          registeredAssetList.forEach((item) => {
            if (updateAmountShow) {
              // 個数管理の場合
              eachAssetList.push({
                assetId: item['assetId'],
                willChangeAmount: item['assetScanedCount'],
              });
            } else {
              // 個体管理の場合
              assetIdList.push(item['assetId']);
            }
          });
        }

        console.log(assetIdList);
        let map = new Map();
        if (updateAmountShow) {
          // 個数管理の場合
          map['eachAssetList'] = eachAssetList;
        } else {
          // 個体管理の場合
          map['assetIds'] = assetIdList;
        }

        map['assetListTitle'] = workflowName;
        map['assetTypeId'] = assetTypeId;
        map['assetListId'] = assetListId;
        formData.append('assetList', JSON.stringify(map));
      }
      if (assignDynamicData !== null && assignDynamicData !== undefined && assignDynamicData.length > 0) {
        formData.append('assignDynamicData', JSON.stringify(assignDynamicData));
      }
      if (isNew) {
        // 正常提出
        formData.append('processDefinitionId', processDefinitionId);
        // 后台新增字段，主要为了区分开点击的提出名字传给后台，即使是默认的提出两个字也要传给后台
        formData.append('buttonName', nameRaise);
        const result = await this.workflowGetApplicationWorkflowListService.startWithThirdFlow(formData);
        // 点击保存按钮后删除S3文件
        deleteFileFromS3(this.http);
        this.actionWhenFinish('提出', _.isEmpty(newBackUrl) ? this.newURL : newBackUrl);
      } else {
        if (this.customBooleanCheck(isMyselfInputTask)) {
          // 差し戻し
          formData.append('taskId', taskId);
          formData.append('mode', '2');
          formData.append('processInstanceId', processInstanceId);
          formData.append('buttonName', raiseDic?.nameRaise ?? '');
          const result = await this.workflowGetApplicationWorkflowListService.transitFlowStart(formData);
          // 点击保存按钮后删除S3文件
          deleteFileFromS3(this.http);
          console.log(result);
        } else {
          // 一时保存
          formData.append('status', 'active');
          formData.append('processInstanceId', processInstanceId);
          formData.append('buttonName', raiseDic?.nameRaise ?? '');
          const result = await this.workflowGetApplicationWorkflowListService.workflowStartSaveTemporary(formData);
          // 点击保存按钮后删除S3文件
          deleteFileFromS3(this.http);
          console.log(result);
        }
        //重置flag和备份用的资产list
        clearLocalData();
        setTimeout(() => {
          this.setNavigateBack('提出', progress, this.applicationURL);
        }, 500);
      }
    });
  }

  /**
   * パラメータをブール値に変換します。
   * パラメータがブール型の場合、そのブール値をそのまま返します。
   * パラメータが文字列型で空でない場合、文字列をブール値に変換して返します。
   * それ以外の場合、false を返します。
   *
   * @param value 変換する値
   * @returns 変換後のブール値
   */
  customBooleanCheck(value: any): boolean {
    if (typeof value === 'boolean') {
      return value;
    } else if (typeof value === 'string') {
      const trimmedString = value.trim();
      if (trimmedString !== '') {
        return trimmedString.toLowerCase() === 'true';
      }
    }
    return false; // 默认返回 false
  }

  /**
   * @description:
   * @param {type}
   * @return {type}
   */
  async applyWithMaihamaList(applyDic: {
    assetDict: { [key: string]: AssetTypeItem[] };
    comments: any;
    processDefinitionId: string;
    processInstanceId: string;
    registeredAssetList: [];
    isNew: boolean;
    progress: string;
    raiseDic: { isCustomizedButtonName: boolean; nameRaise: string };
  }) {
    const {
      assetDict,
      comments,
      processDefinitionId,
      processInstanceId,
      registeredAssetList,
      isNew,
      progress,
      raiseDic,
    } = applyDic;
    let formData = this.getFormData(assetDict, comments); //共通
    let amountActionAssetList = [];
    if (registeredAssetList) {
      registeredAssetList.forEach((item) => {
        let map = new Map();
        map['assetId'] = item['assetId'];
        map['amount'] = item['amount'];
        amountActionAssetList.push(map);
      });
    }

    formData.append('amountActionAssetList', JSON.stringify(amountActionAssetList));
    if (isNew) {
      this.baseAction(
        _.isEmpty(raiseDic?.nameRaise) ? '提出' : raiseDic?.nameRaise,
        null,
        this.newURL,
        true,
        false,
        async () => {
          formData.append('processDefinitionId', processDefinitionId);
          const result = await this.workflowGetApplicationWorkflowListService.startWithThirdFlow(formData);
          // 点击保存按钮后删除S3文件
          deleteFileFromS3(this.http);
          this.actionWhenFinish('提出', this.newURL);
          console.log(result);
        },
      );
    } else {
      this.baseAction(
        _.isEmpty(raiseDic?.nameRaise) ? '提出' : raiseDic?.nameRaise,
        null,
        null,
        false,
        false,
        async () => {
          formData.append('status', 'active');
          formData.append('processInstanceId', processInstanceId);
          const result = await this.workflowGetApplicationWorkflowListService.workflowStartSaveTemporary(formData);
          // 点击保存按钮后删除S3文件
          deleteFileFromS3(this.http);
          console.log(result);
          //重置flag和备份用的资产list
          clearLocalData();
          setTimeout(() => {
            this.setNavigateBack('提出', progress, this.applicationURL);
          }, 500);
        },
      );
    }
  }

  /**
   * @description: 舞浜
   * @param {type}
   * @return {type}
   */
  async approvalWithMaihamaList(approvalDic: {
    assetDict: { [key: string]: AssetTypeItem[] };
    comments: any;
    processInstanceId: string;
    amountActionAssetList: any;
    taskId: string;
    progress: string;
    raiseDic: { isCustomizedButtonName: boolean; nameRaise: string };
  }) {
    const { assetDict, comments, processInstanceId, amountActionAssetList, taskId, progress, raiseDic } = approvalDic;
    const nameRaise = _.isEmpty(raiseDic?.nameRaise) ? '承認' : raiseDic.nameRaise;
    this.baseAction(nameRaise, null, null, false, false, async () => {
      let formData = this.getFormData(assetDict, comments); //共通
      formData.append('processInstanceId', processInstanceId);
      formData.append('taskId', taskId);
      formData.append('mode', '0');
      let list = [];
      JSON.parse(amountActionAssetList).forEach((item) => {
        let map = new Map();
        map['assetId'] = item['assetId'];
        map['amount'] = item['amount'];
        list.push(map);
      });
      formData.append('amountActionAssetList', JSON.stringify(list));
      // 后台新增字段，主要为了区分开点击的承認名字传给后台，即使是默认的承認两个字也要传给后台
      formData.append('buttonName', nameRaise);
      const result = await this.workflowGetApplicationWorkflowListService.approvalWorkFlow(formData);
      // 点击保存按钮后删除S3文件
      deleteFileFromS3(this.http);
      console.log(result);
      //重置flag和备份用的资产list
      clearLocalData();
      setTimeout(() => {
        this.setNavigateBack('承認', progress, this.applicationURL);
      }, 500);
    });
  }

  /**
   * @description: 普通
   * @return {type}
   * @param assetDict
   * @param comments
   * @param processInstanceId
   * @param taskId
   * @param progress
   * @param backpage
   * @param assignDynamicData
   */
  async approval(approvalDic: {
    assetDict: { [key: string]: AssetTypeItem[] };
    comments: any;
    processInstanceId: string;
    taskId: string;
    progress: string;
    backpage: string;
    assignDynamicData: any[];
    perviewComponent: PerviewComponent;
    workflowScript: string;
    stepName: string;
    callBack: (that: any, result: any) => void;
    that: any;
    raiseDic: { isCustomizedButtonName: boolean; nameRaise: string };
  }) {
    const {
      assetDict,
      comments,
      processInstanceId,
      taskId,
      progress,
      backpage,
      assignDynamicData,
      perviewComponent,
      workflowScript,
      stepName,
      callBack,
      that,
      raiseDic,
    } = approvalDic;
    const nameRaise = _.isEmpty(raiseDic?.nameRaise) ? '承認' : raiseDic?.nameRaise;
    this.baseAction(nameRaise, null, null, false, false, async () => {
      // run javaScript
      console.log('11112223333666-admit');
      if (perviewComponent) {
        let resultCus = await perviewComponent.runJavaScripInWF(workflowScript, [], stepName, 'admit');
        if (resultCus === false) {
          return;
        }
      }

      const formData = this.getFormData(assetDict, comments); //共通
      formData.append('processInstanceId', processInstanceId);
      formData.append('taskId', taskId);
      formData.append('mode', '0');
      if (assignDynamicData !== null && assignDynamicData !== undefined && assignDynamicData.length > 0) {
        formData.append('assignDynamicData', JSON.stringify(assignDynamicData));
      }
      // 后台新增字段，主要为了区分开点击的承認名字传给后台，即使是默认的承認两个字也要传给后台
      formData.append('buttonName', nameRaise);
      if (callBack) {
        callBack(that, true);
      }
      try {
        const result = await this.workflowGetApplicationWorkflowListService.approvalWorkFlow(formData);
        // 点击保存按钮后删除S3文件
        deleteFileFromS3(this.http);
        console.log(result);
      } catch {
        if (callBack) {
          callBack(that, false);
        }
      }
      setTimeout(() => {
        this.setNavigateBack('承認', progress, _.isEmpty(backpage) ? this.applicationURL : backpage);
        if (callBack) {
          callBack(that, false);
        }
      }, 500);
    });
  }

  /**
   * @description: 普通
   * @param {type}
   * @return {type}
   */
  async approvalFromApproval(approvalDic: {
    assetDict: { [key: string]: AssetTypeItem[] };
    comments: any;
    processInstanceId: string;
    taskId: string;
    progress: string;
    assignDynamicData: any[];
    perviewComponent: PerviewComponent;
    workflowScript: string;
    stepName: string;
    callBack: (that: any, result: any) => void;
    that: any;
    raiseDic: { isCustomizedButtonName: boolean; nameRaise: string };
  }) {
    this.approval({ ...approvalDic, backpage: this.approvalURL });
  }

  /**
   * @description: 普通
   * @param {type}
   * @return {type}
   */
  async approvalWithList(approvalDic: {
    assetDict: { [key: string]: AssetTypeItem[] };
    comments: any;
    processInstanceId: string;
    datasCount: string;
    taskId: string;
    progress: string;
    assignDynamicData: Array<any>;
    perviewComponent: PerviewComponent;
    stepName: string;
    workflowScript: string;
    isAssetListEditable: boolean;
    raiseDic?: { isCustomizedButtonName: boolean; nameRaise: string };
  }) {
    const {
      assetDict,
      comments,
      processInstanceId,
      datasCount,
      taskId,
      progress,
      assignDynamicData,
      perviewComponent,
      stepName,
      workflowScript,
      isAssetListEditable,
      raiseDic,
    } = approvalDic;
    const nameRaise = _.isEmpty(raiseDic?.nameRaise) ? '承認' : raiseDic?.nameRaise;
    this.baseAction(nameRaise, null, null, false, false, async () => {
      // run javaScript
      console.log('82344338888-admit');
      if (perviewComponent) {
        let resultCus = await perviewComponent.runJavaScripInWF(workflowScript, [], stepName, 'admit');
        if (resultCus === false) {
          return;
        }
      }

      const confirm = async () => {
        let formData = this.getFormData(assetDict, comments); //共通

        formData.append('processInstanceId', processInstanceId);
        formData.append('taskId', taskId);
        formData.append('mode', '0');
        // 后台新增字段，主要为了区分开点击的承認名字传给后台，即使是默认的承認两个字也要传给后台
        formData.append('buttonName', nameRaise);
        if (assignDynamicData !== undefined && assignDynamicData !== null && assignDynamicData.length > 0) {
          formData.append('assignDynamicData', JSON.stringify(assignDynamicData));
        }
        const result = await this.workflowGetApplicationWorkflowListService.approvalWorkFlow(formData);
        // 点击保存按钮后删除S3文件
        deleteFileFromS3(this.http);
        console.log(result);
        //重置flag和备份用的资产list
        clearLocalData();
        setTimeout(() => {
          this.setNavigateBack('承認', progress, this.applicationURL);
        }, 500);
      };

      // 承認時資産が選択されない場合
      if (datasCount == '0' && isAssetListEditable) {
        const alert = await this.alertController.create({
          message: '資産が選択されていませんが、承認しますか？',
          buttons: [
            {
              text: 'キャンセル',
              role: 'cancel',
              cssClass: 'common-alert-text-primary',
              handler: async () => {},
            },
            {
              text: 'OK',
              role: 'ok',
              cssClass: 'common-alert-text-primary font-weight-bold',
              handler: async () => {
                try {
                  await this.http.addLoadingQueue();
                  await confirm();
                } finally {
                  await this.http.removeLoadingQueue();
                }
              },
            },
          ],
        });
        await alert.present();
      } else {
        await confirm();
      }
    });
  }

  /**
   * @description: 普通
   * @param {type}
   * @return {type}
   */
  async approvalWithActionData(
    assetDict: { [key: string]: AssetTypeItem[] },
    comments: any,
    processInstanceId: string,
    assetActionData: any,
    taskId: string,
    progress: string,
  ) {
    this.baseAction('承認', null, null, false, false, async () => {
      let formData = this.getFormData(assetDict, comments); //共通
      formData.append('processInstanceId', processInstanceId);
      formData.append('taskId', taskId);
      formData.append('mode', '0');
      let assetAction = {};
      assetAction['processId'] = assetActionData.processId;
      assetAction['processType'] = assetActionData.processType;
      assetAction['assetList'] = '[null]';
      formData.append('assetActionData', JSON.stringify(assetAction));
      const result = await this.workflowGetApplicationWorkflowListService.approvalWorkFlow(formData);
      // 点击保存按钮后删除S3文件
      deleteFileFromS3(this.http);
      console.log(result);
      setTimeout(() => {
        this.setNavigateBack('承認', progress, this.applicationURL);
      }, 500);
    });
  }

  /**
   * @description: 普通
   * @param {type}
   * @return {type}
   */
  async confirmWithActionData(
    assetDict: { [key: string]: AssetTypeItem[] },
    comments: any,
    processInstanceId: string,
    assetActionData: any,
    assetList: any,
    taskId: string,
    progress: string,
    isApproval: boolean = true,
    perviewComponent = null,
    workflowScript = null,
    stepName = null,
    backPage: string = this.approvalURL,
  ) {
    let actionName = isApproval ? '承認' : '否決';

    this.baseAction(actionName, null, null, false, false, async () => {
      var actionNameCus;
      if (actionName === '承認') {
        actionNameCus = 'admit';
      } else if (actionName === '否決') {
        actionNameCus = 'deny';
      }
      if (perviewComponent) {
        // run javaScript
        console.log('89123458888888===', actionNameCus);
        let resultCus = await perviewComponent.runJavaScripInWF(workflowScript, [], stepName, actionNameCus);
        if (resultCus === false) {
          return;
        }
      }
      let formData = this.getFormData(assetDict, comments); //共通
      formData.append('processInstanceId', processInstanceId);
      formData.append('taskId', taskId);
      formData.append('mode', isApproval ? '0' : '1');
      let assetAction = {};
      assetAction['processId'] = assetActionData.processId;
      assetAction['processType'] = '1';

      var assetIDList = [];
      assetList.forEach((value) => {
        assetIDList.push(value.assetId);
      });
      assetAction['assetList'] = JSON.stringify(assetIDList);

      formData.append('assetActionData', JSON.stringify(assetAction));
      const result = await this.workflowGetApplicationWorkflowListService.approvalWorkFlow(formData);
      // 点击保存按钮后删除S3文件
      deleteFileFromS3(this.http);
      console.log(result);
      setTimeout(() => {
        this.setNavigateBack(actionName, progress, backPage);
      }, 500);
    });
  }

  /**
   * @description: 普通
   * @param {type}
   * @return {type}
   */
  async confirmWithMaihamaList(
    assetDict: { [key: string]: AssetTypeItem[] },
    comments: any,
    processInstanceId: string,
    assetList: any,
    taskId: string,
    progress: string,
    isApproval: boolean = true,
    backpage: string = this.approvalURL,
  ) {
    let actionName = isApproval ? '承認' : '否決';
    this.baseAction(actionName, null, null, false, false, async () => {
      let formData = this.getFormData(assetDict, comments); //共通
      formData.append('processInstanceId', processInstanceId);
      formData.append('taskId', taskId);
      formData.append('mode', isApproval ? '0' : '1');
      let amountActionAssetList = [];
      if (assetList) {
        assetList.forEach((value) => {
          let amountActionAsset = {
            assetId: value['assetId'],
            amount: value['amount'],
          };
          amountActionAssetList.push(amountActionAsset);
        });
      }

      formData.append('amountActionAssetList', JSON.stringify(amountActionAssetList));

      const result = await this.workflowGetApplicationWorkflowListService.approvalWorkFlow(formData);
      // 点击保存按钮后删除S3文件
      deleteFileFromS3(this.http);
      console.log(result);
      setTimeout(() => {
        this.setNavigateBack(actionName, progress, backpage);
      }, 500);
    });
  }

  /**
   * @description:claim
   * @param {type}
   * @return {type}
   */
  async claim(
    assetDict: { [key: string]: AssetTypeItem[] },
    comments: any,
    processInstanceId: string,
    taskId: string,
    callback,
  ) {
    this.baseAction('ピッキング', null, null, false, false, async () => {
      // let formData = this.getFormData(assetDict, comments); //共通
      // formData.append('processInstanceId', processInstanceId);
      // formData.append('taskId', taskId);
      let veriables = this.getVeriables(assetDict, comments); //共通
      let param = {
        processInstanceId: processInstanceId,
        taskId: taskId,
        variables: veriables,
      };

      // processInstanceId commentItem taskId
      const result = await this.workflowGetApplicationWorkflowListService.ClaimWorkFlow(param);
      // 点击保存按钮后删除S3文件
      deleteFileFromS3(this.http);
      console.log('claim ==> baseAction ==> callback ===>: ', result);
      callback(result);
    });
  }

  /**
   * @description:unclaim
   * @param {type}
   * @return {type}
   */
  async unclaim(
    assetDict: { [key: string]: AssetTypeItem[] },
    comments: any,
    processInstanceId: string,
    taskId: string,
    progress: string,
    backPage: string = this.applicationURL,
  ) {
    this.baseAction('承認', null, null, false, false, async () => {
      try {
        await this.http.addLoadingQueue();
        let formData = this.getFormData(assetDict, comments); //共通
        formData.append('processInstanceId', processInstanceId);
        formData.append('taskId', taskId);
        const result = await this.workflowGetApplicationWorkflowListService.workflowUnclaim(formData);
        // 点击保存按钮后删除S3文件
        deleteFileFromS3(this.http);
        //重置flag和备份用的资产list
        clearLocalData();
        console.log('unclaim ==> baseAction ==> callback ===>: ', result);
        // console.log("zlt unclaim assetDict: " + JSON.stringify(assetDict));
      } finally {
        await this.http.removeLoadingQueue(true);
      }
      setTimeout(() => {
        this.setNavigateBack('ご自身に割り当てられたタスクを解除', progress, backPage);
      }, 500);
    });
  }

  /**
   * @description:unclaim
   * @param {type}
   * @return {type}
   */
  async unclaimFormApproval(
    assetDict: { [key: string]: AssetTypeItem[] },
    comments: any,
    processInstanceId: string,
    taskId: string,
    progress: string,
    callBack,
    that,
    backPage: string = this.approvalURL,
  ) {
    // this.unclaim(assetDict,comments,processInstanceId,taskId, progress, this.approvalURL)

    let itemArr = _.flatMap(assetDict);
    if (itemArr) {
      for (let i = 0; i < itemArr.length; i++) {
        let item = itemArr[i];
        let optionObject = item['optionObject'];
        if (_.isEmpty(optionObject)) {
          optionObject = JSON.parse(item.option);
        }
        // true 不可以编辑（只读），false可以编辑
        const isReadonly = _.isEmpty(_.toString(optionObject['readonly']))
          ? false
          : _.toString(optionObject['readonly']) === '1'
            ? true
            : false;
        if (isReadonly) {
          continue;
        }
        if (item.inputFlg === '1') {
          if (
            item.defaultData == '' ||
            item.defaultData == undefined ||
            item.defaultData == null ||
            Object.keys(item.defaultData).length === 0 ||
            String(item.defaultData).trim() === '' ||
            item.defaultData == '[]'
          ) {
            alert(item.itemName + 'を設定してください。');
            return;
          }
        }

        if (item.inputFlg === '1' && item.itemType === 'checkbox') {
          if (item.defaultData) {
            if (typeof item.defaultData === 'string') {
              if (item.defaultData !== '1') {
                let cbName = item.itemName ?? item.itemDisplayName;
                alert(cbName + 'を設定してください。');
                return;
              }
            }
          }
        }

        if (item.isShowMessageTS) {
          alert('入力エラーがあるのでご確認ください。');
          return;
        }
        if (item.defaultData) {
          // 桁数チェック
          if (item.optionObject.maxlength && typeof item.defaultData === 'string') {
            if (item.defaultData.length > item.optionObject.maxlength) {
              alert(`${item.itemName}を${item.optionObject.maxlength}文字以内に入力してください。`);
              return;
            }
          }

          // 数字或通货类型输入位数检查
          if (item.itemType === 'number' || item.itemType === 'currency') {
            const valideResult = validateNumericAndCurrency(item.defaultData,false);
            if (valideResult !== '') {
              if (item.itemDisplayName) {
                alert(`${item.itemDisplayName}は${valideResult}`);
              } else {
                alert(`${item.itemName}は${valideResult}`);
              }
              return false;
            }
          }

          // メールアドレスの有効性チェック
          if (item.itemType === 'email') {
            const reg = /^(\w+|[-+.])*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
            if (!reg.test(item.defaultData)) {
              alert(`${item.itemName}のフォーマットが間違っています。`);
              return;
            }
          }
        }
      }
    }

    this.baseAction('ご自身に割り当てられたタスクを解除', null, null, false, false, async () => {
      try {
        await this.http.addLoadingQueue();
        let formData = this.getFormData(assetDict, comments); //共通
        formData.append('processInstanceId', processInstanceId);
        formData.append('taskId', taskId);
        if (callBack) {
          callBack(that, true);
        }
        try {
          const result = await this.workflowGetApplicationWorkflowListService.workflowUnclaim(formData);
          // 点击保存按钮后删除S3文件
          deleteFileFromS3(this.http);
          //
          clearLocalData();
          console.log(result);
        } catch {
          if (callBack) {
            callBack(that, false);
          }
        }
        setTimeout(() => {
          this.setNavigateBack('ご自身に割り当てられたタスクを解除', progress, backPage);
          if (callBack) {
            callBack(that, false);
          }
        }, 500);
      } finally {
        await this.http.removeLoadingQueue(true);
      }
    });
  }

  /**
   * @description:
   * @param {type}
   * @return {type}
   */
  async rejection(rejectionWithDic: {
    assetDict: { [key: string]: AssetTypeItem[] };
    comments: [];
    processInstanceId: string;
    taskId: string;
    progress: string;
    perviewComponent: PerviewComponent;
    workflowScript;
    stepName: string;
    callBack: (that: any, result: any) => void;
    that: any;
    backPage: string;
    buttonName: string;
  }) {
    const {
      assetDict,
      comments,
      processInstanceId,
      taskId,
      progress,
      perviewComponent,
      workflowScript,
      stepName,
      callBack,
      that,
      backPage,
      buttonName,
    } = rejectionWithDic;
    this.baseAction(_.isEmpty(buttonName) ? '否決' : buttonName, null, null, false, false, async () => {
      try {
        await this.http.addLoadingQueue();
        // run javaScript
        console.log('82898764538888-deny');
        if (perviewComponent) {
          let resultCus = await perviewComponent.runJavaScripInWF(workflowScript, [], stepName, 'deny');
          if (resultCus === false) {
            if (callBack) {
              callBack(that, false);
            }
            return;
          }
        }

        let formData = this.getFormData(assetDict, comments); //共通
        formData.append('processInstanceId', processInstanceId);
        formData.append('taskId', taskId);
        formData.append('mode', '1');
        formData.append('buttonName', _.isEmpty(buttonName) ? '否決' : buttonName);
        if (callBack) {
          callBack(that, true);
        }
        try {
          const result = await this.workflowGetApplicationWorkflowListService.approvalWorkFlow(formData);
          // 点击保存按钮后删除S3文件
          deleteFileFromS3(this.http);
          console.log(result);
        } catch {
          if (callBack) {
            callBack(that, false);
          }
        }
        setTimeout(() => {
          this.setNavigateBack('否決', progress, _.isEmpty(backPage) ? this.applicationURL : backPage);
          if (callBack) {
            callBack(that, false);
          }
        }, 500);
      } finally {
        await this.http.removeLoadingQueue(true);
      }
    });
  }

  /**
   * @description:
   * @param {type}
   * @return {type}
   */
  async rejectionFromApproval(rejectApprovalDic: {
    assetDict: { [key: string]: AssetTypeItem[] };
    comments: [];
    processInstanceId: string;
    taskId: string;
    progress: string;
    perviewComponent: PerviewComponent;
    workflowScript;
    stepName: string;
    callBack: (that: any, result: any) => void;
    that: any;
    buttonName: string;
  }) {
    const {
      assetDict,
      comments,
      processInstanceId,
      taskId,
      progress,
      perviewComponent,
      workflowScript,
      stepName,
      callBack,
      that,
      buttonName,
    } = rejectApprovalDic;

    this.rejection({
      assetDict: assetDict,
      comments: comments,
      processInstanceId: processInstanceId,
      taskId: taskId,
      progress: progress,
      perviewComponent: perviewComponent,
      workflowScript: workflowScript,
      stepName: stepName,
      callBack: callBack,
      that: that,
      backPage: this.approvalURL,
      buttonName: buttonName,
    });
  }

  /**
   * @description:
   * @param {type}
   * @return {type}
   */
  async rejectionWithActionData(
    assetDict: { [key: string]: AssetTypeItem[] },
    comments: any,
    processInstanceId: string,
    assetActionData: any,
    taskId: string,
    progress: string,
  ) {
    this.baseAction('否決', null, null, false, false, async () => {
      try {
        await this.http.addLoadingQueue();
        let formData = this.getFormData(assetDict, comments); //共通
        formData.append('processInstanceId', processInstanceId);
        formData.append('taskId', taskId);
        let assetAction = {};
        assetAction['processId'] = assetActionData.processId;
        assetAction['processType'] = assetActionData.processType;
        assetAction['assetList'] = '[null]';
        formData.append('assetActionData', JSON.stringify(assetAction));
        formData.append('mode', '1');
        const result = await this.workflowGetApplicationWorkflowListService.approvalWorkFlow(formData);
        // 点击保存按钮后删除S3文件
        deleteFileFromS3(this.http);
        console.log(result);
      } finally {
        await this.http.removeLoadingQueue(true);
      }
      setTimeout(() => {
        this.setNavigateBack('否決', progress, this.applicationURL);
      }, 500);
    });
  }

  /**
   * @description:
   * @param {type}
   * @return {type}
   */
  async rejectionWithList(rejectionWithDic: {
    assetDict: { [key: string]: AssetTypeItem[] };
    comments: any;
    processInstanceId: string;
    assetListDatas: any[];
    assetListTitle: string;
    assetTypeIdWithFirstWf: number;
    taskId: string;
    progress: string;
    perviewComponent;
    workflowScript;
    stepName;
    callBack: (that: any, result: any) => void;
    that;
    buttonName: string;
  }) {
    const {
      assetDict,
      comments,
      processInstanceId,
      assetListDatas,
      assetListTitle,
      assetTypeIdWithFirstWf,
      taskId,
      progress,
      perviewComponent,
      workflowScript,
      stepName,
      callBack,
      that,
      buttonName,
    } = rejectionWithDic;
    this.baseAction(_.isEmpty(buttonName) ? '否決' : buttonName, null, null, false, false, async () => {
      try {
        await this.http.addLoadingQueue();
        // run javaScript
        console.log('000123211333666-deny');
        if (perviewComponent) {
          let resultCus = await perviewComponent.runJavaScripInWF(
            workflowScript,
            assetListDatas,
            stepName,
            'deny',
            processInstanceId,
            taskId,
          );
          if (resultCus === false) {
            return;
          }
        }

        let formData = this.getFormData(assetDict, comments); //共通
        formData.append('processInstanceId', processInstanceId);
        formData.append('taskId', taskId);

        let assetList = {};
        assetList['assetListTitle'] = assetListTitle;
        assetList['assetTypeId'] = assetTypeIdWithFirstWf;
        let assetIdList: string[] = [];
        assetListDatas.forEach((item) => {
          assetIdList.push(item['assetId']);
        });
        assetList['assetIds'] = assetIdList;
        formData.append('assetList', JSON.stringify(assetList));

        formData.append('mode', '1');
        formData.append('buttonName', _.isEmpty(buttonName) ? '否決' : buttonName);
        if (callBack) {
          callBack(that, true);
        }
        try {
          const result = await this.workflowGetApplicationWorkflowListService.approvalWorkFlow(formData);
          // 点击保存按钮后删除S3文件
          deleteFileFromS3(this.http);
          console.log(result);
        } catch {
          if (callBack) {
            callBack(that, false);
          }
        }
        //重置flag和备份用的资产list
        clearLocalData();
        setTimeout(() => {
          this.setNavigateBack('否決', progress, this.applicationURL);
          if (callBack) {
            callBack(that, false);
          }
        }, 500);
      } finally {
        await this.http.removeLoadingQueue(true);
      }
    });
  }

  /**
   * @description:
   * @param {type}
   * @return {type}
   */
  async comfrimWithAssets(comfrimWithDic: {
    assetDict: { [key: string]: AssetTypeItem[] };
    comments: any;
    processInstanceId: string;
    assetList: Asset[];
    assetListDatas: any;
    assetListTitle: string;
    assetTypeIdWithFirstWf: number;
    taskId: string;
    progress: string;
    perviewComponent: any;
    workflowScript: any;
    stepName: any;
    callBack: (that: any, result: any) => void;
    that;
    isFromAssetListPage: boolean;
    isApproval: boolean;
    backPage: string;
    buttonName: string;
  }) {
    const {
      assetDict,
      comments,
      processInstanceId,
      assetList,
      assetListDatas,
      assetListTitle,
      assetTypeIdWithFirstWf,
      taskId,
      progress,
      perviewComponent,
      workflowScript,
      stepName,
      callBack,
      that,
      isFromAssetListPage,
      isApproval,
      backPage,
      buttonName,
    } = comfrimWithDic;
    let actionName = isApproval ? '承認' : '否決';
    this.baseAction(_.isEmpty(buttonName) ? actionName : buttonName, null, null, false, false, async () => {
      var actionNameCus;
      if (actionName === '承認') {
        actionNameCus = 'admit';
      } else if (actionName === '否決') {
        actionNameCus = 'deny';
      }
      if (perviewComponent && !isFromAssetListPage) {
        // run javaScript
        console.log('888234567888998===', actionNameCus);
        let resultCus = await perviewComponent.runJavaScripInWF(
          workflowScript,
          assetListDatas,
          stepName,
          actionNameCus,
          processInstanceId,
          taskId,
        );
        if (resultCus === false) {
          return;
        }
      } else {
        console.log('8990123458888==--', actionNameCus);
        let resultCus = await this.runJavaScripInWF(
          workflowScript,
          assetListDatas,
          stepName,
          actionNameCus,
          assetDict,
          processInstanceId,
          taskId,
        );
        if (resultCus === false) {
          // alert('入力内容に誤りがあります、ご確認ください');
          this.showVllidationCheckAlert();
          return;
        }
      }
      let formData = this.getFormData(assetDict, comments); //共通
      formData.append('processInstanceId', processInstanceId);
      formData.append('taskId', taskId);

      let resAssetList = {};
      resAssetList['assetListTitle'] = assetListTitle;
      resAssetList['assetTypeId'] = assetTypeIdWithFirstWf;

      var assetIDList = [];
      assetList.forEach((value) => {
        assetIDList.push(value.assetId);
      });

      var assetListId = 0;
      if (assetListDatas.length > 0) {
        assetListId = [0]['assetListId'];
      }
      resAssetList['assetIds'] = assetIDList;
      resAssetList['assetListId'] = assetListId;
      formData.append('assetList', JSON.stringify(resAssetList));

      formData.append('mode', isApproval ? '0' : '1');
      formData.append('buttonName', _.isEmpty(buttonName) ? actionName : buttonName);
      if (callBack) {
        callBack(that, true);
      }
      try {
        const result = await this.workflowGetApplicationWorkflowListService.approvalWorkFlow(formData);
        // 点击保存按钮后删除S3文件
        deleteFileFromS3(this.http);
        console.log(result);
      } catch {
        if (callBack) {
          callBack(that, false);
        }
      }
      //重置flag和备份用的资产list
      clearLocalData();
      setTimeout(() => {
        this.setNavigateBack(actionName, progress, _.isEmpty(backPage) ? this.approvalURL : backPage);
        if (callBack) {
          callBack(that, false);
        }
      }, 500);
    });
  }

  async comfrimWithScanComplete(comfrimWithDic: {
    isAssetListEditable: boolean;
    isCountingType: boolean;
    assetDict: { [key: string]: AssetTypeItem[] };
    comments: any;
    processInstanceId: string;
    assetList: any[];
    assetListDatas: any;
    assetListTitle: string;
    assetTypeIdWithFirstWf: number;
    taskId: string;
    progress: string;
    isApproval: boolean;
    backPage: string;
    assignDynamicData: any[];
    perviewComponent: any;
    workflowScript: any;
    confirmData: any;
    stepName: any;
    callBack: (that: any, result: any) => void;
    that: any;
    isFromAssetListPage: boolean;
    buttonName: string;
  }) {
    const {
      isAssetListEditable,
      isCountingType,
      assetDict,
      comments,
      processInstanceId,
      assetList,
      assetListDatas,
      assetListTitle,
      assetTypeIdWithFirstWf,
      taskId,
      progress,
      isApproval,
      backPage,
      assignDynamicData,
      perviewComponent,
      workflowScript,
      confirmData,
      stepName,
      callBack,
      that,
      isFromAssetListPage,
      buttonName,
    } = comfrimWithDic;
    let actionName = isApproval ? '承認' : '否決';
    await this.baseAction(_.isEmpty(buttonName) ? actionName : buttonName, null, null, false, false, async () => {
      var actionNameCus;
      if (actionName === '承認') {
        actionNameCus = 'admit';
      } else if (actionName === '否決') {
        actionNameCus = 'deny';
      }
      if (perviewComponent && !isFromAssetListPage) {
        // run javaScript
        console.log('8000000888888==--', actionNameCus);
        let resultCus = await perviewComponent.runJavaScripInWF(
          workflowScript,
          confirmData.assetListDatas,
          stepName,
          actionNameCus,
          processInstanceId,
          taskId,
        );
        if (resultCus === false) {
          return;
        }
      } else {
        console.log('899034567778888==--', actionNameCus);
        let resultCus = await this.runJavaScripInWF(
          workflowScript,
          assetListDatas,
          stepName,
          actionNameCus,
          assetDict,
          processInstanceId,
          taskId,
        );
        if (resultCus === false) {
          // alert('入力内容に誤りがあります、ご確認ください');
          this.showVllidationCheckAlert();
          return;
        }
      }

      const confirm = async () => {
        let formData = this.getFormData(assetDict, comments); //共通
        formData.append('processInstanceId', processInstanceId);
        formData.append('taskId', taskId);

        formData.append('mode', isApproval ? '0' : '1');

        if (assignDynamicData !== null && assignDynamicData !== undefined && assignDynamicData.length > 0) {
          formData.append('assignDynamicData', JSON.stringify(assignDynamicData));
        }
        if (isApproval) {
          formData.append('buttonName', buttonName);
        }
        const result = await this.workflowGetApplicationWorkflowListService.approvalWorkFlow(formData);
        // 点击保存按钮后删除S3文件
        deleteFileFromS3(this.http);
        console.log(result);
        setTimeout(() => {
          this.setNavigateBack(actionName, progress, backPage);
        }, 500);
      };

      // 承認時資産が選択されない場合
      if (that.assetCount == '0' && isAssetListEditable) {
        const alert = await this.alertController.create({
          message: '資産が選択されていませんが、承認しますか？',
          buttons: [
            {
              text: 'キャンセル',
              role: 'cancel',
              cssClass: 'common-alert-text-primary',
              handler: async () => {
                if (callBack) {
                  callBack(that, false);
                }
              },
            },
            {
              text: 'OK',
              role: 'ok',
              cssClass: 'common-alert-text-primary font-weight-bold',
              handler: async () => {
                await this.http.addLoadingQueue();
                try {
                  if (callBack) {
                    callBack(that, true);
                  }
                  await confirm();
                } catch {
                  if (callBack) {
                    callBack(that, false);
                  }
                } finally {
                  if (callBack) {
                    callBack(that, false);
                  }
                  await this.http.removeLoadingQueue();
                }
              },
            },
          ],
        });
        await alert.present();
      } else {
        try {
          if (callBack) {
            callBack(that, true);
          }
          await confirm();
        } catch {
          if (callBack) {
            callBack(that, false);
          }
        } finally {
          if (callBack) {
            callBack(that, false);
          }
          await this.http.removeLoadingQueue();
        }
      }
    });
  }

  /**
   * @description:
   * @param {type}
   * @return {type}
   */
  async comfrimWithScanNewW3(
    assetDict: { [key: string]: AssetTypeItem[] },
    comments: any,
    processInstanceId: string,
    taskIds: [],
    wfName: string = '',
    subformData: { assetId; taskId; subformAssetDict } = null,
    doneAction,
  ) {
    let scanedCount = 'スキャンした' + taskIds.length + '件の資産が';
    const alert = await this.alertController.create({
      message:
        `
      WF「` +
        wfName +
        `」を承認しますか？<br/>` +
        scanedCount +
        `<br/>承認されます
    `,
      buttons: [
        {
          text: 'キャンセル',
          role: 'cancel',
          cssClass: 'common-alert-text-primary',
          handler: async () => {},
        },
        {
          text: 'OK',
          role: 'ok',
          cssClass: 'common-alert-text-primary font-weight-bold',
          handler: async () => {
            await this.http.addLoadingQueue();
            let formData = this.getFormData(JSON.parse(JSON.stringify(assetDict)), comments); //共通
            formData.append('processInstanceId', processInstanceId);
            formData.append('taskIds', JSON.stringify(taskIds));

            try {
              if (subformData) {
                await this.workflowGetApplicationWorkflowListService.updateSubformByAsset(
                  subformData.assetId,
                  subformData.taskId,
                  subformData.subformAssetDict,
                );
              }
              const result =
              await this.workflowGetApplicationWorkflowListService.approvalNewW3ScanedAssetsWorkFlow(formData);
              // 点击保存按钮后删除S3文件
              deleteFileFromS3(this.http);
              await this.dealWithNewW3APICallback(result, doneAction);
            } catch (result) {
              await this.dealWithNewW3APICallback(result, doneAction);
            } finally {
              await this.http.removeLoadingQueue();
            }
          },
        },
      ],
    });
    await alert.present();
  }

  /**
   * @description:comfrimWithScanCompleteNoSubprocess
   * @return {type}
   * @param assetDict
   * @param comments
   * @param processInstanceId
   * @param assetListId
   * @param taskId
   * @param progress
   * @param isApproval
   * @param backPage
   * @param assignDynamicData
   */
  // async comfrimWithScanCompleteNoSubprocess(
  //   assetDict: { [key: string]: AssetTypeItem[] },
  //   comments: any,
  //   processInstanceId: string,
  //   taskId: string,
  //   progress: string,
  //   isApproval: boolean = true,
  //   backPage: string = this.approvalURL,
  //   assignDynamicData: any[] | undefined,
  //   perviewComponent = null,
  //   workflowScript = null,
  //   confirmData = null,
  //   stepName = null,
  //   callBack,
  //   that,
  //   isFromAssetListPage = false
  // ) {
  //   let actionName = isApproval ? '承認' : '否決';
  //   this.baseAction(actionName, null, null, false, false, async () => {
  //     var actionNameCus;
  //     if (actionName === '承認') {
  //       actionNameCus = 'admit';
  //     } else if (actionName === '否決') {
  //       actionNameCus = 'deny';
  //     }
  //     if (perviewComponent && !isFromAssetListPage) {
  //       // run javaScript
  //       console.log('89870568888888-==', actionNameCus);
  //       let resultCus = await perviewComponent.runJavaScripInWF(workflowScript, confirmData.assetListDatas, stepName, actionNameCus);
  //       if (resultCus === false) {
  //         return;
  //       }
  //     } else {
  //       console.log('8994523545378888==--', actionNameCus);
  //       let resultCus = await this.runJavaScripInWF(workflowScript, confirmData.assetListDatas, stepName, actionNameCus, assetDict);
  //       if (resultCus === false) {
  //         // alert('入力内容に誤りがあります、ご確認ください');
  //         this.showVllidationCheckAlert();
  //         return;
  //       }
  //     }
  //     let formData = new FormData();
  //     let variables = this.getMobileVariableData(assetDict, comments);
  //     formData.append('processInstanceId', processInstanceId);
  //     formData.append('taskId', taskId);
  //     formData.append('mode', isApproval ? '0' : '1');
  //     if (assignDynamicData !== undefined && assignDynamicData !== null && assignDynamicData.length > 0) {
  //       formData.append('assignDynamicData', JSON.stringify(assignDynamicData));
  //     }
  //     Object.keys(variables).forEach(key => {
  //       if (key && key != 'null') {
  //         formData.append(key, variables[key]);
  //       }
  //     })
  //     if (callBack) {
  //       callBack(that, true);
  //     }
  //     try {
  //       var result;
  //       result = await this.workflowGetApplicationWorkflowListService.mobileScanCompleteConfirmWorkFlow(formData);
  //       console.log(result);
  //     } catch {
  //       if (callBack) {
  //         callBack(that, false);
  //       }
  //     }

  //     this.setNavigateBack(actionName, progress, backPage);
  //     setTimeout(() => {
  //       if (callBack) {
  //         callBack(that, false);
  //       }
  //     }, 500);
  //   });
  // }
  async comfrimWithScanCompleteNoSubprocessWithAllMust(dataIntegration: {
    assetDict: { [key: string]: AssetTypeItem[] };
    comments: any;
    processInstanceId: string;
    taskId: string;
    progress: string;
    isApproval: boolean;
    backPage: string;
    assignDynamicData?: any[];
    perviewComponent: PerviewComponent;
    workflowScript: any;
    confirmData: any;
    stepName: string;
    isFromAssetListPage: boolean;
    workflowexecData: WorkflowexecDataInterface;
    isAllMustScan: boolean;
    buttonName: string;
  }): Promise<boolean> {
    const {
      isApproval,
      perviewComponent,
      isFromAssetListPage,
      workflowScript,
      confirmData,
      stepName,
      assetDict,
      comments,
      processInstanceId,
      taskId,
      assignDynamicData,
      progress,
      backPage,
      workflowexecData,
      isAllMustScan,
      buttonName,
    } = dataIntegration;
    let actionName = isApproval ? '承認' : '否決';
    const isBaseAction = await this.baseAction(
      _.isEmpty(buttonName) ? actionName : buttonName,
      null,
      null,
      false,
      false,
      null,
    );
    if (!isBaseAction) {
      return false;
    }

    var actionNameCus;
    if (actionName === '承認') {
      actionNameCus = 'admit';
    } else if (actionName === '否決') {
      actionNameCus = 'deny';
    }

    const confirm = async () => {
      await this.http.addLoadingQueue();
      try {
        if (perviewComponent && !isFromAssetListPage) {
          // run javaScript
          console.log('88888345339888998-====', actionNameCus);
          let resultCus = await perviewComponent.runJavaScripInWF(
            workflowScript,
            confirmData.assetListDatas,
            stepName,
            actionNameCus,
            processInstanceId,
            taskId,
          );
          if (resultCus === false) {
            return false;
          }
        } else {
          console.log('899972345778888==--', actionNameCus);
          let resultCus = await this.runJavaScripInWF(
            workflowScript,
            confirmData.assetListDatas,
            stepName,
            actionNameCus,
            assetDict,
            processInstanceId,
            taskId,
            false,
          );
          if (resultCus === false) {
            // alert('入力内容に誤りがあります、ご確認ください');
            this.showVllidationCheckAlert();
            return false;
          }
        }
        let formData = this.getFormData(assetDict, comments);
        formData.append('processInstanceId', processInstanceId);
        formData.append('taskId', taskId);
        formData.append('mode', isApproval ? '0' : '1');
        if (assignDynamicData !== undefined && assignDynamicData !== null && assignDynamicData.length > 0) {
          formData.append('assignDynamicData', JSON.stringify(assignDynamicData));
        }

        const result = await this.workflowGetApplicationWorkflowListService.mobileScanCompleteConfirmWorkFlow(formData);
        // 点击保存按钮后删除S3文件
        deleteFileFromS3(this.http);
        console.log(result);
        //重置flag和备份用的资产list
        clearLocalData();
        setTimeout(() => {
          this.setNavigateBack(actionName, progress, backPage);
        }, 500);
        return true;
      } catch (error) {
        console.error('Error in confirm:', error);
        return false;
      } finally {
        await this.http.removeLoadingQueue();
      }
    };
    // 全部の資産をスキャンしたのか
    const alert = await this.alertController.create({
      message: 'スキャンされてない資産が存在しています。未スキャン資産はリストから削除されますがこのまま進めますか？',
      buttons: [
        {
          text: 'キャンセル',
          role: 'cancel',
          cssClass: 'common-alert-text-primary',
        },
        {
          text: 'OK',
          role: 'ok',
          cssClass: 'common-alert-text-primary font-weight-bold',
          handler: () => {
            // 执行异步操作
            (async () => {
              const confirmResult = await confirm();
              await alert.dismiss(confirmResult,'ok');
            })();
            return false; // 返回 false 防止 alert 自动关闭，让我们手动控制关闭时机
          },
        },
      ],
    });

    // 主要为了替换callback
    let isExecutionMarker: boolean;
    const isWFStatus =
      workflowexecData.numberOfUnscanned > 0 && workflowexecData.totalAssets >= workflowexecData.numberOfUnscanned;
    if (isAllMustScan && workflowexecData.numberOfUnscanned !== 0) {
      // TODO 必须全资产时候处理条件
      return false;
    }

    if (isWFStatus) {
      await alert.present();
      const { role, data } = await alert.onDidDismiss();
      isExecutionMarker = role === 'ok' && data === true;
    } else {
      const confirmResult = await confirm();
      isExecutionMarker = confirmResult;
    }
    return isExecutionMarker;
  }

  /**
   * 复数人专用
   * @description:
   * @param {type}
   * @return {type}
   */
  async comfrimWithMultiScanCompleteNoSubprocess(comfrimWithMultiDic: {
    assetDict: { [key: string]: AssetTypeItem[] };
    comments: any;
    processInstanceId: string;
    taskId: string;
    progress: string;
    isApproval: boolean;
    backPage: string;
    assignDynamicData: any[] | undefined;
    perviewComponent: any;
    workflowScript: any;
    confirmData: any;
    stepName: any;
    callBack: (that: any, result: any) => void;
    that: any;
    isFromAssetListPage: boolean;
    taskDefKey: any;
    buttonName: string;
  }) {
    const {
      assetDict,
      comments,
      processInstanceId,
      taskId,
      progress,
      isApproval,
      backPage,
      assignDynamicData,
      perviewComponent,
      workflowScript,
      confirmData,
      stepName,
      callBack,
      that,
      isFromAssetListPage,
      taskDefKey,
      buttonName,
    } = comfrimWithMultiDic;
    let actionName = isApproval ? '承認' : '否決';
    await this.baseAction(actionName, null, null, false, false, async () => {
      var actionNameCus;
      if (actionName === '承認') {
        actionNameCus = 'admit';
      } else if (actionName === '否決') {
        actionNameCus = 'deny';
      }

      const confirm = async () => {
        if (perviewComponent && !isFromAssetListPage) {
          // run javaScript
          console.log('8880000088888=-=-', actionNameCus);
          let resultCus = await perviewComponent.runJavaScripInWF(
            workflowScript,
            confirmData.assetListDatas,
            stepName,
            actionNameCus,
            processInstanceId,
            taskId,
          );
          if (resultCus === false) {
            return;
          }
        } else {
          console.log('8901234332378888==--', actionNameCus);
          let resultCus = await this.runJavaScripInWF(
            workflowScript,
            confirmData.assetListDatas,
            stepName,
            actionNameCus,
            assetDict,
            processInstanceId,
            taskId,
          );
          if (resultCus === false) {
            // alert('入力内容に誤りがあります、ご確認ください');
            this.showVllidationCheckAlert();
            return;
          }
        }

        let formData = this.getFormData(assetDict, comments);
        formData.append('processInstanceId', processInstanceId);
        formData.append('taskId', taskId);
        formData.append('mode', isApproval ? '0' : '1');
        // 动态制定担当者时候用的参数
        if (assignDynamicData !== undefined && assignDynamicData !== null && assignDynamicData.length > 0) {
          formData.append('assignDynamicData', JSON.stringify(assignDynamicData));
        }

        const result = await this.workflowGetApplicationWorkflowListService.mobileScanCompleteConfirmWorkFlow(formData);
        // 点击保存按钮后删除S3文件
        deleteFileFromS3(this.http);
        //重置flag和备份用的资产list
        clearLocalData();
        console.log(result);
        setTimeout(() => {
          this.setNavigateBack(actionName, progress, _.isEmpty(backPage) ? this.approvalURL : backPage);
        }, 500);
      };

      // 全部の資産をスキャンしたのか
      // const isWFStatus = that.workflowexecData.numberOfUnscanned > 0 && that.workflowexecData.totalAssets >= that.workflowexecData.numberOfUnscanned
      const isAllMustScan: boolean = confirmData['allMustScan'];
      const totalAssetAmountCount: number = _.toNumber(confirmData['totalAssetAmountCount']);
      const totalSavedAssetAmountCount: number = _.toNumber(confirmData['totalSavedAssetAmountCount']);
      let isWFStatus: boolean = totalAssetAmountCount !== totalSavedAssetAmountCount;
      const isScanTask = _.toString(taskDefKey).startsWith('ScanTask');
      if (!isAllMustScan && isWFStatus && isScanTask) {
        const alert = await this.alertController.create({
          message:
            'スキャンされてない資産が存在しています。未スキャン資産はリストから削除されますがこのまま進めますか？',
          buttons: [
            {
              text: 'キャンセル',
              role: 'cancel',
              cssClass: 'common-alert-text-primary',
              handler: async () => {
                if (callBack) {
                  callBack(that, false);
                }
              },
            },
            {
              text: 'OK',
              role: 'ok',
              cssClass: 'common-alert-text-primary font-weight-bold',
              handler: async () => {
                try {
                  await this.http.addLoadingQueue();
                  if (callBack) {
                    callBack(that, true);
                  }
                  await confirm();
                } catch {
                  if (callBack) {
                    callBack(that, false);
                  }
                } finally {
                  if (callBack) {
                    callBack(that, false);
                  }
                  await this.http.removeLoadingQueue();
                }
              },
            },
          ],
        });
        await alert.present();
        return;
      } else {
        try {
          await this.http.addLoadingQueue();
          if (callBack) {
            callBack(that, true);
          }
          await confirm();
        } catch (e){
          console.error(e);
        } finally {
          if (callBack) {
            callBack(that, false);
          }
          await this.http.removeLoadingQueue();
        }
      }
    });
  }

  /**
   * @description:舞浜
   * @param {type}
   * @return {type}
   */
  async rejectionWithMaihamaList(rejectionWithDic: {
    assetDict: { [key: string]: AssetTypeItem[] };
    comments: any;
    processInstanceId: string;
    amountActionAssetList: any;
    taskId: string;
    progress: string;
    callBack: (that: any, result: any) => void;
    that;
    buttonName: string;
  }) {
    const {
      assetDict,
      comments,
      processInstanceId,
      amountActionAssetList,
      taskId,
      progress,
      callBack,
      that,
      buttonName,
    } = rejectionWithDic;
    this.baseAction(_.isEmpty(buttonName) ? '否決' : buttonName, null, null, false, false, async () => {
      try {
        await this.http.addLoadingQueue();
        let formData = this.getFormData(assetDict, comments); //共通

        formData.append('processInstanceId', processInstanceId);
        formData.append('taskId', taskId);
        formData.append('mode', '1');
        let list = [];
        JSON.parse(amountActionAssetList).forEach((item) => {
          let map = new Map();
          map['assetId'] = item['assetId'];
          map['amount'] = item['amount'];
          list.push(map);
        });
        formData.append('amountActionAssetList', JSON.stringify(list));
        formData.append('buttonName', _.isEmpty(buttonName) ? '否決' : buttonName);
        if (callBack) {
          callBack(that, true);
        }
        try {
          const result = await this.workflowGetApplicationWorkflowListService.approvalWorkFlow(formData);
          // 点击保存按钮后删除S3文件
          deleteFileFromS3(this.http);
          console.log(result);
        } catch {
          if (callBack) {
            callBack(that, false);
          }
        }
        //重置flag和备份用的资产list
        clearLocalData();
        setTimeout(() => {
          this.setNavigateBack('否決', progress, this.applicationURL);
          if (callBack) {
            callBack(that, false);
          }
        }, 500);
      } finally {
        if (callBack) {
          callBack(that, false);
        }
        await this.http.removeLoadingQueue(true);
      }
    });
  }

  /**
   * @description: updateScanTaskLock
   * @param {type}
   * @return {type}
   */
  async updateScanTaskLock(
    taskId: string,
    lockType: string,
    alertMessage: string,
    progress: string,
    callBack = null,
    that = null,
    comments: any[],
    assetDict: { [key: string]: AssetTypeItem[] },
    processInstanceId: string,
  ) {
    const assetTypeList: AssetTypeItem[] = [];
    let itemDataDictCopy = JSON.parse(JSON.stringify(assetDict));
    for (const sectionName in itemDataDictCopy) {
      assetTypeList.push(...assetDict[sectionName]);
    }
    for (const assetType of assetTypeList) {
      // 必須チェック
      if (assetType.inputFlg === '1') {
        if (
          assetType.defaultData == '' ||
          assetType.defaultData == undefined ||
          assetType.defaultData == null ||
          Object.keys(assetType.defaultData).length === 0 ||
          String(assetType.defaultData).trim() === '' ||
          assetType.defaultData == '[]'
        ) {
          alert(assetType.itemName + 'を設定してください。');
          return;
        }
      }

      if (assetType.inputFlg === '1' && assetType.itemType === 'checkbox') {
        if (assetType.defaultData) {
          if (typeof assetType.defaultData === 'string') {
            if (assetType.defaultData !== '1') {
              let cbName = assetType.itemName ?? assetType.itemDisplayName;
              alert(cbName + 'を設定してください。');
              return;
            }
          }
        }
      }

      if (assetType.isShowMessageTS) {
        alert('入力エラーがあるのでご確認ください。');
        return;
      }
      if (assetType.defaultData) {
        // 桁数チェック
        if (assetType.optionObject.maxlength && typeof assetType.defaultData === 'string') {
          if (assetType.defaultData.length > assetType.optionObject.maxlength) {
            alert(`${assetType.itemName}を${assetType.optionObject.maxlength}文字以内に入力してください。`);
            return;
          }
        }

        // 数字或通货类型输入位数检查
        if (!validateNumericAndCurrencyField(assetType,false)) {
          return false;
        }

        // メールアドレスの有効性チェック
        if (assetType.itemType === 'email') {
          const reg = /^(\w+|[-+.])*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
          if (!reg.test(assetType.defaultData)) {
            alert(`${assetType.itemName}のフォーマットが間違っています。`);
            return;
          }
        }
      }
    }
    this.baseAction(alertMessage, null, null, false, false, async () => {
      try {
        await this.http.addLoadingQueue();
        let formData = this.getFormData(assetDict, comments); //共通
        formData.append('processInstanceId', processInstanceId);
        formData.append('taskId', taskId);
        formData.append('lockType', lockType);
        if (callBack) {
          callBack(that, true);
        }
        try {
          const result = await this.workflowGetApplicationWorkflowListService.updateScanTaskLock(formData);
          console.log(result);
        } catch {
          if (callBack) {
            callBack(that, false);
          }
        }
        //重置flag和备份用的资产list
        clearLocalData();
        setTimeout(() => {
          this.setNavigateBack(alertMessage, progress, this.approvalURL);
          if (callBack) {
            callBack(that, false);
          }
        }, 500);
      } finally {
        if (callBack) {
          callBack(that, false);
        }
        await this.http.removeLoadingQueue(true);
      }
    });
  }

  /**
   * @description: sendBack
   * @param {type}
   * @return {type}
   */
  async sendBack(sendBackDic: {
    assetDict: { [key: string]: AssetTypeItem[] };
    comments: any;
    sendBackDestination: string;
    taskId: string;
    progress: string;
    perviewComponent;
    workflowScript;
    stepName;
    callBack;
    that;
    isFromAssetListPage;
    backPage: string;
    buttonName: string;
  }) {
    const {
      assetDict,
      comments,
      sendBackDestination,
      taskId,
      progress,
      perviewComponent,
      workflowScript,
      stepName,
      callBack,
      that,
      isFromAssetListPage,
      backPage,
      buttonName,
    } = sendBackDic;
    this.baseAction(_.isEmpty(buttonName) ? '差戻し' : buttonName, null, null, false, false, async () => {
      try {
        await this.http.addLoadingQueue();
        // run javaScript
        if (perviewComponent && workflowScript && !isFromAssetListPage) {
          console.log('00098993333666- sendBack');
          let resultCus = await perviewComponent.runJavaScripInWF(workflowScript, [], stepName, 'sendBack');
          if (resultCus === false) {
            return;
          }
        } else {
          console.log('8990dfsfsfdfd123458888==--');
          let resultCus = await this.runJavaScripInWF(workflowScript, [], stepName, 'sendBack', assetDict);
          if (resultCus === false) {
            // alert('入力内容に誤りがあります、ご確認ください');
            this.showVllidationCheckAlert();
            return;
          }
        }
        let formData = this.getFormData(assetDict, comments); //共通
        formData.append('sendBackDestination', sendBackDestination);
        formData.append('taskId', taskId);
        formData.append('buttonName', _.isEmpty(buttonName) ? '差戻し' : buttonName);

        if (callBack) {
          callBack(that, true);
        }
        try {
          const result = await this.workflowGetApplicationWorkflowListService.sendBackWorkFlow(formData);
          // 点击保存按钮后删除S3文件
          deleteFileFromS3(this.http);
          console.log(result);
        } catch {
          if (callBack) {
            callBack(that, false);
          }
        }
        //重置flag和备份用的资产list
        clearLocalData();
        setTimeout(() => {
          this.setNavigateBack('差戻', progress, _.isEmpty(backPage) ? this.applicationURL : backPage);
          if (callBack) {
            callBack(that, false);
          }
        }, 500);
      } finally {
        if (callBack) {
          callBack(that, false);
        }
        await this.http.removeLoadingQueue(true);
      }
    });
  }

  /**
   * @description: get dynamic setting name
   * @param {type}
   * @return {type}
   */
  async getDynamicSettingName(nameKey) {
    if (!nameKey) {
      return null;
    }
    if (typeof nameKey != 'string') {
      nameKey = nameKey.toString();
    }
    let [_, dynamicKey] = nameKey.split('_');
    var name = await this.dynamicSettingNamedic[nameKey];
    if (name == null) {
      let data = await this.getCustomItem();
      name = data[dynamicKey];
    }

    return name;
  }

  async getCustomItem() {
    let dynamicItemDic = new Map();
    const result = await this.actionService.actionGetCustomItem();
    if (result.code == 0) {
      let arr = result.layoutSettingList;
      for (let i = 0; i < arr.length; i++) {
        let item = arr[i];
        dynamicItemDic[String(item['itemId'])] = String(item['itemName']);
      }
    }
    console.log('******list.dynamicItemDic**********');
    console.log(dynamicItemDic);
    return dynamicItemDic;
  }

  /**
   * @description: sendBack
   * @param {type}
   * @return {type}
   */
  async getAppurtenancesInformation(model: any, fromApporval: boolean = false) {
    let appurtenancesInformationTypeInfo = await this.createAppurtenancesInformationTypeInfo(model['assetActionData']);
    let appurtenancesInformationItemList = appurtenancesInformationTypeInfo.appurtenancesInformationItemList;
    let name = appurtenancesInformationTypeInfo.name;
    let itemList: Array<AssetTypeItem> = [];
    appurtenancesInformationItemList.forEach((item) => {
      //未対応の項目、しばらく非表示にする
      // itemlist
      if (
        item.itemType != 'map' &&
        item.itemType != 'file' &&
        item.itemType != 'digitalSign' &&
        item.itemType != 'image' &&
        item.itemName != 'ユーザー名' &&
        item.itemName != '新規登録時間' &&
        item.itemName != 'ロケーション'
      ) {
        if (fromApporval) {
          item.optionObject.readonly = '1';
        }
        itemList.push(item);
      }
    });

    // 履歴情報のレイアウト表示部品へ引渡す変数にデータを格納
    let appurtenancesInformationActionItemListDict = {};

    let result = await this.assetDetailService.getAssetTypeItems(String(model.assetActionData.assetTypeId));
    this.assetTypeItemList = result.assetItemList;
    itemList.forEach((item) => {
      if (String(item.defaultData).includes('assetItemName:')) {
        let itemId = String(item.defaultData).split(':').pop();
        this.assetTypeItemList.forEach((assetSearchColumnItem) => {
          if (String(assetSearchColumnItem.itemId) === String(itemId)) {
            item.defaultData = assetSearchColumnItem.itemName;
          }
        });
      }
    });

    appurtenancesInformationActionItemListDict[name] = itemList;
    return appurtenancesInformationActionItemListDict;
  }

  /**
   * 履歴書情報変数初期化(this.appurtenancesInformationItemList)
   */
  async createAppurtenancesInformationTypeInfo(model) {
    var rt = model.appurtenancesInformationTypeInfo;
    let arr: Array<any> = JSON.parse(rt);
    var result = [];
    if (arr != null && arr.length > 0) {
      let appuId: number = arr[0]['appurtenancesInformationTypeId'];
      let appuName: string = arr[0]['appurtenancesInformationTypeName'];
      let appurtenancesInformationTextDic = JSON.parse(arr[0]['dataList']);
      if (appuId == undefined) {
        model.appurtenancesInformationTypeId = 0;
      } else {
        model.appurtenancesInformationTypeId = appuId;
      }
      if (appuName == undefined) {
        model.appurtenancesInformationTypeName = '履歴情報';
      } else {
        model.appurtenancesInformationTypeName = appuName;
      }
      result = await this.getItemByAppurtenancesInformationType(
        model.appurtenancesInformationTypeId,
        appurtenancesInformationTextDic,
      );
    }
    return {
      appurtenancesInformationItemList: result,
      name: model.appurtenancesInformationTypeName ?? '履歴情報',
    };
  }

  /**
   * 履歴設定情報変数初期化(appurtenancesInformationItemList)
   * @param AppurtenancesInformationTypeId 履歴情報種類ID
   */
  async getItemByAppurtenancesInformationType(AppurtenancesInformationTypeId: number, appurtenancesInformationTextDic) {
    const result = await this.actionService.getItemByAppurtenancesInformationType(AppurtenancesInformationTypeId);
    var appurtenancesInformationItemList: Array<AssetTypeItem> = [];
    if (result.code == 0) {
      let appurtenancesInformationItemL: AssetTypeItem[] = result.layoutSettingList;

      for (let i = 0; i < appurtenancesInformationItemL.length; i++) {
        var item: AssetTypeItem = appurtenancesInformationItemL[i];
        item.defaultData = appurtenancesInformationTextDic[item.itemName];
        if (item.itemName == '処理時間' || item.itemName == '更新時間' || item.itemName == '新規登録時間') {
          item.defaultData = getFormatDateTime(new Date());
        }
        if (item.itemName == 'ロケーション') {
          item.defaultData = 'ロケーションが特定できません';
        }
        item.optionObject = JSON.parse(item.option);
        appurtenancesInformationItemList.push(item);
      }
    }
    console.log(appurtenancesInformationItemList);
    return appurtenancesInformationItemList;
  }

  // /**
  //  * @description: sendBack
  //  * @param {type}
  //  * @return {type}
  //  */
  // async sendBackMobile(
  //   assetDict: { [key: string]: AssetTypeItem[] },
  //   comments: any,
  //   sendBackDestination: string,
  //   taskId: string,
  //   progress: string,
  //   backPage: string = this.approvalURL
  // ) {
  //   // taskId / sendBackDestination / variables
  //   this.baseAction('差戻', null, null, false, async () => {
  //     let formData = this.getFormData(assetDict, comments); //共通
  //     formData.append('sendBackDestination', sendBackDestination);
  //     formData.append('taskId', taskId);

  //     // const result = await this.workflowGetApplicationWorkflowListService.sendBackWorkFlow(formData);
  //     const result = await this.workflowGetApplicationWorkflowListService.sendBackMobileWorkFlow(formData);
  //     console.log(result);

  //     this.setNavigateBack('差戻',progress,backPage);
  //   });
  // }
  /**
   * @description: sendBack
   * @param {type}
   * @return {type}
   */
  async sendBackFromApproval(sendBackDic: {
    assetDict: { [key: string]: AssetTypeItem[] };
    comments: any;
    sendBackDestination: string;
    taskId: string;
    progress: string;
    perviewComponent;
    workflowScript;
    stepName;
    callBack;
    that;
    isFromAssetListPage;
    buttonName: string;
  }) {
    this.sendBack({ ...sendBackDic, backPage: null });
  }

  /**
   * キャンセル
   */
  async cancel(processInstanceId: string, progress: string) {
    this.baseAction('取り消し', null, null, false, false, async () => {
      const result = await this.workflowGetApplicationWorkflowListService.workFlowCancel(processInstanceId);
      // 点击保存按钮后删除S3文件
      deleteFileFromS3(this.http);
      console.log(result);
      //重置flag和备份用的资产list
      clearLocalData();
      setTimeout(() => {
        this.setNavigateBack('取り消し', progress, this.applicationURL);
      }, 500);
    });
  }

  /**
   * 項目の開閉
   * @param index インデックス
   */
  toggleExpand(index: number, expand: boolean[]) {
    expand[index] = !expand[index];
  }

  /**
   * @description: 入力したデータのObjectを返す
   * @return:
   */
  async checkAndGetData(itemDataDict: { [key: string]: AssetTypeItem[] }, needLoading = true): Promise<boolean> {
    const assetTypeList: AssetTypeItem[] = [];
    let itemDataDictCopy = JSON.parse(JSON.stringify(itemDataDict));
    for (const sectionName in itemDataDictCopy) {
      assetTypeList.push(...itemDataDict[sectionName]);
    }
    await this.loginService.updateLatestUserRole(needLoading);
    for (const assetType of assetTypeList) {
      if (assetType.itemType !== 'commentItem') {
        //概覧権限チェック
        if (!assetType.optionObject) {
          continue;
        }
        if (assetType.optionObject.sectionPrivateGroups == undefined) {
          // 入力不可の項目はチェック対象外
          if (assetType.optionObject.readonly === '1') {
            // 数字或通货类型输入位数检查
            if (!validateNumericAndCurrencyField(assetType)) {
              return false;
            }
            continue;
          }
          // 必須チェック
          if (assetType.inputFlg === '1') {
            if (
              assetType.defaultData == '' ||
              assetType.defaultData == undefined ||
              assetType.defaultData == null ||
              Object.keys(assetType.defaultData).length === 0 ||
              String(assetType.defaultData).trim() === ''
            ) {
              alert(assetType.itemName + 'を設定してください。');
              return false;
            }
          }
          if (assetType.isShowMessageTS) {
            alert('入力エラーがあるのでご確認ください。');
            return false;
          }
          if (assetType.defaultData) {
            // 桁数チェック
            if (assetType.optionObject.maxlength && typeof assetType.defaultData === 'string') {
              if (assetType.defaultData.length > assetType.optionObject.maxlength) {
                alert(`${assetType.itemName}を${assetType.optionObject.maxlength}文字以内に入力してください。`);
                return false;
              }
            }

            // 数字或通货类型输入位数检查
            if (!validateNumericAndCurrencyField(assetType)) {
              return false;
            }

            // メールアドレスの有効性チェック
            if (assetType.itemType === 'email') {
              const reg = /^(\w+|[-+.])*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
              if (!reg.test(assetType.defaultData)) {
                alert(`${assetType.itemName}のフォーマットが間違っています。`);
                return false;
              }
            }
          }
        } else {
          let isView = await this.loginService.visiblePermissionsCheck(assetType.optionObject.sectionPrivateGroups);
          if (isView) {
            // 入力不可の項目はチェック対象外
            if (assetType.optionObject.readonly === '1') {
              // 数字或通货类型输入位数检查
              if (!validateNumericAndCurrencyField(assetType)) {
                return false;
              }
              continue;
            }
            // 必須チェック
            if (assetType.inputFlg === '1') {
              if (
                assetType.defaultData == '' ||
                assetType.defaultData == undefined ||
                assetType.defaultData == null ||
                Object.keys(assetType.defaultData).length === 0 ||
                String(assetType.defaultData).trim() === '' ||
                assetType.defaultData == '[]'
              ) {
                alert(assetType.itemName + 'を設定してください。');
                return false;
              }
            }

            if (assetType.inputFlg === '1' && assetType.itemType === 'checkbox') {
              if (assetType.defaultData) {
                if (typeof assetType.defaultData === 'string') {
                  if (assetType.defaultData !== '1') {
                    let cbName = assetType.itemName ?? assetType.itemDisplayName;
                    alert(cbName + 'を設定してください。');
                    return false;
                  }
                }
              }
            }

            if (assetType.isShowMessageTS) {
              alert('入力エラーがあるのでご確認ください。');
              return false;
            }
            if (assetType.defaultData) {
              // 桁数チェック
              if (assetType.optionObject.maxlength && typeof assetType.defaultData === 'string') {
                if (assetType.defaultData.length > assetType.optionObject.maxlength) {
                  alert(`${assetType.itemName}を${assetType.optionObject.maxlength}文字以内に入力してください。`);
                  return false;
                }
              }

              // 数字或通货类型输入位数检查
              if (!validateNumericAndCurrencyField(assetType)) {
                return false;
              }

              // メールアドレスの有効性チェック
              if (assetType.itemType === 'email') {
                const reg = /^(\w+|[-+.])*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
                if (!reg.test(assetType.defaultData)) {
                  alert(`${assetType.itemName}のフォーマットが間違っています。`);
                  return false;
                }
              }
            }
          }
        }
      }
    }
    return true;
  }

  setMethodName(methodkey: string): string {
    if (methodkey == 'include') {
      return '右の文字列を含む';
    }
    if (methodkey == 'list') {
      return '右の配列に含まれる';
    }
    if (methodkey == 'equeue') {
      return '右の入力と一致する';
    }
    if (methodkey == 'unequeue') {
      return '右の入力と一致しない';
    }
    if (methodkey == 'includeFirst') {
      return '右の文字列で始まる';
    }
    if (methodkey == 'includeLast') {
      return '右の文字列で終わる';
    }
    if (methodkey == 'dateUp') {
      return '>';
    }
    if (methodkey == 'dateDown') {
      return '<';
    }
    if (methodkey == 'dateUpEqu') {
      return '>=';
    }
    if (methodkey == 'dateDownEqu') {
      return '<=';
    }
    if (methodkey == 'dateEqu') {
      return '=';
    }
    if (methodkey == 'dateSetting') {
      return '指定日付期限';
    }
    if (methodkey == 'numberUp') {
      return '>';
    }
    if (methodkey == 'numberDown') {
      return '<';
    }
    if (methodkey == 'numberUpEqu') {
      return '>=';
    }
    if (methodkey == 'numberDownEqu') {
      return '<=';
    }
    if (methodkey == 'numberEqu') {
      return '=';
    }
    if (methodkey == 'checkboxEqu') {
      return '右の入力と一致する';
    }
    if (methodkey == 'listInclude') {
      return '右の配列に含まれる';
    }
    if (methodkey == 'unEquBlank') {
      return '≠空白';
    }
    if (methodkey == 'equBlank') {
      return '空白';
    }
    if (methodkey == 'dateUnEqu') {
      return '≠';
    }
    if (methodkey == 'numberUnEqu') {
      return '≠';
    }
    if (methodkey == 'equals') {
      return '右の入力と一致する';
    }
    if (methodkey == 'unequals') {
      return '右の入力と一致しない';
    }
  }

  setSearchData(field: ScanCondition): string {
    if (field.method === 'listInclude') {
      if (!field.searchData) {
        return 'ブランク';
      } else if (typeof field.searchData === 'string') {
        return field.searchData
          .split(',')
          .map((item) => {
            if (item == '') {
              item = 'ブランク';
            }
            return item;
          })
          .join(',');
      } else {
        return field.searchData
          .map((item) => {
            if (item == '') {
              item = 'ブランク';
            }
            return item;
          })
          .join(',');
      }
    }
    return field.searchData;
  }

  /**
   * @description: 入力項目（フォーム値、スキャン実績）を保存 (w1+assets)
   * @param {type}
   * @return {type}
   */
  async saveTemporaryFromScanTask(
    isCountingType: boolean,
    assetDict: { [key: string]: AssetTypeItem[] },
    comments: any,
    processInstanceId: string,
    assetListId: string,
    taskId: any,
    progress: string,
    backPage: string = this.approvalURL,
    assignDynamicData: Array<any>,
    isMultiScanTask: boolean, //是否为复数人，先留着以后可能需要,
    isUserTask: boolean,
  ) {
    const isBaseAction = await this.baseAction('一時保存', null, null, false, false, null);
    if (!isBaseAction) {
      return;
    }
    try {
      await this.http.addLoadingQueue();
      let formData = this.getFormData(assetDict, comments); //共通

      formData.append('processInstanceId', processInstanceId);
      formData.append('taskId', taskId);
      if (assignDynamicData !== undefined && assignDynamicData !== null && assignDynamicData.length > 0) {
        formData.append('assignDynamicData', JSON.stringify(assignDynamicData));
      }
      formData.append('assetListId', assetListId);
      if (isUserTask) {
        const result = await this.workflowGetApplicationWorkflowListService.saveTemporaryFormData(formData);
        // 点击保存按钮后删除S3文件
        deleteFileFromS3(this.http);
      } else {
        if (isCountingType) {
          formData.append('eachAssetList', JSON.stringify([]));
        } else {
          formData.append('assetIds', JSON.stringify([]));
        }
        const result = await this.workflowGetApplicationWorkflowListService.saveTemporaryFromScanTaskWorkFlow(formData);
        // 点击保存按钮后删除S3文件
        deleteFileFromS3(this.http);
      }
    } finally {
      await this.http.removeLoadingQueue(true);
    }
    setTimeout(() => {
      this.setNavigateBack('保存', progress, backPage);
    }, 500);
  }

  /**
   * @description: 入力項目（フォーム値）を保存 (w1+assets)
   * @param {type}
   * @return {type}
   */
  saveTemporaryFormData(
    assetDict: { [key: string]: AssetTypeItem[] },
    comments: any,
    processInstanceId: string,
    taskId: any,
    progress: string,
    backPage: string = this.approvalURL,
    assignDynamicData: Array<any> = undefined,
    assetList: any[] = undefined,
    assetListDatas: any = undefined,
    isCountingType: boolean = undefined,
    assetTypeId: number = undefined,
  ) {
    this.baseAction(
      '保存して終了',
      null,
      null,
      false,
      false,
      async () => {
        let formData = this.getFormData(assetDict, comments); //共通

        formData.append('processInstanceId', processInstanceId);
        formData.append('taskId', taskId);

        if (assignDynamicData !== undefined && assignDynamicData !== null && assignDynamicData.length > 0) {
          formData.append('assignDynamicData', JSON.stringify(assignDynamicData));
        }

        if (assetList) {
          let assetListId = 0;
          if (assetListDatas && assetListDatas.length > 0) {
            assetListId = assetListDatas[0]['assetListId'];
          }

          const assetIds = assetList.map((asset) => asset.assetId);
          const assetLists = {
            assetTypeId,
            assetIds,
            assetListId,
          };

          if (isCountingType) {
            let eachAssetList = assetList.map((asset) => {
              return {
                assetId: asset.assetId,
                assetAmount: asset.assetScanedCount,
                savedAssetAmount: asset.savedAssetAmount,
              };
            });
            formData.append(
              'assetList',
              JSON.stringify({
                ...assetLists,
                eachAssetList,
              }),
            );
          } else {
            formData.append('assetList', JSON.stringify(assetLists));
          }
        }

        const result = await this.workflowGetApplicationWorkflowListService.saveTemporaryFormData(formData);
        // 点击保存按钮后删除S3文件
        deleteFileFromS3(this.http);
        setTimeout(() => {
          this.setNavigateBack('保存', progress, backPage);
        }, 500);
      },
      async () => {
        setTimeout(() => {
          this.setNavigateBackWithoutText(backPage);
        }, 500);
      },
    );
  }

  /**
   * @description: 入力項目（フォーム値）を保存 (w1+assets)
   * @param {type}
   * @return {type}
   */
  async saveTemporarySubformData(
    assetDict: { [key: string]: AssetTypeItem[] },
    assetId: string,
    taskId: any,
    customizeViewComponent: any,
  ) {
    this.baseAction(
      '保存して終了',
      null,
      null,
      false,
      false,
      async () => {
        try {
          await this.http.addLoadingQueue();
          if (customizeViewComponent != null) {
            let map = customizeViewComponent.checkAndGetData();
            if (!map) {
              return;
            }
          }
          var isSuccess = false;
          var resultMsg = '保存して終了' + 'しました';
          try {
            await this.workflowGetApplicationWorkflowListService.updateSubformByAsset(assetId, taskId, assetDict);
              // 点击保存按钮后删除S3文件
            deleteFileFromS3(this.http);
            isSuccess = true;
          } catch (error) {
            resultMsg = '保存失敗しました';
          }

          this.toastr.success(resultMsg, '', {
            positionClass: 'toast-center-center',
            timeOut: 1000,
          });

          if (isSuccess) {
            this.navController.navigateBack('/workflow/approval/w1-asset-list/list');
          }
        } finally {
          await this.http.removeLoadingQueue();
        }
      },
      async () => {
        this.navController.navigateBack('/workflow/approval/w1-asset-list/list');
      },
    );
  }

  /**
   * @description: 資産項目取得
   * @param {type}
   * @return {type}
   */
  async getAssetItemCommon(needLoading = true) {
    const result = await this.actionService.actionGetAssetItemCommon(needLoading);
    return result;
  }

  /**
   * @description: 資産項目場所表示されるか取得
   * @param {type}
   * @return {type}
   */
  async showTenantAssetLocation(needLoading = true) {
    const result = await this.getAssetItemCommon(needLoading);
    var showLocation = false;
    if (result.code == 0) {
      result.assetCommonList.forEach((value) => {
        if (value['itemName'] == 'location') {
          showLocation = value['unUseFlg'] == '0';
        }
      });
    }
    return showLocation;
  }

  getOriginalForm(assetDict: { [key: string]: AssetTypeItem[] }, comments: any) {
    let cloneAssetDict = _.cloneDeep(assetDict);
    let cloneComments = _.cloneDeep(comments);
    // 旧版比对方法（比对fromdata里所有的参数）
    // const delObjAry = ['valueForShow', 'isShowMessage', 'isValid', 'loaded', 'undisplayItemNameList', 'displayList']
    // for (var key in cloneForm) {
    //   if (!_.isEmpty(cloneForm[key])) {
    //     if (_.isArray(cloneForm[key])) {
    //       const valAry = []
    //       cloneForm[key].forEach(element => {
    //         valAry.push(_.omit(element, delObjAry))
    //       });
    //       cloneForm[key] = valAry
    //     }
    //     if (_.isObject(cloneForm[key])) {
    //       cloneForm[key] = _.omit(cloneForm[key], delObjAry);
    //     }
    //   }
    // }
    // 新版比对方法（只比对结果 val）
    const formData: FormData = this.getFormData(cloneAssetDict, cloneComments, true);
    var object = {};
    formData.forEach((value, key) => {
      object[key] = value;
    });
    return JSON.stringify(object);
  }

  async dealWithNewW3APICallback(result, action) {
    if (result.code == 0) {
      // スキャンした資産が承認されだ
      if (result['subprocessIsFinished']) {
        await this.showAlert(result.msg, action, result);
      } else {
        this.toastr.success(result.msg, '', {
          positionClass: 'toast-center-center',
          timeOut: 1000,
        });
        action(result);
      }
    } else if (result.code == 103) {
      // サブプロセスが完了しました。
      await this.showAlert(result.msg, action, result);
    } else {
      await this.showAlert(result.msg);
    }
  }

  async showAlert(msg: string, action = null, result = null) {
    try {
      await this.http.addLoadingQueue();
      const alert = await this.alertController.create({
        message: msg ?? 'システムエラーが発生しました。管理者にご連絡ください。',
        buttons: [
          {
            text: '閉じる',
            role: 'cancel',
            cssClass: 'font-weight-bold',
            handler: async () => {
              if (action) {
                action(result);
              }
            },
          },
        ],
      });
      await alert.present();
    } finally {
      await this.http.removeLoadingQueue();
    }
  }

  /**
   * @description: deal with form data
   * @param {type}
   * @return {type}
   */
  initFormdata(subformLayout, subformContent, readOnly) {
    var dict: { [key: string]: AssetTypeItem[] } = {};
    subformLayout.subformDetail.forEach((item) => {
      //Optionオブジェクト設定
      item.optionObject = JSON.parse(item.option);
      if (readOnly) {
        item.optionObject['readonly'] = '1';
      }

      if (subformContent.subformInfo && subformContent.subformInfo['subformItem' + item.itemId]) {
        let value = subformContent.subformInfo['subformItem' + item.itemId];
        if (item.itemType == 'master' || item.itemType == 'digitalSign') {
          if (value != '' && value != 'null' && value != null && value != undefined) {
            item.defaultData = JSON.parse(value);
          } else {
            item.defaultData = value;
          }
        } else if (item.itemType == 'file' || item.itemType == 'image') {
          item.defaultData = JSON.parse(value);
        } else if (item.itemType == 'checkbox') {
          if (item.optionObject.checkboxMultiFlg == '1') {
            //複数
            if (value == null || value == undefined) {
              item.defaultData = [];
            } else {
              item.defaultData = JSON.parse(value);
            }
          } else {
            item.defaultData = value;
          }
        } else {
          item.defaultData = value;
        }
      }

      //データ設定
      if (Object.keys(dict).includes(item.sectionName)) {
        dict[item.sectionName].push(item);
      } else {
        var itemList: AssetTypeItem[] = [];
        itemList.push(item);
        dict[item.sectionName] = itemList;
      }
    });
    return dict;
  }

  /**
   * スキャンされた資産数を更新する
   */
  async updateScanCount(
    processInstanceId: string,
    taskId: string,
    assetListId: string,
    assetId: number,
    operation: number,
    amount: number,
  ) {
    const formData = new FormData();
    formData.append('processInstanceId', processInstanceId);
    formData.append('taskId', taskId);
    formData.append('assetListId', assetListId);
    formData.append('operation', operation.toString());
    formData.append('assetId', assetId.toString());
    if (operation === 2) {
      formData.append('amount', amount.toString());
    }
    const result = await this.workflowGetApplicationWorkflowListService.updateScanAmount(formData);
  }

  /**
   * @description:amountActionTaskUpdateItemName
   * @param {type}
   * @return {type}
   */
  async minusClick(item: any, isUpdateAsset = false, param = undefined, isFromCreateNewWF: boolean = false) {
    if (isUpdateAsset) {
      if (item.assetAmount) {
        item.assetAmount = Number(item.assetAmount);
      }
      if (item.assetAmount > 1) {
        item.assetAmount -= 1;
      }
      param['location'] = item['assetText']['location'];
      let result = await this.workflowGetApplicationWorkflowListService.mobileUpdateByBarCode(param);
      if (result['code'] > 0) {
        item.assetAmount += 1;
      } else {
        item.assetAmountBeforeScan = result.assetAmountBeforeScan;
      }
      console.log('======== result ====================================', result);
    } else if (isFromCreateNewWF) {
      if (item.assetScanedCount) {
        item.assetScanedCount = Number(item.assetScanedCount);
      }
      if (item.assetScanedCount > 1) {
        item.assetScanedCount -= 1;
      }
      this.checkAssetCount(item);
    } else {
      if (item.assetScanedCount) {
        item.assetScanedCount = Number(item.assetScanedCount);
      }
      if (item.assetScanedCount >= 1) {
        item.assetScanedCount -= 1;
      }
      this.checkAssetCount(item);
    }
  }

  /**
   * @description:
   * @param {type}
   * @return {type}
   */
  async plusClick(item: any, isUpdateAsset = false, param = undefined) {
    if (isUpdateAsset) {
      if (item.assetAmount) {
        item.assetAmount = Number(item.assetAmount);
      }
      item.assetAmount += 1;
      param['location'] = item['assetText']['location'];
      let result = await this.workflowGetApplicationWorkflowListService.mobileUpdateByBarCode(param);
      if (result['code'] > 0) {
        item.assetAmount -= 1;
      } else {
        item.assetAmountBeforeScan = result.assetAmountBeforeScan;
      }
    } else {
      if (item.assetScanedCount) {
        item.assetScanedCount = Number(item.assetScanedCount);
      }
      item.assetScanedCount += 1;
      this.checkAssetCount(item);
    }
  }

  /**
   *
   * @param item 在庫数量はマイナスにならないように
   */
  checkAssetCount(item) {
    if (item.assetScanedCount < 0) {
      item.assetScanedCount = 0;
      return false;
    }

    if (item.assetScanedCount > item.assetTotalCount) {
      item.assetScanedCount = item.assetTotalCount;
      return false;
    }
    return true;
  }

  /**
   * @description:
   * @param {type}
   * @return {type}
   */
  updateCount(item: any, $event, isUpdateAsset = false, param = undefined, needResetAmount = true) {
    const isApprovalSys = item['isApprovalSys'] ? item['isApprovalSys'] : false;
    return new Promise<boolean>(async (resolve, reject) => {
      if ($event.target.value === '') {
        const alert = await this.alertController.create({
          message: `数量を入力してください。`,
          cssClass: 'alert-title-primary',
          buttons: [
            {
              text: 'はい',
              handler: () => {
                if (needResetAmount) {
                  if (isUpdateAsset) {
                    item.assetAmount = item.oldassetAmount;
                  } else {
                    item.assetScanedCount = 1;
                  }
                }
              },
            },
          ],
          backdropDismiss: false,
        });
        await alert.present();
        resolve(false);
        return;
      } else if ($event.target.value == 0 && isUpdateAsset && !isApprovalSys) {
        param['amount'] = item.assetAmount = 1;
        param['location'] = item['assetText']['location'];
        let result = await this.workflowGetApplicationWorkflowListService.mobileUpdateByBarCode(param);
        if (result['code'] == 0) {
          item.assetAmountBeforeScan = result.assetAmountBeforeScan;
        }
        resolve(false);
        alert('1以上の数字を入力してください。');
        return;
      } else if ($event.target.value == 0 && !isUpdateAsset && !isApprovalSys) {
        item.assetScanedCount = 1;
        resolve(false);
        alert('1以上の数字を入力してください。');
        return;
      }
      const hankakudata = this.hankaku2Zenkaku($event.target.value).replace(/\b(0+)/gi, '');
      if (hankakudata != null) {
        if (this.isIntegerAll(hankakudata)) {
          resolve(this.isWhenAllInteger(item, hankakudata, isUpdateAsset, param));
        } else {
          var val = hankakudata;
          val = val.replace(/[^0-9]/gi, '');
          this.isWhenNotInteger(item, val, isUpdateAsset, param);
          resolve(false);
        }
      }
    });
  }

  //全角⇨半角
  hankaku2Zenkaku(str) {
    return str.replace(/[Ａ-Ｚａ-ｚ０-９]/g, function (s) {
      return String.fromCharCode(s.charCodeAt(0) - 0xfee0);
    });
  }

  /**
   * 正則表現で数字のみかチェック
   * @param value
   */
  isIntegerAll(value) {
    var z_reg = /^\d+$/;
    return z_reg.test(value);
  }

  /**
   *
   * @param item 数字のみの場合
   * @param amount
   */
  async isWhenAllInteger(item, amount, isUpdateAsset = false, param) {
    if (isUpdateAsset) {
      // 差し戻し後　追加或者删除资产
      param['amount'] = item.assetAmount = Number(amount);
      param['location'] = item['assetText']['location'];
      let result = await this.workflowGetApplicationWorkflowListService.mobileUpdateByBarCode(param);
      if (result['code'] > 0) {
        item.assetAmount = item.tempAssetCount;
      } else {
        item.tempAssetCount = item.assetAmount;
        item.assetAmountBeforeScan = result['assetAmountBeforeScan'];
      }
    } else {
      item.assetScanedCount = Number(amount);
    }
    return this.checkAssetCount(item);
  }

  /**
   * 数字のみではない場合
   * @param item
   */
  async isWhenNotInteger(item, val, isUpdateAsset = false, param) {
    if (isUpdateAsset) {
      item.assetAmount = Number(val);
    } else {
      item.assetScanedCount = Number(val);
    }
    const alert = await this.alertController.create({
      message: `数字のみを入力して下さい。`,
      cssClass: 'alert-title-primary',
      buttons: [
        {
          text: 'はい',
          handler: () => {},
        },
      ],
      backdropDismiss: false,
    });
    const isApprovalSys = item['isApprovalSys'] ? item['isApprovalSys'] : false;
    if (!isApprovalSys) {
      await alert.present();
    }
  }

  actionWhenFinish(actionName, url) {
    let successText = actionName + '完了しました';
    this.toastr.success(successText, '', {
      positionClass: 'toast-center-center',
      timeOut: 1000,
    });
    this.navController.navigateForward(url);
  }

  /**
   * @description: setValue
   * @param {type}
   * @return:
   */
  setValueByTS(itemName: string, itemValue: string, instance) {
    var itemDataDict = instance;
    if (instance.itemDataDict) {
      itemDataDict = instance.itemDataDict;
    }
    for (let sectionName in itemDataDict) {
      itemDataDict[sectionName].forEach((item) => {
        if (item.itemName === itemName) {
          // 数字或通货类型输入位数检查
          if (item.itemType === 'number' || item.itemType === 'currency') {
            item = validateAndProcessNumericItem(item, itemValue);
            let iv = item['defaultData'];
            // 数字
            if (item.itemType === 'number') {
              if (iv == undefined || iv == '') {
                // This is intentional
              } else {
                if (_.toString(iv).includes(',')) {
                  // This is intentional
                } else {
                  if (checkIsNumber(iv)) {
                    const option = JSON.parse(item.option);
                    const numberCommaDecimalPoint = Number(option.numberCommaDecimalPoint); // 有没有逗号分隔符0没有、1有
                    const numberDecimalPoint = Number(option.numberDecimalPoint); // 小数点的位数
                    const str: string = _.toString(iv);
                    iv = dealNumber(numberCommaDecimalPoint, numberDecimalPoint, str, true);
                    setPercentageUnit(item, iv, option);
                  }
                }
              }
            }
            // 通货
            if (item.itemType === 'currency') {
              if (iv == undefined || iv == '') {
                // This is intentional
              } else {
                if (String(iv).includes(',')) {
                  // This is intentional
                } else {
                  let option = JSON.parse(item.option);
                  let numberCommaDecimalPoint = 3;
                  let numberDecimalPoint = Number(option['currencyDecimalPoint']); //小数点的位数
                  let str: string = iv;
                  iv = dealNumber(numberCommaDecimalPoint, numberDecimalPoint, toFixed(str), true);
                }
              }
            }
          }

          item.defaultData = itemValue.toString();
          item.valueForShow = itemValue.toString();
          item.itemValue = itemValue.toString();
        }
      });
    }
  }

  checkIsNumber(value: string | number): boolean {
    return value != null && value !== '' && !isNaN(Number(value.toString()));
  }

  /**
   * @description: getValue
   * @param {type}
   * @return:
   */
  getValueByTS(itemName: string, instance) {
    var value = '';
    var itemDataDict = instance;
    if (instance.itemDataDict) {
      itemDataDict = instance.itemDataDict;
    }
    for (let sectionName in itemDataDict) {
      itemDataDict[sectionName].forEach((item) => {
        if (item.itemName === itemName) {
          value = item.defaultData;
        }
      });
    }
    return value;
  }

  /**
   * マスタ値を取得する
   */
  getMasterValueByTS(itemName, masterItemName, instance) {
    let value = '';
    var itemDataDict = instance;
    if (instance.itemDataDict) {
      itemDataDict = instance.itemDataDict;
    }
    for (let sectionName in itemDataDict) {
      itemDataDict[sectionName].forEach((item) => {
        if (item.itemName === itemName) {
          item.optionObject.masterDisplayItems.forEach((masterDisplayItem) => {
            if (masterDisplayItem && masterDisplayItem.itemName && masterDisplayItem.itemName === masterItemName) {
              if (item.defaultData.display && masterDisplayItem.itemId) {
                value = item.defaultData.display[masterDisplayItem.itemId];
              }
            }
          });
        }
      });
    }
    return value;
  }

  /**
   * @description: readonly
   * @param {type}
   * @return:
   */
  readonlyByTS(itemName, instance) {
    var itemDataDict = instance;
    if (instance.itemDataDict) {
      itemDataDict = instance.itemDataDict;
    }
    for (let sectionName in itemDataDict) {
      itemDataDict[sectionName].forEach((item) => {
        if (item.itemName === itemName) {
          item.optionObject.readonly = '1';
        }
      });
    }
  }

  /**
   * @description: canWrite
   * @param {type}
   * @return:
   */
  canWriteByTS(itemName, instance) {
    var itemDataDict = instance;
    if (instance.itemDataDict) {
      itemDataDict = instance.itemDataDict;
    }
    for (let sectionName in itemDataDict) {
      itemDataDict[sectionName].forEach((item) => {
        if (item.itemName === itemName) {
          item.optionObject.readonly = '0';
        }
      });
    }
  }

  /**
   * @description: showItemMsg
   * @param {type}
   * @return:
   */
  showItemMsgByTS(itemName, msg, instance) {
    var itemDataDict = instance;
    if (instance.itemDataDict) {
      itemDataDict = instance.itemDataDict;
    }
    for (let sectionName in itemDataDict) {
      itemDataDict[sectionName].forEach((item) => {
        if (item.itemName === itemName) {
          item.isShowMessageTS = true;
          item.showMessage = msg;
          item.itemMsgIsShow = true;
        }
      });
    }
  }

  /**
   * @description: clearItemMsg
   * @param {type}
   * @return:
   */
  clearItemMsgByTS(itemName, instance) {
    var itemDataDict = instance;
    if (instance.itemDataDict) {
      itemDataDict = instance.itemDataDict;
    }
    for (let sectionName in itemDataDict) {
      itemDataDict[sectionName].forEach((item) => {
        if (item.itemName === itemName) {
          item.isShowMessageTS = false;
          item.showMessage = '';
          item.itemMsgIsShow = false;
        }
      });
    }
  }

  /**
   * @description: showMsg
   * @param {type}
   * @return:
   */
  showMsgByTS(type, that, msg) {
    if (that && that.alertMsgIsShow !== undefined) {
      that.alertMsgIsShow = true;
    }
    // alert(msg);
  }

  /**
   * 資産情報取得
   */
  async getAssetTS(assetId, that) {
    if (assetId && assetId !== '') {
      let result = await that.assetDetailService.getFindById(assetId);
      var asset;
      if (result.arrayAsset.length > 0) {
        asset = result.arrayAsset[0];
      }
      return asset;
    }
  }

  /**
   * 資産情報アップデート
   */
  async updateAssetTS(asset, that) {
    let relatesString = await that.getRelationData(asset.assetId);
    const assetText = await getNumbersCommaRemoved(
      asset?.assetText,
      asset?.assetTypeId,
      (that as WorkflowActionsService).assetService,
      false,
    );
    let assetOld = await that.getAssetTS(asset.assetId, that);
    asset.modifiedDate = assetOld.modifiedDate;
    await that.assetDetailService.saveAssetdata(
      asset.assetTypeId,
      asset.assetId,
      assetText,
      asset.modifiedDate,
      relatesString,
      asset.barcode,
    );
  }

  async getRelationData(assetId) {
    //関連情報設定
    let result2 = await this.assetDetailService.getAssetRelationList(assetId);
    var dict: { [key: string]: AssetTypeItem[] } = {};
    result2.assetRelationList.forEach((assetRelation) => {
      //データ設定
      if (Object.keys(dict).includes(String(assetRelation.assetTypeId))) {
        dict[assetRelation.assetTypeId].push(assetRelation);
      } else {
        var itemList: AssetTypeItem[] = [];
        itemList.push(assetRelation);
        dict[String(assetRelation.assetTypeId)] = itemList;
      }
    });
    var relateListString = '';
    for (let item of Object.keys(dict)) {
      dict[item].forEach((data) => {
        relateListString = relateListString + data.assetId + ',';
      });
    }
    return relateListString;
  }

  /**
   * 資産Idを取得
   */
  getAssetIdTS(that) {
    if (that.assetId) {
      return that.assetId;
    } else {
      return null;
    }
  }

  /**
   * アクション名の取得
   * cancel 取消 sendBack　差戻し admit 承认　startNew 新規申請
   */
  getActionTS(that) {
    if (that) {
      return that.actionName;
    }
  }

  /**
   * 資産新規作成
   */
  async insertAssetTS(asset, that) {
    var result;
    const assetText = await getNumbersCommaRemoved(
      asset?.assetText,
      asset?.assetTypeId,
      (that as WorkflowActionsService).assetService,
      false,
    );
    await that.addAssetService
      .insertAssetData(asset.assetTypeId, asset.barcode, assetText)
      .then(async (data) => {
        result = 1;
      })
      .catch((err) => {
        result = 0;
      });
    return result;
  }

  /**
   * 資産リストを取得
   */
  async getWorkflowAssetListTS(that) {
    if (that.processInstanceId == '' || that.taskId == '') {
      return [];
    }
    let dataNumAndNextPage = {
      processInstanceId: _.toNumber(that.processInstanceId),
      taskId: _.toNumber(that.taskId),
      scanState: undefined,
      skip: 0,
      rows: 99999,
      keyword: undefined,
      moreThenLimit: false,
    };
    const getAssetListData =
      await that.workflowGetApplicationWorkflowListService.getAssetsByKeywordInWfAssetListForAssignScan(
        dataNumAndNextPage,
      );
    const wfAssetList = getAssetListData.assetListDatas ?? [];
    return wfAssetList;
  }

  /**
   * WF名を取得
   */
  getWorkflowTaskNameTS(that) {
    if (that.stepName) {
      return that.stepName;
    }
  }

  /**
   * 履歴情報を取得
   */
  async getAppurtenancesInformationTS(assetId, appurtenanceTypeId, count, that) {
    if (appurtenanceTypeId && count && that) {
      let startIndex: number = 0;
      // 履歴情報リストを取得する
      let resultAppurtenancesInfoData = await that.assetDetailService.getAppurtenancesInfoData(
        assetId,
        appurtenanceTypeId,
        String(startIndex),
        String(count),
      );
      let appurtenancesInformationList = resultAppurtenancesInfoData.appurtenancesInformationList;
      return appurtenancesInformationList;
    }
  }

  /**
   * 履歴情報を新規作成
   */
  async insertAppurtenancesInformationTS(appurtenance, that) {
    if (appurtenance && that) {
      const appurtenancesInformationText = await getNumbersCommaRemoved(
        appurtenance?.appurtenancesInformationText,
        appurtenance?.appurtenancesInformationTypeId,
        (that as WorkflowActionsService).assetService,
        true,
      );
      let map = {};
      const appurtenancesInformationTextObj = JSON.parse(appurtenancesInformationText);
      const allKeys = Object.keys(appurtenancesInformationTextObj);
      allKeys.forEach((key) => {
        map[key] = appurtenancesInformationTextObj[key];
      });
      const userInfo = await that.getinfoService.getUserInfo();
      const user = userInfo.data;
      const firstName = user.firstName;
      const lastName = user.lastName;

      map['ユーザー名'] = lastName + ' ' + firstName;
      map['新規登録時間'] = getFormatDateTime(new Date());
      map['ロケーション'] = await StorageUtils.get(StorageUtils.KEY_LOCATION);

      await that.assetDetailService.getAppurtenancesInfoLayoutInsert(
        String(appurtenance.assetId),
        String(appurtenance.appurtenancesInformationTypeId),
        JSON.stringify(map),
      );
    }
  }

  /**
   * 履歴情報をアップデート
   */
  async updateAppurtenancesInformationTS(appurtenance, that) {
    if (appurtenance && that) {
      const appurtenancesInformationText = await getNumbersCommaRemoved(
        appurtenance?.appurtenancesInformationText,
        appurtenance?.appurtenancesInformationTypeId,
        (that as WorkflowActionsService).assetService,
        true,
      );
      let appurtenanceOld = await that.getAppurtenancesInformationList(appurtenance.appurtenancesInformationId, that);
      appurtenance.modifiedDate = appurtenanceOld.modifiedDate;
      await that.assetDetailService.getAppurtenancesInfoLayoutUpdate(
        appurtenance.assetId,
        String(appurtenance.appurtenancesInformationTypeId),
        String(appurtenance.appurtenancesInformationId),
        appurtenancesInformationText,
        appurtenance.modifiedDate,
      );
    }
  }

  async getAppurtenancesInformationList(appurtenancesInformationId, that) {
    var data;
    let appurtenanceOld = await that.assetDetailService.getAppurtenancesInformationListById(appurtenancesInformationId);
    if (appurtenanceOld.appurtenancesInformationList.length > 0) {
      data = appurtenanceOld.appurtenancesInformationList[0];
    }
    return data;
  }

  /**
   * マスターリストを取得
   */
  async getMasterValueListTS(masterTypeId, that) {
    if (masterTypeId && that) {
      var masterList = [];
      const result = await that.actionService.getMasterInfoById(masterTypeId);
      console.log(result);
      if (result.code == 0) {
        result.masterDetail.forEach((master) => {
          master['masterText'] = JSON.parse(master['masterText']);
          masterList.push(master);
        });
      }
      return masterList;
    }
  }

  async getAssetsByTypeTS(assetTypeId, rows, that) {
    if (assetTypeId && rows && that) {
      let newAssetList: Asset[] = [];
      newAssetList = await that.assetService.getAssetListData(String(0), String(rows), assetTypeId);
      return newAssetList;
    }
  }

  /**
   * date format
   */
  dateFormatTS(date, fmt) {
    let o = {
      'M+': date.getMonth() + 1,
      'd+': date.getDate(),
      'h+': date.getHours(),
      'm+': date.getMinutes(),
      's+': date.getSeconds(),
      S: date.getMilliseconds(),
    };
    if (/(y+)/.test(fmt)) {
      fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
    }
    for (let k in o) {
      if (new RegExp('(' + k + ')').test(fmt)) {
        fmt = fmt.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length));
      }
    }
    return fmt;
  }

  /**
   * get current date time
   */
  nowTS() {
    return new Date();
  }

  async runJavascriptTogetAssetOrUpdateAssetOrInsertAsset(javaScriptText, assetList = [], stepName = '') {
    var str = this.globalVariable.CustomizedLogicJavascript + javaScriptText;
    var result: any;
    try {
      var run;
      global.pageThis = this;
      eval(str);
      result = await run(this);
    } catch (err) {
      result = 'error';
      alert('JavaScriptの実行は失敗しました。');
    }
    return result;
  }

  async runJavaScripInWF(
    javaScriptText,
    assetList,
    stepName,
    actionName,
    itemDataDict,
    processInstanceId = '',
    taskId = '',
    showErrAlert=true,
  ) {
    console.log('actionName ==== ', actionName);
    this.actionName = actionName;
    this.assetList = assetList;
    this.stepName = stepName;
    this.itemDataDict = itemDataDict;
    this.processInstanceId = processInstanceId;
    this.taskId = taskId;
    if (!javaScriptText || javaScriptText === '') {
      return;
    }
    StorageUtils.vallidationOn = false;
    this.resetItemDataDict();
    // カスタマイズロジック実行、（資産の新規作成、アップデート、取得）
    let result = await this.runJavascriptTogetAssetOrUpdateAssetOrInsertAsset(javaScriptText);
    console.log('result１２３４ ++++======+++++++', result);
    // 数字和通货有问题时的提示信息
    let showMessageInfo;
    var itemMsgIsShow;
    for (let sectionName in this.itemDataDict) {
      this.itemDataDict[sectionName].forEach((item) => {
        if (item.itemMsgIsShow) {
          itemMsgIsShow = true;// 过滤数字和通货的错误信息
          // 过滤数字和通货的错误信息
          const isCurrencyOrNumber = item.itemType == 'number' || item.itemType == 'currency';
          if (isCurrencyOrNumber && showMessageInfo == undefined) {
            showMessageInfo = item.showMessage;
          }
        }
      });
    }
    if ('error' === result || this.alertMsgIsShow === true || itemMsgIsShow === true) {
      this.alertMsgIsShow = false;
      if (showMessageInfo && showErrAlert) {
        // 延迟弹出，保证外层的 alert 先关闭避免奇怪的显示效果
        setTimeout(() => {
          alert(showMessageInfo);
        }, 300);
      }
      return false;
    }
  }

  resetItemDataDict() {
    for (let sectionName in this.itemDataDict) {
      this.itemDataDict[sectionName].forEach((item) => {
        if (item.itemMsgIsShow) {
          item.itemMsgIsShow = false;
          item.isShowMessageTS = false;
        }
      });
    }
  }

  backToViewPage() {
    this.navController.navigateBack('/workflow/approval/w1-asset-list/view', {
      queryParams: {},
    });
  }

  async showVllidationCheckAlert() {
    const alert = await this.alertController.create({
      message: '入力内容に誤りがあります、ご確認ください',
      buttons: [
        {
          text: 'OK',
          cssClass: 'text-danger font-weight-normal',
          handler: async () => {
            this.navController.navigateBack('/workflow/approval/w1-asset-list/view', {
              queryParams: {},
            });
          },
        },
      ],
    });
    await alert.present();
  }

  roleObj;

  async getRoleObj(needLoading = true, delay = true): Promise<any> {
    try {
      this.roleObj = await this.assetDetailService.getRoleList(needLoading, delay);
      return this.roleObj;
    } catch (error) {
      return undefined;
    }
  }

  /**
   * 用来使用传入的groupId从group对象中筛选出对应的groupName
   * 注：使用此方法前，确保调用完成一次getRoleObj方法
   * @param groupIdsStr groupIds字符串 json格式
   */
  getGroupSelectInfo(groupIdsStr: string) {
    if (!this.roleObj) {
      return [];
    }
    let roleList = this.roleObj.roleList;

    let arr = [];
    let choseRoleIdObj = JSON.parse(groupIdsStr);
    if (choseRoleIdObj) {
      choseRoleIdObj.forEach((roleId) => {
        // 如果role.roleId 于 choseRoleName中的item的groupId相同，则选中
        let find = roleList.find((item) => {
          return roleId === item.roleId;
        });
        if (find) {
          arr.push({
            groupId: find.roleId,
            groupName: find.roleName,
          });
        }
      });
    }
    return arr;
  }

  /**
   * 用来获取master里的复数个checkbox的值
   * @param params {
              assetText,
              assetType,
              masterDisplayItem,
              assetColumn
            }
   */
  getMasterCheckBoxValue(paramas) {
    if (
      paramas.masterDisplayItem.itemType == 'checkbox' &&
      this.checkIsJSON(paramas.assetText[paramas.assetType.itemName]?.display?.[paramas.assetColumn.subItemId] ?? '')
    ) {
      return JSON.parse(
        paramas.assetText[paramas.assetType.itemName]?.display?.[paramas.assetColumn.subItemId] ?? '',
      ).toString();
    } else {
      return paramas.assetText[paramas.assetType.itemName]?.display?.[paramas.assetColumn.subItemId] ?? '';
    }
  }

  checkIsJSON(str) {
    if (typeof str == 'string') {
      try {
        var obj = JSON.parse(str);
        if (typeof obj == 'object' && obj) {
          return true;
        } else {
          return false;
        }
      } catch (e) {
        return false;
      }
    }
    return false;
  }
}
