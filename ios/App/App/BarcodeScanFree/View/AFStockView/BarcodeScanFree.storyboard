<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Center Btn View Controller-->
        <scene sceneID="VDa-if-mvq">
            <objects>
                <viewController storyboardIdentifier="CenterBtnViewController" id="gsB-sQ-lOL" customClass="CenterBtnViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="HCB-Hi-cfI">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="demo" translatesAutoresizingMaskIntoConstraints="NO" id="yDT-OG-GUw">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bg" translatesAutoresizingMaskIntoConstraints="NO" id="NTL-K7-wb1">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                            </imageView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Fx7-Fy-mpc">
                                <rect key="frame" x="0.0" y="517" width="375" height="150"/>
                                <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="150" id="U0J-8Q-F2a"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="yF9-cM-V6o">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="517"/>
                                <subviews>
                                    <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="dmH-0a-0Ul">
                                        <rect key="frame" x="16" y="8" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="40" id="Pw3-By-guf"/>
                                            <constraint firstAttribute="width" constant="40" id="vAk-VE-Pch"/>
                                        </constraints>
                                        <state key="normal" image="scan_close"/>
                                        <connections>
                                            <action selector="closeBtnAction:" destination="gsB-sQ-lOL" eventType="touchUpInside" id="QzA-D1-xVW"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="dmH-0a-0Ul" firstAttribute="leading" secondItem="yF9-cM-V6o" secondAttribute="leading" constant="16" id="eBg-WT-m9T"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="kbd-ad-OVR"/>
                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="NTL-K7-wb1" secondAttribute="bottom" id="4Nz-jt-lnv"/>
                            <constraint firstItem="dmH-0a-0Ul" firstAttribute="top" secondItem="kbd-ad-OVR" secondAttribute="top" constant="8" id="BcP-hB-XlA"/>
                            <constraint firstItem="yDT-OG-GUw" firstAttribute="trailing" secondItem="HCB-Hi-cfI" secondAttribute="trailing" id="CTR-gR-yGh"/>
                            <constraint firstAttribute="bottom" secondItem="Fx7-Fy-mpc" secondAttribute="bottom" id="EVa-FW-gWq"/>
                            <constraint firstItem="yDT-OG-GUw" firstAttribute="top" secondItem="HCB-Hi-cfI" secondAttribute="top" id="FRN-JS-EFW"/>
                            <constraint firstItem="kbd-ad-OVR" firstAttribute="trailing" secondItem="Fx7-Fy-mpc" secondAttribute="trailing" id="JbI-99-dvK"/>
                            <constraint firstItem="NTL-K7-wb1" firstAttribute="top" secondItem="HCB-Hi-cfI" secondAttribute="top" id="L1B-M1-myM"/>
                            <constraint firstItem="kbd-ad-OVR" firstAttribute="trailing" secondItem="yF9-cM-V6o" secondAttribute="trailing" id="LVP-Bq-oLd"/>
                            <constraint firstItem="yF9-cM-V6o" firstAttribute="top" secondItem="HCB-Hi-cfI" secondAttribute="top" id="OVf-XJ-iWX"/>
                            <constraint firstItem="yF9-cM-V6o" firstAttribute="leading" secondItem="kbd-ad-OVR" secondAttribute="leading" id="QLF-v8-akr"/>
                            <constraint firstItem="NTL-K7-wb1" firstAttribute="leading" secondItem="HCB-Hi-cfI" secondAttribute="leading" id="TGQ-Ye-xbv"/>
                            <constraint firstItem="yDT-OG-GUw" firstAttribute="bottom" secondItem="HCB-Hi-cfI" secondAttribute="bottom" id="THH-GX-6kB"/>
                            <constraint firstItem="yDT-OG-GUw" firstAttribute="leading" secondItem="HCB-Hi-cfI" secondAttribute="leading" id="ruO-Rn-ILc"/>
                            <constraint firstItem="Fx7-Fy-mpc" firstAttribute="top" secondItem="yF9-cM-V6o" secondAttribute="bottom" id="uhC-fu-H12"/>
                            <constraint firstItem="Fx7-Fy-mpc" firstAttribute="leading" secondItem="kbd-ad-OVR" secondAttribute="leading" id="vWg-4G-vjC"/>
                            <constraint firstAttribute="trailing" secondItem="NTL-K7-wb1" secondAttribute="trailing" id="vbq-P6-5Zo"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="backView" destination="yF9-cM-V6o" id="Ecy-wI-oF2"/>
                        <outlet property="bottomView" destination="Fx7-Fy-mpc" id="e5s-jy-Coe"/>
                        <outlet property="bottomViewHeight" destination="U0J-8Q-F2a" id="v9X-OK-m2c"/>
                        <outlet property="closeBtn" destination="dmH-0a-0Ul" id="pqm-aM-Uk6"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="4TO-Gw-eCQ" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-455.19999999999999" y="-340.47976011994007"/>
        </scene>
        <!--Action Scan View Controller-->
        <scene sceneID="8rV-rt-P5i">
            <objects>
                <viewController storyboardIdentifier="ActionScanViewController" id="WcZ-m9-h7q" customClass="ActionScanViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="9Qj-XZ-16d">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="QCW-dz-pvX">
                                <rect key="frame" x="311" y="475" width="48" height="176"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="m2l-1y-Gdv">
                                        <rect key="frame" x="0.0" y="0.0" width="48" height="48"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="48" id="8Dk-9G-Ok6"/>
                                            <constraint firstAttribute="height" constant="48" id="KJB-TZ-gdq"/>
                                        </constraints>
                                        <state key="normal" image="scan_location"/>
                                        <connections>
                                            <action selector="locationSetting:" destination="u7X-I3-qMw" eventType="touchUpInside" id="5Fx-qp-exS"/>
                                            <action selector="locationSetting:" destination="WcZ-m9-h7q" eventType="touchUpInside" id="bu7-Gs-4Wf"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="QLj-cU-zLc">
                                        <rect key="frame" x="0.0" y="64" width="48" height="48"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="48" id="T0g-Yc-n41"/>
                                            <constraint firstAttribute="width" constant="48" id="bAz-Mq-Ujk"/>
                                        </constraints>
                                        <state key="normal" image="sacan_input"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                <real key="value" value="24"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="editByManualBtnAction:" destination="WcZ-m9-h7q" eventType="touchUpInside" id="BhM-UX-vyp"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" clipsSubviews="YES" contentMode="scaleAspectFit" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Knx-9i-dBx">
                                        <rect key="frame" x="0.0" y="128" width="48" height="48"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="48" id="dVE-xC-fY8"/>
                                            <constraint firstAttribute="width" constant="48" id="tsw-mp-iHE"/>
                                        </constraints>
                                        <color key="tintColor" systemColor="systemBlueColor"/>
                                        <state key="normal" image="light-bulb-off">
                                            <color key="titleShadowColor" systemColor="systemBlueColor"/>
                                        </state>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                <integer key="value" value="24"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="setLightOnOrOff:" destination="WcZ-m9-h7q" eventType="touchUpInside" id="U4Y-xF-YmP"/>
                                        </connections>
                                    </button>
                                </subviews>
                            </stackView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="aOe-yf-UHw">
                                <rect key="frame" x="16" y="8" width="40" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="5HW-hH-VvD"/>
                                    <constraint firstAttribute="width" constant="40" id="bTS-eF-C6R"/>
                                </constraints>
                                <state key="normal" image="scan_close"/>
                                <connections>
                                    <action selector="closeBtnAction:" destination="WcZ-m9-h7q" eventType="touchUpInside" id="wrH-ZQ-bNp"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Xjc-rF-ahj"/>
                        <color key="backgroundColor" white="0.66666666666666663" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="aOe-yf-UHw" firstAttribute="leading" secondItem="Xjc-rF-ahj" secondAttribute="leading" constant="16" id="6Wr-Qh-AxZ"/>
                            <constraint firstItem="aOe-yf-UHw" firstAttribute="top" secondItem="Xjc-rF-ahj" secondAttribute="top" constant="8" id="oUk-yl-33c"/>
                            <constraint firstItem="Xjc-rF-ahj" firstAttribute="bottom" secondItem="QCW-dz-pvX" secondAttribute="bottom" constant="16" id="pIf-2v-psQ"/>
                            <constraint firstItem="Xjc-rF-ahj" firstAttribute="trailing" secondItem="QCW-dz-pvX" secondAttribute="trailing" constant="16" id="zcM-U5-3fD"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="clostBtn" destination="aOe-yf-UHw" id="YaU-hS-xoU"/>
                        <outlet property="editBtn" destination="QLj-cU-zLc" id="cVK-LY-Z5K"/>
                        <outlet property="lightBtn" destination="Knx-9i-dBx" id="jNg-3G-tE1"/>
                        <outlet property="locationBtn" destination="m2l-1y-Gdv" id="RRe-xH-gRI"/>
                        <outlet property="topOfCloseBtn" destination="oUk-yl-33c" id="PiH-EX-zwJ"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="QCi-oe-Gia" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1209" y="354"/>
        </scene>
        <!--Barcode Scan Free View Controller-->
        <scene sceneID="jTm-bG-ZuU">
            <objects>
                <viewController storyboardIdentifier="BarcodeScanFreeViewController" id="dyb-zW-wVw" customClass="BarcodeScanFreeViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="NC2-Jj-OQg">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="sna-vk-E4m">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="MGO-xJ-A9E" customClass="PreviewView">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                    </view>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="2WO-E8-IH8" userLabel="close">
                                        <rect key="frame" x="16" y="8" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="40" id="4Kw-Vq-UBo"/>
                                            <constraint firstAttribute="width" constant="40" id="DHw-eX-3Vs"/>
                                        </constraints>
                                        <state key="normal" image="scan_close"/>
                                        <connections>
                                            <action selector="closeWindow:" destination="dyb-zW-wVw" eventType="touchUpInside" id="Gxg-Wi-7nW"/>
                                        </connections>
                                    </button>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="bottom" spacing="70" translatesAutoresizingMaskIntoConstraints="NO" id="Do0-4h-HGu">
                                        <rect key="frame" x="311" y="539" width="48" height="112"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="aC7-q8-rRK">
                                                <rect key="frame" x="0.0" y="0.0" width="48" height="112"/>
                                                <subviews>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Bvg-kf-EF9">
                                                        <rect key="frame" x="0.0" y="0.0" width="48" height="48"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="48" id="geU-M3-Nym"/>
                                                            <constraint firstAttribute="width" constant="48" id="yKn-ur-p08"/>
                                                        </constraints>
                                                        <state key="normal" image="scan_location"/>
                                                        <connections>
                                                            <action selector="locationSetting:" destination="dyb-zW-wVw" eventType="touchUpInside" id="6c9-9B-eWK"/>
                                                        </connections>
                                                    </button>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="kVN-ef-WXZ">
                                                        <rect key="frame" x="0.0" y="64" width="48" height="48"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="48" id="b14-T3-FMT"/>
                                                            <constraint firstAttribute="height" constant="48" id="fR0-fR-zsw"/>
                                                        </constraints>
                                                        <state key="normal" title="Button" image="sacan_input"/>
                                                        <connections>
                                                            <action selector="barcodeEditByManual:" destination="dyb-zW-wVw" eventType="touchUpInside" id="wrZ-Hj-hd8"/>
                                                        </connections>
                                                    </button>
                                                    <button hidden="YES" opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="0id-q9-Flo" userLabel="light">
                                                        <rect key="frame" x="0.0" y="112" width="48" height="48"/>
                                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="48" id="UjF-Jr-Yh9"/>
                                                            <constraint firstAttribute="width" constant="48" id="xpo-AZ-Gjl"/>
                                                        </constraints>
                                                        <state key="normal" image="light-bulb-off"/>
                                                        <state key="selected" image="light-bulb-on"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                <real key="value" value="24"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                        <connections>
                                                            <action selector="lightOnOrOff:" destination="dyb-zW-wVw" eventType="touchUpInside" id="v2j-fo-Bx9"/>
                                                        </connections>
                                                    </button>
                                                </subviews>
                                            </stackView>
                                            <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="HhS-Oc-jmb">
                                                <rect key="frame" x="0.0" y="0.0" width="48" height="40"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="40" id="RJX-U4-5fZ"/>
                                                    <constraint firstAttribute="width" constant="48" id="a9L-4Q-CpV"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                                                <state key="normal" title="スキャンリストへ">
                                                    <color key="titleColor" red="0.18119523700000001" green="0.27625621109999998" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                </state>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                        <integer key="value" value="5"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="boolean" keyPath="layer.clipsToBounds" value="YES"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="showDetailAction:" destination="dyb-zW-wVw" eventType="touchUpInside" id="dSA-GB-QW8"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <constraints>
                                            <constraint firstItem="HhS-Oc-jmb" firstAttribute="leading" secondItem="Do0-4h-HGu" secondAttribute="leading" id="72I-zJ-8Wi"/>
                                            <constraint firstAttribute="trailing" secondItem="HhS-Oc-jmb" secondAttribute="trailing" id="P7y-W7-Tl6"/>
                                        </constraints>
                                    </stackView>
                                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="スキャン済:0件" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="hUJ-8j-ENq">
                                        <rect key="frame" x="217.5" y="16" width="141.5" height="24"/>
                                        <fontDescription key="fontDescription" type="boldSystem" pointSize="20"/>
                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" white="0.66666666666666663" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="hUJ-8j-ENq" secondAttribute="trailing" constant="16" id="0BN-iE-TfP"/>
                                    <constraint firstItem="MGO-xJ-A9E" firstAttribute="top" secondItem="sna-vk-E4m" secondAttribute="top" id="8KQ-MJ-sIs"/>
                                    <constraint firstItem="MGO-xJ-A9E" firstAttribute="leading" secondItem="sna-vk-E4m" secondAttribute="leading" id="9e3-bh-pdy"/>
                                    <constraint firstItem="2WO-E8-IH8" firstAttribute="leading" secondItem="sna-vk-E4m" secondAttribute="leading" constant="16" id="FPF-qJ-hr5"/>
                                    <constraint firstAttribute="trailing" secondItem="Do0-4h-HGu" secondAttribute="trailing" constant="16" id="Tbf-Ml-P3N"/>
                                    <constraint firstAttribute="bottom" secondItem="Do0-4h-HGu" secondAttribute="bottom" constant="16" id="Ump-ty-d7v"/>
                                    <constraint firstAttribute="bottom" secondItem="MGO-xJ-A9E" secondAttribute="bottom" id="iUa-YP-fuH"/>
                                    <constraint firstAttribute="trailing" secondItem="MGO-xJ-A9E" secondAttribute="trailing" id="pVw-5p-x4I"/>
                                    <constraint firstItem="hUJ-8j-ENq" firstAttribute="centerY" secondItem="2WO-E8-IH8" secondAttribute="centerY" id="ukE-jt-Izj"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="LKe-8n-Ybd"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="sna-vk-E4m" secondAttribute="bottom" id="Bls-v3-zwP"/>
                            <constraint firstItem="2WO-E8-IH8" firstAttribute="top" secondItem="LKe-8n-Ybd" secondAttribute="top" constant="8" id="dVg-dc-Tbc"/>
                            <constraint firstItem="LKe-8n-Ybd" firstAttribute="trailing" secondItem="sna-vk-E4m" secondAttribute="trailing" id="eef-Ay-XUV"/>
                            <constraint firstItem="sna-vk-E4m" firstAttribute="leading" secondItem="LKe-8n-Ybd" secondAttribute="leading" id="paJ-DL-J7b"/>
                            <constraint firstItem="sna-vk-E4m" firstAttribute="top" secondItem="NC2-Jj-OQg" secondAttribute="top" id="wb3-Sd-C39"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="assetCountL" destination="hUJ-8j-ENq" id="Bk3-eU-cgr"/>
                        <outlet property="ligntBtn" destination="0id-q9-Flo" id="CyZ-zS-vU0"/>
                        <outlet property="locationBtn" destination="Bvg-kf-EF9" id="gyb-DZ-O8w"/>
                        <outlet property="previewAsset" destination="MGO-xJ-A9E" id="cyt-wP-7Ah"/>
                        <outlet property="previewView" destination="sna-vk-E4m" id="ZfS-1k-cok"/>
                        <outlet property="topOfCloseBtn" destination="dVg-dc-Tbc" id="RGx-Ci-r6H"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Wpz-oQ-nKR" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="295" y="-340"/>
        </scene>
        <!--Barcode Scan Free View Controller-->
        <scene sceneID="Y04-Zm-xeD">
            <objects>
                <viewController storyboardIdentifier="RFIDBarcodeScanFreeViewController" id="FL0-wK-cuY" customClass="RFIDBarcodeScanFreeViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="aac-Bv-XaU">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="LRY-wj-geX">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="XUi-Sw-KOa" customClass="PreviewView">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                    </view>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="3aX-9b-Zpe" userLabel="close">
                                        <rect key="frame" x="16" y="8" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="40" id="dCL-Ot-m3E"/>
                                            <constraint firstAttribute="height" constant="40" id="w4r-V9-FGd"/>
                                        </constraints>
                                        <state key="normal" image="scan_close"/>
                                        <connections>
                                            <action selector="closeWindow:" destination="FL0-wK-cuY" eventType="touchUpInside" id="vZM-t7-8aA"/>
                                        </connections>
                                    </button>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="bottom" spacing="70" translatesAutoresizingMaskIntoConstraints="NO" id="9Re-VY-frn">
                                        <rect key="frame" x="311" y="539" width="48" height="112"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="Sds-oI-uBZ">
                                                <rect key="frame" x="0.0" y="0.0" width="48" height="112"/>
                                                <subviews>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="u1c-3q-nnE">
                                                        <rect key="frame" x="0.0" y="0.0" width="48" height="48"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="48" id="8Ho-0y-WXV"/>
                                                            <constraint firstAttribute="height" constant="48" id="oe8-uK-tKy"/>
                                                        </constraints>
                                                        <state key="normal" image="scan_location"/>
                                                        <connections>
                                                            <action selector="locationSetting:" destination="FL0-wK-cuY" eventType="touchUpInside" id="u8i-Jh-2bz"/>
                                                        </connections>
                                                    </button>
                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Nxl-MC-YMP">
                                                        <rect key="frame" x="0.0" y="64" width="48" height="48"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="48" id="2fd-9H-7hA"/>
                                                            <constraint firstAttribute="height" constant="48" id="Ll6-id-bGO"/>
                                                        </constraints>
                                                        <state key="normal" title="Button" image="sacan_input"/>
                                                        <connections>
                                                            <action selector="barcodeEditByManual:" destination="FL0-wK-cuY" eventType="touchUpInside" id="GJW-aE-xCj"/>
                                                        </connections>
                                                    </button>
                                                    <button hidden="YES" opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="8FS-67-N1h" userLabel="light">
                                                        <rect key="frame" x="0.0" y="112" width="48" height="48"/>
                                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="48" id="9sY-Pf-JRL"/>
                                                            <constraint firstAttribute="width" constant="48" id="VWX-hJ-uAb"/>
                                                        </constraints>
                                                        <state key="normal" image="light-bulb-off"/>
                                                        <state key="selected" image="light-bulb-on"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                <real key="value" value="24"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                        <connections>
                                                            <action selector="lightOnOrOff:" destination="FL0-wK-cuY" eventType="touchUpInside" id="voS-1H-zew"/>
                                                        </connections>
                                                    </button>
                                                </subviews>
                                            </stackView>
                                            <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="XBQ-pT-4WX">
                                                <rect key="frame" x="0.0" y="0.0" width="48" height="40"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="48" id="KEV-zq-Hz1"/>
                                                    <constraint firstAttribute="height" constant="40" id="lHW-Ff-vxU"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                                                <state key="normal" title="スキャンリストへ">
                                                    <color key="titleColor" red="0.18119523700000001" green="0.27625621109999998" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                </state>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                        <integer key="value" value="5"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="boolean" keyPath="layer.clipsToBounds" value="YES"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="showDetailAction:" destination="FL0-wK-cuY" eventType="touchUpInside" id="28h-8k-eet"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="XBQ-pT-4WX" secondAttribute="trailing" id="8OF-Y3-kN9"/>
                                            <constraint firstItem="XBQ-pT-4WX" firstAttribute="leading" secondItem="9Re-VY-frn" secondAttribute="leading" id="xYc-qN-z2v"/>
                                        </constraints>
                                    </stackView>
                                    <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="スキャン済:0件" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="HSL-Z3-QoB">
                                        <rect key="frame" x="217.5" y="16" width="141.5" height="24"/>
                                        <fontDescription key="fontDescription" type="boldSystem" pointSize="20"/>
                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" white="0.66666666669999997" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="XUi-Sw-KOa" firstAttribute="top" secondItem="LRY-wj-geX" secondAttribute="top" id="0mc-Qu-VUV"/>
                                    <constraint firstItem="XUi-Sw-KOa" firstAttribute="top" secondItem="LRY-wj-geX" secondAttribute="top" id="8Wy-Ll-G9M"/>
                                    <constraint firstItem="3aX-9b-Zpe" firstAttribute="leading" secondItem="LRY-wj-geX" secondAttribute="leading" constant="16" id="Bt2-Gu-yGI"/>
                                    <constraint firstAttribute="trailing" secondItem="HSL-Z3-QoB" secondAttribute="trailing" constant="16" id="MWE-dQ-CYv"/>
                                    <constraint firstAttribute="trailing" secondItem="XUi-Sw-KOa" secondAttribute="trailing" id="OPo-Br-o0f"/>
                                    <constraint firstAttribute="bottom" secondItem="9Re-VY-frn" secondAttribute="bottom" constant="16" id="X8c-aK-Iem"/>
                                    <constraint firstItem="HSL-Z3-QoB" firstAttribute="centerY" secondItem="3aX-9b-Zpe" secondAttribute="centerY" id="avL-Zl-LKg"/>
                                    <constraint firstItem="XUi-Sw-KOa" firstAttribute="leading" secondItem="LRY-wj-geX" secondAttribute="leading" id="gw6-mL-ufM"/>
                                    <constraint firstAttribute="trailing" secondItem="9Re-VY-frn" secondAttribute="trailing" constant="16" id="h6A-Kr-NLa"/>
                                    <constraint firstAttribute="bottom" secondItem="XUi-Sw-KOa" secondAttribute="bottom" id="mdV-AY-4Bl"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="BYK-Fr-rB6"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="LRY-wj-geX" firstAttribute="top" secondItem="aac-Bv-XaU" secondAttribute="top" id="4WL-kd-6fQ"/>
                            <constraint firstItem="BYK-Fr-rB6" firstAttribute="trailing" secondItem="LRY-wj-geX" secondAttribute="trailing" id="4bP-o5-iIk"/>
                            <constraint firstAttribute="bottom" secondItem="LRY-wj-geX" secondAttribute="bottom" id="cUr-Ah-9gK"/>
                            <constraint firstItem="3aX-9b-Zpe" firstAttribute="top" secondItem="BYK-Fr-rB6" secondAttribute="top" constant="8" id="h9O-wb-d7I"/>
                            <constraint firstItem="LRY-wj-geX" firstAttribute="leading" secondItem="BYK-Fr-rB6" secondAttribute="leading" id="iWZ-rk-6ir"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="assetCountL" destination="HSL-Z3-QoB" id="jzB-H3-hLj"/>
                        <outlet property="ligntBtn" destination="8FS-67-N1h" id="N99-Na-op1"/>
                        <outlet property="locationBtn" destination="u1c-3q-nnE" id="f1E-tk-XCU"/>
                        <outlet property="previewAsset" destination="XUi-Sw-KOa" id="7LT-2C-eDu"/>
                        <outlet property="previewView" destination="LRY-wj-geX" id="g8N-3Y-Owh"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="nd2-B9-YpG" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="294" y="-1037"/>
        </scene>
        <!--Select Image View Controller-->
        <scene sceneID="l4k-6V-Vfs">
            <objects>
                <viewController storyboardIdentifier="SelectImageViewController" id="2Qi-Sz-HNQ" customClass="SelectImageViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="5xN-Xt-d8F">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <viewLayoutGuide key="safeArea" id="wae-x0-CgU"/>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="x2s-NI-mHs" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="345" y="-1740"/>
        </scene>
        <!--Select File View Controller-->
        <scene sceneID="TFL-gB-H7Z">
            <objects>
                <viewController storyboardIdentifier="SelectFileViewController" id="ZM4-DH-C4t" customClass="SelectFileViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="agI-k5-jkv">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <label hidden="YES" opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Please wait..." textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2sx-ca-mLi">
                                <rect key="frame" x="132" y="322" width="111" height="23"/>
                                <fontDescription key="fontDescription" type="system" pointSize="19"/>
                                <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="jRi-ol-GFg"/>
                        <color key="backgroundColor" systemColor="opaqueSeparatorColor"/>
                        <constraints>
                            <constraint firstItem="2sx-ca-mLi" firstAttribute="centerY" secondItem="agI-k5-jkv" secondAttribute="centerY" id="Nk0-3p-pLb"/>
                            <constraint firstItem="2sx-ca-mLi" firstAttribute="centerX" secondItem="agI-k5-jkv" secondAttribute="centerX" id="pAn-Zq-P24"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="textLabel" destination="2sx-ca-mLi" id="QQQ-cv-4xw"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="gOb-rk-BBg" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1164" y="-1740"/>
        </scene>
        <!--GLKit View Controller-->
        <scene sceneID="TFz-nX-VCW">
            <objects>
                <glkViewController storyboardIdentifier="glkViewController" preferredFramesPerSecond="30" id="pt9-wM-5jk" customClass="DigitalSignViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <glkView key="view" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" enableSetNeedsDisplay="NO" id="j0G-SP-3PD" customClass="PPSSignatureView">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <viewLayoutGuide key="safeArea" id="t59-Mg-ZDB"/>
                        <connections>
                            <outlet property="delegate" destination="pt9-wM-5jk" id="y3t-Nn-QkS"/>
                        </connections>
                    </glkView>
                    <navigationItem key="navigationItem" id="qwF-fx-bjG">
                        <barButtonItem key="leftBarButtonItem" title="戻る" image="返回" id="Ob9-Q5-9rF">
                            <connections>
                                <action selector="goBack:" destination="pt9-wM-5jk" id="5UO-bG-VuW"/>
                            </connections>
                        </barButtonItem>
                        <rightBarButtonItems>
                            <barButtonItem title="消す" id="vRZ-Cf-IP5">
                                <connections>
                                    <action selector="eraseAction:" destination="pt9-wM-5jk" id="8SJ-fD-6bw"/>
                                </connections>
                            </barButtonItem>
                            <barButtonItem title="完了" id="BlN-lN-5Cq">
                                <connections>
                                    <action selector="takePictureAction:" destination="pt9-wM-5jk" id="2Yq-vk-Zzo"/>
                                </connections>
                            </barButtonItem>
                        </rightBarButtonItems>
                    </navigationItem>
                    <connections>
                        <outlet property="signView" destination="j0G-SP-3PD" id="z6J-jm-bxk"/>
                    </connections>
                </glkViewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="c7j-hy-rwa" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="3657" y="572"/>
        </scene>
        <!--Navigation Controller-->
        <scene sceneID="DWW-MI-bOb">
            <objects>
                <navigationController automaticallyAdjustsScrollViewInsets="NO" id="fk7-EH-3HI" sceneMemberID="viewController">
                    <toolbarItems/>
                    <navigationBar key="navigationBar" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="d9U-mh-fVn">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <color key="backgroundColor" red="0.043137254899999998" green="0.24313725489999999" blue="0.52549019610000003" alpha="1" colorSpace="calibratedRGB"/>
                        <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    </navigationBar>
                    <nil name="viewControllers"/>
                    <connections>
                        <segue destination="pt9-wM-5jk" kind="relationship" relationship="rootViewController" id="Yx0-2b-Cyt"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="uji-hm-jgp" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2838" y="539"/>
        </scene>
        <!--Action Scan Free View Controller-->
        <scene sceneID="gfl-r8-n4j">
            <objects>
                <viewController storyboardIdentifier="ActionScanFreeViewController" id="bH2-8v-3Qu" customClass="ActionScanFreeViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="1Q9-Qh-7js">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="QuC-CB-g04">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="NAD-VH-Nks">
                                        <rect key="frame" x="16" y="16" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="40" id="hao-gZ-C5K"/>
                                            <constraint firstAttribute="width" constant="40" id="hh0-dq-VfH"/>
                                        </constraints>
                                        <state key="normal" image="scan_close"/>
                                        <connections>
                                            <action selector="closeBtnAction:" destination="bH2-8v-3Qu" eventType="touchUpInside" id="Lgl-Kb-U15"/>
                                        </connections>
                                    </button>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="g4l-gh-Clz">
                                        <rect key="frame" x="311" y="475" width="48" height="176"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="4U1-rN-Jce">
                                                <rect key="frame" x="0.0" y="0.0" width="48" height="48"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="48" id="5Hm-Ss-biQ"/>
                                                    <constraint firstAttribute="width" constant="48" id="Z9g-BC-O8C"/>
                                                </constraints>
                                                <state key="normal" image="scan_location"/>
                                                <connections>
                                                    <action selector="locationSetting:" destination="bH2-8v-3Qu" eventType="touchUpInside" id="E52-n6-m7j"/>
                                                    <action selector="locationSetting:" destination="u7X-I3-qMw" eventType="touchUpInside" id="IYf-r2-yk9"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="bx6-j3-uOF">
                                                <rect key="frame" x="0.0" y="64" width="48" height="48"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="48" id="62S-lP-LQM"/>
                                                    <constraint firstAttribute="width" constant="48" id="ih6-OH-DCi"/>
                                                </constraints>
                                                <state key="normal" image="sacan_input"/>
                                                <connections>
                                                    <action selector="editByManualBtnAution:" destination="bH2-8v-3Qu" eventType="touchUpInside" id="SOI-bt-NWz"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="czA-wz-buQ">
                                                <rect key="frame" x="0.0" y="128" width="48" height="48"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="48" id="REW-9W-c07"/>
                                                    <constraint firstAttribute="width" constant="48" id="sjj-M2-JOK"/>
                                                </constraints>
                                                <state key="normal" image="light-bulb-off"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                        <real key="value" value="24"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="setLightOnOrOff:" destination="Tvd-Xa-vqZ" eventType="touchUpInside" id="OpS-XW-1IA"/>
                                                    <action selector="setLightOnOrOff:" destination="bH2-8v-3Qu" eventType="touchUpInside" id="hHq-Nm-Vop"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="48" id="vw6-bs-XPx"/>
                                        </constraints>
                                    </stackView>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hJ6-vL-giB" customClass="PreviewViewForAction" customModule="App" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                    </view>
                                </subviews>
                                <viewLayoutGuide key="safeArea" id="Twa-DJ-JHG"/>
                                <color key="backgroundColor" white="0.66666666669999997" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="hJ6-vL-giB" firstAttribute="top" secondItem="QuC-CB-g04" secondAttribute="top" id="1QV-Ok-afD"/>
                                    <constraint firstItem="Twa-DJ-JHG" firstAttribute="bottom" secondItem="g4l-gh-Clz" secondAttribute="bottom" constant="16" id="4Eq-RN-hrt"/>
                                    <constraint firstItem="NAD-VH-Nks" firstAttribute="top" secondItem="Twa-DJ-JHG" secondAttribute="top" constant="16" id="8qg-ho-KLi"/>
                                    <constraint firstItem="hJ6-vL-giB" firstAttribute="leading" secondItem="QuC-CB-g04" secondAttribute="leading" id="BPN-bC-MBB"/>
                                    <constraint firstAttribute="trailing" secondItem="hJ6-vL-giB" secondAttribute="trailing" id="Fpu-MQ-vj0"/>
                                    <constraint firstAttribute="bottom" secondItem="hJ6-vL-giB" secondAttribute="bottom" id="P5x-Hp-p7C"/>
                                    <constraint firstItem="NAD-VH-Nks" firstAttribute="leading" secondItem="QuC-CB-g04" secondAttribute="leading" constant="16" id="w2B-94-K6D"/>
                                    <constraint firstItem="Twa-DJ-JHG" firstAttribute="trailing" secondItem="g4l-gh-Clz" secondAttribute="trailing" constant="16" id="zrG-h3-vp1"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="rBz-fZ-Fw7"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="QuC-CB-g04" secondAttribute="bottom" id="8d5-sN-Axf"/>
                            <constraint firstItem="QuC-CB-g04" firstAttribute="top" secondItem="1Q9-Qh-7js" secondAttribute="top" id="MVl-kc-sK4"/>
                            <constraint firstItem="QuC-CB-g04" firstAttribute="leading" secondItem="rBz-fZ-Fw7" secondAttribute="leading" id="Xgf-Qk-mVJ"/>
                            <constraint firstItem="rBz-fZ-Fw7" firstAttribute="trailing" secondItem="QuC-CB-g04" secondAttribute="trailing" id="fBI-jY-XKQ"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="lightBtn" destination="czA-wz-buQ" id="DrQ-PE-jWW"/>
                        <outlet property="locationBtn" destination="4U1-rN-Jce" id="gSu-cJ-uDe"/>
                        <outlet property="previewAction" destination="hJ6-vL-giB" id="LcB-3n-TQ7"/>
                        <outlet property="previewView" destination="QuC-CB-g04" id="QiB-UK-gY4"/>
                        <outlet property="topOfCloseBtn" destination="8qg-ho-KLi" id="g4N-As-Q3B"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="9rQ-08-u69" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="344.80000000000001" y="363.86806596701655"/>
        </scene>
        <!--New Work Flow Scan View Controller-->
        <scene sceneID="GyR-qk-wOn">
            <objects>
                <viewController storyboardIdentifier="NewWorkFlowScanViewController" id="K9e-O7-ydh" customClass="NewWorkFlowScanViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="wW8-Jx-kMr">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="MpN-Rm-j3x">
                                <rect key="frame" x="311" y="475" width="48" height="176"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="b9i-t3-W3R">
                                        <rect key="frame" x="0.0" y="0.0" width="48" height="48"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="48" id="AJE-GZ-IvX"/>
                                            <constraint firstAttribute="height" constant="48" id="eTM-pK-Jhx"/>
                                        </constraints>
                                        <state key="normal" image="scan_location"/>
                                        <connections>
                                            <action selector="locationSetting:" destination="K9e-O7-ydh" eventType="touchUpInside" id="g7d-pm-P1k"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="kjI-fP-sFg">
                                        <rect key="frame" x="0.0" y="64" width="48" height="48"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="48" id="PWf-hH-gfO"/>
                                            <constraint firstAttribute="height" constant="48" id="hQ9-Sv-I6z"/>
                                        </constraints>
                                        <state key="normal" image="sacan_input"/>
                                        <connections>
                                            <action selector="editByManualBtnAction:" destination="K9e-O7-ydh" eventType="touchUpInside" id="Kbc-v0-EUp"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="iim-lj-neZ">
                                        <rect key="frame" x="0.0" y="128" width="48" height="48"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="48" id="1DL-ln-kO1"/>
                                            <constraint firstAttribute="height" constant="48" id="bXJ-l0-nhi"/>
                                        </constraints>
                                        <state key="normal" image="light-bulb-off"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                <real key="value" value="24"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="setLightOnOrOff:" destination="K9e-O7-ydh" eventType="touchUpInside" id="JbL-SY-svo"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="width" constant="48" id="XE5-Xm-1Wu"/>
                                </constraints>
                            </stackView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="wjN-2A-rOg">
                                <rect key="frame" x="16" y="8" width="40" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="40" id="1bf-nz-yUF"/>
                                    <constraint firstAttribute="height" constant="40" id="ALS-o6-ST9"/>
                                </constraints>
                                <state key="normal" image="scan_close"/>
                                <connections>
                                    <action selector="closeBtnAction:" destination="K9e-O7-ydh" eventType="touchUpInside" id="f10-D6-rmS"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="0gh-vZ-c0o"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="wjN-2A-rOg" firstAttribute="top" secondItem="0gh-vZ-c0o" secondAttribute="top" constant="8" id="ZuH-Co-X3g"/>
                            <constraint firstItem="wjN-2A-rOg" firstAttribute="leading" secondItem="0gh-vZ-c0o" secondAttribute="leading" constant="16" id="cAe-2A-XYT"/>
                            <constraint firstItem="0gh-vZ-c0o" firstAttribute="bottom" secondItem="MpN-Rm-j3x" secondAttribute="bottom" constant="16" id="e0Z-ba-UDU"/>
                            <constraint firstItem="0gh-vZ-c0o" firstAttribute="trailing" secondItem="MpN-Rm-j3x" secondAttribute="trailing" constant="16" id="mAe-j1-fvq"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="clostBtn" destination="wjN-2A-rOg" id="Ayg-Jx-uUI"/>
                        <outlet property="editBtn" destination="kjI-fP-sFg" id="h2B-sg-jXj"/>
                        <outlet property="lightBtn" destination="iim-lj-neZ" id="vyM-fZ-Mxq"/>
                        <outlet property="locationBtn" destination="b9i-t3-W3R" id="BB9-yk-DV5"/>
                        <outlet property="topOfCloseBtn" destination="ZuH-Co-X3g" id="d3j-cP-5BP"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="ZLs-5b-nhU" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1164" y="1076"/>
        </scene>
        <!--Work Flow Scan Free View Controller-->
        <scene sceneID="oLt-fs-5OD">
            <objects>
                <viewController storyboardIdentifier="WorkFlowScanFreeViewController" id="obQ-gE-4A8" customClass="WorkFlowScanFreeViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Uox-Cn-QRl">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="RUK-aV-9fk">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="lZv-IH-uwg" customClass="PreviewViewForAction" customModule="App" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                    </view>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="pAX-fd-9fL">
                                        <rect key="frame" x="311" y="475" width="48" height="176"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="vuL-Uk-JRc">
                                                <rect key="frame" x="0.0" y="0.0" width="48" height="48"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="48" id="b6d-Ln-bLJ"/>
                                                    <constraint firstAttribute="width" constant="48" id="jBZ-bE-4hh"/>
                                                </constraints>
                                                <state key="normal" image="scan_location"/>
                                                <connections>
                                                    <action selector="locationSetting:" destination="obQ-gE-4A8" eventType="touchUpInside" id="C9H-Km-hIz"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="uNl-8t-o8t">
                                                <rect key="frame" x="0.0" y="64" width="48" height="48"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="48" id="JWh-ky-yJK"/>
                                                    <constraint firstAttribute="height" constant="48" id="fdJ-5f-6qG"/>
                                                </constraints>
                                                <state key="normal" image="sacan_input"/>
                                                <connections>
                                                    <action selector="editByManualBtnAution:" destination="obQ-gE-4A8" eventType="touchUpInside" id="VPa-b5-Jk8"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="E5g-93-VlL">
                                                <rect key="frame" x="0.0" y="128" width="48" height="48"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="48" id="Hbd-ii-Y1M"/>
                                                    <constraint firstAttribute="height" constant="48" id="vh0-w4-ZhL"/>
                                                </constraints>
                                                <state key="normal" image="light-bulb-off"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                        <real key="value" value="24"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="setLightOnOrOff:" destination="obQ-gE-4A8" eventType="touchUpInside" id="sJS-nP-E3K"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="48" id="Q7T-Lp-A9O"/>
                                        </constraints>
                                    </stackView>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="KwQ-f3-1Kt">
                                        <rect key="frame" x="16" y="8" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="40" id="C9Q-9h-Fpt"/>
                                            <constraint firstAttribute="width" constant="40" id="H9h-tP-CGf"/>
                                        </constraints>
                                        <state key="normal" image="scan_close"/>
                                        <connections>
                                            <action selector="closeBtnAction:" destination="obQ-gE-4A8" eventType="touchUpInside" id="Apg-or-FqF"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" white="0.66666666669999997" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="KwQ-f3-1Kt" firstAttribute="leading" secondItem="RUK-aV-9fk" secondAttribute="leading" constant="16" id="9oZ-me-8YJ"/>
                                    <constraint firstItem="lZv-IH-uwg" firstAttribute="top" secondItem="RUK-aV-9fk" secondAttribute="top" id="B5d-ez-9y2"/>
                                    <constraint firstAttribute="bottom" secondItem="lZv-IH-uwg" secondAttribute="bottom" id="SCh-bl-4oU"/>
                                    <constraint firstItem="lZv-IH-uwg" firstAttribute="leading" secondItem="RUK-aV-9fk" secondAttribute="leading" id="ZmH-47-YkM"/>
                                    <constraint firstAttribute="bottom" secondItem="pAX-fd-9fL" secondAttribute="bottom" constant="16" id="fVK-io-MUz"/>
                                    <constraint firstAttribute="trailing" secondItem="pAX-fd-9fL" secondAttribute="trailing" constant="16" id="suQ-24-9xf"/>
                                    <constraint firstAttribute="trailing" secondItem="lZv-IH-uwg" secondAttribute="trailing" id="wyw-61-dsI"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="zor-Ua-Sjh"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="KwQ-f3-1Kt" firstAttribute="top" secondItem="zor-Ua-Sjh" secondAttribute="top" constant="8" id="0p3-Zg-TNU"/>
                            <constraint firstItem="RUK-aV-9fk" firstAttribute="top" secondItem="Uox-Cn-QRl" secondAttribute="top" id="270-77-cJP"/>
                            <constraint firstItem="RUK-aV-9fk" firstAttribute="leading" secondItem="zor-Ua-Sjh" secondAttribute="leading" id="7Fy-2c-8tz"/>
                            <constraint firstItem="zor-Ua-Sjh" firstAttribute="trailing" secondItem="RUK-aV-9fk" secondAttribute="trailing" id="7ZW-r9-fdD"/>
                            <constraint firstAttribute="bottom" secondItem="RUK-aV-9fk" secondAttribute="bottom" id="pvM-bl-Pjc"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="lightBtn" destination="E5g-93-VlL" id="Qp9-SQ-AaI"/>
                        <outlet property="locationBtn" destination="vuL-Uk-JRc" id="PBi-TS-hxt"/>
                        <outlet property="previewAction" destination="lZv-IH-uwg" id="L8u-vw-qN7"/>
                        <outlet property="previewView" destination="RUK-aV-9fk" id="1aX-jk-x7Y"/>
                        <outlet property="topOfCloseBtn" destination="0p3-Zg-TNU" id="cgP-eL-aRl"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="qIV-Pk-W46" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="344.80000000000001" y="1077.2113943028487"/>
        </scene>
        <!--Third Work Flow Scan View Controller-->
        <scene sceneID="KGY-Ok-SKy">
            <objects>
                <viewController storyboardIdentifier="ThirdWorkFlowScanViewController" id="W4m-Vk-Sa3" customClass="ThirdWorkFlowScanViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="ODn-vo-DUz">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="VeJ-Ut-rjl">
                                <rect key="frame" x="0.0" y="558" width="375" height="109"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="gNC-wr-IIc" customClass="FontsizeButton" customModule="App" customModuleProvider="target">
                                        <rect key="frame" x="20" y="32" width="335" height="45"/>
                                        <color key="backgroundColor" red="0.**********" green="0.**********" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="45" id="Ud5-da-4PS"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                        <state key="normal" title="スキャンリストへ">
                                            <color key="titleColor" red="0.46274509800000002" green="0.46666666670000001" blue="0.46666666670000001" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                        </state>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                <integer key="value" value="8"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="layer.clipsToBounds" value="YES"/>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="scanListAction:" destination="W4m-Vk-Sa3" eventType="touchUpInside" id="wuW-dG-eWx"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="gNC-wr-IIc" secondAttribute="trailing" constant="20" id="4Jk-MM-kS1"/>
                                    <constraint firstAttribute="bottom" secondItem="gNC-wr-IIc" secondAttribute="bottom" constant="32" id="c6M-kp-xfz"/>
                                    <constraint firstItem="gNC-wr-IIc" firstAttribute="top" secondItem="VeJ-Ut-rjl" secondAttribute="top" constant="32" id="srb-Ie-Qib"/>
                                    <constraint firstItem="gNC-wr-IIc" firstAttribute="leading" secondItem="VeJ-Ut-rjl" secondAttribute="leading" constant="20" id="tJH-eh-5yb"/>
                                </constraints>
                            </view>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="VzN-Jb-KtE">
                                <rect key="frame" x="16" y="8" width="40" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="40" id="o85-hj-6Sz"/>
                                    <constraint firstAttribute="height" constant="40" id="zcg-sU-32c"/>
                                </constraints>
                                <state key="normal" image="scan_close"/>
                                <connections>
                                    <action selector="closeBtnAction:" destination="W4m-Vk-Sa3" eventType="touchUpInside" id="zw2-gi-dDD"/>
                                </connections>
                            </button>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="mzv-7c-3jg">
                                <rect key="frame" x="311" y="438" width="48" height="104"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="A8O-5x-oPR">
                                        <rect key="frame" x="0.0" y="0.0" width="48" height="48"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="48" id="1Tg-qx-5Qb"/>
                                            <constraint firstAttribute="height" constant="48" id="Dal-W3-LTL"/>
                                        </constraints>
                                        <state key="normal" image="scan_location"/>
                                        <connections>
                                            <action selector="locationSetting:" destination="W4m-Vk-Sa3" eventType="touchUpInside" id="B9g-91-4Pc"/>
                                        </connections>
                                    </button>
                                    <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="weL-3W-DGk">
                                        <rect key="frame" x="0.0" y="52" width="48" height="48"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="48" id="2kr-dL-wkp"/>
                                            <constraint firstAttribute="width" constant="48" id="OhD-Ox-O2g"/>
                                        </constraints>
                                        <state key="normal" image="sacan_input"/>
                                        <connections>
                                            <action selector="editByManualBtnAction:" destination="W4m-Vk-Sa3" eventType="touchUpInside" id="eaI-G4-A28"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="dDL-4A-tbJ">
                                        <rect key="frame" x="0.0" y="56" width="48" height="48"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="48" id="KAg-kc-OAj"/>
                                            <constraint firstAttribute="height" constant="48" id="nMJ-oc-GhK"/>
                                        </constraints>
                                        <state key="normal" image="light-bulb-off"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                <real key="value" value="24"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                    </button>
                                </subviews>
                            </stackView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="X9i-pA-I8N"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="X9i-pA-I8N" firstAttribute="trailing" secondItem="mzv-7c-3jg" secondAttribute="trailing" constant="16" id="59c-wB-3Ra"/>
                            <constraint firstAttribute="bottom" secondItem="VeJ-Ut-rjl" secondAttribute="bottom" id="8ZE-Iw-8Yl"/>
                            <constraint firstItem="VzN-Jb-KtE" firstAttribute="leading" secondItem="X9i-pA-I8N" secondAttribute="leading" constant="16" id="Cwd-c8-XxW"/>
                            <constraint firstItem="VeJ-Ut-rjl" firstAttribute="top" secondItem="mzv-7c-3jg" secondAttribute="bottom" constant="16" id="GOn-Si-d9w"/>
                            <constraint firstItem="VeJ-Ut-rjl" firstAttribute="leading" secondItem="ODn-vo-DUz" secondAttribute="leading" id="Jxd-BL-D8l"/>
                            <constraint firstAttribute="trailing" secondItem="VeJ-Ut-rjl" secondAttribute="trailing" id="ZtT-cg-anT"/>
                            <constraint firstItem="VzN-Jb-KtE" firstAttribute="top" secondItem="X9i-pA-I8N" secondAttribute="top" constant="8" id="nAy-gR-ERH"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="clostBtn" destination="VzN-Jb-KtE" id="nrz-Q7-Bc9"/>
                        <outlet property="editBtn" destination="weL-3W-DGk" id="J9x-Gb-iZ3"/>
                        <outlet property="locationBtn" destination="A8O-5x-oPR" id="FeJ-dv-Ilo"/>
                        <outlet property="toListBtn" destination="gNC-wr-IIc" id="nDZ-Yb-tPR"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="elR-dG-o4p" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-455" y="1826"/>
        </scene>
        <!--Third Work Flow Scan Free View Controller-->
        <scene sceneID="sXj-EW-nUP">
            <objects>
                <viewController storyboardIdentifier="ThirdWorkFlowScanFreeViewController" id="mPQ-Th-8K2" customClass="ThirdWorkFlowScanFreeViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="zGd-DA-ObE">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="NHJ-jy-nG5" customClass="PreviewViewForAction" customModule="App" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Sc5-YP-JcL">
                                        <rect key="frame" x="16" y="8" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="40" id="CZw-R5-fCh"/>
                                            <constraint firstAttribute="height" constant="40" id="yBz-32-InG"/>
                                        </constraints>
                                        <state key="normal" image="scan_close"/>
                                        <connections>
                                            <action selector="closeBtnAction:" destination="mPQ-Th-8K2" eventType="touchUpInside" id="64w-kh-BTU"/>
                                        </connections>
                                    </button>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="1nz-CF-8sD">
                                        <rect key="frame" x="311" y="430" width="48" height="112"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="EVd-AC-2cM">
                                                <rect key="frame" x="0.0" y="0.0" width="48" height="48"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="48" id="MRK-M5-7Rg"/>
                                                    <constraint firstAttribute="width" constant="48" id="v0r-gy-ywO"/>
                                                </constraints>
                                                <state key="normal" image="scan_location"/>
                                                <connections>
                                                    <action selector="locationSetting:" destination="mPQ-Th-8K2" eventType="touchUpInside" id="Dn2-t7-3CT"/>
                                                </connections>
                                            </button>
                                            <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="5pv-Wj-SB0">
                                                <rect key="frame" x="0.0" y="56" width="48" height="48"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="48" id="e7y-x9-14O"/>
                                                    <constraint firstAttribute="width" constant="48" id="snT-Ec-eni"/>
                                                </constraints>
                                                <state key="normal" image="sacan_input"/>
                                                <connections>
                                                    <action selector="editByManualBtnAution:" destination="mPQ-Th-8K2" eventType="touchUpInside" id="6Io-Iy-YEi"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Q3c-5b-cyb">
                                                <rect key="frame" x="0.0" y="64" width="48" height="48"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="48" id="Vkj-dr-wM1"/>
                                                    <constraint firstAttribute="height" constant="48" id="g9G-Qd-fyf"/>
                                                </constraints>
                                                <state key="normal" image="light-bulb-off"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                        <real key="value" value="24"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="setLightOnOrOff:" destination="Tvd-Xa-vqZ" eventType="touchUpInside" id="EvA-is-WCA"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                    </stackView>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="JeC-LB-Moa">
                                        <rect key="frame" x="0.0" y="558" width="375" height="109"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Mc7-A2-RhC" customClass="FontsizeButton" customModule="App" customModuleProvider="target">
                                                <rect key="frame" x="20" y="32" width="335" height="45"/>
                                                <color key="backgroundColor" red="0.**********" green="0.**********" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="45" id="gyg-Kj-noy"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                                <state key="normal" title="スキャンリストへ">
                                                    <color key="titleColor" red="0.46274509800000002" green="0.46666666670000001" blue="0.46666666670000001" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                </state>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                        <integer key="value" value="8"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="boolean" keyPath="layer.clipsToBounds" value="YES"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="countOfAssetsBtnAction:" destination="mPQ-Th-8K2" eventType="touchUpInside" id="9gt-eX-irA"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="bottom" secondItem="Mc7-A2-RhC" secondAttribute="bottom" constant="32" id="MQL-zu-glg"/>
                                            <constraint firstItem="Mc7-A2-RhC" firstAttribute="top" secondItem="JeC-LB-Moa" secondAttribute="top" constant="32" id="NlD-wb-nhd"/>
                                            <constraint firstItem="Mc7-A2-RhC" firstAttribute="leading" secondItem="JeC-LB-Moa" secondAttribute="leading" constant="20" id="fSw-PY-Ut6"/>
                                            <constraint firstAttribute="trailing" secondItem="Mc7-A2-RhC" secondAttribute="trailing" constant="20" id="jeD-ia-z5C"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="0.66666666669999997" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="Sc5-YP-JcL" firstAttribute="leading" secondItem="NHJ-jy-nG5" secondAttribute="leading" constant="16" id="8hI-Na-RVJ"/>
                                    <constraint firstAttribute="trailing" secondItem="JeC-LB-Moa" secondAttribute="trailing" id="GXS-vS-2R4"/>
                                    <constraint firstAttribute="bottom" secondItem="JeC-LB-Moa" secondAttribute="bottom" id="dB2-bX-njd"/>
                                    <constraint firstItem="JeC-LB-Moa" firstAttribute="top" secondItem="1nz-CF-8sD" secondAttribute="bottom" constant="16" id="fFc-l2-gY3"/>
                                    <constraint firstItem="JeC-LB-Moa" firstAttribute="leading" secondItem="NHJ-jy-nG5" secondAttribute="leading" id="qf6-Ds-ldc"/>
                                    <constraint firstAttribute="trailing" secondItem="1nz-CF-8sD" secondAttribute="trailing" constant="16" id="yCq-nK-VbC"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="FgJ-Dy-JeX"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="NHJ-jy-nG5" secondAttribute="bottom" id="4RI-9A-jMZ"/>
                            <constraint firstItem="Sc5-YP-JcL" firstAttribute="top" secondItem="FgJ-Dy-JeX" secondAttribute="top" constant="8" id="KhL-JY-kAn"/>
                            <constraint firstItem="NHJ-jy-nG5" firstAttribute="leading" secondItem="FgJ-Dy-JeX" secondAttribute="leading" id="gn0-rI-lpm"/>
                            <constraint firstItem="FgJ-Dy-JeX" firstAttribute="trailing" secondItem="NHJ-jy-nG5" secondAttribute="trailing" id="lGf-xp-Lnl"/>
                            <constraint firstItem="NHJ-jy-nG5" firstAttribute="top" secondItem="zGd-DA-ObE" secondAttribute="top" id="yn2-cl-xDa"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="lightBtn" destination="Q3c-5b-cyb" id="u4f-EQ-vkH"/>
                        <outlet property="locationBtn" destination="EVd-AC-2cM" id="5Gx-48-hoH"/>
                        <outlet property="previewView" destination="NHJ-jy-nG5" id="kF5-au-xhs"/>
                        <outlet property="toListBtn" destination="Mc7-A2-RhC" id="x8K-mT-Vjv"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="deE-td-ztk" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="345" y="1826"/>
        </scene>
        <!--Asset Work Flow Scan Free View Controller-->
        <scene sceneID="1SG-B7-hDO">
            <objects>
                <viewController storyboardIdentifier="AssetWorkFlowScanFreeViewController" hidesBottomBarWhenPushed="YES" id="u7X-I3-qMw" customClass="AssetWorkFlowScanFreeViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="vRQ-45-rwI">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="fa2-Ub-uDo">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Ems-4U-1nT" customClass="PreviewForWorkflow" customModule="App" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                    </view>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="aOD-ul-mIg">
                                        <rect key="frame" x="16" y="8" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="40" id="2Va-Fh-CxA"/>
                                            <constraint firstAttribute="width" constant="40" id="guL-Wi-FpU"/>
                                        </constraints>
                                        <state key="normal" image="scan_close"/>
                                        <connections>
                                            <action selector="closeBtnAction:" destination="u7X-I3-qMw" eventType="touchUpInside" id="apm-1e-SGm"/>
                                        </connections>
                                    </button>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="UIQ-sG-sXi">
                                        <rect key="frame" x="311" y="366" width="48" height="176"/>
                                        <subviews>
                                            <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="dyo-Pp-CEm">
                                                <rect key="frame" x="0.0" y="-48" width="48" height="48"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="48" id="mLv-Le-dYL"/>
                                                    <constraint firstAttribute="width" constant="48" id="szy-Tz-CpW"/>
                                                </constraints>
                                                <state key="normal" image="scan_trim"/>
                                                <connections>
                                                    <action selector="regixSettingAction:" destination="u7X-I3-qMw" eventType="touchUpInside" id="91z-1N-ezj"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="BwA-yf-3kJ">
                                                <rect key="frame" x="0.0" y="0.0" width="48" height="48"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="48" id="Bim-d0-TrC"/>
                                                    <constraint firstAttribute="height" constant="48" id="arp-7z-gNR"/>
                                                </constraints>
                                                <state key="normal" image="scan_location"/>
                                                <connections>
                                                    <action selector="locationSetting:" destination="u7X-I3-qMw" eventType="touchUpInside" id="Ayk-C1-dOH"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="GSJ-Qa-H3f">
                                                <rect key="frame" x="0.0" y="64" width="48" height="48"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="48" id="5t7-AR-IhB"/>
                                                    <constraint firstAttribute="width" constant="48" id="Gsb-aG-tpB"/>
                                                </constraints>
                                                <state key="normal" image="sacan_input"/>
                                                <connections>
                                                    <action selector="enterBarcodeManually:" destination="u7X-I3-qMw" eventType="touchUpInside" id="wZH-7i-B2P"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="BB2-he-6KI">
                                                <rect key="frame" x="0.0" y="128" width="48" height="48"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="48" id="BWQ-7h-PI3"/>
                                                    <constraint firstAttribute="width" constant="48" id="ggP-eI-F3g"/>
                                                </constraints>
                                                <state key="normal" image="light-bulb-off"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                        <real key="value" value="24"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="setLightOnOrOff:" destination="Tvd-Xa-vqZ" eventType="touchUpInside" id="9jZ-X1-wXS"/>
                                                    <action selector="setLightOnOrOff:" destination="u7X-I3-qMw" eventType="touchUpInside" id="cD7-dA-NwY"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                    </stackView>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ttO-cO-OeF">
                                        <rect key="frame" x="0.0" y="558" width="375" height="109"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="cr0-Ov-uIQ" customClass="FontsizeButton" customModule="App" customModuleProvider="target">
                                                <rect key="frame" x="20" y="32" width="335" height="45"/>
                                                <color key="backgroundColor" red="0.**********" green="0.**********" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="45" id="STo-II-a0H"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                                <state key="normal" title="スキャンリストへ">
                                                    <color key="titleColor" red="0.46274509800000002" green="0.46666666670000001" blue="0.46666666670000001" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                </state>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                        <integer key="value" value="8"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="boolean" keyPath="layer.clipsToBounds" value="YES"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="showAssetDetailBtn:" destination="u7X-I3-qMw" eventType="touchUpInside" id="0k1-WT-gKz"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="cr0-Ov-uIQ" firstAttribute="leading" secondItem="ttO-cO-OeF" secondAttribute="leading" constant="20" id="6BG-Wg-CvG"/>
                                            <constraint firstAttribute="bottom" secondItem="cr0-Ov-uIQ" secondAttribute="bottom" constant="32" id="DAN-3L-eXO"/>
                                            <constraint firstItem="cr0-Ov-uIQ" firstAttribute="top" secondItem="ttO-cO-OeF" secondAttribute="top" constant="32" id="NQj-JM-Abk"/>
                                            <constraint firstAttribute="trailing" secondItem="cr0-Ov-uIQ" secondAttribute="trailing" constant="20" id="QbB-Jl-JWW"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="aOD-ul-mIg" firstAttribute="leading" secondItem="fa2-Ub-uDo" secondAttribute="leading" constant="16" id="1YA-VU-HW5"/>
                                    <constraint firstAttribute="trailing" secondItem="ttO-cO-OeF" secondAttribute="trailing" id="1ci-qE-d1s"/>
                                    <constraint firstItem="ttO-cO-OeF" firstAttribute="top" secondItem="UIQ-sG-sXi" secondAttribute="bottom" constant="16" id="8Pm-bh-gmf"/>
                                    <constraint firstAttribute="trailing" secondItem="Ems-4U-1nT" secondAttribute="trailing" id="Jok-oh-vu7"/>
                                    <constraint firstItem="Ems-4U-1nT" firstAttribute="top" secondItem="fa2-Ub-uDo" secondAttribute="top" id="O94-9m-36Y"/>
                                    <constraint firstItem="Ems-4U-1nT" firstAttribute="leading" secondItem="fa2-Ub-uDo" secondAttribute="leading" id="UO2-AO-z2v"/>
                                    <constraint firstAttribute="bottom" secondItem="ttO-cO-OeF" secondAttribute="bottom" id="Vqm-Lh-2RN"/>
                                    <constraint firstAttribute="trailing" secondItem="UIQ-sG-sXi" secondAttribute="trailing" constant="16" id="WLE-hT-jgJ"/>
                                    <constraint firstItem="ttO-cO-OeF" firstAttribute="leading" secondItem="fa2-Ub-uDo" secondAttribute="leading" id="mfG-DH-YG8"/>
                                    <constraint firstAttribute="bottom" secondItem="Ems-4U-1nT" secondAttribute="bottom" id="ysK-bW-dhL"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="eYD-OJ-YXI"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="eYD-OJ-YXI" firstAttribute="trailing" secondItem="fa2-Ub-uDo" secondAttribute="trailing" id="8Fn-8h-rkG"/>
                            <constraint firstItem="aOD-ul-mIg" firstAttribute="top" secondItem="eYD-OJ-YXI" secondAttribute="top" constant="8" id="DkE-ST-mH2"/>
                            <constraint firstAttribute="bottom" secondItem="fa2-Ub-uDo" secondAttribute="bottom" id="SgT-gO-xBM"/>
                            <constraint firstItem="fa2-Ub-uDo" firstAttribute="leading" secondItem="eYD-OJ-YXI" secondAttribute="leading" id="nmj-rb-Og5"/>
                            <constraint firstItem="fa2-Ub-uDo" firstAttribute="top" secondItem="vRQ-45-rwI" secondAttribute="top" id="v9d-LU-pA8"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="guO-H3-LpR"/>
                    <connections>
                        <outlet property="enterBarcodeManuallyBtn" destination="GSJ-Qa-H3f" id="iWH-2u-3jh"/>
                        <outlet property="lightBtn" destination="BB2-he-6KI" id="rrC-B6-6q2"/>
                        <outlet property="locationBtn" destination="BwA-yf-3kJ" id="daF-d2-Rbv"/>
                        <outlet property="previewView" destination="fa2-Ub-uDo" id="vzS-H6-jPy"/>
                        <outlet property="previewWorkflow" destination="Ems-4U-1nT" id="1sk-9w-Deo"/>
                        <outlet property="regixSettingBtn" destination="dyo-Pp-CEm" id="Rhg-Uq-jOL"/>
                        <outlet property="showDetailBtn" destination="cr0-Ov-uIQ" id="F37-nP-Vke"/>
                        <outlet property="topOfCloseBtn" destination="DkE-ST-mH2" id="lLJ-ue-WTV"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="gEg-dr-8a3" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="344.80000000000001" y="2556.9715142428786"/>
        </scene>
        <!--Asset Work Flow Scan View Controller-->
        <scene sceneID="vZF-du-1Jp">
            <objects>
                <viewController storyboardIdentifier="AssetWorkFlowScanViewController" id="HL4-gc-dBK" customClass="AssetWorkFlowScanViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="xyV-3h-hRu">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="QKs-K4-yeX">
                                <rect key="frame" x="0.0" y="558" width="375" height="109"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="H6d-y4-8e0" customClass="FontsizeButton" customModule="App" customModuleProvider="target">
                                        <rect key="frame" x="20" y="32" width="335" height="45"/>
                                        <color key="backgroundColor" red="0.**********" green="0.**********" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="45" id="z6D-Mu-Mg1"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                        <state key="normal" title="スキャンリストへ">
                                            <color key="titleColor" red="0.46274509800000002" green="0.46666666670000001" blue="0.46666666670000001" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                        </state>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                <integer key="value" value="8"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="layer.clipsToBounds" value="YES"/>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="showAssetDetailBtn:" destination="HL4-gc-dBK" eventType="touchUpInside" id="5In-BY-nJl"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="H6d-y4-8e0" firstAttribute="leading" secondItem="QKs-K4-yeX" secondAttribute="leading" constant="20" id="51I-8w-aeE"/>
                                    <constraint firstAttribute="bottom" secondItem="H6d-y4-8e0" secondAttribute="bottom" constant="32" id="PR4-V0-2j5"/>
                                    <constraint firstItem="H6d-y4-8e0" firstAttribute="top" secondItem="QKs-K4-yeX" secondAttribute="top" constant="32" id="bLz-Uv-CrW"/>
                                    <constraint firstAttribute="trailing" secondItem="H6d-y4-8e0" secondAttribute="trailing" constant="20" id="hPE-RE-KqA"/>
                                </constraints>
                            </view>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Wkt-Os-2jc">
                                <rect key="frame" x="16" y="8" width="40" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="40" id="T8b-2z-g1c"/>
                                    <constraint firstAttribute="height" constant="40" id="Uu1-B2-136"/>
                                </constraints>
                                <state key="normal" image="scan_close"/>
                                <connections>
                                    <action selector="closeBtnAction:" destination="u7X-I3-qMw" eventType="touchUpInside" id="Chk-K3-Vj8"/>
                                    <action selector="closeBtnAction:" destination="HL4-gc-dBK" eventType="touchUpInside" id="UrG-cY-PgO"/>
                                </connections>
                            </button>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="3HL-nM-Ggy">
                                <rect key="frame" x="311" y="366" width="48" height="176"/>
                                <subviews>
                                    <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="0AP-wl-QUF">
                                        <rect key="frame" x="0.0" y="-48" width="48" height="48"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="48" id="T0k-O2-slr"/>
                                            <constraint firstAttribute="height" constant="48" id="kEo-dT-yHK"/>
                                        </constraints>
                                        <state key="normal" image="scan_trim"/>
                                        <connections>
                                            <action selector="regixSettingAction:" destination="HL4-gc-dBK" eventType="touchUpInside" id="5eU-aH-wCC"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Vpi-WQ-AdW">
                                        <rect key="frame" x="0.0" y="0.0" width="48" height="48"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="48" id="0A2-xu-TRt"/>
                                            <constraint firstAttribute="height" constant="48" id="x6c-sD-bW2"/>
                                        </constraints>
                                        <state key="normal" image="scan_location"/>
                                        <connections>
                                            <action selector="locationSetting:" destination="HL4-gc-dBK" eventType="touchUpInside" id="0RY-VW-F3L"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="7BT-aR-0TV">
                                        <rect key="frame" x="0.0" y="64" width="48" height="48"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="48" id="BUu-zi-T3D"/>
                                            <constraint firstAttribute="width" constant="48" id="iEg-DD-Xdj"/>
                                        </constraints>
                                        <state key="normal" image="sacan_input"/>
                                        <connections>
                                            <action selector="enterBarcodeManually:" destination="HL4-gc-dBK" eventType="touchUpInside" id="Lvb-GX-9f3"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="CiJ-IE-mlD">
                                        <rect key="frame" x="0.0" y="128" width="48" height="48"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="48" id="Hq9-1T-gTO"/>
                                            <constraint firstAttribute="height" constant="48" id="WPO-Kt-dsx"/>
                                        </constraints>
                                        <state key="normal" image="light-bulb-off"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                <real key="value" value="24"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="setLightOnOrOff:" destination="HL4-gc-dBK" eventType="touchUpInside" id="Dsd-vm-ghF"/>
                                        </connections>
                                    </button>
                                </subviews>
                            </stackView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="UdY-IE-lah"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="Wkt-Os-2jc" firstAttribute="leading" secondItem="UdY-IE-lah" secondAttribute="leading" constant="16" id="Chd-jJ-2fT"/>
                            <constraint firstItem="Wkt-Os-2jc" firstAttribute="top" secondItem="UdY-IE-lah" secondAttribute="top" constant="8" id="Iel-4A-tbH"/>
                            <constraint firstItem="QKs-K4-yeX" firstAttribute="top" secondItem="3HL-nM-Ggy" secondAttribute="bottom" constant="16" id="Nl4-xr-AnR"/>
                            <constraint firstAttribute="trailing" secondItem="QKs-K4-yeX" secondAttribute="trailing" id="aX4-3Y-Rg3"/>
                            <constraint firstItem="QKs-K4-yeX" firstAttribute="leading" secondItem="xyV-3h-hRu" secondAttribute="leading" id="bT7-A3-ejz"/>
                            <constraint firstItem="UdY-IE-lah" firstAttribute="trailing" secondItem="3HL-nM-Ggy" secondAttribute="trailing" constant="16" id="fJO-fb-tlS"/>
                            <constraint firstAttribute="bottom" secondItem="QKs-K4-yeX" secondAttribute="bottom" id="x1h-GE-kJ3"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="1G8-0x-g08"/>
                    <connections>
                        <outlet property="enterBarcodeManuallyBtn" destination="7BT-aR-0TV" id="ZSX-ai-U3H"/>
                        <outlet property="lightBtn" destination="CiJ-IE-mlD" id="SIy-og-opD"/>
                        <outlet property="loctionBtn" destination="Vpi-WQ-AdW" id="jZC-Lh-uLT"/>
                        <outlet property="regixSettingBtn" destination="0AP-wl-QUF" id="CMF-VL-csp"/>
                        <outlet property="showDetailBtn" destination="H6d-y4-8e0" id="kMI-M5-KYN"/>
                        <outlet property="topOfCloseBtn" destination="Iel-4A-tbH" id="gBX-wc-G6v"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="MXL-Np-Zpg" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-442.39999999999998" y="2556.9715142428786"/>
        </scene>
        <!--No Subprocess Work Flow Scan View Controller-->
        <scene sceneID="HgE-uw-hOp">
            <objects>
                <viewController storyboardIdentifier="NoSubprocessWorkFlowScanViewController" id="Tvd-Xa-vqZ" customClass="NoSubprocessWorkFlowScanViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" restorationIdentifier="NoSubprocessWorkFlowScanViewController" id="Djd-5e-i2A">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="689-1I-5q7">
                                <rect key="frame" x="16" y="8" width="40" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="5GC-4o-h7L"/>
                                    <constraint firstAttribute="width" constant="40" id="nes-lZ-ruV"/>
                                </constraints>
                                <state key="normal" image="scan_close"/>
                                <connections>
                                    <action selector="closeBtnAction:" destination="Tvd-Xa-vqZ" eventType="touchUpInside" id="pLA-wF-ie9"/>
                                </connections>
                            </button>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="3iU-Xd-Weo">
                                <rect key="frame" x="311" y="411" width="48" height="240"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="KKc-Km-lxx">
                                        <rect key="frame" x="0.0" y="0.0" width="48" height="48"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="48" id="GT6-9x-d0k"/>
                                            <constraint firstAttribute="width" constant="48" id="m5i-nE-OL8"/>
                                        </constraints>
                                        <state key="normal" image="scan_trim"/>
                                        <connections>
                                            <action selector="regixSettingAction:" destination="Tvd-Xa-vqZ" eventType="touchUpInside" id="XUK-Pn-yuy"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="DsC-Ty-j3Z">
                                        <rect key="frame" x="0.0" y="64" width="48" height="48"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="48" id="965-kW-Pab"/>
                                            <constraint firstAttribute="height" constant="48" id="fn6-Zu-2AU"/>
                                        </constraints>
                                        <state key="normal" image="scan_location"/>
                                        <connections>
                                            <action selector="locationSetting:" destination="Tvd-Xa-vqZ" eventType="touchUpInside" id="XTy-JY-zaa"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="LHN-7r-Zhx">
                                        <rect key="frame" x="0.0" y="128" width="48" height="48"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="48" id="WTh-Tp-aiO"/>
                                            <constraint firstAttribute="width" constant="48" id="eVy-wV-kYd"/>
                                        </constraints>
                                        <state key="normal" image="sacan_input"/>
                                        <connections>
                                            <action selector="enterBarcodeManually:" destination="Tvd-Xa-vqZ" eventType="touchUpInside" id="OjQ-pJ-hK0"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="YZl-1k-vIw">
                                        <rect key="frame" x="0.0" y="192" width="48" height="48"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="48" id="Aj1-19-6X2"/>
                                            <constraint firstAttribute="width" constant="48" id="RKN-0g-8wT"/>
                                        </constraints>
                                        <state key="normal" image="light-bulb-off"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                <real key="value" value="24"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="setLightOnOrOff:" destination="Tvd-Xa-vqZ" eventType="touchUpInside" id="zwk-G5-hAn"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="width" constant="48" id="29q-tp-TCY"/>
                                </constraints>
                            </stackView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="hJb-Dt-zn0"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="689-1I-5q7" firstAttribute="leading" secondItem="hJb-Dt-zn0" secondAttribute="leading" constant="16" id="75W-VQ-OeN"/>
                            <constraint firstItem="hJb-Dt-zn0" firstAttribute="trailing" secondItem="3iU-Xd-Weo" secondAttribute="trailing" constant="16" id="WOl-8f-gJX"/>
                            <constraint firstItem="hJb-Dt-zn0" firstAttribute="bottom" secondItem="3iU-Xd-Weo" secondAttribute="bottom" constant="16" id="osX-GR-eQG"/>
                            <constraint firstItem="689-1I-5q7" firstAttribute="top" secondItem="hJb-Dt-zn0" secondAttribute="top" constant="8" id="reP-YC-SKE"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="wRe-fi-Mai"/>
                    <connections>
                        <outlet property="enterBarcodeManuallyBtn" destination="LHN-7r-Zhx" id="ECc-Ye-Dhi"/>
                        <outlet property="lightBtn" destination="YZl-1k-vIw" id="LVB-zO-62h"/>
                        <outlet property="locationBtn" destination="DsC-Ty-j3Z" id="OhA-q0-s40"/>
                        <outlet property="regixSettingBtn" destination="KKc-Km-lxx" id="dhL-Wz-pNk"/>
                        <outlet property="topOfCloseBtn" destination="reP-YC-SKE" id="M3k-J9-dfP"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="ySx-VT-zX8" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-442.39999999999998" y="3253.2233883058475"/>
        </scene>
        <!--No Subprocess Work Flow Scan Free View Controller-->
        <scene sceneID="lua-N0-f2l">
            <objects>
                <viewController storyboardIdentifier="NoSubprocessWorkFlowScanFreeViewController" hidesBottomBarWhenPushed="YES" id="Tt6-ay-zvG" customClass="NoSubprocessWorkFlowScanFreeViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="FrC-l9-9LM">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="E1v-G0-5w4">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="a82-3H-Yr6" customClass="PreviewForWorkflow" customModule="App" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                    </view>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="pdI-r9-nas">
                                        <rect key="frame" x="16" y="8" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="40" id="7Ps-6S-tpu"/>
                                            <constraint firstAttribute="height" constant="40" id="NnH-WO-OLx"/>
                                        </constraints>
                                        <state key="normal" image="scan_close"/>
                                        <connections>
                                            <action selector="closeBtnAction:" destination="Tt6-ay-zvG" eventType="touchUpInside" id="YVS-c0-o2b"/>
                                        </connections>
                                    </button>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="X13-Q3-GdN">
                                        <rect key="frame" x="311" y="411" width="48" height="240"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="g5F-ZQ-PDa">
                                                <rect key="frame" x="0.0" y="0.0" width="48" height="48"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="48" id="i8m-ef-nMu"/>
                                                    <constraint firstAttribute="width" constant="48" id="y2h-pQ-sGu"/>
                                                </constraints>
                                                <state key="normal" image="scan_trim"/>
                                                <connections>
                                                    <action selector="regixSettingAction:" destination="Tt6-ay-zvG" eventType="touchUpInside" id="kvL-AP-BOn"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="DDc-2h-rFg">
                                                <rect key="frame" x="0.0" y="64" width="48" height="48"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="48" id="lmo-oe-kgk"/>
                                                    <constraint firstAttribute="height" constant="48" id="mLR-K3-z42"/>
                                                </constraints>
                                                <state key="normal" image="scan_location"/>
                                                <connections>
                                                    <action selector="locationSetting:" destination="Tt6-ay-zvG" eventType="touchUpInside" id="as1-3T-BDH"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="99U-w8-7gM">
                                                <rect key="frame" x="0.0" y="128" width="48" height="48"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="48" id="AxA-o5-djL"/>
                                                    <constraint firstAttribute="width" constant="48" id="HOJ-Bq-ykw"/>
                                                </constraints>
                                                <state key="normal" image="sacan_input"/>
                                                <connections>
                                                    <action selector="enterBarcodeManually:" destination="Tt6-ay-zvG" eventType="touchUpInside" id="K9E-c2-Pk4"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="GdV-Cr-HL0">
                                                <rect key="frame" x="0.0" y="192" width="48" height="48"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="48" id="ipy-6V-egu"/>
                                                    <constraint firstAttribute="width" constant="48" id="zxo-jm-jic"/>
                                                </constraints>
                                                <state key="normal" image="light-bulb-off"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                        <real key="value" value="24"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="setLightOnOrOff:" destination="Tvd-Xa-vqZ" eventType="touchUpInside" id="5Oj-y1-OwE"/>
                                                    <action selector="setLightOnOrOff:" destination="Tt6-ay-zvG" eventType="touchUpInside" id="RFi-CR-zpf"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="48" id="Web-gQ-LDX"/>
                                        </constraints>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="a82-3H-Yr6" secondAttribute="trailing" id="ILg-sY-NYT"/>
                                    <constraint firstAttribute="bottom" secondItem="X13-Q3-GdN" secondAttribute="bottom" constant="16" id="JY2-1J-gE1"/>
                                    <constraint firstItem="a82-3H-Yr6" firstAttribute="top" secondItem="E1v-G0-5w4" secondAttribute="top" id="K1U-8u-jDk"/>
                                    <constraint firstItem="pdI-r9-nas" firstAttribute="leading" secondItem="E1v-G0-5w4" secondAttribute="leading" constant="16" id="eXa-4k-hdZ"/>
                                    <constraint firstItem="a82-3H-Yr6" firstAttribute="leading" secondItem="E1v-G0-5w4" secondAttribute="leading" id="kff-T7-2Z2"/>
                                    <constraint firstAttribute="bottom" secondItem="a82-3H-Yr6" secondAttribute="bottom" id="khH-Je-Vqn"/>
                                    <constraint firstAttribute="trailing" secondItem="X13-Q3-GdN" secondAttribute="trailing" constant="16" id="u49-ik-19D"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="iSC-eC-H7J"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="pdI-r9-nas" firstAttribute="top" secondItem="iSC-eC-H7J" secondAttribute="top" constant="8" id="8Co-bM-4vJ"/>
                            <constraint firstItem="iSC-eC-H7J" firstAttribute="trailing" secondItem="E1v-G0-5w4" secondAttribute="trailing" id="DrD-FA-q1b"/>
                            <constraint firstAttribute="bottom" secondItem="E1v-G0-5w4" secondAttribute="bottom" id="Fjb-wD-ByE"/>
                            <constraint firstItem="E1v-G0-5w4" firstAttribute="leading" secondItem="iSC-eC-H7J" secondAttribute="leading" id="uB7-Vr-bHm"/>
                            <constraint firstItem="E1v-G0-5w4" firstAttribute="top" secondItem="FrC-l9-9LM" secondAttribute="top" id="yJv-Ue-0c5"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="E1u-MS-19T"/>
                    <connections>
                        <outlet property="enterBarcodeManuallyBtn" destination="99U-w8-7gM" id="fhQ-Cn-gBm"/>
                        <outlet property="lightBtn" destination="GdV-Cr-HL0" id="t0K-13-4GJ"/>
                        <outlet property="locationBtn" destination="DDc-2h-rFg" id="8YJ-Rv-3c9"/>
                        <outlet property="previewView" destination="E1v-G0-5w4" id="7Dn-8q-KWq"/>
                        <outlet property="previewWorkflow" destination="a82-3H-Yr6" id="APY-Jn-HBe"/>
                        <outlet property="regixSettingBtn" destination="g5F-ZQ-PDa" id="Omd-8m-ZJs"/>
                        <outlet property="topOfCloseBtn" destination="8Co-bM-4vJ" id="eO6-GH-X60"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="X0D-l0-KMw" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="344.80000000000001" y="3252.3238380809598"/>
        </scene>
        <!--No Claim Work Flow Scan View Controller-->
        <scene sceneID="DtU-gh-um5">
            <objects>
                <viewController storyboardIdentifier="NoClaimWorkFlowScanViewController" id="g5F-EO-qcV" customClass="NoClaimWorkFlowScanViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="rPa-tq-olN">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Gb0-Wf-QFL">
                                <rect key="frame" x="16" y="8" width="40" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="1k6-46-Ybi"/>
                                    <constraint firstAttribute="width" constant="40" id="Lpv-p1-API"/>
                                </constraints>
                                <state key="normal" image="scan_close"/>
                                <connections>
                                    <action selector="closeBtnAction:" destination="g5F-EO-qcV" eventType="touchUpInside" id="K6k-ww-ySK"/>
                                </connections>
                            </button>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="hkd-pF-SaL">
                                <rect key="frame" x="311" y="475" width="48" height="176"/>
                                <subviews>
                                    <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="VbG-h1-agc">
                                        <rect key="frame" x="0.0" y="-48" width="48" height="48"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="48" id="YHS-3d-rh8"/>
                                            <constraint firstAttribute="height" constant="48" id="bog-fn-Efh"/>
                                        </constraints>
                                        <state key="normal" image="scan_trim"/>
                                        <connections>
                                            <action selector="onClickTrim:" destination="g5F-EO-qcV" eventType="touchUpInside" id="ud1-1G-fAV"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ugO-Ry-Pin">
                                        <rect key="frame" x="0.0" y="0.0" width="48" height="48"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="48" id="MXp-bP-uxG"/>
                                            <constraint firstAttribute="width" constant="48" id="V6H-yF-uNO"/>
                                        </constraints>
                                        <state key="normal" image="scan_location"/>
                                        <connections>
                                            <action selector="onClickLocation:" destination="g5F-EO-qcV" eventType="touchUpInside" id="Khh-EI-Pww"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="RB9-Wj-I2v">
                                        <rect key="frame" x="0.0" y="64" width="48" height="48"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="48" id="c4X-oV-6Yv"/>
                                            <constraint firstAttribute="width" constant="48" id="suh-ii-dN4"/>
                                        </constraints>
                                        <state key="normal" image="sacan_input"/>
                                        <connections>
                                            <action selector="onClickInput:" destination="g5F-EO-qcV" eventType="touchUpInside" id="NIa-Xy-In3"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="5WP-lt-QtG">
                                        <rect key="frame" x="0.0" y="128" width="48" height="48"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="48" id="gkC-GM-qgE"/>
                                            <constraint firstAttribute="width" constant="48" id="rto-sM-uYd"/>
                                        </constraints>
                                        <state key="normal" image="light-bulb-off"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                <real key="value" value="24"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="setLightOnOrOff:" destination="g5F-EO-qcV" eventType="touchUpInside" id="rqj-6e-Kuc"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="width" constant="48" id="fQg-cW-AtK"/>
                                </constraints>
                            </stackView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Zfk-NW-NBU"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="Gb0-Wf-QFL" firstAttribute="top" secondItem="Zfk-NW-NBU" secondAttribute="top" constant="8" id="1cL-Tg-mrm"/>
                            <constraint firstItem="Zfk-NW-NBU" firstAttribute="bottom" secondItem="hkd-pF-SaL" secondAttribute="bottom" constant="16" id="OKT-CO-OJc"/>
                            <constraint firstItem="Zfk-NW-NBU" firstAttribute="trailing" secondItem="hkd-pF-SaL" secondAttribute="trailing" constant="16" id="Tpu-H9-TpK"/>
                            <constraint firstItem="Gb0-Wf-QFL" firstAttribute="leading" secondItem="Zfk-NW-NBU" secondAttribute="leading" constant="16" id="i1H-Rq-jnc"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="mNj-CR-8RI"/>
                    <connections>
                        <outlet property="inputBtn" destination="RB9-Wj-I2v" id="faL-AV-Fn8"/>
                        <outlet property="lightBtn" destination="5WP-lt-QtG" id="euo-GJ-OUs"/>
                        <outlet property="locationBtn" destination="ugO-Ry-Pin" id="Qbu-XG-g87"/>
                        <outlet property="topOfCloseBtn" destination="1cL-Tg-mrm" id="4EJ-QX-0N1"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="gsN-QS-JRO" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-442.39999999999998" y="3994.4527736131936"/>
        </scene>
        <!--No Claim Work Flow Scan Free View Controller-->
        <scene sceneID="uEc-l5-1R3">
            <objects>
                <viewController storyboardIdentifier="NoClaimWorkFlowScanFreeViewController" hidesBottomBarWhenPushed="YES" id="h8v-nk-Iku" customClass="NoClaimWorkFlowScanFreeViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Zl7-nM-5EK">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3wn-Rc-I3p">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="AL3-Z2-hRJ" customClass="PreviewForWorkflow" customModule="App" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                    </view>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Np9-74-rbb">
                                        <rect key="frame" x="16" y="8" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="40" id="58g-fm-Il2"/>
                                            <constraint firstAttribute="height" constant="40" id="mFr-g8-Z8x"/>
                                        </constraints>
                                        <state key="normal" image="scan_close"/>
                                        <connections>
                                            <action selector="closeBtnAction:" destination="h8v-nk-Iku" eventType="touchUpInside" id="p4d-YR-6px"/>
                                        </connections>
                                    </button>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="pDh-SG-INn">
                                        <rect key="frame" x="311" y="475" width="48" height="176"/>
                                        <subviews>
                                            <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="pDo-GY-BCP">
                                                <rect key="frame" x="0.0" y="-48" width="48" height="48"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="48" id="gwL-Vh-Fgd"/>
                                                    <constraint firstAttribute="width" constant="48" id="hDR-68-F5D"/>
                                                </constraints>
                                                <state key="normal" image="scan_trim"/>
                                                <connections>
                                                    <action selector="onClickTrim:" destination="h8v-nk-Iku" eventType="touchUpInside" id="PGm-6g-Xcb"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="9gE-4t-Mmp">
                                                <rect key="frame" x="0.0" y="0.0" width="48" height="48"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="48" id="1aO-eJ-3bA"/>
                                                    <constraint firstAttribute="width" constant="48" id="VOJ-3d-bwB"/>
                                                </constraints>
                                                <state key="normal" image="scan_location"/>
                                                <connections>
                                                    <action selector="onClickLocation:" destination="g5F-EO-qcV" eventType="touchUpInside" id="RpT-6x-Sft"/>
                                                    <action selector="onClickLocation:" destination="h8v-nk-Iku" eventType="touchUpInside" id="uYC-Di-vVC"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ijh-Q1-TUa">
                                                <rect key="frame" x="0.0" y="64" width="48" height="48"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="48" id="AOn-EZ-ejH"/>
                                                    <constraint firstAttribute="height" constant="48" id="Ubh-5J-gQg"/>
                                                </constraints>
                                                <state key="normal" image="sacan_input"/>
                                                <connections>
                                                    <action selector="onClickInput:" destination="h8v-nk-Iku" eventType="touchUpInside" id="14U-sO-5dU"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="qKR-5K-bnp">
                                                <rect key="frame" x="0.0" y="128" width="48" height="48"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="48" id="45f-BC-33b"/>
                                                    <constraint firstAttribute="height" constant="48" id="Gdg-IX-gkM"/>
                                                </constraints>
                                                <state key="normal" image="light-bulb-off"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                        <real key="value" value="24"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="setLightOnOrOff:" destination="h8v-nk-Iku" eventType="touchUpInside" id="FhR-NL-QZ0"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="48" id="eKY-GU-nwR"/>
                                        </constraints>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="AL3-Z2-hRJ" firstAttribute="leading" secondItem="3wn-Rc-I3p" secondAttribute="leading" id="4LN-51-4xu"/>
                                    <constraint firstAttribute="bottom" secondItem="AL3-Z2-hRJ" secondAttribute="bottom" id="9Ex-dW-mbT"/>
                                    <constraint firstAttribute="bottom" secondItem="pDh-SG-INn" secondAttribute="bottom" constant="16" id="Rct-wu-kgd"/>
                                    <constraint firstItem="AL3-Z2-hRJ" firstAttribute="top" secondItem="3wn-Rc-I3p" secondAttribute="top" id="g0v-Ao-XW6"/>
                                    <constraint firstItem="Np9-74-rbb" firstAttribute="leading" secondItem="3wn-Rc-I3p" secondAttribute="leading" constant="16" id="i8h-1k-zEb"/>
                                    <constraint firstAttribute="trailing" secondItem="pDh-SG-INn" secondAttribute="trailing" constant="16" id="kpI-AW-76t"/>
                                    <constraint firstAttribute="trailing" secondItem="AL3-Z2-hRJ" secondAttribute="trailing" id="ppA-Y9-YWd"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="8Rv-kj-6eZ"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="3wn-Rc-I3p" firstAttribute="leading" secondItem="8Rv-kj-6eZ" secondAttribute="leading" id="2EM-Un-xwZ"/>
                            <constraint firstAttribute="bottom" secondItem="3wn-Rc-I3p" secondAttribute="bottom" id="7Bw-C8-FaD"/>
                            <constraint firstItem="3wn-Rc-I3p" firstAttribute="top" secondItem="Zl7-nM-5EK" secondAttribute="top" id="MVe-Ct-qBF"/>
                            <constraint firstItem="8Rv-kj-6eZ" firstAttribute="trailing" secondItem="3wn-Rc-I3p" secondAttribute="trailing" id="QUV-j4-dms"/>
                            <constraint firstItem="Np9-74-rbb" firstAttribute="top" secondItem="8Rv-kj-6eZ" secondAttribute="top" constant="8" id="tkm-bu-6PR"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="2OW-pV-fAm"/>
                    <connections>
                        <outlet property="inputBtn" destination="ijh-Q1-TUa" id="zDz-VN-heF"/>
                        <outlet property="lightBtn" destination="qKR-5K-bnp" id="WRn-3E-LhQ"/>
                        <outlet property="locationBtn" destination="9gE-4t-Mmp" id="jqs-Bz-hJx"/>
                        <outlet property="previewView" destination="3wn-Rc-I3p" id="9C1-6S-CN9"/>
                        <outlet property="previewWorkflow" destination="AL3-Z2-hRJ" id="Xbd-6V-Cgt"/>
                        <outlet property="topOfCloseBtn" destination="tkm-bu-6PR" id="N5k-Uz-tn0"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="J8M-a8-Q7J" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="344.80000000000001" y="3993.5532233883064"/>
        </scene>
        <!--Center Action Btn View Controller-->
        <scene sceneID="eje-yn-CUJ">
            <objects>
                <viewController storyboardIdentifier="CenterActionBtnViewController" id="kpW-1J-JZZ" customClass="CenterActionBtnViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="D2l-xx-Itx">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="demo" translatesAutoresizingMaskIntoConstraints="NO" id="0sU-tR-jCr">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bg" translatesAutoresizingMaskIntoConstraints="NO" id="m6w-8s-5MJ">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                            </imageView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Yqi-aN-4lr">
                                <rect key="frame" x="0.0" y="517" width="375" height="150"/>
                                <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="150" id="8cg-Gw-QIV"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="yRT-99-Kfn">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="517"/>
                                <subviews>
                                    <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="bky-jA-fH0">
                                        <rect key="frame" x="16" y="8" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="40" id="UyE-LP-abd"/>
                                            <constraint firstAttribute="height" constant="40" id="mYm-zH-MVD"/>
                                        </constraints>
                                        <state key="normal" image="scan_close"/>
                                        <connections>
                                            <action selector="closeBtnAction:" destination="kpW-1J-JZZ" eventType="touchUpInside" id="NjJ-uv-zmk"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="bky-jA-fH0" firstAttribute="leading" secondItem="yRT-99-Kfn" secondAttribute="leading" constant="16" id="kDI-OU-BOY"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="g7T-CO-BsX"/>
                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="m6w-8s-5MJ" firstAttribute="leading" secondItem="D2l-xx-Itx" secondAttribute="leading" id="1kN-rD-NYn"/>
                            <constraint firstItem="0sU-tR-jCr" firstAttribute="top" secondItem="D2l-xx-Itx" secondAttribute="top" id="4FJ-fl-tJN"/>
                            <constraint firstItem="0sU-tR-jCr" firstAttribute="leading" secondItem="D2l-xx-Itx" secondAttribute="leading" id="887-1L-gJM"/>
                            <constraint firstItem="g7T-CO-BsX" firstAttribute="trailing" secondItem="yRT-99-Kfn" secondAttribute="trailing" id="Gmm-vf-LGe"/>
                            <constraint firstItem="Yqi-aN-4lr" firstAttribute="top" secondItem="yRT-99-Kfn" secondAttribute="bottom" id="Grs-OE-dGz"/>
                            <constraint firstItem="yRT-99-Kfn" firstAttribute="leading" secondItem="g7T-CO-BsX" secondAttribute="leading" id="LKQ-86-Axl"/>
                            <constraint firstItem="0sU-tR-jCr" firstAttribute="bottom" secondItem="D2l-xx-Itx" secondAttribute="bottom" id="Qxb-Ry-1TT"/>
                            <constraint firstAttribute="trailing" secondItem="m6w-8s-5MJ" secondAttribute="trailing" id="Xkd-MT-l4i"/>
                            <constraint firstItem="bky-jA-fH0" firstAttribute="top" secondItem="g7T-CO-BsX" secondAttribute="top" constant="8" id="lGL-LT-fof"/>
                            <constraint firstItem="Yqi-aN-4lr" firstAttribute="leading" secondItem="g7T-CO-BsX" secondAttribute="leading" id="mzd-Ky-H9x"/>
                            <constraint firstAttribute="bottom" secondItem="m6w-8s-5MJ" secondAttribute="bottom" id="q1y-Rf-Dbm"/>
                            <constraint firstAttribute="bottom" secondItem="Yqi-aN-4lr" secondAttribute="bottom" id="vL7-qf-hjF"/>
                            <constraint firstItem="m6w-8s-5MJ" firstAttribute="top" secondItem="D2l-xx-Itx" secondAttribute="top" id="yBE-tJ-urG"/>
                            <constraint firstItem="0sU-tR-jCr" firstAttribute="trailing" secondItem="D2l-xx-Itx" secondAttribute="trailing" id="ymC-XV-RgS"/>
                            <constraint firstItem="g7T-CO-BsX" firstAttribute="trailing" secondItem="Yqi-aN-4lr" secondAttribute="trailing" id="yoV-PU-ehw"/>
                            <constraint firstItem="yRT-99-Kfn" firstAttribute="top" secondItem="D2l-xx-Itx" secondAttribute="top" id="z6e-Hs-7K4"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="backView" destination="yRT-99-Kfn" id="f6u-Y2-OeO"/>
                        <outlet property="bottomView" destination="Yqi-aN-4lr" id="ueg-IF-zGv"/>
                        <outlet property="bottomViewHeight" destination="8cg-Gw-QIV" id="994-kv-XAC"/>
                        <outlet property="closeBtn" destination="bky-jA-fH0" id="Z2r-wm-Os8"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="cW1-Fb-FST" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-455" y="354"/>
        </scene>
        <!--Center Work Flow Btn View Controller-->
        <scene sceneID="JxC-6X-0yR">
            <objects>
                <viewController storyboardIdentifier="CenterWorkFlowBtnViewController" id="guN-OS-5kh" customClass="CenterWorkFlowBtnViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="9xf-l7-zRz">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="demo" translatesAutoresizingMaskIntoConstraints="NO" id="Nhv-LD-8fq">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bg" translatesAutoresizingMaskIntoConstraints="NO" id="aGi-Hb-4iF">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                            </imageView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="A0B-Mp-5Ed">
                                <rect key="frame" x="0.0" y="517" width="375" height="150"/>
                                <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="150" id="9a7-9S-qcT"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="0Jj-vi-hVN">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="517"/>
                                <subviews>
                                    <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="VJf-0H-2cq">
                                        <rect key="frame" x="16" y="8" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="40" id="YLg-le-Khx"/>
                                            <constraint firstAttribute="height" constant="40" id="dQY-1f-wbf"/>
                                        </constraints>
                                        <state key="normal" image="scan_close"/>
                                        <connections>
                                            <action selector="closeBtnWorkFlow:" destination="guN-OS-5kh" eventType="touchUpInside" id="3iB-0E-XTL"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="VJf-0H-2cq" firstAttribute="leading" secondItem="0Jj-vi-hVN" secondAttribute="leading" constant="16" id="tBz-Am-Rru"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="eZi-HH-Ncf"/>
                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="Nhv-LD-8fq" firstAttribute="leading" secondItem="9xf-l7-zRz" secondAttribute="leading" id="205-gj-oOG"/>
                            <constraint firstItem="eZi-HH-Ncf" firstAttribute="trailing" secondItem="A0B-Mp-5Ed" secondAttribute="trailing" id="90Z-An-cwB"/>
                            <constraint firstItem="eZi-HH-Ncf" firstAttribute="trailing" secondItem="0Jj-vi-hVN" secondAttribute="trailing" id="GfM-mY-sl5"/>
                            <constraint firstItem="aGi-Hb-4iF" firstAttribute="trailing" secondItem="9xf-l7-zRz" secondAttribute="trailing" id="PeQ-Sh-Kmr"/>
                            <constraint firstItem="aGi-Hb-4iF" firstAttribute="bottom" secondItem="9xf-l7-zRz" secondAttribute="bottom" id="QIe-4d-hWC"/>
                            <constraint firstItem="A0B-Mp-5Ed" firstAttribute="top" secondItem="0Jj-vi-hVN" secondAttribute="bottom" id="QUe-E4-wmi"/>
                            <constraint firstItem="Nhv-LD-8fq" firstAttribute="top" secondItem="9xf-l7-zRz" secondAttribute="top" id="Xhh-mI-36J"/>
                            <constraint firstAttribute="bottom" secondItem="A0B-Mp-5Ed" secondAttribute="bottom" id="cK4-GQ-EdJ"/>
                            <constraint firstItem="VJf-0H-2cq" firstAttribute="top" secondItem="eZi-HH-Ncf" secondAttribute="top" constant="8" id="e0o-Wz-Q6y"/>
                            <constraint firstItem="Nhv-LD-8fq" firstAttribute="trailing" secondItem="9xf-l7-zRz" secondAttribute="trailing" id="fE4-am-K5a"/>
                            <constraint firstItem="0Jj-vi-hVN" firstAttribute="top" secondItem="9xf-l7-zRz" secondAttribute="top" id="gfw-NW-aF7"/>
                            <constraint firstItem="0Jj-vi-hVN" firstAttribute="leading" secondItem="eZi-HH-Ncf" secondAttribute="leading" id="hLl-VQ-21R"/>
                            <constraint firstItem="Nhv-LD-8fq" firstAttribute="bottom" secondItem="9xf-l7-zRz" secondAttribute="bottom" id="nyz-22-k1F"/>
                            <constraint firstItem="aGi-Hb-4iF" firstAttribute="leading" secondItem="9xf-l7-zRz" secondAttribute="leading" id="shd-9a-Ldn"/>
                            <constraint firstItem="A0B-Mp-5Ed" firstAttribute="leading" secondItem="eZi-HH-Ncf" secondAttribute="leading" id="wH6-L9-Hn4"/>
                            <constraint firstItem="aGi-Hb-4iF" firstAttribute="top" secondItem="9xf-l7-zRz" secondAttribute="top" id="x90-9Z-faz"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="backView" destination="0Jj-vi-hVN" id="OqE-IO-W6L"/>
                        <outlet property="bottomView" destination="A0B-Mp-5Ed" id="0F6-qr-NnN"/>
                        <outlet property="bottomViewHeight" destination="9a7-9S-qcT" id="Jxq-Ry-gCP"/>
                        <outlet property="closeBtn" destination="VJf-0H-2cq" id="4m7-3f-5V5"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="GPA-hE-5wb" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1161" y="1075"/>
        </scene>
        <!--Center No Subprocess Work Flow Btn View Controller-->
        <scene sceneID="ghF-ud-9a3">
            <objects>
                <viewController storyboardIdentifier="CenterNoSubprocessWorkFlowBtnViewController" id="2Jd-Iz-MYH" customClass="CenterNoSubprocessWorkFlowBtnViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="MF7-Sb-p9u">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="demo" translatesAutoresizingMaskIntoConstraints="NO" id="RkD-Fs-nno">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bg" translatesAutoresizingMaskIntoConstraints="NO" id="fEJ-nF-Hgc">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                            </imageView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ExG-sx-TDw">
                                <rect key="frame" x="0.0" y="517" width="375" height="150"/>
                                <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="150" id="ZC3-mh-RaX"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Lyq-cD-Uge">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="517"/>
                                <subviews>
                                    <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="I9P-Hi-9Da">
                                        <rect key="frame" x="16" y="8" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="40" id="MQk-3C-Om3"/>
                                            <constraint firstAttribute="width" constant="40" id="cPm-8L-dCp"/>
                                        </constraints>
                                        <state key="normal" image="scan_close"/>
                                        <connections>
                                            <action selector="closeBtnWorkFlow:" destination="2Jd-Iz-MYH" eventType="touchUpInside" id="y69-gb-sNc"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="I9P-Hi-9Da" firstAttribute="leading" secondItem="Lyq-cD-Uge" secondAttribute="leading" constant="16" id="r1j-dD-sdX"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="ZZa-uZ-sqz"/>
                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="I9P-Hi-9Da" firstAttribute="top" secondItem="ZZa-uZ-sqz" secondAttribute="top" constant="8" id="037-rw-8Dk"/>
                            <constraint firstItem="RkD-Fs-nno" firstAttribute="leading" secondItem="MF7-Sb-p9u" secondAttribute="leading" id="3VF-EA-Mqo"/>
                            <constraint firstAttribute="bottom" secondItem="ExG-sx-TDw" secondAttribute="bottom" id="CiP-FP-CMq"/>
                            <constraint firstItem="fEJ-nF-Hgc" firstAttribute="top" secondItem="MF7-Sb-p9u" secondAttribute="top" id="DiW-Rr-Ec5"/>
                            <constraint firstAttribute="bottom" secondItem="fEJ-nF-Hgc" secondAttribute="bottom" id="MoU-GS-Rpx"/>
                            <constraint firstItem="ZZa-uZ-sqz" firstAttribute="trailing" secondItem="ExG-sx-TDw" secondAttribute="trailing" id="PJH-eJ-WBk"/>
                            <constraint firstItem="RkD-Fs-nno" firstAttribute="trailing" secondItem="MF7-Sb-p9u" secondAttribute="trailing" id="UjD-4p-U2r"/>
                            <constraint firstItem="ZZa-uZ-sqz" firstAttribute="trailing" secondItem="Lyq-cD-Uge" secondAttribute="trailing" id="VSC-Wd-i1b"/>
                            <constraint firstItem="RkD-Fs-nno" firstAttribute="bottom" secondItem="MF7-Sb-p9u" secondAttribute="bottom" id="Yvs-RU-7ma"/>
                            <constraint firstAttribute="trailing" secondItem="fEJ-nF-Hgc" secondAttribute="trailing" id="bzv-Hf-fXE"/>
                            <constraint firstItem="ExG-sx-TDw" firstAttribute="top" secondItem="Lyq-cD-Uge" secondAttribute="bottom" id="gOP-QF-Pop"/>
                            <constraint firstItem="Lyq-cD-Uge" firstAttribute="leading" secondItem="ZZa-uZ-sqz" secondAttribute="leading" id="mGE-lv-MSw"/>
                            <constraint firstItem="fEJ-nF-Hgc" firstAttribute="leading" secondItem="MF7-Sb-p9u" secondAttribute="leading" id="nqG-Sn-Jvm"/>
                            <constraint firstItem="Lyq-cD-Uge" firstAttribute="top" secondItem="MF7-Sb-p9u" secondAttribute="top" id="p7I-Q8-ViT"/>
                            <constraint firstItem="RkD-Fs-nno" firstAttribute="top" secondItem="MF7-Sb-p9u" secondAttribute="top" id="t5q-H9-8JG"/>
                            <constraint firstItem="ExG-sx-TDw" firstAttribute="leading" secondItem="ZZa-uZ-sqz" secondAttribute="leading" id="zdI-Qm-2fx"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="backView" destination="Lyq-cD-Uge" id="BJs-E1-jb6"/>
                        <outlet property="bottomView" destination="ExG-sx-TDw" id="9Q0-F2-FeT"/>
                        <outlet property="bottomViewHeight" destination="ZC3-mh-RaX" id="n0l-Mi-7fE"/>
                        <outlet property="closeBtn" destination="I9P-Hi-9Da" id="gO1-G0-AUx"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="jYa-Sq-W29" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1241" y="3253"/>
        </scene>
        <!--Center Multiple Scans Work Flow Btn View Controller-->
        <scene sceneID="3Xv-rj-lRu">
            <objects>
                <viewController storyboardIdentifier="CenterMultipleScansWorkFlowBtnViewController" id="s9o-AF-bVl" customClass="CenterMultipleScansWorkFlowBtnViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="4o2-ff-NVu">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="demo" translatesAutoresizingMaskIntoConstraints="NO" id="LGb-10-rOy">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bg" translatesAutoresizingMaskIntoConstraints="NO" id="agh-vx-zqt">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                            </imageView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Au9-Rg-935">
                                <rect key="frame" x="0.0" y="517" width="375" height="150"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="vGr-Fv-i38">
                                        <rect key="frame" x="5" y="22.5" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="40" id="f4S-Rx-yvv"/>
                                            <constraint firstAttribute="width" constant="40" id="hPN-Go-haX"/>
                                        </constraints>
                                        <color key="tintColor" red="0.80784313730000001" green="0.34901960780000002" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                        <state key="normal" image="arrow_left"/>
                                        <connections>
                                            <action selector="leftBtnAction:" destination="s9o-AF-bVl" eventType="touchUpInside" id="KhV-m1-Xyp"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Xgj-ea-8Dz">
                                        <rect key="frame" x="330" y="22.5" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="40" id="MW2-6j-ldT"/>
                                            <constraint firstAttribute="width" constant="40" id="xcd-pU-pWM"/>
                                        </constraints>
                                        <color key="tintColor" systemColor="systemRedColor"/>
                                        <state key="normal" image="arrow_right"/>
                                        <connections>
                                            <action selector="rightBtnAction:" destination="s9o-AF-bVl" eventType="touchUpInside" id="DRs-CR-o2T"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="wordWrap" translatesAutoresizingMaskIntoConstraints="NO" id="hmK-MV-vEe">
                                        <rect key="frame" x="20" y="85" width="70" height="45"/>
                                        <color key="backgroundColor" red="0.043137254901960784" green="0.24313725490196078" blue="0.52549019607843139" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="45" id="AE0-Dp-6g1"/>
                                            <constraint firstAttribute="width" constant="70" id="bYz-b9-iSC"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <state key="normal">
                                            <string key="title">未完了
資産</string>
                                            <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        </state>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                <integer key="value" value="8"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="layer.clipsToBounds" value="YES"/>
                                            <userDefinedRuntimeAttribute type="number" keyPath="layer.shadowOpacity">
                                                <real key="value" value="0.90000000000000002"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="size" keyPath="layer.shadowOffset">
                                                <size key="value" width="3" height="3"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="number" keyPath="titleLabel.textAlignment">
                                                <integer key="value" value="1"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="number" keyPath="titleLabel.numberOfLines">
                                                <integer key="value" value="0"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="showWillScanAssetBtn:" destination="s9o-AF-bVl" eventType="touchUpInside" id="8Ua-W3-4Sr"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="hl5-6I-Uy5" customClass="FontsizeButton" customModule="App" customModuleProvider="target">
                                        <rect key="frame" x="100" y="85" width="255" height="45"/>
                                        <color key="backgroundColor" red="0.50196078431372548" green="0.50196078431372548" blue="0.50196078431372548" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="147.5" id="9zK-NX-jxx"/>
                                            <constraint firstAttribute="height" constant="45" id="wMF-vU-cwt"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                        <state key="normal" title="スキャンリストへ">
                                            <color key="titleColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        </state>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                <integer key="value" value="8"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="layer.clipsToBounds" value="YES"/>
                                            <userDefinedRuntimeAttribute type="number" keyPath="layer.shadowOpacity">
                                                <real key="value" value="0.90000000000000002"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="size" keyPath="layer.shadowOffset">
                                                <size key="value" width="3" height="3"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <variation key="default">
                                            <mask key="constraints">
                                                <exclude reference="9zK-NX-jxx"/>
                                            </mask>
                                        </variation>
                                        <connections>
                                            <action selector="showAssetDetailBtn:" destination="s9o-AF-bVl" eventType="touchUpInside" id="ISE-2V-58I"/>
                                        </connections>
                                    </button>
                                    <pickerView contentMode="scaleToFill" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Akg-tY-0xY" customClass="FontsizePickerView" customModule="App" customModuleProvider="target">
                                        <rect key="frame" x="152" y="-68" width="70" height="200"/>
                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMaxY="YES"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <accessibility key="accessibilityConfiguration">
                                            <accessibilityTraits key="traits" notEnabled="YES"/>
                                        </accessibility>
                                    </pickerView>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="bottom" secondItem="hl5-6I-Uy5" secondAttribute="bottom" constant="25" id="1I6-OP-TTn"/>
                                    <constraint firstItem="hmK-MV-vEe" firstAttribute="top" secondItem="Au9-Rg-935" secondAttribute="top" constant="85" id="8XA-PP-nL3"/>
                                    <constraint firstAttribute="trailing" secondItem="Xgj-ea-8Dz" secondAttribute="trailing" constant="5" id="8ro-sz-SHd"/>
                                    <constraint firstAttribute="height" constant="150" id="NqB-Jb-gcb"/>
                                    <constraint firstItem="Xgj-ea-8Dz" firstAttribute="centerY" secondItem="vGr-Fv-i38" secondAttribute="centerY" id="Oyq-Il-pCL"/>
                                    <constraint firstItem="hmK-MV-vEe" firstAttribute="leading" secondItem="Au9-Rg-935" secondAttribute="leading" constant="20" id="g6J-QR-jWM"/>
                                    <constraint firstItem="hl5-6I-Uy5" firstAttribute="leading" secondItem="hmK-MV-vEe" secondAttribute="trailing" constant="10" id="hbX-TE-stu"/>
                                    <constraint firstAttribute="trailing" secondItem="hl5-6I-Uy5" secondAttribute="trailing" constant="20" id="i72-db-tu7"/>
                                    <constraint firstItem="vGr-Fv-i38" firstAttribute="top" secondItem="Au9-Rg-935" secondAttribute="top" constant="22.5" id="iTA-5x-LWT"/>
                                    <constraint firstItem="hl5-6I-Uy5" firstAttribute="top" secondItem="Au9-Rg-935" secondAttribute="top" constant="85" id="kod-sZ-udV"/>
                                    <constraint firstAttribute="bottom" secondItem="hmK-MV-vEe" secondAttribute="bottom" constant="25" id="kuW-Tv-XBx"/>
                                    <constraint firstItem="Xgj-ea-8Dz" firstAttribute="top" secondItem="Au9-Rg-935" secondAttribute="top" constant="22.5" id="uPH-K0-g6W"/>
                                    <constraint firstItem="vGr-Fv-i38" firstAttribute="leading" secondItem="Au9-Rg-935" secondAttribute="leading" constant="5" id="yI8-N8-bJp"/>
                                </constraints>
                                <variation key="default">
                                    <mask key="constraints">
                                        <exclude reference="kuW-Tv-XBx"/>
                                        <exclude reference="1I6-OP-TTn"/>
                                        <exclude reference="Oyq-Il-pCL"/>
                                    </mask>
                                </variation>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Bvm-yT-4Hh">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="517"/>
                                <subviews>
                                    <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Dtp-eV-Pbz">
                                        <rect key="frame" x="16" y="8" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="40" id="Hnq-HD-Q95"/>
                                            <constraint firstAttribute="height" constant="40" id="fHh-GE-nOn"/>
                                        </constraints>
                                        <state key="normal" image="scan_close"/>
                                        <connections>
                                            <action selector="closeBtnWorkFlow:" destination="s9o-AF-bVl" eventType="touchUpInside" id="COj-fN-AbD"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="Dtp-eV-Pbz" firstAttribute="leading" secondItem="Bvm-yT-4Hh" secondAttribute="leading" constant="16" id="QjK-L8-lTh"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="3vD-X4-sVD"/>
                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="LGb-10-rOy" firstAttribute="top" secondItem="4o2-ff-NVu" secondAttribute="top" id="0EW-XX-bZD"/>
                            <constraint firstItem="Dtp-eV-Pbz" firstAttribute="top" secondItem="3vD-X4-sVD" secondAttribute="top" constant="8" id="2Ju-Qp-Eib"/>
                            <constraint firstAttribute="bottom" secondItem="agh-vx-zqt" secondAttribute="bottom" id="9f2-74-42X"/>
                            <constraint firstItem="agh-vx-zqt" firstAttribute="leading" secondItem="4o2-ff-NVu" secondAttribute="leading" id="A0c-Ax-C56"/>
                            <constraint firstItem="Au9-Rg-935" firstAttribute="leading" secondItem="3vD-X4-sVD" secondAttribute="leading" id="Bmi-hw-Me9"/>
                            <constraint firstItem="Bvm-yT-4Hh" firstAttribute="leading" secondItem="3vD-X4-sVD" secondAttribute="leading" id="CYD-gk-Ui1"/>
                            <constraint firstAttribute="bottom" secondItem="Au9-Rg-935" secondAttribute="bottom" id="GUR-vM-lh4"/>
                            <constraint firstItem="Bvm-yT-4Hh" firstAttribute="top" secondItem="4o2-ff-NVu" secondAttribute="top" id="VBK-iA-WSB"/>
                            <constraint firstItem="agh-vx-zqt" firstAttribute="top" secondItem="4o2-ff-NVu" secondAttribute="top" id="VWn-gk-sEc"/>
                            <constraint firstItem="LGb-10-rOy" firstAttribute="bottom" secondItem="4o2-ff-NVu" secondAttribute="bottom" id="ZCo-ee-wrt"/>
                            <constraint firstItem="Au9-Rg-935" firstAttribute="top" secondItem="Bvm-yT-4Hh" secondAttribute="bottom" id="ZrG-lY-ZOa"/>
                            <constraint firstItem="3vD-X4-sVD" firstAttribute="trailing" secondItem="Au9-Rg-935" secondAttribute="trailing" id="coX-8E-2RD"/>
                            <constraint firstAttribute="trailing" secondItem="agh-vx-zqt" secondAttribute="trailing" id="dNH-M6-l1O"/>
                            <constraint firstItem="3vD-X4-sVD" firstAttribute="trailing" secondItem="Bvm-yT-4Hh" secondAttribute="trailing" id="n2N-eh-9bQ"/>
                            <constraint firstItem="LGb-10-rOy" firstAttribute="leading" secondItem="4o2-ff-NVu" secondAttribute="leading" id="qsh-C0-Agl"/>
                            <constraint firstItem="LGb-10-rOy" firstAttribute="trailing" secondItem="4o2-ff-NVu" secondAttribute="trailing" id="xgj-kl-GNk"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="backView" destination="Bvm-yT-4Hh" id="d7f-3b-DiN"/>
                        <outlet property="bottomView" destination="Au9-Rg-935" id="zQs-pm-DPb"/>
                        <outlet property="bottomViewHeight" destination="NqB-Jb-gcb" id="Ibz-Fq-Io3"/>
                        <outlet property="closeBtn" destination="Dtp-eV-Pbz" id="glm-4y-9tT"/>
                        <outlet property="leftBtn" destination="vGr-Fv-i38" id="vgV-vO-HNx"/>
                        <outlet property="leftButtonHeight" destination="f4S-Rx-yvv" id="Emq-hJ-vfQ"/>
                        <outlet property="listBtn" destination="hl5-6I-Uy5" id="opU-Jk-dqm"/>
                        <outlet property="listBtnTop" destination="kod-sZ-udV" id="eIt-S6-lco"/>
                        <outlet property="pickerView" destination="Akg-tY-0xY" id="dVw-rC-3nW"/>
                        <outlet property="rightBtn" destination="Xgj-ea-8Dz" id="MLs-pl-GtM"/>
                        <outlet property="rightBtnHeight" destination="MW2-6j-ldT" id="0ck-NU-Hte"/>
                        <outlet property="uncompleteBtn" destination="hmK-MV-vEe" id="97y-ZS-gWT"/>
                        <outlet property="uncompleteBtnTop" destination="8XA-PP-nL3" id="o24-jy-hKN"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="fWo-x2-wpz" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1242.4000000000001" y="3993.5532233883064"/>
        </scene>
        <!--Code Scan Free View Controller-->
        <scene sceneID="Kma-04-mEa">
            <objects>
                <viewController storyboardIdentifier="QRCodeScanFreeViewController" id="Ckz-Kb-VMf" customClass="QRCodeScanFreeViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="xK2-VL-NyG">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="fOg-Di-Mmg">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="eE6-r1-v5n" customClass="PreviewView">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                    </view>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="YrQ-AZ-30O" userLabel="close">
                                        <rect key="frame" x="16" y="8" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="40" id="YwC-r7-lIS"/>
                                            <constraint firstAttribute="width" constant="40" id="pXH-Fz-7qe"/>
                                        </constraints>
                                        <state key="normal" image="scan_close"/>
                                        <connections>
                                            <action selector="closeWindow:" destination="Ckz-Kb-VMf" eventType="touchUpInside" id="NO7-fO-b0h"/>
                                        </connections>
                                    </button>
                                    <view alpha="0.5" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="FqB-mt-eRq">
                                        <rect key="frame" x="0.0" y="567" width="375" height="100"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="100" id="290-4v-c93"/>
                                            <constraint firstAttribute="height" constant="100" id="pfi-im-yqx"/>
                                        </constraints>
                                        <variation key="default">
                                            <mask key="constraints">
                                                <exclude reference="290-4v-c93"/>
                                            </mask>
                                        </variation>
                                    </view>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="V4f-SV-UE2">
                                        <rect key="frame" x="20" y="608.5" width="168" height="43.5"/>
                                        <string key="text">処理設定のQRコードを
スキャンしてください</string>
                                        <fontDescription key="fontDescription" name="NotoSansKannada-Bold" family="Noto Sans Kannada" pointSize="16"/>
                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="bottom" spacing="70" translatesAutoresizingMaskIntoConstraints="NO" id="XWm-T5-2qP">
                                        <rect key="frame" x="311" y="651" width="48" height="0.0"/>
                                        <subviews>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="UeL-UD-Jns">
                                                <rect key="frame" x="48" y="0.0" width="0.0" height="0.0"/>
                                                <subviews>
                                                    <button hidden="YES" opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="bYH-Bb-nUS" userLabel="light">
                                                        <rect key="frame" x="0.0" y="0.0" width="48" height="48"/>
                                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="48" id="KO3-Fc-0Jb"/>
                                                            <constraint firstAttribute="height" constant="48" id="LJz-kn-lTa"/>
                                                        </constraints>
                                                        <state key="normal" image="light-bulb-off"/>
                                                        <state key="selected" image="light-bulb-on"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                <real key="value" value="24"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                        <connections>
                                                            <action selector="lightOnOrOff:" destination="Ckz-Kb-VMf" eventType="touchUpInside" id="Epa-hi-Hgo"/>
                                                        </connections>
                                                    </button>
                                                </subviews>
                                            </stackView>
                                            <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="npw-Oo-Kgr">
                                                <rect key="frame" x="0.0" y="0.0" width="48" height="40"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="40" id="Rvg-oK-y6D"/>
                                                    <constraint firstAttribute="width" constant="48" id="nBJ-gO-JTD"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="boldSystem" pointSize="15"/>
                                                <state key="normal" title="スキャンリストへ">
                                                    <color key="titleColor" red="0.18119523700000001" green="0.27625621109999998" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                </state>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                        <integer key="value" value="5"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="boolean" keyPath="layer.clipsToBounds" value="YES"/>
                                                </userDefinedRuntimeAttributes>
                                            </button>
                                        </subviews>
                                        <constraints>
                                            <constraint firstItem="npw-Oo-Kgr" firstAttribute="leading" secondItem="XWm-T5-2qP" secondAttribute="leading" id="wcQ-Uw-knp"/>
                                            <constraint firstAttribute="trailing" secondItem="npw-Oo-Kgr" secondAttribute="trailing" id="xfd-j9-ZiW"/>
                                        </constraints>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" white="0.66666666669999997" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="eE6-r1-v5n" firstAttribute="leading" secondItem="fOg-Di-Mmg" secondAttribute="leading" id="Dd9-PO-5Eu"/>
                                    <constraint firstItem="YrQ-AZ-30O" firstAttribute="leading" secondItem="fOg-Di-Mmg" secondAttribute="leading" constant="16" id="R47-fj-qhm"/>
                                    <constraint firstItem="eE6-r1-v5n" firstAttribute="top" secondItem="fOg-Di-Mmg" secondAttribute="top" id="Wts-Hm-aGW"/>
                                    <constraint firstAttribute="trailing" secondItem="eE6-r1-v5n" secondAttribute="trailing" id="dHx-pv-CCS"/>
                                    <constraint firstItem="V4f-SV-UE2" firstAttribute="leading" secondItem="fOg-Di-Mmg" secondAttribute="leading" constant="20" id="eRL-Dn-Tbz"/>
                                    <constraint firstAttribute="bottom" secondItem="eE6-r1-v5n" secondAttribute="bottom" id="vfZ-PK-yBP"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="T9L-sh-SeQ"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="fOg-Di-Mmg" firstAttribute="bottom" secondItem="XWm-T5-2qP" secondAttribute="bottom" constant="16" id="B58-ww-aYI"/>
                            <constraint firstAttribute="bottom" secondItem="fOg-Di-Mmg" secondAttribute="bottom" id="Dad-gy-Qjq"/>
                            <constraint firstItem="YrQ-AZ-30O" firstAttribute="top" secondItem="T9L-sh-SeQ" secondAttribute="top" constant="8" id="LOD-mt-fZS"/>
                            <constraint firstItem="FqB-mt-eRq" firstAttribute="leading" secondItem="fOg-Di-Mmg" secondAttribute="leading" id="MvM-ju-8vD"/>
                            <constraint firstItem="fOg-Di-Mmg" firstAttribute="top" secondItem="xK2-VL-NyG" secondAttribute="top" id="NcA-8c-YOs"/>
                            <constraint firstItem="fOg-Di-Mmg" firstAttribute="bottom" secondItem="FqB-mt-eRq" secondAttribute="bottom" id="YpV-48-VBY"/>
                            <constraint firstItem="fOg-Di-Mmg" firstAttribute="bottom" secondItem="V4f-SV-UE2" secondAttribute="bottom" constant="15" id="b98-3G-haE"/>
                            <constraint firstItem="T9L-sh-SeQ" firstAttribute="trailing" secondItem="XWm-T5-2qP" secondAttribute="trailing" constant="16" id="c0Y-h7-aXP"/>
                            <constraint firstItem="FqB-mt-eRq" firstAttribute="height" secondItem="T9L-sh-SeQ" secondAttribute="height" constant="100" id="n2r-o8-jpL"/>
                            <constraint firstItem="fOg-Di-Mmg" firstAttribute="trailing" secondItem="FqB-mt-eRq" secondAttribute="trailing" id="rCh-ne-Ach"/>
                            <constraint firstItem="T9L-sh-SeQ" firstAttribute="trailing" secondItem="fOg-Di-Mmg" secondAttribute="trailing" id="uI5-c3-v4o"/>
                            <constraint firstItem="fOg-Di-Mmg" firstAttribute="leading" secondItem="T9L-sh-SeQ" secondAttribute="leading" id="veT-XN-GLF"/>
                        </constraints>
                        <variation key="default">
                            <mask key="constraints">
                                <exclude reference="n2r-o8-jpL"/>
                            </mask>
                        </variation>
                    </view>
                    <connections>
                        <outlet property="gradientBgView" destination="FqB-mt-eRq" id="Wbp-Cv-c06"/>
                        <outlet property="ligntBtn" destination="bYH-Bb-nUS" id="XfG-wG-tUO"/>
                        <outlet property="previewAsset" destination="eE6-r1-v5n" id="wFy-F9-ny2"/>
                        <outlet property="previewView" destination="fOg-Di-Mmg" id="5lk-bU-DEw"/>
                        <outlet property="processingSettingsTextLabel" destination="V4f-SV-UE2" id="7Kj-tp-NOZ"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Peg-D6-xqG" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-503.19999999999999" y="-1740.1799100449775"/>
        </scene>
        <!--CenterRFID Btn View Controller-->
        <scene sceneID="HH3-8Q-oSb">
            <objects>
                <viewController storyboardIdentifier="CenterRFIDBtnViewController" id="fcJ-rJ-03d" customClass="CenterRFIDBtnViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="0Ib-5G-Wv1">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="demo" translatesAutoresizingMaskIntoConstraints="NO" id="oNq-0x-oKP">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                            </imageView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="aaY-pv-juL">
                                <rect key="frame" x="0.0" y="517" width="375" height="150"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ytg-qx-e4T" customClass="FontsizeButton" customModule="App" customModuleProvider="target">
                                        <rect key="frame" x="20" y="85" width="335" height="44"/>
                                        <color key="backgroundColor" white="0.17709609339999999" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="44" id="Q9C-0b-YGf"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                        <state key="normal" title="  スキャンリストへ  ">
                                            <color key="titleColor" red="0.46666666670000001" green="0.46666666670000001" blue="0.46666666670000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        </state>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                <integer key="value" value="8"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="layer.clipsToBounds" value="YES"/>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="listBtnAction:" destination="fcJ-rJ-03d" eventType="touchUpInside" id="Hhw-dD-RrK"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="bottom" secondItem="ytg-qx-e4T" secondAttribute="bottom" constant="32" id="Bjq-fF-0gB"/>
                                    <constraint firstItem="ytg-qx-e4T" firstAttribute="leading" secondItem="aaY-pv-juL" secondAttribute="leading" constant="20" id="JFz-A2-2ge"/>
                                    <constraint firstItem="ytg-qx-e4T" firstAttribute="top" secondItem="aaY-pv-juL" secondAttribute="top" constant="85" id="Z4n-9B-iU5"/>
                                    <constraint firstAttribute="trailing" secondItem="ytg-qx-e4T" secondAttribute="trailing" constant="20" id="ebj-zV-t7G"/>
                                    <constraint firstAttribute="height" constant="150" id="xrc-eg-ebo"/>
                                </constraints>
                                <variation key="default">
                                    <mask key="constraints">
                                        <exclude reference="Bjq-fF-0gB"/>
                                    </mask>
                                </variation>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8r2-Hf-TD0">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="517"/>
                                <subviews>
                                    <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="pr1-58-gpm">
                                        <rect key="frame" x="16" y="8" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="40" id="EWq-Io-Jci"/>
                                            <constraint firstAttribute="width" constant="40" id="YcQ-0S-eDJ"/>
                                        </constraints>
                                        <state key="normal" image="scan_close"/>
                                        <connections>
                                            <action selector="closeBtnAction:" destination="fcJ-rJ-03d" eventType="touchUpInside" id="sVF-7i-as3"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" white="0.66666666669999997" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="pr1-58-gpm" firstAttribute="leading" secondItem="8r2-Hf-TD0" secondAttribute="leading" constant="16" id="w3B-ro-4hp"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="XkC-sJ-ucF"/>
                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="oNq-0x-oKP" firstAttribute="leading" secondItem="0Ib-5G-Wv1" secondAttribute="leading" id="3Yh-FA-wpI"/>
                            <constraint firstItem="8r2-Hf-TD0" firstAttribute="top" secondItem="0Ib-5G-Wv1" secondAttribute="top" id="DV2-Pi-ar3"/>
                            <constraint firstAttribute="bottom" secondItem="aaY-pv-juL" secondAttribute="bottom" id="ILP-y0-81E"/>
                            <constraint firstItem="oNq-0x-oKP" firstAttribute="trailing" secondItem="0Ib-5G-Wv1" secondAttribute="trailing" id="LCC-fQ-r7r"/>
                            <constraint firstItem="aaY-pv-juL" firstAttribute="top" secondItem="8r2-Hf-TD0" secondAttribute="bottom" id="Nf9-dd-v2x"/>
                            <constraint firstItem="XkC-sJ-ucF" firstAttribute="trailing" secondItem="8r2-Hf-TD0" secondAttribute="trailing" id="RDx-YH-glI"/>
                            <constraint firstItem="8r2-Hf-TD0" firstAttribute="leading" secondItem="XkC-sJ-ucF" secondAttribute="leading" id="bo1-vu-306"/>
                            <constraint firstItem="oNq-0x-oKP" firstAttribute="bottom" secondItem="0Ib-5G-Wv1" secondAttribute="bottom" id="bru-Ea-VRr"/>
                            <constraint firstItem="pr1-58-gpm" firstAttribute="top" secondItem="XkC-sJ-ucF" secondAttribute="top" constant="8" id="ecH-hg-Vbi"/>
                            <constraint firstItem="aaY-pv-juL" firstAttribute="leading" secondItem="XkC-sJ-ucF" secondAttribute="leading" id="gCN-aN-ulR"/>
                            <constraint firstItem="oNq-0x-oKP" firstAttribute="top" secondItem="0Ib-5G-Wv1" secondAttribute="top" id="map-xO-7yX"/>
                            <constraint firstItem="XkC-sJ-ucF" firstAttribute="trailing" secondItem="aaY-pv-juL" secondAttribute="trailing" id="xNl-Dd-CHW"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="backView" destination="8r2-Hf-TD0" id="IiV-ba-K8l"/>
                        <outlet property="bottomView" destination="aaY-pv-juL" id="Vrr-hE-ybM"/>
                        <outlet property="closeBtn" destination="pr1-58-gpm" id="BwI-b0-iwM"/>
                        <outlet property="listBtn" destination="ytg-qx-e4T" id="fiE-v4-tcx"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="HaQ-Qf-SKn" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-442.39999999999998" y="-1036.7316341829087"/>
        </scene>
        <!--Work Flow Scan View Controller-->
        <scene sceneID="ohh-or-JlL">
            <objects>
                <viewController storyboardIdentifier="WorkFlowScanViewController" id="rOQ-cY-WIb" customClass="WorkFlowScanViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Uu4-po-247">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="RMQ-5t-hjP">
                                <rect key="frame" x="311" y="475" width="48" height="176"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="RKb-EO-H92">
                                        <rect key="frame" x="0.0" y="0.0" width="48" height="48"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="48" id="GKa-rC-XJz"/>
                                            <constraint firstAttribute="width" constant="48" id="S2P-UK-MJY"/>
                                        </constraints>
                                        <state key="normal" image="scan_location"/>
                                        <connections>
                                            <action selector="locationSetting:" destination="rOQ-cY-WIb" eventType="touchUpInside" id="aZB-P1-nFL"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="YPN-Wq-NPp">
                                        <rect key="frame" x="0.0" y="64" width="48" height="48"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="48" id="8CN-KW-RhJ"/>
                                            <constraint firstAttribute="height" constant="48" id="Dyg-X0-hnX"/>
                                        </constraints>
                                        <state key="normal" image="sacan_input"/>
                                        <connections>
                                            <action selector="editByManualBtnAction:" destination="rOQ-cY-WIb" eventType="touchUpInside" id="dzB-Pv-Pgk"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="haj-kP-UlF">
                                        <rect key="frame" x="0.0" y="128" width="48" height="48"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="48" id="3cz-dQ-tO3"/>
                                            <constraint firstAttribute="width" constant="48" id="QYw-va-PJR"/>
                                        </constraints>
                                        <state key="normal" image="light-bulb-off"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                <real key="value" value="24"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="setLightOnOrOff:" destination="rOQ-cY-WIb" eventType="touchUpInside" id="XWl-0z-6ko"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="width" constant="48" id="FDe-3U-jya"/>
                                </constraints>
                            </stackView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="54N-Z7-4js">
                                <rect key="frame" x="16" y="8" width="40" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="40" id="KiG-3g-sW6"/>
                                    <constraint firstAttribute="height" constant="40" id="qs9-PW-il4"/>
                                </constraints>
                                <state key="normal" image="scan_close"/>
                                <connections>
                                    <action selector="closeBtnAction:" destination="rOQ-cY-WIb" eventType="touchUpInside" id="p4B-vp-vwt"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Z2r-yd-vS7"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="Z2r-yd-vS7" firstAttribute="trailing" secondItem="RMQ-5t-hjP" secondAttribute="trailing" constant="16" id="0wo-Eb-v55"/>
                            <constraint firstItem="54N-Z7-4js" firstAttribute="leading" secondItem="Z2r-yd-vS7" secondAttribute="leading" constant="16" id="dR5-QW-8vm"/>
                            <constraint firstItem="54N-Z7-4js" firstAttribute="top" secondItem="Z2r-yd-vS7" secondAttribute="top" constant="8" id="fDd-qc-A2a"/>
                            <constraint firstItem="Z2r-yd-vS7" firstAttribute="bottom" secondItem="RMQ-5t-hjP" secondAttribute="bottom" constant="16" id="j0X-fw-RhQ"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="clostBtn" destination="54N-Z7-4js" id="Bnu-vb-nLy"/>
                        <outlet property="editBtn" destination="YPN-Wq-NPp" id="p2N-uS-KLf"/>
                        <outlet property="lightBtn" destination="haj-kP-UlF" id="xcN-2F-rEj"/>
                        <outlet property="locationBtn" destination="RKb-EO-H92" id="C9P-EY-tE4"/>
                        <outlet property="topOfCloseBtn" destination="fDd-qc-A2a" id="hsy-OV-Mgn"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="MWf-IG-ob5" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-455.19999999999999" y="1078.1109445277361"/>
        </scene>
        <!--New Work Flow Scan Free View Controller-->
        <scene sceneID="bAn-bn-kAi">
            <objects>
                <viewController storyboardIdentifier="NewWorkFlowScanFreeViewController" id="Sbv-Km-gUe" customClass="NewWorkFlowScanFreeViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="pg1-Lt-mOF">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="uEm-z0-gif">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="dwh-SH-sYl" customClass="PreviewViewForAction" customModule="App" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                    </view>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="YD4-6j-Bhm">
                                        <rect key="frame" x="311" y="475" width="48" height="176"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="59j-Sc-AX1">
                                                <rect key="frame" x="0.0" y="0.0" width="48" height="48"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="48" id="MHs-oK-9RC"/>
                                                    <constraint firstAttribute="height" constant="48" id="Yk3-So-Tmt"/>
                                                </constraints>
                                                <state key="normal" image="scan_location"/>
                                                <connections>
                                                    <action selector="locationSetting:" destination="Sbv-Km-gUe" eventType="touchUpInside" id="dXg-WF-VXx"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="5zp-Co-dKB">
                                                <rect key="frame" x="0.0" y="64" width="48" height="48"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="48" id="duL-1L-6f6"/>
                                                    <constraint firstAttribute="height" constant="48" id="e26-mZ-yfG"/>
                                                </constraints>
                                                <state key="normal" image="sacan_input"/>
                                                <connections>
                                                    <action selector="editByManualBtnAution:" destination="Sbv-Km-gUe" eventType="touchUpInside" id="kX4-eO-xtv"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="LSZ-3f-SE1">
                                                <rect key="frame" x="0.0" y="128" width="48" height="48"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="48" id="9Nj-NN-Ze4"/>
                                                    <constraint firstAttribute="width" constant="48" id="cZS-mx-viW"/>
                                                </constraints>
                                                <state key="normal" image="light-bulb-off"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                        <real key="value" value="24"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="setLightOnOrOff:" destination="Tvd-Xa-vqZ" eventType="touchUpInside" id="O1v-lv-i1H"/>
                                                    <action selector="setLightOnOrOff:" destination="Sbv-Km-gUe" eventType="touchUpInside" id="vmp-De-N7X"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="48" id="xld-nR-5ex"/>
                                        </constraints>
                                    </stackView>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="FRi-pV-MA8">
                                        <rect key="frame" x="16" y="8" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="40" id="Ra2-HG-FfC"/>
                                            <constraint firstAttribute="width" constant="40" id="lru-0n-s6Y"/>
                                        </constraints>
                                        <state key="normal" image="scan_close"/>
                                        <connections>
                                            <action selector="closeBtnAction:" destination="Sbv-Km-gUe" eventType="touchUpInside" id="Ufd-Ys-OFr"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" white="0.66666666669999997" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="FRi-pV-MA8" firstAttribute="leading" secondItem="uEm-z0-gif" secondAttribute="leading" constant="16" id="80h-WT-QhI"/>
                                    <constraint firstAttribute="bottom" secondItem="YD4-6j-Bhm" secondAttribute="bottom" constant="16" id="IOE-cX-oUR"/>
                                    <constraint firstItem="dwh-SH-sYl" firstAttribute="leading" secondItem="uEm-z0-gif" secondAttribute="leading" id="j6T-BY-xBW"/>
                                    <constraint firstAttribute="bottom" secondItem="dwh-SH-sYl" secondAttribute="bottom" id="l8i-Tc-8rl"/>
                                    <constraint firstAttribute="trailing" secondItem="YD4-6j-Bhm" secondAttribute="trailing" constant="16" id="lWN-Q1-sxD"/>
                                    <constraint firstAttribute="trailing" secondItem="dwh-SH-sYl" secondAttribute="trailing" id="s91-tD-O76"/>
                                    <constraint firstItem="dwh-SH-sYl" firstAttribute="top" secondItem="uEm-z0-gif" secondAttribute="top" id="wQJ-O7-rki"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="M52-zE-JBg"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="FRi-pV-MA8" firstAttribute="top" secondItem="M52-zE-JBg" secondAttribute="top" constant="8" id="42G-ia-HjO"/>
                            <constraint firstItem="uEm-z0-gif" firstAttribute="top" secondItem="pg1-Lt-mOF" secondAttribute="top" id="44f-Ci-Bp8"/>
                            <constraint firstItem="M52-zE-JBg" firstAttribute="trailing" secondItem="uEm-z0-gif" secondAttribute="trailing" id="4Tf-ay-swR"/>
                            <constraint firstAttribute="bottom" secondItem="uEm-z0-gif" secondAttribute="bottom" id="Bx9-HY-vV9"/>
                            <constraint firstItem="uEm-z0-gif" firstAttribute="leading" secondItem="M52-zE-JBg" secondAttribute="leading" id="apj-YQ-1O3"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="lightBtn" destination="LSZ-3f-SE1" id="P6c-8P-uh8"/>
                        <outlet property="locationBtn" destination="59j-Sc-AX1" id="bSW-Aj-uAI"/>
                        <outlet property="previewAction" destination="dwh-SH-sYl" id="aFp-V9-dFm"/>
                        <outlet property="previewView" destination="uEm-z0-gif" id="u02-lG-z7R"/>
                        <outlet property="topOfCloseBtn" destination="42G-ia-HjO" id="OeW-Kt-uIW"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="svu-hV-poT" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1964" y="1075"/>
        </scene>
        <!--Center New Work Flow Btn View Controller-->
        <scene sceneID="B2V-dT-VQV">
            <objects>
                <viewController storyboardIdentifier="CenterNewWorkFlowBtnViewController" id="XeQ-ni-35n" customClass="CenterNewWorkFlowBtnViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="FwO-72-OK9">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="demo" translatesAutoresizingMaskIntoConstraints="NO" id="01R-Cn-7LB">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                            </imageView>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bg" translatesAutoresizingMaskIntoConstraints="NO" id="d7N-DP-tHR">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                            </imageView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8LJ-xf-Ps9">
                                <rect key="frame" x="0.0" y="517" width="375" height="150"/>
                                <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="150" id="kVT-mX-O8v"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="dv3-As-f0G">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="517"/>
                                <subviews>
                                    <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="xHO-6k-baW">
                                        <rect key="frame" x="16" y="8" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="40" id="1OJ-bC-1pa"/>
                                            <constraint firstAttribute="height" constant="40" id="QHI-gb-1YV"/>
                                        </constraints>
                                        <state key="normal" image="scan_close"/>
                                        <connections>
                                            <action selector="closeBtnWorkFlow:" destination="XeQ-ni-35n" eventType="touchUpInside" id="mcE-Uo-HCN"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="xHO-6k-baW" firstAttribute="leading" secondItem="dv3-As-f0G" secondAttribute="leading" constant="16" id="vxz-Ta-Nbd"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="nJo-bA-ukg"/>
                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="01R-Cn-7LB" firstAttribute="bottom" secondItem="FwO-72-OK9" secondAttribute="bottom" id="71J-4U-GLR"/>
                            <constraint firstItem="xHO-6k-baW" firstAttribute="top" secondItem="nJo-bA-ukg" secondAttribute="top" constant="8" id="Kpj-gw-IuO"/>
                            <constraint firstItem="d7N-DP-tHR" firstAttribute="trailing" secondItem="FwO-72-OK9" secondAttribute="trailing" id="NLI-pT-az6"/>
                            <constraint firstAttribute="bottom" secondItem="8LJ-xf-Ps9" secondAttribute="bottom" id="Op0-k7-PkI"/>
                            <constraint firstItem="nJo-bA-ukg" firstAttribute="trailing" secondItem="8LJ-xf-Ps9" secondAttribute="trailing" id="Q9D-5F-wvb"/>
                            <constraint firstItem="01R-Cn-7LB" firstAttribute="trailing" secondItem="FwO-72-OK9" secondAttribute="trailing" id="Th9-rI-jAy"/>
                            <constraint firstItem="8LJ-xf-Ps9" firstAttribute="top" secondItem="dv3-As-f0G" secondAttribute="bottom" id="Wjm-rS-E5D"/>
                            <constraint firstItem="8LJ-xf-Ps9" firstAttribute="leading" secondItem="nJo-bA-ukg" secondAttribute="leading" id="aUk-pe-OfG"/>
                            <constraint firstItem="01R-Cn-7LB" firstAttribute="top" secondItem="FwO-72-OK9" secondAttribute="top" id="dd1-NQ-i8J"/>
                            <constraint firstItem="d7N-DP-tHR" firstAttribute="bottom" secondItem="FwO-72-OK9" secondAttribute="bottom" id="fLz-l7-bor"/>
                            <constraint firstItem="dv3-As-f0G" firstAttribute="leading" secondItem="nJo-bA-ukg" secondAttribute="leading" id="jn5-Fr-srC"/>
                            <constraint firstItem="01R-Cn-7LB" firstAttribute="leading" secondItem="FwO-72-OK9" secondAttribute="leading" id="jx1-3Y-5gq"/>
                            <constraint firstItem="nJo-bA-ukg" firstAttribute="trailing" secondItem="dv3-As-f0G" secondAttribute="trailing" id="qdk-re-53Q"/>
                            <constraint firstItem="d7N-DP-tHR" firstAttribute="top" secondItem="FwO-72-OK9" secondAttribute="top" id="weI-vn-XFC"/>
                            <constraint firstItem="d7N-DP-tHR" firstAttribute="leading" secondItem="FwO-72-OK9" secondAttribute="leading" id="z2O-kd-Y5h"/>
                            <constraint firstItem="dv3-As-f0G" firstAttribute="top" secondItem="FwO-72-OK9" secondAttribute="top" id="zkx-3Y-vRj"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="backView" destination="dv3-As-f0G" id="tDc-n0-WaA"/>
                        <outlet property="bottomView" destination="8LJ-xf-Ps9" id="SbJ-mz-WIv"/>
                        <outlet property="bottomViewHeight" destination="kVT-mX-O8v" id="89c-rC-y9f"/>
                        <outlet property="closeBtn" destination="xHO-6k-baW" id="eqC-Wn-ZZT"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="qx6-Ia-ghR" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-1842" y="1767"/>
        </scene>
    </scenes>
    <resources>
        <image name="arrow_left" width="64" height="64"/>
        <image name="arrow_right" width="64" height="64"/>
        <image name="bg" width="750" height="1334"/>
        <image name="demo" width="443" height="960"/>
        <image name="light-bulb-off" width="100" height="100"/>
        <image name="light-bulb-on" width="100" height="100"/>
        <image name="sacan_input" width="100" height="100"/>
        <image name="scan_close" width="50" height="50"/>
        <image name="scan_location" width="100" height="100"/>
        <image name="scan_trim" width="100" height="100"/>
        <image name="返回" width="10" height="16"/>
        <systemColor name="opaqueSeparatorColor">
            <color red="0.77647058823529413" green="0.77647058823529413" blue="0.78431372549019607" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemBlueColor">
            <color red="0.0" green="0.47843137254901963" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
        <systemColor name="systemRedColor">
            <color red="1" green="0.23137254901960785" blue="0.18823529411764706" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
