<div class="global-bg"></div>

<ion-header
  translucent="true"
  class="header"
>
  <ion-toolbar class="title-toolbar">
    <ion-buttons slot="start">
      <ion-button
        class="back-button"
        (click)="back()"
      >
        <ion-icon
          slot="icon-only"
          name="chevron-back"
        ></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>未完了資産</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content
  #content
  [scrollEvents]="true"
  (ionScroll)="onScroll($event)"
  class="offset-bottom-0">
  <div class="unit-data-sty">
    <!-- テーブル要素の子要素をテーブルセル要素にする -->
    <ion-label>総資産数 : {{ mTotal }}件</ion-label>
    <ion-label>未完了数 : {{ willBescan }}件</ion-label>
  </div>
  <div class="container layout-default">
    <!--検索-->
    <div class="search-input-search-div">
      <ion-icon
        class="icon-assets-list-search-search"
        [ngClass]="inputKey === '' ? 'assets-list-search-icon-sky-normal' : 'assets-list-search-icon-sky-highlight'"
        name="search-outline"></ion-icon>
      <ion-input
        type="search"
        value="{{ inputKey }}"
        placeholder="何をお探しですか？"
        enterkeyhint="search"
        (keyup.enter)="doSearch($event.target.value)"
        class="icon-assets-list-search-input"></ion-input>
      <ion-icon
        class="icon-assets-list-search-button"
        name="close-outline"
        (click)="$event.stopPropagation(); clearInputKey()"
        *ngIf="inputKey"></ion-icon>
    </div>

    <!--リスト表示-->
    <div class="assets-list by-list asset-list-div-sty">
      <div class="scrollingContainer invisible-scrollbar" style="overflow: hidden;position: relative" [class.hidden-div]="!isContentViewDisplay">
        <div class="phantomContainer" [style.height.px]="phantomHeight"></div>
        <div class="actualContent invisible-scrollbar" [style.transform]="getTransform()">
          <ng-container *ngFor="let idx of rangeArr">
            <div
              #slidingItems
              [id]="idx"
              class="ion-item-sliding-container"
              style="margin-bottom: 10px;">
              <div
                class="list-table-item"
                style="flex-wrap: wrap"
                [ngClass]="{ 'is-primary': getItem(idx).finishScan }">
                <!--コンテンツ-->
                <div
                  class="list-table-item-start overflow"
                  [ngStyle]="{ 'flex-basis': hasHomeImage(getItem(idx)) ? 'calc(100% - 65px)' : '100%' }">
                  <table style="overflow-x: auto">
                    <tr *ngFor="let assetItem of getItem(idx)['assetItems']; let itemIndex = index">
                      <ng-container *ngIf="more[getItem(idx).assetId] || itemIndex < 10">
                        <!-- <div> -->
                        <th scope="row"><span class="with-colon">{{ assetItem['itemDisplayName'] }}</span></th>
                        <td><span class="table-content break-line">{{ assetItem['value'] }}</span></td>
                        <!-- </div> -->
                      </ng-container>
                    </tr>
                    <!-- <div > -->
                    <tr *ngIf="isCountingType">
                      <th scope="row"><span class="with-colon">数量</span></th>
                      <td><span class="table-content line-clamp-3">{{ getItem(idx).assetScanedCount }}/{{ getItem(idx).assetTotalCount }}</span>
                      </td>
                    </tr>
                    <!-- </div> -->
                  </table>
                </div>
                <!--サムネイル-->
                <div
                  class="asset-item__thumbnail align-self-center"
                  *ngIf="hasHomeImage(getItem(idx))"
                  style="width: 50px; height: 50px">
                  <figure>
                    <div class="homeImage">
                      <ion-spinner
                        [ngClass]="{ center: true }"
                        *ngIf="checkImageIsLoadingStatus(getItem(idx))"></ion-spinner>
                      <ion-img
                        style="object-fit: cover"
                        src="{{ getItem(idx).homeImage.turl }}"
                        alt=""
                        (ionImgDidLoad)="getItem(idx)['homeImgLoaded'] = true"
                        class="is-rounded"
                        (ionError)="errorImgLoad(getItem(idx))"></ion-img>
                    </div>
                  </figure>
                </div>
                <div
                  class="pt-2"
                  *ngIf="getItem(idx)?.assetItems?.length > 10"
                  style="margin: 10px 0 5px; width: 100%"
                  (click)="$event.stopPropagation()">
                  <a
                    *ngIf="!more[getItem(idx).assetId]"
                    (click)="onMoreClick(idx,true)"
                    class="btn btn-sm w-20 float-icon-right contents-button"
                    [ngClass]="
                getItem(idx).finishScan
                  ? 'btn-outline-blue-inverse float-icon-more-white'
                  : 'btn-outline-blue float-icon-more-primary'
              "
                    style="width: 100%">
                    さらに表示
                  </a>
                  <a
                    *ngIf="more[getItem(idx).assetId]"
                    (click)="onMoreClick(idx,false)"
                    class="btn btn-sm w-20 float-icon-right contents-button"
                    [ngClass]="
                getItem(idx).finishScan
                  ? 'btn-outline-blue-inverse float-icon-less-white'
                  : 'btn-outline-blue float-icon-less-primary'
              "
                    style="width: 100%">
                    表示を減らす
                  </a>
                </div>
              </div>
            </div>
          </ng-container>
        </div>
      </div>
      <ng-template
        [ngIf]="itemsList.length > 0"
        [ngIfElse]="empty">
        <div>
          <ion-row>
            <ion-col class="text-center text-white font-weight-light"> 全件表示されました</ion-col>
          </ion-row>
        </div>
      </ng-template>
      <ng-template #empty>
        <!--一覧なし-->
        <div class="empty-message">
          <p class="empty-message-title">データがありません</p>
        </div>
      </ng-template>
    </div>
  </div>
</ion-content>
