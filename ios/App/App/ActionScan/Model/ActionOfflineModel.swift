//
//  ActionOfflineModel.swift
//  App
//
//  Created by bao.x on 2024/7/15.
//

import Foundation

class ActionOfflineModel : NSObject,NSCoding {
    
    override init() {
        super.init()
    }
    func encode(with coder: NSCoder) {
        coder.encode(actionInfoModels, forKey: "actionInfoModels")
    }
    
    required convenience init?(coder: NSCoder) {
        let actionInfoModels = coder.decodeObject(forKey: "actionInfoModels")
        self.init(actionInfoModels: actionInfoModels as! Array<ActionInfoModel>)
    }
    
    init(actionInfoModels:Array<ActionInfoModel>) {
        self.actionInfoModels = actionInfoModels
    }
    
    var actionInfoModels:Array<ActionInfoModel> = []
    
    // 将对象转换为字典
    func toDictionary() -> [String: Any] {
        return ["actionInfoModels": actionInfoModels.map { $0.toDictionary() }]
    }
}

class ActionInfoModel : NSObject,NSCoding{
    override init() {
        super.init()
    }
    init(timestamp:String,actionInfo:String) {
        self.timestamp = timestamp
        self.actionInfo = actionInfo
    }
    
    var timestamp:String = ""
    var actionInfo:String = ""
    
    func encode(with coder: NSCoder) {
        coder.encode(timestamp, forKey: "timestamp")
        coder.encode(actionInfo, forKey: "actionInfo")
    }
    
    required convenience init?(coder: NSCoder) {
        let timestamp = coder.decodeObject(forKey: "timestamp")
        let actionInfo = coder.decodeObject(forKey: "actionInfo")
        self.init(timestamp: timestamp as! String, actionInfo: actionInfo as! String)
    }
    
    // 将对象转换为字典
    func toDictionary() -> [String: Any] {
        return ["timestamp": timestamp, "actionInfo": actionInfo]
    }
}
