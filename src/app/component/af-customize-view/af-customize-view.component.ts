import { ChangeDete<PERSON><PERSON><PERSON>, Component, <PERSON><PERSON><PERSON><PERSON>, Input, Ng<PERSON>one, OnInit } from '@angular/core';
import { AssetTypeItem } from 'src/app/models/assetTypeItem';
import { ActionSheetController, Alert<PERSON>ontroller, ModalController, NavController, Platform } from '@ionic/angular';
import { SelectImageService } from '../../services/selectImage/select-image.service';
import { SelecFileService } from 'src/app/services/selectFile/selec-file.service';
import { LoginService } from 'src/app/services/login/login.service';
import { DownloadService } from 'src/app/services/download.service';
import { StorageUtils } from 'src/app/utils/storage-utils';
import { ActionService } from 'src/app/services/action/action-service.service';
import { WorkflowActionsService } from 'src/app/services/workflow/workflow-actions.service';
import { AssetType } from 'src/app/models/assetType';
import { DigitalSignPadComponent } from '../digital-sign/digital-sign-pad/digital-sign-pad.component';
import { AssetDetailService } from 'src/app/services/assetdetail/asset-detail.service';
import { ToastrService } from 'ngx-toastr';
import { HttpLoginService } from 'src/app/httpUtils/login/http-login.service';
import { GlobalVariableService } from 'src/app/utils/globl-variable.service';
import { AssetService } from 'src/app/services/asset/asset.service';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { Asset } from 'src/app/models/asset';
import { ScanService } from 'src/app/services/scan/scan.service';
import { HttpUtilsService, MethodTypeEnum } from '../../utils/http-utils.service';
import {
  checkIsNaN,
  dealNumber,
  enumToArray,
  getDateFromDateType,
  replaceAll,
  replaceBreakLine,
  replaceSymbol,
  setPercentageUnit,
  getNumbersCommaRemoved,
  toFixed,
  deepEqual,
  calculate,
  validateNumericAndCurrency,
  processNumericAndCurrency,
  validateNumericAndCurrencyField,
  checkIsNumber,
  validateAndProcessNumericItem,
} from 'src/app/utils/utils';
import * as _ from 'lodash';
import { ActivatedRoute } from '@angular/router';
import { Throttle } from 'src/app/utils/throttle.decorator';
import { Overlay } from 'src/app/live/overlay.component';
import { ScreenOrientationService } from '../../services/screen-orientation.service';
import {
  getDaysDifference,
  getFormatDate,
  getFormatDateHasTime,
  getFormatDateReturnDate,
  getFormatDateTime,
  getFutureDate,
} from 'src/app/utils/time-utils';
import { RouterPage } from '../../base/router-page';
import { AddAssetService } from 'src/app/services/add/add-asset.service';
import { MypageGetuserinfoService } from 'src/app/services/mypage/get-userInfo/mypage-getuserinfo.service';
import { WorkflowGetApplicationWorkflowListService } from 'src/app/services/workflow/get-applicationWorkflowList/workflow-getApplicationWorkflowList.service';
import { LRU } from 'src/app/utils/LRU';
import { registerPlugin } from '@capacitor/core';

enum ITEM_TYPE_ENUM {
  INPUT = 'input',
  TEXTAREA = 'textarea',
  LIST = 'list',
  DATE = 'date',
  NUMBER = 'number',
  EMAIL = 'email',
  RADIO = 'radio',
  CHECKBOX = 'checkbox',
  MASTER = 'master',
  USER_SELECT = 'userSelect',
  GROUP_SELECT = 'groupSelect',
  CURRENCY = 'currency',
}

/**
 * assetforce カスタマイズ項目表示用の共通部品を作成
 */
@Component({
  selector: 'app-af-customize-view',
  templateUrl: './af-customize-view.component.html',
  styleUrls: ['./af-customize-view.component.scss'],
})
export class AfCustomizeViewComponent extends RouterPage implements OnInit, DoCheck {
  sectionSortItemArray: { sectionName: string; sectionSort: number }[] = [];
  expand: boolean[] = []; // セクションエリア開閉状況
  sectionNameArray: string[]; // セクション名配列
  itemDataDict: { [sectionName: string]: AssetTypeItem[] }; // 項目固有情報および項目データ情報
  // 用于存储需要删除的文件URL
  private filesToDelete: string[] = [];
  /**
   * key 为 item的 itemName,  value 是否有权限可以编辑.  true 表示有权限, 否则返回false
   */
  permissionsDict: { [key: string]: boolean } = {};
  originItemDataDict: string;
  datePickerOptions: any; // 日付ピッカーのオプション
  date_record_key: string; // 日付レコードキー
  date_record_index: number; // 日付レコード索引
  isHaveRlateData: boolean; // 関連情報チェック
  isChangeValue: boolean = false;
  relateData: { [sectionName: string]: AssetTypeItem[] }; // 関連情報
  relateNameArray: string[]; // 関連情報配列
  relateNameCountDic: { [sectionName: string]: string }; // 関連情報配列数
  relateAssetTypeNametDic: { [assetTypeId: string]: string };
  selectOptions: any = {
    // 入力ダイナミック選択
    header: '',
  };
  dynamicItemDicData = new Map(); // 動的アイテム
  dynamicItemValues: string[] = []; // 動的項目値
  dynamicItemKeys: string[] = []; // 動的項目キー
  inputLimitMaxDate: string; // 入力制限
  currentLocalName = '';
  safeLiveTalkListUrl: SafeResourceUrl;
  tempSectionSortItemArray: { sectionName: string; sectionSort: number }[] = []; // 用于储存临时被隐藏的section
  alertMsgIsShow: boolean = false; // アラートのエラーメッセージ
  actionName: string = ''; //アクション名
  appurtenancesInformationId; // 履歴情報ID
  sectionHomeImageFlgArray = [];
  isCleared = false;
  clearedItems: AssetTypeItem[];
  /**
   ＊是否时处理设定，如果是处理设定时则针对只读条目允许编辑并且取消锁icon
   */
  @Input()
  isAction = false;
  /**
   * 資産詳細から遷移して来たか
   */
  @Input()
  isFromAssetDetail: boolean = false;
  /**
   * 資産詳細から遷移して来たか
   */
  @Input()
  fromPage: string = '';
  /**
   * js
   */
  @Input()
  jsString: string = '';
  /**
   * 戻るURL
   */
  @Input()
  backUrl: string = '';
  /**
   * ステップ名
   */
  @Input()
  stepName: string = '';

  /**
   * asset/detail.page.ts or add/new/edit.page.ts
   */
  @Input()
  isFromDetailOrEditPage: boolean = false;

  /**
   * 是否是来自履历情报。
   */
  @Input()
  isFromRelationPage: boolean = false;

  /**
   * 是否显示Master的初始值
   */
  @Input()
  isShowDefaultValue: boolean = false;

  /**
   * 資産トーク一覧のURL
   */
  @Input()
  set liveTalkListUrl(liveTalkListUrl: string) {
    if (liveTalkListUrl) {
      this.safeLiveTalkListUrl = this.sanitizer.bypassSecurityTrustResourceUrl(liveTalkListUrl);
    }
  }

  /**
   * 項目固有情報および項目データ情報
   */
  @Input()
  set itemData(itemData: { [sectionName: string]: AssetTypeItem[] }) {
    const TempItemData: { [sectionName: string]: AssetTypeItem[] } = {};
    if (itemData) {
      for (const sectionName of Object.keys(itemData)) {
        if (itemData[sectionName].length === 0) {
          return;
        }
        if (itemData[sectionName][0].sectionType != 'spread') {
          // 给画像和署名追加turl属性以便显示图片
          itemData[sectionName].forEach((item) => {
            if (item.itemType == 'image' || item.itemType == 'homeImage' || item.itemType == 'digitalSign') {
              if (item.defaultData && _.isArray(item.defaultData)) {
                item.defaultData.forEach(async (defaultData) => {
                  defaultData.turl = await this.getTurl(defaultData.url);
                });
              }
            }
          });
          TempItemData[sectionName] = itemData[sectionName];
        }
      }
      this.setInitData(TempItemData);
    }
  }

  @Input()
  set permissions(permissionsData: { [key: string]: boolean }) {
    this.permissionsDict = permissionsData || {};

    if (this.itemDataDict) {
      setTimeout(() => {
        // 触发 js的 custom logic. 刷新界面权限.
        this.runAssetTypeAndCheckJavascript();
      }, 30);
    }
  }

  /**
   * isChangeValue
   */
  @Input()
  set setIsChangeValue(isChangeValue: boolean) {
    this.isChangeValue = isChangeValue;
  }

  /**
   * 関連情報
   */
  @Input()
  set relationData(relationData: { [assetTypeName: string]: AssetTypeItem[] }) {
    if (relationData) {
      this.relateData = relationData;
      this.setRelateData(relationData);
    }
  }

  /**
   * ///ダイナミックな動的値
   */
  @Input()
  set dynamicItemDic(dynamicItemDic: {}) {
    console.log(dynamicItemDic);
    if (dynamicItemDic == null || dynamicItemDic == undefined) {
      // This is intentional
    } else {
      console.log(this.dynamicItemDicData);
    }
  }

  /**
   * セクションなしを表す（true:セクションなし;false:セクションあり）
   */
  @Input()
  noSection = false;

  @Input()
  isResumeInformation = false;
  /**
   * セクションなしを表す（true:セクションなし;false:セクションあり）
   */
  @Input()
  topDiv = 'list-container has-list-title is-no-rounded-top';

  /**
   * 表示のみかを示すフラグ（true:表示のみ編集不可;false:編集も可能）
   */
  @Input()
  previewFlg = false;

  /**
   * 戻りページ（マスター関連）
   */
  @Input()
  backPath = '/asset/detail';

  /**
   * 関連資産セクション表示か
   */
  @Input()
  isShowRelativeAssetSection: boolean = false;

  //assetDataBefore
  @Input()
  assetDataBefore: string;
  @Input()
  relateListStringBefore: string;
  //assetId
  @Input()
  assetId: string;
  @Input()
  modifiedDate: string;
  @Input()
  assetText: string;
  @Input()
  saveAssetTypeId: string;
  relationExtraData: any;
  @Input()
  set relationExtra(value) {
    console.log(value);
    this.relationExtraData = value;
  }

  /**
   * 数量对象的输入长度限制， 默认为10， 资产详细为15
   */
  @Input()
  lengthLimit: number = 10;

  calculateReplace = ['\\', '(', ')', '$', '*', '+', '.', '[', ']', '?', '^', '{', '}', '|'];
  errorScheduleInfo = [];
  // masterTypeId毎に、masterDataをリストに入れる
  masterResult = {};

  constructor(
    private scanService: ScanService,
    navController: NavController,
    private ref: ChangeDetectorRef,
    private selecImageService: SelectImageService,
    private selectFileService: SelecFileService,
    private alertController: AlertController,
    private loginService: LoginService,
    private actionService: ActionService,
    private downloadService: DownloadService,
    private workflowActionsService: WorkflowActionsService,
    private modalController: ModalController,
    private platform: Platform,
    private assetDetailService: AssetDetailService,
    private toastr: ToastrService,
    public assetService: AssetService,
    private httpUtils: HttpLoginService,
    private globalVariable: GlobalVariableService,
    public sanitizer: DomSanitizer,
    route: ActivatedRoute,
    private http: HttpUtilsService,
    private screenOrientationService: ScreenOrientationService,
    public addAssetService: AddAssetService, // js 专用请勿删除
    public getinfoService: MypageGetuserinfoService, // js 专用请勿删除
    public workflowGetApplicationWorkflowListService: WorkflowGetApplicationWorkflowListService, // js 专用请勿删除
    ngZone: NgZone,
    private actionSheetController: ActionSheetController,
  ) {
    super(navController, route, ngZone);
    console.log('af-customize-view.component.ts constructor');
    this.expand = [];
    this.expand[-1] = true;
  }

  ngDoCheck(): void {
    if (_.isEmpty(this.jsString)) {
      return;
    }
    this.doJavascript(this.jsString);
    console.log('[blog] ngDoCheck customize Logic 実行完了')
  }

  /**
   * ページ初期化
   * @param：なし
   */
  ngOnInit() {
    this.getCustomItem();
    this.getCurrentLocalName();
    console.log('af-customize-view.component.ts ngOnInit');
    this.inputLimitMaxDate = getFutureDate(10);
  }

  /**
   * @description:amountActionTaskUpdateItemName
   * @param {type}
   * @return {type}
   */
  minusClick(assetTypeItem: any, index) {
    assetTypeItem = this.assetService.minusClick(assetTypeItem);
    //check customs
    this.runAssetTypeAndCheckJavascript();
  }

  /**
   * @description:
   * @param {type}
   * @return {type}
   */
  plusClick(assetTypeItem: any, index) {
    assetTypeItem = this.assetService.plusClick(assetTypeItem);
    //check customs
    this.runAssetTypeAndCheckJavascript();
  }

  /**
   * @description:
   * @param {type}
   * @return {type}
   */
  updateCount(assetTypeItem: any, value: any) {
    assetTypeItem = this.assetService.updateCount(assetTypeItem, value);
    //check customs
    this.runAssetTypeAndCheckJavascript();
  }

  /**
   * 無効な数値であれば、空にする
   * @param：なし
   */
  clearInvalidNumberValue(assetTypeItem: any, value: any) {
    if (value === '-') {
      assetTypeItem.defaultData = '';
      this.runAssetTypeAndCheckJavascript();
    }
  }

  /**
   * getCalculate
   * @param：なし
   */
  getCalculate(calculate, itemOption) {
    for (const sectionName of Object.keys(this.itemDataDict)) {
      if (!(this.isChangeValue == true && sectionName == '変更項目')) {
        for (const sitem of this.itemDataDict[sectionName]) {
          let tItemName = sitem.itemName;
          this.calculateReplace.forEach(function (ch) {
            tItemName = tItemName.replace(ch, '\\' + ch);
          });

          let reg = new RegExp('`' + tItemName + '`', 'g');
          let tval = sitem.defaultData;
          if (tval == undefined || (sitem['itemType'] == 'checkbox' && tval == '[]')) {
            tval = '';
          }
          if (tval instanceof Date) {
            tval = getFormatDate(tval);
          }
          if (sitem.itemType == 'number' || sitem.itemType == 'currency' || sitem.itemType == 'appurInfoSummary') {
            const invalidExpressions = ["''+", "''-", "''*", "''/", "+''", "-''", "*''", "/''"];
            calculate = invalidExpressions.some((expression) => calculate.includes(expression))
              ? ''
              : calculate.replace(reg, tval);
          } else if (sitem.itemType == 'calculate') {
            // 項目タイプ：計算
            if (sitem.option.calculateType == 'currency' || sitem.option.calculateType == 'digital') {
              // 計算式の種類：数値、通貨
              calculate = calculate.replace(reg, tval);
            } else if (sitem.option.calculateType == 'time') {
              // 計算式の種類：日付/時間
              if ((tval + '').search(new RegExp('/', 'g')) >= 0) {
                // YYYY/MM/DD
                tval = "'" + tval + "'";
              }
              calculate = calculate.replace(reg, tval);
            } else {
              // 計算式の種類：はい/いいえ、1行テキスト
              calculate = calculate.replace(reg, "'" + tval + "'");
              const invalidExpressions = ["''==''", "''>''", "''>=''", "''<''", "''<=''"];
              if (invalidExpressions.some((expression) => calculate.includes(expression))) {
                calculate = '';
              }
            }
          } else if (sitem.itemType == 'master') {
            if (Object.keys(tval).length === 0) {
              let masterTypeId = sitem.optionObject.masterTypeId;
              calculate = calculate.replace(reg, "'" + masterTypeId + "'");
            } else {
              let masterId = tval.masterId;
              calculate = calculate.replace(reg, "'" + masterId + "'");
            }
          } else {
            calculate = calculate.replace(reg, "'" + tval + "'");
            calculate = replaceAll(calculate);
          }
        }
      }
    }
    calculate = replaceAll(calculate);
    return replaceBreakLine(calculate);
  }

  /**
   * calculateMaster
   * @param itemOption
   * @returns
   */
  calculateMaster(itemOption): boolean {
    let calculate = this.getCalculate(itemOption.calculate, itemOption);
    calculate = calculate.toLocaleString().replace(/,/gi, '');
    const invalidExpressions = ["''==''", "''>''", "''>=''", "''<''", "''<=''"];
    if (invalidExpressions.some((expression) => calculate.includes(expression))) {
      return undefined;
    }
    let result = eval(calculate);
    return result;
  }

  /**
   * calculateValue
   * @param：なし
   */
  calculateValue() {
    console.log('begin calculateValue');
    for (const sectionName of Object.keys(this.itemDataDict)) {
      if (!(this.isChangeValue == true && sectionName == '変更項目')) {
        for (const item of this.itemDataDict[sectionName]) {
          if (item.itemType == 'calculate') {
            console.log('begin calculate ' + item.itemType + ':' + item.itemName);
            let itemOption = item.optionObject;
            if (itemOption.calculateType == undefined || itemOption.calculate == undefined) {
              return false;
            }
            if (itemOption.calculateType == 'time') {
              let calculate = this.getCalculate(itemOption.calculate, itemOption);
              let result;
              try {
                let js = this.globalVariable.CalculateJavascript + calculate;
                global.pageThis = this;
                let date = eval(js);
                if (date == 0) {
                  result = 0;
                } else if (!date) {
                  result = '';
                } else if (typeof date === 'object' && date instanceof Date) {
                  result = getFormatDate(date);
                } else {
                  result = date;
                }
              } catch (err) {
                item.defaultData = '';
                continue;
              }
              item.defaultData = checkIsNaN(result);
            }
            if (itemOption.calculateType == 'digital') {
              let calculateStr = this.getCalculate(itemOption.calculate, itemOption);
              calculateStr = calculateStr.replace(/,/g, '');
              let decimalPoint = itemOption.calculateDecimalPoint == undefined ? 0 : _.toNumber(itemOption.calculateDecimalPoint);

              let result;

              if (calculateStr == '') {
                item.defaultData = '';
                continue;
              }
              try {
                let calculateResult = replaceSymbol(calculateStr);
                global.pageThis = this;
                result = calculate(calculateResult, decimalPoint);

                const numberCommaDecimalPoint = itemOption.calculateCommaDecimalPoint; // 有没有逗号分隔符
                if (numberCommaDecimalPoint !== '0') {
                  let num = String(result);
                  if (num) {
                    if (num.indexOf('e+') > -1) {
                      //  找到科学计数法类型的数字
                      num = result.toLocaleString().replaceAll(',', '');
                    }
                    let left = num.split('.')[0],
                      right = num.split('.')[1];
                    right = right == undefined ? '' : '.' + right;
                    let temp = left
                      .split('')
                      .reverse()
                      .join('')
                      .match(/(\d{1,3})/g);
                    result = (Number(num) < 0 ? '-' : '') + temp.join(',').split('').reverse().join('') + right;
                  }
                } else {
                  let num = String(result);
                  if (num) {
                    if (num.indexOf('e+') > -1) {
                      //  找到科学计数法类型的数字
                      num = result.toLocaleString().replaceAll(',', '');
                      result = num;
                    }
                  }
                }
              } catch (err) {
                item.defaultData = '';
                continue;
              }
              item.defaultData = checkIsNaN(result);
              setPercentageUnit(item, item.defaultData, itemOption, true);
            }
            if (itemOption.calculateType == 'currency') {
              let calculateStr = this.getCalculate(itemOption.calculate, itemOption);
              calculateStr = calculateStr.replace(/,/g, '');
              let decimalPoint = itemOption.calculateDecimalPoint == undefined ? 0 : _.toNumber(itemOption.calculateDecimalPoint);

              if (calculateStr == '') {
                item.defaultData = '';
                continue;
              }
              let result;
              try {
                let calculateResult = replaceSymbol(calculateStr);
                global.pageThis = this;
                result = calculate(calculateResult,decimalPoint);

                let num = String(result);
                if (num && num.indexOf('e+') > -1) {
                  //  找到科学计数法类型的数字
                  num = result.toLocaleString().replaceAll(',', '');
                }
                let left = num.split('.')[0],
                  right = num.split('.')[1];
                right = right == undefined ? '' : '.' + right;
                let temp = left
                  .split('')
                  .reverse()
                  .join('')
                  .match(/(\d{1,3})/g);
                result = (Number(num) < 0 ? '-' : '') + temp.join(',').split('').reverse().join('') + right;
              } catch (err) {
                item.defaultData = '';
                continue;
              }
              item.defaultData = checkIsNaN(result);
            }
            if (itemOption.calculateType == 'bool') {
              let result;
              try {
                result = this.calculateMaster(itemOption);
                if (result == undefined) {
                  item.defaultData = '';
                } else {
                  item.defaultData = result ? 'はい' : 'いいえ';
                }
              } catch (err) {
                item.defaultData = '';
                continue;
              }
            }
            if (itemOption.calculateType == 'text') {
              let calculate = this.getCalculate(itemOption.calculate, itemOption);
              if (calculate) {
                item.defaultData = replaceSymbol(calculate);
              }
            }
          }
        }
      }
    }
  }

  /**
   * 現在の位置を取得
   */
  async getCurrentLocalName() {
    this.currentLocalName = await StorageUtils.get(StorageUtils.KEY_LOCATION);
  }

  /**
   * カスタムアイテム
   */
  async getCustomItem() {
    let dynamicItemDic = new Map();
    const result = await this.actionService.actionGetCustomItem();
    if (result.code == 0) {
      const arr = result.layoutSettingList;
      this.dynamicItemValues.push('<空白>');
      this.dynamicItemValues.push('ログインユーザーのユーザー名');
      this.dynamicItemValues.push('ログインユーザーのEmail');
      this.dynamicItemKeys.push('valueBlank');
      this.dynamicItemKeys.push('currentUserName');
      this.dynamicItemKeys.push('currentUserEmail');

      for (let i = 0; i < arr.length; i++) {
        const item = arr[i];
        dynamicItemDic.set(String(item.itemId), String(item.itemName));
        this.dynamicItemValues.push(String(item.itemName));
        this.dynamicItemKeys.push(String(item.itemId));
      }
      dynamicItemDic.set('valueBlank', '<空白>');
      dynamicItemDic.set('currentUserName', 'ログインユーザーのユーザー名');
      dynamicItemDic.set('currentUserEmail', 'ログインユーザーのEmail');
    }
    console.log('******list.dynamicItemDic**********');
    console.log(dynamicItemDic);
    this.dynamicItemDicData = dynamicItemDic;
    StorageUtils.set(StorageUtils.KEY_ACTION_DYNAMICITEMDIC, dynamicItemDic);
    return dynamicItemDic;
  }

  /**
   * 引渡されたデータより、画面起動が必要な変数を初期化する
   * @param itemData
   */
  async setInitData(itemData: { [sectionName: string]: AssetTypeItem[] }) {
    this.sectionSortItemArray = [];
    this.tempSectionSortItemArray = [];
    if (this.dynamicItemDicData.size == 0) {
      await this.getCustomItem();
    }
    // 画面表示データ変数初期化
    this.itemDataDict = {};
    // セクション名配列変数初期化
    this.sectionNameArray = [];
    // セクションの開閉変数初期化
    this.expand = [];
    this.expand[-1] = true;
    this.isHaveRlateData = false;
    this.sectionHomeImageFlgArray = [];
    await this.loginService.updateLatestUserRole();
    for (const sectionName of Object.keys(itemData)) {
      if (itemData[sectionName].length === 0) {
        return;
      }
      //default false setting
      itemData[sectionName].forEach((assetTypeItem, index) => {
        if (assetTypeItem.itemType === 'homeImage' || assetTypeItem.itemType === 'image') {
          assetTypeItem.loaded = false;
          if (assetTypeItem.itemType === 'image') {
            if (!this.sectionHomeImageFlgArray[sectionName]) {
              this.sectionHomeImageFlgArray[sectionName] = [];
            }
            //メイン画像を自動設定の場合、１-> true
            if (assetTypeItem.option) {
              let optionObj = JSON.parse(assetTypeItem.option);
              this.sectionHomeImageFlgArray[sectionName][index] = optionObj.mainImageAutoSetFlg ?? '';
            }
            if (assetTypeItem.defaultData instanceof Array) {
              for (const data in assetTypeItem.defaultData) {
                if (assetTypeItem.defaultData[data]) {
                  assetTypeItem.defaultData[data].loaded = false;
                }
              }
            }
          }
        }
      });
      let isView = true;
      const firstItem = itemData[sectionName][0];
      // 閲覧権限設定ありの場合の処理
      if (firstItem.optionObject.sectionPrivateGroups) {
        isView = await this.loginService.visiblePermissionsCheck(firstItem.optionObject.sectionPrivateGroups);
      }
      if (!isView) {
        continue;
      }
      // 閲覧権限ありのセクションデータ
      let tempItem = {
        sectionName: sectionName,
        sectionSort: firstItem.sectionSort,
      };
      this.sectionSortItemArray.push(tempItem);
      // セクションの開閉状態を「開く」にする
      this.expand.push(true);

      this.itemDataDict[sectionName] = [];
      // .filter(it => it['classification'] != 4)是因为·复制履历情报·添加了master的里面内容数据，导致这里得排除
      // 具体·复制履历情报·这个功能为什么要添加进master的里面内容数据无从考证。
      // src/app/asset/relate/list/list.page.ts ===> getLayoutSettingInfoList 这个方法
      for (const item of itemData[sectionName].filter((it) => it['classification'] != 4)) {
        let isEdit = true;
        // 編集権限設定ありの場合の処理
        if (item.optionObject.sectionPrivateEditGroups) {
          isEdit = await this.loginService.visiblePermissionsCheck(item.optionObject.sectionPrivateEditGroups);
        }
        if (!String(item.itemValue).includes('assetItemName:')) {
          item.valueForShow = item.defaultData;
        }
        // 編集可否（readonly）属性の初期設定
        if (this.previewFlg || !isEdit || item.sysSetFlg == '1') {
          // 編集権限なしの場合は、編集不可にする
          // システム設定項目の場合は、編集不可にする
          item.optionObject.readonly = '1';
        }
        // 数字
        if (!String(item.itemValue).includes('assetItemName:')) {
          if (item.itemType === 'number') {
            if (item.defaultData != '個別に設定されています') {
              if (item.defaultData == null || item.defaultData == '' || item.defaultData == undefined) {
                // This is intentional
              } else {
                if (_.toString(item.defaultData).includes(',')) {
                  // This is intentional
                } else {
                  if (checkIsNumber(item.defaultData)) {
                    const option = JSON.parse(item.option);
                    const numberCommaDecimalPoint = Number(option.numberCommaDecimalPoint); // 有没有逗号分隔符0没有、1有
                    const numberDecimalPoint = Number(option.numberDecimalPoint); // 小数点的位数
                    const str: string = _.toString(item.defaultData);
                    item.defaultData = dealNumber(numberCommaDecimalPoint, numberDecimalPoint, str, true);
                    setPercentageUnit(item, item.defaultData, option);
                  }
                }
              }
            }
          }
        }

        //経過日数
        if (item.itemType === 'number' && item.itemName === 'passedTime') {
          let isCaked: boolean;
          if (_.isString(item.defaultData)) {
            isCaked = _.isEmpty(isCaked);
          } else {
            isCaked = _.isEmpty(_.toString(isCaked));
          }
          if (isCaked) {
            for (const temp of itemData[sectionName]) {
              if (temp.itemName == 'createdDate') {
                if (temp.defaultData != undefined || temp.defaultData != null) {
                  item.defaultData = getDaysDifference({ startDate: temp.defaultData, endDate: new Date() });
                }
              }
            }
          }
        }

        // 履历情报合计
        if (item.itemType === 'appurInfoSummary') {
          if (item.defaultData == undefined || item.defaultData == null || item.defaultData == '') {
            // This is intentional
          } else {
            if (String(item.defaultData).includes(',')) {
              // This is intentional
            } else {
              let option = JSON.parse(item.option);
              let numberCommaDecimalPoint = 3;
              let str: string = item.defaultData;
              item.defaultData = dealNumber(numberCommaDecimalPoint, -1, toFixed(str), true);
            }
          }
        }

        //通貨
        if (item.itemType === 'currency') {
          if (item.defaultData == null || item.defaultData == '' || item.defaultData == undefined) {
            // This is intentional
          } else {
            if (String(item.defaultData).includes(',')) {
              // This is intentional
            } else {
              let option = JSON.parse(item.option);
              let numberCommaDecimalPoint = 3;
              let numberDecimalPoint = Number(option['currencyDecimalPoint']); //小数点的位数
              let str: string = item.defaultData;
              item.defaultData = dealNumber(numberCommaDecimalPoint, numberDecimalPoint, toFixed(str), true);
            }
          }
        }

        // 日付項目の初期設定
        if (!String(item.itemValue).includes('assetItemName:')) {
          if (item.itemType === 'date' && item.defaultData) {
            if (item.defaultData == 'currentUserInput') {
              item.defaultData = '';
            }
            var dateFormat;
            if (JSON.parse(item.option).dateType == 'dateTime') {
              dateFormat = 'YYYY/MM/DD HH:mm';
            } else {
              dateFormat = 'YYYY/MM/DD';
            }

            item.dataForInit = getDateFromDateType(
              item,
              item.defaultData,
              item.optionObject.dateType,
              itemData,
              sectionName,
            );
            let splits = item.defaultData.split('_');
            // 判断时间格式
            if (!!Date.parse(item.defaultData)) {
              item.defaultData = item.dataForInit;
            }
            if (item.defaultData !== undefined && item.defaultData != null && item.defaultData !== '') {
              if (item.defaultData == 'now' || item.defaultData == '<now>') {
                if (this.isFromAssetDetail) {
                  item.defaultData = getFormatDateHasTime({ date: new Date(), format: dateFormat });
                } else {
                  item.defaultData = '現在';
                }
              } else if (item.defaultData === 'today' || item.defaultData === '<today>') {
                if (this.isResumeInformation == true) {
                  item.defaultData = item.dataForInit;
                } else {
                  if (this.isFromAssetDetail) {
                    item.defaultData = getFormatDateHasTime({ date: new Date(), format: dateFormat });
                  } else {
                    item.defaultData = '今日';
                  }
                }
              } else if (splits[0] == 'after') {
                item.defaultData = item.dataForInit;
              }
            } else {
              if (item.itemValue !== undefined) {
                item.defaultData = item.itemValue;
              }
              if (item.defaultData == 'currentUserInput') {
                item.defaultData = '';
              }
            }

            if (item.defaultData === 'now' || item.defaultData === '<now>') {
              item.valueForShow = '現在';
            } else if (item.defaultData === 'today' || item.defaultData === '<today>') {
              item.valueForShow = '今日';
            }
          }
        }

        //マスター初期化
        if (item.itemType === 'master') {
          //defaultData.displayにマスタ初期値を入れる
          if (this.isShowDefaultValue) {
            if (!_.isEmpty(item.masterId) && _.isEmpty(item.defaultData.display)) {
              let display = {};
              for (let tmpItem of item.optionObject.masterDisplayItems) {
                let masterDefaultValue = await this.getMasterDefaultValue(
                  item.optionObject.masterTypeId,
                  item.masterId,
                  tmpItem['itemName'],
                );
                if (!_.isEmpty(masterDefaultValue)) {
                  display[tmpItem['itemId']] = masterDefaultValue;
                }
              }
              item.defaultData = {
                masterId: item.masterId,
                display,
              };
            }
          }
          this.showMasterDisplayItems(item);
        }

        // ダイナミックな動的値
        if (item.itemType === 'input' && item.method === '2') {
          if (!String(item.itemValue).includes('assetItemName:')) {
            if (this.dynamicItemDicData == null || undefined) {
              // This is intentional
            } else {
              const dyAllkeys: Array<string> = [];
              const str = String(item.itemValue).trim();
              const index = str.split('_').length;
              if (index > 1) {
                const itemId: string = String(str.split('_')[str.split('_').length - 1]);
                item.valueForShow = String(this.dynamicItemDicData.get(itemId));
              } else {
                item.valueForShow = String(str === '' ? '' : this.dynamicItemDicData.get(str));
              }
            }
          }
        }

        if (
          (item.itemType === 'input' && item.method !== '2') ||
          item.itemType === 'email' ||
          (item.itemValue == 'currentUserInput' && item.itemType === 'input' && item.method === '2')
        ) {
          // 如果不为空并且非string，就转成string（input类型必须是string）
          if (!_.isEmpty(_.toString(item.itemValue)) && !_.isString(item.itemValue)) {
            item.itemValue = _.toString(item.itemValue);
          }
          if (!_.isEmpty(_.toString(item.defaultData)) && !_.isString(item.defaultData)) {
            item.defaultData = _.toString(item.defaultData);
          }

          let name = await this.workflowActionsService.getDynamicSettingName(item.itemValue ?? item.defaultData);
          if (name) {
            item.defaultData = name;
          }
        }

        if (item.itemValue == 'currentUserInput' && item.method === '2') {
          if (item.defaultData != '個別に設定されています') {
            if (
              item.defaultData == undefined ||
              item.defaultData == 'undefined' ||
              item.defaultData == 'currentUserInput'
            ) {
              item.defaultData = '';
            }
          }
          if (
            item.valueForShow == undefined ||
            item.valueForShow == 'undefined' ||
            item.valueForShow == 'currentUserInput'
          ) {
            item.valueForShow = '';
          }
        }

        if (item.itemType === 'map') {
          const isShowGeo = await this.assetService.isShowGeocodeMap(item, true);
          if (isShowGeo.isShowGeo) {
            item['defaultData'] = isShowGeo.geocode;
            item['mapDefaultData'] = isShowGeo.geocode['location'];
          } else {
            item['mapDefaultData'] = item.defaultData;
          }
        }
        if (item.itemType === 'checkbox') {
          const optionObj = item?.optionObject;
          if (optionObj['checkboxMultiFlg'] === '1') {
            if (_.isEmpty(_.toString(item['defaultData']))) {
              item['defaultData'] = [];
            }
            if (_.isString(item['defaultData'])) {
              try {
                item['defaultData'] = JSON.parse(item['defaultData']);
              } catch (error) {
                console.error('checkbox defaultData 多选类型 json转换失败');
              }
            }
            if (_.isString(item['valueForShow'])) {
              try {
                item.valueForShow = JSON.parse(item['valueForShow']);
              } catch (error) {
                console.error('checkbox valueForShow 多选类型 json转换失败');
              }
            }
          }
        }

        // 数字或通货类型输入位数检查
        if (item.itemType === 'number' || item.itemType === 'currency') {
          validateAndProcessNumericItem(item);
        }

        this.itemDataDict[sectionName].push(item);
        // ブランクに設定
        let blankItemTypeList = ['groupSelect', 'userSelect', 'input', 'textarea', 'currency', 'number', 'list'];
        if (item.itemValue == 'valueBlank' && blankItemTypeList.includes(item.itemType)) {
          this.setShowValue(item);
        }
      }
    }
    this.tempSectionSortItemArray = this.sectionSortItemArray;
    this.sectionNameArray = this.createSectionNameArray(this.sectionSortItemArray);
    this.itemDataDict = this.sortData(this.itemDataDict);
    this.ref.detectChanges();
    this.originItemDataDict = JSON.stringify(this.itemDataDict);
    //check customs
    this.runAssetTypeAndCheckJavascript();
  }

  /**
   * isFormChanged
   */
  get isFormChanged() {
    return this.originItemDataDict != JSON.stringify(this.itemDataDict);
  }

  /**
   * setRelateData 对显示的关联资产做过滤. 原始数据在 relateData 里面.  此处只对显示的关联资产进行处理.
   */
  async setRelateData(itemData: { [sectionName: string]: AssetTypeItem[] }) {
    this.httpUtils.httpGetAssetType(true, true).then((data) => {
      let assetTypes: [AssetType] = data.assetTypeList;
      let itemDataKeys = Object.keys(itemData);
      if (itemDataKeys.length > 0) {
        this.isHaveRlateData = true;
        this.relateNameArray = [];
        var dict: { [sectionName: string]: string } = {};
        var dict2: { [sectionName: string]: string } = {};
        for (const sectionName of itemDataKeys) {
          // 此处过滤掉 assetTypeId 不在assetTypes里面的.
          var itemDataSection = itemData[sectionName];
          if(!_.isEmpty(itemDataSection)) {
            itemDataSection = itemDataSection.filter((item) => {
              return assetTypes.some((assetType) => assetType.assetTypeId == String(item.assetTypeId));
            });
          }
          if (itemDataSection.length === 0) {
            continue;
          }

          this.relateNameArray.push(sectionName);
          dict[sectionName] = String(itemData[sectionName].length);
          assetTypes.forEach((assetType) => {
            if (String(assetType.assetTypeId) == sectionName) {
              dict2[sectionName] = assetType.assetTypeName;
            }
          });
        }
        this.relateNameCountDic = dict;
        this.relateAssetTypeNametDic = dict2;
        this.isHaveRlateData = true;
      } else {
        this.isHaveRlateData = false;
      }
    });
  }

  /**
   * 表示値を設定する
   */
  setShowValue(item: AssetTypeItem) {
    let resultShowData = this.getShowValue(item);

    if (resultShowData != undefined && resultShowData != 'undefined') {
      item.valueForShow = resultShowData;
      item.defaultData = resultShowData;
    }
  }

  /**
   * 値を得る
   */
  getShowValue(item: AssetTypeItem) {
    var str = String(item.defaultData).trim();
    if (!str || str == 'undefined' || str == 'null') {
      str = item.itemValue;
    }
    const index = str.split('_').length;
    if (index > 1) {
      const itemId: string = String(str.split('_')[str.split('_').length - 1]);
      return String(this.dynamicItemDicData.get(itemId));
    } else {
      return String(str === '' ? '' : this.dynamicItemDicData.get(str));
    }
  }

  // セクションの並び順を調整
  createSectionNameArray(itemArray: { sectionName: string; sectionSort: number }[]) {
    function compare(property) {
      return function (obj1, obj2) {
        var value1 = obj1[property];
        var value2 = obj2[property];
        return value1 - value2;
      };
    }

    var sectionNameArray = [];
    var sortObj = itemArray.sort(compare('sectionSort'));
    sortObj.forEach((item) => {
      sectionNameArray.push(item.sectionName);
    });
    return sectionNameArray;
  }

  /**
   * データの並べ替え
   */
  sortData(dict: { [sectionName: string]: AssetTypeItem[] }): { [sectionName: string]: AssetTypeItem[] } {
    const sectionName = Object.keys(dict);
    for (let name of sectionName) {
      dict[name].sort((a, b) => {
        let retVal = 0;
        if (a['positionX'] !== undefined && b['positionX'] !== undefined) {
          if (a['positionX'] > b['positionX']) {
            retVal = 1;
          } else if (a['positionX'] < b['positionX']) {
            retVal = -1;
          }
          return retVal;
        }
      });
      dict[name].sort((a, b) => {
        let retVal = 0;
        if (a['positionY'] !== undefined && b['positionY'] !== undefined) {
          if (a['positionY'] > b['positionY']) {
            retVal = 1;
          } else if (a['positionY'] < b['positionY']) {
            retVal = -1;
          }
          return retVal;
        }
      });
    }
    return dict;
  }

  /**
   * 資産詳細変更前のデータ
   * @param itemData
   */
  async setInitChangeBeforeData(itemData: { [sectionName: string]: AssetTypeItem[] }) {
    // 画面表示データ変数初期化
    var itemChangeBeforeDataDict = {};
    // セクション名配列変数初期化
    var sectionNameArrayForChangeBefore = [];
    // セクションの開閉変数初期化
    var expandForChangeBefore = [];
    for (const sectionName of Object.keys(itemData)) {
      if (itemData[sectionName].length === 0) {
        return;
      }
      let isView = true;
      let isEdit = true;
      const firstItem = itemData[sectionName][0];
      // 閲覧権限設定ありの場合の処理
      if (firstItem.optionObject.sectionPrivateGroups) {
        isView = await this.loginService.visiblePermissionsCheck(firstItem.optionObject.sectionPrivateGroups);
      }
      if (!isView) {
        continue;
      }
      // 編集権限設定ありの場合の処理
      if (firstItem.optionObject.sectionPrivateEditGroups) {
        isEdit = await this.loginService.visiblePermissionsCheck(firstItem.optionObject.sectionPrivateEditGroups);
      }

      // セクション名配列に保持する
      sectionNameArrayForChangeBefore.push(sectionName);
      // セクションの開閉状態を「開く」にする
      expandForChangeBefore.push(true);

      itemChangeBeforeDataDict[sectionName] = [];
      for (const item of itemData[sectionName]) {
        if (!String(item.itemValue).includes('assetItemName:')) {
          item.valueForShow = item.defaultData;
        }
        // 編集可否（readonly）属性の初期設定
        if (this.previewFlg || !isEdit || item.sysSetFlg == '1') {
          // 編集権限なしの場合は、編集不可にする
          // システム設定項目の場合は、編集不可にする
          item.optionObject.readonly = '1';
        }
        // 数字
        if (item.itemType === 'number') {
          if (item.defaultData == null || item.defaultData == '' || item.defaultData == undefined) {
            // This is intentional
          } else {
            if (String(item.defaultData).includes(',')) {
              // This is intentional
            } else {
              const option = JSON.parse(item.option);
              const numberCommaDecimalPoint = Number(option.numberCommaDecimalPoint); // 有没有逗号分隔符0没有、1有
              const numberDecimalPoint = Number(option.numberDecimalPoint); // 小数点的位数
              const str: string = item.defaultData;
              item.defaultData = dealNumber(numberCommaDecimalPoint, numberDecimalPoint, str, true);
              setPercentageUnit(item, item.defaultData, option);
            }
          }
        }
        //通貨
        if (item.itemType === 'currency') {
          if (item.defaultData == null || item.defaultData == '' || item.defaultData == undefined) {
            // This is intentional
          } else {
            if (String(item.defaultData).includes(',')) {
              // This is intentional
            } else {
              let option = JSON.parse(item.option);
              let numberCommaDecimalPoint = 3;
              let numberDecimalPoint = Number(option['currencyDecimalPoint']); //小数点的位数
              let str: string = item.defaultData;
              item.defaultData = dealNumber(numberCommaDecimalPoint, numberDecimalPoint, toFixed(str), true);
            }
          }
        }

        // 日付項目の初期設定
        if (item.itemType === 'date' && item.defaultData) {
          if (item.defaultData == 'currentUserInput') {
            item.defaultData = '';
          }
          var dateFormat;
          if (JSON.parse(item.option).dateType == 'dateTime') {
            dateFormat = 'YYYY/MM/DD HH:mm';
          } else {
            dateFormat = 'YYYY/MM/DD';
          }

          item.dataForInit = getDateFromDateType(
            item,
            item.defaultData,
            item.optionObject.dateType,
            itemData,
            sectionName,
          );
          let splits = item.defaultData.split('_');
          // 判断是否为时间格式数据
          if (!!Date.parse(item.defaultData)) {
            item.defaultData = item.dataForInit;
          }
          if (item.defaultData !== undefined && item.defaultData != null && item.defaultData !== '') {
            if (item.defaultData == 'now' || item.defaultData == '<now>') {
              if (this.isFromAssetDetail) {
                item.defaultData = getFormatDateHasTime({ date: new Date(), format: dateFormat });
              } else {
                item.defaultData = '現在';
              }
            } else if (item.defaultData === 'today' || item.defaultData === '<today>') {
              if (this.isResumeInformation == true) {
                item.defaultData = item.dataForInit;
              } else {
                if (this.isFromAssetDetail) {
                  item.defaultData = getFormatDateHasTime({ date: new Date(), format: dateFormat });
                } else {
                  item.defaultData = '今日';
                }
              }
            } else if (splits[0] == 'after') {
              item.defaultData = item.dataForInit;
            }
          } else {
            if (item.itemValue !== undefined) {
              item.defaultData = item.itemValue;
            }
            if (item.defaultData == 'currentUserInput') {
              item.defaultData = '';
            }
          }

          if (item.defaultData === 'now' || item.defaultData === '<now>') {
            item.valueForShow = '現在';
          } else if (item.defaultData === 'today' || item.defaultData === '<today>') {
            item.valueForShow = '今日';
          }
        }

        //経過日数
        if (item.itemType === 'number' && item.itemName === 'passedTime') {
          if (item.defaultData == undefined || item.defaultData == null || item.defaultData == '') {
            for (const temp of itemData[sectionName]) {
              if (temp.itemName == 'createdDate') {
                if (temp.defaultData != undefined || temp.defaultData != null) {
                  item.defaultData = getDaysDifference({ startDate: temp.defaultData, endDate: new Date() });
                }
              }
            }
          }
        }
        //マスター初期化
        if (item.itemType === 'master') {
          this.showMasterDisplayItems(item);
        }

        // ダイナミックな動的値
        if (item.itemType === 'input' && item.method === '2') {
          if (this.dynamicItemDicData == null || undefined) {
            // This is intentional
          } else {
            const dyAllkeys: Array<string> = [];
            const str = String(item.defaultData).trim();
            const index = str.split('_').length;
            if (index > 1) {
              const itemId: string = String(str.split('_')[str.split('_').length - 1]);
              item.valueForShow = String(this.dynamicItemDicData.get(itemId));
            } else {
              item.valueForShow = String(str === '' ? '' : this.dynamicItemDicData.get(str));
            }
          }
        }

        if (
          (item.itemType === 'input' && item.method !== '2') ||
          item.itemType === 'email' ||
          (item.itemValue == 'currentUserInput' && item.itemType === 'input' && item.method === '2')
        ) {
          let name = await this.workflowActionsService.getDynamicSettingName(item.itemValue ?? item.defaultData);
          if (name) {
            item.defaultData = name;
          }
        }

        // ブランクに設定
        let blankItemTypeList = ['userSelect', 'input', 'textarea', 'currency', 'date', 'number', 'list', 'dateTime'];
        if (item.itemValue == 'valueBlank' && blankItemTypeList.includes(item.itemType)) {
          this.setShowValue(item);
        }

        if (item.itemValue == 'currentUserInput' && item.method === '2') {
          if (item.defaultData != '個別に設定されています') {
            if (
              item.defaultData == undefined ||
              item.defaultData == 'undefined' ||
              item.defaultData == 'currentUserInput'
            ) {
              item.defaultData = '';
            }
          }
          if (
            item.valueForShow == undefined ||
            item.valueForShow == 'undefined' ||
            item.valueForShow == 'currentUserInput'
          ) {
            item.valueForShow = '';
          }
        }
        itemChangeBeforeDataDict[sectionName].push(item);
      }
    }
    return itemChangeBeforeDataDict;
  }

  /**
   * format date as 'YYYY/MM/DD'
   */
  private getDateFromDate(defaultDate: string): string {
    if (defaultDate.length >= 10) {
      return getFormatDate(defaultDate);
    }
    return defaultDate;
  }

  /**
   * 項目の開閉
   * @param index インデックス
   */
  @Throttle()
  toggleExpand(index: number) {
    this.expand[index] = !this.expand[index];
    setTimeout(() => {
      this.ref.detectChanges();
    }, 300);
  }

  /**
   * @description: input項目クリック処理
   * @param {type}
   * @return:
   */
  @Throttle()
  clickInputItem(sectionName: string, index: string) {
    if (this.itemDataDict[sectionName][index].optionObject.click != undefined) {
      this.runJavascript(this.itemDataDict[sectionName][index].optionObject.click);
    }
    if (
      this.itemDataDict[sectionName][index].optionObject.readonly == '1' &&
      !(this.isAction && this.itemDataDict[sectionName][index].editable)
    ) {
      // 編集不可の場合は、何もしないで処理を中止
      return;
    }
    const navigationExtras = {
      title: this.itemDataDict[sectionName][index].itemDisplayName || this.itemDataDict[sectionName][index].itemName,
      value: this.itemDataDict[sectionName][index].defaultData,
      itemType: this.itemDataDict[sectionName][index].itemType,
      itemOption: JSON.parse(this.itemDataDict[sectionName][index].option),
      // バックメソッド
      callback: (data) => {
        // データを更新する
        if (this.itemDataDict[sectionName][index].itemType == 'email') {
          const reg = /^(\w+|[-+.])*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
          if (reg.test(data.info) || data.info == '') {
            this.itemDataDict[sectionName][index].defaultData = data.info;
            this.itemDataDict[sectionName][index].valueForShow = data.info;
            this.itemDataDict[sectionName][index].method = 1;
            this.itemDataDict[sectionName][index].itemValue = data.info;
          } else {
            alert('メールボックスのフォーマットが間違っています。');
          }
        } else {
          this.itemDataDict[sectionName][index].defaultData = data.info;
          this.itemDataDict[sectionName][index].valueForShow = data.info;
          this.itemDataDict[sectionName][index].method = 1;
          this.itemDataDict[sectionName][index].itemValue = data.info;
        }
        this.editValueDynamicOrStatic(sectionName, index, data);
        //check customs
        this.runAssetTypeAndCheckJavascript();
        // リフレッシュ
        this.ref.detectChanges();
      },
    };
    // 編集ページに遷移
    this.goForward({ url: '/edit-item/input', params: navigationExtras });
  }

  /**
   * @description: 一行テキストダイナミックな値処理
   * @param {type}
   * @return:
   */
  clickToSelectDynamicText(sectionName: string, index: number, event) {
    if (this.itemDataDict[sectionName][index].optionObject.click != undefined) {
      this.runJavascript(this.itemDataDict[sectionName][index].optionObject.click);
    }
    if (this.itemDataDict[sectionName][index].optionObject.readonly == '1') {
      // 編集不可の場合は、何もしないで処理を中止
      return;
    }

    this.dynamicItemKeys.forEach((key) => {
      if (this.dynamicItemDicData.get(key) === event.target.value) {
        if (key === 'currentUserName' || key === 'currentUserEmail') {
          this.itemDataDict[sectionName][index].defaultData = key;
          this.itemDataDict[sectionName][index].itemValue = key;
        } else {
          this.itemDataDict[sectionName][index].defaultData = 'currentUserItem_' + key;
          this.itemDataDict[sectionName][index].itemValue = 'currentUserItem_' + key;
        }
        this.itemDataDict[sectionName][index].valueForShow = this.dynamicItemDicData.get(key);
        this.editValueDynamicOrStatic(sectionName, index, key);
      }
    });
    //check customs
    this.runAssetTypeAndCheckJavascript();
  }

  /**
   * @description: textarea項目クリック処理
   * @param {type}
   * @return {type}
   */
  @Throttle()
  clickInputAreaItem(sectionName: string, index: string) {
    if (this.itemDataDict[sectionName][index].optionObject.click != undefined) {
      this.runJavascript(this.itemDataDict[sectionName][index].optionObject.click);
    }
    if (
      this.itemDataDict[sectionName][index].optionObject.readonly == '1' &&
      !(this.isAction && this.itemDataDict[sectionName][index].editable)
    ) {
      // 編集不可の場合は、何もしないで処理を中止
      return;
    }
    const navigationExtras = {
      title: this.itemDataDict[sectionName][index].itemDisplayName || this.itemDataDict[sectionName][index].itemName,
      value: this.itemDataDict[sectionName][index].defaultData,
      itemOption: JSON.parse(this.itemDataDict[sectionName][index].option),
      // バックメソッド
      callback: (data) => {
        // データを更新する
        this.itemDataDict[sectionName][index].defaultData = data.info;
        this.itemDataDict[sectionName][index].valueForShow = data.info;
        this.itemDataDict[sectionName][index].itemValue = data.info;
        this.editValueDynamicOrStatic(sectionName, index, data);
        //check customs
        this.runAssetTypeAndCheckJavascript();
        // リフレッシュ
        this.ref.detectChanges();
      },
    };
    // 編集ページに遷移
    this.goForward({ url: '/edit-item/textarea', params: navigationExtras });
  }

  /**
   * @description: rfid項目クリック処理
   * @param {type}
   * @return {type}
   */
  @Throttle()
  async toScanRFID(sectionName: string, index: string) {
    let isConnect = await this.scanService.getRFIDStatus();
    let assetName = this.getAssetName();
    let assetId = this.assetId;
    let callback = (data) => {
      // 同じRFIDコード値での更新をできない
      if (this.itemDataDict[sectionName][index].defaultData == data) {
        (async () => {
          const alert = await this.alertController.create({
            message: `同じRFIDコードでの更新はできません。`,
            cssClass: 'alert-title-primary',
            buttons: [
              {
                text: '戻る',
                handler: () => {},
              },
            ],
            backdropDismiss: false,
          });
          alert.present();
        })();
        return;
      }
      // データを更新する
      this.itemDataDict[sectionName][index].defaultData = data;
      this.itemDataDict[sectionName][index].valueForShow = data;
      this.itemDataDict[sectionName][index].itemValue = data;
      this.editValueDynamicOrStatic(sectionName, index, data);
      //check customs
      this.runAssetTypeAndCheckJavascript();
      // リフレッシュ
      this.ref.detectChanges();

      this.toastr.success('RFIDコードを登録しました', '', {
        positionClass: 'toast-center-center',
        timeOut: 1000,
      });
    };

    if (this.assetId === '' || this.assetId === null || this.assetId === undefined) {
      let params = this.activatedRoute.snapshot.queryParams;
      assetId = params['assetId'];
    }

    const scanExtras = {
      assetId: assetId,
      assetName: assetName,
      backUrl: this.backPath,
      callback: callback,
      backParams: this.activatedRoute.snapshot.queryParams,
      rfidCode: this.itemDataDict[sectionName][index].defaultData,
      isFromPairPage: false,
    };
    const pairExtras = {
      assetId: assetId,
      assetName: assetName,
      backUrl: this.backPath,
      callback: callback,
      backParams: this.activatedRoute.snapshot.queryParams,
      rfidCode: this.itemDataDict[sectionName][index].defaultData,
    };
    if (isConnect.data) {
      this.goForward({ url: '/asset/rfid-scan', params: pairExtras });
    } else {
      this.goForward({ url: '/asset/rfid-pair', params: scanExtras });
    }
    return;
  }

  /**
   * @description: number項目クリック処理
   * @param {type}
   * @return:
   */
  @Throttle()
  clickNumberItem(sectionName: string, index: string) {
    if (this.itemDataDict[sectionName][index].optionObject.click != undefined) {
      this.runJavascript(this.itemDataDict[sectionName][index].optionObject.click);
    }
    if (
      this.itemDataDict[sectionName][index].optionObject.readonly == '1' &&
      !this.itemDataDict[sectionName][index].editable
    ) {
      // 編集不可の場合は、何もしないで処理を中止
      return;
    }
    const option = JSON.parse(this.itemDataDict[sectionName][index].option);
    const numberCommaDecimalPoint = Number(option.numberCommaDecimalPoint); // 有没有逗号分隔符0没有、1有
    const numberDecimalPoint = Number(option.numberDecimalPoint); // 小数点的位数
    let str: string = this.itemDataDict[sectionName][index].defaultData;
    if (str == undefined || str == null) {
      str = '';
    } else {
      str = str.replace(/,/g, '');
    }
    console.log(str);
    const navigationExtras = {
      title: this.itemDataDict[sectionName][index].itemDisplayName || this.itemDataDict[sectionName][index].itemName,
      value: str,
      itemType: this.itemDataDict[sectionName][index].itemType,
      itemOption: option,
      // バックメソッド
      callback: (data) => {
        setPercentageUnit(this.itemDataDict[sectionName][index], data.info, option);
        // データを更新する
        this.itemDataDict[sectionName][index].defaultData = dealNumber(
          numberCommaDecimalPoint,
          numberDecimalPoint,
          data.info,
          false,
        );
        this.itemDataDict[sectionName][index].valueForShow = dealNumber(
          numberCommaDecimalPoint,
          numberDecimalPoint,
          data.info,
          false,
        );
        this.itemDataDict[sectionName][index].itemValue = dealNumber(
          numberCommaDecimalPoint,
          numberDecimalPoint,
          data.info,
          false,
        );
        this.editValueDynamicOrStatic(sectionName, index, data);

        //check customs
        this.runAssetTypeAndCheckJavascript();
        // リフレッシュ
        this.ref.detectChanges();
      },
    };
    // 編集ページに遷移
    this.goForward({ url: '/edit-item/input', params: navigationExtras });
  }

  /**
   * runAssetTypeAndCheckJavascript
   */
  runAssetTypeAndCheckJavascript() {
    //計算
    this.calculateValue();
    this.calculateValue();

    //check customs
    for (const sectionName of Object.keys(this.itemDataDict)) {
      for (const item of this.itemDataDict[sectionName]) {
        //customs js
        if (item.optionObject.check != '' && item.optionObject.check != undefined) {
          let result;
          if (!this.isAction) {
            result = this.checkJavascript(item.defaultData, item.optionObject.check);
            console.log('[blog] item checkJavascript 実行完了');
          }
          if (result && result.valid !== undefined && result.valid == false) {
            item.isShowMessage = true;
            item.isValid = false;
            item.showMessage = result.data.message;
          } else {
            item.isShowMessage = false;
            item.isValid = true;
          }
        }

        validateAndProcessNumericItem(item);
      }
    }
    //assettype customs js
    if (this.jsString != '' && this.jsString != undefined) {
      this.doJavascript(this.jsString);
      console.log('[blog] customize logic 実行完了');
    }
  }

  /**
   * @description: currency項目クリック処理
   * @param {type}
   * @return:
   */
  @Throttle()
  clickCurrencyItem(sectionName: string, index: string) {
    if (this.itemDataDict[sectionName][index].optionObject.click != undefined) {
      this.runJavascript(this.itemDataDict[sectionName][index].optionObject.click);
    }
    if (
      this.itemDataDict[sectionName][index].optionObject.readonly == '1' &&
      !this.itemDataDict[sectionName][index].editable
    ) {
      // 編集不可の場合は、何もしないで処理を中止
      return;
    }
    const option = JSON.parse(this.itemDataDict[sectionName][index].option);
    const numberCommaDecimalPoint = 3;
    const numberDecimalPoint = Number(option.currencyDecimalPoint); // 小数点的位数
    let str: string = this.itemDataDict[sectionName][index].defaultData;
    if (str == undefined || str == null) {
      str = '';
    } else {
      str = str.replace(/,/g, '');
    }
    console.log(str);
    const navigationExtras = {
      title: this.itemDataDict[sectionName][index].itemDisplayName || this.itemDataDict[sectionName][index].itemName,
      value: str,
      itemType: this.itemDataDict[sectionName][index].itemType,
      itemOption: option,
      // バックメソッド
      callback: (data) => {
        // データを更新する
        this.itemDataDict[sectionName][index].defaultData = dealNumber(
          numberCommaDecimalPoint,
          numberDecimalPoint,
          toFixed(data.info),
          false,
        );
        this.itemDataDict[sectionName][index].valueForShow = dealNumber(
          numberCommaDecimalPoint,
          numberDecimalPoint,
          toFixed(data.info),
          false,
        );
        this.itemDataDict[sectionName][index].itemValue = dealNumber(
          numberCommaDecimalPoint,
          numberDecimalPoint,
          toFixed(data.info),
          false,
        );
        this.editValueDynamicOrStatic(
          sectionName,
          index,
          dealNumber(numberCommaDecimalPoint, numberDecimalPoint, toFixed(data.info), false),
        );
        this.editValueDynamicOrStatic(sectionName, index, data);

        //check customs
        this.runAssetTypeAndCheckJavascript();
        // リフレッシュ
        this.ref.detectChanges();
      },
    };
    // 編集ページに遷移
    this.goForward({ url: '/edit-item/input', params: navigationExtras });
  }

  /**
   * チェックボックス項目クリック処理
   * @param index インデックス
   */
  // @Throttle()
  clickCheckBoxItem(sectionName: string, index: number) {
    if (this.itemDataDict[sectionName][index].optionObject.click != undefined) {
      this.runJavascript(this.itemDataDict[sectionName][index].optionObject.click);
    }
    if (
      this.itemDataDict[sectionName][index].optionObject.readonly == '1' &&
      !this.itemDataDict[sectionName][index].editable
    ) {
      // 編集不可の場合は、何もしないで処理を中止
      return;
    }
    if (this.itemDataDict[sectionName][index].defaultData == '1') {
      this.itemDataDict[sectionName][index].defaultData = '0';
    } else if (
      this.itemDataDict[sectionName][index].defaultData == '0' ||
      this.itemDataDict[sectionName][index].defaultData == '' ||
      this.itemDataDict[sectionName][index].defaultData === null
    ) {
      this.itemDataDict[sectionName][index].defaultData = '1';
    }
    this.editValueDynamicOrStatic(sectionName, index, null);

    //check customs
    this.runAssetTypeAndCheckJavascript();
  }

  /**
   * マルチチェックボックス項目クリック処理
   * @param index インデックス
   */
  // @Throttle()
  clickMultipleCheckBoxItem(sectionName: string, index: number, checkItem: string) {
    if (this.itemDataDict[sectionName][index].optionObject.click != undefined) {
      this.runJavascript(this.itemDataDict[sectionName][index].optionObject.click);
    }
    if (this.itemDataDict[sectionName][index].optionObject.readonly == '1') {
      // 編集不可の場合は、何もしないで処理を中止
      return;
    }
    let checkItemArrary: any;
    if (
      !this.itemDataDict[sectionName][index].defaultData ||
      this.itemDataDict[sectionName][index].defaultData == '' ||
      this.itemDataDict[sectionName][index].defaultData == '[]'
    ) {
      checkItemArrary = [];
    } else {
      checkItemArrary = [];
      this.itemDataDict[sectionName][index].optionObject.checkboxOptions.forEach((checkItemData) => {
        if (this.itemDataDict[sectionName][index].defaultData.includes(checkItemData)) {
          checkItemArrary.push(checkItemData);
        }
      });
    }
    if (checkItemArrary.includes(checkItem)) {
      // 削除
      const indexNumber = checkItemArrary.indexOf(checkItem, 0);
      if (indexNumber > -1) {
        checkItemArrary.splice(indexNumber, 1);
      }
    } else {
      // 追加
      checkItemArrary.push(checkItem);
    }
    this.itemDataDict[sectionName][index].defaultData = checkItemArrary;
    this.editValueDynamicOrStatic(sectionName, index, null);
    //check customs
    this.runAssetTypeAndCheckJavascript();
  }

  /**
   * ラジオ項目クリック処理
   * @param index インデックス
   */
  // @Throttle()
  clickRadioItem(sectionName: string, index: number, radioItem: string) {
    if (this.itemDataDict[sectionName][index].optionObject.click != undefined) {
      this.runJavascript(this.itemDataDict[sectionName][index].optionObject.click);
    }
    if (this.itemDataDict[sectionName][index].optionObject.readonly == '1') {
      // 編集不可の場合は、何もしないで処理を中止
      return;
    }
    if (this.itemDataDict[sectionName][index].defaultData == radioItem) {
      this.itemDataDict[sectionName][index].defaultData = '';
    } else {
      this.itemDataDict[sectionName][index].defaultData = radioItem;
    }
    this.editValueDynamicOrStatic(sectionName, index, null);
    //check customs
    this.runAssetTypeAndCheckJavascript();
  }

  /**
   * @description:日付項目クリック処理
   * @param {type}
   * @return:
   */
  clickListItem(sectionName: string, index: number, event) {
    if (this.itemDataDict[sectionName][index].optionObject.click != undefined) {
      this.runJavascript(this.itemDataDict[sectionName][index].optionObject.click);
    }
    if (this.itemDataDict[sectionName][index].optionObject.readonly == '1') {
      // 編集不可の場合は、何もしないで処理を中止
      return;
    }
    this.itemDataDict[sectionName][index].defaultData = event.target.value;
    this.itemDataDict[sectionName][index].valueForShow = event.target.value;
    this.itemDataDict[sectionName][index].itemValue = event.target.value;
    this.editValueDynamicOrStatic(sectionName, index, event);
    //check customs
    this.runAssetTypeAndCheckJavascript();
  }

  /**
   * @description: リスト項目クリック処理
   * @param {string} sectionName*****
   * @param {number} index
   * @param {*} event
   * @return {*}
   */
  @Throttle()
  clickListTypeItem(sectionName: string, index: number) {
    if (this.itemDataDict[sectionName][index].optionObject.click != undefined) {
      this.runJavascript(this.itemDataDict[sectionName][index].optionObject.click);
    }
    if (
      this.itemDataDict[sectionName][index].optionObject.readonly == '1' &&
      !(this.isAction && this.itemDataDict[sectionName][index].editable)
    ) {
      // 編集不可の場合は、何もしないで処理を中止
      return;
    }
    const navigationExtras = {
      listData: this.itemDataDict[sectionName][index].optionObject.data,
      title: this.itemDataDict[sectionName][index].itemDisplayName || this.itemDataDict[sectionName][index].itemName,
      // バックメソッド
      callback: (data) => {
        // データを更新する
        this.itemDataDict[sectionName][index].defaultData = data.info;
        this.itemDataDict[sectionName][index].valueForShow = data.info;
        this.itemDataDict[sectionName][index].itemValue = data.info;
        this.editValueDynamicOrStatic(sectionName, index, data);
        //check customs
        this.runAssetTypeAndCheckJavascript();
        // リフレッシュ
        this.ref.detectChanges();
      },
    };
    // 編集ページに遷移
    this.goForward({ url: '/component/list', params: navigationExtras, unique: true });
  }

  /**
   * @description:地図項目クリック処理
   * @param {type}
   * @return:
   */
  @Throttle()
  clickMapItem(sectionName: string, index: number) {
    if (this.itemDataDict[sectionName][index].optionObject.click != undefined) {
      this.runJavascript(this.itemDataDict[sectionName][index].optionObject.click);
    }
    if (
      this.itemDataDict[sectionName][index].optionObject.readonly == '1' &&
      !(this.isAction && this.itemDataDict[sectionName][index].editable)
    ) {
      // 編集不可の場合は、何もしないで処理を中止
      return;
    }
    const navigationExtras = {
      assetType: this.itemDataDict[sectionName][index],
      // バックメソッド
      callback: async (data) => {
        const localName = await this.assetService.mapForDetailsInfo(
          this.itemDataDict[sectionName][index],
          _.toString(data.info['mapDefaultData']),
          this.isFromAssetDetail,
        );
        if (!_.isUndefined(localName) || !_.isNull(localName)) {
          this.itemDataDict[sectionName][index] = localName;
        }
        this.editValueDynamicOrStatic(sectionName, index, data);
        this.runAssetTypeAndCheckJavascript();
        // リフレッシュ
        this.ref.detectChanges();
      },
      isFromAssetDetail: this.isFromAssetDetail,
      backPath: this.getRouteCurrentUrl(),
    };
    // 編集ページに遷移
    this.goForward({ url: '/preview/map', params: navigationExtras });
  }

  /**
   * 現在位置ボタンをクリックします
   */
  @Throttle()
  async clickSetCurrentLoaction(sectionName: string, index: number) {
    await this.http.addLoadingQueue();
    try {
      const localName = await this.assetService.mapForDetailsInfo(
        this.itemDataDict[sectionName][index],
        this.currentLocalName,
        this.isFromAssetDetail,
      );
      if (!_.isUndefined(localName) || !_.isNull(localName)) {
        this.itemDataDict[sectionName][index] = localName;
      }
      this.editValueDynamicOrStatic(sectionName, index, null);

      this.runAssetTypeAndCheckJavascript();
      // リフレッシュ
      this.ref.detectChanges();
    } finally {
      await this.http.removeLoadingQueue();
    }
  }

  /**
   * グループ選択
   */
  @Throttle()
  groupSelect(sectionName: string, index: number) {
    let element = this.itemDataDict[sectionName][index];
    if (element.optionObject.click != undefined) {
      this.runJavascript(element.optionObject.click);
    }
    if (element.optionObject.readonly == '1' && !(this.isAction && this.itemDataDict[sectionName][index].editable)) {
      // 編集不可の場合は、何もしないで処理を中止
      return;
    }
    if (typeof element.valueForShow === 'string') {
      if (element.valueForShow !== '' && element.valueForShow !== '{}') {
        element.valueForShow = JSON.parse(element.valueForShow);
      }
    }
    const navigationExtras = {
      isFromPage: 'groupSelect',
      titleLabel: element.itemDisplayName,
      scheduleAlertInfo:
        element.valueForShow !== '' && element.valueForShow !== '{}' ? element.valueForShow : undefined,
      // バックメソッド
      callback: (data) => {
        // データを更新する
        if (data !== undefined) {
          let datum = data['info'];

          let roleArry = [];
          for (let entries of datum.role.entries()) {
            roleArry.push(entries[0]);
          }

          let roleStr = JSON.stringify(roleArry);
          if (JSON.parse(roleStr).length === 0) {
            element.defaultData = '';
          } else {
            element.defaultData = roleStr;
          }
          element.valueForShow = roleStr;
          element.itemValue = roleStr;
        }
        this.editValueDynamicOrStatic(sectionName, index, data);
        //check customs
        this.runAssetTypeAndCheckJavascript();
        // リフレッシュ
        this.ref.detectChanges();
      },
    };
    this.goForward({ url: '/edit-item/staff', params: navigationExtras });
  }

  /**
   * @description:ハイパーリンク項目クリック処理
   * @param {type}
   * @return:
   */
  clickHyperLinkItem(sectionName: string, index: number) {
    if (this.itemDataDict[sectionName][index].optionObject.click != undefined) {
      this.runJavascript(this.itemDataDict[sectionName][index].optionObject.click);
    }
    if (
      this.itemDataDict[sectionName][index].optionObject.readonly == '1' &&
      !(this.isAction && this.itemDataDict[sectionName][index].editable)
    ) {
      // 編集不可の場合は、何もしないで処理を中止
      return;
    }
    const navigationExtras = {
      title: this.itemDataDict[sectionName][index].itemDisplayName || this.itemDataDict[sectionName][index].itemName,
      value: this.itemDataDict[sectionName][index].defaultData,
      itemType: this.itemDataDict[sectionName][index].itemType,
      itemOption: JSON.parse(this.itemDataDict[sectionName][index].option),
      // バックメソッド
      callback: (data) => {
        // データを更新する

        this.itemDataDict[sectionName][index].defaultData = data.info;
        this.itemDataDict[sectionName][index].valueForShow = data.info;
        this.itemDataDict[sectionName][index].itemValue = data.info;
        this.editValueDynamicOrStatic(sectionName, index, data);

        //check customs
        this.runAssetTypeAndCheckJavascript();
        // リフレッシュ
        this.ref.detectChanges();
      },
    };
    // 編集ページに遷移
    this.goForward({ url: '/edit-item/input', params: navigationExtras });
  }

  getGroupList(sectionName, i) {
    let element = this.itemDataDict[sectionName][i];
    if (element && element.valueForShow) {
      let groupSelectInfo = this.workflowActionsService.getGroupSelectInfo(element.valueForShow);
      return groupSelectInfo;
    } else {
      return [];
    }
  }

  /**
   * ユーザー選択
   * @param {type}
   * @return:
   */
  @Throttle()
  userSelect(sectionName: string, index: number) {
    if (this.itemDataDict[sectionName][index].optionObject.click != undefined) {
      this.runJavascript(this.itemDataDict[sectionName][index].optionObject.click);
    }
    if (
      this.itemDataDict[sectionName][index].optionObject.readonly == '1' &&
      !(this.isAction && this.itemDataDict[sectionName][index].editable)
    ) {
      // 編集不可の場合は、何もしないで処理を中止
      return;
    }
    const navigationExtras = {
      // バックメソッド
      callback: (data) => {
        // データを更新する
        this.itemDataDict[sectionName][index].defaultData = {
          userId: data.info.userId,
          userName: data.info.lastName + ' ' + data.info.firstName,
        };
        this.itemDataDict[sectionName][index].valueForShow = {
          userId: data.info.userId,
          userName: data.info.lastName + ' ' + data.info.firstName,
        };
        this.itemDataDict[sectionName][index].itemValue = {
          userId: data.info.userId,
          userName: data.info.lastName + ' ' + data.info.firstName,
        };
        this.editValueDynamicOrStatic(sectionName, index, data);
        //check customs
        this.runAssetTypeAndCheckJavascript();
        // リフレッシュ
        this.ref.detectChanges();
      },
    };
    this.goForward({ url: '/edit-item/staff', params: navigationExtras });
  }

  /**
   * @description: Master項目データ編集のクリックする
   * @param {type}
   * @return:
   */
  @Throttle()
  clickMasterItem(sectionName: string, index: number, item: AssetTypeItem, levelIndex: number) {
    item = _.cloneDeep(item);
    if (this.itemDataDict[sectionName][index].optionObject.click != undefined) {
      this.runJavascript(this.itemDataDict[sectionName][index].optionObject.click);
    }
    if (
      this.itemDataDict[sectionName][index].optionObject.readonly == '1' &&
      !(this.isAction && this.itemDataDict[sectionName][index].editable)
    ) {
      // 編集不可の場合は、何もしないで処理を中止
      return;
    }
    // defaultData backup
    this.itemDataDict[sectionName][index].defaultDataBackup = this.itemDataDict[sectionName][index].defaultData;

    const navigationExtras = {
      index,
      option: item.option,
      optionObject: item.optionObject,
      display: item.defaultData === null ? '' : item.defaultData.display === null ? '' : item.defaultData.display,
      undisplayItemNameList: item['undisplayItemNameList'],
      path: this.getRouteCurrentUrl(),
      levelIndex: levelIndex,
      displayList: item['displayList'],
      fromDetailPage: true,
      // バックメソッド

      actionCallback: (data) => {
        let display;
        let itemType;
        // 戻るボタンから戻って来た場合：
        if (data.fromBackButton == true) {
          // バックアップからデータ復元
          this.itemDataDict[sectionName][data.index].defaultData =
            this.itemDataDict[sectionName][index].defaultDataBackup;
          this.showMasterDisplayItems(this.itemDataDict[sectionName][data.index]);
          //check customs
          this.runAssetTypeAndCheckJavascript();
          this.ref.detectChanges();
        } else {
          if (this.itemDataDict[sectionName][index].optionObject.masterDisplayItems) {
            display = {};
            const masterDisplayItems = this.itemDataDict[sectionName][index].optionObject.masterDisplayItems;
            masterDisplayItems.forEach((displayItem) => {
              const masterItemId = displayItem['itemId'];
              const masterItemName = displayItem['itemName'];
              data.layoutSettingModelList.forEach((item) => {
                if (masterItemName === item.itemName) {
                  itemType = item.option;
                }
              });
              display[masterItemId] = data.info.masterText[masterItemName] || '';
              if (JSON.parse(itemType)['dateType'] !== undefined && JSON.parse(itemType)['dateType'] === 'date') {
                display[masterItemId] = this.getDateFromDate(display[masterItemId]);
              }
            });
            masterDisplayItems.forEach((item) => {
              if (display[item['itemId']] === undefined) {
                itemType = item['option'];
                display[item['itemId']] = data.info.masterText[item['itemName']] || '';
                if (
                  JSON.parse(itemType)['dateType'] !== undefined &&
                  JSON.parse(itemType)['dateType'] === 'date' &&
                  data.info.masterText[item['itemName']]
                ) {
                  display[item['itemId']] = this.getDateFromDate(data.info.masterText[item['itemName']]);
                }
              }
            });
          } else {
            display = data.info.itemValue;
            if (data.layoutSettingModelList !== undefined) {
              data.layoutSettingModelList.forEach((item) => {
                if (data.info.itemName === item.itemName) {
                  itemType = item.option;
                }
              });
            }
            if (itemType !== undefined) {
              if (JSON.parse(itemType)['dateType'] !== undefined && JSON.parse(itemType)['dateType'] === 'date') {
                display = this.getDateFromDate(data.info.itemValue);
              }
            }
          }
          // データを更新する
          this.itemDataDict[sectionName][index].defaultData = {
            masterId: data.info.masterId,
            display,
          };
          // defaultData backup
          this.itemDataDict[sectionName][index].defaultDataBackup = this.itemDataDict[sectionName][index].defaultData;
          this.showMasterDisplayItems(this.itemDataDict[sectionName][index]);
          this.editValueDynamicOrStatic(sectionName, index, data);
          //check customs
          this.runAssetTypeAndCheckJavascript();
          this.ref.detectChanges();
        }
      },
    };
    // 編集ページに遷移
    if (item.optionObject.masterDisplayItems && item.optionObject.masterDisplayItems.length > 0) {
      if (levelIndex != 0 && navigationExtras.display == undefined) {
        alert('上から順に選んでください。');
      } else {
        this.goForward({ url: '/master-info/master-select', params: navigationExtras });
      }
    } else {
      this.goForward({ url: '/action/master', params: navigationExtras });
    }
  }

  /**
   * マスタークリア
   */
  @Throttle()
  async masterClear(sectionName: string, index: number, item: AssetTypeItem) {
    let defaultData = this.itemDataDict[sectionName][index].defaultData;
    if (
      (defaultData !== Object() &&
        defaultData !== undefined &&
        defaultData !== null &&
        defaultData !== '' &&
        defaultData['masterId'] !== undefined) ||
      String(defaultData).trim() !== ''
    ) {
      const alert = await this.alertController.create({
        message: item.itemName + 'をクリアしますか？',
        buttons: [
          {
            text: 'いいえ',
            role: 'cancel',
            cssClass: 'font-weight-bold',
          },
          {
            text: 'はい',
            cssClass: 'text-danger font-weight-normal',
            handler: () => {
              this.itemDataDict[sectionName][index].defaultData = {};
              let displayList = this.itemDataDict[sectionName][index].displayList;
              for (let i = 0; i < displayList.length; i++) {
                let temp = displayList[i];
                temp['itemValue'] = '';
                temp['type'] = '';
              }
              this.itemDataDict[sectionName][index].displayList = displayList;
              //check customs
              this.runAssetTypeAndCheckJavascript();
            },
          },
        ],
      });
      await alert.present();
    }
    this.ref.detectChanges();
    console.log('🔥', sectionName, index, item);
  }

  /**
   * マスターの値を表示する
   * @param assetTypeItem レコード詳細
   */
  showMasterDisplay(assetTypeItem: AssetTypeItem) {
    let displayValue = '';
    if (typeof assetTypeItem.defaultData.display === 'object') {
      displayValue = Object.values(assetTypeItem.defaultData.display).join(',');
    } else {
      displayValue = assetTypeItem.defaultData.display;
    }
    return displayValue;
  }

  /*
   * 一括更新処理の更新項目の更新先は資産項目かどうか
   */
  isAssetItemValue(assetTypeItem: AssetTypeItem) {
    return String(assetTypeItem.itemValue).includes('assetItemName:');
  }

  /**
   * マスタータイプを判断する
   * @param assetTypeItem
   */
  masterType(assetTypeItem: AssetTypeItem): boolean {
    if (assetTypeItem !== undefined) {
      if (assetTypeItem.optionObject.masterDisplayItems && assetTypeItem.optionObject.masterDisplayItems.length > 0) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  /**
   * マスターディスプレイの表示項目
   */
  async showMasterDisplayItems(assetTypeItem: AssetTypeItem) {
    let displayList = [];
    let undisplayItemNameList = [];
    // 5件だけ表示
    if (assetTypeItem.optionObject.masterDisplayItems.length > 5) {
      assetTypeItem.optionObject.masterDisplayItems.splice(5, assetTypeItem.optionObject.masterDisplayItems.length);
    }
    let result = await this.actionService.getActionMasterData(assetTypeItem.optionObject.masterTypeId);
    if (
      assetTypeItem.defaultData !== null &&
      assetTypeItem.defaultData.display !== null &&
      assetTypeItem.defaultData !== undefined &&
      typeof assetTypeItem.defaultData.display === 'object'
    ) {
      for (let i = 0; i < assetTypeItem.optionObject.masterDisplayItems.length; i++) {
        let item = {};
        let temp = assetTypeItem.optionObject.masterDisplayItems[i];
        result.layoutSettingList.forEach((itemLayout) => {
          const itemLayoutItemId = _.toString(itemLayout.itemId);
          const tempItemId = _.toString(temp['itemId']);
          if (_.isEqual(itemLayoutItemId, tempItemId)) {
            if (itemLayout.itemType === 'number') {
              const option = JSON.parse(itemLayout.option);
              const numberCommaDecimalPoint = Number(option.numberCommaDecimalPoint); // 有没有逗号分隔符0没有、1有
              const numberDecimalPoint = Number(option.numberDecimalPoint); // 小数点的位数
              const str: string = assetTypeItem.defaultData.display[tempItemId];
              // const str: string = item.defaultData;
              item['type'] = 'number';
              let value = dealNumber(numberCommaDecimalPoint, numberDecimalPoint, str, true);
              item['itemValue'] = value;
              setPercentageUnit(item, item['itemValue'], option);
            } else {
              if (temp['option']) {
                const option = JSON.parse(temp['option']);
                if (temp['itemType'] == 'checkbox'){
                  if (option.checkboxMultiFlg == '0') {
                    item['type'] = 'checkbox';
                  }else{
                    const itemValue = assetTypeItem.defaultData.display[tempItemId];
                    if (!(itemValue instanceof Array)) {
                      try {
                        assetTypeItem.defaultData.display[tempItemId] = JSON.parse(itemValue)
                      } catch (error) {
                        assetTypeItem.defaultData.display[tempItemId] = itemValue
                      }
                    }
                  }
                }
              }
              item['itemValue'] = assetTypeItem.defaultData.display[tempItemId];
            }
            item['itemName'] = _.isEmpty(temp['itemDisplayName']) ? temp['itemName'] : temp['itemDisplayName'];
            item['itemId'] = tempItemId;
            displayList.push(item);
          }
        });
      }
    } else if (
      assetTypeItem.optionObject.masterDisplayItems !== undefined &&
      assetTypeItem.optionObject.masterDisplayItems.length > 0 &&
      (assetTypeItem.defaultData === null ||
        assetTypeItem.defaultData === undefined ||
        assetTypeItem.defaultData == '' ||
        String(assetTypeItem.defaultData).trim() === '' ||
        assetTypeItem.defaultData.display == '' ||
        assetTypeItem.defaultData.display == undefined ||
        assetTypeItem.defaultData.display == null ||
        String(assetTypeItem.defaultData.display).trim() === '')
    ) {
      for (let i = 0; i < assetTypeItem.optionObject.masterDisplayItems.length; i++) {
        let item = {};
        let temp = assetTypeItem.optionObject.masterDisplayItems[i];
        // const option = JSON.parse(temp["option"]);
        // if (temp["itemType"] == 'checkbox' && option.checkboxMultiFlg == "0") {
        //   item['type'] = "checkbox";
        // }
        item['itemValue'] = '';
        item['itemName'] = _.isEmpty(temp['itemDisplayName']) ? temp['itemName'] : temp['itemDisplayName'];
        item['itemId'] = temp['itemId'];
        displayList.push(item);
      }
    } else if (
      assetTypeItem.optionObject.masterDisplayItems !== undefined &&
      assetTypeItem.optionObject.masterDisplayItems.length > 0 &&
      assetTypeItem.defaultData !== undefined &&
      assetTypeItem.defaultData.display !== undefined &&
      typeof assetTypeItem.defaultData.display === 'string'
    ) {
      let item = {};
      let temp = assetTypeItem.optionObject.masterDisplayItems[0];
      result.layoutSettingList.forEach((itemLayout) => {
        if (itemLayout.itemId === temp['itemId']) {
          if (itemLayout.itemType === 'number') {
            const option = JSON.parse(itemLayout.option);
            const numberCommaDecimalPoint = Number(option.numberCommaDecimalPoint); // 有没有逗号分隔符0没有、1有
            const numberDecimalPoint = Number(option.numberDecimalPoint); // 小数点的位数
            const str: string = assetTypeItem.defaultData.display;
            let value = dealNumber(numberCommaDecimalPoint, numberDecimalPoint, str, true);
            item['itemValue'] = value;
          } else {
            if (temp['option']) {
              const option = JSON.parse(temp['option']);
              if (temp['itemType'] == 'checkbox' && option.checkboxMultiFlg == '0') {
                item['type'] = 'checkbox';
              }
            }
            item['itemValue'] = assetTypeItem.defaultData.display;
          }
          item['itemName'] = _.isEmpty(temp['itemDisplayName']) ? temp['itemName'] : temp['itemDisplayName'];
          item['itemId'] = temp['itemId'];
          displayList.push(item);
        }
      });
    }
    // masterDisplayItems 里边被删除的项目要给显示内容添加一个空值
    let displayItemIds = [];
    result.layoutSettingList.forEach((itemLayout) => {
      displayItemIds.push(itemLayout['itemId'].toString());
    });
    assetTypeItem.optionObject.masterDisplayItems.forEach((displayItem) => {
      if (displayItemIds.indexOf(displayItem['itemId'].toString()) == -1) {
        let item = {};
        if (displayItem['option']) {
          const option = JSON.parse(displayItem['option']);
          if (displayItem['itemType'] == 'checkbox' && option.checkboxMultiFlg == '0') {
            item['type'] = 'checkbox';
          }
        }
        item['itemValue'] = '';
        item['itemName'] = displayItem['itemName'];
        item['itemId'] = displayItem['itemId'];
        displayList.push(item);
        undisplayItemNameList.push(_.isEmpty(item['itemDisplayName']) ? item['itemName'] : item['itemDisplayName']);
      }
    });
    assetTypeItem.undisplayItemNameList = undisplayItemNameList;
    assetTypeItem.displayList = displayList;
  }

  /**
   * @description:画像を選択する
   * @param : sectionName
   * @param : index
   * @return:
   */
  @Throttle()
  async toSelectImage(sectionName: string, index: number) {
    if (this.itemDataDict[sectionName][index].optionObject.click != undefined) {
      this.runJavascript(this.itemDataDict[sectionName][index].optionObject.click);
    }
    if (this.itemDataDict[sectionName][index].optionObject.readonly == '1') {
      // 編集不可の場合は、何もしないで処理を中止
      return;
    }
    let dataScan = {
      itemName: this.itemDataDict[sectionName][index].itemDisplayName || this.itemDataDict[sectionName][index].itemName,
    };

    // 判断如果是 android 设备增加 dataScan['pickType'] 字段
    if (this.platform.is('android')) {
      const actionSheet = await this.actionSheetController.create({
        header: '画像の選択',
        buttons: [
          {
            text: '写真を撮る',
            handler: () => {
              // 拍照PickType==1
              dataScan['pickType'] = 1;
              this.pickPicture(dataScan, sectionName, index);
            },
          },
          {
            text: '画像を選択',
            handler: () => {
              // 相册选择 PickType==1
              dataScan['pickType'] = 2;
              this.pickPicture(dataScan, sectionName, index);
            },
          },
          {
            text: 'キャンセル',
            role: 'cancel',
          },
        ],
      });
      await actionSheet.present();
    } else {
      this.pickPicture(dataScan, sectionName, index);
    }
  }

  private pickPicture(dataScan: { itemName: string }, sectionName: string, index: number) {
    this.selecImageService.selecImage(dataScan).then((data) => {
      if (data instanceof Array) {
        //当其它图像项目里已存在homeImage时,不做自动homeImage处理
        let isAutoMainImage = true;
        for (let sectionName of this.sectionNameArray) {
          for (let i = 0; i < this.itemDataDict[sectionName].length; i++) {
            if (this.itemDataDict[sectionName][i].itemType === 'image') {
              if (this.sectionHomeImageFlgArray[sectionName][i] !== '1') {
                if (this.itemDataDict[sectionName][i].defaultData) {
                  let isHomeImage = this.itemDataDict[sectionName][i].defaultData.find(
                    (item) => item.isHomeImage === true,
                  );
                  if (isHomeImage) {
                    isAutoMainImage = false;
                    break;
                  }
                }
              }
            }
            if (!isAutoMainImage) {
              break;
            }
          }
        }

        if (
          this.itemDataDict[sectionName][index].defaultData == '' ||
          String(this.itemDataDict[sectionName][index].defaultData).trim() === ''
        ) {
          //メイン画像を自動設定する
          if (isAutoMainImage && this.sectionHomeImageFlgArray[sectionName][index] === '1') {
            data[0]['isHomeImage'] = true;
          }
          this.itemDataDict[sectionName][index].defaultData = data;
        } else {
          let checkItemArrary: any = this.itemDataDict[sectionName][index].defaultData;
          if (data.length > 0) {
            if (!checkItemArrary) {
              checkItemArrary = data;
            } else {
              checkItemArrary = data.concat(checkItemArrary);
            }
            //メイン画像を自動設定する
            if (isAutoMainImage && this.sectionHomeImageFlgArray[sectionName][index] === '1') {
              let isHomeImage = checkItemArrary.find((item) => item.isHomeImage === true);
              if (!isHomeImage) {
                checkItemArrary[0]['isHomeImage'] = true;
              }
            }
          }
          this.itemDataDict[sectionName][index].defaultData = checkItemArrary;
        }
        console.log('****Image返回的数据****');
        console.log(this.itemDataDict[sectionName][index].defaultData);
        //check customs
        this.runAssetTypeAndCheckJavascript();
        this.ref.detectChanges();
      }
    });
  }

  /**
   * @description:画像表示・削除画面へ
   * @param {type}
   * @return:
   */
  @Throttle()
  toImagePage(sectionName: string, index: number, count: number) {
    if (this.itemDataDict[sectionName][index].optionObject.click != undefined) {
      this.runJavascript(this.itemDataDict[sectionName][index].optionObject.click);
    }
    const navigationExtras = {
      pageTitle:
        this.itemDataDict[sectionName][index].itemDisplayName || this.itemDataDict[sectionName][index].itemName,
      count,
      value: this.itemDataDict[sectionName][index].defaultData,
      isFromAssetDetail: this.isFromAssetDetail,
      readonly: this.itemDataDict[sectionName][index].optionObject.readonly == '1',
      // バックメソッド
      callback: (data) => {
        if (data.isHomeImageSetting !== undefined && data.isHomeImageSetting !== null && data.isHomeImageSetting != 0) {
          for (let sectionName of this.sectionNameArray) {
            for (let item of this.itemDataDict[sectionName]) {
              if (item.itemType === 'image') {
                console.log(item);
                if (item.defaultData instanceof Array) {
                  for (const it of item.defaultData) {
                    var hasValue = false;
                    for (const it2 of data.info) {
                      if (it.url == it2.url) {
                        // 如果两个图片url一样，因为是值引用，内层修改外层无需更新
                        hasValue = true;
                      }
                    }
                    if (!hasValue) {
                      // 未找到值，重置false
                      it['isHomeImage'] = false;
                    }
                    console.log(it);
                  }
                }
              }
            }
          }
          if (data.isHomeImageSetting == 1) {
            this.toastr.success('メイン画像を設定しました', '', {
              positionClass: 'toast-center-center',
              timeOut: 1500,
            });
          } else {
            this.toastr.success('メイン画像設定を解除しました', '', {
              positionClass: 'toast-center-center',
              timeOut: 1500,
            });
          }
        } else {
          // データを更新する
          this.itemDataDict[sectionName][index].defaultData = data.info;
        }
        // check customs
        this.runAssetTypeAndCheckJavascript();
        this.ref.detectChanges();
      },
    };
    this.goForward({ url: '/preview/image', unique: true, params: navigationExtras });
  }

  getRelateListString() {
    var relateListString = '';
    if (this.relateData) {
      for (let item of Object.keys(this.relateData)) {
        this.relateData[item].forEach((data) => {
          relateListString = relateListString + data.assetId + ',';
        });
      }
    }
    return relateListString;
  }

  /**
   * @description:関連資産の追加
   * @param {type}
   * @return:
   */
  @Throttle()
  async addRlateData() {
    let navigationExtras = {
      //バックメソッド
      callback: async (data) => {
        //クリック
        if (!data.isFromScan) {
          // 対象と同一の資産は関連資産として登録することができません。
          if (data.info.assetId.toString() === this.assetId.toString()) {
            this.showSameAssetNotAllowAlert();
            return;
          }

          if (this.isHaveRlateData) {
            var isExsitAssetTypeId = false;
            //既存
            for (let relateId of Object.keys(this.relateNameCountDic)) {
              if (relateId == data.info.assetTypeId) {
                //資産すでに存在するか
                if (!this.relateData[data.info.assetTypeId].some((e) => e.assetId === data.info.assetId)) {
                  this.relateNameCountDic[relateId] = String(Number(this.relateNameCountDic[relateId]) + 1);
                  this.relateData[data.info.assetTypeId].unshift(data.info);
                }
                isExsitAssetTypeId = true;
              }
            }
            //新規
            if (!isExsitAssetTypeId) {
              var itemList: AssetTypeItem[] = [];
              itemList.unshift(data.info);
              this.relateData[String(data.info.assetTypeId)] = itemList;
              this.relateAssetTypeNametDic[data.info.assetTypeId] = data.assetTypeName;
              this.relateNameCountDic[data.info.assetTypeId] = String(itemList.length);
              this.relateNameArray.unshift(data.info.assetTypeId);
            }
          } else {
            this.relateNameArray = [];
            var dict: { [sectionName: string]: string } = {};
            var dict2: { [sectionName: string]: string } = {};

            this.relateNameArray.unshift(data.info.assetTypeId);
            dict[data.info.assetTypeId] = '1';
            dict2[data.info.assetTypeId] = data.assetTypeName;
            this.relateNameCountDic = dict;
            this.relateAssetTypeNametDic = dict2;

            var itemList: AssetTypeItem[] = [];
            itemList.unshift(data.info);
            this.relateData[String(data.info.assetTypeId)] = itemList;
            this.isHaveRlateData = true;
          }
        } else {
          //スキャン
          if (data.info.data instanceof Array) {
            if (this.isHaveRlateData) {
              data.info.data.forEach((asset) => {
                // 対象と同一の資産は関連資産として登録することができません。
                if (asset.assetId.toString() === this.assetId.toString()) {
                  this.showSameAssetNotAllowAlert();
                  return;
                }

                var isExsitAssetTypeId = false;
                //既存
                for (let relateId of Object.keys(this.relateNameCountDic)) {
                  if (relateId == asset.assetTypeId) {
                    //資産すでに存在するか
                    if (!this.relateData[asset.assetTypeId].some((e) => e.assetId === asset.assetId)) {
                      this.relateNameCountDic[relateId] = String(Number(this.relateNameCountDic[relateId]) + 1);
                      this.relateData[asset.assetTypeId].unshift(asset);
                    }
                    isExsitAssetTypeId = true;
                  }
                }
                //新規
                if (!isExsitAssetTypeId) {
                  var itemList: AssetTypeItem[] = [];
                  itemList.unshift(asset);
                  this.relateData[String(asset.assetTypeId)] = itemList;
                  let assetTypeNameTest = asset['assetTypeName'];
                  if (asset?.assetText) {
                    assetTypeNameTest = JSON.parse(asset.assetText)['資産種類'];
                  }
                  this.relateAssetTypeNametDic[asset.assetTypeId] = assetTypeNameTest;
                  this.relateNameCountDic[asset.assetTypeId] = String(itemList.length);
                  this.relateNameArray.unshift(asset.assetTypeId);
                }
              });
            } else {
              this.relateNameArray = [];
              var dicCount: { [sectionName: string]: string } = {};
              var dicName: { [sectionName: string]: string } = {};
              var relateData: { [sectionName: string]: AssetTypeItem[] } = {}; // 関連情報
              data.info.data.forEach((asset) => {
                // 対象と同一の資産は関連資産として登録することができません。
                if (asset.assetId.toString() === this.assetId.toString()) {
                  this.showSameAssetNotAllowAlert();
                  return;
                }

                var isExsitAssetTypeId = false;
                //既存
                for (let relateId of Object.keys(dicCount)) {
                  if (relateId == asset.assetTypeId) {
                    //資産すでに存在するか
                    if (!relateData[asset.assetTypeId].some((e) => e.assetId === asset.assetId)) {
                      dicCount[relateId] = String(Number(dicCount[relateId]) + 1);
                      relateData[asset.assetTypeId].unshift(asset);
                    }
                    isExsitAssetTypeId = true;
                  }
                }
                //新規
                if (!isExsitAssetTypeId) {
                  var itemList: AssetTypeItem[] = [];
                  itemList.unshift(asset);
                  relateData[String(asset.assetTypeId)] = itemList;
                  dicName[asset.assetTypeId] = asset['assetTypeName'];
                  dicCount[asset.assetTypeId] = String(itemList.length);
                  this.relateNameArray.unshift(asset.assetTypeId);
                }
              });
              this.relateNameCountDic = dicCount;
              this.relateAssetTypeNametDic = dicName;
              this.relateData = relateData;
              this.isHaveRlateData = true;
            }
          }
        }
      },
    };
    this.goForward({ url: '/asset/relation-add', params: navigationExtras, unique: true });
  }

  /**
   * 対象と同一の資産は関連資産として登録できないアラート出る
   * @return:
   */
  async showSameAssetNotAllowAlert() {
    const sameAssetNotAllowAlert = await this.alertController.create({
      message: '対象と同一の資産は関連資産として登録することができません。',
      buttons: [
        {
          text: '閉じる',
          role: 'cancel',
          cssClass: 'font-weight-bold',
          handler: async () => {
            // This is intentional
          },
        },
      ],
    });
    await sameAssetNotAllowAlert.present();
  }
  /**
   * 判断数据是否已经被编辑了，
   * @param assetDataBefore
   * @param dealData
   */
  checkDataHasBeenEdited(assetDataBefore, dealData) {
    const tempAssetDataBefore = this.deleteUnusedKey(JSON.parse(assetDataBefore));
    const tempDealData = this.deleteUnusedKey(dealData);
    return !deepEqual(tempAssetDataBefore, tempDealData);
  }
  /**
   * 判断资产是够被编辑的时候有一些项目并非资产信息，而是为了标记某种状态设的flag，影响到了判断资产是否被编辑
   * 所以要临时删除图片，file，sign里不用的turl以及那些不能编辑的系统时间项目（因为format可能影响判断）等
   * @param cloneForm
   */
  deleteUnusedKey(cloneForm) {
    const deleteNonEditableSystemObj = ['createdDate', 'updatedDate', 'passedTime'];
    const delObjAry = ['fileName', 'loaded', 'uploadDate', 'isHomeImage', 'turl'];
    for (var key in cloneForm) {
      // 删除系统自动登录的不能手动改变的key，防止format不一致导致check失败
      deleteNonEditableSystemObj.forEach((element) => {
        if (key == element) {
          delete cloneForm[element];
        }
      });
      if (!_.isEmpty(cloneForm[key])) {
        if (_.isArray(cloneForm[key])) {
          const valAry = [];
          cloneForm[key].forEach((element) => {
            valAry.push(_.omit(element, delObjAry));
          });
          cloneForm[key] = valAry;
        }
        if (_.isObject(cloneForm[key])) {
          cloneForm[key] = _.omit(cloneForm[key], delObjAry);
        }
      }
    }
    return cloneForm;
  }

  /**
   * @description:関連資産の選択
   * @return:
   * @param assetTypeId 资产种类ID
   */
  @Throttle()
  async clickRelationItem(assetTypeId: string) {
    let dealMap = await this.dealData();
    // 資産種類
    dealMap['資産種類'] = JSON.parse(this.assetText)['資産種類'];
    // 更新日を現在の日時に設定する ASCHK-26883 票号中 后台指出 这个字段可以去掉
    // dealMap['更新日'] = getFormatDateTime(new Date());
    const assetText = JSON.stringify(_.cloneDeep(dealMap));
    this.relateData[assetTypeId].forEach((item) => {
      if (!_.isEmpty(item['assetText'])) {
        const jd = JSON.parse(item['assetText']);
        item['assetName'] = jd['assetName'];
        item['barcode'] = jd['identityCode'];
        item['location'] = jd['location'];
      }
    });
    if (this.fromPage == 'wf') {
      const navigationExtras = {
        value: JSON.stringify(this.relateData[assetTypeId]),
        title: this.relateAssetTypeNametDic[assetTypeId],
        assetTypeId: assetTypeId,
        fromScanPage: this.fromPage,
        backUrl: this.backUrl,
        saveAssetTypeId: this.saveAssetTypeId,
        assetId: this.assetId,
        assetText: assetText,
        modifiedDate: JSON.stringify(this.modifiedDate),
        relateData: JSON.stringify(this.relateData),
        extra: this.relationExtraData,
        callback: (asset: Asset, assetList: AssetTypeItem[]) => {
          console.log('sdasdasdasd', this);
          if (_.isEmpty(asset)) {
            return;
          }
          // this.relateData[String(asset.assetTypeId)] = assetList;
          // this.relateAssetTypeNametDic[asset.assetTypeId] = JSON.parse(asset.assetText)['資産種類'];
          this.relateNameCountDic[asset.assetTypeId] = String(assetList.length);
          if (assetList.length <= 0) {
            this.isHaveRlateData = false;
          }
        },
      };
      this.goForward({ url: this.getTabsPrefixUrl('/asset/relation-list'), params: navigationExtras, unique: true });
    } else {
      delete dealMap['資産種類'];
      // ASCHK-26883 票号中 后台指出 这个字段可以去掉
      // delete dealMap['更新日'];
      const assetDataCheck = _.cloneDeep(dealMap);

      if (
        this.checkDataHasBeenEdited(this.assetDataBefore, assetDataCheck) ||
        this.relateListStringBefore !== this.getRelateListString()
      ) {
        await this.http.clearLoadingQueue();
        const alert = await this.alertController.create({
          message: 'これまでの編集内容を保存しますか？',
          buttons: [
            {
              text: 'いいえ',
              role: 'cancel',
              cssClass: 'font-weight-bold',
              handler: () => {
                const navigationExtras = {
                  value: JSON.stringify(this.relateData[assetTypeId]),
                  title: this.relateAssetTypeNametDic[assetTypeId],
                  assetTypeId: assetTypeId,
                  saveAssetTypeId: this.saveAssetTypeId,
                  assetId: this.assetId,
                  assetText: assetText,
                  modifiedDate: JSON.stringify(this.modifiedDate),
                  relateData: JSON.stringify(this.relateData),
                  fromScanPage: 'asset-list',
                  extra: this.relationExtraData,
                };
                this.goForward({
                  url: this.getTabsPrefixUrl('/asset/relation-list'),
                  params: navigationExtras,
                  unique: true,
                });
              },
            },
            {
              text: 'はい',
              cssClass: 'text-danger font-weight-normal',
              handler: async () => {
                await this.http.addLoadingQueue();
                this.assetDetailService
                  .saveAssetdata(
                    this.saveAssetTypeId,
                    this.assetId,
                    assetText,
                    this.modifiedDate,
                    this.getRelateListString(),
                    JSON.parse(assetText)['identityCode'],
                  )
                  .then(async (data) => {
                    console.log(data);
                    this.toastr.success('アップデート完了しました', '', {
                      positionClass: 'toast-center-center',
                      timeOut: 500,
                    });
                    //最新の資産テキスト取得
                    let result = await this.assetDetailService.getFindById(this.assetId);
                    this.assetText = result.arrayAsset[0].assetText;
                    this.modifiedDate = result.arrayAsset[0].modifiedDate;
                    const navigationExtras = {
                      value: JSON.stringify(this.relateData[assetTypeId]),
                      title: this.relateAssetTypeNametDic[assetTypeId],
                      assetTypeId: assetTypeId,
                      saveAssetTypeId: this.saveAssetTypeId,
                      assetId: this.assetId,
                      assetText: assetText,
                      modifiedDate: JSON.stringify(this.modifiedDate),
                      relateData: JSON.stringify(this.relateData),
                      fromScanPage: 'asset-list',
                      extra: this.relationExtraData,
                    };
                    this.goForward({
                      url: this.getTabsPrefixUrl('/asset/relation-list'),
                      params: navigationExtras,
                      unique: true,
                    });
                  })
                  .finally(() => {
                    this.http.removeLoadingQueue();
                  });
              },
            },
          ],
        });
        await alert.present();
      } else {
        const navigationExtras = {
          value: JSON.stringify(this.relateData[assetTypeId]),
          title: this.relateAssetTypeNametDic[assetTypeId],
          assetTypeId: assetTypeId,
          saveAssetTypeId: this.saveAssetTypeId,
          assetId: this.assetId,
          assetText: assetText,
          modifiedDate: JSON.stringify(this.modifiedDate),
          relateData: JSON.stringify(this.relateData),
          fromScanPage: 'asset-list',
          extra: this.relationExtraData,
        };
        this.goForward({ url: '/tabs/asset/relation-list', params: navigationExtras, unique: true });
      }
    }
  }

  /**
   * ファイルを選択する
   */
  @Throttle()
  toSelectFile(sectionName: string, index: number) {
    if (this.itemDataDict[sectionName][index].optionObject.readonly == '1') {
      // 編集不可の場合は、何もしないで処理を中止
      return;
    }
    if (!this.itemDataDict[sectionName][index].defaultData) {
      this.itemDataDict[sectionName][index].defaultData = [];
    }
    this.selectFileService.selecFile().then(async (data) => {
      if (_.isError(data)) {
        return;
      }
      if (_.isEmpty(data?.fileName)) {
        return;
      }
      // description: '';
      if(this.isFromDetailOrEditPage || this.isFromRelationPage){
        data['description'] = '';
        let userId = await StorageUtils.get(StorageUtils.KEY_USER_ID);
        userId = _.toNumber(userId);
        data['uploadUserId'] = userId;
      }

      this.itemDataDict[sectionName][index].defaultData.push(data);
      this.ref.detectChanges();
    });
  }

  /**
   * ファイルダウンロード
   */
  @Throttle()
  async downloadFile(sectionName: string, index: number, fileIndex: number) {
    const { url, fileName } = this.itemDataDict[sectionName][index].defaultData[fileIndex];
    this.downloadService.downloadFileIonic(url, fileName);
  }

 /**
 * ファイル削除
 * @param sectionName - セクション名
 * @param index - アイテムのインデックス
 * @param fileIndex - ファイルのインデックス
 */
deleteFile(sectionName: string, index: number, fileIndex: number): void {
  // ファイルを削除し、URLを収集
  this.filesToDelete = LRU.shared.get('filesToDelete') == null ? [] : LRU.shared.get('filesToDelete')
  const removedFiles = this.itemDataDict[sectionName][index].defaultData.splice(fileIndex, 1);
  if (removedFiles.length > 0 && removedFiles[0].url) {
    this.filesToDelete.push(removedFiles[0].url); // URLを収集
  }
  LRU.shared.set('filesToDelete', this.filesToDelete);
  this.ref.detectChanges(); // 変更を検出
}

  /**
   * ファイル削除確認のダイアログ
   */
  @Throttle()
  async presentDeleteConfirm(sectionName: string, index: number, fileIndex: number) {
    const alert = await this.alertController.create({
      message: 'この添付ファイルを削除しますか？',
      buttons: [
        {
          text: 'いいえ',
          role: 'cancel',
          cssClass: 'font-weight-bold',
        },
        {
          text: 'はい',
          cssClass: 'text-danger font-weight-normal',
          handler: () => {
            this.deleteFile(sectionName, index, fileIndex);
          },
        },
      ],
    });

    await alert.present();
  }

  /**
   * @description: デジタル署名する
   * @param sectionName
   * @param index
   * @return {type}
   */
  @Throttle()
  async toDigitalSign(sectionName: string, index: number) {
    if (this.itemDataDict[sectionName][index].optionObject.click != undefined) {
      this.runJavascript(this.itemDataDict[sectionName][index].optionObject.click);
    }
    if (
      this.itemDataDict[sectionName][index].optionObject.readonly == '1' &&
      !(this.isAction && this.itemDataDict[sectionName][index].editable)
    ) {
      // 編集不可の場合は、何もしないで処理を中止
      return;
    }

    if (this.platform.is('ios')) {
      this.selecImageService.signature().then((data) => {
        if (data instanceof Array) {
          this.itemDataDict[sectionName][index].defaultData = [data[0]];
        }
        //check customs
        this.runAssetTypeAndCheckJavascript();
        // リフレッシュ
        this.ref.detectChanges();
      });
    } else {
      // Use ionic implementation for other platform
      try {
        this.screenOrientationService.screenLandscape();
        const modal = await this.modalController.create({
          component: DigitalSignPadComponent,
        });
        await modal.present();
        const { data } = await modal.onWillDismiss();
        if (data != null) {
          this.itemDataDict[sectionName][index].defaultData = [data];
          //check customs
          this.runAssetTypeAndCheckJavascript();
          this.ref.detectChanges();
        }
      } catch (e) {
        console.log(e);
        // Shouldn't happen
      } finally {
        this.screenOrientationService.screenPortrait();
      }
    }
  }

  /**
   * 有効性チェック(false:エラーあり)
   * @param assetTypeList
   */
  private checkValidate(assetTypeList: AssetTypeItem[]): boolean {
    for (const assetType of assetTypeList) {
      if (assetType.isValid === false) {
        alert('入力エラーがあるのでご確認ください。');
        return false;
      }
      // 入力不可の項目はチェック対象外
      if (assetType.optionObject.readonly === '1') {
        if (assetType.inputFlg === '1') {
          // 資産の新規 or 編集の場合、編集不可セクションにある必須項目チェック
          if (this.isFromDetailOrEditPage) {
            if (
              assetType.defaultData == '' ||
              assetType.defaultData == undefined ||
              assetType.defaultData == null ||
              String(assetType.defaultData).trim() === ''
            ) {
              assetType.isShowMessage = true;
              if (assetType.itemDisplayName) {
                assetType.showMessage = assetType.itemDisplayName + 'を設定してください。';
              } else {
                assetType.showMessage = assetType.itemName + 'を設定してください。';
              }
              alert('入力エラーがあるのでご確認ください。');
              return false;
            }
          }
        }
        // 对于通过 customizeLogic setValue 进来的 通货、数字不可编辑内容
        // 需要判断是否不符合规则，如果不符合的情况则弹出 error 信息并拦截下一步操作
        // 数字或通货类型输入位数检查
        if (!validateNumericAndCurrencyField(assetType)) {
          return false;
        }
        continue;
      }

      console.log('zlt assetType: ' + JSON.stringify(assetType));
      // 必須チェック
      if (assetType.optionObject.readonly !== '1') {
        if (assetType.inputFlg === '1') {
          if (
            assetType.defaultData === '' ||
            assetType.defaultData === undefined ||
            assetType.defaultData === null ||
            (typeof assetType.defaultData === 'object' && Object.keys(assetType.defaultData).length === 0) ||
            (typeof assetType.defaultData === 'string' && String(assetType.defaultData).trim() === '')
          ) {
            if (assetType.itemDisplayName) {
              alert(assetType.itemDisplayName + 'を設定してください。');
            } else {
              alert(assetType.itemName + 'を設定してください。');
            }
            return false;
          }
        }
      }

      if (assetType.optionObject.readonly !== '1') {
        if (assetType.inputFlg === '1' && assetType.itemType === 'checkbox') {
          if (assetType.defaultData === '[]') {
            if (assetType.itemDisplayName) {
              alert(assetType.itemDisplayName + 'を設定してください。');
            } else {
              alert(assetType.itemName + 'を設定してください。');
            }
            return false;
          }
        }
      }

      //チェックボックス単数チェック
      if (assetType.optionObject.readonly !== '1') {
        if (assetType.inputFlg === '1' && assetType.itemType === 'checkbox') {
          if (
            assetType.defaultData === '' ||
            assetType.defaultData === undefined ||
            assetType.defaultData === null ||
            assetType.defaultData === '0'
          ) {
            if (assetType.itemDisplayName) {
              alert(assetType.itemDisplayName + 'を設定してください。');
            } else {
              alert(assetType.itemName + 'を設定してください。');
            }
            return false;
          }
        }
      }

      if (assetType.defaultData) {
        // 桁数チェック
        if (assetType.optionObject.maxlength && typeof assetType.defaultData === 'string') {
          if (assetType.defaultData.length > assetType.optionObject.maxlength) {
            if (assetType.itemDisplayName) {
              alert(`${assetType.itemDisplayName}を${assetType.optionObject.maxlength}文字以内に入力してください。`);
            } else {
              alert(`${assetType.itemName}を${assetType.optionObject.maxlength}文字以内に入力してください。`);
            }
            return false;
          }
        }

        // 数字或通货类型输入位数检查
        if (!validateNumericAndCurrencyField(assetType)) {
          return false;
        }

        // メールアドレスの有効性チェック
        if (assetType.itemType === 'email') {
          const reg = /^(\w+|[-+.])*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
          if (!reg.test(assetType.defaultData)) {
            if (assetType.itemDisplayName) {
              alert(`${assetType.itemDisplayName}のフォーマットが間違っています。`);
            } else {
              alert(`${assetType.itemName}のフォーマットが間違っています。`);
            }

            return false;
          }
        }
        // TODO 如果后期需要在保存时候加入地图校验，请把代码加到这里
        if (assetType.itemType === 'map') {
        }
      }
    }
    return true;
  }

  /**
   * @description: 入力したデータのObjectを返す
   * @return:
   */
  checkAndGetData(): Object {
    const assetTypeList: AssetTypeItem[] = [];
    let map: any = {};

    let itemDataDictCopy = JSON.parse(JSON.stringify(this.itemDataDict));
    for (const sectionName in itemDataDictCopy) {
      assetTypeList.push(...this.itemDataDict[sectionName]);
    }

    // データの有効性をチェックする
    if (!this.checkValidate(assetTypeList)) {
      return false;
    }

    assetTypeList.forEach((assetType) => {
      if (assetType.method !== '2') {
        assetType.itemValue = assetType.defaultData;
      }

      // 通貨/数値  の場合のチェック
      if (assetType.itemType === 'currency' || assetType.itemType === 'number') {
        if (assetType.defaultData == null || assetType.defaultData == '' || assetType.defaultData == undefined) {
          // This is intentional
        } else {
          const str: string = (function () {
            const tmp: string = _.toString(assetType.defaultData);
            if (tmp.trim() == '-0') {
              return '0';
            }
            if (tmp.startsWith('+')) {
              return tmp.substring(1);
            }
            return tmp;
          })();
          let a = String(str).split(',').join('');
          assetType.defaultData = a;
        }
      }
      // 計算 の場合のチェック
      if (assetType.itemType === 'calculate') {
        let itemOption = assetType.optionObject;
        if (!(itemOption.calculateType == undefined || itemOption.calculate == undefined)) {
          if (itemOption.calculateType == 'digital' || itemOption.calculateType == 'currency') {
            const str: string = assetType.defaultData;
            let a = String(str).split(',').join('');
            assetType.defaultData = a;
          }
        }
      }

      const arr = assetType.defaultData;
      if (assetType.itemType == 'checkbox' && arr == '[]') {
        map[assetType.itemName] = [];
      } else {
        map[assetType.itemName] = arr;
      }
    });
    // 在保存之前删除updatedDate, createdDate, 更新日
    const { updatedDate, createdDate, 更新日, ...newAsset } = map;
    console.log(
      '💡 ==== src/app/component/af-customize-view/af-customize-view.component.ts === dealData ====> ',
      newAsset,
    );
    return newAsset;
  }

  /**
   * 現在のデータを取得する
   */
  getCurrentData(): { [sectionName: string]: AssetTypeItem[] } {
    return this.itemDataDict;
  }

  /**
   * @description: 入力したデータのObjectを返す
   * @return:
   */
  checkAndGetDataWithTURL(): Object {
    const assetTypeList: AssetTypeItem[] = [];
    let map = {};

    let itemDataDictCopy = JSON.parse(JSON.stringify(this.itemDataDict));
    for (const sectionName in itemDataDictCopy) {
      assetTypeList.push(...this.itemDataDict[sectionName]);
    }

    // データの有効性をチェックする
    if (!this.checkValidate(assetTypeList)) {
      return false;
    }

    assetTypeList.forEach((assetType) => {
      if (assetType.method !== '2') {
        assetType.itemValue = assetType.defaultData;
      }
      const arr: Array<Object> = assetType.defaultData;
      map[assetType.itemName] = arr;
    });

    JSON.stringify(this.itemDataDict);
    return map;
  }

  /**
   * データ処理
   */
  async dealData(): Promise<Object> {
    const assetTypeList: AssetTypeItem[] = [];
    let map: any = {};
    for (let sectionName in this.itemDataDict) {
      assetTypeList.push(...this.itemDataDict[sectionName]);
    }

    for (let i = 0; i < assetTypeList.length; i++) {
      let assetType = assetTypeList[i];
      if (assetType.itemType === 'homeImage' || assetType.itemType === 'image' || assetType.itemType === 'file') {
        if (assetType.defaultData == null || assetType.defaultData == undefined || assetType.defaultData == '') {
          assetType.defaultData = [];
          assetType.changeBeforeDefaultData = [];
        }
      }
      if (assetType.itemType === 'master') {
        if (assetType.defaultData == null || assetType.defaultData == undefined || assetType.defaultData == '') {
          assetType.defaultData = {};
          assetType.changeBeforeDefaultData = {};
        }
      }
      // 通貨/数値  の場合のチェック
      if (assetType.itemType === 'currency' || assetType.itemType === 'number') {
        if (assetType.defaultData == null || assetType.defaultData == '' || assetType.defaultData == undefined) {
          // This is intentional
        } else {
          const str: string = _.toString(assetType.defaultData);
          let a = String(str).split(',').join('');
          assetType.defaultData = a;
        }
      }
      // 計算 の場合のチェック
      if (assetType.itemType === 'calculate') {
        let itemOption = assetType.optionObject;
        if (!(itemOption.calculateType == undefined || itemOption.calculate == undefined)) {
          if (itemOption.calculateType == 'digital' || itemOption.calculateType == 'currency') {
            const str: string = assetType.defaultData;
            let a = String(str).split(',').join('');
            assetType.defaultData = a;
          }
        }
      }
      if (assetType.itemType === 'master') {
        let masterInfo = await this.createMasterInfo(assetType);
        map[assetType.itemName] = masterInfo;
      } else {
        const arr: Array<Object> = assetType.defaultData;
        map[assetType.itemName] = arr;
      }
    }
    // 在保存之前删除updatedDate, createdDate, 更新日
    const { updatedDate, createdDate, 更新日, ...newAsset } = map;
    console.log(
      '💡 ==== src/app/component/af-customize-view/af-customize-view.component.ts === dealData ====> ',
      newAsset,
    );
    return newAsset;
  }

  /**
   * create master info
   */
  async createMasterInfo(assetType) {
    let masterInfo = {};
    let masterIdList = [];
    let displayList = {};
    let result = await this.actionService.getActionMasterData(JSON.parse(assetType.option).masterTypeId);
    if (result.code == 0) {
      console.log(result.layoutSettingList);
      result.layoutSettingList.forEach((item) => {
        masterIdList.push(item.itemId);
      });
      if (assetType.defaultData !== null && assetType.defaultData !== '' && assetType.defaultData !== undefined) {
        if (
          assetType.defaultData.display !== null &&
          assetType.defaultData.display !== '' &&
          assetType.defaultData.display !== undefined
        ) {
          let masterIds = Object.keys(assetType.defaultData.display);
          masterIds.forEach((mId) => {
            masterIdList.forEach((masterId) => {
              if (masterId == mId) {
                displayList[masterId] = assetType.defaultData.display[masterId];
              }
            });
          });
          masterInfo['masterId'] = assetType.defaultData['masterId'];
          masterInfo['display'] = displayList;
        }
      }
    }
    return masterInfo;
  }

  getUserInfoByTS() {
    let roleList = LRU.shared.get(StorageUtils.KEY_USER_ROLE_LIST);
    let groupId = [];
    if (_.isArray(roleList)) {
      groupId = roleList.map((role) => _.toNumber(role['roleId']));
    }
    return {
      userDisplayName: LRU.shared.get(StorageUtils.KEY_LAST_NAME) + ' ' + LRU.shared.get(StorageUtils.KEY_FIRST_NAME),
      userId: LRU.shared.get(StorageUtils.KEY_USER_ID),
      userName: LRU.shared.get(StorageUtils.KEY_USER_NAME),
      userGroupIds: groupId,
      getUserFormValue: function () {
        return {
          userId: this.userId,
          userName: this.userName,
        };
      },
    };
  }

  async getSQLQueryResultsTS(queryName, params = {}, that, instance) {
    try {
      if (typeof queryName != 'string') {
        throw new TypeError(`クエリ名は文字列型でなければなりません。クエリ名タイプ：${typeof queryName}`);
      } else if (!queryName) {
        throw new Error('クエリ名は必須項目です。');
      }
      if (typeof params != 'object') {
        throw new TypeError(`パラメータはオブジェクト型でなければなりません。パラメータタイプ：${typeof params}`);
      } else if (params === null) {
        throw new Error('パラメータはオブジェクト型でなければなりません。パラメータ：null');
      }
      await that.http.get(that.globalVariable.GetTenant, false, false, false, true).then(
        (response) => {
          if (response.tenant?.materialisticViewFlg != '1') {
            throw new Error('マテリアライドビュー機能の有効化が必要です。');
          }
        },
        () => {
          throw new Error('マテリアライドビュー機能の有効化が必要です。');
        },
      );
      let apiSettingList = [];
      await that.http
        .get(that.globalVariable.getApiSettingList + 'apiName=' + queryName, false, false, false, true)
        .then((reponse) => {
          apiSettingList = reponse.data;
        });
      let apiSetting = {};
      if (apiSettingList.length === 1) {
        apiSetting = apiSettingList[0];
      }
      if (!apiSetting['apiSettingId']) {
        throw new Error(`指定された ${queryName} という名前のクエリ設定は存在しません。`);
      } else {
        let apiQuery = apiSetting['apiQuery'];
        Object.keys(params).forEach((paramKey) => {
          let replaceParam = `{${paramKey}}`;
          apiQuery = apiQuery.replace(replaceParam, params[paramKey]);
        });
        let connectJson = {
          command: 'ExecuteQuery',
          connectionString: '',
          queryString: apiQuery,
          database: 'MySQL',
          parameters: [],
          timeout: 30000,
          connection: 'Connection',
        };
        await that.http.get(that.globalVariable.getReportDb, false, false, false, true).then((res) => {
          let db = res.data;
          let connectionString = `url=jdbc:mysql://${db.host}:${db.port}/${db.dbname}; database=${db.dbname};username=${db.user}; pwd=${db.password}`;
          connectJson.connectionString = connectionString;
        });
        return new Promise(async (resolve, reject) => {
          let reportHost = await StorageUtils.get(StorageUtils.KEY_API_REPORT_HOST);

          let body = new Blob([JSON.stringify(connectJson)]);
          that.http
            .postGetSqlResult(reportHost, body)
            .then(
              (rRet) => {
                resolve(rRet);
              },
              (reject) => {
                resolve({ success: false, message: 'システムエラーが発生しました。管理者にご連絡ください。' });
              },
            )
            .catch((err) => {
              resolve({ success: false, message: 'システムエラーが発生しました。管理者にご連絡ください。' });
            });
        });
      }
    } catch (e) {
      if (!e.message) {
        e.message = 'システムエラーが発生しました。管理者にご連絡ください。';
      }
      return Promise.reject({ success: false, message: e.message });
    }
  }

  async callOpenAPITS(path, method, params = {}, apiKey = null, that: AfCustomizeViewComponent, instance: any) {

    // pathチェック
    if (typeof path !== 'string') {
      let message = `API Path must be a string, received: ${typeof path}`;
      throw { msg: message };
    }
    if (!path?.trim()) {
      let message = 'API Path is required.';
      throw { msg: message };
    }

    // methodチェック
    if (typeof method !== 'string') {
      let message = `HTTP method must be an string, received: ${typeof method}`;
      throw { msg: message };
    }
    method = method.toUpperCase();
    const validMethods = ['GET', 'POST', 'PUT', 'DELETE']; // 有効なHTTPメソッド（PATCHは未対応）
    if (!validMethods.includes(method)) {
      let message = `Unsupported HTTP method: ${method}`;
      throw { msg: message };
    }

    if (typeof params != 'object') {
      throw { msg: `パラメータはオブジェクト型でなければなりません。パラメータタイプ：${typeof params}` };
    } else if (params === null) {
      throw { msg: 'パラメータはオブジェクト型でなければなりません。パラメータ：null' };
    }

    const requestMethod = MethodTypeEnum[method as keyof typeof MethodTypeEnum];

    const host = await StorageUtils.get(StorageUtils.KEY_API_OPENAPI_HOST);
    const zoneId = await StorageUtils.get(StorageUtils.KEY_ZONE_ID);
    const token = await StorageUtils.get(StorageUtils.KEY_TOKEN);
    const url = new URL(host + path);

    const headers = {
      'X-Z-ID': zoneId,
      Accept: 'application/json',
      'Content-Type': 'application/json',
      ...(apiKey ? { 'X-API-Key': apiKey } : { Token: token }),
    };

    // 构造请求体或查询参数
    let body: any = null;
    if (method === 'GET' && Object.keys(params).length > 0) {
      url.search = new URLSearchParams(params as Record<string, string>).toString();
    } else if (['POST', 'PUT'].includes(method) && Object.keys(params).length > 0) {
      body = params;
    }

    // APIリクエストを実行
    let response = null;
    try {
      response = await that.http.callOpenApi(url.toString(), requestMethod, headers, body);
    } catch (e) {
      response = e;
    }
    let bodyData = response?.body ?? response?.error ?? {};
    if (typeof bodyData === 'object') {
      bodyData = JSON.stringify(bodyData);
    }
    let respHeader = response.headers ?? {};
    if (typeof respHeader !== 'object') {
      respHeader = JSON.parse(respHeader);
    }

    return {
      ok: response.status >= 200 && response.status < 300,
      status: response.status,
      headers: respHeader,
      body: bodyData || '',
      json: function () {
        return JSON.parse(this.body);
      },
      text: function () {
        return this.body;
      },
      formData: function () {
        const formData = new FormData();
        const json = JSON.parse(this.body);
        for (const [key, value] of Object.entries(json)) {
          if (typeof value === 'string' || value instanceof Blob) {
            formData.append(key, value);
          } else if (typeof value === 'object' && value !== null) {
            formData.append(key, JSON.stringify(value));
          } else {
            formData.append(key, _.toString(value));
          }
        }
        return formData;
      },
    };
  }

  async apiProxyTS(
    url: string,
    method = 'GET',
    headers = {},
    body = '',
    that: AfCustomizeViewComponent,
    instance: any,
  ): Promise<any> {
    try {
      // URLチェック
      if (typeof url !== 'string') {
        throw new TypeError(`URL must be a string, received: ${typeof url}`);
      }
      if (!url?.trim()) {
        throw new Error('URL is required.');
      }

      // methodチェック
      if (typeof method !== 'string') {
        throw new TypeError(`HTTP method must be a string, received: ${typeof method}`);
      }
      const validMethods = ['GET', 'POST', 'PUT', 'DELETE'];
      if (!validMethods.includes(method.toUpperCase())) {
        throw new TypeError(`Unsupported HTTP method: ${method}`);
      }

      // headersチェック
      if (typeof headers !== 'object' || headers === null) {
        throw new TypeError(`Headers must be an object, received: ${headers === null ? 'null' : typeof headers}`);
      }
      const normalizedHeaders = Object.fromEntries(
        Object.entries(headers).map(([key, value]) => [key.toLowerCase(), value]),
      );

      // リクエストボディの前処理（POST, PUTの場合のみ）
      let serializedBody = '';
      if (['POST', 'PUT'].includes(method.toUpperCase())) {
        const contentType = _.toString(normalizedHeaders['content-type']).toLowerCase();
        if (!contentType) {
          throw new Error(`"Content-Type" header is required for ${method} requests.`);
        }
        const validContentTypes = [
          'application/json',
          'application/x-www-form-urlencoded',
          'application/xml',
          'text/xml',
          'text/plain',
        ];
        if (!validContentTypes.some((validContentType) => contentType.includes(validContentType))) {
          throw new TypeError(`Unsupported Content-Type: ${contentType}`);
        }

        if (body === null || body === undefined) {
          serializedBody = '';
        } else if (typeof body === 'string') {
          serializedBody = body;
        } else if (typeof body === 'object') {
          if (contentType.includes('application/json')) {
            serializedBody = JSON.stringify(body);
          } else if (contentType.includes('application/x-www-form-urlencoded')) {
            serializedBody = new URLSearchParams(body).toString();
          } else if (contentType.includes('application/xml') || contentType.includes('text/xml')) {
            throw new TypeError(`For Content-Type ${contentType}, body must be XML.`);
          } else if (contentType.includes('text/plain')) {
            throw new TypeError(`For Content-Type ${contentType}, body must be a string.`);
          }
        } else {
          throw new TypeError(`Body must be an object or string, received: ${typeof body}`);
        }
      }

      const body2 = new URLSearchParams();
      body2.set('url', url);
      body2.set('method', method);
      body2.set('headers', JSON.stringify(normalizedHeaders));
      body2.set('body', serializedBody);

      return new Promise(async (resolve, reject) => {
        try {
          const response = await that.http.postApiProxy('/secure/apiProxy', body2);

          resolve(response);
        } catch (error) {
          reject({ msg: `Error in promise: ${error.message}` });
        }
      });
    } catch (e) {
      let message: string;

      if (e instanceof Error) {
        message = e.message;
      } else if (typeof e === 'string') {
        message = e;
      } else {
        message = JSON.stringify(e) || 'Unknown error';
      }

      return Promise.reject({ msg: message });
    }
  }

  /**
   * 資産情報取得
   */
  async getAssetTS(assetId, that) {
    if (assetId && assetId !== '') {
      let result = await that.assetDetailService.getFindById(assetId);
      var asset;
      if (result.arrayAsset.length > 0) {
        asset = result.arrayAsset[0];
      }
      return asset;
    }
  }

  /**
   * 資産情報アップデート
   */
  async updateAssetTS(asset, that) {
    const assetText = await getNumbersCommaRemoved(
      asset?.assetText,
      asset?.assetTypeId,
      (that as AfCustomizeViewComponent).assetService,
      false,
    );
    let relatesString = await that.getRelationData(asset.assetId);
    let assetOld = await that.getAssetTS(asset.assetId, that);
    asset.modifiedDate = assetOld.modifiedDate;
    await that.assetDetailService.saveAssetdata(
      asset.assetTypeId,
      asset.assetId,
      assetText,
      asset.modifiedDate,
      relatesString,
      asset.barcode,
    );
  }

  async getRelationData(assetId) {
    //関連情報設定
    let result2 = await this.assetDetailService.getAssetRelationList(assetId);
    var dict: { [key: string]: AssetTypeItem[] } = {};
    result2.assetRelationList.forEach((assetRelation) => {
      //データ設定
      if (Object.keys(dict).includes(String(assetRelation.assetTypeId))) {
        dict[assetRelation.assetTypeId].push(assetRelation);
      } else {
        var itemList: AssetTypeItem[] = [];
        itemList.push(assetRelation);
        dict[String(assetRelation.assetTypeId)] = itemList;
      }
    });
    var relateListString = '';
    for (let item of Object.keys(dict)) {
      dict[item].forEach((data) => {
        relateListString = relateListString + data.assetId + ',';
      });
    }
    return relateListString;
  }

  /**
   * 資産新規作成
   */
  async insertAssetTS(asset, that) {
    var result;
    const assetText = await getNumbersCommaRemoved(
      asset?.assetText,
      asset?.assetTypeId,
      (that as AfCustomizeViewComponent).assetService,
      false,
    );
    await that.addAssetService
      .insertAssetData(asset.assetTypeId, asset.barcode, assetText)
      .then(async (data) => {
        result = 1;
      })
      .catch((err) => {
        result = 0;
      });
    return result;
  }

  /**
   * 資産Idを取得
   */
  getAssetIdTS(that) {
    if (that.assetId) {
      return that.assetId;
    } else {
      return null;
    }
  }

  /**
   * アクション名の取得
   * cancel 取消 sendBack　差戻し admit 承认　startNew 新規申請
   */
  getActionTS(that) {
    if (that) {
      return that.actionName;
    }
  }

  /**
   * 履歴情報を取得
   */
  async getAppurtenancesInformationTS(assetId, appurtenanceTypeId, count, that) {
    let startIndex: number = 0;
    // 履歴情報リストを取得する
    let resultAppurtenancesInfoData = await that.assetDetailService.getAppurtenancesInfoData(
      assetId,
      appurtenanceTypeId,
      String(startIndex),
      String(count),
    );
    let appurtenancesInformationList = resultAppurtenancesInfoData.appurtenancesInformationList;
    return appurtenancesInformationList;
  }

  /**
   * 履歴情報を新規作成
   */
  async insertAppurtenancesInformationTS(appurtenance, that) {
    const appurtenancesInformationText = await getNumbersCommaRemoved(
      appurtenance?.appurtenancesInformationText,
      appurtenance?.appurtenancesInformationTypeId,
      (that as AfCustomizeViewComponent).assetService,
      true,
    );
    let map = {};
    const appurtenancesInformationTextObj = JSON.parse(appurtenancesInformationText);
    const allKeys = Object.keys(appurtenancesInformationTextObj);
    allKeys.forEach((key) => {
      map[key] = appurtenancesInformationTextObj[key];
    });
    const userInfo = await that.getinfoService.getUserInfo();
    const user = userInfo.data;
    const firstName = user.firstName;
    const lastName = user.lastName;
    map['ユーザー名'] = lastName + ' ' + firstName;
    map['新規登録時間'] = getFormatDateTime(new Date());
    map['ロケーション'] = await StorageUtils.get(StorageUtils.KEY_LOCATION);

    await that.assetDetailService.getAppurtenancesInfoLayoutInsert(
      String(appurtenance.assetId),
      String(appurtenance.appurtenancesInformationTypeId),
      JSON.stringify(map),
    );
  }

  /**
   * 履歴情報をアップデート
   */
  async updateAppurtenancesInformationTS(appurtenance, that) {
    var appurtenancesInformationText = await getNumbersCommaRemoved(
      appurtenance?.appurtenancesInformationText,
      appurtenance?.appurtenancesInformationTypeId,
      (that as AfCustomizeViewComponent).assetService,
      true,
    );
    let appurtenanceOld = await that.getAppurtenancesInformationList(appurtenance.appurtenancesInformationId, that);
    appurtenance.modifiedDate = appurtenanceOld.modifiedDate;
    await that.assetDetailService.getAppurtenancesInfoLayoutUpdate(
      appurtenance.assetId,
      String(appurtenance.appurtenancesInformationTypeId),
      String(appurtenance.appurtenancesInformationId),
      appurtenancesInformationText,
      appurtenance.modifiedDate,
    );
  }

  async getAppurtenancesInformationList(appurtenancesInformationId, that) {
    var data;
    let appurtenanceOld = await that.assetDetailService.getAppurtenancesInformationListById(appurtenancesInformationId);
    if (appurtenanceOld.appurtenancesInformationList.length > 0) {
      data = appurtenanceOld.appurtenancesInformationList[0];
    }
    return data;
  }

  /**
   * get AppurtenancesInformation's id
   * @param that
   * @returns
   */
  getAppurtenancesInformationIdTS(that) {
    return that.appurtenancesInformationId ? that.appurtenancesInformationId : null;
  }

  /**
   * 資産リストを取得
   */
  async getWorkflowAssetListTS(that) {
    if (that.processInstanceId || that.taskId || that.processInstanceId == '' || that.taskId == '') {
      return [];
    }
    let dataNumAndNextPage = {
      processInstanceId: _.toNumber(that.processInstanceId),
      taskId: _.toNumber(that.taskId),
      scanState: undefined,
      skip: 0,
      rows: 99999,
      keyword: undefined,
      moreThenLimit: false,
    };
    const getAssetListData =
      await that.workflowGetApplicationWorkflowListService.getAssetsByKeywordInWfAssetListForAssignScan(
        dataNumAndNextPage,
      );
    const wfAssetList = getAssetListData.assetListDatas ?? [];
    return wfAssetList;
  }

  /**
   * WF名を取得
   */
  getWorkflowTaskNameTS(that) {
    if (that.stepName) {
      return that.stepName;
    }
  }

  async getAssetsByTypeTS(assetTypeId, rows, that) {
    if (assetTypeId && rows && that) {
      let newAssetList: Asset[] = [];
      newAssetList = await (that.assetService as AssetService).getAssetListData(String(0), String(rows), assetTypeId);
      return newAssetList;
    }
  }

  /**
   * マスターリストを取得
   */
  async getMasterValueListTS(masterTypeId, that) {
    if (masterTypeId && that) {
      var masterList = [];
      const result = await that.actionService.getMasterInfoById(masterTypeId);
      if (result.code == 0) {
        result.masterDetail.forEach((master) => {
          master['masterText'] = JSON.parse(master['masterText']);
          masterList.push(master);
        });
      }
      return masterList;
    }
  }

  async getMasterDefaultValue(masterTypeId, masterId, itemName) {
    var masterListDefaultValue = '';
    if (masterTypeId && masterId && itemName) {
      // 同じmasterTypeIdだったら、２度とAPIを呼び出さない
      if (this.masterResult && !(masterTypeId in this.masterResult)) {
        const result = await this.actionService.getMasterInfoById(masterTypeId, false);
        if (result.code == 0) {
          this.masterResult[masterTypeId] = result;
        }
      }
      let masterDetail: [] = this.masterResult[masterTypeId].masterDetail;
      masterDetail.forEach((master) => {
        if (!_.isEmpty(masterId) && !_.isEmpty(itemName)) {
          if (master['masterId'] == masterId) {
            let masterText = JSON.parse(master['masterText']);
            masterListDefaultValue = masterText[itemName];
          }
        }
      });
    }
    return masterListDefaultValue;
  }

  /**
   * date format
   */
  dateFormatTS(date, fmt) {
    let o = {
      'M+': date.getMonth() + 1,
      'd+': date.getDate(),
      'h+': date.getHours(),
      'm+': date.getMinutes(),
      's+': date.getSeconds(),
      S: date.getMilliseconds(),
    };
    if (/(y+)/.test(fmt)) {
      fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
    }
    for (let k in o) {
      if (new RegExp('(' + k + ')').test(fmt)) {
        fmt = fmt.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length));
      }
    }
    return fmt;
  }

  /**
   * 用于设定不显示某个section
   * @param sectionName
   * @param isShowSection
   * @param that
   * @param instance
   */
  toggleSectionTS(sectionName: string, isShowSection, that: AfCustomizeViewComponent, instance) {
    if (that.tempSectionSortItemArray.length == 0) {
      return;
    }
    // 如果 isShowSection 为 true，则添加 sectionName 到 sectionSortItemArray
    if (isShowSection) {
      // 从原始数组里面取出即将被添加的数据
      const sn = that.tempSectionSortItemArray.find((e) => e.sectionName == sectionName);
      // 添加
      that.sectionSortItemArray = Array.from(new Set([...that.sectionSortItemArray, sn]));
    } else {
      // 如果 isShowSection 为 false，则从 sectionSortItemArray 中移除指定 sectionName
      // 删除
      that.sectionSortItemArray = that.sectionSortItemArray.filter((fr) => fr.sectionName != sectionName);
    }
    // 重新创建排序后的 sectionNameArray
    that.sectionNameArray = that.createSectionNameArray(that.sectionSortItemArray);
  }

  /**
   * 用于变更list的显示内容
   * @param sectionName
   * @param list
   * @param instance
   */
  changeListItemTS(itemName, list, that, instance) {
    // 遍历各个section来寻找项目名相同的那个,进行值的更新
    that.sectionNameArray.forEach((item) => {
      instance[item].forEach((data) => {
        if (data.itemName === itemName) {
          // 获取要被改变的数组的原始数据
          var tempData = [];
          if (data.option) {
            let option = JSON.parse(data.option);
            tempData = _.cloneDeep(option.data);
          }
          // 取两个数组的交集
          var intersectionList = list.filter(function (v) {
            return tempData.indexOf(v) > -1;
          });
          data.optionObject.data = intersectionList ? intersectionList : [];
          // 如果这个详细的项目并不在,更新后的list里的时候要把这个显示值干掉
          if (!intersectionList.includes(data.defaultData)) {
            data.itemValue = '';
            data.defaultData = '';
          }
        }
      });
    });
  }

  /**
   * get current date time
   */
  nowTS() {
    return new Date();
  }

  async runJavascriptTogetAssetOrUpdateAssetOrInsertAsset(
    javaScriptText: string,
    isFromCreateNewAsset: boolean,
    actionName,
    appurtenancesInformationId = null,
    assetId = null,
  ) {
    console.log('actionName ==== ', actionName);
    if (actionName) {
      this.actionName = actionName;
    }
    if (appurtenancesInformationId) {
      this.appurtenancesInformationId = appurtenancesInformationId;
    }
    if (assetId) {
      this.assetId = assetId;
    }
    if (!javaScriptText || javaScriptText === '') {
      return;
    }
    try {
      await this.http.addLoadingQueue();
      StorageUtils.vallidationOn = false;
      this.resetItemDataDict();
      var str = this.globalVariable.CustomizedLogicJavascript + javaScriptText;
      // 数字和通货有问题时的提示信息
      var showMessageInfo;
      var result: any;
      try {
        var run;
        global.pageThis = this;
        eval(str);
        await run(this);
      } catch (err) {
        console.error(err);
        result = 'error';
        alert('JavaScriptの実行は失敗しました。');
      }
      var itemMsgIsShow;
      for (let sectionName in this.itemDataDict) {
        this.itemDataDict[sectionName].forEach((item) => {
          if (item.itemMsgIsShow) {
            itemMsgIsShow = true;
            // 过滤数字和通货的错误信息
            var isCurrencyOrNumber = item.itemType == 'number' || item.itemType == 'currency';
            if (isCurrencyOrNumber && showMessageInfo == undefined) {
              showMessageInfo = item.showMessage;
            }
          }
        });
      }
    } finally {
      await this.http.removeLoadingQueue();
    }
    if ('error' === result || this.alertMsgIsShow === true || itemMsgIsShow === true) {
      if (showMessageInfo) {
        // 延迟弹出，保证外层的 alert 先关闭避免奇怪的显示效果
        setTimeout(()=>{
          alert(showMessageInfo);
        },300);
      }
      this.alertMsgIsShow = false;
      return false;
    }
  }

  resetItemDataDict() {
    for (let sectionName in this.itemDataDict) {
      this.itemDataDict[sectionName].forEach((item) => {
        if (item.itemMsgIsShow) {
          item.itemMsgIsShow = false;
          item.isShowMessageTS = false;
        }
      });
    }
  }

  /**
   * トーク作成ボタン押下
   */
  @Throttle()
  clickCreateTalk(e: Event) {
    e.stopPropagation();
    Overlay.self.createTalk();
  }

  //担当者名を取得
  getUserNameForShow(item) {
    var userInfo = item.defaultData ? item.defaultData : {};
    if (!_.isEmpty(userInfo) && this.isJsonString(userInfo)) {
      userInfo = JSON.parse(userInfo);
    } else if (!_.isEmpty(userInfo) && _.isString(userInfo) && !this.isJsonString(userInfo)) {
      userInfo = {};
    }
    return userInfo['userName'] ? userInfo['userName'] : '';
  }

  // jsonStringかどうかをチェック
  isJsonString(str) {
    try {
      if (typeof JSON.parse(str) == 'object') {
        return true;
      }
    } catch (e) {
      return false;
    }
  }

  /**
   * check result is NaN or not
   * @param：result
   */
  checkIsNaN(result) {
    if (this.isNaN(result) || result == Infinity || result == 'Invalid date' || result == -Infinity) {
      return '';
    } else {
      return result;
    }
  }

  isNaN(result) {
    return result !== result;
  }

  /**
   * dayHandleByTS
   * @param：なし
   */
  dayHandleByTS(dateStr, quantity) {
    let date;
    if (dateStr instanceof Date) {
      date = dateStr;
    } else {
      date = getFormatDateReturnDate(dateStr);
    }
    date.setDate(date.getDate() + quantity);
    return date;
  }

  /**
   * monthHandleByTS
   * @param：なし
   */
  monthHandleByTS(dateStr, quantity) {
    let date;
    if (dateStr instanceof Date) {
      date = dateStr;
    } else {
      date = getFormatDateReturnDate(dateStr);
    }
    let day = date.getDate();
    date.setMonth(date.getMonth() + quantity);
    let day2 = date.getDate();
    if (day > day2) {
      return new Date(date.getFullYear(), date.getMonth(), 0);
    }
    return date;
  }

  /**
   * yearHandleByTS
   * @param：なし
   */
  yearHandleByTS(dateStr, quantity) {
    let date;
    if (dateStr instanceof Date) {
      date = dateStr;
    } else {
      date = getFormatDateReturnDate(dateStr);
    }
    date.setFullYear(date.getFullYear() + quantity);
    return date;
  }

  /**
   * nowByTS
   * @param：なし
   */
  nowByTS() {
    return new Date();
  }

  /**
   * dayBetweenByTS
   * @param：なし
   */
  dayBetweenByTS(dateStr, dateStr2) {
    let date1;
    if (dateStr instanceof Date) {
      date1 = dateStr;
    } else {
      date1 = getFormatDateReturnDate(dateStr);
    }
    let date2;
    if (dateStr2 instanceof Date) {
      date2 = dateStr2;
    } else {
      date2 = getFormatDateReturnDate(dateStr2);
    }
    let interval = date2.getTime() - date1.getTime();
    let days = Math.floor(interval / (24 * 3600 * 1000));
    return days;
  }

  /**
   * monthBetweenByTS
   * @param：なし
   */
  monthBetweenByTS(dateStr, dateStr2) {
    let date1;
    if (dateStr instanceof Date) {
      date1 = dateStr;
    } else {
      date1 = getFormatDateReturnDate(dateStr);
    }
    let date2;
    if (dateStr2 instanceof Date) {
      date2 = dateStr2;
    } else {
      date2 = getFormatDateReturnDate(dateStr2);
    }
    let interval = date2.getTime() - date1.getTime();
    let days = Math.floor(interval / (30 * 24 * 3600 * 1000));
    return days;
  }

  /**
   * yearBetweenByTS
   * @param：なし
   */
  yearBetweenByTS(dateStr, dateStr2) {
    let date1;
    if (dateStr instanceof Date) {
      date1 = dateStr;
    } else {
      date1 = getFormatDateReturnDate(dateStr);
    }
    let date2;
    if (dateStr2 instanceof Date) {
      date2 = dateStr2;
    } else {
      date2 = getFormatDateReturnDate(dateStr2);
    }
    let interval = date2.getTime() - date1.getTime();
    let days = Math.floor(interval / (365 * 24 * 3600 * 1000));
    return days;
  }

  /**
   * @description: setValue
   * @param {type}
   * @return:
   */
  setValueByTS(itemName: string, itemValue: any, instance) {
    var itemDataDict = instance;
    if (instance.itemDataDict) {
      itemDataDict = instance.itemDataDict;
    }
    for (let sectionName in itemDataDict) {
      const itemDic: any[] = itemDataDict[sectionName];
      for (let index = 0; index < itemDic.length; index++) {
        const item = itemDic[index];
        if (item.itemName != itemName) {
          continue;
        }
        if (itemValue == null || itemValue == undefined) {
          continue;
        }
        if (itemValue instanceof Array || itemValue instanceof Object || typeof itemValue == 'boolean') {
          item.defaultData = itemValue;
          item.valueForShow = itemValue;
          item.itemValue = itemValue;
          continue;
        }
        let iv = itemValue;
        if (typeof iv != 'string') {
          try {
            iv = iv.toString();
          } catch (error) {
            iv = '';
          }
        }
        // 数字或通货类型输入位数检查
        if (item.itemType === 'number' || item.itemType === 'currency') {
          iv = processNumericAndCurrency(iv);
          const valideResult = validateNumericAndCurrency(iv,true, item.inputFlg == '1');
          if (valideResult !== '') {
            item.isShowMessageTS = true;
            item.itemMsgIsShow = true;
            if (item.itemDisplayName) {
              item.showMessage = `${item.itemDisplayName}は${valideResult}`;
            } else {
              item.showMessage = `${item.itemName}は${valideResult}`;
            }
          } else {
            item.isShowMessageTS = false;
            item.itemMsgIsShow = false;
            item.showMessage = '';
          }
          if (item.itemType === 'number') {
            if (iv == null || iv == undefined || iv == '') {
              // This is intentional
            } else {
              if (_.toString(iv).includes(',')) {
                // This is intentional
              } else {
                if (checkIsNumber(iv)) {
                  const option = JSON.parse(item.option);
                  const numberCommaDecimalPoint = Number(option.numberCommaDecimalPoint); // 有没有逗号分隔符0没有、1有
                  const numberDecimalPoint = Number(option.numberDecimalPoint); // 小数点的位数
                  const str: string = _.toString(iv);
                  iv = dealNumber(numberCommaDecimalPoint, numberDecimalPoint, str, true);
                  setPercentageUnit(item, iv, option);
                }
              }
            }
          }
          // 通货
          if (item.itemType === 'currency') {
            if (iv == undefined || iv == null || iv == '') {
              // This is intentional
            } else {
              if (String(iv).includes(',')) {
                // This is intentional
              } else {
                let option = JSON.parse(item.option);
                let numberCommaDecimalPoint = 3;
                let numberDecimalPoint = Number(option['currencyDecimalPoint']); //小数点的位数
                let str: string = iv;
                iv = dealNumber(numberCommaDecimalPoint, numberDecimalPoint, toFixed(str), true);
              }
            }
          }
        }
        item.defaultData = iv;
        item.valueForShow = iv;
        item.itemValue = iv;
      }
    }
  }

  /**
   * @description: getValue
   * @param {type}
   * @return:
   */
  getValueByTS(itemName: string, instance) {
    var value = '';
    var itemDataDict = instance;
    if (instance?.itemDataDict) {
      itemDataDict = instance.itemDataDict;
    }
    for (let sectionName in itemDataDict) {
      itemDataDict[sectionName].forEach((item) => {
        if (item.itemName === itemName) {
          value = item.defaultData;
        }
      });
    }
    return value;
  }

  /**
   * マスタ値を取得する
   */
  getMasterValueByTS(itemName, masterItemName, instance) {
    let value = '';
    var itemDataDict = instance;
    if (instance.itemDataDict) {
      itemDataDict = instance.itemDataDict;
    }
    for (let sectionName in itemDataDict) {
      itemDataDict[sectionName].forEach((item) => {
        if (item.itemName === itemName) {
          item.optionObject.masterDisplayItems.forEach((masterDisplayItem) => {
            if (
              masterDisplayItem &&
              masterDisplayItem.itemName &&
              masterDisplayItem.itemId &&
              masterDisplayItem.itemName === masterItemName
            ) {
              if (item.defaultData.display && masterDisplayItem.itemId) {
                value = item.defaultData.display[masterDisplayItem.itemId];
              }
            }
          });
        }
      });
    }
    return value;
  }

  /**
   * @description: readonly
   * @param {type}
   * @return:
   */
  readonlyByTS(itemName, instance) {
    var itemDataDict = instance;
    if (instance.itemDataDict) {
      itemDataDict = instance.itemDataDict;
    }
    for (let sectionName in itemDataDict) {
      itemDataDict[sectionName].forEach((item) => {
        if (item.itemName === itemName) {
          item.optionObject.readonly = '1';
        }
      });
    }
  }

  /**
   * @description: canWrite
   * @param {type}
   * @return:
   */
  canWriteByTS(itemName, instance) {
    var itemDataDict = instance;
    if (instance.itemDataDict) {
      itemDataDict = instance.itemDataDict;
    }
    // console.log("🚀 ~ file: af-customize-view.component.ts:4070  itemName: "+itemName+"  ~global.pageThis.permissionsDict:", global.pageThis.permissionsDict)
    for (let sectionName in itemDataDict) {
      itemDataDict[sectionName].forEach((item) => {
        if (item.itemName === itemName) {
          // 先检查有米有对应的权限,
          let hasPermission = true; // 此处默认为true 是为了兼容老逻辑. 做兜底处理.

          if (
            global &&
            global.pageThis &&
            global.pageThis.permissionsDict &&
            global.pageThis.permissionsDict.hasOwnProperty(itemName)
          ) {
            const value = global.pageThis.permissionsDict[itemName];
            if (value === 'false') {
              // 兼容部分传递之后的场景.
              hasPermission = false;
            } else {
              hasPermission = !!value;
            }
            // console.log("🚀 ~ file: af-customize-view.component.ts:4078 ~ "+itemName+" hasPermission:", hasPermission)
          }
          if (hasPermission) {
            item.optionObject.readonly = '0';
          }
        }
      });
    }
  }

  /**
   * @description: showItemMsg
   * @param {type}
   * @return:
   */
  showItemMsgByTS(itemName, msg, instance) {
    var itemDataDict = instance;
    if (instance.itemDataDict) {
      itemDataDict = instance.itemDataDict;
    }
    for (let sectionName in itemDataDict) {
      itemDataDict[sectionName].forEach((item) => {
        if (item.itemName === itemName) {
          item.isShowMessageTS = true;
          item.showMessage = msg;
          item.itemMsgIsShow = true;
        }
      });
    }
  }

  /**
   * @description: clearItemMsg
   * @param {type}
   * @return:
   */
  clearItemMsgByTS(itemName, instance) {
    var itemDataDict = instance;
    if (instance.itemDataDict) {
      itemDataDict = instance.itemDataDict;
    }
    for (let sectionName in itemDataDict) {
      itemDataDict[sectionName].forEach((item) => {
        if (item.itemName === itemName) {
          item.isShowMessageTS = false;
          item.showMessage = '';
          item.itemMsgIsShow = false;
        }
      });
    }
  }

  /**
   * @description: showMsg
   * @param {type}
   * @return:
   */
  showMsgByTS(type, that, msg) {
    if (that && that.alertMsgIsShow !== undefined) {
      that.alertMsgIsShow = true;
    }
    alert(msg);
  }

  /**
   * @description: Javascriptクリック処理
   * @param {type}
   * @return:
   */
  runJavascript(javaScriptText: string) {
    var str = this.globalVariable.CustomizedLogicJavascript + javaScriptText + 'onClick()';
    try {
      global.pageThis = this;
      eval(str);
    } catch (err) {}
  }

  checkJavascript(value: any, javaScriptText: string) {
    var str = this.globalVariable.CustomizedLogicJavascript + javaScriptText + 'validation("' + value + '",instance)';
    var result: any;
    try {
      global.pageThis = this;
      result = eval(str);
    } catch (err) {
      result = '';
    }
    return result;
  }

  doJavascript(javaScriptText: string) {
    var str = this.globalVariable.CustomizedLogicJavascript + javaScriptText;
    var result: any;
    try {
      global.pageThis = this;
      result = eval(str);
    } catch (err) {
      console.error('doJavascript', err);
      result = '';
    }
    return result;
  }

  /**
   * 判断是否是home画像
   * @param data
   */
  isHomeImage(data) {
    if (data == undefined) {
      return false;
    }
    return data;
  }

  @Throttle()
  async toDeleteRfidCode(sectionName: string, index: string) {
    const alert = await this.alertController.create({
      header: 'RFIDコードを削除しますか？',
      message: '削除後、画面右上の保存ボタンをタップすると、編集が確定します。',
      buttons: [
        {
          text: 'キャンセル',
          role: 'cancel',
          cssClass: 'font-weight-normal',
        },
        {
          text: 'OK',
          cssClass: 'font-weight-bold',
          handler: () => {
            // データを更新する
            this.itemDataDict[sectionName][index].defaultData = '';
            this.itemDataDict[sectionName][index].valueForShow = '';
            this.itemDataDict[sectionName][index].itemValue = '';
            //check customs
            this.runAssetTypeAndCheckJavascript();
            // リフレッシュ
            this.ref.detectChanges();

            this.toastr.success('RFIDコードを削除しました', '', {
              positionClass: 'toast-center-center',
              timeOut: 1000,
            });
          },
        },
      ],
    });
    await alert.present();
  }

  getAssetName(): string {
    let assetName = '';

    for (let sectionName of this.sectionNameArray) {
      let section = this.itemDataDict[sectionName] ?? [];
      for (let item of section) {
        if (item.itemName == 'assetName') {
          return item.defaultData ?? '';
        }
      }
    }

    return assetName;
  }

  // 判断是否是必须
  isNeedCheck(item: any) {
    if (item.inputFlg === '1' || (item.editable !== undefined && item.editable)) {
      return true;
    }

    return false;
  }

  async getTurl(filePath) {
    let resultData = await this.assetDetailService.getTurl(filePath, false, false);
    return resultData.data.getUrl;
  }

  async errorImgLoad(assetItem: AssetTypeItem, count) {
    assetItem.defaultData[count].turl = await this.getTurl(assetItem.defaultData[count].url);
  }

  showMasterValue(mobielDisplay, item = null) {
    let value = mobielDisplay.itemValue ?? '';
    if (mobielDisplay.type == 'checkbox') {
      if (value == '1') {
        value = 'あり';
      } else if (value == '0' || value == '') {
        value = 'なし';
      }
    } else if (mobielDisplay.type == 'number') {
      if (mobielDisplay.percentage && value != '') {
        value = mobielDisplay.percentage;
      }
      if (mobielDisplay.unit && value != '') {
        value = value + ' ' + mobielDisplay.unit;
      }
    }
    return value;
  }
  /**
   * 编辑值 动值或者静值
   */
  editValueDynamicOrStatic = (sectionName: string, index: string | number, data: any) => {
    console.log('editValueDynamicOrStatic => sectionName ==>', sectionName);
    console.log('editValueDynamicOrStatic => index ==>', index);
    console.log('editValueDynamicOrStatic => data ==>', data);
    console.log('editValueDynamicOrStatic => itemDataDict ==>', this.itemDataDict[sectionName][index]);
    const itemData = this.itemDataDict[sectionName][index];
    if (_.isEmpty(itemData)) {
      return;
    }
    const itemType = itemData['itemType'];
    if (_.isEmpty(itemType)) {
      return;
    }
    const editable = itemData['editable'];
    let method: string = itemData['method'];
    if (_.isNumber(method)) {
      method = _.toString(method);
    }
    if (_.isEmpty(itemType)) {
      return;
    }
    if (_.isEmpty(method)) {
      return;
    }
    const arrayMatchTypes = enumToArray(ITEM_TYPE_ENUM);

    if (method === '2' && editable && arrayMatchTypes.includes(itemType)) {
      this.itemDataDict[sectionName][index].method = '1';
    }

    // if (itemType === ITEM_TYPE_ENUM.INPUT) {

    // }
    // if (itemType === ITEM_TYPE_ENUM.NUMBER) {

    // }
    // if (itemType === ITEM_TYPE_ENUM.LIST) {

    // }
    // if (itemType === ITEM_TYPE_ENUM.TEXTAREA) {

    // }
    // if (itemType === ITEM_TYPE_ENUM.USER_SELECT) {

    // }
    // if (itemType === ITEM_TYPE_ENUM.MASTER) {

    // }
    // if (itemType === ITEM_TYPE_ENUM.CURRENCY) {

    // }
  };

  valueForShow(item) {
    if (this.isAssetItemValue(item)) {
      return item.valueForShow;
    } else {
      return item.defaultData;
    }
  }
  showPercentage(item) {
    if (this.isAssetItemValue(item)) {
      return item.valueForShow;
    }
    let value = item.defaultData;
    if (item.percentage && value != '') {
      value = item.percentage;
    }
    if (item.itemType === 'calculate') {
      value = _.toString(value);
      if (value.includes('.')) {
        // 截取小数部分，判断位数如果小于 12位则不处理，超过 12位则截取
        const numberSplit = value.split('.');
        const number = numberSplit[0];
        const decimal = numberSplit[1];
        if (decimal.length <= 12) {
          return value;
        } else {
          return `${number}.${decimal.substring(0, 12)}`;
        }
      }
    }
    return value;
  }

  showUnit(item) {
    // showPercentage(item)已经处理过了，不要重复处理
    if (this.isAssetItemValue(item)) {
      return '';
    }
    let unit = '';
    let value = item.defaultData;
    if (item.unit && value != '') {
      unit = item.unit;
    }
    return unit;
  }

  scrollToTop() {
    setTimeout(() => {
      window.scrollTo(0, document.body.scrollTop + 1);
      document.body.scrollTop >= 1 && window.scrollTo(0, document.body.scrollTop - 1);
    }, 200);
  }

  setToDateComplete(
    selectInfo: {
      valueState: 'cancel' | 'today' | 'kria' | 'finished';
      valueTime: {
        yearValues: string;
        monthValues: string;
        dayValues: string;
        hourValues: string;
        minuteValues: string;
      };
    },
    sectionName: string,
    index: number,
    itemType: string,
  ) {
    this.date_record_key = sectionName;
    this.date_record_index = index;
    let data = '';
    const { valueState: selectType, valueTime: pickdatetime } = selectInfo;
    if (selectType == 'cancel') {
      const beforeModifiedDate = this.itemDataDict[this.date_record_key][this.date_record_index].defaultData;
      // 同じ日付を代入（値が変更されない）する処理ではpickerで選択された日付に変更されてしまうので、一時的にnullを設定する（強制的に値を変更する）
      this.itemDataDict[this.date_record_key][this.date_record_index].defaultData = null;
      // 変更前の日付を設定し直す
      setTimeout(() => {
        this.itemDataDict[this.date_record_key][this.date_record_index].defaultData = beforeModifiedDate;
      }, 20);
    }

    if (selectType == 'today') {
      const selectedDate = new Date(); // cancel時のために選択した日付を保持
      let formate = '';
      if (this.itemDataDict[this.date_record_key][this.date_record_index].optionObject.dateType == 'dateTime') {
        formate = getFormatDateTime(selectedDate);
      } else {
        formate = getFormatDate(selectedDate);
      }
      this.itemDataDict[this.date_record_key][this.date_record_index].defaultData = null;
      data = formate;

      setTimeout(() => {
        this.itemDataDict[this.date_record_key][this.date_record_index].defaultData = formate;
        this.itemDataDict[this.date_record_key][this.date_record_index].itemValue = formate;
        this.itemDataDict[this.date_record_key][this.date_record_index].valueForShow = formate;
        this.editValueDynamicOrStatic(this.date_record_key, this.date_record_index, undefined);
        this.runAssetTypeAndCheckJavascript();
      }, 20);
    }

    if (selectType == 'kria') {
      if (!this.clearedItems) {
        this.clearedItems = new Array();
      }
      this.isCleared = true;
      data = '';
      this.itemDataDict[this.date_record_key][this.date_record_index].defaultData = '';
      this.itemDataDict[this.date_record_key][this.date_record_index].changeBeforeDefaultData = '';
      this.itemDataDict[this.date_record_key][this.date_record_index].valueForShow = '';
      this.itemDataDict[this.date_record_key][this.date_record_index].dataForInit = '';
      this.clearedItems.push(this.itemDataDict[this.date_record_key][this.date_record_index]);

      this.runAssetTypeAndCheckJavascript();
    }

    if (selectType == 'finished') {
      const year = pickdatetime.yearValues;
      const month = pickdatetime.monthValues;
      const day = pickdatetime.dayValues;
      const hour = pickdatetime.hourValues;
      const minute = pickdatetime.minuteValues;
      if (this.itemDataDict[this.date_record_key][this.date_record_index].optionObject.dateType == 'dateTime') {
        this.itemDataDict[this.date_record_key][this.date_record_index].defaultData =
          year + '/' + month + '/' + day + ' ' + hour + ':' + minute;
        this.itemDataDict[this.date_record_key][this.date_record_index].itemValue =
          year + '/' + month + '/' + day + ' ' + hour + ':' + minute;
        this.itemDataDict[this.date_record_key][this.date_record_index].valueForShow =
          year + '/' + month + '/' + day + ' ' + hour + ':' + minute;
      } else {
        this.itemDataDict[this.date_record_key][this.date_record_index].defaultData = year + '/' + month + '/' + day;
        this.itemDataDict[this.date_record_key][this.date_record_index].itemValue = year + '/' + month + '/' + day;
        this.itemDataDict[this.date_record_key][this.date_record_index].valueForShow = year + '/' + month + '/' + day;
      }
      data = this.itemDataDict[this.date_record_key][this.date_record_index].defaultData;
      this.editValueDynamicOrStatic(this.date_record_key, this.date_record_index, undefined);
      this.runAssetTypeAndCheckJavascript();
    }

    if (this.itemDataDict[sectionName][index].optionObject.click != undefined) {
      this.runJavascript(this.itemDataDict[sectionName][index].optionObject.click);
    }

    if (!_.isEmpty(data) && data.length >= 10) {
      this.itemDataDict[this.date_record_key][this.date_record_index].dataForInit = getDateFromDateType(
        '',
        data,
        itemType,
        [],
        '',
      );
    } else {
      return;
    }
    this.itemDataDict[this.date_record_key][this.date_record_index].defaultData =
      this.itemDataDict[this.date_record_key][this.date_record_index].dataForInit;
    this.itemDataDict[this.date_record_key][this.date_record_index].changeBeforeDefaultData =
      this.itemDataDict[this.date_record_key][this.date_record_index].dataForInit;
    this.editValueDynamicOrStatic(sectionName, index, data);

    //check customs
    this.runAssetTypeAndCheckJavascript();
  }

  /**
   * 用于给日期控件赋值
   * @param assetTypeItem
   * @returns {any}
   */
  genDataExample(assetTypeItem: AssetTypeItem) {
    if (!_.isEmpty(assetTypeItem?.defaultData)) {
      return assetTypeItem?.defaultData;
    } else if (!_.isEmpty(assetTypeItem?.value)) {
      return assetTypeItem?.value;
    } else {
      return '';
    }
  }
  conversionOfValue(defaultData: any, checkItem: any): boolean {
    if (_.isArray(defaultData)) {
      return defaultData.includes(checkItem);
    }
    if (!_.isString(defaultData)) {
      return _.toString(defaultData).includes(checkItem);
    }
    return defaultData.includes(checkItem);
  }
}
