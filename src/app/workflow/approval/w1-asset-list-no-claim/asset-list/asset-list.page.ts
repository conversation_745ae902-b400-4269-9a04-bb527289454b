import {ChangeDete<PERSON><PERSON><PERSON>, <PERSON>mponent, <PERSON><PERSON><PERSON>, On<PERSON><PERSON><PERSON>, OnInit} from '@angular/core';
import {ActivatedRoute, NavigationExtras} from '@angular/router';
import {Alert<PERSON>ontroller, ModalController, NavController, Platform} from '@ionic/angular';
import {Subject, Subscription} from 'rxjs';
import {ActiveToast} from 'ngx-toastr';
import {
  Progress,
  WorkflowGetApplicationWorkflowListService,
} from 'src/app/services/workflow/get-applicationWorkflowList/workflow-getApplicationWorkflowList.service';
import {WorkflowEngineTask} from 'src/app/models/workflow/WorkflowEngineTask';
import {StorageUtils} from 'src/app/utils/storage-utils';
import {Asset} from 'src/app/models/asset';
import {ScanService} from 'src/app/services/scan/scan.service';
import {WorkflowActionsService} from 'src/app/services/workflow/workflow-actions.service';
import {AssetService} from 'src/app/services/asset/asset.service';
import {WfPageWidgetService} from 'src/app/services/workflow/workflow-service/other/wf-page-widget.service';
import {WorkflowAssetColumn} from '../../../../models/workflow/WorkflowAssetColumn';
import {AssetTypeItem} from '../../../../models/assetTypeItem';
import {HttpGetWorkflowListService} from '../../../../httpUtils/workflow/http-getWorkflowList.service';
import {HttpAssetService} from '../../../../httpUtils/asset/http-aseet.service';
import {App} from '@capacitor/app';
import {Throttle} from 'src/app/utils/throttle.decorator';
import {LoginService} from 'src/app/services/login/login.service';
import {AssetDetailService} from 'src/app/services/assetdetail/asset-detail.service';
import {MypageGetuserinfoService} from 'src/app/services/mypage/get-userInfo/mypage-getuserinfo.service';
import * as _ from 'lodash';
import {HttpUtilsService} from '../../../../utils/http-utils.service';
import {
  starNoClaimWorkflowScanBacode,
} from 'src/app/services/workflow/workflow-service/workflow-conmmon-scan.service';
import {VListPage} from '../../../../v-list/v-list.page';
import { Keyboard } from '@capacitor/keyboard';

@Component({
  selector: 'app-asset-list',
  templateUrl: './asset-list.page.html',
  styleUrls: ['./asset-list.page.scss'],
})
export class AssetListPage extends VListPage<any> implements OnInit, OnDestroy {
  assetItems: {
    name: string;
    id: number;
    location: string;
    homeImage: string;
    assetId: string;
    finishScan: boolean;
    rawAssetList: Asset;
    assetTotalCount: number;
    assetScanedCount: number;
  }[] = []; // 資産の一覧
  willBeScanAssetItems: {
    name: string;
    id: number;
    location: string;
    homeImage: string;
    assetId: string;
    finishScan: boolean;
    rawAssetList: Asset;
  }[] = []; // 資産の一覧
  assetList: Asset[] = []; // 資産リスト
  [x: string]: any;

  order: 'descend' | 'ascend' = 'descend'; // 並び順
  orderSubject: Subject<'ascend' | 'descend'> = new Subject(); // 並び順対象
  orderState = this.orderSubject.asObservable(); // 並び順状態
  search: string = ''; // 検索テキスト
  progress: Progress = 'all'; // 進捗の絞り込み
  activeToast: ActiveToast<number>; // 表示中のtoastr
  routeObserver: Subscription; // 遷移を監視するオブジェクト
  searchConditionDataList: any; // 検索条件データリスト
  willBescan: string = ''; // 資産スキャン待ち個数
  firstLoad: boolean = true; // 初期化か
  rawFormData; // オリジナルフォームデータ
  locationInfo: string; // 場所情報
  wfModel: WorkflowEngineTask; // ワークフロー承認情報
  showItems; // 表示項目
  websock; // web socket
  toast;
  scanResult;
  assetListDatas = [];
  webSocketScanedAssetIdList: string[] = [];
  scanedAssetIdList: string[] = [];
  showSendBackButton: boolean = false;
  showRejectButton: boolean = false;
  showUnclaimButton: boolean = false;
  backDestination: string = '';
  comments = [];
  confirmData;
  isMultiScanTask: boolean = false;
  isAllScaned;
  isPermission;
  isUserTask;
  interval;
  isFromeWakeUp = false;
  isReloaded = false;
  showAssetLocation: boolean = false; // 資産情報表示画面で「場所」が表示されるか
  isCountingType: boolean = true; // 個数管理場合
  scanedAssetidListTemp = [];
  scanedAssetBarcodesListTemp = [];
  scanedAssetDataListTemp = [];
  scanedAssetListTemp = [];

  assetTypeItemMap: { [key: number]: AssetTypeItem };
  private assetNameDisplayName: string;
  private identityCodeDisplayName: string;
  assetColumns: WorkflowAssetColumn[] = [];

  private dynamicTaskInfo = [];
  private dynamicTantousha = [];
  stepName;
  workflowScript;
  private isInitHomeImage = false;
  /** 是否有权限一时保存 */
  isPermissionWF = false;
  // 判断键盘状态
  isKeyboardVisible: boolean = false;
  updateAssetAction: (processInstanceId: string, taskId: string) => Promise<void>;

  constructor(
    private scanService: ScanService,
    activatedRoute: ActivatedRoute,
    private workflowGetApplicationWorkflowListService: WorkflowGetApplicationWorkflowListService,
    navController: NavController,
    private workflowActionsService: WorkflowActionsService,
    public cd: ChangeDetectorRef,
    private assetService: AssetService,
    private httpGetWorkflowListService: HttpGetWorkflowListService,
    private httpAssetService: HttpAssetService,
    private loginService: LoginService,
    private assetDetailService: AssetDetailService,
    private getinfoService: MypageGetuserinfoService,
    private http: HttpUtilsService,
    private alertController: AlertController,
    private modalController: ModalController,
    private platform: Platform,
    ngZone: NgZone,
  ) {
    super(navController, activatedRoute, ngZone);
  }

  ngOnDestroy(): void {
    console.log('asset-list销毁');
  }

  /**
   * 初期化
   * @param {type} なし
   */
  ngOnInit() {
    super.ngOnInit();
    this.activatedRoute.queryParams.subscribe(async (params) => {
      console.log('asset-list创建');
      if (this.firstLoad) {
        //リスト画面から
        this.assetListDatas = this.assetList = params['assetList'];
        this.dynamicTantousha = params['dynamicTantousha'];
        this.dynamicTaskInfo = params['dynamicTaskInfo'];
        this.rawFormData = params['rawFormData'];
        this.total = this.rawFormData['allAssetCount'];
        this.willBescan = this.rawFormData['noScanningAssetCount'];
        this.isCountingType = params['rawFormData']['updateAmountShow'] ?? WfPageWidgetService.WF_DEFAULT_AMOUNT_TYPE;
        this.wfModel = params['rawWorkFlowModel'];
        this.wfDict = params['wfDict'];
        this.formPostVariables = params['formPostVariables'];
        this.comments = params['comments'];
        this.confirmData = params['confirmData'];
        this.actions = this.rawFormData['actions'];
        this.stepName = params['stepName'];
        this.workflowScript = params['workflowScript'];
        this.updateAssetAction = params['updateAssetAction'];
        if (this.rawFormData['taskType'] != null) {
          this.isUserTask = this.rawFormData['taskType'] === 'USER';
        }
        this.isPermissionWF = this.actions['isPermission'] ?? false;

        try {
          await this.http.addLoadingQueue();
          this.showAssetLocation = await this.workflowActionsService.showTenantAssetLocation();
          await this.loadAssetColumnsData();

          await this.reloadData(
            this.assetListDatas,
            this.scanedAssetIdList,
            this.webSocketScanedAssetIdList,
            function (isAllScaned) {
              // This is intentional
            },
          );
        } finally {
          await this.http.removeLoadingQueue();
        }

        console.log('====== init ');
        console.log(this.formPostVariables);

        this.firstLoad = false;
      }

      if (this.isScanTask || this.isUserTask) {
        await this.loadData(this.processInstanceId, this.taskID);
      }
    });
    /// アプリの状態を監視
    this.addAppListener();

    Keyboard.addListener('keyboardWillShow', () => {
      this.isKeyboardVisible = true;
    });

    Keyboard.addListener('keyboardWillHide', () => {
      this.isKeyboardVisible = false;
    });
  }

  /**
   * 遅延
   * @param {type} ms
   */
  delay(ms) {
    return new Promise((res) => setTimeout(res, ms));
  }

  /**
   * 画面を表示される時
   * @param {type} argName I am argument argName.
   */
  ionViewDidEnter() {
    console.log('ionViewDidEnter');
    // this.clearData();
    setTimeout(() => {
      this.initWebSocket();
    });
  }

  /**
   * 資産表示項目取得
   */
  async loadAssetColumnsData() {
    const assetItemResult = await this.httpAssetService.httpGetItemByAssetType(
      String(this.wfModel.assetTypeIdWithFirstWf),
    );
    this.assetTypeItemMap = assetItemResult.assetItemList.reduce((map, obj) => {
      map[obj.itemId] = obj;
      // 資産名の表示名を取得
      if (obj.itemName === 'assetName') {
        this.assetNameDisplayName = obj.itemDisplayName;
      }
      // 識別コードの表示名を取得
      if (obj.itemName === 'identityCode') {
        this.identityCodeDisplayName = obj.itemDisplayName;
      }
      return map;
    }, {});
    this.assetColumns = await this.httpGetWorkflowListService.httpGetWorkflowAssetColumn(
      this.wfModel.workflowId,
      this.wfModel.processDefinitionId,
    );
  }

  /**
   * 画面を遷移する時
   * @param {type} argName I am argument argName.
   */
  ionViewWillLeave() {
    console.log('ionViewWillLeave()');
    this.isFromeWakeUp = false;
    App.removeAllListeners();
    typeof this.websock.close == 'function' && this.websock.close();
    this.websock = null;
    this.clearTimer();
  }

  /// アプリの状態を監視
  addAppListener() {
    App.removeAllListeners();
    App.addListener('appStateChange', async ({ isActive }) => {
      // state.isActive contains the active state
      console.log('App state changed. Is active?', isActive);
      if (isActive === true) {
        this.isFromeWakeUp = true;
        const that = this;
        setTimeout(function () {
          that.initWebSocket();
        }, 1000);
      } else {
        this.isFromeWakeUp = false;
        typeof this.websock.close == 'function' && this.websock.close();
        this.websock = null;
        this.clearTimer();
      }
    });
  }

  /**
   * クリアタイマー
   * @param {type} なし
   */
  clearTimer() {
    window.clearInterval(this.interval);
  }

  /**
   * データを再読み込む
   * @param {type} assetListDatas
   * @param {type} scanedAssetIdList
   * @param {type} webSocketScanedAssetIdList
   * @param {type} callback
   */
  async reloadData(assetListDatas, scanedAssetIdList, webSocketScanedAssetIdList, callback) {
    await this.http.addLoadingQueue(true);
    try {
      this.clearData();
      this.itemsForSearch = this.showItems = await this.updateAssetList(
        assetListDatas,
        this.progress,
        scanedAssetIdList,
        webSocketScanedAssetIdList,
      );
      let value = this.filterItems;
      this.itemsList = _.isEmpty(value) ? [] : value;
      // await this.updateAssetAction(this.processInstanceId, this.taskID);

      callback(this.isAllScaned);
      this.doAfterRequest(0);
      this.cd.detectChanges();
    } finally {
      this.http.removeLoadingQueue(true);
    }
  }

  /**
   * データを読み込む
   */
  async loadData(processInstanceId: string, taskId: string, isLoading: boolean = true) {
    if (isLoading) {
      await this.http.addLoadingQueue();
    }
    const result = await this.workflowGetApplicationWorkflowListService.MultiTaskReload(processInstanceId, taskId);
    this.showAssetLocation = await this.workflowActionsService.showTenantAssetLocation();

    this.assetListDatas = result['assetListDatas'];

    let actions = result['actions'];

    if (actions['rejectionStatus'] === '1' && (actions['isPermission'] ?? false)) {
      this.showRejectButton = true;
    }

    if (actions['isPermission'] !== undefined) {
      this.isPermission = actions['isPermission'] ?? false;
    }

    if (
      (actions['sendBackStatus'] === '1' || actions['sendBackStatus'] === '2') &&
      (actions['isPermission'] ?? false)
    ) {
      this.showSendBackButton = true;
    }

    if (actions['sendBackDestination'] != null) {
      this.backDestination = actions['sendBackDestination'];
    }

    if (actions['isMultiScanTask'] != null) {
      this.isMultiScanTask = actions['isMultiScanTask'];
    }

    if (this.rawFormData['taskType'] != null) {
      this.isUserTask = this.rawFormData['taskType'] === 'USER';
    }

    this.reloadData(
      this.assetListDatas,
      this.scanedAssetIdList,
      this.webSocketScanedAssetIdList,
      function (isAllScaned) {
        // This is intentional
      },
    );
    if (isLoading) {
      await this.http.removeLoadingQueue();
    }
  }

  /**
   * ワークフローを絞り込み
   */
  get filterItems(): any {
    if (this.showItems === null) {
      return null;
    }
    return this.filter(this.search, this.progress);
  }

  /**
   * 検索結果なし
   */
  get isEmptyResult(): boolean {
    if (this.showItems === null) {
      return true;
    }
    return this.filterItems.length <= 0;
  }

  /**
   * 名前検索（簡易的な文字列一致）と進捗で絞り込み
   * @param search 検索するワード
   * @param progress 進捗
   */
  filter(search: string, progress: Progress = 'all'): any {
    const formatted = this.formatSearch(search); // 検索ワード
    let retVal = this.itemsForSearch;

    let fuzzyQuery = (list, keyWord) => {
      var reg = new RegExp(keyWord);
      var arr = [];
      for (var i = 0; i < list.length; i++) {
        const rawAsset = list[i]['rawAssetList'];
        // 为空放弃
        if (_.isEmpty(rawAsset)) {
          continue;
        }
        let assetText = rawAsset['assetText'];
        // 为空放弃
        if (_.isEmpty(assetText)) {
          continue;
        }
        if (_.isString(assetText)) {
          assetText = JSON.parse(assetText);
        }
        // 取到所有val并转换成string合集
        const allValStr = _.join(
          _.values(assetText).filter((val) => _.isString(val) || _.isNumber(val)),
          '   ',
        );
        // 为空放弃
        if (_.isEmpty(allValStr)) {
          continue;
        }
        // 检索
        if (reg.test(allValStr)) {
          arr.push(list[i]);
        }
      }
      return arr;
    };

    // 検索
    if (search.length > 0) {
      let find = []; // 見つかった資産

      // 検索ワードに該当するインデックスを取得
      find = fuzzyQuery(this.itemsForSearch, search);

      // this.itemsForSearch.forEach((item, index) => {
      //   if (
      //     (item.name != null && this.formatSearch(item.name.toLowerCase()).indexOf(formatted.toLowerCase()) >= 0) ||
      //     (item.id != null && this.formatSearch(item.id.toLowerCase()).indexOf(formatted.toLowerCase()) >= 0)
      //   ) {
      //     find.push(this.itemsForSearch[index]);
      //   }
      // });

      retVal = find;
      if (find.length > 0) {
        this.showItems = find;
      }
    } else {
      this.showItems = this.itemsForSearch;
    }

    // 处理home画像，且仅处理1次
    if (retVal !== undefined && this.isInitHomeImage === false) {
      this.isInitHomeImage = true;
      let initHomeImage = async () => {
        this.turlObj = {};
        for (const item of retVal) {
          const index = retVal.indexOf(item);
          if (item.rawAssetList) {
            if (item.rawAssetList.assetText) {
              item.homeImgLoaded = false;
              // assetText转obj
              let assetText = JSON.parse(item.rawAssetList.assetText);
              try {
                // ホーム画像の更新 初始化home画像
                const assetText = JSON.parse(item.rawAssetList.assetText);
                var findItems = [];
                this.filterMobileVisibleImage(findItems);
                // 查找到image布局信息
                if (findItems) {
                  for (const iterator of findItems) {
                    // 通过布局信息itemName获取iamge array对象
                    var imageObj = assetText[iterator.itemName];
                    if (typeof imageObj === 'string' && imageObj !== '') {
                      imageObj = JSON.parse(imageObj);
                    }
                    if (imageObj instanceof Array) {
                      if (imageObj.length > 0) {
                        const homeImage = imageObj.find((value) => {
                          return value.isHomeImage === true;
                        });
                        if (homeImage) {
                          let isView = true;
                          // 获取item中的option并转换json
                          let optionObj = JSON.parse(iterator.option);
                          let sectionPrivateGroups = optionObj.sectionPrivateGroups;
                          if (sectionPrivateGroups) {
                            isView = await this.loginService.visiblePermissionsCheck(sectionPrivateGroups);
                          }
                          if (!isView) {
                            item.homeImage = undefined;
                            console.log('blog-item.turl is undefined');
                            continue;
                          }
                          item.homeImage = homeImage;
                          console.log('blog-item.turl is ' + item.homeImage.turl);
                          break;
                        }
                      }
                    }
                  }
                }
              } catch (error) {
                console.log(error);
                item.homeImage = undefined;
                console.log('blog-item.turl is undefined');
              }
            }
          }
        }
        this.cd.detectChanges();
      };
      initHomeImage();
    }
    return retVal;
  }

  // 未スキャン
  @Throttle()
  public openNotYet({ event }) {
    this.progress = 'not-yet';
    this.more = [];
    this.reloadData(
      this.assetListDatas,
      this.scanedAssetIdList,
      this.webSocketScanedAssetIdList,
      function (isAllScaned) {
        // This is intentional
      },
    );
  }

  // 全て
  @Throttle()
  public openDone({ event }) {
    this.progress = 'all';
    this.more = [];
    this.reloadData(
      this.assetListDatas,
      this.scanedAssetIdList,
      this.webSocketScanedAssetIdList,
      function (isAllScaned) {
        // This is intentional
      },
    );
  }

  /**
   * 検索用にフォーマット
   * @param str
   */
  public formatSearch(str: string): string {
    return this.hiraToKana(this.zenToHan(str));
  }

  /**
   * ひらがな→カタカナ
   * @param str
   */
  private hiraToKana(str: string): string {
    return str.replace(/[\u3041-\u3096]/g, (match) => {
      const chr = match.charCodeAt(0) + 0x60;
      return String.fromCharCode(chr);
    });
  }

  /**
   * 英数の全角→半角
   * @param str
   */
  private zenToHan(str: string): string {
    return str.replace(/[A-Za-z0-9]/g, (s) => {
      return String.fromCharCode(s.charCodeAt(0) + 65248);
    });
  }

  /**
   * 資産リスト更新する
   * @param {type} assetListDatas
   * @param {type} progress
   * @param {type} scanedAssetIdList
   * @param {type} webSocketScanedAssetIdList
   */
  async updateAssetList(assetListDatas, progress, scanedAssetIdList, webSocketScanedAssetIdList) {
    /// 並び順を調整
    var assetList: any;
    assetList = assetListDatas;

    this.assetItems.length = 0;
    this.willBeScanAssetItems.length = 0;
    this.isAllScaned = !!this.allMustScan;

    for (const asset of assetList) {
      // 個数管理場合
      if (this.isCountingType) {
        if (this.allMustScan) {
          // 全件扫描 当前扫描数量和资产数一致时扫描完成
          asset['scanState'] = asset['savedAssetAmount'] == asset['assetAmount'];
        } else {
          // 非全件扫描 当前扫描数量>=1为扫描完成
          asset['scanState'] = asset['savedAssetAmount'] >= 1;
        }

        if (this.allMustScan) {
          if (asset['savedAssetAmount'] < asset['assetAmount']) {
            this.isAllScaned = false;
          }
        } else {
          if (asset['savedAssetAmount'] > 0) {
            this.isAllScaned = true;
          }
        }
      } else {
        // 個体管理場合
        if (this.allMustScan) {
          if (asset['scanState'] !== '1' && asset['scanState'] !== true) {
            this.isAllScaned = false;
          }
        } else {
          if (asset['scanState'] === '1' || asset['scanState'] === true) {
            this.isAllScaned = true;
          }
        }
      }

      //セクション関連の設定
      let assetDic = JSON.parse(asset['assetText']);

      var homeImageItem = undefined;

      let initHomeImage = async () => {
        this.turlObj = {};
        try {
          // ホーム画像の更新 初始化home画像
          const assetText = assetDic;
          var findItems = [];
          this.filterMobileVisibleImage(findItems);
          // 查找到image布局信息
          if (findItems) {
            for (const iterator of findItems) {
              // 通过布局信息itemName获取iamge array对象
              var imageObj = assetText[iterator.itemName];
              if (typeof imageObj === 'string' && imageObj !== '') {
                imageObj = JSON.parse(imageObj);
              }
              if (imageObj instanceof Array) {
                if (imageObj.length > 0) {
                  const homeImage = imageObj.find((value) => {
                    return value.isHomeImage === true;
                  });
                  if (homeImage) {
                    let isView = true;
                    // 获取item中的option并转换json
                    let optionObj = JSON.parse(iterator.option);
                    let sectionPrivateGroups = optionObj.sectionPrivateGroups;
                    if (sectionPrivateGroups) {
                      isView = await this.loginService.visiblePermissionsCheck(sectionPrivateGroups);
                    }
                    if (!isView) {
                      console.log('blog-item.turl is undefined');
                      continue;
                    }
                    homeImageItem = homeImage;
                    console.log('blog-item.turl is ' + homeImageItem.turl);
                    break;
                  }
                }
              }
            }
          }
        } catch (error) {
          console.log(error);
          console.log('blog-item.turl is undefined');
        }
        this.cd.detectChanges();
      };

      await initHomeImage();

      let assetScanedCount = 0;
      if (this.isCountingType) {
        assetScanedCount = asset['savedAssetAmount'];
      } else {
        if (asset['scanState'] == '1' || asset['scanState'] == true) {
          assetScanedCount = 1;
        } else {
          assetScanedCount = 0;
        }
      }

      let tempItem = {
        assetItems: asset['assetItemList'],
        name: assetDic['assetName'],
        id: assetDic['identityCode'],
        assetId: asset['assetId'],
        location: assetDic['location'],
        homeImage: homeImageItem,
        finishScan: asset['scanState'] == '1' || asset['scanState'] == true ? true : false,
        rawAssetList: asset,
        assetScanedCount: assetScanedCount,
        assetTotalCount: asset['assetAmount'],
      };

      this.assetItems.push(tempItem);

      if (this.isCountingType) {
        if (this.allMustScan) {
          if (asset['savedAssetAmount'] < asset['assetAmount']) {
            this.willBeScanAssetItems.push(tempItem);
          }
        } else {
          if (asset['savedAssetAmount'] == 0) {
            this.willBeScanAssetItems.push(tempItem);
          }
        }
      } else {
        if (asset['scanState'] === '0') {
          this.willBeScanAssetItems.push(tempItem);
        }
      }
    }
    this.willBeScanAssetItems = _.uniqBy(this.willBeScanAssetItems, (value) => value.assetId);
    this.willBescan = String(this.willBeScanAssetItems.length);
    if (progress === 'not-yet') {
      return this.willBeScanAssetItems;
    } else {
      this.assetItems = _.uniqBy(this.assetItems, (value) => value.assetId);
      return this.assetItems;
    }
  }

  private filterMobileVisibleImage(findItems: any[]) {
    for (let assetTypeItemMapKey in this.assetTypeItemMap) {
      let value = this.assetTypeItemMap[assetTypeItemMapKey];
      if (value.itemType === 'image' && value.mobileFlg == '1') {
        findItems.push(value);
      }
    }
  }

  /**
   * バーコードリスト
   * @param {type} なし
   */
  get barcodeList() {
    var res = [];
    this.rawFormData['assetListDatas'].forEach((value) => {
      res.push(value['barcode'].toString());
    });

    return res;
  }

  /**
   * リフレッシュ
   * @param {type} argName I am argument argName.
   */
  @Throttle()
  refresh() {
    this.loadData(this.processInstanceId, this.taskID);
  }

  /**
   * スキャン画面に遷移する
   * @param {type}
   */
  @Throttle(1500)
  toScan() {
    App.removeAllListeners();
    if (this.websock) {
      typeof this.websock.close == 'function' && this.websock.close();
      this.websock = null;
    }
    this.clearTimer();
    const setScanData = {
      progressDefinitionId: this.wfModel['processDefinitionId'],
      assetListId: this.rawFormData['assetListId'].toString(),
      assetDataList: this.assetItems,
      barcodeList: this.barcodeList,
      locationInfo: this.locationInfo,
      processInstanceId: this.confirmData['processInstanceId'],
      taskId: this.confirmData['taskId'],
      assetTypeId: this.wfModel.assetTypeIdWithFirstWf,
      scanType: undefined,
      isFromLocationSettingPage: false,
      isFromScannedPagesUnfinishedAssetsPage: false,
    };
    starNoClaimWorkflowScanBacode(
      { data: setScanData, scanService: this.scanService, modalController: this.modalController },
      (resultData) => {
        this.processNoClaimScanResult(resultData);
      },
    );
  }

  /**
   * サブプロセスなしスキャン結果
   * @param {type} result
   */
  processNoClaimScanResult(result: any) {
    /// アプリの状態を監視
    this.addAppListener();
    this.initWebSocket();
    this.scanedAssetIdList = result['assetIdLIst'] ?? [];
    if (this.allMustScan === false && this.scanedAssetIdList.length > 0) {
      this.isAllScaned = true;
    }
    this.loadData(this.processInstanceId, this.taskID);
  }

  /**
   * apiHost
   * @param {type} なし
   */
  async apiHost() {
    var apihost = await StorageUtils.get<string>(StorageUtils.KEY_API_SOCKET_HOST);
    // 修改 zoneid  规则同 apiHost.
    return apihost;
  }

  /**
   * WebSocketの初期化
   * @param {type} なし
   */
  async initWebSocket() {
    if (!this.websock) {
      const wsuri = await this.apiHost();
      let token = await StorageUtils.get(StorageUtils.KEY_TOKEN);
      const websocket = (this.websock = new WebSocket(wsuri, [token, this.processInstanceId]));

      this.websock.onopen = () => {
        console.log('this.websock.onopen');
      };

      this.websock.onclose = () => {
        console.log('WebSocket connection closed. can not send messages.');
      };

      this.websock.onopen = () => {
        const that = this;
        if (that.isFromeWakeUp == true) {
          that.loadData(that.processInstanceId, that.taskID);
        }
        console.log('WebSocket connection opened. Ready to send messages.');
        // 30秒間隔でメッセージを出す
        this.interval = window.setInterval(function () {
          console.log('==============will    this.websock.send(Pin)');
          if (websocket != null) {
            websocket.send('message');
            console.log('==============this.websock.send(Pin)');
          }
        }, 30000);
      };

      this.websock.onmessage = (value) => {
        const that = this;
        console.log('❌❌❌ get message from server ---- websocket');
        this.webSocketScanedAssetIdList = JSON.parse(value.data)['scannedAssetIds'];
        console.log();
        that.loadData(that.processInstanceId, that.taskID, false);
        this.cd.detectChanges();
      };
    }
  }

  // processInstanceIdを読み込む
  get processInstanceId() {
    return this.wfModel.processInstanceId;
  }

  // taskIDを読み込む
  get taskID() {
    return this.wfModel.id;
  }

  // allMustScanを読み込む
  get allMustScan() {
    if (!this.isUserTask && this.actions) {
      if (this.actions['allMustScan'] != null) {
        return this.actions['allMustScan'] ?? false;
      }
    }
    return false;
  }

  // scanTaskIsLockedを読み込む
  get scanTaskIsLocked() {
    if (!this.isUserTask && this.actions) {
      if (this.actions['scanTaskIsLocked'] != null) {
        return this.actions['scanTaskIsLocked'] ?? false;
      }
    }
    return false;
  }

  // isScanTaskを読み込む
  get isScanTask() {
    if (this.rawFormData && this.rawFormData['taskType'] != null) {
      return this.rawFormData['taskType'] === 'SCAN';
    }
    return false;
  }

  // フーターを表示されるか
  get isShowFooter() {
    return (
      (this.scanTaskIsLocked && this.confrimStatus) ||
      this.showRejectButton ||
      this.showSendBackButton ||
      (!this.scanTaskIsLocked && this.isAllScaned && this.isScanTask) ||
      (this.confrimStatus && this.isUserTask)
    );
  }

  //承認状態
  get confrimStatus() {
    if (this.actions != null) {
      return (this.isUserTask || this.isAllScaned) && (this.actions['isPermission'] ?? false);
    }
  }

  /**
   * 現物確認完了
   */
  @Throttle(1500)
  scanTasktoFinish() {
    this.workflowActionsService.updateScanTaskLock(
      this.taskID,
      'LOCK',
      '現物確認完了に変更',
      'not-yet',
      null,
      null,
      this.comments,
      this.wfDict,
      this.processInstanceId,
    );
  }

  /**
   * 現物確認を解除
   */
  @Throttle(1500)
  restartScantask() {
    this.workflowActionsService.updateScanTaskLock(
      this.taskID,
      '',
      '完了状態を解除',
      'not-yet',
      null,
      null,
      this.comments,
      this.wfDict,
      this.processInstanceId,
    );
  }

  /**
   * @description: sendBack
   * @param {type}
   * @return {type}
   */
  @Throttle(1500)
  async sendBack() {
    try {
      await this.http.addLoadingQueue();
      let isCheck: boolean = await this.workflowActionsService.checkAndGetData(this.wfDict, false);
      if (isCheck == false) {
        return;
      }
    } finally {
      await this.http.removeLoadingQueue();
    }
    this.workflowActionsService.sendBackFromApproval({
      assetDict: this.wfDict,
      comments: this.comments,
      sendBackDestination: this.backDestination,
      taskId: this.taskID,
      progress: 'not-yet',
      perviewComponent: null,
      workflowScript: this.workflowScript,
      stepName: this.stepName,
      callBack: null,
      that: null,
      isFromAssetListPage: true,
      buttonName: null,
    });
  }

  /**
   * @description: 普通
   * @param {type}
   * @return {type}
   */
  @Throttle(1500)
  async rejection() {
    try {
      await this.http.addLoadingQueue();
      let isCheck: boolean = await this.workflowActionsService.checkAndGetData(this.wfDict, false);
      if (isCheck == false) {
        return;
      }
    } finally {
      await this.http.removeLoadingQueue();
    }
    this.workflowActionsService.comfrimWithAssets({
      assetDict: this.wfDict, // assetDict
      comments: this.comments, // comments
      processInstanceId: this.processInstanceId, // processInstanceId
      assetList: this.confirmData.assetList, // assetList
      assetListDatas: this.confirmData.assetListDatas, // assetListDatas
      assetListTitle: this.confirmData.assetListTitle, // assetListTitle
      assetTypeIdWithFirstWf: this.wfModel.assetTypeIdWithFirstWf, // assetTypeIdWithFirstWf
      taskId: this.taskID, // taskId
      progress: 'not-yet', // progress
      perviewComponent: null, // perviewComponent
      workflowScript: this.workflowScript, // workflowScript
      stepName: this.stepName, // stepName
      callBack: null, // callBack
      that: null, // that
      isFromAssetListPage: true, // isFromAssetListPage
      isApproval: false, // isApproval
      backPage: null,
      buttonName: null,
    });
  }

  /**
   * @description: 普通
   * @param {type}
   * @return {type}
   */
  @Throttle(1500)
  async approval() {
    if (!this.checkDynamicTaskCanSubmit()) {
      const alert = await this.alertController.create({
        message: '各ステップの承認者を選択してください。',
        buttons: [
          {
            text: 'はい',
            role: 'cancel',
            cssClass: 'font-weight-bold',
            handler: async () => {},
          },
        ],
      });
      await alert.present();
      return;
    } else {
      if (!_.isEmpty(this.dynamicTaskInfo) && !_.isEmpty(this.dynamicTantousha)) {
        for (let i = 0; i < this.dynamicTaskInfo.length; i++) {
          const element = this.dynamicTaskInfo[i];
          if (element['assignDynamicType'] === 'group') {
            element['authorizerId'] = this.dynamicTantousha[i]['roleId'];
            element['authorizerName'] = this.dynamicTantousha[i]['roleName'];
            console.log('新增Group', element);
          } else {
            element['authorizerId'] = this.dynamicTantousha[i]['userId'];
            element['authorizerName'] =
              this.dynamicTantousha[i]['lastName'] + ' ' + this.dynamicTantousha[i]['firstName'];
            console.log('新增User', element);
          }
        }
      }
    }

    try {
      await this.http.addLoadingQueue();
      let isCheck: boolean = await this.workflowActionsService.checkAndGetData(this.wfDict, false);
      if (isCheck == false) {
        return;
      }
    } finally {
      await this.http.removeLoadingQueue();
    }
    const confirmDataClone = _.cloneDeep(this.confirmData);
    confirmDataClone['allMustScan'] = this.allMustScan;
    confirmDataClone['totalAssetAmountCount'] = this.rawFormData['totalAssetAmountCount'];
    confirmDataClone['totalSavedAssetAmountCount'] = this.rawFormData['totalSavedAssetAmountCount'];
    const taskDefKey = this.actions['taskDefKey'];
    this.workflowActionsService.comfrimWithMultiScanCompleteNoSubprocess({
      assetDict: this.wfDict,
      comments: this.comments,
      processInstanceId: this.processInstanceId,
      taskId: this.taskID,
      progress: 'done',
      isApproval: true,
      backPage: this.workflowActionsService.approvalURL,
      assignDynamicData: this.dynamicTaskInfo,
      perviewComponent: null,
      workflowScript: this.workflowScript,
      confirmData: confirmDataClone,
      stepName: this.stepName,
      callBack: null,
      that: this,
      isFromAssetListPage: true,
      taskDefKey: taskDefKey,
      buttonName: null,
    });
  }

  // 判断是否符合提交条件
  // 返回true时满足条件可以提交
  private checkDynamicTaskCanSubmit() {
    if (this.dynamicTaskInfo && this.dynamicTaskInfo.length > 0) {
      if (this.dynamicTantousha && this.dynamicTantousha.length > 0) {
        return this.dynamicTaskInfo.length === this.dynamicTantousha.length;
      } else {
        return false;
      }
    } else {
      return true;
    }
  }

  /**
   * 戻る
   */
  @Throttle()
  back() {
    this.navController.navigateBack('/workflow/approval/w1-asset-list/view', {
      queryParams: { goToListPage: { processInstanceId: this.processInstanceId, taskId: this.taskId } },
    });
  }

  /**
   * 資産詳細に遷移する
   */
  @Throttle()
  clickAsset(assetID) {
    let clickable = this.rawFormData['taskType'] == 'SCAN';
    if (!clickable) {
      return;
    }

    this.itemsForSearch.forEach(async (value) => {
      if (value.assetId == assetID) {
        let assetTypeID = String(this.wfModel.assetTypeIdWithFirstWf);
        const result1 = await this.assetService.getAssetAuthority(assetID, assetTypeID);
        if (result1.count === 1) {
          let asset = value.rawAssetList;
          let navigationExtras: NavigationExtras = {
            queryParams: {
              assetText: asset.assetText,
              assetId: asset.assetId,
              assetTypeId: assetTypeID,
              fromScanPage: 'wf',
              backUrl: '/workflow/asset-list-no-claim/asset-list',
            },
          };
          this.navController.navigateForward('/asset/detail', navigationExtras);
        } else {
          alert('資産が削除された又は権限が変更された可能性があるので、処理できません。');
        }
      }
    });
  }

  /**
   * @description:amountActionTaskUpdateItemName
   * @param {type}
   * @return {type}
   */
  @Throttle()
  async minusClick(item: any) {
    if (this.isKeyboardVisible) {
      Keyboard.hide();
      return;
    }
    if (
      !this.workflowActionsService.checkAssetCount({
        assetScanedCount: item.assetScanedCount - 1,
        assetTotalCount: item.assetTotalCount,
      })
    ) {
      return;
    }
    await this.updateScanCount(item.assetId, 0);
  }

  /**
   * @description:
   * @param {type}
   * @return {type}
   */
  @Throttle()
  async plusClick(item: any) {
    if (this.isKeyboardVisible) {
      Keyboard.hide();
      return;
    }
    if (
      !this.workflowActionsService.checkAssetCount({
        assetScanedCount: _.toNumber(item.assetScanedCount) + 1,
        assetTotalCount: _.toNumber(item.assetTotalCount),
      })
    ) {
      return;
    }
    await this.updateScanCount(item.assetId, 1);
  }

  /**
   * @description:
   * @param {type}
   * @return {type}
   */
  async updateCount(item: any, $event) {
    const oldValue = _.cloneDeep(item);
    let a = await this.workflowActionsService.updateCount(item, $event, false, undefined, false);
    if (!a) {
      if (_.toNumber(item['assetScanedCount']) === 0) {
        item['assetScanedCount'] = 1;
      } else {
        let number = _.toNumber(oldValue['oldAssetScanedCount']);
        item['assetScanedCount'] = number != 0 ? number : 1;
      }
    }
    if (a !== false || item.assetScanedCount !== 0) {
      await this.updateScanCount(item.assetId, 2, item.assetScanedCount);
    }
  }

  private lastInputElement: HTMLInputElement;

  /**
   * 当手动输入时候获取点击输入框获取焦点
   * @param item any
   */
  updateCountFocus(item: any) {
    // 当用户输入错误数量时候，还原之前的数量准备
    item['oldAssetScanedCount'] = item['assetScanedCount'];
    this.lastInputElement = document.activeElement as HTMLInputElement;
  }

  /**
   * 資産数の更新
   */
  async updateScanCount(assetId: number, operation: number, amount: number = undefined) {
    try {
      await this.http.addLoadingQueue();
      await this.workflowActionsService.updateScanCount(
        this.processInstanceId,
        this.taskID,
        this.rawFormData['assetListId'].toString(),
        assetId,
        operation,
        amount,
      );
      await this.loadData(this.processInstanceId, this.taskID, false);
    } finally {
      await this.http.removeLoadingQueue();
    }
  }

  // @Throttle() 无网络请求将此注解去掉
  clickMore($event: MouseEvent, assetIndex: number, b: boolean) {
    $event.stopPropagation();
    this.onMoreClick(assetIndex, b);
    return false;
  }

  // 检索时用于记录命中条目总数
  countOfSearchHit: number = 0;

  // 判断是会否有home画像
  hasHomeImage(item) {
    return item.homeImage !== undefined;
  }

  async getTurl(filePath) {
    let resultData = await this.assetDetailService.getTurl(filePath);
    return resultData.data.getUrl;
  }

  async errorImgLoad(item) {
    item.homeImage.turl = await this.getTurl(item.homeImage['url']);
  }

  /**
   * @description: 一时保存
   * @param {type}
   * @return {type}
   */
  @Throttle()
  async saveTemporaryFromScanTask() {
    await this.workflowActionsService.saveTemporaryFromScanTask(
      this.isCountingType,
      this.wfDict,
      this.comments,
      _.toString(this.processInstanceId),
      this.rawFormData['assetListId'].toString(),
      this.confirmData.taskId,
      'not-yet',
      this.workflowActionsService.approvalURL,
      this.dynamicTaskInfo,
      true,
      this.rawFormData['taskType'] === 'USER',
    );
  }

  isResultEmpty() {
    return _.isEmpty(this.itemsList);
  }

  async doSearch() {
    try {
      await this.http.addLoadingQueue(true);
      await this.reloadData(
        this.assetListDatas,
        this.scanedAssetIdList,
        this.webSocketScanedAssetIdList,
        function (isAllScaned) {
          // This is intentional
        },
      );
    } finally {
      this.http.removeLoadingQueue();
    }
  }

  isAutoBlur = false;
  updateCountBlur(item: any, event: any) {
    event.stopPropagation();
    if (this.isAutoBlur) {
      this.isAutoBlur = false;
      return;
    }
    if (this.platform.is('android')) {
      // 如果输入框有焦点并且有文本内容
      if (this.lastInputElement) {
        // 全选文本
        this.lastInputElement.select();

        // 执行命令（例如复制文本到剪贴板）
        document.execCommand('insertText', false, event.target.value);
        this.isAutoBlur = true;
        this.lastInputElement.blur();
        this.lastInputElement = undefined;
      }
    }
  }
}
