<!--
 * @Author: your name
 * @Date: 2020-10-13 16:54:53
 * @LastEditTime: 2021-01-15 16:27:12
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /asset-force-mobile/asset-force/src/app/workflow/approval/w1-asset-list/view/view.page.html
-->
<div class="global-bg"></div>

<ion-header
  translucent="true"
  class="header"
>
  <ion-toolbar
    class="title-toolbar"
    appTitleCenter
  >
    <ion-buttons slot="start">
      <ion-button
        class="back-button"
        (click)="backButton()"
      >
        <ion-icon
          slot="icon-only"
          name="chevron-back"
        ></ion-icon>
      </ion-button>
    </ion-buttons>
    <div
      id="titleContainer"
      class="title-container"
    >
      <div class="title">{{ workflowName }}</div>
    </div>
  </ion-toolbar>
</ion-header>

<ion-content
  fullscreen="true"
  class="offset-bottom-0"
>
  <div class="workflow-subtitle p-b-5">
    <span> {{ stepName }} </span>
  </div>

  <div class="container">
    <div class="remove-gap">
      <div
        class="list-title is-no-rounded-bottom"
        (click)="toggleExpand(0)"
        [class.is-no-rounded-bottom]="expand[0]"
      >
        <span class="head"></span>
        <p class="title font-weight-bold text-truncate mr-auto">資産情報</p>
        <ion-icon
          src="assets/images/icon/collapse.svg"
          *ngIf="expand[0]"
        ></ion-icon>
        <ion-icon
          src="assets/images/icon/expand.svg"
          *ngIf="!expand[0]"
        ></ion-icon>
      </div>

      <div
        class="list-container has-list-title has-py"
        *ngIf="expand[0]"
      >
        <!--スキャン未完了-->
        <ng-container>
          <div (click)="toAssetList(false)">
            <div class="float-icon-container float-icon-right float-icon-next float-icon-no-gutter">
              <div class="list-item is-no-border is-compact">
                <div class="list-head">資産数</div>
                <div class="list-body">{{ assetCount }}<span class="ml-2">件</span></div>
              </div>
              <div class="list-item is-no-border is-compact">
                <div class="list-head">資産種類</div>
                <div class="list-body text-truncate">{{ assetTypeName }}</div>
              </div>
            </div>
          </div>
        </ng-container>

        <!--スキャン完了-->
        <!-- <ng-container *ngIf="isScanComplete">
          <div routerLink="../list" [queryParams]="{ 'scan-complete': true }">
            <div class="float-icon-container float-icon-right float-icon-next float-icon-no-gutter">
              <div class="list-item is-no-border is-compact">
                <div class="list-head">資産数</div>
                <div class="list-body">{{assetCount}}<span class="ml-2">件</span></div>
              </div>
              <div class="list-item is-no-border is-compact">
                <div class="list-head">資産種類</div>
                <div class="list-body text-truncate">{{assetTypeName}}</div>
              </div>
            </div>
          </div>
        </ng-container> -->
      </div>
    </div>
  </div>

  <div class="container">
    <app-perview
      #perviewComponent
      [itemData]="wfDict"
      [permissions]="permissions"
      [jsString]="jsString"
      [backPageUrl]="backPageUrl"
      [commentsText]="comments"
      [ifShowButton]="showButton"
      [workflowScript]="workflowScript"
    ></app-perview>
  </div>

  <div class="container">
    <app-workflow-talk-list
      *ngIf="isAllowTalk"
      [liveTalkListUrl]="liveTalkListUrl"
      [processInstanceId]="processInstanceId"
    >
    </app-workflow-talk-list>
  </div>

  <!--担当者選択-->
  <div
    class="container"
    style="padding-bottom: 15px"
    *ngIf="isHasTantoushaF"
  >
    <div class="remove-gap">
      <div
        class="list-title is-no-rounded-bottom"
        (click)="toggleExpand(1)"
        [class.is-no-rounded-bottom]="expand[1]"
      >
        <span class="head"></span>
        <p class="title font-weight-bold text-truncate mr-auto">担当者</p>
        <ion-icon
          src="assets/images/icon/collapse.svg"
          *ngIf="expand[1]"
        ></ion-icon>
        <ion-icon
          src="assets/images/icon/expand.svg"
          *ngIf="!expand[1]"
        ></ion-icon>
      </div>

      <div
        class="list-container has-list-title has-py"
        *ngIf="expand[1]"
      >
        <div
          (click)="chooseTantousha(i)"
          *ngFor="let item of dynamicTaskInfo; let i = index"
        >
          <div
            class="float-icon-container float-icon-right float-icon-next float-icon-no-gutter"
            [ngStyle]="{ 'border-top': i !== 0 ? 'solid 1px #646464' : 'none' }"
          >
            <div class="list-item">
              <div
                class="list-head"
                style="width: 50%"
                >{{ item.taskDefName }}<span style="color: #d60047">※</span></div
              >
              <div
                class="list-body"
                [ngStyle]="{ color: getTantoushaInfoWithIndex(i) === '未選択' ? '#d60047' : '#141414' }"
              >
                {{ getTantoushaInfoWithIndex(i) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="container">
    <app-wf-component-form-output-modal
      [queryPar]="thisQueryPar"
      [backUrl]="backPageUrl"
      [setRawFormData]="rawFormData"
      [workflowName]="workflowName"
      [processInstanceId]="processInstanceId"
      [assetList]="assetList"
      [assetListId]="assetListId"
      [assetTypeId]="assetTypeId"
    ></app-wf-component-form-output-modal>
  </div>
</ion-content>

<ion-footer
  class="page-footer ion-no-border"
  *ngIf="buttonStatus['footer']"
>
  <ion-toolbar>
    <div
      class="footer-buttons"
      [ngClass]="{
        'wf-footer-more-buttons': numberButtonsFooterSection > 3
      }"
    >
      <!-- 複数人 -->
      <ng-template
        [ngIf]="isMultiScanTask"
        [ngIfElse]="NotIsMultiScanTask"
      >
        <!-- スキャン未完了 -->
        <ion-button
          color="blue-default"
          fill="outline"
          *ngIf="isPermission"
          [disabled]="isDisabled"
          size="small"
          class="footer-button"
          (click)="saveTemporaryFromScanTask()"
          >一時保存</ion-button
        >
        <ion-button
          color="red-default"
          fill="outline"
          [disabled]="isDisabled"
          size="small"
          class="footer-button"
          (click)="rejection(workflowButtonName?.vetoButtonDic?.nameVeto)"
          *ngIf="showRejectButton"
          >{{ workflowButtonName?.vetoButtonDic?.nameVeto }}</ion-button
        >
        <ion-button
          color="red-default"
          fill="outline"
          [disabled]="isDisabled"
          size="small"
          class="footer-button"
          (click)="sendBack(workflowButtonName?.turndBackButtonDic?.nameTurndBack)"
          *ngIf="showSendBackButton"
          >{{ workflowButtonName?.turndBackButtonDic?.nameTurndBack }}</ion-button
        >
        <ion-button
          color="blue-default"
          fill="outline"
          [disabled]="isDisabled"
          size="small"
          class="footer-button"
          (click)="restartScantask()"
          *ngIf="scanTaskIsLocked && assetCount !== 0"
          >解除</ion-button
        >

        <ion-button
          color="blue-default"
          [disabled]="isDisabled"
          size="small"
          class="footer-button"
          (click)="scanTasktoFinish()"
          *ngIf="!isUserTask && !scanTaskIsLocked && isAllScaned"
          >完了</ion-button
        >
        <ng-template [ngIf]="(scanTaskIsLocked || isUserTask) && enableConfrimButton && assetCount !== 0">
          <ng-template
            [ngIf]="numberButtonsFooterSection >= 6"
            [ngIfElse]="notWorkflowBtnName"
          >
            <ion-button
              color="blue-default"
              fill="outline"
              size="small"
              [disabled]="isDisabled"
              class="footer-button next-five-buttons-sty"
              *ngIf="workflowButtonName?.raiseButtonList?.length > 1"
              (click)="otherButtonSwitchOpenModal()"
              >その他</ion-button
            >
            <ion-button
              color="blue-default"
              [disabled]="isDisabled"
              size="small"
              (click)="approval(workflowButtonName?.raiseButtonList[2])"
              class="footer-button next-five-buttons-sty"
              >{{ workflowButtonName?.raiseButtonList[2]?.nameRaise }}</ion-button
            >
          </ng-template>
          <ng-template #notWorkflowBtnName>
            <ng-template
              ngFor
              let-raiseButtonDic
              [ngForOf]="workflowButtonName?.raiseButtonList"
            >
              <ion-button
                color="blue-default"
                size="small"
                [disabled]="isDisabled"
                (click)="approval(raiseButtonDic)"
                class="footer-button wf-footer-organize-button"
                [ngClass]="{
                  'next-four-buttons-sty': numberButtonsFooterSection == 4 && isBigFont && isUserTask,
                  'next-five-buttons-sty': numberButtonsFooterSection >= 5
                }"
                >{{ raiseButtonDic?.nameRaise }}</ion-button
              >
            </ng-template>
          </ng-template>
        </ng-template>
      </ng-template>
      <!-- 非複数人 -->
      <ng-template #NotIsMultiScanTask>
        <!-- subprocess & subForm -->
        <ng-template [ngIf]="workFlowType.isNewW3">
          <ion-button
            color="red-default"
            fill="outline"
            [disabled]="isDisabled"
            size="small"
            class="footer-button"
            (click)="rejection(workflowButtonName?.vetoButtonDic?.nameVeto)"
            *ngIf="showRejectButton"
            >{{ workflowButtonName?.vetoButtonDic?.nameVeto }}</ion-button
          >
          <ion-button
            color="red-default"
            fill="outline"
            [disabled]="isDisabled"
            size="small"
            class="footer-button"
            (click)="sendBack(workflowButtonName?.turndBackButtonDic?.nameTurndBack)"
            *ngIf="showSendBackButton"
            >{{ workflowButtonName?.turndBackButtonDic?.nameTurndBack }}</ion-button
          >

          <ion-button
            color="blue-default"
            fill="outline"
            [disabled]="isDisabled"
            size="small"
            (click)="backButton()"
            class="footer-button"
            >戻る</ion-button
          >
        </ng-template>
        <!-- 普通WF -->
        <ng-template [ngIf]="workFlowType.isDefault">
          <!--扫描完了-->
          <ng-template
            [ngIf]="isScanComplete"
            [ngIfElse]="NotIsScanComplete"
          >
            <ng-template
              ngFor
              let-raiseButtonDic
              [ngForOf]="workflowButtonName?.raiseButtonList"
            >
              <ion-button
                color="blue-default"
                size="small"
                [disabled]="isDisabled"
                (click)="approval(raiseButtonDic)"
                class="footer-button"
                [ngClass]="{
                  'next-four-buttons-sty': numberButtonsFooterSection == 4 && isBigFont && isUserTask,
                  'next-five-buttons-sty': numberButtonsFooterSection >= 5
                }"
                >{{ raiseButtonDic?.nameRaise }}</ion-button
              >
            </ng-template>
          </ng-template>

          <!--未扫描-->
          <ng-template #NotIsScanComplete>
            <ion-button
              color="blue-default"
              fill="outline"
              [disabled]="isDisabled"
              size="small"
              *ngIf="isPermission"
              class="footer-button"
              [ngClass]="{
                'next-four-buttons-sty': numberButtonsFooterSection == 4 && isBigFont && isUserTask,
                'next-five-buttons-sty': numberButtonsFooterSection >= 5
              }"
              (click)="saveTemporaryFromScanTask()"
              >一時保存</ion-button
            >
            <ion-button
              color="red-default"
              fill="outline"
              [disabled]="isDisabled"
              size="small"
              class="footer-button"
              (click)="rejection(workflowButtonName?.vetoButtonDic?.nameVeto)"
              *ngIf="showRejectButton"
              [ngClass]="{
                'next-four-buttons-sty': numberButtonsFooterSection == 4 && isBigFont && isUserTask,
                'next-five-buttons-sty': numberButtonsFooterSection >= 5
              }"
              >{{ workflowButtonName?.vetoButtonDic?.nameVeto }}</ion-button
            >
            <ion-button
              color="red-default"
              fill="outline"
              [disabled]="isDisabled"
              size="small"
              class="footer-button"
              (click)="sendBack(workflowButtonName?.turndBackButtonDic?.nameTurndBack)"
              *ngIf="showSendBackButton"
              [ngClass]="{
                'next-four-buttons-sty': numberButtonsFooterSection == 4 && isBigFont && isUserTask,
                'next-five-buttons-sty': numberButtonsFooterSection >= 5
              }"
              >{{ workflowButtonName?.turndBackButtonDic?.nameTurndBack }}</ion-button
            >

            <ion-button
              color="blue-default"
              fill="outline"
              [disabled]="isDisabled"
              size="small"
              class="footer-button"
              (click)="unclaim()"
              *ngIf="showUnclamin"
              [ngClass]="{
                'next-four-buttons-sty': numberButtonsFooterSection == 4 && isBigFont && isUserTask,
                'next-five-buttons-sty': numberButtonsFooterSection >= 5
              }"
              >解除</ion-button
            >
            <ng-template [ngIf]="enableConfrimButton">
              <ng-template
                [ngIf]="numberButtonsFooterSection >= 6"
                [ngIfElse]="notWorkflowBtnName"
              >
                <ion-button
                  color="blue-default"
                  fill="outline"
                  size="small"
                  [disabled]="isDisabled"
                  class="footer-button next-five-buttons-sty"
                  *ngIf="workflowButtonName?.raiseButtonList?.length > 1"
                  (click)="otherButtonSwitchOpenModal()"
                  >その他</ion-button
                >
                <ion-button
                  color="blue-default"
                  [disabled]="isDisabled"
                  size="small"
                  (click)="approval(workflowButtonName?.raiseButtonList[2])"
                  class="footer-button next-five-buttons-sty"
                  >{{ workflowButtonName?.raiseButtonList[2]?.nameRaise }}</ion-button
                >
              </ng-template>
              <ng-template #notWorkflowBtnName>
                <ng-template
                  ngFor
                  let-raiseButtonDic
                  [ngForOf]="workflowButtonName?.raiseButtonList"
                >
                  <ion-button
                    color="blue-default"
                    size="small"
                    [disabled]="isDisabled"
                    (click)="approval(raiseButtonDic)"
                    class="footer-button wf-footer-organize-button"
                    [ngClass]="{
                      'next-four-buttons-sty': numberButtonsFooterSection == 4 && isBigFont && isUserTask,
                      'next-five-buttons-sty': numberButtonsFooterSection >= 5
                    }"
                    >{{ raiseButtonDic?.nameRaise }}</ion-button
                  >
                </ng-template>
              </ng-template>
            </ng-template>
            <ion-button
              color="blue-default"
              size="small"
              class="footer-button"
              (click)="toAssetList(true)"
              *ngIf="showClamin"
              [ngClass]="{
                'next-four-buttons-sty': numberButtonsFooterSection == 4 && isBigFont && isUserTask,
                'next-five-buttons-sty': numberButtonsFooterSection >= 5
              }"
              >実行</ion-button
            >
          </ng-template>
        </ng-template>
      </ng-template>
    </div>
  </ion-toolbar>
</ion-footer>

<ion-footer
  class="page-footer ion-no-border"
  *ngIf="!buttonStatus['footer']"
>
  <ion-toolbar>
    <div class="footer-buttons">
      <ion-button
        color="blue-default"
        fill="outline"
        size="small"
        (click)="backButton()"
        class="footer-button"
        >戻る</ion-button
      >
    </div>
  </ion-toolbar>
</ion-footer>
