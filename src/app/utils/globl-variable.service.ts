/*
 * @Author: your name
 * @Date: 2020-05-14 11:17:53
 * @,@LastEditTime: ,: 2021-12-03 16:15:21
 * @,@LastEditors: ,: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /ionic-screenTransition/src/app/Utils/global-variable.service.ts
 */

// 名前：SMFL_Maps_web_API_Key_01
// 制限： 「API の制限」で「Maps JavaScript API」だけが利用できる

import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class GlobalVariableService {
  constructor() {}
  private _AppVersion: string = '7.1.0';
  private _USER_NAME: string;
  private _TOKEN: string;
  private _USER_ID: string;
  private _TENANT_ID: string;
  private _APP_FONTSIZE_NORMAL: string = '16px';
  private _APP_FONTSIZE_BIG: string = '20px';

  private _APP_PAGE_LIMITED_NUMBER = 200;
  private _APP_PAGE_ITEM_NUMBER = 20;
  private _GetAuthority: string = '/secure/RolePermission/findUser';
  private _SsoTicket: string = '/auth/sso/exchangeTicket';
  // Password
  private _SecureCognitoUpdatePassword: string = '/secure/cognito/updatePassword';
  //Asset
  private _SecureAsset: string = 'secure/Asset?';
  private _SecureFindById: string = 'secure/Asset/findById?';
  private _SecureAssetWithKeyWord: string = 'secure/Asset/findByKeyword?';
  private _SecureSignIn: string = 'auth/login';
  private _SecureLogout: string = 'auth/logout';
  private _SecureSignInTenant: string = 'secure/cognito/getToken?';
  private _SecureMobileSetting: string = 'secure/AssetMobileSetting/forMobile?';
  private _SecureAssetItem: string = 'secure/AssetItem?';
  private _SecureAsseCommontItem: string = 'secure/assetItemCommon/list';
  private _SecureLoginRecord: string = 'secure/login/record/mobile';
  private _SecureGetLastedApi: string = 'common/mobile-deployment/last?';
  private _SecureGetMobile: string = 'common/mobile-deployment';
  private _SecureAssetType: string = 'secure/AssetType/sort?';
  private _SecureAssetInsert: string = 'secure/Asset/insert?';
  private _SecureAppurtenancesInformationCount: string =
    'secure/AppurtenancesInformation/mobile/appurtenancesInformationDataCount?';
  private _SecureAppurtenancesInformation: string =
    'secure/AppurtenancesInformation/mobile/appurtenancesInformationByTypeId?';
  private _SecureAppurtenancesInformationtMobileSetting: string =
    'secure/AppurtenancesInformationType/getMobileSetting?';
  private _SecureAppurtenancesInformationtLayoutSetting: string = 'secure/LayoutSetting?';
  private _SecureAppurtenancesInformationMobileSettingForMobile: string =
    '/secure/AppurtenancesInformationMobileSetting/forMobile?';
  private _GetAssetByBarcode: string = '/secure/Asset/findByBarCodeForMobile?';
  private _GetCopyAppurtenancesInformationListById =
    'secure/AppurtenancesInformation/getCopyAppurtenancesInformationListById?';
  private _GetAppurtenancesInformationListById = 'secure/AppurtenancesInformation/getAppurtenancesInformationListById?';
  private _SecureAppurtenancesInformationtInsert: string = 'secure/AppurtenancesInformation/insert?';
  private _SecureAppurtenancesInformationtUpdate: string = 'secure/AppurtenancesInformation/update?';
  private _SecureAppurtenancesInformationtDelete: string = 'secure/AppurtenancesInformation/delete';
  private _SearchByKeyword: string = 'secure/User/searchByKeyword?';
  private _SecureUploadUrl: string = 'secure/s3/uploadUrl?';
  private _FileDelete: string = 'secure/s3/delete';
  private _GetAssetsPrintAble: string = 'secure/Asset/mobile/hasPrintTemplateMapping?';
  private _FindAssetsByKeyword: string = 'secure/Asset/mobile/findByKeywordAndAssetIdList?';
  private _GetSearchConditions: string = 'secure/AssetSearchName?';
  private _GetAssetsBySearchCondition: string = 'secure/Asset/findAll?';
  private _GetAssetsOneMonthOrAll: string = 'secure/Asset?';
  private _GetAssetItemCommon: string = 'secure/assetItemCommon/list?';
  private _GetAssetAuthority: string = 'secure/Asset/checkAuthority?';
  private _GetAssetRelationList: string = 'secure/Asset/getAssetRelationList?';
  private _GetTurl: string = 'secure/s3/downloadUrl?';
  private _GetGroupColumn: string = 'secure/AssetSearchColumn/groupColumn?';
  private _GetActionLabelAndIcon: string = '/secure/AssetActionSetting/getActionLabelAndIcon';
  private _GetCategoryCountForMobile: string = 'secure/AssetMobileSetting/getCategoryCountForMobile';
  private _CustomizeSearch: string = 'secure/Asset/customizeSearch';
  private _GetAppointmentListByAssetId: string = 'secure/Appointment/getAppointmentListByAssetId';
  private _SaveAssetAppoint: string = 'secure/Appointment/update';
  private _CheckAssetAppoint: string = 'secure/Appointment/checkAssetReservationStatus';
  private _UnPromission: string = 'secure/RolePermission/unPermission';
  private _GetTenant: string = 'secure/Tenant/getTenantInfo';
  private _GetHomePageListByMobileHomeSetting: string = '/secure/mobileHomeSetting/homePageListByMobileHomeSetting';
  private _GetHomePageProcessSummary: string = '/secure/mobileHomeSetting/homePageProcessSummary';
  private _GetAssetInfoByRfid: string = '/secure/Asset/getAssetByRfid';
  private _CustomizeSearchForMobile: string = 'secure/Asset/customizeSearchForMobile';
  private _CustomizeSearchCountForMobile: string = 'secure/Asset/customizeSearchCountForMobile';
  //Mypage
  private _GetUserInfoApi: string = 'secure/User/getMyAccount?';
  private _MyPageUpdateUserInfo: string = 'secure/User/updateBase?';
  private _GetUserRole: string = 'secure/UserRole/myRole?';
  private _SecureAssetSave: string = 'secure/Asset/update';
  private _GetTenantInfo: string = 'secure/Tenant/userTenant';
  //MFA
  private _GetVerificationCode: string = 'secure/User/sendVerifyCode?';
  private _ChangeBindPhone: string = 'secure/User/changePhone?';
  private _UserBindPhone: string = 'secure/User/bindPhone?';
  private _GetEmailVerificationCode: string = 'secure/User/sendMfaMailVerifyCode?';
  //Action
  private _ActionGetAssetActionById = 'secure/AssetActionSetting/getAssetActionById?';
  private _ActionOfflineGetAssetActionById = 'secure/AssetActionList/getAssetActionById';
  private _ActionOfflineAssetActionBatch = 'secure/AssetActionList/offline/batch';
  private _ActionOffllineSearch = 'secure/AssetActionList/offline/search';
  private _ActionDataListCount: string = 'secure/AssetActionList/getAssetActionData?';
  private _ActionVerifyAssetRegistrationStatus = 'secure/AssetActionList/verify?';
  private _ActionVerifyAssetRegistrationStatusByCarNumber = 'secure/AssetActionList/verify/ocrIdentityCode?';
  private _ActionSettingSearch: string = '/secure/AssetActionSetting/mobile/search';
  private _ActionListSearch: string = '/secure/AssetActionList/mobile/search';
  private _ActionData: string = 'secure/AssetActionList?';
  private _ActionGetItemById = 'secure/LayoutSetting?';
  private _ActionGetItemByAssetType = 'secure/AssetItem?';
  private _ActionGetMasterByMasterTypeId = 'secure/LayoutSetting?';
  private _ActionGetMasterInfoById = 'secure/MasterDetail?';
  private _ActionGetRevisedMasterInfoById = 'secure/MasterDetail/search';
  private _ActionTransitAssetActionData = 'secure/AssetActionList/transit?';
  private _ActionPreviewInfo = 'secure/AssetActionList/previewInfo?';
  private _ActionAssetActionListDelete = 'secure/AssetActionList/delete?';

  private _ActionGetCustomItem = 'secure/LayoutSetting?typeId=0&classification=5';
  private _ActionGetBarcodeAndLocation = 'secure/AssetActionList/getBarcodeAndLocation?';
  private _ActionGetAssetListByProcessId = 'secure/AssetActionList/mobile/getAssetListByProcessId?';
  private _AssetActionListVerify = '/secure/AssetActionList/mobile/asset/verify?';
  private _AssetActionGetAssetlist = '/secure/AssetActionList/mobile/getAssetListByAssetId?';
  private _CompareInfoForPreview = '/secure/AssetActionList/mobile/getAssetInfoByActionId?';
  // @deprecated 资产一览一时保存数据api废弃,替换为_ActionGetAssetListByProcessId
  private _ActionGetSelectedAssetList = '/secure/AssetActionList/getSelectedAssetList?';
  private _GetGeocodeByAddress = 'secure/Geocode/getGeocodeByAddress';
  private _GetSavedInventoryAssetActionInfo = 'secure/AssetActionList/getSavedInventoryAssetActionInfo';
  private _LockAssetActionData = '/secure/AssetActionList/lockAssetActionData';
  private _UnlockAssetActionData = '/secure/AssetActionList/unlockAssetActionData';
  private _GetAssetListByProcessId = '/secure/AssetActionList/getAssetListByProcessId';
  private _ActionUpdateAssetActionData = 'secure/AssetActionList/update?';
  private _ActionInsertAssetActionData = 'secure/AssetActionList/insert?';

  //Workflow
  private _WorkFlowGetAssignDynamicGroupList = 'secure/Role/getAssignDynamicGroupList';
  private _WorkFlowSearchByGroupIds = 'secure/User/searchByGroupIds';
  private _NewWorkflowList: string = 'secure/workflowEngine/mobile/list';
  private _ApplicationList: string = 'secure/workflowEngine/searchTaskList?';
  private _GetWorkFlowApplicationCount = 'secure/workflowEngine/getProcessCount?';
  private _WorkFlowStartForm: string = 'secure/workflowEngine/startForm?';
  private _WorkflowStart: string = 'secure/workflowEngine/start';
  private _WorkflowStartSecond: string = 'secure/workflowEngine/mobile/start/secondWf?';
  private _WorkflowTransitSecond: string = 'secure/workflowEngine/mobile/transit/secondWf';
  private _WorkflowSaveTemporary: string = 'secure/workflowEngine/saveTemporary';
  private _WorkflowTaskForm: string = 'secure/workflowEngine/mobile/taskForm?';
  private _WorkflowCancel: string = 'secure/workflowEngine/cancel?';
  private _WorkflowTransit: string = 'secure/workflowEngine/transit';
  private _WorkflowClaim: string = 'secure/workflowEngine/mobile/claim';
  private _WorkflowUnclaim: string = 'secure/workflowEngine/unClaim';
  private _WorkflowsSendBack: string = 'secure/workflowEngine/sendBack';
  private _WorkflowsSaveTemporaryFromScanTask: string = 'secure/workflowEngine/mobile/saveTemporaryFromScanTask';
  private _WorkflowssaveTemporaryFromApprover: string = 'secure/workflowEngine/saveTemporaryFromApprover';
  private _StartThirdWorkflow: string = 'secure/workflowEngine/startWithThirdFlow';
  private _WorkflowStartSaveTemporary: string = 'secure/workflowEngine/startSaveTemporary';
  private _WorkflowStartSaveMobileTemporary: string = 'secure/workflowEngine/mobile/startSaveTemporary?';
  private _WorkflowStartScan: string = 'secure/workflowEngine/mobile/start';
  private _WorkflowMultiTaskReload: string = '/secure/workflowEngine/mobile/multiTaskReload';
  private _WorkflowSubFormLayout: string = '/secure/subformDetail';
  private _WorkflowSubFormLayoutInfo: string = '/secure/subform/info';
  private _WorkflowSubFormContentByAsset: string = '/secure/workflowEngine/subform/info';
  private _WorkflowSubformUpdateByAsset: string = '/secure/workflowEngine/subform/update';
  private _WorkflowMobUpdateByBarCodet: string = '/secure/workflowEngine/mobile/assetListData/updateByBarCodeOrRfid';
  private _WorkflowCreateAssetList: string = '/secure/AssetList/createAssetList';
  // private _WorkflowMobileTransit: string = 'secure/workflowEngine/mobile/transit';
  private _WorkflowMobileNewW3ScanedAssetsTransit: string = 'secure/workflowEngine/transitForMultiInst';
  private _WorkflowMobileScan: string = 'secure/workflowEngine/mobile/scan';
  private _WorkflowUpdateAssetListDataScanReal: string = 'secure/workflowEngine/updateAssetListDataScanReal';
  private _WorkflowMobileSendBack: string = 'secure/workflowEngine/mobile/sendBack';
  private _WorkflowMobileAmountScan: string = '/secure/workflowEngine/mobile/amount/scan';
  private _WorkflowAssignDynamicTaskListByTaskId: string =
    'secure/workflowEngine/getAssignDynamicTaskListFromBpmnByTaskId';
  private _WorkflowAssetColumn: string = '/secure/WorkflowAssetColumn/getColumns';
  private _WorkflowGetWorkflowFormByEngineId = 'secure/WorkflowForm/getWorkflowFormByEngineId';
  private _AssetListData = 'secure/AssetListData';
  private _ReportDatabase = 'secure/report/database';
  private _GetAssetsByKeywordInWfAssetListForAssignScan: string =
    '/secure/workflowEngine/mobile/getAssetsByKeywordInWfAssetListForAssignScan';
  private _RestoreAssetScanStateForAssignScan: string =
    '/secure/workflowEngine/mobile/restoreAssetScanStateForAssignScan';
  private _RestoreAssetScanState: string = '/secure/workflowEngine/mobile/assetListData/restoreAssetList';
  private _AmountAssignScan: string = '/secure/workflowEngine/mobile/amount/assignScan';

  private _getLocationList: string = 'secure/Tenant/location/list';
  private _getRoleList: string = 'secure/Role';
  private _UpdateScanTaskLock: string = 'secure/workflowEngine/updateScanTaskLock';

  //Notification
  private _getNotificationList: string = 'notification/getNotificationList';
  private _getNotificationListByPage: string = 'secure/Notification/getNotificationListByPage';
  private _getNotificationCount: string = 'secure/Notification/getPublishedCount';
  private _getNotificationAnyNew: string = 'secure/Notification/anyNew';
  private _getNotificationById: string = 'notification/getNotificationById';
  private _setNotificationReadStatus: string = 'secure/NotificationUserRead/insert';
  // 获取apiKey
  private _getGoolePlatformApiKeyWeb: string = 'secure/Setting/getGoolePlatformApiKeyWeb';

  // getSQLQueryResults 使用
  private _getApiSettingList: string = 'secure/ApiSetting/getApiSettingList?';
  private _getReportDb: string = 'secure/report/database';

  // full user info
  private _getFullUserInfo: string = '/secure/User/getFullUserInfo';
  private _getReportGetById: string = 'secure/report/getById';
  // 通过assetIdList获取多资产详细
  private _getAssetInfoByAssetIds: string = 'secure/Asset/getAssetInfoByAssetIds';
  private _insertsForMobile: string = 'secure/AppurtenancesInformation/insertsForMobile';
  private _CalculateJavascript: string = `
  let pageThis = global.pageThis
  var dayHandleTemp = pageThis.dayHandleByTS;
  var dayHandle = function (dateStr, quantity){
      return dayHandleTemp(dateStr, quantity);
  }
  var monthHandleTemp = pageThis.monthHandleByTS;
  var monthHandle = function (dateStr, quantity){
      return monthHandleTemp(dateStr, quantity);
  }
  var yearHandleTemp = pageThis.yearHandleByTS;
  var yearHandle = function (dateStr, quantity){
      return yearHandleTemp(dateStr, quantity);
  }

  var nowTemp = pageThis.nowByTS;
  var now = function (){
      return nowTemp();
  }

  var dayBetweenTemp = pageThis.dayBetweenByTS;
  var dayBetween = function (dateStr, quantity){
      return dayBetweenTemp(dateStr, quantity);
  }

  var monthBetweenTemp = pageThis.monthBetweenByTS;
  var monthBetween = function (dateStr, quantity){
      return monthBetweenTemp(dateStr, quantity);
  }

  var yearBetweenTemp = pageThis.yearBetweenByTS;
  var yearBetween = function (dateStr, quantity){
      return yearBetweenTemp(dateStr, quantity);
  }
  `;
  private _CustomizedLogicJavascript: string = `
  let pageThis = global.pageThis
  let instance = pageThis.itemDataDict;
  let that = pageThis;
  //項目に値を設定する
  var setValueTemp = pageThis.setValueByTS;
  var setValue = function (itemName,itemValue,instance){
    setValueTemp(itemName,itemValue,instance);
  }
  //項目の値を取得する
  var getValueTemp = pageThis.getValueByTS;
  var getValue = function (itemName,instance){
      return getValueTemp(itemName,instance);
  }
  //マスタの値を取得する
  var getMasterValueTemp = pageThis.getMasterValueByTS;
  var getMasterValue = function (value1,value2,instance){
      return getMasterValueTemp(value1,value2,instance);
  }
  //項目を参照のみに設定する
  var readonlyTemp = pageThis.readonlyByTS;
  var readonly = function (itemName,instance){
      return readonlyTemp(itemName,instance);
  }
  //項目を編集可にする
  var canWriteTemp = pageThis.canWriteByTS;
  var canWrite = function (itemName,instance){
      return canWriteTemp(itemName,instance);
  }
  //項目に対するメッセージを表示する
  var showItemMsgTemp = pageThis.showItemMsgByTS;
  var showItemMsg = function (itemName,itemValue,instance){
      return showItemMsgTemp(itemName,itemValue,instance);
  }
  //項目に対するメッセージの表示を消す
  var clearItemMsgTemp = pageThis.clearItemMsgByTS;
  var clearItemMsg = function (itemName,instance){
      return clearItemMsgTemp(itemName,instance);
  }

  //ポップアップメッセージを表示する
  var showMsgTemp = pageThis.showMsgByTS;
  var showMsg = function (type,instance){
      return showMsgTemp(type,that,instance);
  }

  //資産新規登録する時に、JavaScriptで資産情報を自動的にマーピングする
  var generateMasterTemp = pageThis.generateMasterTS;
  var generateMaster = function (assetTypeId, masterTypeId, masterId, instance){
      return generateMasterTemp(assetTypeId, masterTypeId, masterId, instance);
  }

  // assetIdで資産情報を取得
  var getAssetTemp = pageThis.getAssetTS;
  var getAsset = function (assetId, instance) {
      return getAssetTemp(assetId, instance);
  }

  // 資産情報をアップデート
  var updateAssetTemp = pageThis.updateAssetTS;
  var updateAsset = function (asset, instance) {
      return updateAssetTemp(asset, instance);
  }

  // 資産新規作成
  var insertAssetTemp = pageThis.insertAssetTS;
  var insertAsset = function (asset, instance) {
      return insertAssetTemp(asset, instance);
  }

  // 資産リストを取得
  var getWorkflowAssetListTemp = pageThis.getWorkflowAssetListTS;
  var getWorkflowAssetList = function (instance) {
      return getWorkflowAssetListTemp(instance);
  }

  // 資産Idを取得
  var getAssetIdTemp = pageThis.getAssetIdTS;
  var getAssetId = function (that) {
      return getAssetIdTemp(that);
  }

  // アクション名を取得
  // cancel 取消 sendBack　差戻し admit 承认　startNew 新規申請
  var getActionTemp = pageThis.getActionTS;
  var getAction = function (that) {
      return getActionTemp(that);
  }

  // WF名を取得
  var getWorkflowTaskNameTemp = pageThis.getWorkflowTaskNameTS;
  var getWorkflowTaskName = function (instance) {
      return getWorkflowTaskNameTemp(instance);
  }

  // 履歴情報を取得
  var getAppurtenancesInformationTemp = pageThis.getAppurtenancesInformationTS;
  var getAppurtenancesInformation = function (assetId, appurtenanceTypeId, count, instance) {
      return getAppurtenancesInformationTemp(assetId, appurtenanceTypeId, count, instance);
  }

  // 履歴情報を新規作成
  var insertAppurtenancesInformationTemp = pageThis.insertAppurtenancesInformationTS;
  var insertAppurtenancesInformation = function (appurtenance,instance) {
      return insertAppurtenancesInformationTemp(appurtenance,instance);
  }

  // 履歴情報をアップデート
  var updateAppurtenancesInformationTemp = pageThis.updateAppurtenancesInformationTS;
  var updateAppurtenancesInformation = function (appurtenance,instance) {
      return updateAppurtenancesInformationTemp(appurtenance,instance);
  }

  // masterListを取得
  var getMasterValueListTemp = pageThis.getMasterValueListTS;
  var getMasterValueList = function (masterTypeId, instance) {
    return getMasterValueListTemp (masterTypeId, instance);
  }

  // 資産リスト取得
  var getAssetsByTypeTemp = pageThis.getAssetsByTypeTS;
  var getAssetsByType = function (assetTypeId, rows, instance) {
    return getAssetsByTypeTemp (assetTypeId, rows, instance);
  }

  // dateFormat
  var dateFormatTemp = pageThis.dateFormatTS;
  var dateFormat = function (date, fmt, instance) {
    return dateFormatTemp (date, fmt, instance);
  }
  // get current time
  var nowTemp = pageThis.nowTS;
  var now = function (instance) {
    return nowTemp (instance);
  }

  // set section show or hidden
  var toggleSectionTemp = pageThis.toggleSectionTS;
  var toggleSection = function (sectionName, isShowSection ,instance) {
    return toggleSectionTemp(sectionName, isShowSection , that, instance);  
  }

  // get new list item
  var changeListItemTemp = pageThis.changeListItemTS;
  var changeListItem = function (itemName, list ,instance) {
    return changeListItemTemp(itemName, list, that, instance);
  }

  // get AppurtenancesInformationId
  var getAppurtenancesInformationIdTemp = pageThis.getAppurtenancesInformationIdTS;
  var getAppurtenancesInformationId = function (that) {
    return getAppurtenancesInformationIdTemp(that);
  }

  // getUserInfo
  var getUserInfoTemp = pageThis.getUserInfoByTS;
  var getUserInfo = function (instance) {
    return getUserInfoTemp(that);
  }

  // getSQLQueryResults
  var getSQLQueryResultsTemp = pageThis.getSQLQueryResultsTS;
  var getSQLQueryResults = function (queryName, params) {
    return getSQLQueryResultsTemp(queryName, params, that, instance);
  }

  // apiProxy
  var apiProxyTemp = pageThis.apiProxyTS;
  var apiProxy = function (url, method, headers, body) { //url, method = "GET", headers = {}, body = ""
    return apiProxyTemp(url, method, headers, body, that, instance);
  }

  // callOpenAPI
  var callOpenAPITemp = pageThis.callOpenAPITS;
  var callOpenAPI = function (path, method, params, apiKey) { //url, method = "GET", headers = {}, body = ""
    return callOpenAPITemp(path, method, params, apiKey, that, instance);
  }
  `;

  get SsoTicket(): string {
    return this._SsoTicket;
  }

  set SsoTicket(value: string) {
    this._SsoTicket = value;
  }

  get SecureCognitoUpdatePassword(): string {
    return this._SecureCognitoUpdatePassword;
  }

  get CheckAssetAppoint(): string {
    return this._CheckAssetAppoint;
  }

  get GetAuthority(): string {
    return this._GetAuthority;
  }

  set GetAuthority(value: string) {
    this._GetAuthority = value;
  }

  get SaveAssetAppoint(): string {
    return this._SaveAssetAppoint;
  }

  set SaveAssetAppoint(value: string) {
    this._SaveAssetAppoint = value;
  }

  /**
   * @description: 'SecureAsseCommontItem'
   * @param {}
   * @return: USER_NAME
   */
  get SecureAsseCommontItem(): string {
    return this._SecureAsseCommontItem;
  }

  /**
   * @description: 'SecureAsseCommontItem'
   * @param {USER_NAME}
   * @return:
   */
  set SecureAsseCommontItem(secureAsseCommontItem: string) {
    this._SecureAsseCommontItem = secureAsseCommontItem;
  }

  /**
   * @description: 'getGroupColumn'
   * @param {}
   * @return: USER_NAME
   */
  get GetGroupColumn(): string {
    return this._GetGroupColumn;
  }

  /**
   * @description: 'getGroupColumn'
   * @param {USER_NAME}
   * @return:
   */
  set GetGroupColumn(getGroupColumn: string) {
    this._GetGroupColumn = getGroupColumn;
  }

  get GetActionLabelAndIcon(): string {
    return this._GetActionLabelAndIcon;
  }

  set GetActionLabelAndIcon(getActionLabelAndIcon: string) {
    this._GetActionLabelAndIcon = getActionLabelAndIcon;
  }

  /**
   * @description: 'actionPreviewInfo'
   * @param {}
   * @return: USER_NAME
   */
  get ActionPreviewInfo(): string {
    return this._ActionPreviewInfo;
  }

  /**
   * @description: 'actionPreviewInfo'
   * @param {USER_NAME}
   * @return:
   */
  set ActionPreviewInfo(actionPreviewInfo: string) {
    this._ActionPreviewInfo = actionPreviewInfo;
  }

  /**
   * @description: 'CalculateJavascript'
   * @param {}
   * @return: USER_NAME
   */
  get CalculateJavascript(): string {
    return this._CalculateJavascript;
  }

  /**
   * @description: 'CalculateJavascript'
   * @param {USER_NAME}
   * @return:
   */
  set CalculateJavascript(calculateJavascript: string) {
    this._CalculateJavascript = calculateJavascript;
  }

  /**
   * @description: 'customizedLogicJavascript'
   * @param {}
   * @return: USER_NAME
   */
  get CustomizedLogicJavascript(): string {
    return this._CustomizedLogicJavascript;
  }

  /**
   * @description: 'customizedLogicJavascript'
   * @param {USER_NAME}
   * @return:
   */
  set CustomizedLogicJavascript(customizedLogicJavascript: string) {
    this._CustomizedLogicJavascript = customizedLogicJavascript;
  }

  /**
   * @description: 'secure/s3/downloadUrl?取得'
   * @param {}
   * @return: USER_NAME
   */
  get GetTurl(): string {
    return this._GetTurl;
  }

  /**
   * @description: 'secure/s3/downloadUrl?設定'
   * @param {USER_NAME}
   * @return:
   */
  set GetTurl(getTurl: string) {
    this._GetTurl = getTurl;
  }

  /**
   * @description: 'secure/Asset/findById?取得'
   * @param {}
   * @return: USER_NAME
   */
  get SecureFindById(): string {
    return this._SecureFindById;
  }

  /**
   * @description: 'secure/Asset/findById?設定'
   * @param {USER_NAME}
   * @return:
   */
  set SecureFindById(secureFindById: string) {
    this._SecureFindById = secureFindById;
  }

  /**
   * @description: 'secure/Asset/getAssetRelationList?取得'
   * @param {}
   * @return: USER_NAME
   */
  get GetAssetRelationList(): string {
    return this._GetAssetRelationList;
  }

  /**
   * @description: 'secure/Asset/getAssetRelationList?設定'
   * @param {USER_NAME}
   * @return:
   */
  set GetAssetRelationList(getAssetRelationList: string) {
    this._GetAssetRelationList = getAssetRelationList;
  }

  /**
   * @description: '/secure/AppurtenancesInformationMobileSetting/forMobile?取得'
   * @param {}
   * @return:
   */
  get SecureAppurtenancesInformationMobileSettingForMobile(): string {
    return this._SecureAppurtenancesInformationMobileSettingForMobile;
  }

  /**
   * @description: ''secure/systemMobileDeployManage/mobileApiHost'取得'
   * @param {}
   * @return: USER_NAME
   */
  get WorkflowUpdateAssetListDataScanReal(): string {
    return this._WorkflowUpdateAssetListDataScanReal;
  }

  /**
   * @description: ''secure/workflowEngine/updateAssetListDataScanReal'設定'
   * @param {USER_NAME}
   * @return:
   */
  set WorkflowUpdateAssetListDataScanReal(workflowUpdateAssetListDataScanReal: string) {
    this._WorkflowUpdateAssetListDataScanReal = workflowUpdateAssetListDataScanReal;
  }

  /**
   * @description: ''secure/systemMobileDeployManage/mobileApiHost'取得'
   * @param {}
   * @return: USER_NAME
   */
  get SecureGetMobile(): string {
    return this._SecureGetMobile;
  }

  /**
   * @description: ''secure/systemMobileDeployManage/mobileApiHost'設定'
   * @param {USER_NAME}
   * @return:
   */
  set SecureGetMobile(secureGetMobile: string) {
    this._SecureGetMobile = secureGetMobile;
  }

  /**
   * @description: 'secure/Asset/findByKeyword?取得'
   * @param {}
   * @return: USER_NAME
   */
  get SecureAssetWithKeyWord(): string {
    return this._SecureAssetWithKeyWord;
  }

  /**
   * @description: 'secure/Asset/findByKeyword?設定'
   * @param {USER_NAME}
   * @return:
   */
  set SecureAssetWithKeyWord(secureAssetWithKeyWord: string) {
    this._SecureAssetWithKeyWord = secureAssetWithKeyWord;
  }

  /**
   * @description: 'secure/workflowEngine/unclaim取得'
   * @param {}
   * @return: USER_NAME
   */
  get WorkflowUnclaim(): string {
    return this._WorkflowUnclaim;
  }

  /**
   * @description: 'secure/workflowEngine/unclaim設定'
   * @param {USER_NAME}
   * @return:
   */
  set WorkflowUnclaim(workflowUnclaim: string) {
    this._WorkflowUnclaim = workflowUnclaim;
  }

  /**
   * @description: 'secure/workflowEngine/startSaveTemporary取得'
   * @param {}
   * @return: USER_NAME
   */
  get WorkflowStartSaveTemporary(): string {
    return this._WorkflowStartSaveTemporary;
  }

  /**
   * @description: 'secure/workflowEngine/startSaveTemporary設定'
   * @param {USER_NAME}
   * @return:
   */
  set WorkflowStartSaveTemporary(workflowStartSaveTemporary: string) {
    this._WorkflowStartSaveTemporary = workflowStartSaveTemporary;
  }

  /**
   * @description: 'secure/workflowEngine/startSaveTemporary取得'
   * @param {}
   * @return: USER_NAME
   */
  get WorkflowStartSaveMobileTemporary(): string {
    return this._WorkflowStartSaveMobileTemporary;
  }

  /**
   * @description: 'secure/workflowEngine/startSaveTemporary設定'
   * @param {USER_NAME}
   * @return:
   */
  set WorkflowStartSaveMobileTemporary(workflowStartSaveMobileTemporary: string) {
    this._WorkflowStartSaveMobileTemporary = workflowStartSaveMobileTemporary;
  }

  /**
   * @description: 'secure/workflowEngine/startWithThirdFlow取得'
   * @param {}
   * @return: USER_NAME
   */
  get StartThirdWorkflow(): string {
    return this._StartThirdWorkflow;
  }

  /**
   * @description: 'secure/workflowEngine/startWithThirdFlow設定'
   * @param {USER_NAME}
   * @return:
   */
  set StartThirdWorkflow(startThirdWorkflow: string) {
    this._StartThirdWorkflow = startThirdWorkflow;
  }

  /**
   * @description: 'secure/workflowEngine/transit取得'
   * @param {}
   * @return: USER_NAME
   */
  get WorkflowTransit(): string {
    return this._WorkflowTransit;
  }

  /**
   * @description: 'secure/workflowEngine/transit設定'
   * @param {USER_NAME}
   * @return:
   */
  set WorkflowTransit(workflowTransit: string) {
    this._WorkflowTransit = workflowTransit;
  }

  /**
   * @description: 'secure/workflowEngine/mobile/transit取得'
   * @param {}
   * @return: USER_NAME
   */
  // get WorkflowMobileTransit(): string {
  //   return this._WorkflowMobileTransit;
  // }

  /**
   * @description: 'secure/workflowEngine/mobile/transit設定'
   * @param {USER_NAME}
   * @return:
   */
  // set WorkflowMobileTransit(workflowMobileTransit: string) {
  //   this._WorkflowMobileTransit = workflowMobileTransit;
  // }

  /**
   * @description: 'secure/workflowEngine/transitForMultiInst取得'
   * @param {}
   * @return: USER_NAME
   */
  get WorkflowMobileNewW3ScanedAssetsTransit(): string {
    return this._WorkflowMobileNewW3ScanedAssetsTransit;
  }

  /**
   * @description: 'secure/workflowEngine/transitForMultiInst設定'
   * @param {USER_NAME}
   * @return:
   */
  set WorkflowMobileNewW3ScanedAssetsTransit(workflowMobileNewW3ScanedAssetsTransit: string) {
    this._WorkflowMobileNewW3ScanedAssetsTransit = workflowMobileNewW3ScanedAssetsTransit;
  }

  /**
   * @description: 'secure/workflowEngine/mobile/scan設定'
   * @param {USER_NAME}
   * @return:
   */
  set WorkflowMobileScan(workflowMobileScan: string) {
    this._WorkflowMobileScan = workflowMobileScan;
  }

  /**
   * @description: 'secure/workflowEngine/mobile/scan取得'
   * @param {}
   * @return: USER_NAME
   */
  get WorkflowMobileScan(): string {
    return this._WorkflowMobileScan;
  }

  /**
   * @description: 'secure/workflowEngine/mobile/SendBack設定'
   * @param {USER_NAME}
   * @return:
   */
  set WorkflowMobileSendBack(workflowMobileSendBack: string) {
    this._WorkflowMobileSendBack = workflowMobileSendBack;
  }

  /**
   * @description: 'secure/workflowEngine/mobile/SendBack取得'
   * @param {}
   * @return: USER_NAME
   */
  get WorkflowMobileSendBack(): string {
    return this._WorkflowMobileSendBack;
  }

  /**
   * @description: '/secure/workflowEngine/mobile/amount/scan設定'
   * @param {WorkflowMobileAmountScan}
   * @return:
   */
  set WorkflowMobileAmountScan(WorkflowMobileAmountScan: string) {
    this._WorkflowMobileAmountScan = WorkflowMobileAmountScan;
  }

  /**
   * @description: '/secure/workflowEngine/mobile/amount/scan取得'
   * @param {}
   * @return: WorkflowMobileAmountScan
   */
  get WorkflowMobileAmountScan(): string {
    return this._WorkflowMobileAmountScan;
  }

  /**
   * @description: 'secure/workflowEngine/mobile/start取得'
   * @param {}
   * @return: USER_NAME
   */
  get WorkflowStartScan(): string {
    return this._WorkflowStartScan;
  }

  /**
   * @description: 'secure/workflowEngine/mobile/start設定'
   * @param {USER_NAME}
   * @return:
   */
  set WorkflowStartScan(workflowStartScan: string) {
    this._WorkflowStartScan = workflowStartScan;
  }

  /**
   * @description: 'secure/workflowEngine/multiTaskReload取得'
   * @param {}
   * @return: USER_NAME
   */
  get WorkflowMultiTaskReload(): string {
    return this._WorkflowMultiTaskReload;
  }

  /**
   * @description: 'secure/workflowEngine/multiTaskReload設定'
   * @param {USER_NAME}
   * @return:
   */
  set WorkflowMultiTaskReload(workflowMultiTaskReload: string) {
    this._WorkflowMultiTaskReload = workflowMultiTaskReload;
  }

  /**
   * @description: '/secure/subformDetail取得'
   * @param {}
   * @return: USER_NAME
   */
  get WorkflowSubFormLayout(): string {
    return this._WorkflowSubFormLayout;
  }

  /**
   * @description: '/secure/subformDetail設定'
   * @param {USER_NAME}
   * @return:
   */
  set WorkflowSubFormLayout(workflowSubFormLayout: string) {
    this._WorkflowSubFormLayout = workflowSubFormLayout;
  }

  /**
   * @description: '/secure/subform/info取得'
   * @param {}
   * @return: USER_NAME
   */
  get WorkflowSubFormLayoutInfo(): string {
    return this._WorkflowSubFormLayoutInfo;
  }

  /**
   * @description: '/secure/subform/info設定'
   * @param {USER_NAME}
   * @return:
   */
  set WorkflowSubFormLayoutInfo(workflowSubFormLayoutInfo: string) {
    this._WorkflowSubFormLayoutInfo = workflowSubFormLayoutInfo;
  }

  /**
   * @description: '/secure/workflowEngine/subform/info取得'
   * @param {}
   * @return: USER_NAME
   */
  get WorkflowSubFormContentByAsset(): string {
    return this._WorkflowSubFormContentByAsset;
  }

  /**
   * @description: '/secure/workflowEngine/subform/info設定'
   * @param {USER_NAME}
   * @return:
   */
  set WorkflowSubFormContentByAsset(workflowSubFormContentByAsset: string) {
    this._WorkflowSubFormContentByAsset = workflowSubFormContentByAsset;
  }

  /**
   * @description: '/secure/workflowEngine/subform/update取得'
   * @param {}
   * @return: USER_NAME
   */
  get WorkflowSubformUpdateByAsset(): string {
    return this._WorkflowSubformUpdateByAsset;
  }

  /**
   * @description: '/secure/workflowEngine/subform/update設定'
   * @param {USER_NAME}
   * @return:
   */
  set WorkflowSubformUpdateByAsset(workflowSubformUpdateByAsset: string) {
    this._WorkflowSubformUpdateByAsset = workflowSubformUpdateByAsset;
  }

  public get WorkflowStartSecond(): string {
    return this._WorkflowStartSecond;
  }

  /**
   * @description: 'secure/workflowEngine/mobile/start/secondWf設定'
   * @param {USER_NAME}
   * @return:
   */
  public set WorkflowStartSecond(workflowStartSecond: string) {
    this._WorkflowStartSecond = workflowStartSecond;
  }

  /**
   * @description: 'secure/workflowEngine/mobile/start/secondWf設定'
   * @param {USER_NAME}
   * @return:
   */
  public get WorkflowTransitSecond(): string {
    return this._WorkflowTransitSecond;
  }

  /**
   * @description: 'secure/workflowEngine/mobile/start/secondWf取得'
   * @param {USER_NAME}
   * @return:
   */
  public set WorkflowTransitSecond(workflowTransitSecond: string) {
    this._WorkflowTransitSecond = workflowTransitSecond;
  }

  /**
   * @description: 'secure/workflowEngine/sendBack取得
   * @param {}
   * @return: USER_NAME
   */
  get WorkflowsSendBack(): string {
    return this._WorkflowsSendBack;
  }

  /**
   * @description: 'ssecure/workflowEngine/mobile/claim設定
   * @param {USER_NAME}
   * @return:
   */
  set WorkflowsClaim(workflowsClaim: string) {
    this._WorkflowClaim = workflowsClaim;
  }

  /**
   * @description: 'secure/workflowEngine/mobile/claim取得
   * @param {}
   * @return: USER_NAME
   */
  get WorkflowsClaim(): string {
    return this._WorkflowClaim;
  }

  /**
   * @description: 'ssecure/workflowEngine/unclaim設定
   * @param {USER_NAME}
   * @return:
   */
  set WorkflowsUnclaim(workflowsUnclaim: string) {
    this._WorkflowUnclaim = workflowsUnclaim;
  }

  /**
   * @description: 'secure/workflowEngine/unclaim取得
   * @param {}
   * @return: USER_NAME
   */
  get WorkflowsUnclaim(): string {
    return this._WorkflowUnclaim;
  }

  /**
   * @description: 'ssecure/workflowEngine/sendBack設定
   * @param {USER_NAME}
   * @return:
   */
  set WorkflowsSendBack(workflowsSendBack: string) {
    this._WorkflowsSendBack = workflowsSendBack;
  }

  /**
   * @description: 'secure/workflowEngine/taskForm?取得
   * @param {}
   * @return: USER_NAME
   */
  get WorkflowTaskForm(): string {
    return this._WorkflowTaskForm;
  }

  /**
   * @description: 'secure/workflowEngine/taskForm?設定
   * @param {USER_NAME}
   * @return:
   */
  set WorkflowTaskForm(workflowTaskForm: string) {
    this._WorkflowTaskForm = workflowTaskForm;
  }

  /**
   * @description: 'secure/workflowEngine/cancel?取得
   * @param {}
   * @return: USER_NAME
   */
  get WorkflowCancel(): string {
    return this._WorkflowCancel;
  }

  /**
   * @description: 'secure/workflowEngine/cancel?設定
   * @param {USER_NAME}
   * @return:
   */
  set WorkflowCancel(workflowCancel: string) {
    this._WorkflowCancel = workflowCancel;
  }

  /**
   * @description: 'secure/workflowEngine/mobile/saveTemporaryFromScanTask?取得
   * @param {}
   * @return: USER_NAME
   */
  get WorkflowsSaveTemporaryFromScanTask(): string {
    return this._WorkflowsSaveTemporaryFromScanTask;
  }

  /**
   * @description: 'secure/workflowEngine/mobile/saveTemporaryFromScanTask?設定
   * @param {USER_NAME}
   * @return:
   */
  set WorkflowsSaveTemporaryFromScanTask(workflowsSaveTemporaryFromScanTask: string) {
    this._WorkflowsSaveTemporaryFromScanTask = workflowsSaveTemporaryFromScanTask;
  }

  /**
   * @description: 'secure/workflowEngine/saveTemporaryFromApprover?取得
   * @param {}
   * @return: USER_NAME
   */
  get WorkflowssaveTemporaryFromApprover(): string {
    return this._WorkflowssaveTemporaryFromApprover;
  }

  /**
   * @description: 'secure/workflowEngine/saveTemporaryFromApprover?設定
   * @param {USER_NAME}
   * @return:
   */
  set WorkflowssaveTemporaryFromApprover(workflowssaveTemporaryFromApprover: string) {
    this._WorkflowssaveTemporaryFromApprover = workflowssaveTemporaryFromApprover;
  }

  /**
   * @description: 'secure/workflowEngine/saveTemporary取得
   * @param {}
   * @return: USER_NAME
   */
  get WorkflowSaveTemporary(): string {
    return this._WorkflowSaveTemporary;
  }

  /**
   * @description: 'secure/workflowEngine/saveTemporary設定
   * @param {USER_NAME}
   * @return:
   */
  set WorkflowSaveTemporary(workflowSaveTemporary: string) {
    this._WorkflowSaveTemporary = workflowSaveTemporary;
  }

  /**
   * @description: 'secure/workflowEngine/start取得
   * @param {}
   * @return: USER_NAME
   */
  get WorkflowStart(): string {
    return this._WorkflowStart;
  }

  /**
   * @description: 'secure/workflowEngine/start設定
   * @param {USER_NAME}
   * @return:
   */
  set WorkflowStart(workflowStart: string) {
    this._WorkflowStart = workflowStart;
  }

  /**
   * @description: 'secure/workflowEngine/startForm?取得
   * @param {}
   * @return: USER_NAME
   */
  get WorkFlowStartForm(): string {
    return this._WorkFlowStartForm;
  }

  /**
   * @description: 'secure/workflowEngine/startForm?設定
   * @param {USER_NAME}
   * @return:
   */
  set WorkFlowStartForm(workFlowStartForm: string) {
    this._WorkFlowStartForm = workFlowStartForm;
  }

  get WorkflowAssetColumn(): string {
    return this._WorkflowAssetColumn;
  }

  set WorkflowAssetColumn(value: string) {
    this._WorkflowAssetColumn = value;
  }

  /**
   * @description: 'secure/workflowEngine/updateScanTaskLock'
   * @param {}
   * @return: URL
   */
  get UpdateScanTaskLock(): string {
    return this._UpdateScanTaskLock;
  }

  /**
   * @description: ''secure/workflowEngine/updateScanTaskLock'
   * @param {}
   * @return:
   */
  set UpdateScanTaskLock(updateScanTaskLock: string) {
    this._UpdateScanTaskLock = updateScanTaskLock;
  }

  /**
   * @description: 'secure/s3/uploadUrl?取得
   * @param {}
   * @return: USER_NAME
   */
  get SecureUploadUrl(): string {
    return this._SecureUploadUrl;
  }

  /**
   * @description: 'secure/s3/uploadUrl?設定
   * @param {USER_NAME}
   * @return:
   */
  set SecureUploadUrl(secureUploadUrl: string) {
    this._SecureUploadUrl = secureUploadUrl;
  }

  /**
   * @description: 'secure/User/searchByKeyword?'取得
   * @param {}
   * @return: USER_NAME
   */
  get SearchByKeyword(): string {
    return this._SearchByKeyword;
  }

  /**
   * @description: 'secure/User/searchByKeyword?'設定
   * @param {USER_NAME}
   * @return:
   */
  set SearchByKeyword(searchByKeyword: string) {
    this._SearchByKeyword = searchByKeyword;
  }

  /**
   * @description: 'secure/Asset/mobile/hasPrintTemplateMapping?'取得
   */
  get GetAssetsPrintAble(): string {
    return this._GetAssetsPrintAble;
  }

  /**
   * @description: 'secure/Asset/mobile/hasPrintTemplateMapping?'設定
   */
  set GetAssetsPrintAble(getAssetsPrintAble: string) {
    this._GetAssetsPrintAble = getAssetsPrintAble;
  }
  /**
   * @description: '/secure/Asset/mobile/findByKeywordAndAssetIdList'取得
   */
  get FindAssetsByKeyword(): string {
    return this._FindAssetsByKeyword;
  }

  /**
   * @description: '/secure/Asset/mobile/findByKeywordAndAssetIdList'設定
   */
  set FindAssetsByKeyword(findAssetsByKeyword: string) {
    this._FindAssetsByKeyword = findAssetsByKeyword;
  }

  /**
   * @description: 'secure/AppurtenancesInformation/insert?'取得
   * @param {}
   * @return: USER_NAME
   */
  get SecureAppurtenancesInformationtInsert(): string {
    return this._SecureAppurtenancesInformationtInsert;
  }

  /**
   * @description: 'secure/AppurtenancesInformation/insert?'設定
   * @param {USER_NAME}
   * @return:
   */
  set SecureAppurtenancesInformationtInsert(secureAppurtenancesInformationtInsert: string) {
    this._SecureAppurtenancesInformationtInsert = secureAppurtenancesInformationtInsert;
  }

  /**
   * @description: 'secure/LayoutSetting?'取得
   * @param {}
   * @return: USER_NAME
   */
  get SecureAppurtenancesInformationtUpdate(): string {
    return this._SecureAppurtenancesInformationtUpdate;
  }

  /**
   * @description: 'secure/AppurtenancesInformation/update?'設定
   * @param {USER_NAME}
   * @return:
   */
  set SecureAppurtenancesInformationtUpdate(secureAppurtenancesInformationtUpdate: string) {
    this._SecureAppurtenancesInformationtUpdate = secureAppurtenancesInformationtUpdate;
  }

  /**
   * @description: 'secure/AppurtenancesInformation/delete'取得
   * @param {}
   * @return:
   */
  get SecureAppurtenancesInformationtDelete(): string {
    return this._SecureAppurtenancesInformationtDelete;
  }

  /**
   * @description: 'secure/AppurtenancesInformation/delete'設定
   * @param {secureAppurtenancesInformationtDelete}
   * @return:
   */
  set SecureAppurtenancesInformationtDelete(secureAppurtenancesInformationtDelete: string) {
    this._SecureAppurtenancesInformationtDelete = secureAppurtenancesInformationtDelete;
  }

  /**
   * @description: 'secure/LayoutSetting?'取得
   * @param {}
   * @return: USER_NAME
   */
  get SecureAppurtenancesInformationtLayoutSetting(): string {
    return this._SecureAppurtenancesInformationtLayoutSetting;
  }

  /**
   * @description: 'secure/LayoutSetting?'設定
   * @param {USER_NAME}
   * @return:
   */
  set SecureAppurtenancesInformationtLayoutSetting(secureAppurtenancesInformationtLayoutSetting: string) {
    this._SecureAppurtenancesInformationtLayoutSetting = secureAppurtenancesInformationtLayoutSetting;
  }

  // _GetAssetByBarcode
  /**
   * @description: '/secure/Asset/findByBarCodeForMobile?'取得
   * @param {}
   * @return: USER_NAME
   */
  get GetAssetByBarcode(): string {
    return this._GetAssetByBarcode;
  }

  /**
   * @description: '/secure/Asset/findByBarCodeForMobile?'設定
   * @param {USER_NAME}
   * @return:
   */
  set GetAssetByBarcode(getAssetByBarcode: string) {
    this._GetAssetByBarcode = getAssetByBarcode;
  }

  /**
   * @description: 'secure/AppurtenancesInformationType/getMobileSetting?'取得
   * @param {}
   * @return: USER_NAME
   */
  get SecureAppurtenancesInformationtMobileSetting(): string {
    return this._SecureAppurtenancesInformationtMobileSetting;
  }

  /**
   * @description: 'secure/AppurtenancesInformationType/getMobileSetting?'設定
   * @param {USER_NAME}
   * @return:
   */
  set SecureAppurtenancesInformationtMobileSetting(secureAppurtenancesInformationtMobileSetting: string) {
    this._SecureAppurtenancesInformationtMobileSetting = secureAppurtenancesInformationtMobileSetting;
  }

  /**
   * @description: 'secure/AppurtenancesInformation/mobile/appurtenancesInformationByTypeId?'取得
   * @param {}
   * @return: USER_NAME
   */
  get SecureAppurtenancesInformation(): string {
    return this._SecureAppurtenancesInformation;
  }

  /**
   * @description: 'secure/AppurtenancesInformation/mobile/appurtenancesInformationByTypeId?'設定
   * @param {USER_NAME}
   * @return:
   */
  set SecureAppurtenancesInformation(secureAppurtenancesInformation: string) {
    this._SecureAppurtenancesInformation = secureAppurtenancesInformation;
  }

  // GetAppurtenancesInformationListById
  /**
   * @description: '/secure/AppurtenancesInformation/getAppurtenancesInformationListById?'取得
   * @param {}
   */
  get GetAppurtenancesInformationListById(): string {
    return this._GetAppurtenancesInformationListById;
  }

  /**
   * @description: '/secure/AppurtenancesInformation/getAppurtenancesInformationListById?'設定
   * @return:
   */
  set GetAppurtenancesInformationListById(getAppurtenancesInformationListById: string) {
    this._GetAppurtenancesInformationListById = getAppurtenancesInformationListById;
  }

  /**
   * @description: secure/AppurtenancesInformation/mobile/appurtenancesInformationDataCount?取得
   * @param {}
   * @return: USER_NAME
   */
  get SecureAppurtenancesInformationCount(): string {
    return this._SecureAppurtenancesInformationCount;
  }

  /**
   * @description: secure/AppurtenancesInformation/mobile/appurtenancesInformationDataCount?'設定
   * @param {USER_NAME}
   * @return:
   */
  set SecureAppurtenancesInformationCount(secureAppurtenancesInformationCount: string) {
    this._SecureAppurtenancesInformationCount = secureAppurtenancesInformationCount;
  }

  /**
   * @description: secure/Asset/insert/mobile?得
   * @param {}
   * @return: USER_NAME
   */
  get SecureAssetInsert(): string {
    return this._SecureAssetInsert;
  }

  /**
   * @description: secure/Asset/insert/mobile?'設定
   * @param {USER_NAME}
   * @return:
   */
  set SecureAssetInsert(secureAssetInsert: string) {
    this._SecureAssetInsert = secureAssetInsert;
  }

  /**
   * @description: secure/Asset/update/mobile?得
   * @param {}
   * @return: USER_NAME
   */
  get SecureAssetSave(): string {
    return this._SecureAssetSave;
  }

  /**
   * @description: secure/Asset/update/mobile?設定
   * @param {USER_NAME}
   * @return:
   */
  set SecureAssetSave(secureAssetSave: string) {
    this._SecureAssetSave = secureAssetSave;
  }

  /**

   * @description: secure/AssetType?得
   * @param {}
   * @return: USER_NAME
   */
  get SecureAssetType(): string {
    return this._SecureAssetType;
  }

  /**
   * @description: secure/AssetType?設定
   * @param {USER_NAME}
   * @return:
   */
  set SecureAssetType(secureAssetType: string) {
    this._SecureAssetType = secureAssetType;
  }

  /**
   * @description: secure/login/record/mobile得
   * @param {}
   * @return: USER_NAME
   */
  get SecureLoginRecord(): string {
    return this._SecureLoginRecord;
  }

  /**
   * @description: secure/login/record/mobile設定
   * @param {USER_NAME}
   * @return:
   */
  set SecureLoginRecord(secureLoginRecord: string) {
    this._SecureLoginRecord = secureLoginRecord;
  }

  /**
   * @description: secure/systemMobileDeployManage/mobileApiHost/lastedApi得
   * @param {}
   * @return: USER_NAME
   */
  get SecureGetLastedApi(): string {
    return this._SecureGetLastedApi;
  }

  /**
   * @description: secure/systemMobileDeployManage/mobileApiHost/lastedApi設定
   * @param {USER_NAME}
   * @return:
   */
  set SecureGetLastedApi(secureGetLastedApi: string) {
    this._SecureGetLastedApi = secureGetLastedApi;
  }

  /**
   * @description: secure/AssetItem?得
   * @param {}
   * @return: USER_NAME
   */
  get SecureAssetItem(): string {
    return this._SecureAssetItem;
  }

  /**
   * @description: secure/AssetItem?設定
   * @param {USER_NAME}
   * @return:
   */
  set SecureAssetItem(secureAssetItem: string) {
    this._SecureAssetItem = secureAssetItem;
  }

  /**
   * @description: secure/AssetMobileSetting/forMobile?得
   * @param {}
   * @return: USER_NAME
   */
  get SecureMobileSetting(): string {
    return this._SecureMobileSetting;
  }

  /**
   * @description: secure/AssetMobileSetting/forMobile?設定
   * @param {USER_NAME}
   * @return:
   */
  set SecureMobileSetting(secureMobileSetting: string) {
    this._SecureMobileSetting = secureMobileSetting;
  }

  /**
   * @description: secure/cognito/signIn?得
   * @param {}
   * @return: USER_NAME
   */
  get SecureSignIn(): string {
    return this._SecureSignIn;
  }

  /**
   * @description: secure/cognito/signIn?設定
   * @param {USER_NAME}
   * @return:
   */
  set SecureSignIn(secureSignIn: string) {
    this._SecureSignIn = secureSignIn;
  }

  /**
   * @description: secure/cognito/logout?得
   * @param {}
   * @return: SecureLogout
   */
  get SecureLogout(): string {
    return this._SecureLogout;
  }

  /**
   * @description: secure/cognito/logout?設定
   * @param {secureLogout}
   * @return:
   */
  set SecureLogout(secureLogout: string) {
    this._SecureLogout = secureLogout;
  }

  /**
   * @description: secure/Asset?得
   * @param {}
   * @return: USER_NAME
   */
  get SecureAsset(): string {
    return this._SecureAsset;
  }

  /**
   * @description: secure/Asset?設定
   * @param {USER_NAME}
   * @return:
   */
  set SecureAsset(secureAsset: string) {
    this._SecureAsset = secureAsset;
  }

  /**
   * @description: _AppVersion取得
   * @param {}
   * @return: USER_NAME
   */
  get AppVersion(): string {
    return this._AppVersion;
  }

  /**
   * @description: _AppVersion設定
   * @param {USER_NAME}
   * @return:
   */
  set AppVersion(appVersion: string) {
    this._AppVersion = appVersion;
  }

  /**
   * @description: USER_NAME取得
   * @param {}
   * @return: USER_NAME
   */
  get USER_NAME(): string {
    return this._USER_NAME;
  }

  /**
   * @description: USER_NAME設定
   * @param {USER_NAME}
   * @return:
   */
  set USER_NAME(userName: string) {
    this._USER_NAME = userName;
  }

  /**
   * @description: TOKEN取得
   * @param {}
   * @return: TOKEN
   */
  get TOKEN(): string {
    return this._TOKEN;
  }

  /**
   * @description: TOKEN設定
   * @param {TOKEN}
   * @return:
   */

  set TOKEN(token: string) {
    this._TOKEN = token;
  }

  /**
   * @description: USER_ID取得
   * @param {}
   * @return: USER_ID
   */

  get USER_ID(): string {
    return this._USER_ID;
  }

  /**
   * @description: USER_ID設定
   * @param {USER_ID}
   * @return:
   */

  set USER_ID(userId: string) {
    this._USER_ID = userId;
  }

  /**
   * @description: TENANT_ID取得
   * @param {}
   * @return: TENANT_ID
   */

  get TENANT_ID(): string {
    return this._TENANT_ID;
  }

  /**
   * @description: TENANT_ID設定
   * @param {TENANT_ID}
   * @return:
   */

  set TENANT_ID(tenantId: string) {
    this._TENANT_ID = tenantId;
  }

  get GetUserInfoApi(): string {
    return this._GetUserInfoApi;
  }

  /**
   * @description: secure/User/getById
   * @param {USER_NAME}
   * @return:
   */
  set GetUserInfoApi(getUserInfoApi: string) {
    this._GetUserInfoApi = getUserInfoApi;
  }

  get MyPageUpdateUserInfo(): string {
    return this._MyPageUpdateUserInfo;
  }

  /**
   * @description: secure/User/updateBase
   * @param {USER_NAME}
   * @return:
   */
  set MyPageUpdateUserInfo(myPageUpdateUserInfo: string) {
    this._MyPageUpdateUserInfo = myPageUpdateUserInfo;
  }

  get GetUserRole(): string {
    return this._GetUserRole;
  }

  /**
   * @description: secure/User/updateBase
   * @param {USER_NAME}
   * @return:
   */
  set GetUserRole(getUserRole: string) {
    this._GetUserRole = getUserRole;
  }

  get GetTenantInfo(): string {
    return this._GetTenantInfo;
  }

  /**
   * @description: secure/User/updateBase
   * @param {USER_NAME}
   * @return:
   */
  set GetTenantInfo(getTenantInfo: string) {
    this._GetTenantInfo = getTenantInfo;
  }

  get GetVerificationCode(): string {
    return this._GetVerificationCode;
  }

  /**
   * @description: secure/User/updateBase
   * @param {USER_NAME}
   * @return:
   */
  set GetVerificationCode(getVerificationCode: string) {
    this._GetVerificationCode = getVerificationCode;
  }

  get GetEmailVerificationCode(): string {
    return this._GetEmailVerificationCode;
  }

  set GetEmailVerificationCode(getVerificationCode: string) {
    this._GetEmailVerificationCode = getVerificationCode;
  }

  get ChangeBindPhone(): string {
    return this._ChangeBindPhone;
  }

  /**
   * @description: secure/User/updateBase
   * @param {USER_NAME}
   * @return:
   */
  set ChangeBindPhone(changeBindPhone: string) {
    this._ChangeBindPhone = changeBindPhone;
  }

  get UserBindPhone(): string {
    return this._UserBindPhone;
  }

  /**
   * @description: secure/User/updateBase
   * @param {USER_NAME}
   * @return:
   */
  set UserBindPhone(userBindPhone: string) {
    this._UserBindPhone = userBindPhone;
  }

  get ActionDataListCount(): string {
    return this._ActionDataListCount;
  }

  /**
   * @description: secure/User/updateBase
   * @param {USER_NAME}
   * @return:
   */
  set ActionDataListCount(actionDataListCount: string) {
    this._ActionDataListCount = actionDataListCount;
  }

  get ActionVerifyAssetRegistrationStatus(): string {
    return this._ActionVerifyAssetRegistrationStatus;
  }

  /**
   * @description: secure/AssetActionList/verify
   * @return:
   */
  set ActionVerifyAssetRegistrationStatus(actionVerifyAssetRegistrationStatus: string) {
    this._ActionVerifyAssetRegistrationStatus = actionVerifyAssetRegistrationStatus;
  }

  get ActionVerifyAssetRegistrationStatusByCarNumber(): string {
    return this._ActionVerifyAssetRegistrationStatusByCarNumber;
  }

  /**
   * @description: secure/AssetActionList/verify/ocrIdentityCode
   * @return:
   */
  set ActionVerifyAssetRegistrationStatusByCarNumber(actionVerifyAssetRegistrationStatusByCarNumber: string) {
    this._ActionVerifyAssetRegistrationStatusByCarNumber = actionVerifyAssetRegistrationStatusByCarNumber;
  }

  public get ActionSettingSearch(): string {
    return this._ActionSettingSearch;
  }

  public set ActionSettingSearch(value: string) {
    this._ActionSettingSearch = value;
  }

  public get ActionListSearch(): string {
    return this._ActionListSearch;
  }

  public set ActionListSearch(value: string) {
    this._ActionListSearch = value;
  }

  get ActionGetSelectedAssetList(): string {
    return this._ActionGetSelectedAssetList;
  }

  set ActionGetSelectedAssetList(value: string) {
    this._ActionGetSelectedAssetList = value;
  }

  get ActionData(): string {
    return this._ActionData;
  }

  /**
   * @description: secure/User/updateBase
   * @param {USER_NAME}
   * @return:
   */
  set ActionData(actionData: string) {
    this._ActionData = actionData;
  }

  public get NewWorkflowList(): string {
    return this._NewWorkflowList;
  }

  /**
   * @description: secure/workflowEngine/mobile/list
   * @param {WORKFLOW_TYPE}
   * @return:
   */
  public set NewWorkflowList(newWorkflowList: string) {
    this._NewWorkflowList = newWorkflowList;
  }

  get ActionGetItemById(): string {
    return this._ActionGetItemById;
  }

  /**
   * @description: secure/User/updateBase
   * @param {USER_NAME}
   * @return:
   */
  set ActionGetItemById(actionGetItemById: string) {
    this._ActionGetItemById = actionGetItemById;
  }

  get ActionGetItemByAssetType(): string {
    return this._ActionGetItemByAssetType;
  }

  /**
   * @description: secure/User/updateBase
   * @param {USER_NAME}
   * @return:
   */
  set ActionGetItemByAssetType(actionGetItemByAssetType: string) {
    this._ActionGetItemByAssetType = actionGetItemByAssetType;
  }

  get ActionGetMasterByMasterTypeId(): string {
    return this._ActionGetMasterByMasterTypeId;
  }

  /**
   * @description: secure/User/updateBase
   * @param {USER_NAME}
   * @return:
   */
  set ActionGetMasterByMasterTypeId(actionGetMasterByMasterTypeId: string) {
    this._ActionGetMasterByMasterTypeId = actionGetMasterByMasterTypeId;
  }

  get ActionGetMasterInfoById(): string {
    return this._ActionGetMasterInfoById;
  }

  /**
   * @description: secure/User/updateBase
   * @param {USER_NAME}
   * @return:
   */
  set ActionGetMasterInfoById(actionGetMasterInfoById: string) {
    this._ActionGetMasterInfoById = actionGetMasterInfoById;
  }

  get ActionGetRevisedMasterInfoById(): string {
    return this._ActionGetRevisedMasterInfoById;
  }

  /**
   * @description: secure/User/updateBase
   * @param {USER_NAME}
   * @return:
   */
  set ActionGetRevisedMasterInfoById(actionGetRevisedMasterInfoById: string) {
    this._ActionGetRevisedMasterInfoById = actionGetRevisedMasterInfoById;
  }

  public get ApplicationList(): string {
    return this._ApplicationList;
  }

  /**
   * @description: secure/workflowEngine/searchTaskList?
   * @param {workflowEngineSearchTaskCondition}
   * @return:
   */
  public set ApplicationList(applicationList: string) {
    this._ApplicationList = applicationList;
  }

  get ActionUpdateAssetActionData(): string {
    return this._ActionUpdateAssetActionData;
  }

  /**
   * @description: secure/User/updateBase
   * @param {USER_NAME}
   * @return:
   */
  set ActionUpdateAssetActionData(actionUpdateAssetActionData: string) {
    this._ActionUpdateAssetActionData = actionUpdateAssetActionData;
  }

  get ActionInsertAssetActionData(): string {
    return this._ActionInsertAssetActionData;
  }

  /**
   * @description: secure/User/updateBase
   * @param {USER_NAME}
   * @return:
   */
  set ActionInsertAssetActionData(actionInsertAssetActionData: string) {
    this._ActionInsertAssetActionData = actionInsertAssetActionData;
  }

  get ActionTransitAssetActionData(): string {
    return this._ActionTransitAssetActionData;
  }

  /**
   * @description: secure/User/updateBase
   * @param {USER_NAME}
   * @return:
   */
  set ActionTransitAssetActionData(actionTransitAssetActionData: string) {
    this._ActionTransitAssetActionData = actionTransitAssetActionData;
  }

  get ActionGetCustomItem(): string {
    return this._ActionGetCustomItem;
  }

  /**
   * @description: secure/User/updateBase
   * @param {USER_NAME}
   * @return:
   */
  set ActionGetCustomItem(actionGetCustomItem: string) {
    this._ActionGetCustomItem = actionGetCustomItem;
  }

  /**
   * @description: secure/AssetActionList/getBarcodeAndLocation?
   * @param {USER_NAME}
   * @return:
   */
  get ActionGetBarcodeAndLocation(): string {
    return this._ActionGetBarcodeAndLocation;
  }

  /**
   * @description: secure/AssetActionList/getBarcodeAndLocation?
   * @param {USER_NAME}
   * @return:
   */
  set ActionGetBarcodeAndLocation(actionGetBarcodeAndLocation: string) {
    this._ActionGetBarcodeAndLocation = actionGetBarcodeAndLocation;
  }

  public get getLocationList(): string {
    return this._getLocationList;
  }

  /**
   * secure/Tenant/location/list
   */
  public set getLocationList(value: string) {
    this._getLocationList = value;
  }

  /**
   * @description: 'secure/AssetSearchName?取得'
   * @param {}
   * @return:
   */
  get GetSearchConditions(): string {
    return this._GetSearchConditions;
  }

  /**
   * @description: 'secure/AssetSearchName?設定'
   * @param
   * @return:
   */
  set GetSearchConditions(getSearchConditions: string) {
    this._WorkflowUnclaim = getSearchConditions;
  }

  /**
   * @description: 'secure/Asset/findAll?取得'
   * @param {}
   * @return:
   */
  get GetAssetsBySearchCondition(): string {
    return this._GetAssetsBySearchCondition;
  }

  /**
   * @description: 'secure/Asset/findAll?設定'
   * @param
   * @return:
   */
  set GetAssetsBySearchCondition(getAssetsBySearchCondition: string) {
    this._WorkflowUnclaim = getAssetsBySearchCondition;
  }

  /**
   * @description: 'secure/Asset?取得'
   * @param {}
   * @return: USER_NAME
   */
  get GetAssetsOneMonthOrAll(): string {
    return this._GetAssetsOneMonthOrAll;
  }

  /**
   * @description: 'secure/Asset?設定'
   * @param {USER_NAME}
   * @return:
   */
  set GetAssetsOneMonthOrAll(getAssetsOneMonthOrAll: string) {
    this._GetAssetsOneMonthOrAll = getAssetsOneMonthOrAll;
  }

  /**
   * @description: 'secure/assetItemCommon/list?取得'
   * @param {}
   * @return: assetItemCommon
   */
  get GetAssetItemCommon(): string {
    return this._GetAssetItemCommon;
  }

  /**
   * @description: 'secure/assetItemCommon/list?設定'
   * @param {USER_NAME}
   * @return:
   */
  set GetAssetItemCommon(getAssetItemCommon: string) {
    this._GetAssetItemCommon = getAssetItemCommon;
  }

  /**
   * @description: 'secure/Asset?取得'
   * @param {}
   * @return: USER_NAME
   */
  get GetAssetAuthority(): string {
    return this._GetAssetAuthority;
  }

  /**
   * @description: 'secure/Asset?設定'
   * @param {USER_NAME}
   * @return:
   */
  set GetAssetAuthority(getAssetAuthority: string) {
    this._GetAssetAuthority = getAssetAuthority;
  }

  /**
   * @description: 'secure/Role取得'
   * @param {}
   * @return: ROLE
   */
  get GetRoleList(): string {
    return this._getRoleList;
  }

  /**
   * @description: 'ssecure/Role'
   * @param {ROLE}
   * @return:
   */
  set GetRoleList(getRoleList: string) {
    this._getRoleList = getRoleList;
  }

  /**
   * @description: 'login tenant'
   * @param {}
   * @return:
   */
  get SecureSignInTenant(): string {
    return this._SecureSignInTenant;
  }

  /**
   * @description: ''
   * @param
   * @return:
   */
  set SecureSignInTenant(secureSignInTenant: string) {
    this._SecureSignInTenant = secureSignInTenant;
  }

  /**
   * @description: 'login tenant'
   * @param {}
   * @return:
   */
  get ActionAssetActionListDelete(): string {
    return this._ActionAssetActionListDelete;
  }

  /**
   * @description: ''
   * @param
   * @return:
   */
  set ActionAssetActionListDelete(actionAssetActionListDelete: string) {
    this._ActionAssetActionListDelete = actionAssetActionListDelete;
  }

  /**
   * @description: 'file delete from S3'
   * @param {}
   * @return:
   */
  get FileDelete(): string {
    return this._FileDelete;
  }

  /**
   * @description: ''
   * @param
   * @return:
   */
  set FileDelete(fileDelete: string) {
    this._FileDelete = fileDelete;
  }

  get GetCategoryCountForMobile(): string {
    return this._GetCategoryCountForMobile;
  }

  public get CustomizeSearch(): string {
    return this._CustomizeSearch;
  }

  public get GetAppointmentListByAssetId(): string {
    return this._GetAppointmentListByAssetId;
  }

  get UnPromission(): string {
    return this._UnPromission;
  }

  get GetTenant(): string {
    return this._GetTenant;
  }

  get GetHomePageListByMobileHomeSetting(): string {
    return this._GetHomePageListByMobileHomeSetting;
  }

  get GetHomePageProcessSummary(): string {
    return this._GetHomePageProcessSummary;
  }

  get WorkflowAssignDynamicTaskListByTaskId(): string {
    return this._WorkflowAssignDynamicTaskListByTaskId;
  }

  get WorkFlowSearchByGroupIds(): string {
    return this._WorkFlowSearchByGroupIds;
  }

  get WorkFlowGetAssignDynamicGroupList(): string {
    return this._WorkFlowGetAssignDynamicGroupList;
  }

  get GetWorkFlowApplicationCount(): string {
    return this._GetWorkFlowApplicationCount;
  }

  get GetCopyAppurtenancesInformationListById(): string {
    return this._GetCopyAppurtenancesInformationListById;
  }

  get ActionGetAssetActionById(): string {
    return this._ActionGetAssetActionById;
  }

  get ActionOfflineAssetActionById(): string {
    return this._ActionOfflineGetAssetActionById;
  }

  get ActionOfflineAssetActionBatch() {
    return this._ActionOfflineAssetActionBatch;
  }

  get ActionOffllineSearch() {
    return this._ActionOffllineSearch;
  }

  get GetAssetInfoByRfid(): string {
    return this._GetAssetInfoByRfid;
  }

  get CustomizeSearchForMobile(): string {
    return this._CustomizeSearchForMobile;
  }

  get CustomizeSearchCountForMobile(): string {
    return this._CustomizeSearchCountForMobile;
  }

  get GetGeocodeByAddress(): string {
    return this._GetGeocodeByAddress;
  }

  set GetGeocodeByAddress(getGeocodeByAddress: string) {
    this._GetGeocodeByAddress = getGeocodeByAddress;
  }

  /**
   * @description【mobile】【特定アサイン用】ページ割り付き、WF資産リストにて、資産ID、及び、資産テキストの項目値をキーワードとする検索
   * @param {}
   * @return
   */
  get GetAssetsByKeywordInWfAssetListForAssignScan(): string {
    return this._GetAssetsByKeywordInWfAssetListForAssignScan;
  }

  /**
   * @description【mobile】【特定アサイン用】ページ割り付き、WF資産リストにて、資産ID、及び、資産テキストの項目値をキーワードとする検索
   * @param {USER_NAME}
   * @return
   */
  set GetAssetsByKeywordInWfAssetListForAssignScan(getAssetsByKeywordInWfAssetListForAssignScan: string) {
    this._GetAssetsByKeywordInWfAssetListForAssignScan = getAssetsByKeywordInWfAssetListForAssignScan;
  }

  /**
   * @description user task【特定アサイン用】対象資産リストデータのスキャン状態、スキャン数量を元（今回のスキャン前）に戻す
   * @param {}
   * @return
   */
  get RestoreAssetScanStateForAssignScan(): string {
    return this._RestoreAssetScanStateForAssignScan;
  }

  /**
   * @description scan task【特定アサイン用】対象資産リストデータのスキャン状態、スキャン数量を元（今回のスキャン前）に戻す
   * @param {USER_NAME}
   * @return
   */
  set RestoreAssetScanStateForAssignScan(restoreAssetScanStateForAssignScan: string) {
    this._RestoreAssetScanStateForAssignScan = restoreAssetScanStateForAssignScan;
  }

  /**
   * @description user task【特定アサイン用】対象資産リストデータのスキャン状態、スキャン数量を元（今回のスキャン前）に戻す
   */
  get RestoreAssetScanState(): string {
    return this._RestoreAssetScanState;
  }

  /**
   * @description user task【特定アサイン用】対象資産リストデータのスキャン状態、スキャン数量を元（今回のスキャン前）に戻す
   */
  set RestoreAssetScanState(restoreAssetScanState: string) {
    this._RestoreAssetScanState = restoreAssetScanState;
  }

  /**
   * @descriptionmobile モバイル用資産リストデータのスキャン数量を更新（特定アサイン）
   * @param {}
   * @return
   */
  get AmountAssignScan(): string {
    return this._AmountAssignScan;
  }

  /**
   * @description モバイル用資産リストデータのスキャン数量を更新（特定アサイン）
   * @param {USER_NAME}
   * @return
   */
  set AmountAssignScan(amountAssignScan: string) {
    this._AmountAssignScan = amountAssignScan;
  }

  /**
   * @descriptionmobile 往资产list里新规追加资产
   * @param {}
   * @return
   */
  get WorkflowMobUpdateByBarCodet(): string {
    return this._WorkflowMobUpdateByBarCodet;
  }

  /**
   * @description 往资产list里新规追加资产
   * @param {}
   * @return
   */
  set WorkflowMobUpdateByBarCodet(workflowMobUpdateByBarCodet: string) {
    this._WorkflowMobUpdateByBarCodet = workflowMobUpdateByBarCodet;
  }

  get getNotificationList(): string {
    return this._getNotificationList;
  }

  set getNotificationList(list: string) {
    this._getNotificationList = list;
  }

  get getNotificationListByPage(): string {
    return this._getNotificationListByPage;
  }

  set getNotificationListByPage(list: string) {
    this._getNotificationListByPage = list;
  }

  get getNotificationCount(): string {
    return this._getNotificationCount;
  }

  set getNotificationCount(count: string) {
    this._getNotificationCount = count;
  }

  get getNotificationAnyNew(): string {
    return this._getNotificationAnyNew;
  }

  set getNotificationAnyNew(anyNew: string) {
    this._getNotificationAnyNew = anyNew;
  }

  get getNotificationById(): string {
    return this._getNotificationById;
  }

  set getNotificationById(notificationId: string) {
    this._getNotificationById = notificationId;
  }

  get setNotificationReadStatus(): string {
    return this._setNotificationReadStatus;
  }

  set setNotificationReadStatus(notificationId: string) {
    this._setNotificationReadStatus = notificationId;
  }

  get AppFontSizeNormal(): string {
    return this._APP_FONTSIZE_NORMAL;
  }

  get AppFontSizeBig(): string {
    return this._APP_FONTSIZE_BIG;
  }

  /**
   * 一時保存の棚卸処理関連情報を取得
   */
  public get getSavedInventoryAssetActionInfo(): string {
    return this._GetSavedInventoryAssetActionInfo;
  }
  /**
   * 一時保存の棚卸処理関連情報を取得
   */
  public set getSavedInventoryAssetActionInfo(getSavedInventoryAssetActionInfo: string) {
    this._GetSavedInventoryAssetActionInfo = getSavedInventoryAssetActionInfo;
  }

  /**
   * 処理プロセスをロックする
   */
  public get lockAssetActionData(): string {
    return this._LockAssetActionData;
  }
  /**
   * 処理プロセスをロックする
   */
  public set lockAssetActionData(lockAssetActionData: string) {
    this._LockAssetActionData = lockAssetActionData;
  }

  /**
   * 処理プロセスをロック解除する
   */
  public get unlockAssetActionData(): string {
    return this._UnlockAssetActionData;
  }
  /**
   * 処理プロセスをロック解除する
   */
  public set unlockAssetActionData(unlockAssetActionData: string) {
    this._UnlockAssetActionData = unlockAssetActionData;
  }

  /**
   * プロセスIDで抽出条件に合わせる資産リストを取得
   */
  public get getAssetListByProcessId(): string {
    return this._GetAssetListByProcessId;
  }
  /**
   * プロセスIDで抽出条件に合わせる資産リストを取得
   */
  public set getAssetListByProcessId(getAssetListByProcessId: string) {
    this._GetAssetListByProcessId = getAssetListByProcessId;
  }

  public get APP_PAGE_LIMITED_NUMBER(): number {
    return this._APP_PAGE_LIMITED_NUMBER;
  }

  get APP_PAGE_ITEM_NUMBER(): number {
    return this._APP_PAGE_ITEM_NUMBER;
  }

  public get WorkflowCreateAssetList(): string {
    return this._WorkflowCreateAssetList;
  }

  /**
   * @description /secure/User/getFullUserInfo
   */
  public get getFullUserInfo(): string {
    return this._getFullUserInfo;
  }

  public get getAuthHostUrls(): string[] {
    return [this.SecureSignIn, this.SecureLogout, this.SsoTicket, this.SecureGetLastedApi, this.SecureGetMobile];
  }

  public get getNotificationUrls(): string[] {
    return [
      // this.getNotificationList,
      // this.getNotificationListByPage,
      // this.getNotificationCount,
      // this.getNotificationAnyNew,
      this.getNotificationById,
      // this.setNotificationReadStatus,
    ];
  }

  /**
   * 帳票
   */
  public get getReportGetById(): string {
    return this._getReportGetById;
  }
  /**
   * 帳票
   */
  public set getReportGetById(ReportGetById: string) {
    this._getReportGetById = ReportGetById;
  }

  get WorkflowGetWorkflowFormByEngineId(): string {
    return this._WorkflowGetWorkflowFormByEngineId;
  }

  get AssetListData(): string {
    return this._AssetListData;
  }

  get ReportDatabase(): string {
    return this._ReportDatabase;
  }

  /**
   * @description: secure/Asset/getAssetInfoByAssetIds
   * @param
   * @return:
   */
  get GetAssetInfoByAssetIds(): string {
    return this._getAssetInfoByAssetIds;
  }

  /**
   * @description: secure/AppurtenancesInformation/insertsForMobile
   * @param
   * @return:
   */
  get SetInsertsForMobile(): string {
    return this._insertsForMobile;
  }

  public set getGoolePlatformApiKeyWeb(getGoolePlatformApiKeyWeb: string) {
    this._getGoolePlatformApiKeyWeb = getGoolePlatformApiKeyWeb;
  }

  public get getGoolePlatformApiKeyWeb(): string {
    return this._getGoolePlatformApiKeyWeb;
  }

  /**
   * 处理设定一时保存资产获取
   * 処理設定で一時保存する資産を取得
   * @returns {string}
   * @constructor
   */
  public get ActionGetAssetListByProcessId(): string {
    return this._ActionGetAssetListByProcessId;
  }

  /**
   * 处理设定中用于检索资产信息
   * @returns {string}
   * @constructor
   */
  get AssetActionListVerify(): string {
    return this._AssetActionListVerify;
  }

  /**
   * 处理设定中 通过处理设定id 和资产id 批量检索资产信息
   */
  get AssetActionGetAssetlist(): string {
    return this._AssetActionGetAssetlist;
  }

  get CompareInfoForPreview(): string {
    return this._CompareInfoForPreview;
  }

  get getApiSettingList(): string {
    return this._getApiSettingList;
  }

  get getReportDb(): string {
    return this._getReportDb;
  }

}
