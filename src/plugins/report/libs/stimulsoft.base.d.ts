declare namespace Stimulsoft.Base {
    enum StiAnimationType {
        Opacity = 0,
        Scale = 1,
        Translation = 2,
        Rotation = 3,
        Column = 4,
        Points = 5,
        PieSegment = 6
    }
    enum StiTokenType {
        None = 0,
        Dot = 1,
        Comma = 2,
        Colon = 3,
        SemiColon = 4,
        <PERSON>hl = 5,
        <PERSON>hr = 6,
        Assign = 7,
        Equal = 8,
        NotEqual = 9,
        LeftEqual = 10,
        Left = 11,
        RightEqual = 12,
        Right = 13,
        Or = 14,
        And = 15,
        Not = 16,
        DoubleOr = 17,
        DoubleAnd = 18,
        Copyright = 19,
        Question = 20,
        Plus = 21,
        Minus = 22,
        Mult = 23,
        Div = 24,
        Splash = 25,
        Percent = 26,
        Ampersand = 27,
        <PERSON> = 28,
        <PERSON> = 29,
        <PERSON> = 30,
        DoublePlus = 31,
        DoubleMinus = 32,
        LPar = 33,
        RPar = 34,
        L<PERSON>race = 35,
        RBrace = 36,
        LBracket = 37,
        RBracket = 38,
        Value = 39,
        Ident = 40,
        Unknown = 41,
        EOF = 42
    }
    enum StiLevel {
        Basic = 0,
        Standard = 1,
        Professional = 2
    }
    enum StiAutoBool {
        Auto = 0,
        True = 1,
        False = 2
    }
    enum StiRelationDirection {
        ParentToChild = 1,
        ChildToParent = 0
    }
    enum StiGisDataType {
        Wkt = 0,
        GeoJSON = 1
    }
    enum StiSummaryColumnType {
        Sum = 0,
        Min = 1,
        Max = 2,
        Count = 3,
        Average = 4
    }
}
declare namespace Stimulsoft.Base {
    let IStiApp: System.Interface<IStiApp>;
    interface IStiApp extends IStiAppCell {
        getDictionary(): IStiAppDictionary;
    }
}
declare namespace Stimulsoft.Base {
    let IStiAppAlias: System.Interface<IStiAppAlias>;
    interface IStiAppAlias {
        getAlias(): string;
    }
}
declare namespace Stimulsoft.Base {
    let IStiAppCalcDataColumn: System.Interface<IStiAppCalcDataColumn>;
    interface IStiAppCalcDataColumn extends IStiAppDataColumn {
    }
}
declare namespace Stimulsoft.Base {
    import IAsIs = Stimulsoft.System.IAsIs;
    let IStiAppCell: System.Interface<IStiAppCell>;
    interface IStiAppCell extends IAsIs {
        getKey(): string;
        setKey(key: string): void;
    }
}
declare namespace Stimulsoft.Base {
    let IStiAppComponent: System.Interface<IStiAppComponent>;
    interface IStiAppComponent extends IStiAppCell {
        getName(): string;
        getApp(): IStiApp;
    }
}
declare namespace Stimulsoft.Base {
    let IStiAppConnection: System.Interface<IStiAppConnection>;
    interface IStiAppConnection extends IStiAppCell {
        getName(): string;
    }
}
declare namespace Stimulsoft.Base {
    let IStiAppDataCell: System.Interface<IStiAppDataCell>;
    interface IStiAppDataCell extends IStiAppCell {
        getName(): string;
    }
}
declare namespace Stimulsoft.Base {
    import Type = Stimulsoft.System.Type;
    let IStiAppDataColumn: System.Interface<IStiAppDataColumn>;
    interface IStiAppDataColumn extends IStiAppDataCell {
        getNameInSource(): string;
        getDataType(): Type;
    }
}
declare namespace Stimulsoft.Base {
    import List = Stimulsoft.System.Collections.List;
    let IStiAppDataRelation: System.Interface<IStiAppDataRelation>;
    interface IStiAppDataRelation extends IStiAppCell {
        getName(): string;
        getDictionary(): IStiAppDictionary;
        getParentDataSource(): IStiAppDataSource;
        getChildDataSource(): IStiAppDataSource;
        fetchParentColumns(): List<string>;
        fetchChildColumns(): List<string>;
        getActiveState(): boolean;
    }
}
declare namespace Stimulsoft.Base {
    import DataTable = Stimulsoft.System.Data.DataTable;
    import List = Stimulsoft.System.Collections.List;
    let IStiAppDataSource: System.Interface<IStiAppDataSource>;
    interface IStiAppDataSource extends IStiAppCell {
        getNameInSource(): string;
        getName(): string;
        getDataTable2(allowConnectToData: boolean): Promise<DataTable>;
        getDictionary(): IStiAppDictionary;
        fetchColumns(): List<IStiAppDataColumn>;
        getConnection(): IStiAppConnection;
        fetchParentRelations(activePreferred: boolean): List<IStiAppDataRelation>;
        fetchChildRelations(activePreferred: boolean): List<IStiAppDataRelation>;
        fetchColumnValues(names: List<string>): List<any[]>;
        fetchColumnValuesAsync(names: List<string>): Promise<List<any[]>>;
    }
}
declare namespace Stimulsoft.Base {
    import List = Stimulsoft.System.Collections.List;
    let IStiAppDictionary: System.Interface<IStiAppDictionary>;
    interface IStiAppDictionary {
        fetchDataSources(): List<IStiAppDataSource>;
        fetchDataRelations(): List<IStiAppDataRelation>;
        fetchVariables(): List<IStiAppVariable>;
        getDataSourceByName(name: string): IStiAppDataSource;
        getColumnByName(name: string): IStiAppDataColumn;
        getVariableByName(name: string): IStiAppVariable;
        getVariableValueByName(name: string): any;
        isSystemVariable(name: string): boolean;
        isReadOnlyVariable(name: string): boolean;
        getSystemVariableValue(name: string): any;
        getApp(): IStiApp;
        openConnections(connections: List<IStiAppConnection>): List<IStiAppConnection>;
        closeConnections(connections: List<IStiAppConnection>): void;
    }
}
declare namespace Stimulsoft.Base {
    let IStiAppExpressionCollection: System.Interface<IStiAppExpressionCollection>;
    interface IStiAppExpressionCollection {
        expressions: StiAppExpressionCollection;
    }
}
declare namespace Stimulsoft.Base {
    import List = Stimulsoft.System.Collections.List;
    let IStiAppFunction: System.Interface<IStiAppFunction>;
    interface IStiAppFunction extends IStiAppCell {
        getName(): string;
        invoke(arguments: List<any>): any;
    }
}
declare namespace Stimulsoft.Base {
    import Type = Stimulsoft.System.Type;
    let IStiAppVariable: System.Interface<IStiAppVariable>;
    interface IStiAppVariable extends IStiAppDataCell {
        getValue(): any;
        getCellType(): Type;
    }
}
declare namespace Stimulsoft.Base {
    import Font = Stimulsoft.System.Drawing.Font;
    let IStiGetFonts: System.Interface<IStiGetFonts>;
    interface IStiGetFonts {
        getFonts(): Font[];
    }
}
declare namespace Stimulsoft.Base {
    import List = Stimulsoft.System.Collections.List;
    let IStiReport: System.Interface<IStiReport>;
    interface IStiReport extends IStiApp {
        fetchPages(): List<IStiReportPage>;
    }
}
declare namespace Stimulsoft.Base {
    let IStiReportComponent: System.Interface<IStiReportComponent>;
    interface IStiReportComponent extends IStiAppComponent {
        getReport(): IStiReport;
    }
}
declare namespace Stimulsoft.Base {
    let IStiReportPage: System.Interface<IStiReportPage>;
    interface IStiReportPage extends IStiReportComponent {
        parseExpression(text: string, allowReturnNull: boolean): string;
    }
}
declare namespace Stimulsoft.Base {
    class StiActivator {
        static createObject(_type: Stimulsoft.System.Type): any;
        static createObject2(typeString: string): any;
    }
}
declare namespace Stimulsoft.Base {
    class StiAlignValue {
        static alignToMaxGrid(value: number, gridSize: number, aligningToGrid: boolean): number;
        static alignToMinGrid(value: number, gridSize: number, aligningToGrid: boolean): number;
        static alignToGrid(value: number, gridSize: number, aligningToGrid: boolean): number;
    }
}
declare namespace Stimulsoft.Base.JsonReportObject {
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import StiJson = Stimulsoft.Base.StiJson;
    let IStiJsonReportObject: System.Interface<IStiJsonReportObject>;
    interface IStiJsonReportObject {
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(jObject: StiJson): any;
    }
}
declare namespace Stimulsoft.Base {
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import ICloneable = Stimulsoft.System.ICloneable;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    class StiAppExpression implements ICloneable, IStiJsonReportObject {
        private static ImplementsStiAppExpression;
        implements(): any[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(jObject: StiJson): void;
        loadFromXml(xmlNode: XmlNode): void;
        private upperFirstChar;
        private lowerFirstChar;
        clone(): StiAppExpression;
        name: string;
        expression: string;
        get isEmpty(): boolean;
        constructor(name: string, expression: string);
    }
}
declare namespace Stimulsoft.Base {
    import CollectionBase = Stimulsoft.System.Collections.CollectionBase;
    import ICloneable = Stimulsoft.System.ICloneable;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    class StiAppExpressionCollection extends CollectionBase<StiAppExpression> implements ICloneable, IStiJsonReportObject {
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(jObject: StiJson): void;
        loadFromXml(xmlNode: XmlNode): void;
        add2(name: string, expression: string): void;
        addRange2(props: StiAppExpressionCollection): void;
        contains2(name: string): boolean;
        remove2(name: string): void;
        getByName(name: string): StiAppExpression;
        setByName(name: string, value: StiAppExpression): void;
        clone(): StiAppExpressionCollection;
    }
}
declare namespace Stimulsoft.Base {
    class StiAppExpressionHelper {
        static isExpressionSpecified(component: any, propName: string): boolean;
        static getExpression(component: any, propName: string): StiAppExpression;
        static getExpressionValue(component: any, propName: string): string;
        static setExpression(component: any, propName: string, expression: string): void;
        static removeExpression(component: any, propName: string): void;
    }
}
declare namespace Stimulsoft.Base {
    import Hashtable = Stimulsoft.System.Collections.Hashtable;
    class StiAppFunctions {
        static functionsToCompile: Hashtable<any, any>;
        static functionsToCompileLower: Hashtable<any, any>;
        static functions: Hashtable<any, any>;
        static functionsLower: Hashtable<any, any>;
        static getFunctions(isCompile: boolean, isCaseSensitive: boolean): IStiAppFunction[];
        static getFunctions2(functionName: string, isCompile: boolean, isCaseSensitive: boolean): IStiAppFunction[];
    }
}
declare namespace Stimulsoft.Base {
    class StiAppKey {
        static getOrGeneratedKey(component: IStiReportComponent): string;
        static getOrGeneratedKey2(app: IStiApp): string;
        static getOrGeneratedKey3(dictionary: IStiAppDictionary): string;
        static getOrGeneratedKey4(dataSource: IStiAppDataSource): string;
    }
}
declare namespace Stimulsoft.Base {
    class StiEncryption {
        private static randomSeed;
        private static rand_m;
        private static rand_a;
        private static rand_c;
        static encrypt(src: number[], key: number[]): number[];
        static encrypt2(src: number[], password: string): number[];
        static encryptS(src: string, password: string): string;
        static decrypt(src: number[], key: number[]): number[];
        static decrypt2(src: number[], password: string): number[];
        static decryptS(src: string, password: string): string;
        static generateRandomKey(): number[];
        private static encryptAdv;
        private static decryptAdv;
        private static cryptXor;
        private static cryptShift;
        private static shiftLeft;
        private static shiftRight;
        private static cryptRandom;
        private static getMixArray;
        private static setRandomSeed;
        private static getRandom;
        private static getKeyFromPassword;
    }
}
declare namespace Stimulsoft.Base {
    import Point = Stimulsoft.System.Drawing.Point;
    import List = Stimulsoft.System.Collections.List;
    import DateTime = Stimulsoft.System.DateTime;
    class StiJson {
        static prettyPrint: boolean;
        static dateToJsonDate(date: DateTime): string;
        static jsonDateFormatToDate(jsonDate: string): DateTime;
        name: string;
        value: any;
        private isProperty;
        private isArray;
        properties(): List<StiJson>;
        removeProperty(propertyName: string): void;
        addPropertyNumber(propertyName: string, value: number, defaultValue?: number): void;
        addPropertyNumberNoDefaultValue(propertyName: string, value: number): void;
        addPropertyJObject(propertyName: string, value: StiJson): void;
        addPropertyJObjectArray(propertyName: string, values: StiJson[]): void;
        addPropertyPoint(propertyName: string, point: Point): void;
        addPropertyIdent(propertyName: string, value: string): void;
        addPropertyBool(propertyName: string, value: boolean, defaultValue?: boolean, ignoreDefaultValues?: boolean): void;
        addPropertyDateTime(propertyName: string, value: DateTime): void;
        addPropertyEnum(propertyName: string, enumType: any, value: any, defaultValue?: any): void;
        addPropertyString(propertyName: string, value: string, defaultValue?: string): void;
        addPropertyStringNullOrEmpty(propertyName: string, value: string): void;
        get count(): number;
        serialize(indent?: number): string;
        deserialize(text: any): void;
        private deserializeFromObject;
        toString(): string;
        constructor(name?: string, value?: any, isProperty?: boolean);
    }
}
declare namespace Stimulsoft.Base {
    import StiJson = Stimulsoft.Base.StiJson;
    class StiCID {
        private static key;
        private static undefined;
        private static prefix;
        private machineAddress;
        private machineName;
        private machineUserName;
        private machineGuid;
        saveToString(): string;
        saveToJsonObject(): StiJson;
        loadFromString(value: string): void;
        loadFromJsonObject(jObject: StiJson): void;
        static getDefault(): string;
        private static getDeveloperCID;
        private static getCurrentMachineName;
        private static getCurrentMachineGuid;
        private static getCurrentMachineAddress;
        private static getCurrentMachineUserName;
        static isCID(cid: string): boolean;
        constructor(machineName: string, machineAddress?: string, machineUserName?: string, machineGuid?: string);
    }
}
declare namespace Stimulsoft.Base.Localization {
    class StiLocalization {
        static languages: any;
        static setLocalization(localizationXml: string, onlyThis?: boolean): void;
        private static _cultureName;
        static get cultureName(): string;
        static set cultureName(value: string);
        static addLocalizationFile(filePath: string, load?: boolean, language?: string): string;
        static setLocalizationFile(filePath: string, onlyThis?: boolean): void;
        static getJsonStringLocalization(): string;
        static loadLocalization(localizationXml: any, extension?: boolean): string;
        static loadLocalizationFile(filePath: string): string;
        private static loadLocalizationXmlInternal;
        static get(category: string, key: string): string;
    }
}
declare namespace Stimulsoft.Base {
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    class StiChartNotSupportedException {
        static message: string;
        static getTextJson(chartJson: StiJson): StiJson;
        static getTextXml(chartXml: XmlNode, message?: string): XmlNode;
        get message(): string;
    }
}
declare namespace Stimulsoft.Base.Drawing {
    import Color = Stimulsoft.System.Drawing.Color;
    class StiColor {
        static get(color: string): Color;
        static get2(...colors: string[]): Color[];
    }
}
declare namespace Stimulsoft.Base {
    class StiConvert {
        static changeType(value: any, conversionType: Stimulsoft.System.Type, convertNulls?: boolean): any;
    }
}
declare namespace Stimulsoft.Base {
    class StiDashboardNotSupportedException {
        get message(): string;
    }
}
declare namespace Stimulsoft.Base {
    class StiExportNotSupportedException {
        get message(): string;
    }
}
declare namespace Stimulsoft.Base {
    import FontFamily = Stimulsoft.System.Drawing.FontFamily;
    import FontStyle = Stimulsoft.System.Drawing.FontStyle;
    class StiFontCollection {
        static addOpentypeFont(font: any, fontName?: string, binFont?: any, filePath?: string, fontStyle?: FontStyle, store?: boolean): void;
        static addFont(font: any, fontName?: string, binFont?: any, filePath?: string, fontStyle?: FontStyle, store?: boolean): void;
        static addOpentypeFontFile(filePath: string, fontName?: string, fontStyle?: FontStyle, store?: boolean): void;
        static addFontFile(filePath: string, fontName?: string, fontStyle?: FontStyle, store?: boolean): void;
        static addOpentypeFontFileAsync(callback: () => {}, filePath: string, fontName?: string, fontStyle?: FontStyle, store?: boolean): void;
        static addFontFileAsync(callback: () => {}, filePath: string, fontName?: string, fontStyle?: FontStyle, store?: boolean): void;
        static addFontBytes(data: any, fontName?: string, fontStyle?: FontStyle, store?: boolean): void;
        static setOpentypeFontsFolder(folderPatch: string): void;
        static setFontsFolder(folderPatch: string): void;
        static getFontFamilies(): FontFamily[];
        static getBinFont(fontName: string, fontStyle?: FontStyle): any;
        static getBinFonts(): string[];
    }
}
declare namespace Stimulsoft.Base {
    class StiGisToDataSetConverter {
    }
}
declare namespace Stimulsoft.Base {
    class StiGuidUtils {
        static newGuid(): string;
    }
}
declare namespace Stimulsoft.Base {
    class StiJsonChecker {
        static isValidJson(strInput: string): boolean;
    }
}
declare namespace Stimulsoft.Base {
    class StiKeyHelper {
        static generateKey(): string;
        static isKey(key: string): boolean;
        static isCorrectKey(key: string): boolean;
        static isEmptyKey(key: string): boolean;
        static isEmptyKey2(key1: string, key2: string): boolean;
        static selectKey(key1: string, key2: string): string;
        static isEqualKeys(key1: string, key2: string): boolean;
        static getOrGeneratedKey(key: string): string;
        static getOrGeneratedKey2(key1: string, key2: string): string;
    }
}
declare namespace Stimulsoft.Base {
    class StiKeyObject {
        key: string;
        isStored: string;
        constructor();
    }
}
declare namespace Stimulsoft.Base {
    import List = Stimulsoft.System.Collections.List;
    class StiLexer {
        private _text;
        get text(): string;
        set text(value: string);
        baseText: string;
        private positions;
        positionInText: number;
        savePosToken(): void;
        getPosition(positionInText: number): StiPosition;
        skip(): void;
        waitLparen2(): boolean;
        waitComma2(): boolean;
        waitAssign2(): boolean;
        waitRparen2(): boolean;
        waitLbrace2(): boolean;
        waitSemicolon2(): boolean;
        waitRbrace2(): boolean;
        scanNumber(): StiToken;
        scanIdent(): StiToken;
        scanString(): StiToken;
        scanChar(): StiToken;
        ungetToken(): void;
        getToken(): StiToken;
        reset(): void;
        static replaceWithPrefix(textValue: string, prefix: string, oldValue: string, newValue: string): string;
        replaceWithPrefix(prefix: string, oldValue: string, newValue: string): void;
        replaceWithNotEqualPrefix(prefix: StiTokenType, oldValue: string, newValue: string): void;
        static identExists(str: string, name: string, caseSensitive: boolean): boolean;
        static getAllTokens(str: string): List<StiToken>;
        constructor(textValue: string);
    }
}
declare namespace Stimulsoft.Base {
    class StiMD5Helper {
        static MD5(string: any, convertToUtf8?: boolean): any[];
    }
}
declare namespace Stimulsoft.Base {
    class StiObjectConverter {
        static convertToNumber(value: any): number;
    }
}
declare namespace Stimulsoft.Base {
    class StiPosition {
        line: number;
        column: number;
        constructor(line: number, column: number);
    }
}
declare namespace Stimulsoft.Base {
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiRepositoryItems implements ICloneable {
        implements(): any[];
        clone(): any;
        private items;
        private valueBoolFalse;
        private valueBoolTrue;
        setNumber(key: string, value: number, defaultValue: number): void;
        getNumber(key: string, defaultValue: number): number;
        setBool(key: string, value: boolean, defaultValue: boolean): void;
        getBool(key: string, defaultValue: boolean): boolean;
        set(key: string, value: any, defaultValue: any): void;
        get(key: string, defaultValue: any): any;
        isPresent(key: string): boolean;
    }
}
declare namespace Stimulsoft.Base {
    class StiScale {
        static xx(value: number): number;
        static yy(value: number): number;
        static factor: number;
    }
}
declare namespace Stimulsoft.Base {
    class StiSettings {
        static get(name: string, def: string): string;
        static set(name: string, value: string): void;
    }
}
declare namespace Stimulsoft.Base {
    class StiToken {
        index: number;
        length: number;
        type: StiTokenType;
        data: string;
        toString(): string;
        constructor(type: StiTokenType, index?: number, length?: number, obj?: any);
    }
}
declare namespace Stimulsoft.Base {
    class StiTypeFinder {
        private static findTypes;
        private static getCorrectTypeName;
        static getStiType(typeName: string): Stimulsoft.System.Type;
        private static addTypeFF;
        private static getTypeFF;
        static findType(exType: Stimulsoft.System.Type, typeForFinding: Stimulsoft.System.Type): boolean;
    }
}
declare namespace Stimulsoft.Base {
    class StiTypeWrapper {
        private _type;
        get type(): Stimulsoft.System.Type;
        toString(): string;
        static toString(type: Stimulsoft.System.Type): string;
        private static _simpleTypes;
        static get simpleTypes(): Stimulsoft.System.Type[];
        private static _simpleBaseTypes;
        static get simpleBaseTypes(): Stimulsoft.System.Type[];
        static getTypeWrappers(): StiTypeWrapper[];
        constructor(type: Stimulsoft.System.Type);
    }
}
declare namespace Stimulsoft.Base {
    class StiUrl {
        static combine(uriParts: string[]): string;
    }
}
declare namespace Stimulsoft {
    class StiVersion {
        static version: string;
        static creationDate: string;
        static created: System.DateTime;
        static versionInfo: string;
        static platform(): string;
    }
}
declare namespace Stimulsoft.Base {
    import DateTime = Stimulsoft.System.DateTime;
    class StringExt {
        static tryParseDateTime(value: string, refDateTime: {
            ref: DateTime;
        }): boolean;
        private static tryParseUsingDate;
        private static tryParseJsonDateTime;
        private static tryParseJsonDateTimeInNewDate;
    }
}
declare namespace Stimulsoft.Base.Blocks {
    interface IStiBlocklyValueEventArgs {
    }
}
declare namespace Stimulsoft.Base.Blocks {
    import EventArgs = Stimulsoft.System.EventArgs;
    import IStiReport = Stimulsoft.Base.IStiReport;
    interface IStiBlocksParser {
        evaluate(report: IStiReport, sender: any, xml: string, args: EventArgs): any;
        evaluateAsync(report: IStiReport, sender: any, xml: string, args: EventArgs): any;
    }
}
declare namespace Stimulsoft.Base.Blocks {
    class StiBlocksConst {
        static identXml: string;
    }
}
declare namespace Stimulsoft.Base.Blocks {
    import IStiBlocksParser = Stimulsoft.Base.Blocks.IStiBlocksParser;
    class StiBlocksCreator {
        static getBlockParse(): IStiBlocksParser;
    }
}
declare namespace Stimulsoft.Base.Dashboard {
    import Color = Stimulsoft.System.Drawing.Color;
    class Font {
        Name: string;
        Size: number;
        Color: Color;
        SelectedColor: Color;
        IsBold: boolean;
        getGdiFont(zoom?: number, fontSize?: number, baseFont?: Stimulsoft.System.Drawing.Font): Stimulsoft.System.Drawing.Font;
        getCachedGdiFont(): Stimulsoft.System.Drawing.Font;
        private cachedFont;
        constructor(name: string, size: number, color: Color, isBold?: boolean);
    }
    export class StiElementConsts {
        static TitleFont: Font;
        static ForegroundColor: Color;
        static BackgroundColor: Color;
        static TreeView: {
            ItemHeight: number;
        };
        static ComboBox: {
            ItemHeight: number;
        };
        static ListBox: {
            ItemHeight: number;
            CheckBoxWidth: number;
        };
        static Table: {
            Font: Font;
            BorderColor: Color;
            Height: number;
            getHeight: (font: Stimulsoft.System.Drawing.Font, scale?: number) => number;
            Header: {
                BackgroundColor: Color;
            };
        };
    }
    export {};
}
declare namespace Stimulsoft.Base {
    enum StiDataFormatType {
        Xml = 0,
        Json = 1
    }
    enum StiRetrieveColumnsMode {
        KeyInfo = 0,
        SchemaOnly = 1,
        FillSchema = 2
    }
    enum StiConnectionIdent {
        Db2DataSource = 1,
        InformixDataSource = 2,
        MsAccessDataSource = 3,
        MsSqlDataSource = 4,
        MySqlDataSource = 5,
        OdbcDataSource = 6,
        OleDbDataSource = 7,
        FirebirdDataSource = 8,
        PostgreSqlDataSource = 9,
        OracleDataSource = 10,
        SqlCeDataSource = 11,
        SqLiteDataSource = 12,
        SybaseDataSource = 13,
        TeradataDataSource = 14,
        VistaDbDataSource = 15,
        UniversalDevartDataSource = 16,
        ODataDataSource = 17,
        CsvDataSource = 18,
        DBaseDataSource = 19,
        DynamicsNavDataSource = 20,
        ExcelDataSource = 21,
        JsonDataSource = 22,
        GisDataSource = 23,
        XmlDataSource = 24,
        DropboxCloudStorage = 25,
        GoogleDriveCloudStorage = 26,
        OneDriveCloudStorage = 27,
        SharePointCloudStorage = 28,
        DataWorldDataSource = 29,
        QuickBooksDataSource = 30,
        Unspecified = 31
    }
    enum StiConnectionOrder {
        MsSqlDataSource = 10,
        MySqlDataSource = 20,
        OdbcDataSource = 30,
        OleDbDataSource = 40,
        OracleDataSource = 50,
        GisDataSource = 55,
        MsAccessDataSource = 60,
        PostgreSqlDataSource = 70,
        FirebirdDataSource = 80,
        SqlCeDataSource = 90,
        SqLiteDataSource = 100,
        Db2DataSource = 110,
        InformixDataSource = 120,
        SybaseDataSource = 130,
        TeradataDataSource = 140,
        VistaDbDataSource = 150,
        UniversalDevartDataSource = 160,
        ODataDataSource = 170,
        ExcelDataSource = 180,
        JsonDataSource = 190,
        XmlDataSource = 200,
        CsvDataSource = 210,
        DBaseDataSource = 220,
        DynamicsNavDataSource = 230,
        DropboxCloudStorage = 240,
        GoogleDriveCloudStorage = 250,
        OneDriveCloudStorage = 260,
        SharePointCloudStorage = 270,
        DataWorldDataSource = 330,
        QuickBooksDataSource = 340,
        Unspecified = 0
    }
    enum StiFileType {
        Unknown = 1,
        ReportSnapshot = 2,
        Pdf = 3,
        Xps = 4,
        PowerPoint = 5,
        Html = 6,
        Text = 7,
        RichText = 8,
        Word = 9,
        OpenDocumentWriter = 10,
        Excel = 11,
        OpenDocumentCalc = 12,
        Data = 13,
        Image = 14,
        Xml = 15,
        Xsd = 16,
        Csv = 17,
        Dbf = 18,
        Sylk = 19,
        Dif = 20,
        Json = 21,
        Gis = 22
    }
}
declare namespace Stimulsoft.Base {
    import IStiAppDataSource = Stimulsoft.Base.IStiAppDataSource;
    import DataTable = Stimulsoft.System.Data.DataTable;
    let IStiBIDataCache: System.Interface<IStiBIDataCache>;
    interface IStiBIDataCache {
        exists(dataSource: IStiAppDataSource): boolean;
        exists2(tableKey: string): boolean;
        remove(tableKey: string): void;
        clean(appKey: string): void;
        cleanAll(): void;
        getTableCount(): number;
        getRowCount(tableKey: string): number;
        getSchema(tableKey: string): DataTable;
        getData(tableKey: string): DataTable;
        runQuery(query: string): DataTable;
        add(appKey: string, tableKey: string, dataTable: DataTable): void;
        getTableName(appKey: string, tableKey: string): string;
    }
}
declare namespace Stimulsoft.Base {
    import DataTable = Stimulsoft.System.Data.DataTable;
    class StiBIDataCacheHelper {
        private static checkInitialization;
        static exists(tableKey: string): boolean;
        static remove(tableKey: string): void;
        static clean(appKey: string): void;
        static cleanAll(): void;
        static getTableCount(): number;
        static getRowCount(tableKey: string): number;
        static runQuery(query: string): DataTable;
        static get(tableKey: string, loadData?: boolean): DataTable;
        static add(app: IStiApp, tableKey: string, dataTable: DataTable): void;
        static add2(appKey: string, tableKey: string, dataTable: DataTable): void;
        static getTableName(appKey: string, tableKey: string): string;
    }
}
declare namespace Stimulsoft.Base {
    class StiBIDataCacheOptions {
        static enabled: boolean;
        static cache: IStiBIDataCache;
    }
}
declare namespace Stimulsoft.Base.Data.StiDbType {
    enum Db2 {
        Invalid = 0,
        SmallInt = 1,
        Integer = 2,
        BigInt = 3,
        Real = 4,
        Double = 5,
        Float = 6,
        Decimal = 7,
        Numeric = 8,
        Date = 9,
        Time = 10,
        Timestamp = 11,
        Char = 12,
        VarChar = 13,
        LongVarChar = 14,
        Binary = 15,
        VarBinary = 16,
        LongVarBinary = 17,
        Graphic = 18,
        VarGraphic = 19,
        LongVarGraphic = 20,
        Clob = 21,
        Blob = 22,
        DbClob = 23,
        Datalink = 24,
        RowId = 25,
        Xml = 26,
        Real370 = 27,
        DecimalFloat = 28,
        DynArray = 29,
        BigSerial = 30,
        BinaryXml = 31,
        TimeStampWithTimeZone = 32,
        Cursor = 33,
        Serial = 34,
        Int8 = 35,
        Serial8 = 36,
        Money = 37,
        DateTime = 38,
        Text = 39,
        Byte = 40,
        SmallFloat = 1002,
        Null = 1003,
        NChar = 1006,
        NVarChar = 1007,
        Boolean = 1015,
        Other = 1016
    }
    enum DevartMySql {
        BigInt = 1,
        Binary = 2,
        Bit = 3,
        Blob = 4,
        Char = 5,
        Date = 6,
        DateTime = 7,
        Decimal = 8,
        Double = 9,
        Float = 10,
        Int = 11,
        SmallInt = 12,
        Text = 13,
        Time = 14,
        TimeStamp = 15,
        TinyInt = 16,
        VarBinary = 17,
        VarChar = 18,
        Year = 19,
        Guid = 20,
        Geometry = 21
    }
    enum DevartOracle {
        Array = 1,
        BFile = 2,
        Blob = 3,
        Boolean = 4,
        Char = 5,
        Clob = 6,
        Cursor = 7,
        Date = 8,
        Double = 9,
        Float = 10,
        Integer = 11,
        IntervalDS = 12,
        IntervalYM = 13,
        Long = 14,
        LongRaw = 15,
        NChar = 16,
        NClob = 17,
        NVarChar = 18,
        Number = 19,
        Object = 20,
        Ref = 21,
        Raw = 22,
        RowId = 23,
        Table = 24,
        TimeStamp = 25,
        TimeStampLTZ = 26,
        TimeStampTZ = 27,
        VarChar = 28,
        Xml = 29,
        AnyData = 30,
        Byte = 31,
        Int16 = 32,
        Int64 = 33
    }
    enum DevartPostgreSql {
        Row = 1,
        Array = 2,
        LargeObject = 3,
        Boolean = 16,
        ByteA = 17,
        BigInt = 20,
        SmallInt = 21,
        Int = 23,
        Text = 25,
        Json = 114,
        Xml = 142,
        Point = 600,
        LSeg = 601,
        Path = 602,
        Box = 603,
        Polygon = 604,
        Line = 628,
        CIdr = 650,
        Real = 700,
        Double = 701,
        Circle = 718,
        Money = 790,
        MacAddr = 829,
        Inet = 869,
        Char = 1042,
        VarChar = 1043,
        Date = 1082,
        Time = 1083,
        TimeStamp = 1114,
        TimeStampTZ = 1184,
        Interval = 1186,
        TimeTZ = 1266,
        Bit = 1560,
        VarBit = 1562,
        Numeric = 1700,
        Uuid = 2950,
        IntRange = 3904,
        NumericRange = 3906,
        TimeStampRange = 3908,
        TimeStampTZRange = 3910,
        DateRange = 3912,
        BigIntRange = 3926
    }
    enum Firebird {
        Array = 0,
        BigInt = 1,
        Binary = 2,
        Boolean = 3,
        Char = 4,
        Date = 5,
        Decimal = 6,
        Double = 7,
        Float = 8,
        Guid = 9,
        Integer = 10,
        Numeric = 11,
        SmallInt = 12,
        Text = 13,
        Time = 14,
        TimeStamp = 15,
        VarChar = 16
    }
    enum Informix {
        Char = 0,
        SmallInt = 1,
        Integer = 2,
        Float = 3,
        SmallFloat = 4,
        Real = 4,
        Decimal = 5,
        Serial = 6,
        Date = 7,
        Money = 8,
        Null = 9,
        DateTime = 10,
        Byte = 11,
        Text = 12,
        VarChar = 13,
        NChar = 15,
        NVarChar = 16,
        Int8 = 17,
        Serial8 = 18,
        Other = 99,
        LVarChar = 101,
        LongVarChar = 101,
        Blob = 110,
        Clob = 111,
        Boolean = 126,
        Invalid = 200,
        BigInt = 203,
        Double = 205,
        Numeric = 208,
        Time = 210,
        Timestamp = 211,
        Binary = 215,
        VarBinary = 216,
        LongVarBinary = 217,
        BigSerial = 230
    }
    enum MySql {
        Decimal = 0,
        Byte = 1,
        Int16 = 2,
        Int32 = 3,
        Float = 4,
        Double = 5,
        Timestamp = 7,
        Int64 = 8,
        Int24 = 9,
        Date = 10,
        Time = 11,
        DateTime = 12,
        Year = 13,
        Newdate = 14,
        VarString = 15,
        Bit = 16,
        NewDecimal = 246,
        Enum = 247,
        Set = 248,
        TinyBlob = 249,
        MediumBlob = 250,
        LongBlob = 251,
        Blob = 252,
        VarChar = 253,
        String = 254,
        Geometry = 255,
        UByte = 501,
        UInt16 = 502,
        UInt32 = 503,
        UInt64 = 508,
        UInt24 = 509,
        Binary = 600,
        VarBinary = 601,
        TinyText = 749,
        MediumText = 750,
        LongText = 751,
        Text = 752,
        Guid = 800
    }
    enum MsSql {
        BigInt = 0,
        Binary = 1,
        Bit = 2,
        Char = 3,
        DateTime = 4,
        Decimal = 5,
        Float = 6,
        Image = 7,
        Int = 8,
        Money = 9,
        NChar = 10,
        NText = 11,
        NVarChar = 12,
        Real = 13,
        UniqueIdentifier = 14,
        SmallDateTime = 15,
        SmallInt = 16,
        SmallMoney = 17,
        Text = 18,
        Timestamp = 19,
        TinyInt = 20,
        VarBinary = 21,
        VarChar = 22,
        Variant = 23,
        Xml = 25,
        Udt = 29,
        Structured = 30,
        Date = 31,
        Time = 32,
        DateTime2 = 33,
        DateTimeOffset = 34
    }
    enum Odbc {
        BigInt = 1,
        Binary = 2,
        Bit = 3,
        Char = 4,
        DateTime = 5,
        Decimal = 6,
        Numeric = 7,
        Double = 8,
        Image = 9,
        Int = 10,
        NChar = 11,
        NText = 12,
        NVarChar = 13,
        Real = 14,
        UniqueIdentifier = 15,
        SmallDateTime = 16,
        SmallInt = 17,
        Text = 18,
        Timestamp = 19,
        TinyInt = 20,
        VarBinary = 21,
        VarChar = 22,
        Date = 23,
        Time = 24
    }
    enum OleDb {
        Empty = 0,
        SmallInt = 2,
        Integer = 3,
        Single = 4,
        Double = 5,
        Currency = 6,
        Date = 7,
        BSTR = 8,
        IDispatch = 9,
        Error = 10,
        Boolean = 11,
        Variant = 12,
        IUnknown = 13,
        Decimal = 14,
        TinyInt = 16,
        UnsignedTinyInt = 17,
        UnsignedSmallInt = 18,
        UnsignedInt = 19,
        BigInt = 20,
        UnsignedBigInt = 21,
        Filetime = 64,
        Guid = 72,
        Binary = 128,
        Char = 129,
        WChar = 130,
        Numeric = 131,
        DBDate = 133,
        DBTime = 134,
        DBTimeStamp = 135,
        PropVariant = 138,
        VarNumeric = 139,
        VarChar = 200,
        LongVarChar = 201,
        VarWChar = 202,
        LongVarWChar = 203,
        VarBinary = 204,
        LongVarBinary = 205
    }
    enum Oracle {
        BFile = 101,
        Blob = 102,
        Byte = 103,
        Char = 104,
        Clob = 105,
        Date = 106,
        Decimal = 107,
        Double = 108,
        Long = 109,
        LongRaw = 110,
        Int16 = 111,
        Int32 = 112,
        Int64 = 113,
        IntervalDS = 114,
        IntervalYM = 115,
        NClob = 116,
        NChar = 117,
        NVarchar2 = 119,
        Raw = 120,
        RefCursor = 121,
        Single = 122,
        TimeStamp = 123,
        TimeStampLTZ = 124,
        TimeStampTZ = 125,
        Varchar2 = 126,
        XmlType = 127,
        BinaryDouble = 132,
        BinaryFloat = 133
    }
    enum OracleClient {
        BFile = 1,
        Blob = 2,
        Char = 3,
        Clob = 4,
        Cursor = 5,
        DateTime = 6,
        IntervalDayToSecond = 7,
        IntervalYearToMonth = 8,
        LongRaw = 9,
        LongVarChar = 10,
        NChar = 11,
        NClob = 12,
        Number = 13,
        NVarChar = 14,
        Raw = 15,
        RowId = 16,
        Timestamp = 18,
        TimestampLocal = 19,
        TimestampWithTZ = 20,
        VarChar = 22,
        Byte = 23,
        UInt16 = 24,
        UInt32 = 25,
        SByte = 26,
        Int16 = 27,
        Int32 = 28,
        Float = 29,
        Double = 30
    }
    enum PostgreSql {
        Array = -2147483648,
        Bigint = 1,
        Boolean = 2,
        Box = 3,
        Bytea = 4,
        Circle = 5,
        Char = 6,
        Date = 7,
        Double = 8,
        Integer = 9,
        Line = 10,
        LSeg = 11,
        Money = 12,
        Numeric = 13,
        Path = 14,
        Point = 15,
        Polygon = 16,
        Real = 17,
        Smallint = 18,
        Text = 19,
        Time = 20,
        Timestamp = 21,
        Varchar = 22,
        Refcursor = 23,
        Inet = 24,
        Bit = 25,
        TimestampTZ = 26,
        Uuid = 27,
        Xml = 28,
        Oidvector = 29,
        Interval = 30,
        TimeTZ = 31,
        Name = 32,
        Abstime = 33,
        MacAddr = 34,
        Json = 35,
        Jsonb = 36,
        Hstore = 37
    }
    enum SqlCe {
        BigInt = 0,
        Binary = 1,
        Bit = 2,
        Char = 3,
        DateTime = 4,
        Decimal = 5,
        Float = 6,
        Image = 7,
        Int = 8,
        Money = 9,
        NChar = 10,
        NText = 11,
        NVarChar = 12,
        Real = 13,
        UniqueIdentifier = 14,
        SmallDateTime = 15,
        SmallInt = 16,
        SmallMoney = 17,
        Text = 18,
        Timestamp = 19,
        TinyInt = 20,
        VarBinary = 21,
        VarChar = 22,
        Variant = 23,
        Xml = 25,
        Udt = 29,
        Structured = 30,
        Date = 31,
        Time = 32,
        DateTime2 = 33,
        DateTimeOffset = 34
    }
    enum SqLite {
        Uninitialized = 0,
        Int64 = 1,
        Double = 2,
        Text = 3,
        Blob = 4,
        Null = 5,
        DateTime = 10,
        None = 11
    }
    enum Sybase {
        UnsignedBigInt = -208,
        UnsignedInt = -207,
        UnsignedSmallInt = -206,
        NVarChar = -205,
        NChar = -204,
        TimeStamp = -203,
        SmallDateTime = -202,
        SmallMoney = -201,
        Money = -200,
        Unitext = -10,
        UniVarChar = -9,
        UniChar = -8,
        Bit = -7,
        TinyInt = -6,
        BigInt = -5,
        Image = -4,
        VarBinary = -3,
        Binary = -2,
        Text = -1,
        Unsupported = 0,
        Char = 1,
        Numeric = 2,
        Decimal = 3,
        Integer = 4,
        SmallInt = 5,
        Real = 7,
        Double = 8,
        VarChar = 12,
        Date = 91,
        Time = 92,
        BigDateTime = 93,
        DateTime = 93
    }
    enum Teradata {
        BigInt = 90,
        Blob = 100,
        Byte = 110,
        ByteInt = 120,
        Char = 130,
        Clob = 140,
        Date = 150,
        Decimal = 160,
        Double = 170,
        Graphic = 180,
        Integer = 190,
        IntervalDay = 200,
        IntervalDayToHour = 210,
        IntervalDayToMinute = 220,
        IntervalDayToSecond = 230,
        IntervalHour = 240,
        IntervalHourToMinute = 250,
        IntervalHourToSecond = 260,
        IntervalMinute = 270,
        IntervalMinuteToSecond = 280,
        IntervalSecond = 290,
        IntervalYear = 300,
        IntervalYearToMonth = 310,
        IntervalMonth = 320,
        SmallInt = 330,
        Time = 340,
        TimeWithZone = 350,
        Timestamp = 360,
        TimestampWithZone = 370,
        VarByte = 380,
        VarChar = 390,
        VarGraphic = 400,
        PeriodDate = 410,
        PeriodTime = 420,
        PeriodTimeWithTimeZone = 430,
        PeriodTimestamp = 440,
        PeriodTimestampWithTimeZone = 450,
        Number = 460,
        Xml = 480,
        Json = 500,
        AnyType = 65535
    }
    enum Universal {
        Array = 0,
        BigInt = 1,
        Binary = 2,
        Bit = 3,
        Blob = 4,
        Boolean = 5,
        Byte = 6,
        Char = 7,
        Clob = 8,
        Currency = 9,
        Cursor = 10,
        Date = 11,
        DateTime = 12,
        Decimal = 13,
        Double = 14,
        Guid = 15,
        Int = 16,
        IntervalDS = 17,
        IntervalYM = 18,
        NChar = 19,
        NClob = 20,
        NVarChar = 21,
        Object = 22,
        Single = 23,
        SmallInt = 24,
        TinyInt = 25,
        Time = 26,
        TimeStamp = 27,
        VarChar = 28,
        Xml = 29,
        TimeStampTZ = 30,
        DateTime2 = 31
    }
    enum VistaDb {
        Uninitialized = -1,
        Char = 1,
        NChar = 2,
        VarChar = 3,
        NVarChar = 4,
        Text = 5,
        NText = 6,
        TinyInt = 8,
        SmallInt = 9,
        Int = 10,
        BigInt = 11,
        Real = 12,
        Float = 13,
        Decimal = 14,
        Money = 15,
        SmallMoney = 16,
        Bit = 17,
        DateTime = 19,
        Image = 20,
        UniqueIdentifier = 22,
        SmallDateTime = 23,
        Timestamp = 24,
        Binary = 25,
        VarBinary = 26,
        Time = 27,
        Date = 28,
        DateTime2 = 29,
        DateTimeOffset = 30,
        Unknown = 31
    }
    enum Pdo {
        String = 0
    }
}
declare namespace Stimulsoft.ExternalLibrary.XLSX {
    interface IProperties {
        LastAuthor?: string;
        Author?: string;
        CreatedDate?: Date;
        ModifiedDate?: Date;
        Application?: string;
        AppVersion?: string;
        Company?: string;
        DocSecurity?: string;
        Manager?: string;
        HyperlinksChanged?: boolean;
        SharedDoc?: boolean;
        LinksUpToDate?: boolean;
        ScaleCrop?: boolean;
        Worksheets?: number;
        SheetNames?: string[];
    }
    interface IParsingOptions {
        cellFormula?: boolean;
        cellHTML?: boolean;
        cellNF?: boolean;
        cellStyles?: boolean;
        cellDates?: boolean;
        sheetStubs?: boolean;
        sheetRows?: number;
        bookDeps?: boolean;
        bookFiles?: boolean;
        bookProps?: boolean;
        bookSheets?: boolean;
        bookVBA?: boolean;
        password?: string;
        type?: string;
    }
    interface IWorkBook {
        Sheets: {
            [sheet: string]: IWorkSheet;
        };
        SheetNames: string[];
        Props: IProperties;
    }
    interface IWorkSheet {
        [cell: string]: IWorkSheetCell;
    }
    interface IWorkSheetCell {
        t: string;
        v: string;
        r?: string;
        h?: string;
        w?: string;
        f?: string;
        c?: string;
        z?: string;
        l?: string;
        s?: string;
    }
    interface IUtils {
        sheet_to_json<T>(worksheet: IWorkSheet): T[];
        sheet_to_csv(worksheet: IWorkSheet): any;
        sheet_to_formulae(worksheet: IWorkSheet): any;
    }
    let utils: IUtils;
    function readFile(filename: string, opts?: IParsingOptions): IWorkBook;
    function read(data: any, opts?: IParsingOptions): IWorkBook;
}
declare namespace Stimulsoft.Base.Data.Connectors {
    class StiConnectionStringHelper {
        static getConnectionStringKey(connectionString: string, key: string, separators?: string[]): string;
        static getConnectionStringKey2(connectionString: string): string;
        static setConnectionStringKey(connectionString: string, key: string, value: string): string;
        static removeConnectionStringKey(connectionString: string, key: string): string;
    }
}
declare namespace Stimulsoft.Base {
    class StiDataConnector {
        static advancedRetrievalModeOfDatabaseSchema: boolean;
        static getSchemaColumnsMode: boolean;
        connectionIdent: StiConnectionIdent;
        connectionOrder: StiConnectionOrder;
        name: string;
        nuGetPackages: string[];
        nuGetVersion: string;
        isAvailable: boolean;
        resetSettings(): void;
        getFamilyConnectors(): StiDataConnector[];
    }
}
declare namespace Stimulsoft.Base {
    class StiDataLoaderHelperData {
        name: string;
        array: any;
        toList(): StiDataLoaderHelperData[];
        constructor(name: string, array: any);
    }
    class StiDataLoaderHelper {
        static loadMultiple(path: string, fileExt: string, binary: boolean, headers: {
            key: string;
            value: string;
        }[], withCredentials?: boolean): StiDataLoaderHelperData[];
        static loadSingle(path: string, binary: boolean, headers: {
            key: string;
            value: string;
        }[], withCredentials?: boolean): StiDataLoaderHelperData;
    }
}
declare namespace Stimulsoft.Base {
    class StiDataOptions {
        static readonly SampleConnectionString: {
            dataWorld: string;
            oData: string;
            quickBooks: string;
            graphQL: string;
            azureSql: string;
            cosmosSql: string;
            bigQuery: string;
            firebase: string;
            mongoDb: string;
            db2: string;
            firebird: string;
            informix: string;
            msAccess: string;
            mySql: string;
            odbc: string;
            oleDb: string;
            oracle: string;
            postgreSql: string;
            sqlCe: string;
            msSql: string;
            sqLite: string;
            sybase: string;
            sybaseAds: string;
            teradata: string;
            vistaDb: string;
            googleAnalytics: string;
            azureTableStorage: string;
            azureBlobStorage: string;
        };
    }
}
declare namespace Stimulsoft.Base {
    class StiFileUrlHelper {
        static get(path: string): number[];
    }
}
declare namespace Stimulsoft.Base {
    import DataSet = Stimulsoft.System.Data.DataSet;
    class StiFileDataConnector extends StiDataConnector {
        fileType: StiFileType;
        retrieveSchema(options: StiFileDataOptions): StiDataSchema;
        testConnection(options: StiFileDataOptions): StiTestConnectionResult;
        getDataSet(options: StiFileDataOptions): DataSet;
        static get(ident: StiConnectionIdent): StiFileDataConnector;
    }
}
declare namespace Stimulsoft.Base {
    import DataSet = Stimulsoft.System.Data.DataSet;
    class StiCsvConnector extends StiFileDataConnector {
        connectionIdent: StiConnectionIdent;
        connectionOrder: StiConnectionOrder;
        name: string;
        isAvailable: boolean;
        fileType: StiFileType;
        getDataSet(options: StiFileDataOptions): DataSet;
        private getDataSetFromPath;
        static get(): StiCsvConnector;
    }
}
declare namespace Stimulsoft.Base {
    import DataSet = Stimulsoft.System.Data.DataSet;
    class StiDBaseConnector extends StiFileDataConnector {
        connectionIdent: StiConnectionIdent;
        connectionOrder: StiConnectionOrder;
        name: string;
        isAvailable: boolean;
        fileType: StiFileType;
        getDataSet(options: StiFileDataOptions): DataSet;
        static get(): StiDBaseConnector;
    }
}
declare namespace Stimulsoft.Base {
    import DataSet = Stimulsoft.System.Data.DataSet;
    class StiExcelConnector extends StiFileDataConnector {
        connectionIdent: StiConnectionIdent;
        connectionOrder: StiConnectionOrder;
        name: string;
        isAvailable: boolean;
        fileType: StiFileType;
        getDataSet(options: StiFileDataOptions): DataSet;
        static get(): StiExcelConnector;
    }
}
declare namespace Stimulsoft.Base {
    import DataSet = Stimulsoft.System.Data.DataSet;
    class StiGisConnector extends StiFileDataConnector {
        connectionIdent: StiConnectionIdent;
        connectionOrder: StiConnectionOrder;
        name: string;
        isAvailable: boolean;
        fileType: StiFileType;
        getDataSet(options: StiFileDataOptions): DataSet;
        static get(): StiGisConnector;
    }
}
declare namespace Stimulsoft.Base {
    import DataSet = Stimulsoft.System.Data.DataSet;
    class StiJsonConnector extends StiFileDataConnector {
        connectionIdent: StiConnectionIdent;
        connectionOrder: StiConnectionOrder;
        name: string;
        isAvailable: boolean;
        fileType: StiFileType;
        getDataSet(options: StiFileDataOptions): DataSet;
        static get(): StiJsonConnector;
    }
}
declare namespace Stimulsoft.Base {
    import DataSet = Stimulsoft.System.Data.DataSet;
    class StiXmlConnector extends StiFileDataConnector {
        connectionIdent: StiConnectionIdent;
        connectionOrder: StiConnectionOrder;
        name: string;
        isAvailable: boolean;
        fileType: StiFileType;
        getDataSet(options: StiFileDataOptions): DataSet;
        static get(): StiXmlConnector;
    }
}
declare namespace Stimulsoft.Base {
    import DataTable = Stimulsoft.System.Data.DataTable;
    import DataSet = Stimulsoft.System.Data.DataSet;
    class StiCsvHelper {
        static codePageCodes: number[];
        static codePageNames: string[];
        static getTable(path: string, codePage?: number, separator?: string, maxDataRows?: number): DataTable;
        static getDataSet(data: number[], tableName: string, codePage: number, separator: string, maxDataRows?: number): DataSet;
        static getTable2(data: number[], codePage?: number, separator?: string, loadData?: boolean, maxDataRows?: number): DataTable;
        private static splitToColumns;
    }
}
declare namespace Stimulsoft.Base {
    class StiDataNameValidator {
        static correct(str: string): string;
    }
}
declare namespace Stimulsoft.Base {
    import DataSet = Stimulsoft.System.Data.DataSet;
    class StiExcelHelper {
        static getDataSetFromExcelDocument(content: number[], firstRowIsHeader: boolean): DataSet;
        private static getNumber;
        private static getType;
        private static getTicksForTimeSpan;
        private static isTimeSpan;
        private static getTimeSpan;
    }
}
declare namespace Stimulsoft.Base {
    class StiFileItemTable {
        static defaultCsvTableName: string;
        static defaultDBaseTableName: string;
    }
}
declare namespace Stimulsoft.Base {
    import DataSet = Stimulsoft.System.Data.DataSet;
    class StiFileDataOptions {
        content: number[] | Uint8Array | string;
        dataSet: DataSet;
        constructor(content: number[] | string | Uint8Array);
    }
}
declare namespace Stimulsoft.Base {
    class StiCsvOptions extends StiFileDataOptions {
        maxDataRows: number;
        path: string;
        tableName: string;
        codePage: number;
        separator: string;
        constructor(content: number[], tableName: string, codePage?: number, separator?: string);
    }
}
declare namespace Stimulsoft.Base {
    class StiDBaseOptions extends StiFileDataOptions {
        maxDataRows: number;
        path: string;
        tableName: string;
        codePage: number;
        constructor(content: number[], tableName: string, codePage?: number);
    }
}
declare namespace Stimulsoft.Base {
    class StiExcelOptions extends StiFileDataOptions {
        firstRowIsHeader: boolean;
        constructor(content: number[], firstRowIsHeader?: boolean);
    }
}
declare namespace Stimulsoft.Base {
    class StiGisOptions extends StiFileDataOptions {
        separator: string;
        constructor(content: number[] | string, separator: string);
    }
}
declare namespace Stimulsoft.Base {
    class StiJsonOptions extends StiFileDataOptions {
        relationDirection: StiRelationDirection;
        constructor(content: number[] | string, relationDirection?: StiRelationDirection);
    }
}
declare namespace Stimulsoft.Base {
    class StiXmlOptions extends StiFileDataOptions {
        schema: number[] | string;
        isAdoNet: boolean;
        relationDirection: StiRelationDirection;
        constructor(schema: number[] | string, content: number[] | string, isAdoNet: boolean, relationDirection: StiRelationDirection);
    }
}
declare namespace Stimulsoft.Base {
    import DataTable = Stimulsoft.System.Data.DataTable;
    class StiDataWorldConnector extends StiDataConnector {
        getColumns(collectionName: string): StiDataColumnSchema[];
        getDataTable(collectionName: string, query: string): DataTable;
        getSampleConnectionString(): string;
        retrieveSchema(allowException?: boolean): StiDataSchema;
        testConnection(): StiTestConnectionResult;
        static get(connectionString: string): StiDataWorldConnector;
        connectionString: string;
        constructor(connectionString: string);
    }
}
declare namespace Stimulsoft.Base {
    import List = Stimulsoft.System.Collections.List;
    import DataTable = Stimulsoft.System.Data.DataTable;
    class StiDataWorldHelper {
        urlBase: string;
        private getDefaultWebClient;
        getTableNames(): List<string>;
        getColumns(collectionName: string): List<StiDataColumnSchema>;
        getDataTable(collectionName: string, query: string): DataTable;
        testConnection(): StiTestConnectionResult;
        retrieveSchema(): StiDataSchema;
        private getConnectionStringKey;
        private getConnectionStringKey1;
        connectionString: string;
        get owner(): string;
        get token(): string;
        get database(): string;
        constructor(connectionString: string);
    }
}
declare namespace Stimulsoft.Base {
    class StiObjectSchema {
        name: string;
    }
}
declare namespace Stimulsoft.Base {
    import List = Stimulsoft.System.Collections.List;
    import DataSet = Stimulsoft.System.Data.DataSet;
    class StiDataSchema extends StiObjectSchema {
        tables: List<StiDataTableSchema>;
        views: List<StiDataTableSchema>;
        storedProcedures: List<StiDataTableSchema>;
        queries: List<StiDataTableSchema>;
        relations: List<StiDataRelationSchema>;
        connectionIdent: StiConnectionIdent;
        isEmpty(): boolean;
        getDataSet(): DataSet;
        sort(): StiDataSchema;
        constructor(ident?: StiConnectionIdent);
    }
}
declare namespace Stimulsoft.Base {
    import List = Stimulsoft.System.Collections.List;
    import DataTable = Stimulsoft.System.Data.DataTable;
    import Type = Stimulsoft.System.Type;
    class StiODataHelper {
        connectionString: string;
        get address(): string;
        get userName(): string;
        get password(): string;
        get addressBearer(): string;
        get clientId(): string;
        headers: {
            key: string;
            value: string;
        }[];
        private getConnectionStringKey;
        private getConnectionStringKey1;
        private bearerAccessToken;
        retrieveSchema(): StiDataSchema;
        fillDataTable(table: DataTable, query: string): void;
        testConnection(): StiTestConnectionResult;
        getColumns(collectionName: string): List<StiDataColumnSchema>;
        static getNetType(dbType: string): Type;
        static getBearerAccessToken(url: string, userName: string, password: string, clientId: string): string;
        private getDefaultWebClient;
        constructor(connectionString: string);
    }
}
declare namespace Stimulsoft.Base {
    import DataTable = Stimulsoft.System.Data.DataTable;
    class StiQuickBooksConnector {
        private stimulsoftClientId;
        private stimulsoftClientSecret;
        private oauth2Url;
        private bearerUrl;
        private baseUrl;
        private stimulsoftRedirectUrl;
        private responseType;
        private scope;
        private state;
        connectionString: string;
        connectionIdent: StiConnectionIdent;
        connectionOrder: StiConnectionOrder;
        name: string;
        isAvailable: boolean;
        get useApp(): boolean;
        set useApp(value: boolean);
        get clientId(): string;
        set clientId(value: string);
        get clientIdPrivate(): string;
        set clientIdPrivate(value: string);
        get clientSecret(): string;
        set clientSecret(value: string);
        get clientSecretPrivate(): string;
        set clientSecretPrivate(value: string);
        get redirectURL(): string;
        set redirectURL(value: string);
        get redirectURLPrivate(): string;
        set redirectURLPrivate(value: string);
        get authorizationCode(): string;
        set authorizationCode(value: string);
        get realmId(): string;
        set realmId(value: string);
        get accessToken(): string;
        set accessToken(value: string);
        get refreshToken(): string;
        set refreshToken(value: string);
        connectionTimeout: number;
        fillAuthorizationCode(): void;
        private getDefaultWebClient;
        private getAuthorizationUrl;
        fillTokens(): void;
        refreshAccessToken(): void;
        private getTableNames;
        private getColumns;
        retrieveSchema(allowException?: boolean): StiDataSchema;
        getDataTable(collectionName: string, query: string): DataTable;
        fillDataTable(table: DataTable, query: string): void;
        private executeQuery;
        private removeUnsupportedColumns;
        private correctRefColumns;
        getSampleConnectionString(): string;
        static Get(connectionString?: string): StiQuickBooksConnector;
        constructor(connectionString?: string);
    }
}
declare namespace Stimulsoft.Base {
    class StiTestConnectionResult {
        success: boolean;
        notice: string;
        static makeWrong(notice: string): StiTestConnectionResult;
        static makeWrong2(exception: string): StiTestConnectionResult;
        static makeWrong3(): StiTestConnectionResult;
        static makeFine(): StiTestConnectionResult;
    }
}
declare namespace Stimulsoft.Base {
    class StiDataColumnSchema extends StiObjectSchema {
        type: Stimulsoft.System.Type;
        constructor(name: string, type: Stimulsoft.System.Type);
    }
}
declare namespace Stimulsoft.Base {
    class StiDataParameterSchema extends StiObjectSchema {
        type: Stimulsoft.System.Type;
        value: any;
        constructor(name?: string, type?: Stimulsoft.System.Type);
    }
}
declare namespace Stimulsoft.Base {
    class StiDataRelationSchema {
        name: string;
        parentSourceName: string;
        childSourceName: string;
        parentColumns: string[];
        childColumns: string[];
        constructor(name: string, parentSourceName: string, childSourceName: string, parentColumns: string[], childColumns: string[]);
    }
}
declare namespace Stimulsoft.Base {
    class StiDataTableSchema extends StiObjectSchema {
        columns: StiDataColumnSchema[];
        parameters: StiDataParameterSchema[];
        query: string;
        static newTableOrView(name: string): StiDataTableSchema;
        static newTable(name: string): StiDataTableSchema;
        static newView(name: string): StiDataTableSchema;
        static newProcedure(name: string): StiDataTableSchema;
        constructor(name?: string, query?: string);
    }
}
declare namespace Stimulsoft.Base.Design {
    let IStiDefault: System.Interface<IStiDefault>;
    interface IStiDefault {
        isDefault(): boolean;
    }
}
declare namespace Stimulsoft.Base.Drawing {
    enum StiCheckState {
        Unchecked = 1,
        Checked = 2,
        Indeterminate = 3
    }
    enum StiAction {
        None = 0,
        Move = 1,
        Select = 2,
        SizeLeft = 3,
        SizeRight = 4,
        SizeTop = 5,
        SizeBottom = 6,
        SizeLeftTop = 7,
        SizeLeftBottom = 8,
        SizeRightTop = 9,
        SizeRightBottom = 10,
        ResizeColumns = 11,
        ResizeRows = 12,
        SelectColumn = 13,
        SelectRow = 14
    }
    enum StiBorderSides {
        None = 0,
        All = 15,
        Top = 1,
        Left = 2,
        Right = 4,
        Bottom = 8
    }
    enum StiPenStyle {
        Solid = 0,
        Dash = 1,
        DashDot = 2,
        DashDotDot = 3,
        Dot = 4,
        Double = 5,
        None = 6
    }
    enum StiRotationMode {
        LeftTop = 0,
        LeftCenter = 1,
        LeftBottom = 2,
        CenterTop = 3,
        CenterCenter = 4,
        CenterBottom = 5,
        RightTop = 6,
        RightCenter = 7,
        RightBottom = 8
    }
    enum StiShadowSides {
        Top = 1,
        Right = 2,
        Edge = 4,
        Bottom = 8,
        Left = 16,
        All = 31
    }
    enum StiVertAlignment {
        Top = 0,
        Center = 1,
        Bottom = 2
    }
    enum StiTextHorAlignment {
        Left = 0,
        Center = 1,
        Right = 2,
        Width = 3
    }
    enum StiHorAlignment {
        Left = 1,
        Center = 2,
        Right = 3
    }
    enum StiTextDockMode {
        Top = 0,
        Bottom = 1,
        Left = 2,
        Right = 3
    }
    enum StiBrushIdent {
        Empty = 1,
        Solid = 2,
        Gradient = 3,
        Glare = 4,
        Glass = 5,
        Hatch = 6,
        Default = 7,
        Style = 8
    }
    enum StiBorderIdent {
        Border = 1,
        AdvancedBorder = 2
    }
    enum StiCapStyle {
        None = 0,
        Arrow = 1,
        Open = 2,
        Stealth = 3,
        Diamond = 4,
        Square = 5,
        Oval = 6
    }
    enum StiTableColumnVisibility {
        True = 0,
        False = 1,
        Expression = 2
    }
    enum StiCardsColumnVisibility {
        True = 0,
        False = 1,
        Expression = 2
    }
    enum StiButtonShapeType {
        Rectangle = 0,
        Circle = 1
    }
}
declare namespace Stimulsoft.Base.Drawing {
    let PointD: typeof System.Drawing.Point;
}
declare namespace Stimulsoft.Base.Drawing {
    let RectangleD: typeof System.Drawing.Rectangle;
}
declare namespace Stimulsoft.Base.Drawing {
    let SizeD: typeof System.Drawing.Size;
}
declare namespace Stimulsoft.Base.Drawing {
    import Point = Stimulsoft.System.Drawing.Point;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    class StiActionUtils {
        static pointInEdge(x: number, y: number, point: Point, size: number): boolean;
        static pointInRect(x: number, y: number, rect: Rectangle): boolean;
    }
}
declare namespace Stimulsoft.Base.Drawing {
    import IAsIs = Stimulsoft.System.IAsIs;
    import ICloneable = Stimulsoft.System.ICloneable;
    import Color = Stimulsoft.System.Drawing.Color;
    import Brush = Stimulsoft.System.Drawing.Brush;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    class StiBrush implements ICloneable, IAsIs {
        implements(): any[];
        is<T>(type: (new (...args: any[]) => T) | Stimulsoft.System.Interface<T>): this is T;
        is2<T>(type: (new (...args: any[]) => T) | Stimulsoft.System.Interface<T>): boolean;
        as<T>(type: (new (...args: any[]) => T) | Stimulsoft.System.Interface<T>): T;
        clone(): StiBrush;
        memberwiseClone(): StiBrush;
        equals(obj: any): boolean;
        static convertToBrush(text: string): StiBrush;
        static loadFromXml(text: string): StiBrush;
        static light(baseBrush: StiBrush, value: number): StiBrush;
        static dark(baseBrush: StiBrush, value: number): StiBrush;
        static getBrush(brush: StiBrush, rect: Rectangle): Brush;
        static toColor(brush: StiBrush): Color;
        static isEmpty(brush: StiBrush): boolean;
        static isTransparent(brush: StiBrush): boolean;
    }
}
declare namespace Stimulsoft.Base.Drawing {
    import Color = Stimulsoft.System.Drawing.Color;
    class StiSolidBrush extends StiBrush {
        memberwiseClone(): StiBrush;
        color: Color;
        constructor(color?: Color);
    }
}
declare namespace Stimulsoft.Base.Drawing {
    import IAsIs = Stimulsoft.System.IAsIs;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Color = Stimulsoft.System.Drawing.Color;
    import ICloneable = Stimulsoft.System.ICloneable;
    import Graphics = Stimulsoft.System.Drawing.Graphics;
    class StiBorder implements ICloneable, IAsIs {
        implements(): any[];
        is<T>(type: (new (...args: any[]) => T) | Stimulsoft.System.Interface<T>): this is T;
        is2<T>(type: (new (...args: any[]) => T) | Stimulsoft.System.Interface<T>): boolean;
        as<T>(type: (new (...args: any[]) => T) | Stimulsoft.System.Interface<T>): T;
        private bits;
        clone(): StiBorder;
        equals(obj: StiBorder | any): boolean;
        getSizeOffset(): number;
        getHashCode(): number;
        private defaultHashCode;
        getSizeIncludingSide(): number;
        draw(g: Graphics, rect: Rectangle, zoom: number, emptyColor?: Color, drawBorderFormatting?: boolean, drawBorderSides?: boolean): void;
        drawBorderShadow(g: Graphics, rect: Rectangle, zoom: number): void;
        get isTopBorderSidePresent(): boolean;
        get isBottomBorderSidePresent(): boolean;
        get isLeftBorderSidePresent(): boolean;
        get isRightBorderSidePresent(): boolean;
        get isAllBorderSidesPresent(): boolean;
        private get isDefaultShadowBrush();
        get side(): StiBorderSides;
        set side(value: StiBorderSides);
        get color(): Color;
        set color(value: Color);
        get size(): number;
        set size(value: number);
        get style(): StiPenStyle;
        set style(value: StiPenStyle);
        private _shadowBrush;
        get shadowBrush(): StiBrush;
        set shadowBrush(value: StiBrush);
        get shadowSize(): StiPenStyle;
        set shadowSize(value: StiPenStyle);
        get dropShadow(): boolean;
        set dropShadow(value: boolean);
        get topmost(): boolean;
        set topmost(value: boolean);
        isDefault(): boolean;
        static loadFromXml(text: string): StiBorder;
        constructor(side?: StiBorderSides, color?: Color, size?: number, style?: StiPenStyle, dropShadow?: boolean, shadowSize?: number, shadowBrush?: StiBrush, topmost?: boolean);
    }
}
declare namespace Stimulsoft.Base.Drawing {
    import Color = Stimulsoft.System.Drawing.Color;
    class StiAdvancedBorder extends StiBorder {
        clone(): StiAdvancedBorder;
        equals(obj: StiAdvancedBorder | any): boolean;
        getHashCode(): number;
        private _leftSide;
        get leftSide(): StiBorderSide;
        private _rightSide;
        get rightSide(): StiBorderSide;
        private _topSide;
        get topSide(): StiBorderSide;
        private _bottomSide;
        get bottomSide(): StiBorderSide;
        get isTopBorderSidePresent(): boolean;
        get isBottomBorderSidePresent(): boolean;
        get isLeftBorderSidePresent(): boolean;
        get isRightBorderSidePresent(): boolean;
        get isAllBorderSidesPresent(): boolean;
        get side(): StiBorderSides;
        set side(value: StiBorderSides);
        get color(): Color;
        set color(value: Color);
        get size(): number;
        set size(value: number);
        get style(): StiPenStyle;
        set style(value: StiPenStyle);
        isDefault(): boolean;
        constructor(topSide?: StiBorderSide, bottomSide?: StiBorderSide, leftSide?: StiBorderSide, rightSide?: StiBorderSide, dropShadow?: boolean, shadowSize?: number, shadowBrush?: StiBrush, topmost?: boolean);
    }
}
declare namespace Stimulsoft.Report.Helpers {
    enum StiFontIconSet {
        Rating = 0,
        Quarter = 1,
        Square = 2,
        Star = 3,
        Latin = 4
    }
    enum StiFontIconGroup {
        WebApplicationIcons = 0,
        AccessibilityIcons = 1,
        HandIcons = 2,
        TransportationIcons = 3,
        GenderIcons = 4,
        FileTypeIcons = 5,
        SpinnerIcons = 6,
        FormControlIcons = 7,
        PaymentIcons = 8,
        ChartIcons = 9,
        CurrencyIcons = 10,
        TextEditorIcons = 11,
        DirectionalIcons = 12,
        VideoPlayerIcons = 13,
        BrandIcons = 14,
        MedicalIcons = 15,
        OtherIcons = 16
    }
    enum StiFontIcons {
        Latin5 = 0,
        Latin4 = 1,
        Latin3 = 2,
        Latin2 = 3,
        Latin1 = 4,
        QuarterFull = 5,
        QuarterThreeFourth = 6,
        QuarterHalf = 7,
        QuarterQuarter = 8,
        QuarterNone = 9,
        Rating4 = 10,
        Rating3 = 11,
        Rating2 = 12,
        Rating1 = 13,
        Rating0 = 14,
        Square0 = 15,
        Square1 = 16,
        Square2 = 17,
        Square3 = 18,
        Square4 = 19,
        StarFull = 20,
        StarThreeFourth = 21,
        StarHalf = 22,
        StarQuarter = 23,
        StarNone = 24,
        ArrowDown = 25,
        ArrowRight = 26,
        ArrowRightDown = 27,
        ArrowRightUp = 28,
        ArrowUp = 29,
        Check = 30,
        Circle = 31,
        CircleCheck = 32,
        CircleCross = 33,
        CircleExclamation = 34,
        Cross = 35,
        Rhomb = 36,
        Exclamation = 37,
        Flag = 38,
        Minus = 39,
        Triangle = 40,
        TriangleDown = 41,
        TriangleUp = 42,
        Home = 43,
        Cart = 44,
        Phone = 45,
        Mobile = 46,
        Mug = 47,
        Airplane = 48,
        Man = 49,
        Woman = 50,
        UserTie = 51,
        Truck = 52,
        Earth = 53,
        ManWoman = 54,
        Appleinc = 55,
        Windows8 = 56,
        Glass = 57,
        Music = 58,
        Search = 59,
        EnvelopeO = 60,
        Heart = 61,
        Star = 62,
        StarO = 63,
        User = 64,
        Film = 65,
        ThLarge = 66,
        Th = 67,
        ThList = 68,
        Times = 69,
        SearchPlus = 70,
        SearchMinus = 71,
        PowerOff = 72,
        Signal = 73,
        Cog = 74,
        TrashO = 75,
        FileO = 76,
        ClockO = 77,
        Road = 78,
        Download = 79,
        ArrowCircleODown = 80,
        ArrowCircleOUp = 81,
        Inbox = 82,
        PlayCircleO = 83,
        Repeat = 84,
        Refresh = 85,
        ListAlt = 86,
        Lock = 87,
        FAFlag = 88,
        Headphones = 89,
        VolumeOff = 90,
        VolumeDown = 91,
        VolumeUp = 92,
        Qrcode = 93,
        Barcode = 94,
        Tag = 95,
        Tags = 96,
        Book = 97,
        Bookmark = 98,
        Print = 99,
        Camera = 100,
        Font = 101,
        Bold = 102,
        Italic = 103,
        TextHeight = 104,
        TextWidth = 105,
        AlignLeft = 106,
        AlignCenter = 107,
        AlignRight = 108,
        AlignJustify = 109,
        List = 110,
        Outdent = 111,
        Indent = 112,
        VideoCamera = 113,
        PictureO = 114,
        Pencil = 115,
        MapMarker = 116,
        Adjust = 117,
        Tint = 118,
        PencilSquareO = 119,
        ShareSquareO = 120,
        CheckSquareO = 121,
        Arrows = 122,
        StepBackward = 123,
        FastBackward = 124,
        Backward = 125,
        Play = 126,
        Pause = 127,
        Stop = 128,
        Forward = 129,
        FastForward = 130,
        StepForward = 131,
        Eject = 132,
        ChevronLeft = 133,
        ChevronRight = 134,
        PlusCircle = 135,
        MinusCircle = 136,
        TimesCircle = 137,
        CheckCircle = 138,
        QuestionCircle = 139,
        InfoCircle = 140,
        Crosshairs = 141,
        TimesCircleO = 142,
        CheckCircleO = 143,
        Ban = 144,
        FAArrowLeft = 145,
        FAArrowRight = 146,
        FAArrowUp = 147,
        FAArrowDown = 148,
        Share = 149,
        Expand = 150,
        Compress = 151,
        FAPlus = 152,
        FAMinus = 153,
        Asterisk = 154,
        ExclamationCircle = 155,
        Gift = 156,
        Leaf = 157,
        Fire = 158,
        Eye = 159,
        EyeSlash = 160,
        ExclamationTriangle = 161,
        Plane = 162,
        Calendar = 163,
        Random = 164,
        Comment = 165,
        Magnet = 166,
        ChevronUp = 167,
        ChevronDown = 168,
        Retweet = 169,
        ShoppingCart = 170,
        Folder = 171,
        FolderOpen = 172,
        ArrowsV = 173,
        ArrowsH = 174,
        BarChart = 175,
        TwitterSquare = 176,
        FacebookSquare = 177,
        CameraRetro = 178,
        Key = 179,
        Cogs = 180,
        Comments = 181,
        ThumbsOUp = 182,
        ThumbsODown = 183,
        HeartO = 184,
        SignOut = 185,
        LinkedinSquare = 186,
        ThumbTack = 187,
        ExternalLink = 188,
        SignIn = 189,
        Trophy = 190,
        GithubSquare = 191,
        Upload = 192,
        LemonO = 193,
        SquareO = 194,
        BookmarkO = 195,
        PhoneSquare = 196,
        Twitter = 197,
        Facebook = 198,
        Github = 199,
        Unlock = 200,
        CreditCard = 201,
        Rss = 202,
        HddO = 203,
        Bullhorn = 204,
        Bell = 205,
        Certificate = 206,
        HandORight = 207,
        HandOLeft = 208,
        HandOUp = 209,
        HandODown = 210,
        ArrowCircleLeft = 211,
        ArrowCircleRight = 212,
        ArrowCircleUp = 213,
        ArrowCircleDown = 214,
        Globe = 215,
        Wrench = 216,
        Tasks = 217,
        Filter = 218,
        Briefcase = 219,
        ArrowsAlt = 220,
        Users = 221,
        Link = 222,
        Cloud = 223,
        Flask = 224,
        Scissors = 225,
        FilesO = 226,
        Paperclip = 227,
        FloppyO = 228,
        Square = 229,
        Bars = 230,
        ListUl = 231,
        ListOl = 232,
        Strikethrough = 233,
        Underline = 234,
        Table = 235,
        Magic = 236,
        Pinterest = 237,
        PinterestSquare = 238,
        GooglePlusSquare = 239,
        GooglePlus = 240,
        Money = 241,
        CaretDown = 242,
        CaretUp = 243,
        CaretLeft = 244,
        CaretRight = 245,
        Columns = 246,
        Sort = 247,
        SortDesc = 248,
        SortAsc = 249,
        Envelope = 250,
        Linkedin = 251,
        Undo = 252,
        Gavel = 253,
        Tachometer = 254,
        CommentO = 255,
        CommentsO = 256,
        Bolt = 257,
        Sitemap = 258,
        Umbrella = 259,
        Clipboard = 260,
        LightbulbO = 261,
        Exchange = 262,
        CloudDownload = 263,
        CloudUpload = 264,
        UserMd = 265,
        Stethoscope = 266,
        Suitcase = 267,
        BellO = 268,
        Coffee = 269,
        Cutlery = 270,
        FileTextO = 271,
        BuildingO = 272,
        HospitalO = 273,
        Ambulance = 274,
        Medkit = 275,
        FighterJet = 276,
        Beer = 277,
        HSquare = 278,
        PlusSquare = 279,
        AngleDoubleLeft = 280,
        AngleDoubleRight = 281,
        AngleDoubleUp = 282,
        AngleDoubleDown = 283,
        AngleLeft = 284,
        AngleRight = 285,
        AngleUp = 286,
        AngleDown = 287,
        Desktop = 288,
        Laptop = 289,
        Tablet = 290,
        CircleO = 291,
        QuoteLeft = 292,
        QuoteRight = 293,
        Spinner = 294,
        Reply = 295,
        GithubAlt = 296,
        FolderO = 297,
        FolderOpenO = 298,
        SmileO = 299,
        FrownO = 300,
        MehO = 301,
        Gamepad = 302,
        KeyboardO = 303,
        FlagO = 304,
        FlagCheckered = 305,
        Terminal = 306,
        Code = 307,
        ReplyAll = 308,
        StarHalfO = 309,
        LocationArrow = 310,
        Crop = 311,
        CodeFork = 312,
        ChainBroken = 313,
        Question = 314,
        Info = 315,
        Superscript = 316,
        Subscript = 317,
        Eraser = 318,
        PuzzlePiece = 319,
        Microphone = 320,
        MicrophoneSlash = 321,
        Shield = 322,
        CalendarO = 323,
        FireExtinguisher = 324,
        Rocket = 325,
        Maxcdn = 326,
        ChevronCircleLeft = 327,
        ChevronCircleRight = 328,
        ChevronCircleUp = 329,
        ChevronCircleDown = 330,
        Html5 = 331,
        Css3 = 332,
        Anchor = 333,
        UnlockAlt = 334,
        Bullseye = 335,
        EllipsisH = 336,
        EllipsisV = 337,
        RssSquare = 338,
        PlayCircle = 339,
        Ticket = 340,
        MinusSquare = 341,
        InusSquareO = 342,
        LevelUp = 343,
        LevelDown = 344,
        CheckSquare = 345,
        PencilSquare = 346,
        ExternalLinkSquare = 347,
        ShareSquare = 348,
        Compass = 349,
        CaretSquareODown = 350,
        CaretSquareOUp = 351,
        CaretSquareORight = 352,
        Eur = 353,
        Gbp = 354,
        Usd = 355,
        Inr = 356,
        Jpy = 357,
        Rub = 358,
        Krw = 359,
        Btc = 360,
        File = 361,
        FileText = 362,
        SortAlphaAsc = 363,
        SortAlphaDesc = 364,
        SortAmountAsc = 365,
        SortAmountDesc = 366,
        SortNumericAsc = 367,
        SortNumericDesc = 368,
        ThumbsUp = 369,
        ThumbsDown = 370,
        YoutubeSquare = 371,
        Youtube = 372,
        Xing = 373,
        XingSquare = 374,
        YoutubePlay = 375,
        Dropbox = 376,
        StackOverflow = 377,
        Instagram = 378,
        Flickr = 379,
        Adn = 380,
        Bitbucket = 381,
        BitbucketSquare = 382,
        Tumblr = 383,
        TumblrSquare = 384,
        LongArrowDown = 385,
        LongArrowUp = 386,
        LongArrowLeft = 387,
        LongArrowRight = 388,
        Apple = 389,
        Windows = 390,
        Android = 391,
        Linux = 392,
        Dribbble = 393,
        Skype = 394,
        Foursquare = 395,
        Trello = 396,
        Female = 397,
        Male = 398,
        Gratipay = 399,
        SunO = 400,
        MoonO = 401,
        Archive = 402,
        Bug = 403,
        Vk = 404,
        Weibo = 405,
        Renren = 406,
        Pagelines = 407,
        StackExchange = 408,
        ArrowCircleORight = 409,
        ArrowCircleOLeft = 410,
        CaretSquareOLeft = 411,
        DotCircleO = 412,
        Wheelchair = 413,
        VimeoSquare = 414,
        Try = 415,
        PlusSquareO = 416,
        SpaceShuttle = 417,
        Slack = 418,
        EnvelopeSquare = 419,
        Wordpress = 420,
        Openid = 421,
        University = 422,
        GraduationCap = 423,
        Yahoo = 424,
        Google = 425,
        Reddit = 426,
        RedditSquare = 427,
        StumbleuponCircle = 428,
        Stumbleupon = 429,
        Delicious = 430,
        Digg = 431,
        PiedPiper = 432,
        PiedPiperAlt = 433,
        Drupal = 434,
        Joomla = 435,
        Language = 436,
        Fax = 437,
        Building = 438,
        Child = 439,
        Paw = 440,
        Spoon = 441,
        Cube = 442,
        Cubes = 443,
        Behance = 444,
        BehanceSquare = 445,
        Steam = 446,
        SteamSquare = 447,
        Recycle = 448,
        Car = 449,
        Taxi = 450,
        Tree = 451,
        Spotify = 452,
        Deviantart = 453,
        Soundcloud = 454,
        Database = 455,
        FilePdfO = 456,
        FileWordO = 457,
        FileExcelO = 458,
        FilePowerpointO = 459,
        FileImageO = 460,
        FileArchiveO = 461,
        FileAudioO = 462,
        FileVideoO = 463,
        FileCodeO = 464,
        Vine = 465,
        Codepen = 466,
        Jsfiddle = 467,
        LifeRing = 468,
        CircleONotch = 469,
        Rebel = 470,
        Empire = 471,
        GitSquare = 472,
        Git = 473,
        HackerNews = 474,
        TencentWeibo = 475,
        Qq = 476,
        Weixin = 477,
        PaperPlane = 478,
        PaperPlaneO = 479,
        History = 480,
        CircleThin = 481,
        Header = 482,
        Paragraph = 483,
        Sliders = 484,
        ShareAlt = 485,
        ShareAltSquare = 486,
        Bomb = 487,
        FutbolO = 488,
        Tty = 489,
        Binoculars = 490,
        Plug = 491,
        Slideshare = 492,
        Twitch = 493,
        Yelp = 494,
        NewspaperO = 495,
        Wifi = 496,
        Calculator = 497,
        Paypal = 498,
        GoogleWallet = 499,
        CcVisa = 500,
        CcMastercard = 501,
        CcDiscover = 502,
        CcAmex = 503,
        CcPaypal = 504,
        CcStripe = 505,
        BellSlash = 506,
        BellSlashO = 507,
        Trash = 508,
        Copyright = 509,
        At = 510,
        Eyedropper = 511,
        PaintBrush = 512,
        BirthdayCake = 513,
        AreaChart = 514,
        PieChart = 515,
        LineChart = 516,
        Lastfm = 517,
        LastfmSquare = 518,
        ToggleOff = 519,
        ToggleOn = 520,
        Bicycle = 521,
        Bus = 522,
        Ioxhost = 523,
        Angellist = 524,
        Cc = 525,
        Ils = 526,
        Meanpath = 527,
        Buysellads = 528,
        Connectdevelop = 529,
        Dashcube = 530,
        Forumbee = 531,
        Leanpub = 532,
        Sellsy = 533,
        Shirtsinbulk = 534,
        Simplybuilt = 535,
        Skyatlas = 536,
        CartPlus = 537,
        CartArrowDown = 538,
        Diamond = 539,
        Ship = 540,
        UserSecret = 541,
        Motorcycle = 542,
        StreetView = 543,
        Heartbeat = 544,
        Venus = 545,
        Mars = 546,
        Mercury = 547,
        Transgender = 548,
        TransgenderAlt = 549,
        VenusDouble = 550,
        MarsDouble = 551,
        VenusMars = 552,
        MarsStroke = 553,
        MarsStrokeV = 554,
        MarsStrokeH = 555,
        Neuter = 556,
        Genderless = 557,
        FacebookOfficial = 558,
        PinterestP = 559,
        Whatsapp = 560,
        Server = 561,
        UserPlus = 562,
        UserTimes = 563,
        Bed = 564,
        Viacoin = 565,
        Train = 566,
        Subway = 567,
        Medium = 568,
        YCombinator = 569,
        OptinMonster = 570,
        Opencart = 571,
        Expeditedssl = 572,
        BatteryFull = 573,
        BatteryThreeQuarters = 574,
        BatteryHalf = 575,
        BatteryQuarter = 576,
        BatteryEmpty = 577,
        MousePointer = 578,
        ICursor = 579,
        ObjectGroup = 580,
        ObjectUngroup = 581,
        StickyNote = 582,
        StickyNoteO = 583,
        CcJcb = 584,
        CcDinersClub = 585,
        Clone = 586,
        BalanceScale = 587,
        HourglassO = 588,
        HourglassStart = 589,
        HourglassHalf = 590,
        HourglassEnd = 591,
        Hourglass = 592,
        HandRockO = 593,
        HandPaperO = 594,
        HandScissorsO = 595,
        HandLizardO = 596,
        HandSpockO = 597,
        HandPointerO = 598,
        HandPeaceO = 599,
        Trademark = 600,
        Registered = 601,
        CreativeCommons = 602,
        Gg = 603,
        GgCircle = 604,
        Tripadvisor = 605,
        Odnoklassniki = 606,
        OdnoklassnikiSquare = 607,
        GetPocket = 608,
        WikipediaW = 609,
        Safari = 610,
        Chrome = 611,
        Firefox = 612,
        Opera = 613,
        InternetExplorer = 614,
        Television = 615,
        Contao = 616,
        Px500 = 617,
        Amazon = 618,
        CalendarPlusO = 619,
        CalendarMinusO = 620,
        CalendarTimesO = 621,
        CalendarCheckO = 622,
        Industry = 623,
        MapPin = 624,
        MapSigns = 625,
        MapO = 626,
        Map = 627,
        Commenting = 628,
        CommentingO = 629,
        Houzz = 630,
        Vimeo = 631,
        BlackTie = 632,
        Fonticons = 633
    }
}
declare namespace Stimulsoft.Base.Drawing {
    import Image = Stimulsoft.System.Drawing.Image;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import Font = Stimulsoft.System.Drawing.Font;
    import ContentAlignment = Stimulsoft.System.Drawing.ContentAlignment;
    import Color = Stimulsoft.System.Drawing.Color;
    import IStiDefault = Stimulsoft.Base.Design.IStiDefault;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import StiFontIcons = Stimulsoft.Report.Helpers.StiFontIcons;
    class StiAdvancedWatermark implements ICloneable, IStiJsonReportObject, IStiDefault {
        private static defaultWeaveMajorColor;
        private static defaultWeaveMinorColor;
        private static defaultTextColor;
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(jObject: StiJson): void;
        loadFromXml(xmlNode: XmlNode): void;
        clone(): any;
        isDefault(): boolean;
        private cachedImage;
        get isVisible(): boolean;
        textEnabled: boolean;
        text: string;
        textFont: Font;
        private shouldSerializeTextFont;
        textColor: Color;
        private shouldSerializeTextColor;
        textAngle: number;
        imageEnabled: boolean;
        get image(): Image;
        set image(value: Image);
        private imageBytes_;
        get imageBytes(): number[];
        set imageBytes(value: number[]);
        imageMultipleFactor: number;
        private _imageTransparency;
        get imageTransparency(): number;
        set imageTransparency(value: number);
        imageAlignment: ContentAlignment;
        imageTiling: boolean;
        imageStretch: boolean;
        imageAspectRatio: boolean;
        weaveEnabled: boolean;
        weaveMajorIcon: StiFontIcons;
        private weaveMajorSize_;
        get weaveMajorSize(): number;
        set weaveMajorSize(value: number);
        weaveMajorColor: Color;
        private shouldSerializeWeaveMajorColor;
        weaveMinorIcon: StiFontIcons;
        private weaveMinorSize_;
        get weaveMinorSize(): number;
        set weaveMinorSize(value: number);
        weaveMinorColor: Color;
        private shouldSerializeWeaveMinorColor;
        private weaveAngle_;
        get weaveAngle(): number;
        set weaveAngle(value: number);
        private weaveDistance_;
        get weaveDistance(): number;
        set weaveDistance(value: number);
        private getCachedImage;
        private putCachedImage;
        private disposeCachedImage;
        private existImage;
        private takeImage;
        private takeGdiImage;
        putImage(image: Image): void;
        putImage2(image: number[]): void;
        resetImage(): void;
        constructor(textEnabled?: boolean, text?: string, textColor?: Color, textAngle?: number, textFont?: Font, imageEnabled?: boolean, imageBytes?: number[], imageMultipleFactor?: number, imageTransparency?: number, imageAlignment?: ContentAlignment, imageTiling?: boolean, imageStretch?: boolean, imageAspectRatio?: boolean, weaveEnabled?: boolean, weaveMajorIcon?: StiFontIcons, weaveMajorSize?: number, weaveMajorColor?: Color, weaveMinorIcon?: StiFontIcons, weaveMinorSize?: number, weaveMinorColor?: Color, weaveAngle?: number, weaveDistance?: number);
    }
}
declare namespace Stimulsoft.Base.Drawing {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    class StiAlignHelper {
        static alignToGrid(value: number, gridSize: number, aligningToGrid: boolean): number;
        static alignToGrid2(rect: Rectangle, gridSize: number, aligningToGrid: boolean): Rectangle;
    }
}
declare namespace Stimulsoft.Base.Drawing {
    import ICloneable = Stimulsoft.System.ICloneable;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiBorderSide implements ICloneable {
        implements(): any[];
        clone(): StiBorderSide;
        equals(obj: StiBorderSide | any): boolean;
        getHashCode(): number;
        getSizeOffset(): number;
        side: StiBorderSides;
        private _color;
        get color(): Color;
        set color(value: Color);
        private _size;
        get size(): number;
        set size(value: number);
        private _style;
        get style(): StiPenStyle;
        set style(value: StiPenStyle);
        isDefault(): boolean;
        constructor(color?: Color, size?: number, style?: StiPenStyle);
    }
}
declare namespace Stimulsoft.Base.Drawing {
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import Color = Stimulsoft.System.Drawing.Color;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiCap implements ICloneable {
        implements(): any[];
        clone(): StiCap;
        width: number;
        style: StiCapStyle;
        height: number;
        fill: boolean;
        color: Color;
        loadFromXml(xmlNode: XmlNode): void;
        constructor(width?: number, style?: StiCapStyle, height?: number, fill?: boolean, color?: Color);
    }
}
declare namespace Stimulsoft.Base.Drawing {
    import Color = Stimulsoft.System.Drawing.Color;
    class StiColorUtils {
        static changeLightness(color: Color, correctionFactor: number): Color;
        static changeDarkness(color: Color, percDarker: number): Color;
        static light(baseColor: Color, value: number): Color;
        static mixingColors(color1: Color, color2: Color, alpha: number): Color;
        static dark(baseColor: Color, value: number): Color;
    }
}
declare namespace Stimulsoft.Base.Drawing {
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import IStiDefault = Stimulsoft.Base.Design.IStiDefault;
    class StiCornerRadius implements ICloneable, IStiDefault, IStiJsonReportObject {
        clone(): any;
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(jObject: StiJson): void;
        loadFromXml(xmlNode: XmlNode): void;
        isDefault(): boolean;
        equals(obj: any): boolean;
        toString(): string;
        static tryParse(str: string): StiCornerRadius;
        getUniqueCode(): number;
        private topLeft_;
        get topLeft(): number;
        set topLeft(value: number);
        private topRight_;
        get topRight(): number;
        set topRight(value: number);
        private bottomRight_;
        get bottomRight(): number;
        set bottomRight(value: number);
        private bottomLeft_;
        get bottomLeft(): number;
        set bottomLeft(value: number);
        get isEmpty(): boolean;
        constructor(topLeft?: number, topRight?: number, bottomRight?: number, bottomLeft?: number);
    }
}
declare namespace Stimulsoft.Base.Drawing {
    class StiCornerRadiusHelper {
        static flipVertical(cornerRadius: StiCornerRadius): StiCornerRadius;
        static flipHorizontal(cornerRadius: StiCornerRadius): StiCornerRadius;
        static rotation90(cornerRadius: StiCornerRadius): StiCornerRadius;
    }
}
declare namespace Stimulsoft.Base.Drawing {
    class StiDefaultBrush extends StiBrush {
        equals(obj: any): boolean;
        getHashCode(): number;
        ident: StiBrushIdent;
    }
}
declare namespace Stimulsoft.Base.Drawing {
    import Graphics = Stimulsoft.System.Drawing.Graphics;
    import Brush = Stimulsoft.System.Drawing.Brush;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    class StiDrawing {
        static fillRectangle(g: Graphics, brush: Brush, arg: number | Rectangle, y?: number, width?: number, height?: number): void;
    }
}
declare namespace Stimulsoft.Base.Drawing {
    class StiEmptyBrush extends StiBrush {
        equals(obj: any): boolean;
        private defaultHashCode;
        getHashCode(): number;
    }
}
declare namespace Stimulsoft.Base.Drawing {
    import Font = Stimulsoft.System.Drawing.Font;
    import FontStyle = Stimulsoft.System.Drawing.FontStyle;
    class StiFontUtils {
        static correctStyle(fontName: string, style: FontStyle): FontStyle;
        static changeFontName(font: Font, newFontName: string): Font;
        static changeFontSize(font: Font, newFontSize: number): Font;
        static changeFontStyle(font: Font, style: FontStyle): Font;
        static changeFontStyle2(fontName: string, fontSize: number, style: FontStyle): Font;
        static changeFontStyleBold(font: Font, bold: boolean): Font;
        static changeFontStyleItalic(font: Font, italic: boolean): Font;
        static changeFontStyleUnderline(font: Font, underline: boolean): Font;
        static changeFontStyleStrikeout(font: Font, strikeout: boolean): Font;
    }
}
declare namespace Stimulsoft.Base.Drawing {
    import Color = Stimulsoft.System.Drawing.Color;
    class StiGlareBrush extends StiBrush {
        memberwiseClone(): StiBrush;
        private _startColor;
        get startColor(): Color;
        set startColor(value: Color);
        private _endColor;
        get endColor(): Color;
        set endColor(value: Color);
        private _angle;
        get angle(): number;
        set angle(value: number);
        private _focus;
        get focus(): number;
        set focus(value: number);
        private _scale;
        get scale(): number;
        set scale(value: number);
        equals(obj: any): boolean;
        private defaultHashCode;
        getHashCode(): number;
        constructor(startColor?: Color, endColor?: Color, angle?: number, focus?: number, scale?: number);
    }
}
declare namespace Stimulsoft.Base.Drawing {
    import Color = Stimulsoft.System.Drawing.Color;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    class StiGlassBrush extends StiBrush {
        memberwiseClone(): StiBrush;
        private _color;
        get color(): Color;
        set color(value: Color);
        private _drawHatch;
        get drawHatch(): boolean;
        set drawHatch(value: boolean);
        private _blend;
        get blend(): number;
        set blend(value: number);
        equals(obj: any): boolean;
        private defaultHashCode;
        getHashCode(): number;
        getTopColor(): Color;
        getTopColorLight(): Color;
        getBottomColor(): Color;
        getBottomColorLight(): Color;
        getTopRectangle(rect: Rectangle): Rectangle;
        getBottomRectangle(rect: Rectangle): Rectangle;
        constructor(color?: Color, drawHatch?: boolean, blend?: number);
    }
}
declare namespace Stimulsoft.Base.Drawing {
    import Color = Stimulsoft.System.Drawing.Color;
    class StiGradientBrush extends StiBrush {
        memberwiseClone(): StiBrush;
        private _startColor;
        get startColor(): Color;
        set startColor(value: Color);
        private _endColor;
        get endColor(): Color;
        set endColor(value: Color);
        private _angle;
        get angle(): number;
        set angle(value: number);
        equals(obj: any): boolean;
        private defaultHashCode;
        getHashCode(): number;
        constructor(startColor?: Color, endColor?: Color, angle?: number);
    }
}
declare namespace Stimulsoft.Base.Drawing {
    import Color = Stimulsoft.System.Drawing.Color;
    import HatchStyle = Stimulsoft.System.Drawing.Drawing2D.HatchStyle;
    class StiHatchBrush extends StiBrush {
        memberwiseClone(): StiBrush;
        private _backColor;
        get backColor(): Color;
        set backColor(value: Color);
        private _foreColor;
        get foreColor(): Color;
        set foreColor(value: Color);
        private _style;
        get style(): HatchStyle;
        set style(value: HatchStyle);
        equals(obj: any): boolean;
        private defaultHashCode;
        getHashCode(): number;
        constructor(style?: HatchStyle, foreColor?: Color, backColor?: Color);
    }
}
declare namespace Stimulsoft.Base.Drawing {
    import ImageCodecInfo = Stimulsoft.System.Drawing.Imaging.ImageCodecInfo;
    class StiImageCodecInfo {
        static getImageCodec(mimeType: string): ImageCodecInfo;
    }
}
declare namespace Stimulsoft.Base.Drawing {
    import Image = Stimulsoft.System.Drawing.Image;
    class StiImageConverter {
        static imageToString(image: Image): string;
        static imageToBytes(image: Image, allowNulls?: boolean): number[];
        static bytesToImage(bytes: number[], width?: number, height?: number, stretch?: boolean, aspectRatio?: boolean): Image;
        static stringToImage(str: string): Image;
        static stringToByteArray(str: string): number[];
    }
}
declare namespace Stimulsoft.Base.Drawing {
    import Image = Stimulsoft.System.Drawing.Image;
    class StiImageFromURL {
        static loadBitmap(url: string): Image;
    }
}
declare namespace Stimulsoft.Base.Drawing {
    import DashStyle = Stimulsoft.System.Drawing.Drawing2D.DashStyle;
    class StiPenUtils {
        static getPenStyle(penStyle: StiPenStyle): DashStyle;
    }
}
declare namespace Stimulsoft.Base.Drawing {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import ICloneable = Stimulsoft.System.ICloneable;
    import Color = Stimulsoft.System.Drawing.Color;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    class StiSimpleBorder implements ICloneable, IStiJsonReportObject {
        clone(): any;
        getBorder(): StiBorder;
        getSizeOffset(): number;
        getSize(): number;
        getSizeIncludingSide(): number;
        get isTopBorderSidePresent(): boolean;
        get isBottomBorderSidePresent(): boolean;
        get isLeftBorderSidePresent(): boolean;
        get isRightBorderSidePresent(): boolean;
        get isAllBorderSidesPresent(): boolean;
        side: StiBorderSides;
        color: Color;
        private shouldSerializeColor;
        size: number;
        style: StiPenStyle;
        isDefault(): boolean;
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(jObject: StiJson): void;
        loadFromXml(xmlNode: XmlNode): void;
        constructor(side?: StiBorderSides, color?: Color, size?: number, style?: StiPenStyle);
    }
}
declare namespace Stimulsoft.Base.Drawing {
    import Point = Stimulsoft.System.Drawing.Point;
    import ICloneable = Stimulsoft.System.ICloneable;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiSimpleShadow implements ICloneable {
        clone(): any;
        static loadFromXml(text: string): StiSimpleShadow;
        color: Color;
        private shouldSerializeColor;
        private location_;
        get location(): Point;
        set location(value: Point);
        private shouldSerializeLocation;
        private size_;
        get size(): number;
        set size(value: number);
        visible: boolean;
        isDefault(): boolean;
        constructor(color?: Color, location?: Point, size?: number, visible?: boolean);
    }
}
declare namespace Stimulsoft.Base.Drawing {
    class StiStyleBrush extends StiBrush {
        equals(obj: any): boolean;
        getHashCode(): number;
        ident: StiBrushIdent;
    }
}
declare namespace Stimulsoft.Base.Drawing {
    import Point = Stimulsoft.System.Drawing.Point;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import StringFormat = Stimulsoft.System.Drawing.StringFormat;
    import List = Stimulsoft.System.Collections.List;
    import Size = Stimulsoft.System.Drawing.Size;
    import Graphics = Stimulsoft.System.Drawing.Graphics;
    import Font = Stimulsoft.System.Drawing.Font;
    import StringAlignment = Stimulsoft.System.Drawing.StringAlignment;
    class Range {
        text: string;
        pos: Point;
        size: Size;
        isStart: boolean;
        isEnd: boolean;
        newLineForm: boolean;
        constructor(text: string, size: Size, newLineForm: boolean);
    }
    class StiTextDrawing {
        static measureString(g: Graphics, text: string, font: Font, width?: number, textOptions?: StiTextOptions, ha?: StiTextHorAlignment, va?: StiVertAlignment, antialiasing?: boolean, allowHtmlTags?: boolean): Size;
        private static correctFontSize;
        static splitTextWordwrap(text: string, g: Graphics, font: Font, rect: Rectangle, textOptions: StiTextOptions, ha: StiTextHorAlignment, typographic: boolean): List<LineInfo>;
        static splitTextWordwrap2(text: string, g: Graphics, font: Font, rect: Rectangle, sf: StringFormat, horAlignWidth?: boolean): List<LineInfo>;
        static splitTextWordwrapWidth(text: string, g: Graphics, font: Font, rect: Rectangle): List<string>;
        private static getAdditionalSpaceSize;
        private static makeLineInfo;
        static splitString(inputString: string, removeControl: boolean): List<string>;
        static getStringFormat(textOptions: StiTextOptions, ha: StiTextHorAlignment, va: StiVertAlignment, zoom: number): StringFormat;
        static getAlignment(alignment: StiTextHorAlignment): StringAlignment;
        static getAlignment2(alignment: StiVertAlignment): StringAlignment;
        static getStringFormat2(textOptions: StiTextOptions, ha: StiTextHorAlignment, va: StiVertAlignment, antialiasing: boolean, zoom: number): StringFormat;
        static measureTrailingSpaces: boolean;
    }
}
declare namespace Stimulsoft.Base.Drawing {
    import StringFormat = Stimulsoft.System.Drawing.StringFormat;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StringTrimming = Stimulsoft.System.Drawing.StringTrimming;
    import HotkeyPrefix = Stimulsoft.System.Drawing.Text.HotkeyPrefix;
    import StiJson = Stimulsoft.Base.StiJson;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiTextOptions implements ICloneable, IStiJsonReportObject {
        implements(): any[];
        saveToJsonObject(): StiJson;
        loadFromJsonObject(jObject: StiJson): void;
        static loadFromXml(str: string): StiTextOptions;
        loadFromXml(xmlNode: XmlNode): void;
        clone(): StiTextOptions;
        private bits;
        getStringFormat(antialiasing?: boolean, zoom?: number): StringFormat;
        get rightToLeft(): boolean;
        set rightToLeft(value: boolean);
        get lineLimit(): boolean;
        set lineLimit(value: boolean);
        wordWrap: boolean;
        get angle(): number;
        set angle(value: number);
        get firstTabOffset(): number;
        set firstTabOffset(value: number);
        get distanceBetweenTabs(): number;
        set distanceBetweenTabs(value: number);
        get hotkeyPrefix(): HotkeyPrefix;
        set hotkeyPrefix(value: HotkeyPrefix);
        get trimming(): StringTrimming;
        set trimming(value: StringTrimming);
        isDefault(): boolean;
        getHashCode(): number;
        constructor(rightToLeft?: boolean, lineLimit?: boolean, wordWrap?: boolean, angle?: number, hotkeyPrefix?: HotkeyPrefix, trimming?: StringTrimming, firstTabOffset?: number, distanceBetweenTabs?: number);
    }
}
declare namespace Stimulsoft.Base.Drawing {
    import List = Stimulsoft.System.Collections.List;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Color = Stimulsoft.System.Drawing.Color;
    import StringBuilder = Stimulsoft.System.Text.StringBuilder;
    import Graphics = Stimulsoft.System.Drawing.Graphics;
    import Font = Stimulsoft.System.Drawing.Font;
    import Size = Stimulsoft.System.Drawing.Size;
    import StringTrimming = Stimulsoft.System.Drawing.StringTrimming;
    import Hashtable = Stimulsoft.System.Collections.Hashtable;
    class StiTextRenderer {
        private static precisionDigits;
        private static defaultParagraphLineHeight;
        static precisionModeFactor: number;
        static precisionModeEnabled: boolean;
        static correctionEnabled: boolean;
        static maxFontSize: number;
        static compatibility2009: boolean;
        static optimizeBottomMargin: boolean;
        private static hashFonts;
        private static getTabsWidth;
        static getFontIndex(fontName: string, fontSize: number, bold: boolean, italic: boolean, underlined: boolean, strikeout: boolean, superOrSubscript: boolean, tempFontList: List<StiFontState>): number;
        private static getFontIndex2;
        private static htmlNameToColor;
        static interpreteFontSizeInHtmlTagsAsInHtml: boolean;
        private static _htmlEscapeSequence;
        private static get htmlEscapeSequence();
        private static convertStringToTag;
        static parseHtmlToStates(inputHtml: string, baseState: StiHtmlState, storeStack?: boolean): StiHtmlState[];
        private static getMarginSize;
        static prepareStateText(stateText: StringBuilder): StringBuilder;
        static stateToHtml(state: StiHtmlState, state2: StiHtmlState, text: string, lineInfoIndent: number): string;
        private static getIndentString;
        private static bulletBlack;
        private static bulletWhite;
        private static insertMarker;
        private static stackToString;
        private static listLevelsToString;
        private static parseHtmlTag;
        private static parseTagIntoPairs;
        private static parseMarkerTypeAttribute;
        private static parseStyleAttributes;
        private static parseStyleAttribute;
        private static stringToListLevels;
        private static stringToStack;
        private static parseFontSize;
        private static parseSizeToEm;
        static parseColor(colorAttribute: string, inheritColor: Color): Color;
        static measureString(maxWidth: number, font: Font, text: string, angle?: number, allowHtmlTags?: boolean): Size;
        static getTextLinesAndWidths(g: Graphics, REFtext: any, font: Font, bounds: Rectangle, lineSpacing: number, wordWrap: boolean, rightToLeft: boolean, scale: number, angle: number, trimming: StringTrimming, allowHtmlTags: boolean, REFtextLines: any, REFlinesInfo: any): string[];
        static drawTextForOutput(g: Graphics, text: string, font: Font, bounds: Rectangle, foreColor: Color, backColor: Color, lineSpacing: number, horAlign: StiTextHorAlignment, vertAlign: StiVertAlignment, wordWrap: boolean, rightToLeft: boolean, scale: number, angle: number, trimming: StringTrimming, lineLimit: boolean, allowHtmlTags: boolean, outRunsList: List<RunInfo>, outFontsList: List<StiFontState>, textOptions: StiTextOptions): void;
        static measureText(g: Graphics, text: string, font: Font, bounds: Rectangle, lineSpacing: number, wordWrap: boolean, rightToLeft: boolean, scale: number, angle: number, trimming: StringTrimming, lineLimit: boolean, allowHtmlTags: boolean, textOptions: StiTextOptions): Size;
        private static drawTextBase;
        private static drawTextBase2;
        static StiForceWidthAlignTag: string;
        private static getFontWidth;
        private static getFontWidth2;
        private static isWordWrapSymbol2;
        private static isNotWordWrapSymbol;
        private static isNotWordWrapSymbol2;
        private static isCJKWordWrap;
        private static isCJKSymbol;
    }
    class StiFontState {
        fontName: string;
        fontBase: Font;
        fontScaled: Font;
        superOrSubscriptIndex: number;
        parentFontIndex: number;
        hFont: number;
        hFontScaled: number;
        hScriptCache: number;
        hScriptCacheScaled: number;
        lineHeight: number;
        ascend: number;
        descend: number;
        elipsisWidth: number;
        emValue: number;
        private _fontNameReal;
        get fontNameReal(): string;
    }
    class LineInfo {
        begin: number;
        length: number;
        needWidthAlign: boolean;
        get end(): number;
        set end(value: number);
        width: number;
        widths: number[];
        justifyOffset: number;
        text: string;
        indexOfMaxFont: number;
        lineHeight: number;
        textAlignment: StiTextHorAlignment;
        indent: number;
    }
    class RunInfo {
        text: string;
        xPos: number;
        yPos: number;
        widths: number[];
        glyphWidths: number[];
        textColor: Color;
        backColor: Color;
        fontIndex: number;
        glyphIndexList: number[];
        scaleList: number[];
        href: string;
    }
    enum StiHtmlTag {
        None = 0,
        B = 1,
        I = 2,
        U = 3,
        S = 4,
        Sup = 5,
        Sub = 6,
        Font = 7,
        FontName = 8,
        FontSize = 9,
        FontColor = 10,
        Backcolor = 11,
        LetterSpacing = 12,
        WordSpacing = 13,
        LineHeight = 14,
        TextAlign = 15,
        P = 16,
        Br = 17,
        OrderedList = 18,
        UnorderedList = 19,
        ListItem = 20,
        A = 21,
        Unknown = 22
    }
    enum StiHtmlTag2State {
        Start = 0,
        End = 1,
        Empty = 2
    }
    class StiHtmlTag2 {
        tag: StiHtmlTag;
        tagName: string;
        attributes: List<TagPair>;
        state: StiHtmlTag2State;
        get isStart(): boolean;
        get isEnd(): boolean;
        get isEmpty(): boolean;
        isStartTag(tag: StiHtmlTag): boolean;
        isEndTag(tag: StiHtmlTag): boolean;
        getAttribute(name: string): string;
        equals(tag2: StiHtmlTag2): boolean;
        toString(): string;
        constructor(tag?: StiHtmlTag, state?: StiHtmlTag2State);
    }
    class StiHtmlTagsState {
        clone(): StiHtmlTagsState;
        getStyleAttribute(name: string): string;
        bold: boolean;
        italic: boolean;
        underline: boolean;
        strikeout: boolean;
        fontSize: number;
        fontName: string;
        fontColor: Color;
        backColor: Color;
        subsript: boolean;
        superscript: boolean;
        letterSpacing: number;
        wordSpacing: number;
        lineHeight: number;
        textAlign: StiTextHorAlignment;
        isColorChanged: boolean;
        isBackcolorChanged: boolean;
        tag: StiHtmlTag2;
        indent: number;
        htmlStyle: string;
        href: string;
        hrefTarget: string;
        styleAttributes: Hashtable;
        constructor(bold: any, italic?: boolean, underline?: boolean, strikeout?: boolean, fontSize?: number, fontName?: string, fontColor?: Color, backColor?: Color, superscript?: boolean, subscript?: boolean, letterSpacing?: number, wordSpacing?: number, lineHeight?: number, textAlign?: StiTextHorAlignment);
    }
    class StiHtmlState {
        clone(): StiHtmlState;
        ts: StiHtmlTagsState;
        text: StringBuilder;
        fontIndex: number;
        posBegin: number;
        tagsStack: StiHtmlTagsState[];
        listLevels: number[];
        toString(): string;
        constructor(ts: any, posBegin?: number);
    }
    class TagPair {
        key: string;
        keyBase: string;
        value: string;
    }
}
declare namespace Stimulsoft.Base {
    enum StiPlanIdent {
        OnlineTrial = 100,
        OnlineStandard = 101,
        ServerTrial = 200,
        ServerTeam5 = 201,
        ServerTeam10 = 202,
        ServerTeam25 = 203,
        ServerTeam50 = 204,
        ServerBusiness = 205,
        ServerEnterprise = 206,
        ServerWorldWide = 207,
        Test = 300
    }
    enum StiPlanFeatureIdent {
        Cycles = 1
    }
}
declare namespace Stimulsoft.Base.Helpers {
    import DateTime = Stimulsoft.System.DateTime;
    import TimeSpan = Stimulsoft.System.TimeSpan;
    enum DateTimeFormat {
        USA_DATE = 0,
        UK_DATE = 1
    }
    class ParsedDateTime {
        readonly indexOfDate: number;
        readonly lengthOfDate: number;
        readonly indexOfTime: number;
        readonly lengthOfTime: number;
        readonly dateTime: DateTime;
        readonly isDateFound: boolean;
        readonly isTimeFound: boolean;
        readonly utcOffset: TimeSpan;
        readonly isUtcOffsetFound: boolean;
        utcDateTime: DateTime;
        constructor(indexOfDate: number, lengthOfDate: number, indexOfTime: number, lengthOfTime: number, dateTime: DateTime, utcOffset?: TimeSpan);
    }
    class DateTimeRoutines {
        private static _defaultDate;
        static get defaultDate(): DateTime;
        static set defaultDate(value: DateTime);
        static defaultDateIsNow: boolean;
        static tryParseDateTime(str: string, defaultFormat: DateTimeFormat, refDateTime: {
            ref: DateTime;
        }): boolean;
        static tryParseDateTime2(str: string, defaultFormat: DateTimeFormat, refParsedDateTime: {
            ref: ParsedDateTime;
        }): boolean;
        static tryParseDateOrTime2(str: string, defaultFormat: DateTimeFormat, refParsedDateTime: {
            ref: ParsedDateTime;
        }): boolean;
        static tryParseTime2(str: string, defaultFormat: DateTimeFormat, refParsedTime: {
            ref: ParsedDateTime;
        }, parsedDate: ParsedDateTime): boolean;
        static tryParseDate2(str: string, defaultFormat: DateTimeFormat, refParsedDate: {
            ref: ParsedDateTime;
        }): boolean;
        private static tryParseDateInternal;
        private static convertToDate;
    }
}
declare namespace Stimulsoft.Base.Helpers {
    import Size = Stimulsoft.System.Drawing.Size;
    import List = Stimulsoft.System.Collections.List;
    class StiBingMapHelper {
        static _bingKey: string;
        static bingMapKey: string;
        static bingKeysUrl: string;
        private static defaultBingKey;
        private static Script;
        static get BingKey(): string;
        static getImage(size: Size, map: {
            mapImage: string;
        }, pushPins?: List<string>): Promise<string>;
        private static base64ArrayBuffer;
        private static getBingUrl;
        static getScript(mapData: {
            [key: string]: string;
        }): string;
        private static getCacheKey;
    }
}
declare namespace Stimulsoft.Base.Helpers {
    class StiComponentProgressHelper {
        progressDelta: number;
        timerInterval: number;
        private static lockCompletedProgressHandler;
        static currentValue: number;
        static add(comp: IStiAppComponent): void;
    }
}
declare namespace Stimulsoft.Base.Helpers {
    import List = Stimulsoft.System.Collections.List;
    class StiOnlineMapRepaintHelper {
        timerInterval: number;
        browserLifetime: number;
        static init(): void;
        static fetchAllComponents(report: IStiReport): List<IStiAppComponent>;
        static clean(reportKey: string): void;
    }
}
declare namespace Stimulsoft.Base {
    class StiPacker {
        private static encryptedId;
        static allowPacking: boolean;
        static pack(bytes: number[]): number[];
        static unpack<B extends boolean>(bytes: number[], returnString: B): B extends true ? string : number[];
        static unpack2(bytes: number[]): string;
        static unpackAndDecrypt<B extends boolean>(str: string, returnString: B): B extends true ? string : number[];
        static packAndEncryptToString(data: number[] | string): string;
        static packToString(bytes: number[]): string;
        static unpackFromString(str: string): number[];
        static packToBytes(str: string, allowPacking?: boolean): number[];
        static unpackToString(bytes: number[]): string;
        private static addZipSignature;
        static isPacked(bytes: number[]): boolean;
        private static isPacked2;
    }
}
declare namespace Stimulsoft.Base.Helpers {
    class StiValueComparer {
        static equalValues(value1: any, value2: any): boolean;
        static compareArrays(a: any[], b: any[]): boolean;
    }
}
declare namespace Stimulsoft.Base.Helpers {
    import DateTime = Stimulsoft.System.DateTime;
    import TimeSpan = Stimulsoft.System.TimeSpan;
    class StiValueHelper {
        static isZero(value: any): boolean;
        static equalDecimal(value1: any, value2: any): boolean;
        static tryToString(value: any): string;
        static tryToNumber(value: any): number;
        static tryToBool(value: any): boolean;
        static tryToDateTime(value: any): DateTime;
        static tryToTimeSpan(value: any): TimeSpan;
        static tryToNullableNumber(value: any): number | null;
        static tryToNullableDateTime(value: any): DateTime | null;
        static tryToNullableTimeSpan(value: any): TimeSpan | null;
        static parseNumber(value: string): number;
        private static normalizeFloatingPointValue;
    }
}
declare namespace Stimulsoft.Base {
    enum StiJsonSaveMode {
        Report = 0,
        Document = 1
    }
}
declare namespace Stimulsoft.Base.StiJsonReportObjectHelper {
    import StiSimpleShadow = Stimulsoft.Base.Drawing.StiSimpleShadow;
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import StiBorderSide = Stimulsoft.Base.Drawing.StiBorderSide;
    import StiCap = Stimulsoft.Base.Drawing.StiCap;
    import StiJson = Stimulsoft.Base.StiJson;
    import StiBorder = Stimulsoft.Base.Drawing.StiBorder;
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    import Font = Stimulsoft.System.Drawing.Font;
    import FontStyle = Stimulsoft.System.Drawing.FontStyle;
    import GraphicsUnit = Stimulsoft.System.Drawing.GraphicsUnit;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import Color = Stimulsoft.System.Drawing.Color;
    import Size = Stimulsoft.System.Drawing.Size;
    import Point = Stimulsoft.System.Drawing.Point;
    import StiSimpleBorder = Stimulsoft.Base.Drawing.StiSimpleBorder;
    class Serialize {
        static fontArial8(font: Font): string;
        static fonSegoeUI20(font: Font): string;
        static font(font: Font, defaultFamily?: string, defaultEmSize?: number, defaultStyle?: FontStyle, defaultUnit?: GraphicsUnit): string;
        static rectangleD(rect: Rectangle): string;
        static sizeD(size: Size | Size): string;
        static jColor(color: Color, defColor?: Color): string;
        static colorArray(array: Color[]): StiJson;
        static stringArray(array: string[]): StiJson;
        static boolArray(array: boolean[]): StiJson;
        static numberArray(array: number[]): StiJson;
        static objectArray(list: IStiJsonReportObject[], mode: StiJsonSaveMode): StiJson;
        static size(size: Size): StiJson;
        static point(pos: Point): StiJson;
        static jCap(cap: StiCap): string;
        static jBrush(brush: StiBrush, defaultBrush?: StiBrush): string;
        static jBorderSide(side: StiBorderSide): string;
        static jBorder(border: StiBorder): string;
        static jBorder2(border: StiSimpleBorder): string;
        static jShadow(shadow: StiSimpleShadow): string;
    }
    class Deserialize {
        static stringArray(jObject: StiJson): string[];
        static numberArray(jObject: StiJson): number[];
        static boolArray(jObject: StiJson): boolean[];
        static font(text: string, defaultFont: Font): Font;
        static jBorderSide(text: string): StiBorderSide;
        static jCap(text: string): StiCap;
        static border(text: string): StiBorder;
        static simpleBorder(text: string): StiSimpleBorder;
        static simpleSahdow(text: string): StiSimpleShadow;
        static color(value: string): Color;
        static brush(text: string): StiBrush;
        static colorArray(jObject: StiJson): Color[];
        static size(jObject: StiJson): Size;
        static rectangleD(text: string): Rectangle;
        static sizeD(text: string): Size;
        static point(jObject: StiJson): Point;
    }
}
declare namespace Stimulsoft.Base {
    import DataSet = Stimulsoft.System.Data.DataSet;
    import JsonRelationDirection = Stimulsoft.System.Data.JsonRelationDirection;
    class StiJsonToDataSetConverter {
        static getDataSet(param: string | number[] | any, jsonRelationDirection?: JsonRelationDirection): DataSet;
        static getDataSetFromXml(param: any, relationDirection?: StiRelationDirection): DataSet;
    }
}
declare namespace Stimulsoft.Base.Licenses {
    enum StiProductIdent {
        Ultimate = 1,
        Net = 2,
        Wpf = 3,
        Web = 4,
        Silverlight = 5,
        Js = 6,
        Java = 7,
        Php = 8,
        NetCore = 9,
        Uwp = 10,
        Flex = 11,
        BIDesigner = 12,
        DbsJs = 13,
        DbsWin = 14,
        DbsWeb = 15,
        BIDesktop = 16,
        BIServer = 17,
        BICloud = 18,
        CloudReports = 20,
        CloudDashboards = 21,
        Angular = 22,
        DbsAngular = 23,
        DbsPhp = 24,
        FormsWin = 25,
        FormsWeb = 26,
        FormsJs = 27,
        FormsPhp = 28
    }
    enum StiActivationType {
        Server = 1,
        Developer = 2
    }
}
declare namespace Stimulsoft.Base.Licenses {
    class StiCryptHelper {
        static decrypt(str: string, password: string): string;
        static encrypt(str: string, password?: string): string;
        static recrypt(str: string, oldPassword: string, newPassword: string): string;
    }
}
declare namespace Stimulsoft.Base.Licenses {
    class StiRsaPublicKey {
        static getKey(): any;
    }
}
declare namespace Stimulsoft.Base.Licenses {
    class StiLicenseObject {
        encryptKey: string;
        loadFromString(str: string): void;
        saveToString(): string;
        loadFromBytes(bytes: number[]): void;
        decryptFromBytes(bytes: number[]): void;
        decryptFromString(str: string): void;
    }
}
declare namespace Stimulsoft.Base.Licenses {
    import List = Stimulsoft.System.Collections.List;
    import DateTime = Stimulsoft.System.DateTime;
    class StiLicenseKey extends StiLicenseObject {
        activationDate: DateTime;
        signature: string;
        owner: string;
        userName: string;
        startDate: DateTime;
        endDate: DateTime;
        seviceId: string;
        planId: StiPlanIdent;
        products: List<StiLicenseProduct>;
        productName: string;
        productLogo: number[];
        productFavIcon: number[];
        productDescription: string;
        productUrl: string;
        clone(): StiLicenseKey;
        static get1(bytes: number[]): StiLicenseKey;
        static get2(str: string): StiLicenseKey;
        constructor();
    }
}
declare namespace Stimulsoft.Base {
    class StiLicense {
        static licenseKey: any;
        static _key: string;
        static get key(): string;
        static set key(value: string);
        static get Key(): string;
        static set Key(value: string);
        static setNewLicenseKey(value: string, throwException?: boolean): void;
        private static isValidLicenseKey;
        static loadFromFile(file: string): void;
        static loadFromString(licenseKey: string): void;
    }
}
declare namespace Stimulsoft.Base.Licenses {
    class StiLicenseActivationResponse extends StiLicenseObject {
        encryptKey: string;
        licenseKey: StiLicenseKey;
        exception: string;
        resultSuccess: boolean;
        resultNotice: StiNotice;
    }
}
declare namespace Stimulsoft.Base.Licenses {
    class StiLicenseKeyContainer extends StiKeyObject {
        checkSum: string;
        license: number[];
    }
}
declare namespace Stimulsoft.Base.Licenses {
    import StiProductIdent = Stimulsoft.Base.Licenses.StiProductIdent;
    class StiLicenseKeyValidator {
        private static indexValidator;
        static isValidOn(ident: StiProductIdent): boolean;
        static isValidOnDbsJS(): boolean;
        static isValidOnAnyDbs(): boolean;
        static isValidOnJS(): boolean;
        static isValidOnAnyReports(): boolean;
        static isValidOnAnyPlatform(): boolean;
        static isValidOnBI(): boolean;
        private static isJSPlatform;
        private static isAnyDbsPlatform;
        private static isAnyReportsPlatform;
        private static isDbsJSPlatform;
        private static isBIPlatform;
        private static getLicenseKey;
    }
}
declare namespace Stimulsoft.Base.Licenses {
    import DateTime = Stimulsoft.System.DateTime;
    class StiLicenseProduct {
        expirationDate: DateTime;
        ident: StiProductIdent;
    }
}
declare namespace Stimulsoft.Base.Map {
    import List = Stimulsoft.System.Collections.List;
    let IStiMapKeyHelper: System.Interface<IStiMapKeyHelper>;
    interface IStiMapKeyHelper {
        getMapIdents(key: string): List<string>;
        getIsoAlpha2FromName(country: string, mapId: string, lang: string): string;
        getIsoAlpha3FromName(country: string, mapId: string, lang: string): string;
        getNameFromIsoAlpha2(alpha3: string, mapId: string, lang: string): string;
        getNameFromIsoAlpha3(alpha3: string, mapId: string, lang: string): string;
        normalizeName(name: string, mapId: string, lang: string, report: IStiReport): string;
    }
}
declare namespace Stimulsoft.Base.Meta {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import StiJson = Stimulsoft.Base.StiJson;
    class StiMeta<T = any> {
        jsName: string;
        getValue: (mode?: StiJsonSaveMode) => T | StiJson;
        checkValue: (mode?: StiJsonSaveMode) => boolean;
        setValue: (property: StiJson) => void;
        setValueXml: (node: XmlNode) => void;
        filterSaveMode: StiJsonSaveMode;
        originalName: string[];
        saveToJsonObject(jObject: StiJson, obj: any, mode?: StiJsonSaveMode): void;
        loadFromJsonObject(property: StiJson, obj: IStiJsonReportObject): void;
        loadFromXml(node: XmlNode, obj: IStiJsonReportObject): void;
        get(getValue: (mode?: StiJsonSaveMode) => T | StiJson): StiMeta<T>;
        check(checkValue: (mode?: StiJsonSaveMode) => boolean): StiMeta<T>;
        set(setValue: (property: StiJson) => void): StiMeta<T>;
        setXml(setValueXml: (value: XmlNode) => void): StiMeta<T>;
        constructor(originalName: string | string[], jsName?: string, getValue?: (mode?: StiJsonSaveMode) => T | StiJson, checkValue?: (mode?: StiJsonSaveMode) => boolean, setValue?: (property: StiJson) => void, setValueXml?: (node: XmlNode) => void, filterSaveMode?: StiJsonSaveMode);
    }
}
declare namespace Stimulsoft.Base.Meta {
    import StiJson = Stimulsoft.Base.StiJson;
    class StiBoolMeta extends StiMeta<boolean> {
        defaultValue: boolean;
        ignoreDefaultValues: boolean;
        saveToJsonObject(jObject: StiJson, obj: any): void;
        constructor(originalName: string | string[], jsName?: string, defaultValue?: boolean, ignoreDefaultValues?: boolean);
    }
}
declare namespace Stimulsoft.Base.Meta {
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import StiJson = Stimulsoft.Base.StiJson;
    class StiBrushMeta extends StiMeta<StiBrush> {
        defaultValue: StiBrush;
        saveToJsonObject(jObject: StiJson, obj: any): void;
        constructor(originalName: string | string[], jsName?: string, defaultValue?: StiBrush);
    }
}
declare namespace Stimulsoft.Base.Meta {
    import Color = Stimulsoft.System.Drawing.Color;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import StiJson = Stimulsoft.Base.StiJson;
    class StiColorMeta extends StiMeta<Color> {
        defaultValue: Color;
        saveToJsonObject(jObject: StiJson, obj: any, mode?: StiJsonSaveMode): void;
        constructor(originalName: string, jsName?: string, defaultValue?: Color, filterSaveMode?: StiJsonSaveMode);
    }
}
declare namespace Stimulsoft.Base.Meta {
    import StiJson = Stimulsoft.Base.StiJson;
    class StiEnumMeta extends StiMeta {
        enumType: any;
        defaultValue: any;
        saveToJsonObject(jObject: StiJson, obj: any): void;
        constructor(originalName: string | string[], jsName: string, enumType: any, defaultValue?: any);
    }
}
declare namespace Stimulsoft.Base.Meta {
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import StiJson = Stimulsoft.Base.StiJson;
    class StiEventMeta extends StiMeta {
        eventType: any;
        saveToJsonObject(jObject: StiJson, obj: any, mode: StiJsonSaveMode): void;
        constructor(originalName: string, eventType: any);
    }
}
declare namespace Stimulsoft.Base.Meta {
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import StiJson = Stimulsoft.Base.StiJson;
    class StiExpressionMeta extends StiMeta {
        saveToJsonObject(jObject: StiJson, obj: any, mode: StiJsonSaveMode): void;
        constructor(originalName: string | string[], jsName?: string);
    }
}
declare namespace Stimulsoft.Base.Meta {
    import GraphicsUnit = Stimulsoft.System.Drawing.GraphicsUnit;
    import FontStyle = Stimulsoft.System.Drawing.FontStyle;
    import StiJson = Stimulsoft.Base.StiJson;
    import Font = Stimulsoft.System.Drawing.Font;
    class StiFontMeta extends StiMeta<Font> {
        defaultFamily: string;
        defaultEmSize: number;
        defaultStyle: FontStyle;
        defaultUnit: GraphicsUnit;
        saveToJsonObject(jObject: StiJson, obj: any): void;
        constructor(originalName: string | string[], jsName?: string, defaultFamily?: string, defaultEmSize?: number, defaultStyle?: FontStyle, defaultUnit?: GraphicsUnit);
    }
}
declare namespace Stimulsoft.Base.Meta {
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import StiJson = Stimulsoft.Base.StiJson;
    class StiMetaHelper {
        static saveToJsonObject(mode: StiJsonSaveMode, obj: {
            meta(): StiMeta[];
        }, jObject?: StiJson): StiJson;
        static loadFromJsonObject(jObject: StiJson, obj: any): void;
        static loadFromXml(xmlNode: XmlNode, obj: any): void;
    }
}
declare namespace Stimulsoft.Base.Meta {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import StiJson = Stimulsoft.Base.StiJson;
    class StiNumberMeta extends StiMeta<number> {
        defaultValue: number;
        saveToJsonObject(jObject: StiJson, obj: any): void;
        constructor(originalName: string | string[], jsName?: string, defaultValue?: number);
    }
}
declare namespace Stimulsoft.Base.Meta {
    import IStiJsonReportObject = Stimulsoft.Base.JsonReportObject.IStiJsonReportObject;
    class StiObjectMeta extends StiMeta<IStiJsonReportObject> {
        constructor(originalName: string | string[], jsName?: string);
    }
}
declare namespace Stimulsoft.Base.Meta {
    import StiJson = Stimulsoft.Base.StiJson;
    class StiRemoveMeta extends StiMeta {
        saveToJsonObject(jObject: StiJson, obj?: any): void;
        constructor(originalName: string | string[]);
    }
}
declare namespace Stimulsoft.Base.Meta {
    import Size = Stimulsoft.System.Drawing.Size;
    import StiJson = Stimulsoft.Base.StiJson;
    class StiSizeMeta extends StiMeta<Size> {
        defaultValue: Size;
        saveToJsonObject(jObject: StiJson, obj: any): void;
        constructor(originalName: string, jsName?: string, defaultValue?: Size);
    }
}
declare namespace Stimulsoft.Base.Meta {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import StiJson = Stimulsoft.Base.StiJson;
    class StiStringMeta extends StiMeta<string> {
        defaulString: string;
        saveToJsonObject(jObject: StiJson, obj: any): void;
        constructor(originalName: string | string[], jsName?: string, defaulString?: string);
    }
}
declare namespace Stimulsoft.Base.Meta {
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import StiJsonSaveMode = Stimulsoft.Base.StiJsonSaveMode;
    import StiJson = Stimulsoft.Base.StiJson;
    class StiStringNullOrEmptyMeta extends StiMeta<string> {
        saveToJsonObject(jObject: StiJson, obj: any, mode?: StiJsonSaveMode): void;
        constructor(originalName: string | string[], jsName?: string, filterSaveMode?: StiJsonSaveMode);
    }
}
declare namespace Stimulsoft.Base.Meters {
    let IStiArgumentMeter: System.Interface<IStiArgumentMeter>;
    interface IStiArgumentMeter {
    }
}
declare namespace Stimulsoft.Base.Meters {
    import Color = Stimulsoft.System.Drawing.Color;
    let IStiBubbleColumn: System.Interface<IStiBubbleColumn>;
    interface IStiBubbleColumn {
        allowCustomColors: boolean;
        positiveColor: Color;
        negativeColor: Color;
    }
}
declare namespace Stimulsoft.Base.Meters {
    import StiCardsColumnVisibility = Stimulsoft.Base.Drawing.StiCardsColumnVisibility;
    let IStiCardsColumn: System.Interface<IStiCardsColumn>;
    interface IStiCardsColumn {
        visibility: StiCardsColumnVisibility;
        visibilityExpression: string;
        height: number;
        wrapLine: boolean;
    }
}
declare namespace Stimulsoft.Base.Meters {
    let IStiColorMapMeter: System.Interface<IStiColorMapMeter>;
    interface IStiColorMapMeter {
    }
}
declare namespace Stimulsoft.Base.Meters {
    import Color = Stimulsoft.System.Drawing.Color;
    let IStiColorScaleColumn: System.Interface<IStiColorScaleColumn>;
    interface IStiColorScaleColumn {
        minimumColor: Color;
        maximumColor: Color;
    }
}
declare namespace Stimulsoft.Base.Meters {
    import Color = Stimulsoft.System.Drawing.Color;
    let IStiDataBarsColumn: System.Interface<IStiDataBarsColumn>;
    interface IStiDataBarsColumn {
        width: number;
        positiveColor: Color;
        negativeColor: Color;
        overlappedColor: Color;
        fillColor: Color;
        minimum: string;
        maximum: string;
    }
}
declare namespace Stimulsoft.Base.Meters {
    let IStiDimensionCardsColumn: System.Interface<IStiDimensionCardsColumn>;
    interface IStiDimensionCardsColumn {
    }
}
declare namespace Stimulsoft.Base.Meters {
    let IStiDimensionColumn: System.Interface<IStiDimensionColumn>;
    interface IStiDimensionColumn {
        showHyperlink: boolean;
        hyperlinkPattern: string;
    }
}
declare namespace Stimulsoft.Base.Meters {
    let IStiDimensionMeter: System.Interface<IStiDimensionMeter>;
    interface IStiDimensionMeter extends IStiMeter {
    }
}
declare namespace Stimulsoft.Base.Meters {
    let IStiGroupMapMeter: System.Interface<IStiGroupMapMeter>;
    interface IStiGroupMapMeter {
    }
}
declare namespace Stimulsoft.Base.Meters {
    let IStiIndicatorColumn: System.Interface<IStiIndicatorColumn>;
    interface IStiIndicatorColumn {
    }
}
declare namespace Stimulsoft.Base.Meters {
    let IStiKeyMapMeter: System.Interface<IStiKeyMapMeter>;
    interface IStiKeyMapMeter {
    }
}
declare namespace Stimulsoft.Base.Meters {
    let IStiLocalizedMeter: System.Interface<IStiLocalizedMeter>;
    interface IStiLocalizedMeter {
        localizedName: string;
    }
}
declare namespace Stimulsoft.Base.Meters {
    let IStiMaxGaugeMeter: System.Interface<IStiMaxGaugeMeter>;
    interface IStiMaxGaugeMeter extends IStiMeter {
    }
}
declare namespace Stimulsoft.Base.Meters {
    let IStiMeasureCardsColumn: System.Interface<IStiDimensionCardsColumn>;
    interface IStiMeasureCardsColumn {
    }
}
declare namespace Stimulsoft.Base.Meters {
    let IStiMeasureColumn: System.Interface<IStiMeasureColumn>;
    interface IStiMeasureColumn {
    }
}
declare namespace Stimulsoft.Base.Meters {
    let IStiMeasureMeter: System.Interface<IStiMeasureMeter>;
    interface IStiMeasureMeter extends IStiMeter {
    }
}
declare namespace Stimulsoft.Base.Meters {
    import ICloneable = Stimulsoft.System.ICloneable;
    import IAsIs = Stimulsoft.System.IAsIs;
    let IStiMeter: System.Interface<IStiMeter>;
    interface IStiMeter extends IAsIs, ICloneable {
        getUniqueCode(): number;
        key: string;
        expression: string;
        label: string;
    }
}
declare namespace Stimulsoft.Base.Meters {
    let IStiMinGaugeMeter: System.Interface<IStiMinGaugeMeter>;
    interface IStiMinGaugeMeter extends IStiMeter {
    }
}
declare namespace Stimulsoft.Base.Meters {
    let IStiNameMapMeter: System.Interface<IStiNameMapMeter>;
    interface IStiNameMapMeter {
    }
}
declare namespace Stimulsoft.Base.Meters {
    let IStiPivotColumn: System.Interface<IStiPivotColumn>;
    interface IStiPivotColumn {
        showTotal: boolean;
        totalLabel: string;
        strSortDirection: string;
        expandExpression: string;
    }
}
declare namespace Stimulsoft.Base.Meters {
    let IStiPivotRow: System.Interface<IStiPivotRow>;
    interface IStiPivotRow {
        showTotal: boolean;
        totalLabel: string;
        strSortDirection: string;
        expandExpression: string;
    }
}
declare namespace Stimulsoft.Base.Meters {
    let IStiPivotSummary: System.Interface<IStiPivotSummary>;
    interface IStiPivotSummary {
        hideZeros: boolean;
    }
}
declare namespace Stimulsoft.Base.Meters {
    let IStiSeriesMeter: System.Interface<IStiSeriesMeter>;
    interface IStiSeriesMeter {
    }
}
declare namespace Stimulsoft.Base.Meters {
    import Color = Stimulsoft.System.Drawing.Color;
    let IStiSparklinesColumn: System.Interface<IStiSparklinesColumn>;
    interface IStiSparklinesColumn {
        allowCustomColors: boolean;
        showHighLowPoints: boolean;
        showFirstLastPoints: boolean;
        positiveColor: Color;
        negativeColor: Color;
    }
}
declare namespace Stimulsoft.Base.Meters {
    import StiHorAlignment = Stimulsoft.Base.Drawing.StiHorAlignment;
    import StiTableColumnVisibility = Stimulsoft.Base.Drawing.StiTableColumnVisibility;
    let IStiTableColumn: System.Interface<IStiTableColumn>;
    interface IStiTableColumn {
        visible: boolean;
        showTotalSummary: boolean;
        summaryType: StiSummaryColumnType;
        visibility: StiTableColumnVisibility;
        visibilityExpression: string;
        summaryAlignment: StiHorAlignment;
        headerAlignment: StiHorAlignment;
    }
}
declare namespace Stimulsoft.Base.Meters {
    let IStiTargetMeter: System.Interface<IStiTargetMeter>;
    interface IStiTargetMeter {
    }
}
declare namespace Stimulsoft.Base.Meters {
    let IStiValueMapMeter: System.Interface<IStiValueMapMeter>;
    interface IStiValueMapMeter {
    }
}
declare namespace Stimulsoft.Base.Meters {
    let IStiValueMeter: System.Interface<IStiValueMeter>;
    interface IStiValueMeter {
    }
}
declare namespace Stimulsoft.Base {
    enum StiNoticeIdent {
        ActivationMaxActivationsReached = 1,
        ActivationExpiriedBeforeFirstRelease = 2,
        ActivationLicenseIsNotCorrect = 3,
        ActivationLockedAccount = 4,
        ActivationServerVersionNotAllowed = 5,
        ActivationServerIsNotAvailableNow = 6,
        ActivationSomeTroublesOccurred = 7,
        ActivationUserNameOrPasswordIsWrong = 8,
        ActivationWrongAccountType = 9,
        AuthAccountCantBeUsedNow = 10,
        AuthAccountIsNotActivated = 11,
        AuthCantChangeSystemRole = 12,
        AuthCantChangeRoleBecauseLastAdministratorUser = 13,
        AuthCantChangeRoleBecauseLastSupervisorUser = 14,
        AuthCantDeleteHimselfUser = 15,
        AuthCantDeleteLastAdministratorUser = 16,
        AuthCantDeleteLastSupervisorUser = 17,
        AuthCantDeleteSystemRole = 18,
        AuthCantDisableUserBecauseLastAdministratorUser = 19,
        AuthCantDisableUserBecauseLastSupervisorUser = 20,
        AuthOAuthIdNotSpecified = 21,
        AuthPasswordIsTooShort = 22,
        AuthPasswordIsNotSpecified = 23,
        AuthPasswordIsNotCorrect = 24,
        AuthRequestsLimitIsExceeded = 25,
        AuthRoleCantBeDeletedBecauseUsedByUsers = 26,
        AuthRoleNameAlreadyExists = 27,
        AuthRoleNameIsSystemRole = 28,
        AuthUserHasLoggedOut = 29,
        AuthUserNameAlreadyExists = 30,
        AuthUserNameIsNotSpecified = 31,
        AuthUserNameOrPasswordIsNotCorrect = 32,
        AuthUserNameShouldLookLikeAnEmailAddress = 33,
        AuthWorkspaceNameAlreadyInUse = 34,
        CommandTimeOut = 35,
        CustomMessage = 36,
        ExecutionError = 37,
        IsNotAuthorized = 38,
        IsNotDeleted = 39,
        IsNotCorrect = 40,
        IsNotEqual = 41,
        IsNotFound = 42,
        IsNotRecognized = 43,
        IsNotSpecified = 44,
        ItemCantBeDeletedBecauseItemIsAttachedToOtherItems = 45,
        ItemCantBeMovedToSpecifiedPlace = 46,
        ItemDoesNotSupport = 47,
        KeyAndToKeyIsEqual = 48,
        NotificationFailed = 49,
        NotificationFileUploading = 50,
        NotificationFilesUploadingComplete = 51,
        NotificationItemDelete = 52,
        NotificationItemDeleteComplete = 53,
        NotificationItemRestore = 54,
        NotificationItemRestoreComplete = 55,
        NotificationItemTransfer = 56,
        NotificationItemTransferComplete = 57,
        NotificationItemWaitingProcessing = 58,
        NotificationOperationAborted = 59,
        NotificationRecycleBinCleaning = 60,
        NotificationRecycleBinCleaningComplete = 61,
        NotificationRecycleBinWaitingProcessing = 62,
        NotificationReportCompiling = 63,
        NotificationReportDataProcessing = 64,
        NotificationReportExporting = 65,
        NotificationReportExportingComplete = 66,
        NotificationReportRendering = 67,
        NotificationReportRenderingComplete = 68,
        NotificationReportSaving = 69,
        NotificationReportWaitingProcessing = 70,
        NotificationSchedulerRunning = 71,
        NotificationSchedulerRunningComplete = 72,
        NotificationSchedulerWaitingProcessing = 73,
        NotificationTransferring = 74,
        NotificationTransferringComplete = 75,
        NotificationTitleFilesUploading = 76,
        NotificationTitleItemRefreshing = 77,
        NotificationTitleItemTransferring = 78,
        NotificationTitleReportExporting = 79,
        NotificationTitleReportRendering = 80,
        NotificationTitleSchedulerRunning = 81,
        QuotaMaximumComputingCyclesCountExceeded = 82,
        QuotaMaximumFileSizeExceeded = 83,
        QuotaMaximumItemsCountExceeded = 84,
        QuotaMaximumReportPagesCountExceeded = 85,
        QuotaMaximumUsersCountExceeded = 86,
        QuotaMaximumWorkspacesCountExceeded = 87,
        AccessDenied = 88,
        OutOfRange = 89,
        ParsingCommandException = 90,
        SchedulerCantRunItSelf = 91,
        SessionTimeOut = 92,
        SnapshotAlreadyProcessed = 93,
        SpecifiedItemIsNot = 94,
        WithSpecifiedKeyIsNotFound = 95,
        VersionCopyFromItem = 96,
        VersionCreatedFromFile = 97,
        VersionCreatedFromItem = 98,
        VersionNewItemCreation = 99,
        VersionLoadedFromFile = 100
    }
}
declare namespace Stimulsoft.Base {
    class StiNotice {
        ident: StiNoticeIdent;
        arguments: string[];
        customMessage: string;
    }
}
declare namespace Stimulsoft.Base.Services {
    import XmlNode = Stimulsoft.System.Xml.XmlNode;
    import StiMeta = Stimulsoft.Base.Meta.StiMeta;
    import IAsIs = Stimulsoft.System.IAsIs;
    import StiRepositoryItems = Stimulsoft.Base.StiRepositoryItems;
    import ICloneable = Stimulsoft.System.ICloneable;
    class StiService implements ICloneable, IAsIs {
        clone(cloneProperties?: boolean, cloneComponents?: boolean, base?: boolean): any;
        memberwiseClone(base?: boolean): StiService;
        implements(): any[];
        is<T>(type: (new (...args: any[]) => T) | Stimulsoft.System.Interface<T>): this is T;
        is2<T>(type: (new (...args: any[]) => T) | Stimulsoft.System.Interface<T>): boolean;
        as<T>(type: (new (...args: any[]) => T) | Stimulsoft.System.Interface<T>): T;
        protected _hash: StiMeta[];
        meta(): StiMeta[];
        saveToJsonObject(mode: StiJsonSaveMode): StiJson;
        loadFromJsonObject(jObject: StiJson): void;
        loadFromXml(xmlNode: XmlNode): void;
        isPropertyPresent(key: any): boolean;
        private _properties;
        get properties(): StiRepositoryItems;
        set properties(value: StiRepositoryItems);
        protected isPropertiesInitializedProtected(): boolean;
        get serviceCategory(): string;
        get serviceName(): string;
        get serviceInfo(): string;
        get serviceType(): Stimulsoft.System.Type;
        get serviceEnabled(): boolean;
        set serviceEnabled(value: boolean);
    }
}
declare namespace Stimulsoft.Base.SignatureFonts {
    enum StiSignatureStyle {
        Style1 = 0,
        Style2 = 1,
        Style3 = 2
    }
}
declare namespace Stimulsoft.Base {
    import StiSignatureStyle = Stimulsoft.Base.SignatureFonts.StiSignatureStyle;
    class StiSignatureFontsHelper {
        static getFontName(style: StiSignatureStyle): string;
    }
}
declare namespace Stimulsoft.ExternalLibrary.JSZip {
    interface JSZip {
        file(path: string): JSZipObject;
        file(path: RegExp): JSZipObject[];
        file(path: string, data: any, options?: JSZipFileOptions): JSZip;
        folder(name: string): JSZip;
        folder(name: RegExp): JSZipObject[];
        filter(predicate: (relativePath: string, file: JSZipObject) => boolean): JSZipObject[];
        remove(path: string): JSZip;
        generate(options?: JSZipGeneratorOptions): any;
        load(data: any, options: JSZipLoadOptions): JSZip;
    }
    interface JSZipObject {
        name: string;
        dir: boolean;
        date: Date;
        comment: string;
        options: JSZipObjectOptions;
        asText(): string;
        asBinary(): string;
        asArrayBuffer(): ArrayBuffer;
        asUint8Array(): Uint8Array;
    }
    interface JSZipFileOptions {
        base64?: boolean;
        binary?: boolean;
        date?: Date;
        compression?: string;
        comment?: string;
        optimizedBinaryString?: boolean;
        createFolders?: boolean;
    }
    interface JSZipObjectOptions {
        base64: boolean;
        binary: boolean;
        dir: boolean;
        date: Date;
        compression: string;
    }
    interface JSZipGeneratorOptions {
        base64?: boolean;
        compression?: string;
        type?: string;
        comment?: string;
    }
    interface JSZipLoadOptions {
        base64?: boolean;
        checkCRC32?: boolean;
        optimizedBinaryString?: boolean;
        createFolders?: boolean;
    }
    interface JSZipSupport {
        arraybuffer: boolean;
        uint8array: boolean;
        blob: boolean;
        nodebuffer: boolean;
    }
    interface DEFLATE {
        compress(input: string | number[] | Uint8Array, compressionOptions: {
            level: number;
        }): Uint8Array;
        uncompress(input: string | number[] | Uint8Array): Uint8Array;
    }
    let prototype: JSZip;
    let support: JSZipSupport;
    let compressions: {
        DEFLATE: DEFLATE;
    };
}
declare namespace Stimulsoft.ExternalLibrary {
    function JSZip(data?: any, options?: Stimulsoft.ExternalLibrary.JSZip.JSZipLoadOptions): Stimulsoft.ExternalLibrary.JSZip.JSZip;
}
declare namespace Stimulsoft.Base {
    class StiGZipHelper {
        private static DefaultLevel;
        private static DefaultMethod;
        private static ID1;
        private static ID2;
        private static _crcTable;
        static get crcTable(): number[];
        static crc32(data: number[]): number;
        private static putByte;
        private static putShort;
        private static putLong;
        private static putString;
        private static readByte;
        private static readShort;
        private static readLong;
        private static readString;
        private static readBytes;
        static pack(data2: string | number[] | Uint8Array, name?: string): string | number[];
        static unpack<B extends boolean>(data: string | number[] | Uint8Array, returnString: B): B extends true ? string : number[];
    }
}
declare namespace Stimulsoft.Base.Zip {
    import DateTime = Stimulsoft.System.DateTime;
    import MemoryStream = Stimulsoft.System.IO.MemoryStream;
    class StiZipWriter20 {
        static convertToArray(useUnicode: boolean, str: string): number[];
        static getDosTime(dt: DateTime): number;
        private _mainStream;
        private zip;
        begin(stream: MemoryStream, leaveOpen: boolean): void;
        addFile(fileName: string, dataStream: MemoryStream, closeDataStream?: boolean): void;
        end(): void;
        constructor();
    }
}
