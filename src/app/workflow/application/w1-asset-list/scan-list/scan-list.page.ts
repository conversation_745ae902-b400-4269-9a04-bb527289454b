/*
 * @Author: your name
 * @Date: 2020-08-18 15:32:42
 * @LastEditTime: 2020-11-20 13:32:02
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /asset-force-mobile/asset-force/src/app/workflow/application/w1-asset-list/scan-list/scan-list.page.ts
 */
import {Component, NgZone, OnInit, ViewChild} from '@angular/core';
import {ActivatedRoute} from '@angular/router';
import {WorkflowActionsService} from 'src/app/services/workflow/workflow-actions.service';
import {
  ApplicationAssetPageService,
} from 'src/app/services/workflow/workflow-service/application/scan-list/application-asset-page.service';
import {WfPageWidgetService} from 'src/app/services/workflow/workflow-service/other/wf-page-widget.service';
import {HttpGetWorkflowListService} from '../../../../httpUtils/workflow/http-getWorkflowList.service';
import {HttpAssetService} from '../../../../httpUtils/asset/http-aseet.service';
import {AssetTypeItem} from '../../../../models/assetTypeItem';
import {WorkflowAssetColumn} from '../../../../models/workflow/WorkflowAssetColumn';
import {AssetDetailService} from 'src/app/services/assetdetail/asset-detail.service';
import {LoginService} from '../../../../services/login/login.service';
import {WFDataNumAndNextPageType} from 'src/app/models/workflow/AssetsByKeywordInWfAssetListForAssignScan';
import _ from 'lodash';
import {
  WorkflowGetApplicationWorkflowListService,
} from 'src/app/services/workflow/get-applicationWorkflowList/workflow-getApplicationWorkflowList.service';
import {IonInfiniteScroll, NavController} from '@ionic/angular';
import {Throttle} from 'src/app/utils/throttle.decorator';
import {HttpUtilsService} from '../../../../utils/http-utils.service';
import {HideOverlayIconPage} from 'src/app/live/hide-overlay-icon.page';

@Component({
  selector: 'app-scan-list',
  templateUrl: './scan-list.page.html',
  styleUrls: ['./scan-list.page.scss'],
})
export class ScanListPage extends HideOverlayIconPage<any> implements OnInit {
  // 資産の一覧（表示用）
  items: { name: string; id: number; thumbnail: string; isDelete: boolean }[];
  @ViewChild('infiniteScroll') infiniteScroll: IonInfiniteScroll; // 無限スクロールのローディング
  // アイテムを選択可能か
  canSelect = false;
  // 舞浜かどうか
  isUpdateAmount: string;
  isCountingType = WfPageWidgetService.WF_DEFAULT_AMOUNT_TYPE;
  workflowState: string;
  hasScanTask: boolean;
  showClamin: boolean;
  amountActionTaskUpdateItemName: string;
  // ユーザータスクとか
  taskType: string;
  showAssetLocation = false; // 資産情報表示画面で「場所」が表示されるか
  wfType = { isDefault: true, isNewW3: false }; // 資産種類
  rawFormData; // オリジナルフォームデータ
  pageAction: ApplicationAssetPageService; // 画面ロジック
  assetCardData: { name: string; barcode: string; location: string }[]; // 資産情報
  assetsCount = 0; // 資産個数

  assetTypeItemMap: { [key: number]: AssetTypeItem };
  assetColumns: WorkflowAssetColumn[] = [];
  private assetNameDisplayName: string;
  private identityCodeDisplayName: string;
  workflowScript;
  workflowId;
  processDefinitionId;
  assetTypeId;
  params;
  processInstanceId;
  taskId;

  isReloadData = false; // pull down for reload data
  noData = false; //「全件表示されました」表示
  totalCount; //总计件数
  /** input search 最终api传递数据 核心 */
  dataNumAndNextPage: WFDataNumAndNextPageType = {
    processInstanceId: undefined,
    taskId: undefined,
    skip: undefined,
    rows: undefined,
    keyword: undefined,
    scanState: undefined,
    moreThenLimit: false,
  };
  workflowInputKey = ''; //检索的keyword
  // 页码
  private nextPageNum = 1;
  /** 每次后台返回多少条数据 */
  private readonly getAmountDataNumber = 20;

  /**
   * 移除底部默认的10px空白高度
   *
   * @memberof ScanListPage
   */
  isFooterNeedBlankSpace = false;

  constructor(
    route: ActivatedRoute,
    private workflowActionsService: WorkflowActionsService,
    private httpGetWorkflowListService: HttpGetWorkflowListService,
    private httpAssetService: HttpAssetService,
    private assetDetailService: AssetDetailService,
    private loginService: LoginService,
    private WorkflowGetApplicationWorkflowListService: WorkflowGetApplicationWorkflowListService,
    private http: HttpUtilsService,
    navController: NavController,
    ngZone: NgZone,
  ) {
    super(navController, route, ngZone);
  }

  async ngOnInit() {
    super.ngOnInit();
    const params = this.activatedRoute.snapshot.queryParamMap['params'];
    this.params = params;
    this.workflowId = params.workflowId;
    this.processDefinitionId = params.processDefinitionId;
    this.assetTypeId = params.assetTypeIdWithFirstWf;
    this.processInstanceId = params.processInstanceId;
    this.taskId = params.taskId;

    this.taskType = params.taskType;
    this.isUpdateAmount = params.isUpdateAmount;
    this.isCountingType = params.isCountingType;
    this.hasScanTask = params.hasScanTask;
    this.showClamin = params.showClamin;
    this.workflowState = params.workflowState;
    this.amountActionTaskUpdateItemName = params.amountActionTaskUpdateItemName;
    this.rawFormData = params.rawFormData;
    this.pageAction = new ApplicationAssetPageService(this.rawFormData);
    this.workflowScript = params.workflowScript;
    // 初始数据整理
    this.dataNumAndNextPage = {
      processInstanceId: _.toNumber(this.processInstanceId),
      taskId: _.toNumber(this.taskId),
      scanState: undefined,
      skip: 0,
      rows: this.getAmountDataNumber,
      keyword: undefined,
      moreThenLimit: false,
    };
    await this.assetListPagePaginationTarget();
  }

  async doRefresh(event) {
    this.clearData();
    console.log('doRefresh === === == ');
    event.target.disabled = true;
    this.dataNumAndNextPage.moreThenLimit = true;
    this.nextPageNum = 1;
    await this.assetListPagePaginationTarget();

    event.target.complete();
    setTimeout(() => {
      event.target.disabled = false;
    }, 2000);
  }

  async onClicklistLoadData(event) {
    console.log('fetchMore data=== == == ==');
    this.nextPageNum = this.nextPageNum + 1;
    await this.assetListPagePaginationTarget();
    event.target.complete();
  }

  /**
   * 検索する
   */
  @Throttle()
  async doSearch() {
    this.clearData();
    if (_.isEmpty(this.workflowInputKey)) {
      this.dataNumAndNextPage.keyword = undefined;
    } else {
      this.dataNumAndNextPage.keyword = this.workflowInputKey;
    }
    this.dataNumAndNextPage.moreThenLimit = true;
    this.nextPageNum = 1;
    await this.assetListPagePaginationTarget();
  }

  /**
   * 入力した検索キーを削除
   */
  @Throttle()
  async clearInputKey() {
    this.clearData();
    this.workflowInputKey = '';
    this.dataNumAndNextPage.keyword = undefined;
    this.nextPageNum = 1;
    await this.assetListPagePaginationTarget();
  }

  /**
   * 分页资产列表数据组装
   */
  async assetListPagePaginationTarget() {
    await this.http.addLoadingQueue();
    try {
      const nextPage = this.nextPageNum - 1;
      // skip 从（开始条数）0或者16是从  例：0～15 16～32 公式：nextPage x getAmountDataNumber
      this.dataNumAndNextPage.skip = nextPage * this.getAmountDataNumber;
      const getAssetListData =
        await this.WorkflowGetApplicationWorkflowListService.getAssetsByKeywordInWfAssetListForAssignScan(
          this.dataNumAndNextPage,
        );
      const wfAssetList = getAssetListData.assetListDatas ?? [];
      // 判断是否取到了数据，并做标记
      if (nextPage === 0) {
        // 初始状态资产
        this.itemsList = wfAssetList;
        this.noData = wfAssetList && wfAssetList.length == 0;
      } else {
        // 下一页资产
        this.itemsList.push(...wfAssetList);
        this.noData = false;
      }
      // 对取到的数据进行整理
      await this.dealData();
      this.totalCount = getAssetListData.allAssetCount;
      this.assetsCount = getAssetListData.allAssetCount;

      let totalPageNumber: number;
      if (_.isEmpty(this.dataNumAndNextPage.keyword)) {
        totalPageNumber = Math.ceil(getAssetListData.allAssetCount / this.getAmountDataNumber);
      } else {
        totalPageNumber = Math.ceil(this.itemsList.length / this.getAmountDataNumber);
      }

      // 当前页数
      const currentPageNumber = Math.ceil(this.itemsList.length / this.getAmountDataNumber);
      this.dataNumAndNextPage.moreThenLimit = wfAssetList.length !== 0 && wfAssetList.length % this.getAmountDataNumber == 0;
      ;
      if (nextPage == 0) {
        this.more = [];
      }
      this.doAfterRequestClearCache(nextPage);
    } finally {
      this.http.removeLoadingQueue();
    }
  }

  async dealData() {
    await this.http.addLoadingQueue();
    try {
      const result = await this.httpAssetService.httpGetItemByAssetType(String(this.assetTypeId));
      this.assetTypeItemMap = result.assetItemList.reduce((map, obj) => {
        map[obj.itemId] = obj;
        // 資産名の表示名を取得
        if (obj.itemName === 'assetName') {
          this.assetNameDisplayName = obj.itemDisplayName;
        }
        // 識別コードの表示名を取得
        if (obj.itemName === 'identityCode') {
          this.identityCodeDisplayName = obj.itemDisplayName;
        }
        return map;
      }, {});

      this.assetColumns = await this.httpGetWorkflowListService.httpGetWorkflowAssetColumn(
        this.workflowId,
        this.processDefinitionId,
      );

      const wfType = this.params.wfType;
      this.initByWfType(this.params);
      if (result.code === 0) {
        const imageTypeArray = result.assetItemList.filter((item) => {
          return item.itemType === 'image' && item.mobileFlg == '1';
        });
        for (const iterator of this.itemsList) {
          for (const it2 of imageTypeArray) {
            let assetTextElement = iterator['assetText'][it2.itemName];
            //修改处理assetText为字符串的情况
            if (typeof assetTextElement === 'string' && assetTextElement !== '') {
              assetTextElement = JSON.parse(assetTextElement);
            }
            if (assetTextElement instanceof Array) {
              const homeImage = assetTextElement.find((item) => {
                return item.isHomeImage === true;
              });
              if (homeImage) {
                // 过滤掉没有权限预览的home画像
                let isView = true;
                let optionObj = JSON.parse(it2.option);
                let sectionPrivateGroups = optionObj.sectionPrivateGroups;
                if (sectionPrivateGroups) {
                  let timeStart = new Date().getTime();
                  isView = await this.loginService.visiblePermissionsCheck(sectionPrivateGroups);
                  console.log('blog--检查是否有权限查看section耗时：', new Date().getTime() - timeStart);
                }
                if (!isView) {
                  //没有权限预览
                  continue;
                }
                let timeStart = new Date().getTime();
                homeImage.turl = await this.getTurl(homeImage.url);
                console.log('blog--获取turl耗时：', new Date().getTime() - timeStart);
                iterator['homeImage'] = homeImage;
                break;
              }
            }
          }
        }
      }

      if (wfType == 'wf2') {
        this.showAssetLocation = false;
      } else {
        this.showAssetLocation = await this.workflowActionsService.showTenantAssetLocation();
      }

      //将temp值设置给assetListDatas，开始渲染列表
      this.itemsList = [...this.itemsList];
      var assetItemSize = 0;
      this.itemsList.forEach((item) => {
        assetItemSize = _.size(item?.assetItemList);
        // 重点重点重点 一旦修改了item也需要修改其他文件，所需要修改的全部文件请参考 -> asset-force/item-needs-to-modify-the-file.txt
        item['assetItems'] = item?.assetItemList;
        if (this.taskType === 'END') {
          item['changeAmount'] =
            item['beforeExecuteValue'] + ' → ' + String(Number(item['beforeExecuteValue']) + Number(item['amount']));
        } else {
          item['changeAmount'] =
            item['amountOfNow'] + ' → ' + String(Number(item['amountOfNow']) + Number(item['amount']));
        }
      });
    } catch (e) {
      console.log(e);
    } finally {
      this.http.removeLoadingQueue();
    }
  }

  // メタルワンかつ資産種類変更しますか
  get isPassedSubProcessWithSubForm() {
    return this.rawFormData.hasSubProcess && this.rawFormData.hasPassedSubProcess;
  }

  initByWfType(params) {
    const typeStr = this.pageAction.wfType;
    this.wfType = {
      isDefault: typeStr == WfPageWidgetService.APPROVAL_ASSETS_TYPE_DEFAULT,
      isNewW3: typeStr == WfPageWidgetService.APPROVAL_ASSETS_TYPE_NEW_W3,
    };
    for (let item of this.itemsList) {
      if (typeof item['assetText'] === 'string') {
        item['assetText'] = JSON.parse(item['assetText']);
      }
    }
  }

  async errorImgLoad(assetItem: Asset) {
    assetItem.homeImage.turl = await this.getTurl(assetItem.homeImage['url']);
  }

  async getTurl(filePath, needLoading = true, delay = true) {
    let resultData = await this.assetDetailService.getTurl(filePath, needLoading, delay);
    return resultData.data.getUrl;
  }

  async getMoreFun(assetData) {
    const getMore = this.more[assetData.assetId];
    if (_.isUndefined(getMore)) {
      this.more[assetData.assetId] = true;
    } else {
      this.more[assetData.assetId] = !getMore;
    }
    // const assetItems:[] = _.isArray(assetData.assetItems) ? assetData.assetItems : []
    // if (!_.isEmpty(assetItems)) {
    //   const assetItemSize = _.size(assetItems);
    //   if (this.more[assetData.assetId]) {
    //     this.itemSize = (assetItemSize + 1) * 45 + (8 + 8 + 10)
    //   } else {
    //     this.itemSize = (assetItemSize + 1) * 45 + (8 + 8 + 10)
    //   }
    // }
  }
}
