import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, NavigationExtras } from '@angular/router';
import { NavController } from '@ionic/angular';
import { AfCustomizeViewComponent } from 'src/app/component/af-customize-view/af-customize-view.component';
import { AssetTypeItem } from 'src/app/models/assetTypeItem';
import { WorkflowGetApplicationWorkflowListService } from 'src/app/services/workflow/get-applicationWorkflowList/workflow-getApplicationWorkflowList.service';
import { WorkflowActionsService } from 'src/app/services/workflow/workflow-actions.service';
import { Throttle } from 'src/app/utils/throttle.decorator';
import { HttpUtilsService } from '../../../../utils/http-utils.service';

@Component({
  selector: 'app-subform',
  templateUrl: './subform.page.html',
  styleUrls: ['./subform.page.scss'],
})
export class SubformPage implements OnInit {
  @ViewChild(AfCustomizeViewComponent, { static: false }) customizeViewComponent: AfCustomizeViewComponent;

  subformId;
  subformVersion;
  processInstanceId;
  assetId;
  taskId;
  barcode;
  readOnly: false;
  assetDict: { [key: string]: AssetTypeItem[] }; // 資産DIC
  firstLoad: boolean = true; // 画面初期ロードフラグ
  confirmData: { assetDict: { [key: string]: AssetTypeItem[] }; comments: any; taskIds: []; wfName: string };
  prePageUrl = '/workflow/approval/w1-asset-list/list';
  title = '';
  constructor(
    private navController: NavController,
    private route: ActivatedRoute,
    private workflowActionsService: WorkflowActionsService,
    private workflowGetApplicationWorkflowListService: WorkflowGetApplicationWorkflowListService,
    private http: HttpUtilsService,
  ) {}

  ngOnInit() {
    this.route.queryParams.subscribe(async (params) => {
      if (this.firstLoad) {
        //リスト画面から
        this.subformId = params['subformId'];
        this.subformVersion = params['subformVersion'];
        this.processInstanceId = params['processInstanceId'];
        this.assetId = params['assetId'];
        this.taskId = params['taskId'];
        this.barcode = params['barcode'];
        this.confirmData = params['confirmData'];
        this.readOnly = params['readOnly'];
        this.title = params['title'] ?? 'サブフォーム';

        this.loadData();
        this.firstLoad = false;
      }
    });
  }

  async loadData() {
    try {
      await this.http.addLoadingQueue();
      let subformLayout = await this.workflowGetApplicationWorkflowListService.getSubformLayout(
        this.subformId,
        this.subformVersion,
      );
      let subformContent = await this.workflowGetApplicationWorkflowListService.getSubFormContentByAsset(
        this.processInstanceId,
        this.assetId,
      );

      this.assetDict = this.workflowActionsService.initFormdata(subformLayout, subformContent, this.readOnly);
    } catch (error) {
      console.log('got error', error);
    } finally {
      await this.http.removeLoadingQueue();
    }
  }

  @Throttle()
  async close() {
    if (this.readOnly) {
      this.navController.navigateBack('/workflow/approval/w1-asset-list/list');
    } else if (this.formChanged) {
      this.save();
    } else {
      this.navController.navigateBack('/workflow/approval/w1-asset-list/list');
    }
  }

  get formChanged() {
    return this.customizeViewComponent.isFormChanged;
  }

  async save() {
    this.workflowActionsService.saveTemporarySubformData(
      this.assetDict,
      this.assetId,
      this.taskId,
      this.customizeViewComponent,
    );
  }

  @Throttle()
  async confirm() {
    if (!this.checkInput()) {
      return;
    }

    // this.backtoApprovalList();
    // return
    await this.workflowActionsService.comfrimWithScanNewW3(
      this.confirmData.assetDict,
      this.confirmData.comments,
      this.processInstanceId,
      this.confirmData.taskIds,
      this.confirmData.wfName,
      this.subformData,
      async (value) => {
        if (!value['subProcessProductAssetList']) {
          this.backtoApprovalList();
          return;
        }
        let navigationExtras: NavigationExtras = {
          queryParams: {
            subform: {
              result: value,
              finishedItemBarcode: this.barcode,
            },
          },
        };

        this.navController.navigateBack('/workflow/approval/w1-asset-list/list', navigationExtras);
      },
    );
  }

  backtoApprovalList() {
    this.http.removeLoadingQueue();
    this.navController.navigateBack('/tabs/workflow/approval', {
      queryParams: {
        name: '承認',
        progress: 'done',
      },
    });
  }

  get subformData() {
    return {
      assetId: this.assetId,
      taskId: this.taskId,
      subformAssetDict: this.assetDict,
    };
  }

  checkInput() {
    if (this.customizeViewComponent != null) {
      let map = this.customizeViewComponent.checkAndGetData();
      if (!map) {
        return false;
      }
    }
    return true;
  }
}
