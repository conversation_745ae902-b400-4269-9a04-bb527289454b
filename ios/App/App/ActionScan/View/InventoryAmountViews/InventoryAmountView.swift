//
//  InventoryAmountView.swift
//  App
//
//  Created by macos on 2022/9/19.
//

import UIKit
@objc protocol DidInventoryAmountViewDelegate {
    
    /// 输入错误情况下弹窗
    func testFieldErrorAlertFun()
    
//    /// ok按钮回调
//    func successBlockFun(assetIDStr: String)
//
//    /// 取消按钮回调
//    func faildBlockFun(assetIDStr: String?)
}

/// 按钮
enum AdditionOrSubtraction {
    // 数字加
    case Addition
    // 数字键
    case Subtraction
    // OK按钮
    case Confirm
    // 取消按钮
    case Cancel
    // 输入
    case UserInput
}
class InventoryAmountView: UIView {
    
    static let shared = InventoryAmountView()
     
    /// 代理，主要用于弹出错误提示
    weak var inventoryAmountDelegate: DidInventoryAmountViewDelegate?
    
    /// 回调函数关键方法（assetAmount用户输入或编辑的数量， stateAdditionOrSubtraction用户操作按钮）
    var addOrReduceToAssetNumBlockAction:((_ assetAmount : String?, _ stateAdditionOrSubtraction: AdditionOrSubtraction)->())?

    /// 資産名固定显示名称
    @IBOutlet weak var assetFixedName: UILabel!
    /// 識別コード固定显示名称
    @IBOutlet weak var barcodeFixedName: UILabel!
    /// 数量固定显示名称
    @IBOutlet weak var amountFixedName: UILabel!
    /// 実際数量固定显示名称
    @IBOutlet weak var actualQuantityFixedName: UILabel!

    /// InventoryAmountView  是否已经被显示在页面上，已显示的话不再次添加到页面
    var isShowed: Bool = false
    
    @IBOutlet weak var assetDisplayValue: UILabel!
    @IBOutlet weak var barcodeDisplayValue: UILabel!
    @IBOutlet weak var amountDisplayValue: UILabel!
    
    @IBOutlet weak var addToAssetNum: PlusMinusButton!
    @IBOutlet weak var enterToAssetNum: UITextField!
    @IBOutlet weak var reduceToAssetNum: PlusMinusButton!
    
    var inventoryAmountView: InventoryAmountView!
    
    @IBOutlet var contentView: UIView!
    
    // 资产ID
//    var getAssetId: String?
    
    class var nibName: String {
        return String(describing: self)
    }
    
    @IBInspectable var inventoryAmountInt: Int = 0 {
        didSet {
            enterToAssetNum.text =  "\(inventoryAmountInt)"
            setNeedsLayout()
        }
    }
    override init(frame: CGRect) {
        super.init(frame: frame)
        self.commonInit()
    }
    
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        self.commonInit()
    }
    
    
    /// 弹出棚卸的 编辑框
    /// - Parameters:
    ///   - scanVC: scanVC description
    ///   - currentVC: currentVC description
    ///   - ARInfo: ARInfo description
    ///   - getAssetAmount: 将要被更新的资产数量
    func pushInventoryAmountView(scanVC: ScanVC, currentVC: UIViewController, ARInfo: ARModel, getAssetAmount: @escaping (_ assetAmount: String)-> Void, cancel: @escaping ()-> Void) {
        if isShowed {
            return
        }
        isShowed = true
        scanVC.stopRunning()
        let window = UIApplication.shared.keyWindow!
        inventoryAmountView = InventoryAmountView.init(frame: CGRect.init(x: 0, y: 0, width: WIDTH, height: HEIGHT))
                window.addSubview(inventoryAmountView)
        inventoryAmountView.addConstraints(superView: currentVC.view, getAsset: ARInfo)
        inventoryAmountView.inventoryAmountInt = ARInfo.count
        inventoryAmountView.inventoryAmountDelegate = currentVC as? any DidInventoryAmountViewDelegate
        inventoryAmountView.addOrReduceToAssetNumBlockAction = {(assetAmount, additionOrSubtraction) in
            if additionOrSubtraction == AdditionOrSubtraction.Cancel || additionOrSubtraction == AdditionOrSubtraction.Confirm {
                self.inventoryAmountView.removeFromSuperview()
                self.isShowed = false
                if Utils.getCurrentViewController() is BridgeActionViewController {
                    scanVC.startRunning()
                }
                if additionOrSubtraction == AdditionOrSubtraction.Cancel {
                    cancel()
                    return
                }
            }
            getAssetAmount(assetAmount ?? "0")
        }
    }
    
    func removeFromView() {
        if inventoryAmountView != nil {
            inventoryAmountView.removeFromSuperview()
            self.isShowed = false
        }
    }
    
    /// 初始化XIB
    private func commonInit() {
        // 虚化背景
        self.backgroundColor = UIColor.colorWithHexString("#000000", alpha: 0.4)
        Bundle.main.loadNibNamed(type(of: self).nibName, owner: self, options: nil)
        addSubview(contentView)
        contentView.frame = self.bounds
        contentView.autoresizingMask = [.flexibleHeight, .flexibleWidth]
        // 调整按钮图片的显示尺寸
        addToAssetNum.imageEdgeInsets = UIEdgeInsets(top: 15, left: 15, bottom: 15, right: 15)
        reduceToAssetNum.imageEdgeInsets = UIEdgeInsets(top: 21.5, left: 15, bottom: 21.5, right: 15)
        // 调整输入框border
        enterToAssetNum.delegate = self
        enterToAssetNum.layer.borderWidth = 1.0
        enterToAssetNum.layer.borderColor = UIColor.colorWithHexString("#DFE4EC").cgColor
        enterToAssetNum.layer.cornerRadius = 8.0
    }
    
    /// 界面约束以及赋值
    /// - Parameters:
    ///   - superView: 父类View
    ///   - getAsset: 赋值数据
    func addConstraints(superView: UIView, getAsset: ARModel) {
        contentView.translatesAutoresizingMaskIntoConstraints = false
        // Y轴居中
        contentView.centerYAnchor.constraint(equalTo: self.centerYAnchor).isActive = true
        // 左端约束
        contentView.leadingAnchor.constraint(equalTo: self.leadingAnchor, constant: 48.0).isActive = true
        // 右端约束
        contentView.trailingAnchor.constraint(equalTo: self.trailingAnchor, constant: -48.0).isActive = true
        // 设置圆角
        let tap = UITapGestureRecognizer.init(target: self, action: #selector(InventoryAmountView.tapView))
        self.addGestureRecognizer(tap)
//        contentView.cornerCut(radius: 15, corner: .allCorners)
        contentView.layer.cornerRadius = 15
//        // 减按钮圆角
//        reduceToAssetNum.cornerCut(radius: 9, corner: [.topLeft, .bottomLeft])
//        // 加按钮圆角
//        addToAssetNum.cornerCut(radius: 9, corner: [.topRight, .bottomRight])
        // 取到资产名字并赋值显示
        assetDisplayValue.text = getAsset.assetName
        // 取到识别code并赋值显示
        barcodeDisplayValue.text = getAsset.barCode
        
        // 获取数量
        let quantity = getAsset.quantity
       
        amountDisplayValue.text = (String(quantity))
//        if getAsset["assetId"] is String {
//            getAssetId = getAsset["assetId"] as? String ?? ""
//        } else {
//            if getAsset["assetId"] is Int {
//                getAssetId = "\(String(getAsset["assetId"] as! Int))"
//            }
//        }
    }
    
    /// 数字减
    /// - Parameter sender: 减按钮
    @IBAction func reduceToAssetNumFun(_ sender: UIButton) {
        hideKeyboard()
        enterToAssetNum.text = simpleArithmetic(pinStr: enterToAssetNum.text, additionOrSubtraction: .Subtraction)
    }
    
    /// 数字加
    /// - Parameter sender: 加按钮
    @IBAction func addToAssetNumFun(_ sender: UIButton) {
        hideKeyboard()
        enterToAssetNum.text = simpleArithmetic(pinStr: enterToAssetNum.text, additionOrSubtraction: .Addition)
    }
    
    /// 输入的内容获取
    /// - Parameter sender: 输入
    @IBAction func testFieldDidEndAction(_ sender: UITextField) {
        let textSen = sender.text
        let isVerify = self.verifyInputText(pinStr: textSen)
        if isVerify {
            // 如果需要输入框内的数字和黑色区域内按钮边缘的数字联动需要打开下面注释
//            addOrReduceToAssetNumBlockAction?(textSen, .UserInput)
        }
    }
    
    /// ok按钮触发
    /// - Parameter sender: ok按钮
    @IBAction func okBtnAction(_ sender: UIButton) {
        hideKeyboard()
        let textSen = enterToAssetNum.text
        let isVerify = self.verifyInputText(pinStr: textSen)
        if isVerify {
            addOrReduceToAssetNumBlockAction?(textSen, .Confirm)
            return
        }
        // 触发父类（Controller层）弹出错误提示，弹窗只能写在Controller层
        inventoryAmountDelegate?.testFieldErrorAlertFun()
    }
    
    /// 取消按钮触发方法
    /// - Parameter sender: 取消按钮
    @IBAction func cancelBtnAction(_ sender: UIButton) {
        hideKeyboard()
        // 点击取消需要把原来的值赋回去
        addOrReduceToAssetNumBlockAction?(String(inventoryAmountInt), .Cancel)
    }
    
    /// 校验输入内容
    /// - Parameter pinStr: 输入的内容
    /// - Returns: true 输入正确 false输入错误
    private func verifyInputText(pinStr: String?) -> Bool {
        if pinStr == nil || pinStr == "" {
            return false
        }
        let match: String = "(^[0]|[1-9]\\d*$)"
        let predicate = NSPredicate(format: "SELF matches %@", match)
        let isChi = predicate.evaluate(with: pinStr)
//        如果需要在输入框下面显示输入错误文言请打开下面注释
//        if isChi {
//            errorLable.isHidden = true
//        } else {
//            errorLable.isHidden = false
//        }
        return isChi
    }
    
    /// 加 / 减 / 输入 集成方法
    /// - Parameter pinStr: 数字
    /// - Returns: 算好后的数字
    private func simpleArithmetic(pinStr: String?, additionOrSubtraction: AdditionOrSubtraction) -> String? {
        var clonePinStr = pinStr
        if clonePinStr == nil || clonePinStr == "" {
            clonePinStr = "0"
        }
        let isVerify = self.verifyInputText(pinStr: clonePinStr)
        if isVerify || clonePinStr == "0" {
            var textSen = Int(clonePinStr!)!
            if additionOrSubtraction == .Addition {
                textSen += 1
            }
            if additionOrSubtraction == .Subtraction && textSen >= 1 {
                textSen -= 1
            }
            clonePinStr = String(textSen)
        }
        // 如果需要输入框内的数字和黑色区域内按钮边缘的数字联动需要打开下面注释
//        addOrReduceToAssetNumBlockAction?(clonePinStr, additionOrSubtraction)
        return clonePinStr
    }
    
    /// 键盘收回方法
    private func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
    @objc func tapView() {
        hideKeyboard()
    }
}

extension UIView {
    
    /// 切圆角共通
    /// - Parameters:
    ///   - radius: 切圆角的半径
    ///   - corner: 要切四个角中的哪个角
    func cornerCut(radius:Int,corner:UIRectCorner){
        let maskPath = UIBezierPath.init(roundedRect: bounds, byRoundingCorners: corner, cornerRadii: CGSize.init(width: radius, height: radius))
        let maskLayer = CAShapeLayer()
        maskLayer.frame = bounds
        maskLayer.path = maskPath.cgPath
        layer.mask = maskLayer
    }
}
extension InventoryAmountView: UITextFieldDelegate {

    // 当获取到焦点的同时全选文字
    func textFieldDidBeginEditing(_ textField: UITextField) {
        DispatchQueue.main.async {
            textField.selectedTextRange = textField.textRange(from: textField.beginningOfDocument, to: textField.endOfDocument)
        }
    }
    
    /// 最大数字限制( 暂时不作限制 )
    /// - Parameter textField: textField description
//    func textFieldDidChangeSelection(_ textField: UITextField) {
//        guard var testelt = textField.text else { return }
//        // 最大输入（999999）
//        let maxTesteltLength = 6
//        if testelt.count > maxTesteltLength {
//            // 最大文字数超えた場合は切り捨て
//            testelt = String(testelt.prefix(maxTesteltLength))
//        }
//        // 文字列から全角半角スペースを取り除く
//        textField.text = testelt.removingWhiteSpace()
//    }
}
extension String {

    func removingWhiteSpace() -> String {
        let whiteSpaces: CharacterSet = [" ", "　"]
        return self.trimmingCharacters(in: whiteSpaces)
    }
}
