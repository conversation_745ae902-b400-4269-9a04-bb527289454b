import { Injectable } from '@angular/core';
import { NavigationExtras } from '@angular/router';
import { ActionSheetController, AlertController, ModalController, NavController } from '@ionic/angular';
import { ScanService } from 'src/app/services/scan/scan.service';
import { MypageGetuserinfoService } from 'src/app/services/mypage/get-userInfo/mypage-getuserinfo.service';
import { StorageUtils } from 'src/app/utils/storage-utils';
import { LocationSelectPage } from 'src/app/location-select/location-select.page';
import { ActionService } from 'src/app/services/action/action-service.service';
import * as _ from 'lodash';
import { TabsPage } from 'src/app/tabs/tabs.page';
import { OcrService, OcrType } from 'src/app/ocr/ocr.service';
import { Store } from '@ngxs/store';
import { AMOUNT_TYPE, AssetAction } from 'src/app/models/assetAction';
import { environment } from 'src/environments/environment';
import { SetNewAndSaveActionBasicPropertiesADD } from 'src/app/services/action/action-service.actions';
import { AssetService } from 'src/app/services/asset/asset.service';
import { MemCache } from 'src/app/utils/MemCache';
import { OfflineCheckerService, OfflineCheckerType } from '../offline-checker.service';
import { MmkvService } from '../../mmkv/mmkv.service';

export interface ScanInterface {
  scanType: 'ocr' | 'barCode';
  ocrType: OcrType;
  isFromRegisteredListPage: boolean;
  assetActionModel: AssetAction;
  registeredAssetList: any[]; // 上一次扫描数据
  unRegisteredAssetList: any[]; // 上一次扫描失败数据
  actionLabel;
  tempActionListType;
  isNewAction: boolean; // 是否新规处理设定
  isFromHome: boolean;
  // 【onlineMode】0 -> 無効、1 -> 有効（選択式）、2 -> 有効（固定）
  onlineMode: 0 | 1 | 2;
  isFromLocationSettingPage: boolean;
  isFromTakeMiddleButtonPage: boolean;
  searchKey: string;
}

export interface AssetInfoByAssetIdsApiInterface {
  msg: string;
  code: number;
  assetList: AssetInfoInterface[];
}

export interface AssetInfoInterface {
  assetItemList: AssetItemInfoInterface[];
  assetTypeId: number;
  assetId: number;
  assetName: string;
  assetTypeName: string;
  barcode: string;
  assetScanedCount?: number;
  homeImageUrl?: string;
  homeImage?: {
    turl: string;
    url: string;
    fileName: string;
    homeImgLoaded: boolean;
  };
}

export interface AssetItemInfoInterface {
  itemId: number;
  itemDisplayName: string;
  itemType: string;
  value: string;
}

export interface MyRoleInterface {
  userName: string;
  userId: number;
  roleId: number;
  tenantId: string;
  mainFlg: string;
  createdById: string;
  createdDate: string;
  modifiedById: string;
  modifiedDate: string;
  roleName: string;
  mfaFlg: string;
}

export interface MiddleButtonDataInterface {
  assetDetail: AssetInfoInterface[];
  userRoleList: MyRoleInterface[];
  barCode: string[];
  showARInfo: any[];
  unregisterBarcodeList: any[];
}

export interface ScanDataInfoInterface {
  unregisterBarcodeList: any[];
  type?: string;
  scanType: string;
  showARInfo: any[];
}

// 定义页面路径常量
const PROGRESS_LIST_PAGE = '/tabs/action/progress/list';
const CREATE_OR_SAVE_TEMPORARILY_PAGE = '/action/create-or-save-temporarily';
const PREVIEW_INSIDE_PAGE = '/action/list/preview-inside';

/**
 * 中间按钮以及处理设定扫描核心处理类
 */
@Injectable({
  providedIn: 'root',
})
export class AddAssetScanStartServiceService {
  constructor(
    private actionService: ActionService,
    private scanService: ScanService,
    private modalController: ModalController,
    private actionSheetController: ActionSheetController,
    private getinfoService: MypageGetuserinfoService,
    private ocrService: OcrService,
    private store: Store,
    private navController: NavController,
    private assetService: AssetService,
    private offlineChecker: OfflineCheckerService,
    private mmkv: MmkvService,
    private alertController: AlertController,
  ) {}

  /**
   * LayoutSetting接口调用取得数据
   * @returns Map<string, string>
   */
  public getCustomItem = async (): Promise<Map<string, string>> => {
    let dynamicItemDic = new Map();
    const result = await this.actionService.actionGetCustomItem();
    if (result.code === 0) {
      const arr = result.layoutSettingList;
      for (const item of arr) {
        dynamicItemDic[String(item.itemName)] = String(item.itemId);
      }
    }
    return dynamicItemDic;
  };

  public getAssetDataForAction = async (actionid, assetids: number[]): Promise<any> => {
    const result = await this.actionService.getAssetDataForAction(actionid, assetids);
    if (result && result.code === 0) {
      return result.assets;
    } else {
      return [];
    }
  };

  /**
   * 中间按钮扫描
   * @param cxy 数据
   * @returns bool
   */
  public scanStart = async (cxy: {
    data: ScanDataInfoInterface;
    isFromNewActionListPage: boolean;
  }): Promise<boolean> => {
    const { data, isFromNewActionListPage } = cxy;

    const userRoleList = await this.getinfoService.getLatestUserRole();
    const userName = await StorageUtils.get(StorageUtils.KEY_USER_NAME);
    const locationInfo = await StorageUtils.getLocation(userName);
    let scanData: any = {
      userRoleList: userRoleList,
      canActionScan: true,
    };
    if (_.isEmpty(data)) {
      scanData = {
        ...scanData,
        isFromLocationSettingPage: false,
        scanType: '',
        locationInfo: locationInfo,
      };
    } else {
      scanData = {
        ...scanData,
        ...data,
        locationInfo: locationInfo,
      };
    }
    let toNavAssetScanData: any = {};
    try {
      toNavAssetScanData = await this.scanService.scanBacode(scanData);
    } catch (error) {
      console.error('错误', error);
      return false;
    }

    console.log('中间按钮回调+++++++++++++++++++++++++++++===> ', toNavAssetScanData);
    // 从原生点击悬浮框的qrcode进入资产详细
    if (toNavAssetScanData.type === '1') {
      const extra = {
        ...toNavAssetScanData,
        // assetInfo里面的东西当再一次返回扫描页面时候原生需要
        ...toNavAssetScanData['assetInfo'],
        // 抛弃多余的值
        assetInfo: {},
        isFromMiddleButton: true,
        handled: true,
      };
      let level = Date.now();
      const navigationExtras: NavigationExtras = {
        queryParams: {
          assetId: extra['assetId'],
          assetTypeId: extra['assetTypeId'],
          fromScanPage: 'assetScan',
          isFromMiddleButton: extra['isFromMiddleButton'],
          backUrl: 'tabs/' + TabsPage.lastTabHomePath,
          extra: extra,
          level: level,
        },
      };
      this.navController.navigateForward('/tabs/asset/detail/' + level, navigationExtras);
      return false;
    }
    // 场所切换
    if (toNavAssetScanData.type === '5') {
      try {
        const modal = await this.modalController.create({
          component: LocationSelectPage,
          componentProps: {
            isPlusBtn: 'true',
          },
        });
        await modal.present();
        const wd = await modal.onWillDismiss();
        const itiData = wd?.data ?? '';
        const iti = itiData['location'] ?? '';
        await StorageUtils.set(userName, iti);
        cxy.data = {
          ...toNavAssetScanData,
          isFromLocationSettingPage: true,
        };
        console.log('切换场所+++++++++++++++++++++++++===> ', cxy?.data);
        return await this.scanStart(cxy);
      } catch (err) {
        console.error('切换场所弹窗失败');
      }
      return false;
    }
    // 用户用中间按钮扫描了处理设定二维码
    if (toNavAssetScanData.type === '6') {
      // 处理设定类型
      if (_.isEmpty(toNavAssetScanData.assetAction)) {
        return false;
      }
      let assetActionObj = undefined;
      try {
        assetActionObj = JSON.parse(toNavAssetScanData.assetAction);
      } catch (error) {
        console.error('json 转换失败');
        return false;
      }
      const dynamicItemDic = await this.getCustomItem();
      // 迁移到处理设定
      const clickNextStepData = {
        data: assetActionObj, // native 返回来的信息.
        registeredAssetList: [],
        unRegisteredAssetList: [],
        dynamicItemDic: dynamicItemDic, // network取的信息.
        actionLabel: assetActionObj['actionLabel'], // native 信息中取出来的一部分.
        tempActionListType: 1,
        isNewAction: true,
        isFromHome: false,
        isFromLocationSettingPage: false,
        isFromTakeMiddleButtonPage: true,
        searchKey: '',
        isNeedJump: true,
        pagePath: '',
      };
      // 跳转到处理设定扫描启动
      await this.scanStartNextStep(clickNextStepData);
      // TODO 处理设定逻辑
      return false;
    }

    // 未登陆资产，创建资产
    if (toNavAssetScanData.type === '0') {
      toNavAssetScanData = {
        ...toNavAssetScanData['assetInfo'],
        type: '0',
      };
    }

    // 扫描完成
    const reassemblyDataInfo: {
      barCode: string[];
      assetDetail: AssetInfoInterface[];
      userRoleList: MyRoleInterface[];
      showARInfo: any[];
      unregisterBarcodeList: any[];
    } = {
      assetDetail: [],
      barCode: [],
      // 用户组
      userRoleList: userRoleList ?? [],
      // 未登陆的资产
      // 上一次扫描的原生需要的数据
      showARInfo: toNavAssetScanData['showARInfo'] ?? [],
      unregisterBarcodeList: toNavAssetScanData['unregisterBarcodeList'] ?? [],
    };

    if (reassemblyDataInfo.showARInfo.length > 0) {
      let assetIds = reassemblyDataInfo.showARInfo.map((asi) => _.toNumber(asi.assetId));
      const getAssetInfoList = await this.assetService.getAssetInfoByAssetIds(assetIds);
      // 以登陆的资产
      reassemblyDataInfo.assetDetail = getAssetInfoList?.assetList ?? [];
    }
    if (reassemblyDataInfo.unregisterBarcodeList.length > 0) {
      // 未登陆资产
      reassemblyDataInfo.barCode = reassemblyDataInfo['unregisterBarcodeList'].map((ub) =>
        ub['scanTechnical'] == 'RFIDScan' ? ub['rfid'] : ub['barCode'],
      );
    }
    await StorageUtils.set(StorageUtils.KEY_DATA_FROM_SCAN, reassemblyDataInfo);
    // 未登陆资产，创建资产跳转
    if (toNavAssetScanData.type === '0') {
      console.log('未登陆资产 ionic +++++++++++++++++++++++++===> ', reassemblyDataInfo);
      this.navController.navigateForward('/add/new/list');
      return false;
    }
    // type === 2
    return true;
  };

  /**
   * 处理设定扫描，开启哪一个扫描先行判断
   * @param clickNextStepData
   * @param callBackAction
   * @param reScan 重启扫描
   * @returns
   */
  public scanStartNextStep = async (
    clickNextStepData: {
      data: AssetAction;
      registeredAssetList: any[];
      unRegisteredAssetList: any[];
      actionLabel: string; // 处理设定组名称
      tempActionListType: number; // 1所有处理一览，2处理设定组
      isNewAction: boolean; // 是否新规处理设定
      isFromHome: boolean; // 是否从home页面点进
      isFromLocationSettingPage: boolean; // 是否切换过场所
      isFromTakeMiddleButtonPage: boolean; // 是否从中间按钮进入
      isNeedJump: boolean; // true 可以迁移到扫描济列表， false 不做跳转处理（主要用作处理设定list页面继续扫描添加不跳转）callBackAction 配合使用
      searchKey: string; // 处理设定页面搜索条件
      pagePath: string; // 当前页面路径
    },
    callBackAction?: () => void,
    reScan: boolean = false,
  ): Promise<void> => {
    const {
      data,
      registeredAssetList,
      unRegisteredAssetList,
      actionLabel,
      tempActionListType,
      isNewAction,
      isFromHome,
      isFromLocationSettingPage,
      isFromTakeMiddleButtonPage,
      isNeedJump,
      searchKey,
      pagePath,
    } = clickNextStepData;

    console.log('blog-scanStartNextStep');
    const assetActionModel = data;
    await StorageUtils.set(StorageUtils.KEY_ASSET_ACTION_MODEL, assetActionModel);
    const tenantId = await StorageUtils.get(StorageUtils.KEY_TENANT_ID);
    const textOcrTargetTenant = environment.textOcrTargetTenant ?? [];
    const universalSerialOcrTargetTenant = environment.universalSerialOcrTargetTenant ?? [];
    const carNumberOcrTargetTenant = environment.carNumberOcrTargetTenant ?? [];
    let npRegisteredAssetList = registeredAssetList;

    // 一时保存 并且 不是扫描济列表页面
    if (!isNewAction && isNeedJump) {
      // 一时保存 从后台取出上一次扫描数据
      const { assets } = await this.actionService.getSelectedAssetList(assetActionModel.processId);
      console.log('一时保存-api-获取既存数据', assets, npRegisteredAssetList);
      // 将npRegisteredAssetList与assets中assetId不相同的添加进去，全部遍历一下
      // 使用 Set 来存储已注册资产的 assetId，提高查找效率
      if (reScan) {
        const registeredAssetIds = new Set(npRegisteredAssetList.map((asset) => _.toString(asset.assetId)));

        for (let asset of assets) {
          if (!registeredAssetIds.has(_.toString(asset.assetId))) {
            npRegisteredAssetList.push(asset);
            registeredAssetIds.add(_.toString(asset.assetId)); // 更新 Set 以保持同步
          }
        }
      } else {
        npRegisteredAssetList = assets;
      }
      // 重置非法扫描数量
      if (npRegisteredAssetList) {
        for (let registeredAsset of npRegisteredAssetList) {
          if (registeredAsset['count'] == null || registeredAsset['count'] == undefined) {
            registeredAsset['count'] = 1;
          }
        }
      }
    }

    const scanData: ScanInterface = {
      scanType: 'barCode',
      ocrType: undefined,
      isFromRegisteredListPage: false,
      assetActionModel: data,
      registeredAssetList: npRegisteredAssetList,
      unRegisteredAssetList: unRegisteredAssetList,
      actionLabel,
      tempActionListType,
      isNewAction,
      isFromHome,
      isFromLocationSettingPage,
      isFromTakeMiddleButtonPage,
      searchKey,
      onlineMode: data['onlineMode'],
    };

    // 棚卸指定扫描
    if (_.isEqual(AMOUNT_TYPE.INVENTORY_AMOUNT_TYPE, _.toString(assetActionModel.amountType))) {
      // 处理设定 新规 棚卸
      if (isNewAction && isNeedJump) {
        await this.actionService.goInventoryAmountType(assetActionModel, async () => {
          await this.actionToBarcodeScan(isNeedJump, scanData, callBackAction);
        });
        return;
      }
      // 处理设定 一时保存 list 页面再继续扫描 棚卸
      await this.actionToBarcodeScan(isNeedJump, scanData, callBackAction);
      return;
    }

    // 0: OfflineCheckerType.ONLY_ONLINE,
    // 1: OfflineCheckerType.USER_SELECTION,
    // 2: OfflineCheckerType.ONLY_OFFLINE,
    const scanMode: OfflineCheckerType = _.toNumber(data['onlineMode']) as OfflineCheckerType;
    // 是离线扫描或者可选扫描
    const isNotOnlineMode = [OfflineCheckerType.USER_SELECTION, OfflineCheckerType.ONLY_OFFLINE].includes(scanMode);
    // 是否为指定页面（指定页面则不进入离线扫描或在线离线选择式扫描）
    const isNotOnlineListPage = ![PREVIEW_INSIDE_PAGE, CREATE_OR_SAVE_TEMPORARILY_PAGE, PROGRESS_LIST_PAGE].includes(
      pagePath,
    );
    if (isNotOnlineMode && isNotOnlineListPage) {
      const isOnline = await this.barcodeScanProcess(clickNextStepData, isNeedJump, scanData, callBackAction);
      if (!isOnline) {
        return;
      }
    }

    // 常规默认扫描
    const actionSheetList: any[] = [
      {
        text: 'バーコード / QRコード / RFID',
        handler: () => {
          this.actionToBarcodeScan(isNeedJump, scanData, callBackAction);
        },
      },
    ];
    // 车牌号识别
    if (carNumberOcrTargetTenant.includes(tenantId)) {
      actionSheetList.push({
        text: 'AI OCR（ナンバープレート）',
        handler: () => {
          scanData.ocrType = OcrType.CarNumberOcr;
          this.actionToOcrScan(isNeedJump, scanData, callBackAction);
        },
      });
    }
    // 由于安卓没有实装此功能，如果后期安卓完善此功能后可以把这个判断删除
    if (!StorageUtils.platform.is('android')) {
      // WF用的数字识别
      if (textOcrTargetTenant.includes(tenantId)) {
        actionSheetList.push({
          text: 'AI OCR（資産番号）',
          handler: () => {
            scanData.ocrType = OcrType.SerialNumberOcr;
            this.actionToOcrScan(isNeedJump, scanData, callBackAction);
          },
        });
      }
      // 处理设定用的数字识别
      if (universalSerialOcrTargetTenant.includes(tenantId)) {
        actionSheetList.push({
          text: 'AI OCR（AI用識別コード）',
          handler: async () => {
            scanData.scanType = 'ocr';
            this.actionToBarcodeScan(isNeedJump, scanData, callBackAction);
          },
        });
      }
    }

    // 如果 不包含 AIOCR 扫描 则 直接启动 バーコード / QRコード / RFID 扫描
    if (actionSheetList.length === 1) {
      this.actionToBarcodeScan(isNeedJump, scanData, callBackAction);
      return;
    }
    // 扫描取消按钮
    actionSheetList.push({
      text: 'キャンセル',
      role: 'cancel',
    });

    // 权限取得，根据不同的 Tenant 可启动的 扫描功能也不同，多判断AIOCR
    const ocrTenant = [...textOcrTargetTenant, ...carNumberOcrTargetTenant, ...universalSerialOcrTargetTenant];
    if (ocrTenant.includes(tenantId)) {
      const actionSheet = await this.actionSheetController.create({
        header: '読取りモードの選択',
        buttons: actionSheetList,
      });
      await actionSheet.present();
    } else {
      // 没有权限直接启动 默认扫描
      this.actionToBarcodeScan(isNeedJump, scanData, callBackAction);
    }
  };

  private barcodeScanProcess(
    clickNextStepData: {
      data: AssetAction;
      registeredAssetList: any[];
      unRegisteredAssetList: any[];
      actionLabel: string;
      tempActionListType: number;
      isNewAction: boolean;
      isFromHome: boolean;
      isFromLocationSettingPage: boolean;
      isFromTakeMiddleButtonPage: boolean;
      isNeedJump: boolean;
      searchKey: string;
    },
    isNeedJump: boolean,
    scanData: ScanInterface,
    callBackAction: () => void,
  ): Promise<boolean> {
    console.log(clickNextStepData, scanData);
    const onlineModes = {
      0: OfflineCheckerType.ONLY_ONLINE,
      1: OfflineCheckerType.USER_SELECTION,
      2: OfflineCheckerType.ONLY_OFFLINE,
    };
    const offlineMode = scanData?.onlineMode
      ? onlineModes[scanData.onlineMode] || OfflineCheckerType.ONLY_ONLINE
      : OfflineCheckerType.ONLY_ONLINE;
    const nextDataPayload = JSON.stringify({
      nextData: clickNextStepData,
      scanData: scanData,
    });
    return new Promise(async (resolve, _) => {
      await this.offlineChecker.doProcess(offlineMode, nextDataPayload, async (isOnline, data, timestamp) => {
        if (!isOnline) {
          const action = await this.actionToBarcodeScanOffline;
          action.call(this, isNeedJump, scanData, timestamp, callBackAction);
        }
        resolve(isOnline);
      });
    });
  }

  /**
   * 正常BarCode扫描
   */
  private actionToBarcodeScan = async (
    isNeedJump: boolean,
    scanData: ScanInterface,
    callBackAction: () => void,
  ): Promise<void> => {
    const {
      scanType,
      isFromRegisteredListPage,
      assetActionModel,
      registeredAssetList,
      unRegisteredAssetList,
      actionLabel,
      tempActionListType,
      isNewAction,
      isFromHome,
      isFromTakeMiddleButtonPage,
      searchKey,
      ocrType,
    } = scanData;
    /// ユーザーロールリストをとる
    const [userRoleList, userName, quantityCountScanData] = await Promise.all([
      this.getinfoService.getLatestUserRole(),
      StorageUtils.get(StorageUtils.KEY_USER_NAME),
      StorageUtils.get(StorageUtils.IS_NUMBER_NEED_AUTO_INCREAS),
    ]);
    const isQuantityCountScan = quantityCountScanData['isQuantityCountScan'] ?? false;
    // 切换场所后会被写入
    const locationInfo = await StorageUtils.getLocation(userName);

    const dataScan = {
      assetActionModel: assetActionModel,
      id: assetActionModel.assetActionId.toString(),
      userRoleList: userRoleList,
      locationInfo: locationInfo,
      scanType: scanType,
      showARInfo: registeredAssetList,
      unRegisteredAssetList: unRegisteredAssetList,
      isFromActionListPage: !isNeedJump,
      assetTypeId: String(assetActionModel.assetTypeId), //更新处理ocr扫描用
      isFromRegisteredListPage: isFromRegisteredListPage,
      isNumberNeedToBeAutoIncreas: isQuantityCountScan,
    };

    console.log('blog-actionToBarcodeScan');
    // ジャンプ処理設定スキャン画面
    this.scanService.actionScanBacode(dataScan).then(async (data) => {
      console.log(
        'asset-force/src/app/action/list/list/list.page.ts ====> this.scanService.actionScanBacode 处理设定新规&一时保存',
        data,
      );
      // 当棚卸情况下返回close则关闭扫描并且页面迁移到一时保存
      if (data['close']) {
        // 非扫描济列表页面 和 非确认列表页面（isNeedJump === false）并且 属于棚卸
        if (isNeedJump && _.isEqual(AMOUNT_TYPE.INVENTORY_AMOUNT_TYPE, _.toString(assetActionModel.amountType))) {
          this.navController.navigateForward('/tabs/action/progress/list');
        }
        // 其他情况
        return;
      }

      await this.doActionScanProgress(
        isNeedJump,
        {
          data: data,
          assetActionModel: assetActionModel,
          actionLabel,
          tempActionListType,
          scanType: scanType,
          isNewAction,
          isFromHome,
          isFromTakeMiddleButtonPage,
          searchKey,
          ocrType,
          scanMode: 'online',
          timestamp: '',
        },
        callBackAction,
      );
    });
  };

  /**
   * 离线情况下的正常BarCode扫描
   * @param isNeedJump
   * @param scanData
   * @param timeStamp 时间戳
   * @param callBackAction
   * @returns {Promise<void>}
   */
  public actionToBarcodeScanOffline = async (
    isNeedJump: boolean,
    scanData: ScanInterface,
    timeStamp: string,
    callBackAction?: () => void,
  ): Promise<void> => {
    console.log('actionToBarcodeScanOffline', isNeedJump, timeStamp);
    const {
      scanType,
      isFromRegisteredListPage,
      assetActionModel,
      registeredAssetList,
      unRegisteredAssetList,
      actionLabel,
      tempActionListType,
      isNewAction,
      isFromHome,
      isFromTakeMiddleButtonPage,
      searchKey,
      ocrType,
    } = scanData;
    const [userName, quantityCountScanData] = await Promise.all([
      StorageUtils.get(StorageUtils.KEY_USER_NAME),
      StorageUtils.get(StorageUtils.IS_NUMBER_NEED_AUTO_INCREAS),
    ]);
    const isQuantityCountScan = quantityCountScanData['isQuantityCountScan'] ?? false;
    // 切换场所后会被写入
    const locationInfo = await StorageUtils.getLocation(userName);

    const dataScan = {
      assetActionModel: assetActionModel,
      id: assetActionModel.assetActionId.toString(),
      locationInfo: locationInfo,
      scanType: scanType,
      showARInfo: registeredAssetList,
      unRegisteredAssetList: unRegisteredAssetList,
      isFromActionListPage: !isNeedJump,
      assetTypeId: String(assetActionModel.assetTypeId), //更新处理ocr扫描用
      isFromRegisteredListPage: isFromRegisteredListPage,
      isNumberNeedToBeAutoIncreas: isQuantityCountScan,
      scanMode: 'offline',
      timestamp: timeStamp,
    };

    console.log('blog-actionToBarcodeScan');
    // ジャンプ処理設定スキャン画面
    await this.scanService.actionScanBacode(dataScan).then(async (data) => {
      console.log(
        'asset-force/src/app/action/list/list/list.page.ts ====> this.scanService.actionScanBacode 处理设定新规&一时保存',
        data,
      );
      // 当离线扫描情况下返回close则关闭扫描并且页面迁移到离线一时保存
      if (data['close']) {
        const navigationExtras: NavigationExtras = {
          queryParams: { backUrl: '/tabs/task' },
        };
        this.navController.navigateForward('/tabs/action/offline-progress', navigationExtras);
        return;
      }

      await this.doActionScanProgress(
        isNeedJump,
        {
          data: data,
          assetActionModel: assetActionModel,
          actionLabel,
          tempActionListType,
          scanType: scanType,
          isNewAction,
          isFromHome,
          isFromTakeMiddleButtonPage,
          searchKey,
          ocrType,
          scanMode: 'offline',
          timestamp: timeStamp,
        },
        callBackAction,
      );
    });
  };

  /**
   * OCR扫描
   * @description:OCRスキャン機能
   * @param {type}
   */
  private actionToOcrScan = async (
    isNeedJump: boolean,
    scanData: ScanInterface,
    callBackAction: () => void,
  ): Promise<void> => {
    const {
      scanType,
      isFromRegisteredListPage,
      assetActionModel,
      registeredAssetList,
      actionLabel,
      tempActionListType,
      isNewAction,
      isFromHome,
      isFromTakeMiddleButtonPage,
      searchKey,
      ocrType,
    } = scanData;

    if (registeredAssetList) {
      for (const index in registeredAssetList) {
        registeredAssetList[index]['isShowCell'] = false;
      }
    }
    const isScannedData = registeredAssetList && registeredAssetList.length > 0;

    const actionId = assetActionModel.assetActionId;

    const aiocr = await this.ocrService.openOcrScanModal(ocrType, actionId, isScannedData);
    console.log('处理设定OCR返回参数 ==========> ', aiocr, isScannedData);

    if (_.isEmpty(aiocr)) {
      return;
    }

    const scannedList = registeredAssetList ?? [];
    let inRuleList = [...scannedList];
    for (const newItem of aiocr.inRule) {
      if (scannedList.findIndex((scannedItem) => scannedItem.assetId === newItem.assetId) < 0) {
        inRuleList.push(newItem);
      }
    }

    const data: {
      type: string;
      showARInfo: any[];
      scanType: string;
    } = {
      type: '2',
      scanType: 'ocr',
      showARInfo: inRuleList,
    };

    await this.doActionScanProgress(
      isNeedJump,
      {
        data: data,
        assetActionModel: assetActionModel,
        actionLabel,
        tempActionListType,
        scanType: scanType,
        isNewAction,
        isFromHome,
        isFromTakeMiddleButtonPage,
        searchKey,
        ocrType,
        scanMode: 'online',
        timestamp: '',
      },
      callBackAction,
    );
  };

  /**
   * 处理设定扫描后的数据处理核心方法
   * @param isNeedJump 是否需要跳转
   * @param actionScan 处理设定扫描的数据
   * @param callBackAction 数据处理完回调函数
   * @returns void
   */
  private doActionScanProgress = async (
    isNeedJump: boolean,
    actionScan: {
      data;
      assetActionModel;
      actionLabel;
      tempActionListType;
      scanType: 'ocr' | 'barCode' | 'action' | 'barcode';
      isNewAction: boolean; // 是否新规处理设定
      isFromHome: boolean; // 从home页面直接进入（只可能是新规）,
      isFromTakeMiddleButtonPage: boolean;
      searchKey;
      ocrType;
      scanMode: 'offline' | 'online';
      timestamp: string;
    },
    callBackAction: () => void,
  ): Promise<void> => {
    const {
      data,
      assetActionModel,
      actionLabel,
      tempActionListType,
      scanType,
      isNewAction,
      isFromHome,
      isFromTakeMiddleButtonPage,
      searchKey,
      ocrType,
      scanMode,
      timestamp,
    } = actionScan;

    /**
     * 重新进入扫描视图的功能函数
     * 该函数主要用于在用户重新进入扫描页面时，根据之前的数据状态重新启动扫描过程
     * 它会根据来源页面和扫描类型等信息，构造一个新的扫描数据对象，并调用扫描函数
     */
    const doReenterScanViewFunc = () => {
      // 判断是否来自已注册列表页面
      const isFromRegisteredPage = data['isFromRegisteredListPage'];
      let getScanType = data['scanType'];
      if (getScanType === 'action' || getScanType === 'barcode') {
        getScanType = 'barCode';
      }

      const dataScan: ScanInterface = {
        isFromLocationSettingPage: true,
        scanType: getScanType,
        isFromRegisteredListPage: isFromRegisteredPage,
        assetActionModel: assetActionModel,
        registeredAssetList:
          getScanType === 'barCode' || getScanType === 'ocr' ? data['showARInfo'] : data['realAssetidList'],
        unRegisteredAssetList: [],
        actionLabel: actionLabel,
        tempActionListType: tempActionListType,
        isNewAction: isNewAction,
        isFromHome,
        isFromTakeMiddleButtonPage,
        searchKey,
        ocrType: ocrType,
        onlineMode: data['onlineMode'],
      };
      // 场所切换完成后，重新再次启动扫描
      this.actionToBarcodeScanOffline(isNeedJump, dataScan, timestamp, callBackAction);
    };

    if (scanMode == 'offline') {
      // 离线扫描
      if (data.type === '5') {
        // 场所已弹窗方式出现
        const modal = await this.modalController.create({
          component: LocationSelectPage,
          componentProps: {
            isPlusBtn: 'true',
            isOffline: true,
          },
        });
        // 弹出
        await modal.present();

        // 用户已点击某一个场所
        const loData = await modal.onWillDismiss();
        const newLo = loData?.data['location'];
        const isFromRegisteredPage = data['isFromRegisteredListPage'];
        let getScanType = data['scanType'];
        if (getScanType === 'action' || getScanType === 'barcode') {
          getScanType = 'barCode';
        }

        const dataScan: ScanInterface = {
          isFromLocationSettingPage: true,
          scanType: getScanType,
          isFromRegisteredListPage: isFromRegisteredPage,
          assetActionModel: assetActionModel,
          registeredAssetList:
            getScanType === 'barCode' || getScanType === 'ocr' ? data['showARInfo'] : data['realAssetidList'],
          unRegisteredAssetList: [],
          actionLabel: actionLabel,
          tempActionListType: tempActionListType,
          isNewAction: isNewAction,
          isFromHome,
          isFromTakeMiddleButtonPage,
          searchKey,
          ocrType: ocrType,
          onlineMode: data['onlineMode'],
        };
        // 场所切换完成后，重新再次启动扫描
        setTimeout(() => {
          this.actionToBarcodeScanOffline(isNeedJump, dataScan, timestamp, callBackAction);
        }, 200);
        return;
      }
      let processType = isNewAction ? 1 : 0;
      const amountType = assetActionModel['amountType'];
      let amountData = {
        isAmountType: !_.isEmpty(amountType),
        amountTextList: this.getAcountText(amountType),
        amountType: amountType,
      };
      let currenPath = '';
      let url = MemCache.instance.get(MemCache.KEY_CUR_NAVI_URL);
      if (url && typeof url == 'string') {
        let splitUrl = url.split('?');
        currenPath = splitUrl.length > 0 ? splitUrl[0] : undefined;
      }
      let navigationOfflineExtras: NavigationExtras = {
        queryParams: {
          dynamicItemDic: assetActionModel.dynamicItemDic,
          isFromScanType: assetActionModel.isFromScanType,
          isFromHome: isFromHome,
          registeredAssetList: assetActionModel.registeredAssetList,
          assetActionModel: assetActionModel.assetActionModel,
          processType: processType,
          isFromActionPage: 'true',
          isFromNewAction: isNewAction,
          fromPageRink: currenPath,
          updateScanedAssetAction: undefined,
          assetActionItemList: undefined,
          isAction: true,
          headerText: [
            amountData?.amountTextList[0] === ''
              ? '処理を選択しました。'
              : amountData?.amountTextList[2] + 'を設定しました。',
            '内容をご確認の上、',
            '「実行」をタップしてください。',
          ],
          actionLabel: actionLabel,
          actionListType: tempActionListType,
          isNewAction: isNewAction,
          searchKey: searchKey,
          scanMode: scanMode,
          timestamp: timestamp,
          offlineScanCallback: async (needAlert: boolean = true) => {
            const alert = await this.alertController.create({
              message: 'ネットワークの接続が確認できませんでした。確認後もう一度お試しください。',
              cssClass: 'common-alert',
              buttons: [
                {
                  text: 'OK',
                  cssClass: 'font-weight-bold',
                },
              ],
            });
            if (needAlert) {
              await alert.present();
              await alert.onDidDismiss();
            }
            await this.navController.pop();
            doReenterScanViewFunc();
          },
        },
      };
      this.navController.navigateForward('/action/offline-progress/view', navigationOfflineExtras);
    } else {
      // 跳转到资产详细
      if (data.type === '1') {
        if (data.allData !== undefined) {
          await StorageUtils.set(StorageUtils.KEY_DATA_FROM_ACTION_SCAN, data.allData);
        }
        let registeredAssetList =
          scanType === 'barCode' || scanType === 'action' ? data['showARInfo'] : data['realAssetidList'];
        let tempBackUrl = '';
        if (!isNewAction) {
          tempBackUrl = '/tabs/action/progress/list';
        } else {
          tempBackUrl = '/tabs/action/list/list';
        }
        const navigationExtras: NavigationExtras = {
          queryParams: {
            assetId: data.assetId,
            assetTypeId: data.assetTypeId,
            registeredAssetList: registeredAssetList,
            fromScanPage: 'fromActionScan',
            backUrl: tempBackUrl,
            ocrType: ocrType,
            actionListType: tempActionListType,
            actionLabel: actionLabel,
            assetActionModel: assetActionModel,
            isActionToPageDetail: true,
          },
        };
        let level = Date.now();
        await this.navController.navigateForward(`/asset/detail/${level}`, navigationExtras);
        return;
      }

      // 弹出切换场所
      if (data.type === '5') {
        // 场所已弹窗方式出现
        const modal = await this.modalController.create({
          component: LocationSelectPage,
          componentProps: {
            isPlusBtn: 'true',
          },
        });
        // 弹出
        await modal.present();

        // 用户已点击某一个场所
        const loData = await modal.onWillDismiss();
        const newLo = loData?.data['location'];
        const isFromRegisteredPage = data['isFromRegisteredListPage'];
        let getScanType = data['scanType'];
        if (getScanType === 'action' || getScanType === 'barcode') {
          getScanType = 'barCode';
        }

        const dataScan: ScanInterface = {
          isFromLocationSettingPage: true,
          scanType: getScanType,
          isFromRegisteredListPage: isFromRegisteredPage,
          assetActionModel: assetActionModel,
          registeredAssetList:
            getScanType === 'barCode' || getScanType === 'ocr' ? data['showARInfo'] : data['realAssetidList'],
          unRegisteredAssetList: [],
          actionLabel: actionLabel,
          tempActionListType: tempActionListType,
          isNewAction: isNewAction,
          isFromHome,
          isFromTakeMiddleButtonPage,
          searchKey,
          ocrType: ocrType,
          onlineMode: data['onlineMode'],
        };
        // 场所切换完成后，重新再次启动扫描
        this.actionToBarcodeScan(isNeedJump, dataScan, callBackAction);
        return;
      }
      let getScanType = data['scanType'];
      if (_.isEmpty(getScanType)) {
        getScanType = 'ocr';
      }
      // 扫描完成
      if (getScanType === 'action' || getScanType === 'barCode') {
        getScanType = 'barcode';
      }
      const dynamicItemDic = await this.getCustomItem();

      const actionId = _.toString(assetActionModel.assetActionId);
      // 拼接 showARInfo 里面的值到  assetDatas.
      const arInfos: any[] = data['showARInfo'];
      let ids = [];
      if (_.isArray(arInfos) && arInfos.length > 0) {
        ids = arInfos.map((p) => {
          return _.toNumber(p.assetId);
        });
      }

      // 本地将 native 传回来的数据存起来, 后续再打开扫描的时候传回去.
      // ionic 层显示的数据只使用 assetid, count
      MemCache.instance.set(MemCache.KEY_NATIVE_TO_INOIC_DATA, arInfos);

      // 通过 assetActionModel 里面的 assetid, assetypeid, actionid, 然后进行处理,
      // 请求网络上的真实数据.
      let assetDatas = await this.getAssetDataForAction(actionId, ids);
      if (_.isArray(assetDatas)) {
        // 返回的数据里面的 homeImage 是独立的url, 需要将 homeImage换成对象,  然后后面页面统一使用.
        assetDatas.forEach((p) => {
          let url = p['homeImageUrl']; // 服务器返回的字段.
          p['homeImage'] = {
            // 额外封装为对象, 方便后面界面使用.
            url: url,
          };
          let arInfo = arInfos.find((arInfo) => {
            return _.toString(arInfo.assetId) === _.toString(p.assetId);
          });
          if (arInfo) {
            p.assetScanedCount = arInfo['count'];
          } else {
            p.assetScanedCount = 0;
          }

          // quantity: 个体的情况下会返回为空, 个体的数量固定为1.
          // 个体 服务器会返回空
          if (_.isNull(p['quantity']) || _.isUndefined(p['quantity'])) {
            if (arInfo && !_.isNull(arInfo['quantity']) && !_.isUndefined(arInfo['quantity'])) {
              p['quantity'] = arInfo['quantity'];
            } else {
              p['quantity'] = 1;
            }
          }
        });

        // sort.
        const sortArray = (arr1, arr2) => {
          // 创建一个 Map 来存储 arr1 中 id 和对应对象的映射关系
          const idMap = new Map(arr1.map((item) => [item.assetId, item]));

          // 对 arr2 进行排序
          arr2.sort((a, b) => {
            // 获取 arr1 中对应的对象
            const itemA = idMap.get(a.assetId);
            const itemB = idMap.get(b.assetId);

            // 根据 arr1 中对象的顺序进行比较
            return arr1.indexOf(itemA) - arr1.indexOf(itemB);
          });

          return arr2;
        };

        sortArray(arInfos, assetDatas);
      }

      const dtyul = {
        // 处理设定基本信息
        assetActionModel: assetActionModel,
        // 处理设定 0一时保存，1新规
        processType: isNewAction ? 1 : 0,
        // 是否新规
        isFromNewAction: isNewAction,
        // 是否从home页面进来
        isFromHome: isFromHome,
        // 处理设定头部名称
        actionLabel: actionLabel,
        actionListType: tempActionListType,
        searchKey: searchKey,
        dynamicItemDic: dynamicItemDic,
        // ocr/RFID/barcode
        isFromScanType: getScanType,
        isNewAction: isNewAction,
        isFromTakeMiddleButtonPage: isFromTakeMiddleButtonPage,
        registeredAssetList: assetDatas,
      };
      console.log('处理设定基本数据    =====>   ', dtyul);
      await this.store.dispatch(new SetNewAndSaveActionBasicPropertiesADD(dtyul));
      if (isNeedJump) {
        // 跳转list页面（新股&一时保存会走此判断）
        this.navController.navigateForward('/action/create-or-save-temporarily');
      } else {
        // 通知list页面扫描完成（扫描请求从list页面发出会走此判读）
        callBackAction();
      }
    }
  };

  private getAcountText(amountType: string): string[] {
    if (amountType === AMOUNT_TYPE.INCREASE_AMOUNT_TYPE) {
      //純増
      return ['増加数', '増加数量', '増加数'];
    }
    if (amountType === AMOUNT_TYPE.DECREASE_AMOUNT_TYPE) {
      // 純減
      return ['減少数', '減少数量', '減少数'];
    }
    if (amountType === AMOUNT_TYPE.MOVE_AMOUNT_TYPE) {
      // 移動
      return ['移動させる資産数', '移動数量', '移動数'];
    }
    if (amountType === AMOUNT_TYPE.INVENTORY_AMOUNT_TYPE) {
      // 棚卸し
      return ['実際の数量', '実際数量', '実際数'];
    }
    return ['', '', ''];
  }
}
