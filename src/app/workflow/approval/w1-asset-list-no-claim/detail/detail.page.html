<div class="global-bg"></div>

<ion-header
  translucent="true"
  class="header"
>
  <ion-toolbar class="title-toolbar">
    <ion-buttons slot="start">
      <ion-back-button
        defaultHref="/tabs/temp-task"
        text=""
        icon="chevron-back"
      ></ion-back-button>
    </ion-buttons>
    <ion-title>資産情報</ion-title>
    <ion-buttons slot="end">
      <ion-button>
        <ion-icon
          slot="icon-only"
          src="assets/images/header/icon-send-back.svg"
        ></ion-icon>
      </ion-button>
      <ion-button>
        <ion-icon
          slot="icon-only"
          src="assets/images/header/icon-release.svg"
        ></ion-icon>
      </ion-button>
      <ion-button
        routerLink="../scan"
        routerDirection="back"
      >
        <ion-icon
          slot="icon-only"
          src="assets/images/header/icon-camera.svg"
        ></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>

  <ion-toolbar class="segment-tab">
    <ion-segment
      value="approval"
      class="w-100"
    >
      <ion-segment-button
        value="new"
        class="segment-tab-button"
        routerLink="/tabs/workflow/new"
        routerDirection="root"
      >
        <ion-label>新規申請</ion-label>
      </ion-segment-button>
      <ion-segment-button
        value="application"
        class="segment-tab-button"
        routerLink="/tabs/workflow/application"
        routerDirection="root"
      >
        <ion-label>申請</ion-label>
      </ion-segment-button>
      <ion-segment-button
        value="approval"
        class="segment-tab-button"
        routerLink="/tabs/workflow/approval"
        routerDirection="root"
      >
        <ion-label>承認</ion-label>
      </ion-segment-button>
    </ion-segment>
  </ion-toolbar>
</ion-header>

<ion-content
  fullscreen="true"
  class="offset-bottom-0"
>
  <div
    class="container layout-default"
    [ngClass]="{ 'is-empty': isEmptyResult }"
  >
    <div class="mb-3">
      <!--検索-->
      <div class="input-group searchbar">
        <span class="input-group-prepend pr-0">
          <button
            class="btn btn-outline bg-white text-primary pl-2 pr-1 pt-0 pb-0 shadow-none no-opacity d-flex align-items-center"
            type="button"
            disabled
          >
            <ion-icon src="./assets/images/toolbar/icon-search.svg"></ion-icon>
          </button>
        </span>
        <input
          type="text"
          class="form-control pl-0"
          placeholder="WF名で検索"
          [(ngModel)]="search"
        />
      </div>
    </div>

    <!-- segment -->
    <div
      class="segment-group group-workflow w-100 mb-3"
      [ngClass]="{ 'group-left-corner-fill': progress === 'not-yet', 'group-right-corner-fill': progress === 'done' }"
    >
      <a
        class="segment-item item-workflow"
        [ngClass]="{ 'is-active': progress === 'not-yet' }"
        (click)="openNotYet($event)"
        >すべて
        <span class="badge badge-pill badge-notify ml-2">{{ unaapveCount }}</span>
      </a>
      <a
        class="segment-item item-workflow"
        [ngClass]="{ 'is-active': progress === 'done' }"
        (click)="openDone($event)"
        >未完了</a
      >
    </div>

    <!--リスト表示-->
    <div class="assets-list by-list">
      <ng-container *ngFor="let asset of assetList; let i = index">
        <div class="asset-item float-icon-right float-icon-next">
          <!--サムネイル-->
          <div
            class="asset-item__thumbnail"
            *ngIf="asset.homeImage"
          >
            <figure class="image is-1by1">
              <img
                src="{{ asset.homeImage }}"
                alt=""
                class="is-rounded"
              />
            </figure>
          </div>
          <!--コンテンツ-->
          <div class="asset-item__content">
            <table>
              <div>
                <tr>
                  <th scope="row">資産名</th>
                  <td
                    ><span class="break-line">{{ asset.firstText }}</span></td
                  >
                </tr>
              </div>
              <div>
                <tr>
                  <th scope="row">識別コード</th>
                  <td
                    ><span class="line-clamp-2">{{ asset.secondText }}</span></td
                  >
                </tr>
              </div>
              <div *ngIf="showAssetLocation">
                <tr>
                  <th scope="row">場所</th>
                  <td
                    ><span class="line-clamp-2">{{ asset.thirdText }}</span></td
                  >
                </tr>
              </div>
            </table>
          </div>
        </div>
      </ng-container>
    </div>

    <!--アイテムなし-->
    <div
      class="empty-message"
      *ngIf="isEmptyResult"
    >
      <p class="empty-message-title">一致する結果はありません</p>
    </div>
  </div>
</ion-content>
