<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--New Work Flow Scan View Controller-->
        <scene sceneID="ydH-JR-Vct">
            <objects>
                <viewController storyboardIdentifier="NewWorkFlowScanViewController" id="OyX-HP-IhY" customClass="NewWorkFlowScanViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="flB-c0-Shb">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="6x1-gX-ubg">
                                <rect key="frame" x="329" y="626" width="48" height="176"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="xh8-1k-Shf">
                                        <rect key="frame" x="0.0" y="0.0" width="48" height="48"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="48" id="KBD-8x-WJh"/>
                                            <constraint firstAttribute="width" constant="48" id="aSa-o8-bl3"/>
                                        </constraints>
                                        <state key="normal" image="scan_location"/>
                                        <connections>
                                            <action selector="locationSetting:" destination="OyX-HP-IhY" eventType="touchUpInside" id="n6o-IJ-X29"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="kju-OL-7zz">
                                        <rect key="frame" x="0.0" y="64" width="48" height="48"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="48" id="IWm-gr-fNC"/>
                                            <constraint firstAttribute="width" constant="48" id="W9k-j3-13f"/>
                                        </constraints>
                                        <state key="normal" image="sacan_input"/>
                                        <connections>
                                            <action selector="editByManualBtnAction:" destination="OyX-HP-IhY" eventType="touchUpInside" id="1uP-4o-C2a"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="UTE-u8-5YH">
                                        <rect key="frame" x="0.0" y="128" width="48" height="48"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="48" id="8bT-za-Ioe"/>
                                            <constraint firstAttribute="width" constant="48" id="vJ6-vR-tpf"/>
                                        </constraints>
                                        <state key="normal" image="light-bulb-off"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                <real key="value" value="24"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="setLightOnOrOff:" destination="OyX-HP-IhY" eventType="touchUpInside" id="JBl-ZB-9bf"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="width" constant="48" id="1Hi-10-R4k"/>
                                </constraints>
                            </stackView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="LFa-pm-l5M">
                                <rect key="frame" x="16" y="67" width="40" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="NMI-0Y-OtU"/>
                                    <constraint firstAttribute="width" constant="40" id="WCo-tz-b5x"/>
                                </constraints>
                                <state key="normal" image="scan_close"/>
                                <connections>
                                    <action selector="closeBtnAction:" destination="OyX-HP-IhY" eventType="touchUpInside" id="ju5-yv-QO3"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="KTY-nb-1Do"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="KTY-nb-1Do" firstAttribute="bottom" secondItem="6x1-gX-ubg" secondAttribute="bottom" constant="16" id="8n1-yd-EnU"/>
                            <constraint firstItem="KTY-nb-1Do" firstAttribute="trailing" secondItem="6x1-gX-ubg" secondAttribute="trailing" constant="16" id="iVd-lU-zfs"/>
                            <constraint firstItem="LFa-pm-l5M" firstAttribute="top" secondItem="KTY-nb-1Do" secondAttribute="top" constant="8" id="ySV-Bq-UE8"/>
                            <constraint firstItem="LFa-pm-l5M" firstAttribute="leading" secondItem="KTY-nb-1Do" secondAttribute="leading" constant="16" id="zOI-4W-HGQ"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="clostBtn" destination="LFa-pm-l5M" id="aih-KA-O1x"/>
                        <outlet property="editBtn" destination="kju-OL-7zz" id="VkD-OG-VBV"/>
                        <outlet property="lightBtn" destination="UTE-u8-5YH" id="EFB-WH-y8G"/>
                        <outlet property="locationBtn" destination="xh8-1k-Shf" id="M9T-By-Q8a"/>
                        <outlet property="topOfCloseBtn" destination="ySV-Bq-UE8" id="CdK-b1-Nco"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="VNj-7q-NTC" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1164" y="1076"/>
        </scene>
        <!--Work Flow Scan Free View Controller-->
        <scene sceneID="fZr-63-52c">
            <objects>
                <viewController storyboardIdentifier="WorkFlowScanFreeViewController" id="uyQ-3n-4KM" customClass="WorkFlowScanFreeViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="DFp-kG-W0K">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="W1w-Es-VpN">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="viM-mA-aiU" customClass="PreviewView">
                                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                    </view>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="ggu-vc-gdP">
                                        <rect key="frame" x="329" y="660" width="48" height="176"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="R4I-Re-3J6">
                                                <rect key="frame" x="0.0" y="0.0" width="48" height="48"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="48" id="dtv-TS-Fx6"/>
                                                    <constraint firstAttribute="width" constant="48" id="god-Tx-GVb"/>
                                                </constraints>
                                                <state key="normal" image="scan_location"/>
                                                <connections>
                                                    <action selector="locationSetting:" destination="uyQ-3n-4KM" eventType="touchUpInside" id="1lx-39-ra6"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="jDn-JW-vOu">
                                                <rect key="frame" x="0.0" y="64" width="48" height="48"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="48" id="PRd-6I-SMh"/>
                                                    <constraint firstAttribute="height" constant="48" id="hCO-d7-LYQ"/>
                                                </constraints>
                                                <state key="normal" image="sacan_input"/>
                                                <connections>
                                                    <action selector="editByManualBtnAution:" destination="uyQ-3n-4KM" eventType="touchUpInside" id="Uc4-Yb-JL3"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="fgH-9V-b9a">
                                                <rect key="frame" x="0.0" y="128" width="48" height="48"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="48" id="Rg8-WN-aVH"/>
                                                    <constraint firstAttribute="height" constant="48" id="sj2-qD-be6"/>
                                                </constraints>
                                                <state key="normal" image="light-bulb-off"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                        <real key="value" value="24"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="setLightOnOrOff:" destination="uyQ-3n-4KM" eventType="touchUpInside" id="aGH-4C-QJC"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="48" id="BNl-jQ-QkJ"/>
                                        </constraints>
                                    </stackView>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="LAo-bP-dk5">
                                        <rect key="frame" x="16" y="67" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="40" id="cE9-36-4e9"/>
                                            <constraint firstAttribute="height" constant="40" id="iKK-9P-IBU"/>
                                        </constraints>
                                        <state key="normal" image="scan_close"/>
                                        <connections>
                                            <action selector="closeBtnAction:" destination="uyQ-3n-4KM" eventType="touchUpInside" id="hOK-Zb-GS5"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" white="0.66666666669999997" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="ggu-vc-gdP" secondAttribute="trailing" constant="16" id="Gub-Rx-Z7l"/>
                                    <constraint firstAttribute="trailing" secondItem="viM-mA-aiU" secondAttribute="trailing" id="MJ8-dB-byj"/>
                                    <constraint firstItem="LAo-bP-dk5" firstAttribute="leading" secondItem="W1w-Es-VpN" secondAttribute="leading" constant="16" id="MeZ-Dk-4Ka"/>
                                    <constraint firstAttribute="bottom" secondItem="ggu-vc-gdP" secondAttribute="bottom" constant="16" id="QZP-Cz-CmY"/>
                                    <constraint firstItem="viM-mA-aiU" firstAttribute="leading" secondItem="W1w-Es-VpN" secondAttribute="leading" id="RRo-El-Oyh"/>
                                    <constraint firstAttribute="bottom" secondItem="viM-mA-aiU" secondAttribute="bottom" id="aDg-58-Ukc"/>
                                    <constraint firstItem="viM-mA-aiU" firstAttribute="top" secondItem="W1w-Es-VpN" secondAttribute="top" id="nDE-0y-kfw"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="xST-xg-Dbn"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="W1w-Es-VpN" firstAttribute="leading" secondItem="xST-xg-Dbn" secondAttribute="leading" id="Iex-Z1-URQ"/>
                            <constraint firstItem="W1w-Es-VpN" firstAttribute="top" secondItem="DFp-kG-W0K" secondAttribute="top" id="UVF-Sg-4nK"/>
                            <constraint firstItem="LAo-bP-dk5" firstAttribute="top" secondItem="xST-xg-Dbn" secondAttribute="top" constant="8" id="Vtm-q2-4L1"/>
                            <constraint firstItem="xST-xg-Dbn" firstAttribute="trailing" secondItem="W1w-Es-VpN" secondAttribute="trailing" id="hcg-4n-4ud"/>
                            <constraint firstAttribute="bottom" secondItem="W1w-Es-VpN" secondAttribute="bottom" id="kDV-KE-heV"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="closeBtn" destination="LAo-bP-dk5" id="TFx-Fh-IUP"/>
                        <outlet property="lightBtn" destination="fgH-9V-b9a" id="Kj1-KY-pCP"/>
                        <outlet property="locationBtn" destination="R4I-Re-3J6" id="hIk-Bu-LC5"/>
                        <outlet property="previewAction" destination="viM-mA-aiU" id="p7I-vu-TQd"/>
                        <outlet property="previewView" destination="W1w-Es-VpN" id="e2f-Fr-XQm"/>
                        <outlet property="topOfCloseBtn" destination="Vtm-q2-4L1" id="xRJ-Wq-VkG"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="6UT-gI-GtY" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="344.80000000000001" y="1077.2113943028487"/>
        </scene>
        <!--Asset Work Flow Scan Free View Controller-->
        <scene sceneID="3gL-0J-7xY">
            <objects>
                <viewController storyboardIdentifier="AssetWorkFlowScanFreeViewController" hidesBottomBarWhenPushed="YES" id="UQc-Qh-v2C" customClass="AssetWorkFlowScanFreeViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="wYx-IJ-XJC">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="v4M-7m-CY1">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="uWY-4z-dtC" customClass="PreviewView">
                                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                    </view>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="NhU-Iu-MUe">
                                        <rect key="frame" x="16" y="67" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="40" id="2tB-wm-fAU"/>
                                            <constraint firstAttribute="height" constant="40" id="8TJ-Jx-Ty8"/>
                                        </constraints>
                                        <state key="normal" image="scan_close"/>
                                        <connections>
                                            <action selector="closeBtnAction:" destination="UQc-Qh-v2C" eventType="touchUpInside" id="7BQ-iQ-uwU"/>
                                        </connections>
                                    </button>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="ae7-44-xYc">
                                        <rect key="frame" x="329" y="551" width="48" height="176"/>
                                        <subviews>
                                            <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="r7f-W4-cZt">
                                                <rect key="frame" x="0.0" y="-48" width="48" height="48"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="48" id="cpo-cR-NKS"/>
                                                    <constraint firstAttribute="width" constant="48" id="d0c-DZ-TVI"/>
                                                </constraints>
                                                <state key="normal" image="scan_trim"/>
                                                <connections>
                                                    <action selector="regixSettingAction:" destination="UQc-Qh-v2C" eventType="touchUpInside" id="qIo-xY-cTg"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="9Pv-lu-2jE">
                                                <rect key="frame" x="0.0" y="0.0" width="48" height="48"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="48" id="4aK-B6-9wI"/>
                                                    <constraint firstAttribute="width" constant="48" id="870-oM-5h0"/>
                                                </constraints>
                                                <state key="normal" image="scan_location"/>
                                                <connections>
                                                    <action selector="locationSetting:" destination="UQc-Qh-v2C" eventType="touchUpInside" id="MWo-E9-Lad"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Oev-fX-Rjw">
                                                <rect key="frame" x="0.0" y="64" width="48" height="48"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="48" id="CBs-Uj-DTf"/>
                                                    <constraint firstAttribute="width" constant="48" id="Qes-kh-Xxu"/>
                                                </constraints>
                                                <state key="normal" image="sacan_input"/>
                                                <connections>
                                                    <action selector="enterBarcodeManually:" destination="UQc-Qh-v2C" eventType="touchUpInside" id="XC1-Fj-SIP"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="b7c-9Q-j1X">
                                                <rect key="frame" x="0.0" y="128" width="48" height="48"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="48" id="Ic8-Iw-OMs"/>
                                                    <constraint firstAttribute="height" constant="48" id="ShN-mI-M3X"/>
                                                </constraints>
                                                <state key="normal" image="light-bulb-off"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                        <real key="value" value="24"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="setLightOnOrOff:" destination="Gxw-hl-3bP" eventType="touchUpInside" id="BdI-Yn-pD1"/>
                                                    <action selector="setLightOnOrOff:" destination="UQc-Qh-v2C" eventType="touchUpInside" id="bLl-C5-6z8"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                    </stackView>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="4HT-my-ERe">
                                        <rect key="frame" x="0.0" y="743" width="393" height="109"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="HO6-hD-gV2" customClass="FontsizeButton" customModule="App" customModuleProvider="target">
                                                <rect key="frame" x="20" y="32" width="353" height="45"/>
                                                <color key="backgroundColor" red="0.**********" green="0.**********" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="45" id="JNS-Pt-w3g"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                                <state key="normal" title="スキャンリストへ">
                                                    <color key="titleColor" red="0.46274509800000002" green="0.46666666670000001" blue="0.46666666670000001" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                </state>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                        <integer key="value" value="8"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="boolean" keyPath="layer.clipsToBounds" value="YES"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="showAssetDetailBtn:" destination="UQc-Qh-v2C" eventType="touchUpInside" id="B0d-OG-fE2"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="HO6-hD-gV2" firstAttribute="leading" secondItem="4HT-my-ERe" secondAttribute="leading" constant="20" id="5kR-H0-IMS"/>
                                            <constraint firstItem="HO6-hD-gV2" firstAttribute="top" secondItem="4HT-my-ERe" secondAttribute="top" constant="32" id="EkE-Jf-B1U"/>
                                            <constraint firstAttribute="bottom" secondItem="HO6-hD-gV2" secondAttribute="bottom" constant="32" id="UE8-Hw-ezv"/>
                                            <constraint firstAttribute="trailing" secondItem="HO6-hD-gV2" secondAttribute="trailing" constant="20" id="ZP1-UJ-ctP"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="uWY-4z-dtC" firstAttribute="leading" secondItem="v4M-7m-CY1" secondAttribute="leading" id="Gz0-kb-zZ4"/>
                                    <constraint firstAttribute="bottom" secondItem="uWY-4z-dtC" secondAttribute="bottom" id="Xtk-eM-VYB"/>
                                    <constraint firstItem="4HT-my-ERe" firstAttribute="top" secondItem="ae7-44-xYc" secondAttribute="bottom" constant="16" id="ZSp-Mx-zJE"/>
                                    <constraint firstItem="NhU-Iu-MUe" firstAttribute="leading" secondItem="v4M-7m-CY1" secondAttribute="leading" constant="16" id="fdI-hk-1ST"/>
                                    <constraint firstAttribute="trailing" secondItem="ae7-44-xYc" secondAttribute="trailing" constant="16" id="jP2-hF-wGT"/>
                                    <constraint firstAttribute="trailing" secondItem="uWY-4z-dtC" secondAttribute="trailing" id="jQf-Ta-1JC"/>
                                    <constraint firstAttribute="bottom" secondItem="4HT-my-ERe" secondAttribute="bottom" id="ooN-aD-Nn6"/>
                                    <constraint firstAttribute="trailing" secondItem="4HT-my-ERe" secondAttribute="trailing" id="qi9-xx-02w"/>
                                    <constraint firstItem="4HT-my-ERe" firstAttribute="leading" secondItem="v4M-7m-CY1" secondAttribute="leading" id="w0w-qc-C5d"/>
                                    <constraint firstItem="uWY-4z-dtC" firstAttribute="top" secondItem="v4M-7m-CY1" secondAttribute="top" id="zg3-Xs-Nac"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="dCw-gd-8Jl"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="NhU-Iu-MUe" firstAttribute="top" secondItem="dCw-gd-8Jl" secondAttribute="top" constant="8" id="47e-LN-BOp"/>
                            <constraint firstItem="v4M-7m-CY1" firstAttribute="leading" secondItem="dCw-gd-8Jl" secondAttribute="leading" id="CGX-yK-M3o"/>
                            <constraint firstItem="v4M-7m-CY1" firstAttribute="top" secondItem="wYx-IJ-XJC" secondAttribute="top" id="DcN-9x-UYv"/>
                            <constraint firstItem="dCw-gd-8Jl" firstAttribute="trailing" secondItem="v4M-7m-CY1" secondAttribute="trailing" id="XzW-vq-tUK"/>
                            <constraint firstAttribute="bottom" secondItem="v4M-7m-CY1" secondAttribute="bottom" id="dmS-d2-uqf"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="Xo8-HA-Q8D"/>
                    <connections>
                        <outlet property="closeBtn" destination="NhU-Iu-MUe" id="4wW-YF-NqG"/>
                        <outlet property="enterBarcodeManuallyBtn" destination="Oev-fX-Rjw" id="Bzc-5p-Tke"/>
                        <outlet property="lightBtn" destination="b7c-9Q-j1X" id="bCT-Ga-vGi"/>
                        <outlet property="locationBtn" destination="9Pv-lu-2jE" id="t5a-YA-3lu"/>
                        <outlet property="previewView" destination="v4M-7m-CY1" id="tfD-4k-yau"/>
                        <outlet property="previewWorkflow" destination="uWY-4z-dtC" id="YbQ-tr-yRz"/>
                        <outlet property="regixSettingBtn" destination="r7f-W4-cZt" id="OcL-RS-uhJ"/>
                        <outlet property="showDetailBtn" destination="HO6-hD-gV2" id="IeV-eX-ltP"/>
                        <outlet property="topOfCloseBtn" destination="47e-LN-BOp" id="EiN-aw-wkE"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="2l7-3Q-rxh" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="344.80000000000001" y="2556.9715142428786"/>
        </scene>
        <!--Asset Work Flow Scan View Controller-->
        <scene sceneID="AEy-wM-UL1">
            <objects>
                <viewController storyboardIdentifier="AssetWorkFlowScanViewController" id="IUc-uq-qc9" customClass="AssetWorkFlowScanViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Vyo-dc-k5o">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Pmt-Lb-BQH">
                                <rect key="frame" x="0.0" y="743" width="393" height="109"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="50B-tq-VIU" customClass="FontsizeButton" customModule="App" customModuleProvider="target">
                                        <rect key="frame" x="20" y="32" width="353" height="45"/>
                                        <color key="backgroundColor" red="0.**********" green="0.**********" blue="0.**********" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="45" id="Z8G-vW-uwV"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                        <state key="normal" title="スキャンリストへ">
                                            <color key="titleColor" red="0.46274509800000002" green="0.46666666670000001" blue="0.46666666670000001" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                        </state>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                                <integer key="value" value="8"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="layer.clipsToBounds" value="YES"/>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="showAssetDetailBtn:" destination="IUc-uq-qc9" eventType="touchUpInside" id="4EH-7J-gPl"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="50B-tq-VIU" secondAttribute="trailing" constant="20" id="4Uc-Tc-UMF"/>
                                    <constraint firstAttribute="bottom" secondItem="50B-tq-VIU" secondAttribute="bottom" constant="32" id="7Dx-wx-s8w"/>
                                    <constraint firstItem="50B-tq-VIU" firstAttribute="top" secondItem="Pmt-Lb-BQH" secondAttribute="top" constant="32" id="I42-rl-Id4"/>
                                    <constraint firstItem="50B-tq-VIU" firstAttribute="leading" secondItem="Pmt-Lb-BQH" secondAttribute="leading" constant="20" id="WJP-5A-mXr"/>
                                </constraints>
                            </view>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="D0y-lC-pWU">
                                <rect key="frame" x="16" y="67" width="40" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="40" id="Chp-Cr-ru6"/>
                                    <constraint firstAttribute="height" constant="40" id="Qkc-6R-IgI"/>
                                </constraints>
                                <state key="normal" image="scan_close"/>
                                <connections>
                                    <action selector="closeBtnAction:" destination="IUc-uq-qc9" eventType="touchUpInside" id="A6h-eZ-GZs"/>
                                </connections>
                            </button>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="b3U-Pq-dve">
                                <rect key="frame" x="329" y="551" width="48" height="176"/>
                                <subviews>
                                    <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ueh-li-1qe">
                                        <rect key="frame" x="0.0" y="-48" width="48" height="48"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="48" id="LgO-Vq-BE3"/>
                                            <constraint firstAttribute="height" constant="48" id="c8A-fd-CWV"/>
                                        </constraints>
                                        <state key="normal" image="scan_trim"/>
                                        <connections>
                                            <action selector="regixSettingAction:" destination="IUc-uq-qc9" eventType="touchUpInside" id="b7k-Rs-sHd"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ecX-xL-rMT">
                                        <rect key="frame" x="0.0" y="0.0" width="48" height="48"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="48" id="HNK-q8-Ux6"/>
                                            <constraint firstAttribute="width" constant="48" id="YNY-fS-4Nf"/>
                                        </constraints>
                                        <state key="normal" image="scan_location"/>
                                        <connections>
                                            <action selector="locationSetting:" destination="IUc-uq-qc9" eventType="touchUpInside" id="8xI-aN-RxQ"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="uG6-z9-sjY">
                                        <rect key="frame" x="0.0" y="64" width="48" height="48"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="48" id="IFG-dd-QZL"/>
                                            <constraint firstAttribute="width" constant="48" id="hgF-lS-XEb"/>
                                        </constraints>
                                        <state key="normal" image="sacan_input"/>
                                        <connections>
                                            <action selector="enterBarcodeManually:" destination="IUc-uq-qc9" eventType="touchUpInside" id="R4N-Vr-4wj"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="bIk-kP-Khv">
                                        <rect key="frame" x="0.0" y="128" width="48" height="48"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="48" id="3Da-0k-phs"/>
                                            <constraint firstAttribute="height" constant="48" id="rtm-OT-SaX"/>
                                        </constraints>
                                        <state key="normal" image="light-bulb-off"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                <real key="value" value="24"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="setLightOnOrOff:" destination="IUc-uq-qc9" eventType="touchUpInside" id="Afl-g7-M0N"/>
                                        </connections>
                                    </button>
                                </subviews>
                            </stackView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="7tv-mY-5wY"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="D0y-lC-pWU" firstAttribute="top" secondItem="7tv-mY-5wY" secondAttribute="top" constant="8" id="Hi0-Fl-zLs"/>
                            <constraint firstAttribute="bottom" secondItem="Pmt-Lb-BQH" secondAttribute="bottom" id="KoS-AV-Wzt"/>
                            <constraint firstItem="Pmt-Lb-BQH" firstAttribute="top" secondItem="b3U-Pq-dve" secondAttribute="bottom" constant="16" id="QgJ-AV-Vv9"/>
                            <constraint firstItem="D0y-lC-pWU" firstAttribute="leading" secondItem="7tv-mY-5wY" secondAttribute="leading" constant="16" id="fXj-We-inP"/>
                            <constraint firstAttribute="trailing" secondItem="Pmt-Lb-BQH" secondAttribute="trailing" id="h1h-5T-H6i"/>
                            <constraint firstItem="Pmt-Lb-BQH" firstAttribute="leading" secondItem="Vyo-dc-k5o" secondAttribute="leading" id="pAe-eF-gqr"/>
                            <constraint firstItem="7tv-mY-5wY" firstAttribute="trailing" secondItem="b3U-Pq-dve" secondAttribute="trailing" constant="16" id="uK1-r5-4B7"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="msq-ZE-Jbc"/>
                    <connections>
                        <outlet property="enterBarcodeManuallyBtn" destination="uG6-z9-sjY" id="w6f-Z9-dap"/>
                        <outlet property="lightBtn" destination="bIk-kP-Khv" id="TVF-07-8jT"/>
                        <outlet property="loctionBtn" destination="ecX-xL-rMT" id="O7v-Mb-UVp"/>
                        <outlet property="regixSettingBtn" destination="ueh-li-1qe" id="kj2-bK-A0k"/>
                        <outlet property="showDetailBtn" destination="50B-tq-VIU" id="qsD-eS-V2i"/>
                        <outlet property="topOfCloseBtn" destination="Hi0-Fl-zLs" id="1jN-2V-0GJ"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="B5V-Aw-bcr" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-442.39999999999998" y="2556.9715142428786"/>
        </scene>
        <!--No Subprocess Work Flow Scan View Controller-->
        <scene sceneID="dkR-na-IRh">
            <objects>
                <viewController storyboardIdentifier="NoSubprocessWorkFlowScanViewController" id="Gxw-hl-3bP" customClass="NoSubprocessWorkFlowScanViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" restorationIdentifier="NoSubprocessWorkFlowScanViewController" id="Uh8-pm-ANa">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="HXH-Aw-mkr">
                                <rect key="frame" x="16" y="67" width="40" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="40" id="bef-aX-Hre"/>
                                    <constraint firstAttribute="height" constant="40" id="tIT-4V-Js5"/>
                                </constraints>
                                <state key="normal" image="scan_close"/>
                                <connections>
                                    <action selector="closeBtnAction:" destination="Gxw-hl-3bP" eventType="touchUpInside" id="K3x-vK-B4i"/>
                                </connections>
                            </button>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="rGN-aN-kgb">
                                <rect key="frame" x="329" y="562" width="48" height="240"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="3Ih-Ud-ADT">
                                        <rect key="frame" x="0.0" y="0.0" width="48" height="48"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="48" id="RFP-sK-CWM"/>
                                            <constraint firstAttribute="width" constant="48" id="iWS-uf-w1t"/>
                                        </constraints>
                                        <state key="normal" image="scan_trim"/>
                                        <connections>
                                            <action selector="regixSettingAction:" destination="Gxw-hl-3bP" eventType="touchUpInside" id="Diu-Uv-NbZ"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Way-JS-gv4">
                                        <rect key="frame" x="0.0" y="64" width="48" height="48"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="48" id="D6o-dR-JQz"/>
                                            <constraint firstAttribute="height" constant="48" id="Rge-i5-YAe"/>
                                        </constraints>
                                        <state key="normal" image="scan_location"/>
                                        <connections>
                                            <action selector="locationSetting:" destination="Gxw-hl-3bP" eventType="touchUpInside" id="xqO-0O-vth"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="dTZ-Bn-cRP">
                                        <rect key="frame" x="0.0" y="128" width="48" height="48"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="48" id="Jx7-YV-Ur9"/>
                                            <constraint firstAttribute="width" constant="48" id="oPg-5Y-qCU"/>
                                        </constraints>
                                        <state key="normal" image="sacan_input"/>
                                        <connections>
                                            <action selector="enterBarcodeManually:" destination="Gxw-hl-3bP" eventType="touchUpInside" id="t5g-Vw-4KF"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="oL1-Hr-yKp">
                                        <rect key="frame" x="0.0" y="192" width="48" height="48"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="48" id="2eo-pW-BTk"/>
                                            <constraint firstAttribute="width" constant="48" id="D1g-6Z-6pR"/>
                                        </constraints>
                                        <state key="normal" image="light-bulb-off"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                <real key="value" value="24"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="setLightOnOrOff:" destination="Gxw-hl-3bP" eventType="touchUpInside" id="qme-9b-etv"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="width" constant="48" id="wJ2-fy-fD8"/>
                                </constraints>
                            </stackView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="wTp-5Z-JF5"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="wTp-5Z-JF5" firstAttribute="trailing" secondItem="rGN-aN-kgb" secondAttribute="trailing" constant="16" id="89q-Zb-O7E"/>
                            <constraint firstItem="wTp-5Z-JF5" firstAttribute="bottom" secondItem="rGN-aN-kgb" secondAttribute="bottom" constant="16" id="TgV-qj-M35"/>
                            <constraint firstItem="HXH-Aw-mkr" firstAttribute="leading" secondItem="wTp-5Z-JF5" secondAttribute="leading" constant="16" id="Umy-KW-JVL"/>
                            <constraint firstItem="HXH-Aw-mkr" firstAttribute="top" secondItem="wTp-5Z-JF5" secondAttribute="top" constant="8" id="VVj-fN-bZN"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="U6y-Mg-X4x"/>
                    <connections>
                        <outlet property="enterBarcodeManuallyBtn" destination="dTZ-Bn-cRP" id="efV-SH-JjL"/>
                        <outlet property="lightBtn" destination="oL1-Hr-yKp" id="bBS-L7-4Nj"/>
                        <outlet property="locationBtn" destination="Way-JS-gv4" id="EdH-2l-FfN"/>
                        <outlet property="regixSettingBtn" destination="3Ih-Ud-ADT" id="ez6-eu-WBf"/>
                        <outlet property="topOfCloseBtn" destination="VVj-fN-bZN" id="duU-2d-jbJ"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="AhD-WJ-Ozq" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-442.39999999999998" y="3253.2233883058475"/>
        </scene>
        <!--No Subprocess Work Flow Scan Free View Controller-->
        <scene sceneID="qFn-b3-djc">
            <objects>
                <viewController storyboardIdentifier="NoSubprocessWorkFlowScanFreeViewController" hidesBottomBarWhenPushed="YES" id="MNm-6u-5yZ" customClass="NoSubprocessWorkFlowScanFreeViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="6qn-Sd-FDJ">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="du2-21-CqK">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="z5Q-Cb-SRY" customClass="PreviewView">
                                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                    </view>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="875-l7-QAK">
                                        <rect key="frame" x="16" y="67" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="40" id="Me2-XZ-zyi"/>
                                            <constraint firstAttribute="width" constant="40" id="TAO-Xb-cuh"/>
                                        </constraints>
                                        <state key="normal" image="scan_close"/>
                                        <connections>
                                            <action selector="closeBtnAction:" destination="MNm-6u-5yZ" eventType="touchUpInside" id="nQo-Kb-Dnj"/>
                                        </connections>
                                    </button>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="8Hc-Ot-Cyc">
                                        <rect key="frame" x="329" y="596" width="48" height="240"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Vod-NA-60c">
                                                <rect key="frame" x="0.0" y="0.0" width="48" height="48"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="48" id="LJX-7c-09B"/>
                                                    <constraint firstAttribute="height" constant="48" id="Lev-Gs-BFI"/>
                                                </constraints>
                                                <state key="normal" image="scan_trim"/>
                                                <connections>
                                                    <action selector="regixSettingAction:" destination="MNm-6u-5yZ" eventType="touchUpInside" id="Nxh-Wt-qj9"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="0LU-te-5Sn">
                                                <rect key="frame" x="0.0" y="64" width="48" height="48"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="48" id="K7V-Nh-bzv"/>
                                                    <constraint firstAttribute="height" constant="48" id="gIe-56-K5X"/>
                                                </constraints>
                                                <state key="normal" image="scan_location"/>
                                                <connections>
                                                    <action selector="locationSetting:" destination="MNm-6u-5yZ" eventType="touchUpInside" id="ppt-Hp-K0z"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="yHW-SE-47T">
                                                <rect key="frame" x="0.0" y="128" width="48" height="48"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="48" id="A9U-Vo-j3c"/>
                                                    <constraint firstAttribute="width" constant="48" id="x9L-iK-yyi"/>
                                                </constraints>
                                                <state key="normal" image="sacan_input"/>
                                                <connections>
                                                    <action selector="enterBarcodeManually:" destination="MNm-6u-5yZ" eventType="touchUpInside" id="voq-65-RLM"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="SA8-vH-73X">
                                                <rect key="frame" x="0.0" y="192" width="48" height="48"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="48" id="Ky8-Af-QrD"/>
                                                    <constraint firstAttribute="width" constant="48" id="vT6-XA-4je"/>
                                                </constraints>
                                                <state key="normal" image="light-bulb-off"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                        <real key="value" value="24"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="setLightOnOrOff:" destination="MNm-6u-5yZ" eventType="touchUpInside" id="NOW-AD-OKF"/>
                                                    <action selector="setLightOnOrOff:" destination="Gxw-hl-3bP" eventType="touchUpInside" id="Wlk-eQ-W2F"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="48" id="wpT-y0-Rk6"/>
                                        </constraints>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="z5Q-Cb-SRY" firstAttribute="top" secondItem="du2-21-CqK" secondAttribute="top" id="J1j-Oi-gCM"/>
                                    <constraint firstItem="z5Q-Cb-SRY" firstAttribute="leading" secondItem="du2-21-CqK" secondAttribute="leading" id="bjP-kc-lny"/>
                                    <constraint firstAttribute="trailing" secondItem="z5Q-Cb-SRY" secondAttribute="trailing" id="h4a-WR-acW"/>
                                    <constraint firstAttribute="bottom" secondItem="8Hc-Ot-Cyc" secondAttribute="bottom" constant="16" id="kCB-mH-Bez"/>
                                    <constraint firstAttribute="trailing" secondItem="8Hc-Ot-Cyc" secondAttribute="trailing" constant="16" id="oSB-oD-hkG"/>
                                    <constraint firstAttribute="bottom" secondItem="z5Q-Cb-SRY" secondAttribute="bottom" id="vXd-Se-597"/>
                                    <constraint firstItem="875-l7-QAK" firstAttribute="leading" secondItem="du2-21-CqK" secondAttribute="leading" constant="16" id="zL1-NR-0gE"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="g5S-xP-szC"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="du2-21-CqK" firstAttribute="leading" secondItem="g5S-xP-szC" secondAttribute="leading" id="1QP-ly-crq"/>
                            <constraint firstItem="g5S-xP-szC" firstAttribute="trailing" secondItem="du2-21-CqK" secondAttribute="trailing" id="5fA-vc-RG7"/>
                            <constraint firstItem="875-l7-QAK" firstAttribute="top" secondItem="g5S-xP-szC" secondAttribute="top" constant="8" id="U3K-mK-IYx"/>
                            <constraint firstAttribute="bottom" secondItem="du2-21-CqK" secondAttribute="bottom" id="hBp-xo-WjK"/>
                            <constraint firstItem="du2-21-CqK" firstAttribute="top" secondItem="6qn-Sd-FDJ" secondAttribute="top" id="isS-ZH-oki"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="mqT-Kf-INm"/>
                    <connections>
                        <outlet property="closeBtn" destination="875-l7-QAK" id="iIo-Ge-ZAK"/>
                        <outlet property="enterBarcodeManuallyBtn" destination="yHW-SE-47T" id="AyR-2l-QXH"/>
                        <outlet property="lightBtn" destination="SA8-vH-73X" id="jd6-8a-aPM"/>
                        <outlet property="locationBtn" destination="0LU-te-5Sn" id="DlX-2s-1pE"/>
                        <outlet property="previewView" destination="du2-21-CqK" id="WsV-1r-yh8"/>
                        <outlet property="previewWorkflow" destination="z5Q-Cb-SRY" id="qdP-xE-6Qi"/>
                        <outlet property="regixSettingBtn" destination="Vod-NA-60c" id="7Y4-tz-FSw"/>
                        <outlet property="topOfCloseBtn" destination="U3K-mK-IYx" id="HhF-Ft-Jvx"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="xyM-1f-XL3" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="344.80000000000001" y="3252.3238380809598"/>
        </scene>
        <!--No Claim Work Flow Scan View Controller-->
        <scene sceneID="JYK-nb-bQZ">
            <objects>
                <viewController storyboardIdentifier="NoClaimWorkFlowScanViewController" id="XrS-qi-yVH" customClass="NoClaimWorkFlowScanViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="rGI-yo-sZP">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="E51-yy-jiL">
                                <rect key="frame" x="16" y="67" width="40" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="I5H-aD-DCR"/>
                                    <constraint firstAttribute="width" constant="40" id="hgb-ZE-E5x"/>
                                </constraints>
                                <state key="normal" image="scan_close"/>
                                <connections>
                                    <action selector="closeBtnAction:" destination="XrS-qi-yVH" eventType="touchUpInside" id="eRc-d0-riV"/>
                                </connections>
                            </button>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="huv-M5-NSB">
                                <rect key="frame" x="329" y="626" width="48" height="176"/>
                                <subviews>
                                    <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="CuO-4H-QOQ">
                                        <rect key="frame" x="0.0" y="-48" width="48" height="48"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="48" id="8jV-oB-L8T"/>
                                            <constraint firstAttribute="width" constant="48" id="ybU-GE-y6v"/>
                                        </constraints>
                                        <state key="normal" image="scan_trim"/>
                                        <connections>
                                            <action selector="onClickTrim:" destination="XrS-qi-yVH" eventType="touchUpInside" id="aEy-II-mb4"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="V5Q-MG-ys2">
                                        <rect key="frame" x="0.0" y="0.0" width="48" height="48"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="48" id="7C6-01-vbb"/>
                                            <constraint firstAttribute="height" constant="48" id="wbe-AA-fKH"/>
                                        </constraints>
                                        <state key="normal" image="scan_location"/>
                                        <connections>
                                            <action selector="onClickLocation:" destination="XrS-qi-yVH" eventType="touchUpInside" id="lhR-nA-dg7"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="M9I-2g-ts5">
                                        <rect key="frame" x="0.0" y="64" width="48" height="48"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="48" id="keo-bb-jXO"/>
                                            <constraint firstAttribute="width" constant="48" id="mE3-oF-2aW"/>
                                        </constraints>
                                        <state key="normal" image="sacan_input"/>
                                        <connections>
                                            <action selector="onClickInput:" destination="XrS-qi-yVH" eventType="touchUpInside" id="Uas-NK-Hqz"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Zkb-Jn-ddm">
                                        <rect key="frame" x="0.0" y="128" width="48" height="48"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="48" id="EQv-dX-QAK"/>
                                            <constraint firstAttribute="width" constant="48" id="Swg-y3-PWC"/>
                                        </constraints>
                                        <state key="normal" image="light-bulb-off"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                <real key="value" value="24"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="setLightOnOrOff:" destination="XrS-qi-yVH" eventType="touchUpInside" id="Hkj-2q-hCb"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="width" constant="48" id="FTS-eW-aPL"/>
                                </constraints>
                            </stackView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="mZO-Dk-GVz"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="E51-yy-jiL" firstAttribute="leading" secondItem="mZO-Dk-GVz" secondAttribute="leading" constant="16" id="7dX-Oh-ozQ"/>
                            <constraint firstItem="mZO-Dk-GVz" firstAttribute="trailing" secondItem="huv-M5-NSB" secondAttribute="trailing" constant="16" id="d1H-zf-CHj"/>
                            <constraint firstItem="mZO-Dk-GVz" firstAttribute="bottom" secondItem="huv-M5-NSB" secondAttribute="bottom" constant="16" id="scD-IJ-haz"/>
                            <constraint firstItem="E51-yy-jiL" firstAttribute="top" secondItem="mZO-Dk-GVz" secondAttribute="top" constant="8" id="vm5-pS-5hc"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="dXP-bt-7l1"/>
                    <connections>
                        <outlet property="inputBtn" destination="M9I-2g-ts5" id="Vlx-hQ-15C"/>
                        <outlet property="lightBtn" destination="Zkb-Jn-ddm" id="RCm-k6-BXK"/>
                        <outlet property="locationBtn" destination="V5Q-MG-ys2" id="hgj-Pa-iPb"/>
                        <outlet property="topOfCloseBtn" destination="vm5-pS-5hc" id="EYI-q9-z4g"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="vv0-TS-vLM" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-442.39999999999998" y="3994.4527736131936"/>
        </scene>
        <!--No Claim Work Flow Scan Free View Controller-->
        <scene sceneID="Dv0-H6-aaU">
            <objects>
                <viewController storyboardIdentifier="NoClaimWorkFlowScanFreeViewController" hidesBottomBarWhenPushed="YES" id="n5a-QL-nJJ" customClass="NoClaimWorkFlowScanFreeViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="b5o-FL-f7a">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="eUX-Bh-VEn">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Nzc-yt-WlR" customClass="PreviewView">
                                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                    </view>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="J8p-zO-ApS">
                                        <rect key="frame" x="16" y="67" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="40" id="w3z-Sj-c6O"/>
                                            <constraint firstAttribute="height" constant="40" id="zRJ-6R-4kz"/>
                                        </constraints>
                                        <state key="normal" image="scan_close"/>
                                        <connections>
                                            <action selector="closeBtnAction:" destination="n5a-QL-nJJ" eventType="touchUpInside" id="pi1-Yi-rsz"/>
                                        </connections>
                                    </button>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="MoS-GF-Ijn">
                                        <rect key="frame" x="329" y="660" width="48" height="176"/>
                                        <subviews>
                                            <button hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="J4e-LL-HkL">
                                                <rect key="frame" x="0.0" y="-48" width="48" height="48"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="48" id="PVS-fc-ezx"/>
                                                    <constraint firstAttribute="width" constant="48" id="jC0-0Z-M4I"/>
                                                </constraints>
                                                <state key="normal" image="scan_trim"/>
                                                <connections>
                                                    <action selector="onClickTrim:" destination="n5a-QL-nJJ" eventType="touchUpInside" id="xTi-51-0ai"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="q7p-oU-ANW">
                                                <rect key="frame" x="0.0" y="0.0" width="48" height="48"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="48" id="9Rr-G4-7ov"/>
                                                    <constraint firstAttribute="width" constant="48" id="tQg-sm-rsP"/>
                                                </constraints>
                                                <state key="normal" image="scan_location"/>
                                                <connections>
                                                    <action selector="onClickLocation:" destination="n5a-QL-nJJ" eventType="touchUpInside" id="UbX-b3-TiW"/>
                                                    <action selector="onClickLocation:" destination="XrS-qi-yVH" eventType="touchUpInside" id="W8r-Ts-oa2"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="mCy-dt-3j0">
                                                <rect key="frame" x="0.0" y="64" width="48" height="48"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="48" id="8Hp-me-4P6"/>
                                                    <constraint firstAttribute="width" constant="48" id="pKc-vJ-HyJ"/>
                                                </constraints>
                                                <state key="normal" image="sacan_input"/>
                                                <connections>
                                                    <action selector="onClickInput:" destination="n5a-QL-nJJ" eventType="touchUpInside" id="dAk-6y-Od7"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="fLd-CU-OE3">
                                                <rect key="frame" x="0.0" y="128" width="48" height="48"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="48" id="2IN-Z3-32u"/>
                                                    <constraint firstAttribute="width" constant="48" id="mDl-3g-TVM"/>
                                                </constraints>
                                                <state key="normal" image="light-bulb-off"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                        <real key="value" value="24"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="setLightOnOrOff:" destination="n5a-QL-nJJ" eventType="touchUpInside" id="Oga-kC-KX2"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="48" id="TSh-eE-oMc"/>
                                        </constraints>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="bottom" secondItem="Nzc-yt-WlR" secondAttribute="bottom" id="0iV-nD-FNA"/>
                                    <constraint firstAttribute="bottom" secondItem="MoS-GF-Ijn" secondAttribute="bottom" constant="16" id="DIm-uh-I5v"/>
                                    <constraint firstItem="Nzc-yt-WlR" firstAttribute="top" secondItem="eUX-Bh-VEn" secondAttribute="top" id="HJ5-hS-SjA"/>
                                    <constraint firstItem="Nzc-yt-WlR" firstAttribute="leading" secondItem="eUX-Bh-VEn" secondAttribute="leading" id="a7c-gd-jst"/>
                                    <constraint firstAttribute="trailing" secondItem="MoS-GF-Ijn" secondAttribute="trailing" constant="16" id="aaf-Gk-9QD"/>
                                    <constraint firstAttribute="trailing" secondItem="Nzc-yt-WlR" secondAttribute="trailing" id="fZw-Kr-yrJ"/>
                                    <constraint firstItem="J8p-zO-ApS" firstAttribute="leading" secondItem="eUX-Bh-VEn" secondAttribute="leading" constant="16" id="rml-6l-CF5"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="q98-WW-w1n"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="eUX-Bh-VEn" secondAttribute="bottom" id="ELe-PS-0fm"/>
                            <constraint firstItem="q98-WW-w1n" firstAttribute="trailing" secondItem="eUX-Bh-VEn" secondAttribute="trailing" id="Jng-WL-pJL"/>
                            <constraint firstItem="J8p-zO-ApS" firstAttribute="top" secondItem="q98-WW-w1n" secondAttribute="top" constant="8" id="VI8-7h-Iiw"/>
                            <constraint firstItem="eUX-Bh-VEn" firstAttribute="leading" secondItem="q98-WW-w1n" secondAttribute="leading" id="j9L-WG-ON6"/>
                            <constraint firstItem="eUX-Bh-VEn" firstAttribute="top" secondItem="b5o-FL-f7a" secondAttribute="top" id="r8s-eh-IW1"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="zCf-sb-sL5"/>
                    <connections>
                        <outlet property="closeBtn" destination="J8p-zO-ApS" id="ZLP-GW-rWz"/>
                        <outlet property="inputBtn" destination="mCy-dt-3j0" id="4Ly-fT-NHm"/>
                        <outlet property="lightBtn" destination="fLd-CU-OE3" id="D3Y-YO-XiI"/>
                        <outlet property="locationBtn" destination="q7p-oU-ANW" id="nk7-uV-7Sj"/>
                        <outlet property="previewView" destination="eUX-Bh-VEn" id="5KY-JF-CKe"/>
                        <outlet property="previewWorkflow" destination="Nzc-yt-WlR" id="T4o-oH-XlE"/>
                        <outlet property="topOfCloseBtn" destination="VI8-7h-Iiw" id="3Ps-10-eqE"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="oAX-w1-pyw" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="344.80000000000001" y="3993.5532233883064"/>
        </scene>
        <!--Work Flow Scan View Controller-->
        <scene sceneID="vJ8-aq-yLI">
            <objects>
                <viewController storyboardIdentifier="WorkFlowScanViewController" id="Ltk-QO-FzB" customClass="WorkFlowScanViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="zcy-Ds-9mO">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="hhh-G7-7zH">
                                <rect key="frame" x="329" y="626" width="48" height="176"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="mj9-m9-9yq">
                                        <rect key="frame" x="0.0" y="0.0" width="48" height="48"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="48" id="dlm-bs-9tm"/>
                                            <constraint firstAttribute="width" constant="48" id="t8P-QA-BYO"/>
                                        </constraints>
                                        <state key="normal" image="scan_location"/>
                                        <connections>
                                            <action selector="locationSetting:" destination="Ltk-QO-FzB" eventType="touchUpInside" id="NOY-D2-Qpg"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="zZf-v6-9Ue">
                                        <rect key="frame" x="0.0" y="64" width="48" height="48"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="48" id="BIM-A7-EZr"/>
                                            <constraint firstAttribute="width" constant="48" id="aRx-og-ESo"/>
                                        </constraints>
                                        <state key="normal" image="sacan_input"/>
                                        <connections>
                                            <action selector="editByManualBtnAction:" destination="Ltk-QO-FzB" eventType="touchUpInside" id="wlv-51-8tp"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="GkK-eC-XVt">
                                        <rect key="frame" x="0.0" y="128" width="48" height="48"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="48" id="7Xq-OV-2Hf"/>
                                            <constraint firstAttribute="height" constant="48" id="Z5u-Tw-VDx"/>
                                        </constraints>
                                        <state key="normal" image="light-bulb-off"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                <real key="value" value="24"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="setLightOnOrOff:" destination="Ltk-QO-FzB" eventType="touchUpInside" id="uTT-sV-abW"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="width" constant="48" id="oVK-Ro-i2q"/>
                                </constraints>
                            </stackView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="mTa-QK-iaC">
                                <rect key="frame" x="16" y="67" width="40" height="40"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="40" id="CYI-Lf-6um"/>
                                    <constraint firstAttribute="width" constant="40" id="zlp-v0-4iB"/>
                                </constraints>
                                <state key="normal" image="scan_close"/>
                                <connections>
                                    <action selector="closeBtnAction:" destination="Ltk-QO-FzB" eventType="touchUpInside" id="NZ5-uc-THq"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="TPm-7I-5ud"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="mTa-QK-iaC" firstAttribute="top" secondItem="TPm-7I-5ud" secondAttribute="top" constant="8" id="6wN-Cy-Wpk"/>
                            <constraint firstItem="TPm-7I-5ud" firstAttribute="bottom" secondItem="hhh-G7-7zH" secondAttribute="bottom" constant="16" id="CdC-9C-x3v"/>
                            <constraint firstItem="mTa-QK-iaC" firstAttribute="leading" secondItem="TPm-7I-5ud" secondAttribute="leading" constant="16" id="TLE-hg-XMW"/>
                            <constraint firstItem="TPm-7I-5ud" firstAttribute="trailing" secondItem="hhh-G7-7zH" secondAttribute="trailing" constant="16" id="e4b-G7-MEx"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="clostBtn" destination="mTa-QK-iaC" id="MUM-H1-B7Y"/>
                        <outlet property="editBtn" destination="zZf-v6-9Ue" id="I5J-K3-eDA"/>
                        <outlet property="lightBtn" destination="GkK-eC-XVt" id="Kwj-wC-gnC"/>
                        <outlet property="locationBtn" destination="mj9-m9-9yq" id="Ua1-i5-qF1"/>
                        <outlet property="topOfCloseBtn" destination="6wN-Cy-Wpk" id="8Wj-b1-QD5"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="rnT-1b-M17" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-455.19999999999999" y="1078.1109445277361"/>
        </scene>
        <!--New Work Flow Scan Free View Controller-->
        <scene sceneID="81H-NQ-ZWp">
            <objects>
                <viewController storyboardIdentifier="NewWorkFlowScanFreeViewController" id="hlx-eU-kKg" customClass="NewWorkFlowScanFreeViewController" customModule="App" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="D5h-pA-1fW">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="LG8-yi-dfE">
                                <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Ivc-WY-wJf" customClass="PreviewView">
                                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                    </view>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" alignment="center" spacing="16" translatesAutoresizingMaskIntoConstraints="NO" id="bOb-TO-CbO">
                                        <rect key="frame" x="329" y="660" width="48" height="176"/>
                                        <subviews>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="x3y-3d-O8G">
                                                <rect key="frame" x="0.0" y="0.0" width="48" height="48"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="48" id="O3J-gw-imZ"/>
                                                    <constraint firstAttribute="height" constant="48" id="Svg-aJ-Dbm"/>
                                                </constraints>
                                                <state key="normal" image="scan_location"/>
                                                <connections>
                                                    <action selector="locationSetting:" destination="hlx-eU-kKg" eventType="touchUpInside" id="sNZ-QX-cJ2"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="TT8-gZ-Dd1">
                                                <rect key="frame" x="0.0" y="64" width="48" height="48"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="48" id="Rko-Rp-WuH"/>
                                                    <constraint firstAttribute="width" constant="48" id="bbf-Kr-97T"/>
                                                </constraints>
                                                <state key="normal" image="sacan_input"/>
                                                <connections>
                                                    <action selector="editByManualBtnAution:" destination="hlx-eU-kKg" eventType="touchUpInside" id="QXV-QI-shz"/>
                                                </connections>
                                            </button>
                                            <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ii7-9A-NbB">
                                                <rect key="frame" x="0.0" y="128" width="48" height="48"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="48" id="6yv-c3-ssd"/>
                                                    <constraint firstAttribute="width" constant="48" id="PrH-m5-TDG"/>
                                                </constraints>
                                                <state key="normal" image="light-bulb-off"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                        <real key="value" value="24"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="setLightOnOrOff:" destination="Gxw-hl-3bP" eventType="touchUpInside" id="SpZ-Hz-vhg"/>
                                                    <action selector="setLightOnOrOff:" destination="hlx-eU-kKg" eventType="touchUpInside" id="xdl-7n-ynJ"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="48" id="svy-a5-4Sx"/>
                                        </constraints>
                                    </stackView>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="2Qv-mM-NlK">
                                        <rect key="frame" x="16" y="67" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="40" id="cCT-Ua-xgg"/>
                                            <constraint firstAttribute="width" constant="40" id="uJh-Ar-T15"/>
                                        </constraints>
                                        <state key="normal" image="scan_close"/>
                                        <connections>
                                            <action selector="closeBtnAction:" destination="hlx-eU-kKg" eventType="touchUpInside" id="uhr-Fx-U6O"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" white="0.66666666669999997" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="Ivc-WY-wJf" firstAttribute="leading" secondItem="LG8-yi-dfE" secondAttribute="leading" id="62I-oo-ptn"/>
                                    <constraint firstAttribute="trailing" secondItem="bOb-TO-CbO" secondAttribute="trailing" constant="16" id="7Ln-nH-GN7"/>
                                    <constraint firstAttribute="trailing" secondItem="Ivc-WY-wJf" secondAttribute="trailing" id="Afn-Og-Hey"/>
                                    <constraint firstItem="2Qv-mM-NlK" firstAttribute="leading" secondItem="LG8-yi-dfE" secondAttribute="leading" constant="16" id="BhO-DH-dS4"/>
                                    <constraint firstAttribute="bottom" secondItem="bOb-TO-CbO" secondAttribute="bottom" constant="16" id="l01-4a-vIZ"/>
                                    <constraint firstAttribute="bottom" secondItem="Ivc-WY-wJf" secondAttribute="bottom" id="l0i-Rs-bac"/>
                                    <constraint firstItem="Ivc-WY-wJf" firstAttribute="top" secondItem="LG8-yi-dfE" secondAttribute="top" id="zVo-mg-ivd"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="zL8-Qx-wq1"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="LG8-yi-dfE" firstAttribute="top" secondItem="D5h-pA-1fW" secondAttribute="top" id="KcR-ah-ETs"/>
                            <constraint firstItem="LG8-yi-dfE" firstAttribute="leading" secondItem="zL8-Qx-wq1" secondAttribute="leading" id="O3a-zw-at2"/>
                            <constraint firstItem="zL8-Qx-wq1" firstAttribute="trailing" secondItem="LG8-yi-dfE" secondAttribute="trailing" id="YyL-Sy-et2"/>
                            <constraint firstItem="2Qv-mM-NlK" firstAttribute="top" secondItem="zL8-Qx-wq1" secondAttribute="top" constant="8" id="arH-AN-eLk"/>
                            <constraint firstAttribute="bottom" secondItem="LG8-yi-dfE" secondAttribute="bottom" id="mMo-8F-h7w"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="closeBtn" destination="2Qv-mM-NlK" id="z2S-kP-Two"/>
                        <outlet property="lightBtn" destination="ii7-9A-NbB" id="y7f-oF-KSP"/>
                        <outlet property="locationBtn" destination="x3y-3d-O8G" id="0wZ-Zv-QoU"/>
                        <outlet property="previewAction" destination="Ivc-WY-wJf" id="2sc-sO-ukL"/>
                        <outlet property="previewView" destination="LG8-yi-dfE" id="H1x-8V-vfd"/>
                        <outlet property="topOfCloseBtn" destination="arH-AN-eLk" id="Jzp-2Q-UKK"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="n6H-Ka-nan" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1964" y="1075"/>
        </scene>
    </scenes>
    <resources>
        <image name="light-bulb-off" width="100" height="100"/>
        <image name="sacan_input" width="100" height="100"/>
        <image name="scan_close" width="50" height="50"/>
        <image name="scan_location" width="100" height="100"/>
        <image name="scan_trim" width="100" height="100"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
