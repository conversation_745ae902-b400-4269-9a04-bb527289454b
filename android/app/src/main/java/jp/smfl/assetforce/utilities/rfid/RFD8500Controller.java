package jp.smfl.assetforce.utilities.rfid;

import static java.util.concurrent.TimeUnit.SECONDS;

import android.Manifest;
import android.app.Activity;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.media.AudioManager;
import android.media.ToneGenerator;
import android.os.AsyncTask;
import android.os.Build;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;

import com.zebra.rfid.api3.ACCESS_OPERATION_STATUS;
import com.zebra.rfid.api3.BATCH_MODE;
import com.zebra.rfid.api3.BEEPER_VOLUME;
import com.zebra.rfid.api3.ENUM_TRANSPORT;
import com.zebra.rfid.api3.HANDHELD_TRIGGER_EVENT_TYPE;
import com.zebra.rfid.api3.IEvents;
import com.zebra.rfid.api3.InvalidUsageException;
import com.zebra.rfid.api3.OperationFailureException;
import com.zebra.rfid.api3.RFIDReader;
import com.zebra.rfid.api3.ReaderDevice;
import com.zebra.rfid.api3.Readers;
import com.zebra.rfid.api3.RfidEventsListener;
import com.zebra.rfid.api3.RfidReadEvents;
import com.zebra.rfid.api3.RfidStatusEvents;
import com.zebra.rfid.api3.STATUS_EVENT_TYPE;
import com.zebra.rfid.api3.TagData;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.TreeMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import jp.smfl.assetforce.rfid.adapters.RfidConnectionListAdapter;
import jp.smfl.assetforce.utilities.rfid.common.BaseController;
import jp.smfl.assetforce.utilities.rfid.common.InventoryListItem;
import jp.smfl.assetforce.utilities.rfid.common.MaxLimitArrayList;
import jp.smfl.assetforce.utilities.rfid.common.ResponseHandlerTask;
import jp.smfl.assetforce.utilities.rfid.common.RfidTag;
import jp.smfl.assetforce.utilities.rfid.inter.BluetoothEventListener;
import jp.smfl.assetforce.utilities.rfid.inter.IRFIDConstrant;
import jp.smfl.assetforce.utilities.rfid.inter.RFIDDeviceListener;

/**
 * Created by Kishor on 12/31/2015.
 */
public class RFD8500Controller extends BaseController implements RfidEventsListener, DeviceConnectTask.IRFIDConnectTaskHandlers, IRFIDConstrant {

  public static final String TAG = "blog-RFIDController";
  public static final Integer UNIQUE_TAGS = 0;
  /**
   * 连接的扫码器设备对象
   */
  private RFIDReader connectedReader;
  /**
   * 连接到到设备
   */
  protected ReaderDevice connectedDevice;
  private ArrayList<ReaderDevice> readersList = new ArrayList<>();

  private static final Object MUTEX = new Object();
  private static volatile RFD8500Controller instance;
  protected Readers readers;
  private boolean isBatchModeInventoryRunning;
  private boolean isInventoryRunning;
  private DeviceConnectTask connectTask;
  private BluetoothEventListener bluetoothEventListener;
  private IEvents.BatteryData batteryData;
  private volatile TreeMap<String, Integer> inventoryList = new TreeMap<String, Integer>();
  private volatile CopyOnWriteArrayList<InventoryListItem> tagsReadInventory = new MaxLimitArrayList();
  private int totalTags = 0;
  private int uniqueTags = 0;

  private boolean bFound = false;
  private boolean pc = false;
  private boolean rssi = false;
  private boolean phase = false;
  private boolean channelIndex = false;
  private boolean brandidcheckenabled = false;
  private int inventoryMode = 0;
  public ScheduledExecutorService tbeep;
  private boolean beepON = false;
  public ToneGenerator toneGenerator;
  private boolean isBeepEnable = false;

  // Beeper
  public static BEEPER_VOLUME beeperVolume = BEEPER_VOLUME.HIGH_BEEP;
  public static BEEPER_VOLUME sledBeeperVolume = BEEPER_VOLUME.HIGH_BEEP;
  private IntentFilter bluetoothIntentFilter;
  private boolean isBordercastRegisted = false;
  protected RfidConnectionListAdapter.ReaderDeviceBean mCurrentConnectedDeviceBean;

  private RFIDDeviceListener deviceListener;

  private boolean isScannerEnable = false;

  /**
   * 互斥单例构造
   *
   * @return
   */
  public static RFD8500Controller getInstance() {
    RFD8500Controller result = instance;
    if (result == null) {
      synchronized (MUTEX) {
        result = instance;
        if (result == null) {
          instance = result = new RFD8500Controller();
        }
      }
    }
    return result;
  }

  /**
   * 初始化readers
   *
   * @param ctx
   */
  public Readers createReaders(@NonNull Context ctx) {
    if (readers == null) {
      readers = new Readers(ctx, ENUM_TRANSPORT.BLUETOOTH);
    }
    return readers;
  }

  @Override
  public void initActivity(FragmentActivity activity) {
    this.activityWeakReference = new WeakReference<>(activity);
    initReader(activity);
  }

  public Readers getReaders() {
    return readers;
  }

  public void closeReaders() {
    readers.Dispose();
    readers = null;
  }

  public synchronized void connectRfidDevice(Context context, ReaderDevice device, boolean isConnectedFromIonic) {
    if (device.getRFIDReader() != null && !device.getRFIDReader().isConnected()) {
      this.isConnectedFromIonic = isConnectedFromIonic;
      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB) {
        connectTask = new DeviceConnectTask(context, device, "Conecting...", null, this);
        connectTask.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR);
      } else {
        connectTask = new DeviceConnectTask(context, device, "Conecting...", null, this);
        connectTask.execute();
      }
    }
  }

  public synchronized void disconnectRfidDevice() {
    Log.d(TAG, "disconnect " + connectedReader);
    try {
      if (connectedReader != null) {
        // 移除设备事件回调
        connectedReader.Events.removeEventsListener(this);
        // 设备断开连接
        connectedReader.disconnect();
        if (deviceListener != null) {
          deviceListener.onDisconnected(getConnectedDeviceBean());
        }
      }
    } catch (InvalidUsageException e) {
      e.printStackTrace();
    } catch (OperationFailureException e) {
      e.printStackTrace();
    } catch (Exception e) {
      e.printStackTrace();
    } finally {
      clearData(true);
    }
  }

  /**
   * 清理连接数据
   */
  private void clearData(boolean isClearTagData) {
    if (isClearTagData) {
      clearInventTags();
    }
    clearBatteryData();
    clearConnectedDevice();
    clearConnectedReader();
  }

  private void clearConnectedDevice() {
    connectedDevice = null;
  }

  /**
   * 获取可用设备
   *
   * @return 返回可用设备列表，如果不存在可用返回空列表
   */
  @NonNull
  public ArrayList<ReaderDevice> getConnectableReaders() {
    if (readers != null) {
      ArrayList<ReaderDevice> availableRFIDReaderList = null;
      ArrayList<ReaderDevice> copyRFD8500List = new ArrayList<ReaderDevice>();
      try {
        availableRFIDReaderList = readers.GetAvailableRFIDReaderList();
      } catch (InvalidUsageException e) {
        e.printStackTrace();
        closeReaders();
        return new ArrayList<>();
      }
      if (availableRFIDReaderList != null && !availableRFIDReaderList.isEmpty()) {
        for(int i = 0; i < availableRFIDReaderList.size(); i++){
          if(availableRFIDReaderList.get(i).getName().startsWith("RFD8500")){
            copyRFD8500List.add(availableRFIDReaderList.get(i));
          }
        }
        return copyRFD8500List;
      }
    }
    return new ArrayList<>();
  }

  /**
   * 是否可以使用RFID，判断依据存在可用设备，不论是否连接
   *
   * @param context
   * @return
   */
  public boolean isRFIDEnable(Context context) {
    if (readers != null) {
      ArrayList<ReaderDevice> getAvailableRFIDReaderList = null;
      try {
        getAvailableRFIDReaderList = readers.GetAvailableRFIDReaderList();
      } catch (InvalidUsageException e) {
        throw new RuntimeException(e);
      }
      if (getAvailableRFIDReaderList != null) {
        return getAvailableRFIDReaderList.size() > 0;
      }
    }
    return false;
  }

  @Override
  public DeviceType getDeviceType() {
    return DeviceType.RFD8500;
  }

  @Override
  public void connect(RfidConnectionListAdapter.ReaderDeviceBean bean, boolean isConnectedFromIonic) {
    synchronized (RFD8500Controller.class) {
      if (this.activityWeakReference != null) {
        Activity activity = this.activityWeakReference.get();
        if (activity != null) {
          connectRfidDevice(activity, bean.device, isConnectedFromIonic);
        }
      }
    }
  }

  @Override
  public void disconnect() {
    disconnectRfidDevice();
  }

  @Override
  public ArrayList<RfidConnectionListAdapter.ReaderDeviceBean> getAllDevicesList() {
    ArrayList<RfidConnectionListAdapter.ReaderDeviceBean> list = new ArrayList<>();
    ArrayList<ReaderDevice> connectableReaders = getConnectableReaders();
    ReaderDevice connectedDevice = getConnectedDevice();
    for (ReaderDevice readerDevice : connectableReaders) {
      boolean connected = isConnected();
      String readerDeviceName = readerDevice.getName();
      DeviceType type = null;
      if (readerDeviceName != null && readerDeviceName.startsWith("RFD8500")) {
        type = DeviceType.RFD8500;
        Log.d(TAG, "(RFD8500Controller.java:282): " + (connected && connectedDevice != null && readerDevice.getAddress().equals(connectedDevice.getAddress())) + "设备名：" + readerDeviceName);
        list.add(new RfidConnectionListAdapter.ReaderDeviceBean(connected && connectedDevice != null && readerDevice.getAddress().equals(connectedDevice.getAddress()), readerDevice,type));
      }
    }
    return list;
  }

  @Override
  public void setDeviceListener(RFIDDeviceListener listener) {
    this.deviceListener = listener;
  }

  @Override
  public void resetData() {
    clearData(true);
  }

  public boolean isConnected() {
    if (connectedReader != null) {
      return connectedReader.isConnected();
    }
    return false;
  }

  @Override
  @Nullable
  public RfidConnectionListAdapter.ReaderDeviceBean getConnectedDeviceBean() {
    return getDeviceBean();
  }

  @Nullable
  public RFIDReader getConnectedReader() {
    return connectedReader;
  }

  public void setConnectedReader(RFIDReader connectedReader) {
    this.connectedReader = connectedReader;
  }

  @Nullable
  public ReaderDevice getConnectedDevice() {
    return connectedDevice;
  }

  /**
   * @return 获取当前连接设备的ReaderDevicebean对象（可空，如果未连接获取对象为null）
   */
  @Nullable
  public RfidConnectionListAdapter.ReaderDeviceBean getDeviceBean() {
    if (connectedDevice != null) {
      ArrayList<ReaderDevice> connectableReaders = RFD8500Controller.getInstance().getConnectableReaders();
      for (ReaderDevice readerDevice : connectableReaders) {
        boolean connected = isConnected();
        DeviceType type = null;
        if (readerDevice.getName().startsWith("RFD8500"))  {
          type = DeviceType.RFD8500;
        }
        mCurrentConnectedDeviceBean = new RfidConnectionListAdapter.ReaderDeviceBean(connected && connectedDevice != null && readerDevice.getAddress().equals(connectedDevice.getAddress()), readerDevice,type);
      }
    } else {
      mCurrentConnectedDeviceBean = null;
    }
    return mCurrentConnectedDeviceBean;
  }

  @Override
  public void eventReadNotify(RfidReadEvents rfidReadEvents) {
    if (connectedReader != null) {
      TagData[] myTags = connectedReader.Actions.getReadTags(100);
      if (myTags != null) {
        for (TagData myTag : myTags) {
          if (myTag != null && (myTag.getOpStatus() == null || myTag.getOpStatus() == ACCESS_OPERATION_STATUS.ACCESS_SUCCESS)) {
            new ResponseHandlerTask(myTag).execute();
          }
        }
      }
    }
  }

  @Override
  public void eventStatusNotify(RfidStatusEvents rfidStatusEvents) {
// 断开连接
    if (rfidStatusEvents.StatusEventData.getStatusEventType() == STATUS_EVENT_TYPE.DISCONNECTION_EVENT) {
      if (deviceListener != null) {
        deviceListener.onDisconnected(getDeviceBean());
      }
      // RFIDController.mConnectedReader = null
    }
    // 进入库存扫描状态
    else if (rfidStatusEvents.StatusEventData.getStatusEventType() == STATUS_EVENT_TYPE.INVENTORY_START_EVENT) {
      if (deviceListener != null) {
        deviceListener.isInventoryState(true);
      }
    }
    // 退出库存扫描状态
    else if (rfidStatusEvents.StatusEventData.getStatusEventType() == STATUS_EVENT_TYPE.INVENTORY_STOP_EVENT) {
      if (deviceListener != null) {
        deviceListener.isInventoryState(false);
      }
    }
    // 操作结束总结事件
    else if (rfidStatusEvents.StatusEventData.getStatusEventType() == STATUS_EVENT_TYPE.OPERATION_END_SUMMARY_EVENT) {
      Log.d(TAG, "(RFD8500Controller.java:403) eventStatusNotify: OPERATION_END_SUMMARY_EVENT");
    }
    // 扳机事件 按下和释放
    else if (rfidStatusEvents.StatusEventData.getStatusEventType() == STATUS_EVENT_TYPE.HANDHELD_TRIGGER_EVENT) {
      boolean triggerPressed = rfidStatusEvents.StatusEventData.HandheldTriggerEventData.getHandheldEvent() == HANDHELD_TRIGGER_EVENT_TYPE.HANDHELD_TRIGGER_PRESSED;
      Log.d(TAG, "triggerState: " + triggerPressed);
      if (!isScannerEnable) {
        return;
      }
      if (triggerPressed) {
        try {
          getConnectedReader().Actions.Inventory.perform();
          setInventoryRunning(true);
        } catch (InvalidUsageException e) {
          e.printStackTrace();
        } catch (OperationFailureException e) {
          e.printStackTrace();
        }
      } else {
        try {
          getConnectedReader().Actions.Inventory.stop();
          setInventoryRunning(false);
        } catch (InvalidUsageException e) {
          e.printStackTrace();
        } catch (OperationFailureException e) {
          e.printStackTrace();
        }
      }
      if (deviceListener != null) {
        deviceListener.isTriggerState(triggerPressed);
      }
    }
    // 电池电量事件
    else if (rfidStatusEvents.StatusEventData.getStatusEventType() == STATUS_EVENT_TYPE.BATTERY_EVENT) {
      batteryData = rfidStatusEvents.StatusEventData.BatteryData;
      Log.d(TAG, "batteryState: " + batteryData);
      if (deviceListener != null) {
        deviceListener.batteryState(batteryData.getLevel(), batteryData.getCharging(), batteryData.getCause(), DeviceType.RFD8500);
      }
    }
    // 批处理模式事件
    else if (rfidStatusEvents.StatusEventData.getStatusEventType() == STATUS_EVENT_TYPE.BATCH_MODE_EVENT) {
      Log.d(TAG, "[jp.smfl.assetforce.utilities.rfid.RFIDController.java:328] eventStatusNotify: STATUS_EVENT_TYPE.BATCH_MODE_EVENT");
    }
  }

  public void setBatchModeInventoryRunning(boolean batchModeInventoryRunning) {
    isBatchModeInventoryRunning = batchModeInventoryRunning;
  }

  public boolean isInventoryRunning() {
    return isInventoryRunning;
  }

  public void setInventoryRunning(boolean inventoryRunning) {
    isInventoryRunning = inventoryRunning;
  }

  public boolean isBatchModeInventoryRunning() {
    return isBatchModeInventoryRunning;
  }

  @Override
  public void storeConnectedReader() {
    Log.d(TAG, "StoreConnectedReader: ");
  }

  @Override
  public void readerDeviceConnected(ReaderDevice device) {
    Log.d(TAG, "ReaderDeviceConnected: " + device);
    connectedDevice = device;
    if (connectedDevice != null) {
      connectedReader = connectedDevice.getRFIDReader();
      if (connectedReader != null && connectedReader.Config != null) {
        try {
          Log.d(TAG, "ReaderDeviceConnected: " + connectedReader.Config.getBatchModeConfig());
          // 设备连接成功，获取电量信息
          connectedReader.Config.getDeviceStatus(true, false, false);
          // 设备batch mode设置不是auto时，重置未auto
          if (connectedReader.Config.getBatchModeConfig().getValue() != 1) {
            connectedReader.Config.setBatchMode(BATCH_MODE.AUTO);
          }
        } catch (InvalidUsageException e) {
          e.printStackTrace();
        } catch (OperationFailureException e) {
          e.printStackTrace();
        }
      }
    }
    if (deviceListener != null) {
      deviceListener.onConnected(getDeviceBean());
    }
  }

  @Override
  public void sendNotification(String action, String data) {
    Log.d(TAG, "sendNotification: " + action + "/" + data);
  }

  @Override
  public void readerDeviceConnFailed(ReaderDevice device) {
    Log.d(TAG, "ReaderDeviceConnFailed: " + device);
    if (getInstance().activityWeakReference != null && !isConnectedFromIonic) {
      try {
        Activity activity = this.activityWeakReference.get();
        if (activity != null) {
          new AlertDialog
            .Builder(activity)
            .setMessage("スキャナーとの接続が不可になっているので、スキャナーを再起動して下さい。")
            .setCancelable(true)
            .setPositiveButton("OK", (dialog, which) -> {
              if (deviceListener != null) {
                deviceListener.onConnectedField(DeviceType.RFD8500);
              }
              dialog.dismiss();
            }).show();
        }
      } catch (Exception e) {
        if (deviceListener != null) {
          deviceListener.onConnectedField(DeviceType.RFD8500);
        }
      }
    } else {
      if (deviceListener != null) {
        deviceListener.onConnectedField(DeviceType.RFD8500);
      }
    }
  }

  @Override
  public void onTaskDataCleanUp() {

  }

  @Override
  public void showPasswordDialog(ReaderDevice connectingDevice) {

  }

  @Override
  public void cancelReconnect() {

  }

  @Override
  public void setConnectionProgressState(boolean b) {

  }

  @Override
  public void setReaderConfig() {
  }

  public IEvents.BatteryData getBatteryData() {
    return batteryData;
  }

  public void addTotalTags(int tagSeenCount) {
    totalTags += tagSeenCount;
  }

  public void addTotalTags() {
    totalTags++;
  }

  public int getTotalTags() {
    return totalTags;
  }

  public void startbeepingTimer() {
    if (isBeepEnable && beeperVolume != BEEPER_VOLUME.QUIET_BEEP && !beepON) {
      beepON = true;
      beep();
      if (tbeep == null) {
        tbeep = Executors.newScheduledThreadPool(1);
        tbeep.schedule(() -> {
          stopbeepingTimer();
          beepON = false;
        }, 300, TimeUnit.MILLISECONDS);
      }
    }
  }

  public void stopbeepingTimer() {
    if (tbeep != null && toneGenerator != null) {
      toneGenerator.stopTone();
      tbeep.shutdownNow();
    }
    tbeep = null;
  }

  public void beep() {
    if (toneGenerator != null) {
      int toneType = ToneGenerator.TONE_PROP_BEEP;
      toneGenerator.startTone(toneType);
    } else {
      try {
        toneGenerator = new ToneGenerator(AudioManager.STREAM_DTMF, 100);
        beep();
      } catch (RuntimeException e) {
        e.printStackTrace();
        toneGenerator = null;
      }
    }
  }

  /**
   * 清除扫码cache数据
   */
  public void clearInventTags() {
    if (connectedReader != null && connectedReader.isConnected()) {
      try {
        connectedReader.Actions.Inventory.stop();
      } catch (InvalidUsageException e) {
        e.printStackTrace();
      } catch (OperationFailureException e) {
        e.printStackTrace();
      }
    }
    if (tagsReadInventory != null) {
      tagsReadInventory.clear();
    }
    if (inventoryList != null) {
      inventoryList.clear();
    }
    totalTags = 0;
    uniqueTags = 0;
  }

  @Override
  public ArrayList<RfidTag> getInventTags() {
    CopyOnWriteArrayList<InventoryListItem> list = getTagsReadInventory();
    return list.stream().map(inventoryListItem -> new RfidTag(inventoryListItem.getTagID())).collect(Collectors.toCollection(ArrayList::new));
  }

  @Override
  public void startReceivingScanData() {
    isScannerEnable = true;
  }

  @Override
  public void stopReveivingScanData() {
    if (isConnected()) {
      if (getConnectedReader() != null) {
        try {
          getConnectedReader().Actions.Inventory.stop();
        } catch (InvalidUsageException e) {
          e.printStackTrace();
        } catch (OperationFailureException e) {
          e.printStackTrace();
        }
      }
    }
    isScannerEnable = false;
  }

  @Override
  public void requestBatteryState() {
    if (this.batteryData != null) {
      Log.d(TAG, "batteryState: " + this.batteryData);
      if (deviceListener != null) {
        deviceListener.batteryState(this.batteryData.getLevel(), this.batteryData.getCharging(), this.batteryData.getCause(), DeviceType.RFD8500);
      }
    }
  }

  private ScheduledExecutorService scheduler;
  private ScheduledFuture<?> taskHandle;

  public void startTimer() {
    if (scheduler == null) {
      scheduler = Executors.newScheduledThreadPool(1);
      final Runnable task = () -> {
        try {
          if (connectedDevice != null) {
            connectedReader.Config.getDeviceStatus(true, true, false);
          } else {
            stopTimer();
          }
        } catch (InvalidUsageException | OperationFailureException e) {
          e.printStackTrace();
        }
      };
      taskHandle = scheduler.scheduleAtFixedRate(task, 0, 60, SECONDS);
    }
  }

  /**
   * method to stop timer
   */
  public void stopTimer() {
    if (taskHandle != null) {
      taskHandle.cancel(true);
      scheduler.shutdown();
    }
    taskHandle = null;
    scheduler = null;
  }

  public TreeMap<String, Integer> getInventoryList() {
    return inventoryList;
  }

  public CopyOnWriteArrayList<InventoryListItem> getTagsReadInventory() {
    return tagsReadInventory;
  }

  public boolean isbFound() {
    return bFound;
  }

  public void setbFound(boolean bFound) {
    this.bFound = bFound;
  }

  public boolean isPc() {
    return pc;
  }

  public void setPc(boolean pc) {
    this.pc = pc;
  }

  public boolean isRssi() {
    return rssi;
  }

  public void setRssi(boolean rssi) {
    this.rssi = rssi;
  }

  public boolean isPhase() {
    return phase;
  }

  public void setPhase(boolean phase) {
    this.phase = phase;
  }

  public boolean isChannelIndex() {
    return channelIndex;
  }

  public void setChannelIndex(boolean channelIndex) {
    this.channelIndex = channelIndex;
  }

  public boolean isBrandidcheckenabled() {
    return brandidcheckenabled;
  }

  public void setBrandidcheckenabled(boolean brandidcheckenabled) {
    this.brandidcheckenabled = brandidcheckenabled;
  }

  public int getInventoryMode() {
    return inventoryMode;
  }

  public void setInventoryMode(int inventoryMode) {
    this.inventoryMode = inventoryMode;
  }

  public int getUniqueTags() {
    return uniqueTags;
  }

  public void setUniqueTags(int uniqueTags) {
    this.uniqueTags = uniqueTags;
  }

  public void addUniqueTags() {
    uniqueTags++;
  }

  public void setBeepEnable(boolean beepEnable) {
    isBeepEnable = beepEnable;
  }

  private BroadcastReceiver bluetoothReciver;

  /**
   * 注册蓝牙广播监听器及声明监听类型
   *
   * @param context 全局上下文
   */
  public void registeBordercastReciver(Context context) {
    if (isBordercastRegisted) {
      return;
    }
    Log.d(TAG, "registeBordercastReciver: ");
    isBordercastRegisted = true;
    bluetoothIntentFilter = new IntentFilter();
    bluetoothIntentFilter.addAction(BluetoothAdapter.ACTION_STATE_CHANGED);
    bluetoothIntentFilter.addAction(BluetoothDevice.ACTION_FOUND);
    bluetoothIntentFilter.addAction(BluetoothAdapter.ACTION_DISCOVERY_STARTED);
    bluetoothIntentFilter.addAction(BluetoothAdapter.ACTION_DISCOVERY_FINISHED);
    bluetoothIntentFilter.addAction(BluetoothDevice.ACTION_BOND_STATE_CHANGED);
    bluetoothIntentFilter.addAction(BluetoothDevice.ACTION_ACL_CONNECTED);
    bluetoothIntentFilter.addAction(BluetoothDevice.ACTION_ACL_DISCONNECT_REQUESTED);
    bluetoothIntentFilter.addAction(BluetoothDevice.ACTION_ACL_DISCONNECTED);
    ContextCompat.registerReceiver(context, genBluetoothReciver(), bluetoothIntentFilter, ContextCompat.RECEIVER_NOT_EXPORTED);
  }

  /**
   * 获取蓝牙广播监听器
   *
   * @return BroadcastReceiver蓝牙广播监听器，用于接受蓝牙状态变化回调（蓝牙开闭状态、蓝牙搜索状态、找到设备、配对状态、链接状态）
   */
  private BroadcastReceiver genBluetoothReciver() {
    bluetoothReciver = new BroadcastReceiver() {
      @Override
      public void onReceive(Context context, Intent intent) {
        try {
          String action = intent.getAction();
          Log.d(TAG, "BT  " + action);
          switch (action) {
            case BluetoothAdapter.ACTION_STATE_CHANGED:
              int state = intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, BluetoothAdapter.ERROR);
              Log.d(TAG, "onReceive: ACTION_STATE_CHANGED  isOn:" + (state == BluetoothAdapter.STATE_ON));
              if (state == BluetoothAdapter.STATE_OFF) {
                clearData(false);
              } else if (state == BluetoothAdapter.STATE_TURNING_OFF) {
                Log.d(TAG, "[jp.smfl.assetforce.utilities.rfid.RFIDController.java:760] onReceive: BluetoothAdapter.STATE_TURNING_OFF");
              }
              if (bluetoothEventListener != null) {
                bluetoothEventListener.stateChange(state);
              }
              break;
            case BluetoothAdapter.ACTION_DISCOVERY_STARTED:
              Log.d(TAG, "onReceive: ACTION_DISCOVERY_STARTED");
              if (bluetoothEventListener != null) {
                bluetoothEventListener.discoverStated();
              }
              break;
            case BluetoothAdapter.ACTION_DISCOVERY_FINISHED:
              Log.d(TAG, "onReceive: ACTION_DISCOVERY_FINISHED");
              if (bluetoothEventListener != null) {
                bluetoothEventListener.discoverFinished();
              }
              break;
            case BluetoothDevice.ACTION_FOUND:
              BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
              Activity act = activityWeakReference.get();
              if (act == null || ActivityCompat.checkSelfPermission(act, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                return;
              }
              Log.d(TAG, "onReceive: ACTION_FOUND  name:" + device.getName() + "  mac:" + device.getName());
              if (bluetoothEventListener != null) {
                bluetoothEventListener.deviceFound(device);
              }
              break;
            case BluetoothDevice.ACTION_BOND_STATE_CHANGED:
              state = intent.getIntExtra(BluetoothDevice.EXTRA_BOND_STATE, BluetoothDevice.ERROR);
              final int prevState = intent.getIntExtra(BluetoothDevice.EXTRA_PREVIOUS_BOND_STATE, BluetoothDevice.ERROR);
              if (prevState == BluetoothDevice.BOND_BONDING) {
                Log.d(TAG, "onReceive: BOND_BONDING");
              } else if (prevState == BluetoothDevice.BOND_BONDED) {
                Log.d(TAG, "onReceive: BOND_BONDED");
              }
              if (bluetoothEventListener != null) {
                bluetoothEventListener.boundStateChanged(state, prevState);
              }
              break;
            case BluetoothDevice.ACTION_ACL_CONNECTED:
              Log.d(TAG, "onReceive: device connected");
              break;
            case BluetoothDevice.ACTION_ACL_DISCONNECT_REQUESTED:
            case BluetoothDevice.ACTION_ACL_DISCONNECTED:
              Log.d(TAG, "onReceive: device disconnected");
              BluetoothDevice device1 = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
              if (bluetoothEventListener != null) {
                bluetoothEventListener.deviceDisconnected(device1);
              }
              disconnectRfidDevice();
              break;
            default:
          }
        } catch (Exception ex) {
          Log.d(TAG, "receiver" + ex.getMessage());
          ex.printStackTrace();
          BluetoothAdapter.getDefaultAdapter().cancelDiscovery();
        }
      }
    };
    return bluetoothReciver;
  }

  private void clearConnectedReader() {
    connectedReader = null;
  }

  private void clearBatteryData() {
    batteryData = null;
  }

  public void unregisgeBordercastReciver(Context context) {
    if (isBordercastRegisted && bluetoothReciver != null) {
      Log.d(TAG, "unregisgeBordercastReciver:");
      context.unregisterReceiver(bluetoothReciver);
      bluetoothReciver = null;
    }
  }

  public void setBluetoothEventListener(BluetoothEventListener bluetoothEventListener) {
    this.bluetoothEventListener = bluetoothEventListener;
  }

  @Override
  public void initReader(Context context) {
    createReaders(context);
  }

  public RFIDDeviceListener getDeviceListener() {
    return deviceListener;
  }
}
