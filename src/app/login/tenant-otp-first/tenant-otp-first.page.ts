import { Component, OnInit, ChangeDetectorRef } from '@angular/core';
import { NavController } from '@ionic/angular';
import { StorageUtils } from 'src/app/utils/storage-utils';
import { LoginService } from '../../services/login/login.service';
import { HttpLoginService } from '../../httpUtils/login/http-login.service';
import { AlertController } from '@ionic/angular';
import { NavigationExtras, ActivatedRoute } from '@angular/router';
import { LoginUser } from '../../models/user';
import { StringUtilsService } from '../../utils/string-utils.service';
import { HttpRequestUtilsService } from '../../utils/httpRequest-utils.service';
import { Role } from 'src/app/models/role';
import { Throttle } from 'src/app/utils/throttle.decorator';
import { Keyboard } from '@capacitor/keyboard';
// 可能没有地方调用，需要测试
// 可能没有地方调用，需要测试
// 可能没有地方调用，需要测试
@Component({
  selector: 'app-tenant-otp-first',
  templateUrl: './tenant-otp-first.page.html',
  styleUrls: ['./tenant-otp-first.page.scss'],
})
// 可能没有地方调用，需要测试
// 可能没有地方调用，需要测试
// 可能没有地方调用，需要测试
export class TenantOtpFirstPage implements OnInit {
  isLoading = false; // ログイン中
  userName: string; //ユーザ名
  password: string; //パスワード
  errMsg: string; //エラーメッセージ
  tel: string; //携帯電話番号
  nationCodeStr = '+81'; //(表示用)国番号(デフォルト:+81)
  nationCode = '81'; //国番号(デフォルト:81)
  verifyCode: string; //ワンタイムパスワード
  applicationInterval: any; //ワンタイムパスワードの取得間隔
  nextBtnText: string; //次のボタンテキスト
  btnClick = false; //ボタンをクリックしてもいいですか
  userId: string; //ユーザID
  ticket: string; //チケット
  tenantId: string; //tenantId
  isEmail = false; //メール認証
  constructor(
    private route: ActivatedRoute,
    private navController: NavController,
    private loginService: LoginService,
    private httpRequestUtils: HttpRequestUtilsService,
    private nav: NavController,
    public alertController: AlertController,
    private httpUtils: HttpLoginService,
    private ref: ChangeDetectorRef,
    private stringUtilsService: StringUtilsService,
  ) {
    this.route.queryParams.subscribe(async (params) => {
      if (params['userId'] != undefined && params['userId'] != null) {
        this.userId = params['userId'];
      }
      if (params['password'] != undefined && params['password'] != null) {
        this.password = params['password'];
      }
      if (params['ticket'] != undefined && params['ticket'] != null) {
        this.ticket = params['ticket'];
      }
      if (params['tenantId'] != undefined && params['tenantId'] != null) {
        this.tenantId = params['tenantId'];
      }
      if (params['isEmail'] != undefined && params['isEmail'] != null) {
        this.isEmail = params['isEmail'];
      }
    });
    StorageUtils.get(StorageUtils.KEY_USER_NAME).then((userName) => {
      this.userName = userName;
    });
  }

  /**
   * ページ初期化
   * @param：なし
   */
  ngOnInit() {}

  /**
   * ページを離れる
   * @param ：なし
   */
  ionViewDidLeave() {
    //時間の計測を停止る
    clearInterval(this.applicationInterval);
    this.nextBtnText = '';
    this.btnClick = false;
  }

  /**
   * ワンタイムパスワードボタンをクリック
   */
  @Throttle()
  getVerificationCode() {
    if (this.isEmail) {
      this.getEmailCode();
    } else {
      if (!this.tel && !this.nationCode) {
        alert('携帯電話番号を入力して下さい。');
      } else if (!this.nationCode) {
        alert('国番号を入力してください。');
      } else if (!this.tel) {
        alert('携帯電話番号を入力して下さい。');
      } else {
        this.getCode();
      }
    }
  }

  /**
   * 時間を計る
   * 60秒カウントダウン
   */
  countDown() {
    const applicationPageOpenData: number = parseInt((new Date().getTime() / 1000 + 60).toString()); //120是设置的秒数
    let nowDte: number;
    this.nextBtnText = '60s';
    this.btnClick = true;
    this.applicationInterval = setInterval(() => {
      nowDte = parseInt((new Date().getTime() / 1000).toString());
      console.log(nowDte);
      const receiveDate = applicationPageOpenData - nowDte;
      if (receiveDate > 0) {
        const tss = this.s_to_hs(receiveDate);
        this.nextBtnText = tss + 's';

        console.log(this.s_to_hs(receiveDate));
      } else {
        this.nextBtnText = '';
        this.btnClick = false;
        clearInterval(this.applicationInterval);
      }
    }, 1000);
  }

  s_to_hs(s) {
    let h;
    h = Math.floor(s / 60);
    s = s % 60;
    h += '';
    s += '';
    h = h.length === 1 ? '0' + h : h;
    s = s.length === 1 ? '0' + s : s;
    console.log(h + ':' + s);
    return s;
  }

  /**
   * @description:ワンタイムパスワード
   * @param {type}
   */
  async getCode() {
    this.loginService
      .getVerificationCode(this.tel, this.nationCode.replace('-', ''), this.ticket)
      .then(() => {
        this.errMsg = 'ワンタイムパスワード送信完了しました。';
        this.countDown();
      })
      .catch((error) => {
        if (error.signInResult) {
          this.errMsg = error.signInResult;
        } else {
          this.errMsg = 'ワンタイムパスワード送信失敗しました。';
        }
      });
  }

  async getEmailCode() {
    this.loginService
      .getEmailVerificationCode(this.userName, this.ticket)
      .then(() => {
        this.errMsg = 'ワンタイムパスワード送信完了しました。';
        this.countDown();
      })
      .catch((error) => {
        if (error.signInResult) {
          this.errMsg = error.signInResult;
        } else {
          this.errMsg = 'ワンタイムパスワード送信失敗しました。';
        }
      });
  }

  /**
   * 電話番号と国番号編集画面に遷移する
   */
  @Throttle()
  toPhoneNumber() {
    const navigationExtras: NavigationExtras = {
      queryParams: {
        phoneNumber: this.tel,
        nationCode: '81',
        callback: (data) => {
          console.log(data);
          // データを更新する
          // リフレッシュ

          if (data.nationCode === undefined) {
            this.nationCode = '';
            this.nationCodeStr = '';
            this.tel = '';
          } else {
            this.nationCode = data.nationCode;
            this.nationCodeStr = '+' + this.nationCode;
            this.tel = data.phoneNumber;
            console.log(this.tel);
          }
          this.ref.detectChanges();
        },
      },
    };
    this.navController.navigateForward('/edit-item/phone-number', navigationExtras);
  }

  /**
   * ログイン
   * 条件の判断
   */
  @Throttle()
  login() {
    console.log('login🍑');
    if (!this.tel && !this.nationCode && !this.isEmail) {
      alert('携帯電話番号を入力して下さい。');
    } else if (!this.nationCode && !this.isEmail) {
      alert('国番号を入力してください。');
    } else if (!this.tel && !this.isEmail) {
      alert('携帯電話番号を入力して下さい。');
    } else {
      Keyboard.hide();
      this.isLoading = true;
      setTimeout(() => {
        this.isLoading = false;
        if (!this.verifyCode) {
          alert('ワンタイムパスワードを入力して下さい。');
        } else {
          const reg = /^(0|[1-9]\d*)$/;
          // 数字入力ありの場合は、数字の有効性チェック
          if (this.verifyCode !== undefined && this.verifyCode.length > 0 && !reg.test(this.verifyCode)) {
            alert(StorageUtils.KEY_ONE_TIME_PASSWORD_ERROR);
            this.verifyCode = '';
            this.ref.detectChanges();
          } else {
            this.toLogin();
          }
        }
      }, 1000);
    }
  }

  /**
   * ログイン
   */
  async toLogin() {
    this.isLoading = true;
    // ログイン
    this.httpUtils
      .httpUserBindPhone(
        this.userName.trim(),
        this.password.trim(),
        this.tel,
        Number(this.nationCode.replace('-', '')),
        this.verifyCode,
        this.userId,
        this.tenantId,
        this.ticket
      )
      .then(
        (dataObject) => {
          this.loginDo(dataObject);
        },
        (err) => {
          this.loginErrDo(err);
        },
      );
  }

  loginDo(dataObject) {
    const loginUser: LoginUser = dataObject.user;

    // 有効期間チェックする
    if (loginUser.recoverableLimit !== undefined) {
      if (loginUser.recoverableLimit != null && loginUser.recoverableLimit !== '') {
        // 有効期間近づく場合の処理
        this.stringUtilsService.getDifferentDaysAndCheckLimit(loginUser.recoverableLimit);
      }
    }
    // ユーザー情報保存
    const tenantId = dataObject.user.tenantId;
    const scanType = dataObject.user.scanType;
    const userId = dataObject.user.userId;
    const userName = dataObject.user.userName;
    const firstName = dataObject.user.firstName;
    const lastName = dataObject.user.lastName;
    const refreshToken = dataObject.mapToken.refreshToken;
    const token = dataObject.mapToken.accessToken;
    const tel = dataObject.user.tel;
    const nationCode = dataObject.user.nationCode;

    if (tenantId === 'TENANT_ADMIN') {
      alert(
        'モバイルアプリでは「テナント管理者用メニュー」にログインすることは出来ません。ウェブブラウザからのログインをお願いいたします。',
      );
      return;
    }
    console.log(dataObject);
    StorageUtils.set(StorageUtils.KEY_TOKEN, token);
    StorageUtils.set(StorageUtils.KEY_REFRESH_TOKEN, refreshToken);
    StorageUtils.set(StorageUtils.KEY_USER_NAME, userName);
    StorageUtils.set(StorageUtils.KEY_USER_ID, userId);
    
    StorageUtils.set(StorageUtils.KEY_TENANT_ID, tenantId);
    StorageUtils.set(StorageUtils.KEY_FIRST_NAME, firstName);
    StorageUtils.set(StorageUtils.KEY_LAST_NAME, lastName);
    StorageUtils.set(StorageUtils.KEY_ENABLE_TWO_STEP, true);
    StorageUtils.set(StorageUtils.KEY_TEL + this.userName, tel);
    StorageUtils.set(StorageUtils.KEY_NATION_CODE + this.userName, nationCode);
    StorageUtils.set(StorageUtils.KEY_BARCODE_EXTRACTION, dataObject.barcodeExtraction || '');

    //権限情報
    let userRoleList: [Role] = dataObject.userRoleList;
    StorageUtils.set(StorageUtils.KEY_USER_ROLE_LIST, userRoleList);

    // OCR
    StorageUtils.set(StorageUtils.KEY_OCR_INFO, dataObject.dppInfo);
    StorageUtils.set(StorageUtils.KEY_OCR_EUNOMIA_INFO, dataObject.eunomiaInfo);

    // デフォルトのロケーション情報があれば、そのまま引き継ぎ利用
    this.loginService.setDefaultLocation(loginUser);
    // 場所リストをとる
    this.httpRequestUtils.getLocationList(false).then((locationList) => {
      console.log(locationList);
    });

    this.isLoading = false;
    this.nav.navigateForward('/tabs/my-home');
  }

  loginErrDo(err) {
    this.isLoading = false;
    if (err.newPasswordRequired) {
      // TODO: パスワード変更が必要な場合(初回登録とか)は、パスワード変更画面へ遷移する
    } else if (
      err.signInResult ===
      'assetforceをご利用いただきありがとうございます。\n有効期限が過ぎているためご利用になれません。\nテナント管理者へご連絡お願いします。'
    ) {
      alert('有効期限が過ぎました。\nテナント管理者へご連絡お願いします。');
    } else if (err.signInResult) {
      alert(err.signInResult);
    }
  }
}
