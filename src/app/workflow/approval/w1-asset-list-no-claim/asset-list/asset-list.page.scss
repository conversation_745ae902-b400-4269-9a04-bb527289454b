.right {
  text-align: right;
  margin-bottom: 0px;
  color: white;
  font-weight: bold;
  font-size: 0.9rem;
}

.left {
  text-align: left;
  margin-bottom: 0px;
  color: white;
  font-weight: bold;
  font-size: 0.9rem;
}

.center {
  margin-bottom: 0px;
  color: white;
  font-weight: bold;
  font-size: 0.9rem;
}

.divMain {
  float: left;
  width: 100%;
  margin-bottom: 10px;
}

.divLeft {
  float: left;
  width: 50%;
  padding-right: 8px;
}

.divRight {
  float: right;
  width: 50%;
  padding-left: 8px;
}

.divCenter {
}

.overflow {
  text-overflow: ellipsis;
  overflow: hidden;
}

table {
  table-layout: fixed;
}

td {
  @extend .overflow;
  word-break: break-word;
}

figure {
  > ion-img {
    border-radius: 3px;
    width: 50px;
    height: 50px;
    object-fit: cover;
  }
}

$default-width-height-image: 50px;
$spinner-size: 28px;

.homeImage {
  position: relative;

  > ion-spinner {
    z-index: 2;
    position: absolute;
    left: calc(#{$default-width-height-image} / 2 - (#{$spinner-size} / 2));
    top: calc(#{$default-width-height-image} / 2 - (#{$spinner-size} / 2));
  }

  > ion-img {
    z-index: 1;
    width: #{$default-width-height-image};
    height: #{$default-width-height-image};

    &::part(image) {
      border-radius: 3px;
      object-fit: cover;
    }
  }
}
.list-table-item-wi-th {
  width: 8rem;
}
.list-table-item-wi-tab {
  width: 100%;
  max-width: none;
}

.phantomContainer {
  position: relative;
}

.actualContent {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.invisible-scrollbar::-webkit-scrollbar {
  display: none;
}

.v-middle {
  vertical-align: middle !important;
}

.grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 9px;
}

.v-list-container {
  padding-left: unset !important;
  padding-right: unset !important;
}

.v-list-item {
  margin-left: 10px !important;
  margin-right: 10px !important;
  margin-top: 10px !important;
}

.clear-padding-ltr {
  padding-top: 0;
  padding-left: 0;
  padding-right: 0;
}

.margin-lr-10-b-0 {
  margin-left: 10px;
  margin-right: 10px;
  margin-bottom: 0 !important;
}

.clear-padding-ltr {
  padding-top: 0;
  padding-left: 0;
  padding-right: 0;
}

.margin-lr-10-b-0 {
  margin-left: 10px;
  margin-right: 10px;
  margin-bottom: 0 !important;
}
