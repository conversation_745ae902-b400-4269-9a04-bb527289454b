export interface TaskFormInterface {
  msg: string;
  totalSavedAssetAmountCount: number;
  productAssetTypeId: any;
  noScanningAssetList: NoScanningAssetList[];
  updateAmountShow: boolean;
  subformId: any;
  wfAssignDynamicTasks: any;
  nonSubProcess: boolean;
  hasScanTask: boolean;
  hasSubProcess: boolean;
  subProcessProductAssetList: any;
  processDefinitionId: string;
  workflowEngineAmountProcess: WorkflowEngineAmountProcess;
  elementClassification: string;
  hasPassedSubProcess: boolean;
  assignDynamicFlag: boolean;
  inputAssetListFlag: string;
  isAssigneeTaskAlsoInSubProcess: boolean;
  userOrGroupId: any;
  subProcessDetails: any;
  subProcessReportInfoList: any[];
  subformVersion: any;
  taskName: string;
  actions: Actions;
  noScanningAssetCount: number;
  reportInfoList: any[];
  assetTypeIdWithMultiScan: number;
  code: number;
  isCurrentTaskInSubProcess: boolean;
  workflowScript: string;
  assetTypeName: string;
  userOrGroupList: any[];
  allAssetCount: number;
  assetListDatas: AssetListData[];
  thirdWfProcessDefinitionId: any;
  taskType: string;
  assetListId: number;
  scannedAssetList: ScannedAssetList[];
  groupSectionFlg: boolean;
  totalAssetAmountCount: number;
  productAssetTypeName: string;
  assetListTitle: any;
  isFirstWfWithAssetList: boolean;
  assignDynamicType: string;
  autoFetchSearchId: any;
  workflowName: string;
  workflowLogicScript: string;
  currentUserSubProcessTaskIsCompleted: boolean;
  isRentalWorkflow: boolean;
  form: Form[];
  workflowState: string;
  tenantKbn: any;
}

export interface NoScanningAssetList {
  assetListId: number;
  assetId: number;
  tenantId: any;
  processId: any;
  processState: any;
  processStateText: any;
  assetText: string;
  assetCount: any;
  createdById: any;
  createdDate: any;
  modifiedById: any;
  modifiedDate: any;
  scanState: string;
  scanSort: any;
  scanTarget: any;
  barcode: string;
  processStepName: any;
  mpFlag: any;
  parentAssets: any;
  assetAmount: number;
  savedAssetAmount: number;
  oldAssetText: string;
  assetItemList: any;
  location: any;
}

export interface WorkflowEngineAmountProcess {
  isUpdateAmount: string;
  amountActionTaskUpdateItemId: any;
  amountActionTaskUpdateItemName: any;
  amountActionAssetList: any;
  amountActionTaskExecuted: any;
}

export interface Actions {
  tenantId: string;
  workflowId: number;
  taskDefKey: string;
  rejectionStatus: any;
  sendBackStatus: string;
  sendBackDestination: string;
  createdById: any;
  createdDate: any;
  modifiedById: any;
  modifiedDate: any;
  isPermission: boolean;
  isSuspended: boolean;
  cancelStatus: boolean;
  isMyselfInputTask: boolean;
  possibleOfClaim: boolean;
  canBeCommitScanTask: boolean;
  taskName: any;
  workflowName: any;
  isMultiScanTask: boolean;
  allMustScan: boolean;
  scanTaskIsLocked: boolean;
  saveTemporaryButtonWithScanTask: boolean;
}

export interface AssetListData {
  assetListId: number;
  assetId: number;
  tenantId: any;
  processId: any;
  processState: any;
  processStateText: any;
  assetText: string;
  assetCount: any;
  createdById: any;
  createdDate: any;
  modifiedById: any;
  modifiedDate: any;
  scanState: string;
  scanSort: any;
  scanTarget: any;
  barcode: string;
  processStepName: any;
  mpFlag: any;
  parentAssets: any;
  assetAmount: number;
  savedAssetAmount: number;
  oldAssetText: string;
  assetItemList: AssetItemList[];
  location: string;
}

export interface AssetItemList {
  itemId: number;
  itemDisplayName: string;
  itemType: string;
  value: string;
}

export interface ScannedAssetList {
  assetListId: number;
  assetId: number;
  tenantId: any;
  processId: any;
  processState: any;
  processStateText: any;
  assetText: string;
  assetCount: any;
  createdById: any;
  createdDate: any;
  modifiedById: any;
  modifiedDate: any;
  scanState: string;
  scanSort: any;
  scanTarget: any;
  barcode: string;
  processStepName: any;
  mpFlag: any;
  parentAssets: any;
  assetAmount: number;
  savedAssetAmount: number;
  oldAssetText: string;
  assetItemList: any;
  location: any;
}

export interface Form {
  formField?: FormField;
  workflowForm: WorkflowForm;
  variable: string;
}

export interface FormField {
  businessKey: boolean;
  id: string;
  label: any;
  type: Type;
  defaultValue: any;
  value: Value;
  validationConstraints: any[];
  properties: Properties;
  typeName: string;
}

export interface Type {
  name: string;
}

export interface Value {
  value: string;
  type: Type2;
  transient: boolean;
}

export interface Type2 {
  name: string;
  javaType: string;
  primitiveValueType: boolean;
  parent: any;
  abstract: boolean;
}

export interface Properties {}

export interface WorkflowForm {
  tenantId?: string;
  workflowId?: number;
  engineId?: string;
  itemId?: number;
  itemName: string;
  itemDisplayName: any;
  itemType: string;
  option?: string;
  defaultData: any;
  inputFlg?: string;
  mobileFlg?: string;
  sectionName?: string;
  sectionType?: string;
  sectionSort?: number;
  positionX?: number;
  positionY?: number;
  width?: number;
  height?: number;
  createdById?: string;
  createdDate?: string;
  modifiedById?: string;
  modifiedDate?: string;
  itemIdStr?: string;
  uneditableFlg: any;
  dbtype: any;
}

export interface NoScanningAssetRewListInterface {
  assetItems: AssetItemList[];
  name: string;
  id: string;
  assetId: number;
  location: string;
  homeImage: HomeImageItemInterface;
  finishScan: boolean;
  rawAsset: AssetListData;
  assetScanedCount: number;
  assetTotalCount: number;
}

export interface HomeImageItemInterface {
  loaded: boolean;
  fileName: string;
  uploadDate: string;
  isHomeImage: boolean;
  turl: string;
}

export interface SetPageListPushInterface {
  search?: string;
}

export interface GetPageListPullInterface {
  assetData: { assetList: NoScanningAssetRewListInterface[]; searchNum: number };
}
