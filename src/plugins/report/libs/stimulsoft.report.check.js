var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            let StiCheckStatus;
            (function (StiCheckStatus) {
                StiCheckStatus[StiCheckStatus["ReportRenderingMessage"] = 0] = "ReportRenderingMessage";
                StiCheckStatus[StiCheckStatus["Information"] = 1] = "Information";
                StiCheckStatus[StiCheckStatus["Warning"] = 2] = "Warning";
                StiCheckStatus[StiCheckStatus["Error"] = 3] = "Error";
            })(StiCheckStatus = Check.StiCheckStatus || (Check.StiCheckStatus = {}));
            let StiCheckObjectType;
            (function (StiCheckObjectType) {
                StiCheckObjectType[StiCheckObjectType["Report"] = 0] = "Report";
                StiCheckObjectType[StiCheckObjectType["Page"] = 1] = "Page";
                StiCheckObjectType[StiCheckObjectType["Component"] = 2] = "Component";
                StiCheckObjectType[StiCheckObjectType["Database"] = 3] = "Database";
                StiCheckObjectType[StiCheckObjectType["DataSource"] = 4] = "DataSource";
                StiCheckObjectType[StiCheckObjectType["DataRelation"] = 5] = "DataRelation";
                StiCheckObjectType[StiCheckObjectType["DataColumn"] = 6] = "DataColumn";
                StiCheckObjectType[StiCheckObjectType["Variable"] = 7] = "Variable";
            })(StiCheckObjectType = Check.StiCheckObjectType || (Check.StiCheckObjectType = {}));
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiComponent = Stimulsoft.Report.Components.StiComponent;
            var StiPage = Stimulsoft.Report.Components.StiPage;
            class StiCheck {
                constructor() {
                    this.element = null;
                    this.status = null;
                    this.objectType = null;
                    this.defaultStateEnabled = true;
                    this.actions = [];
                }
                get previewVisible() {
                    return false;
                }
                get elementName() {
                    return null;
                }
                get shortMessage() {
                    return null;
                }
                get longMessage() {
                    return null;
                }
                get enabled() {
                    return this.defaultStateEnabled;
                }
                set enabled(value) {
                }
                processCheck(report, obj) {
                    return null;
                }
                createPreviewImage(refElementImage, refHighlightedElementImage) {
                    let elementImage = null;
                    let highlightedElementImage = null;
                    let comp = this.element.stimulsoft().as(StiComponent);
                    if (comp != null) {
                        if (this.element.stimulsoft().is(StiPage)) {
                        }
                        else {
                        }
                        if (!(this.element.stimulsoft().is(StiPage))) {
                            let page = comp.page;
                            let rect = comp.getPaintRectangle(false, false);
                            rect.y += page.margins.top;
                            rect.x += page.margins.left;
                            let pageRect = page.displayRectangle.clone();
                            let factorX = 500 / pageRect.width;
                            let factorY = 500 / pageRect.height;
                            rect.x *= factorX;
                            rect.y *= factorY;
                            rect.width *= factorX;
                            rect.height *= factorY;
                        }
                    }
                    refElementImage.ref = elementImage;
                    refHighlightedElementImage.ref = highlightedElementImage;
                }
            }
            Check.StiCheck = StiCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiArray = Stimulsoft.System.StiArray;
            var StiString = Stimulsoft.System.StiString;
            var StiLocalization = Stimulsoft.Base.Localization.StiLocalization;
            class CheckComparer {
                compare(check1, check2) {
                    if (check1.status == Check.StiCheckStatus.Error) {
                        if (check2.status == Check.StiCheckStatus.Error)
                            return 0;
                        else
                            return -1;
                    }
                    else if (check1.status == Check.StiCheckStatus.Warning) {
                        if (check2.status == Check.StiCheckStatus.Error)
                            return 1;
                        else if (check2.status == Check.StiCheckStatus.Information)
                            return -1;
                        else
                            return 0;
                    }
                    else {
                        if (check2.status == Check.StiCheckStatus.Error || check2.status == Check.StiCheckStatus.Warning)
                            return 1;
                        else
                            return 0;
                    }
                }
            }
            class StiCheckEngine {
                invokeFinishCheckingReport() {
                }
                invokeStartCheckingPages() {
                }
                invokeCheckingPages() {
                }
                invokeFinishCheckingPages() {
                }
                invokeStartCheckingComponents() {
                }
                invokeCheckingComponents() {
                }
                invokeFinishCheckingComponents() {
                }
                invokeStartCheckingDatabases() {
                }
                invokeCheckingDatabases() {
                }
                invokeFinishCheckingDatabases() {
                }
                invokeStartCheckingDataSource() {
                }
                invokeCheckingDataSource() {
                }
                invokeFinishCheckingDataSource() {
                }
                invokeStartCheckingRelations() {
                }
                invokeCheckingRelations() {
                }
                invokeFinishCheckingRelations() {
                }
                invokeStartCheckingVariables() {
                }
                invokeCheckingVariables() {
                }
                invokeFinishCheckingVariables() {
                }
                static get checks() {
                    if (this._checks == null)
                        this.createChecks();
                    return this._checks;
                }
                get progressValue() {
                    return this._progressValue;
                }
                get progressMaximum() {
                    return this._progressMaximum;
                }
                get progressInformation() {
                    return this._progressInformation;
                }
                static createChecks() {
                    this._checks = [];
                    this._checks.push(new Check.StiDuplicatedNameInSourceCheck());
                    this._checks.push(new Check.StiExpressionElementCheck());
                    this._checks.push(new Check.StiFilterCircularDependencyElementCheck());
                    this._checks.push(new Check.StiDuplicatedNameCheck());
                    this._checks.push(new Check.StiNoNamePageCheck());
                    this._checks.push(new Check.StiComponentStyleIsNotFoundAtPageCheck());
                    this._checks.push(new Check.StiNoNameComponentCheck());
                    this._checks.push(new Check.StiUndefinedComponentCheck());
                    this._checks.push(new Check.StiUndefinedConnectionCheck());
                    this._checks.push(new Check.StiDifferentAmountOfKeysInDataRelationCheck());
                    this._checks.push(new Check.StiKeysInAbsentDataRelationCheck());
                    this._checks.push(new Check.StiKeysNotFoundRelationCheck());
                    this._checks.push(new Check.StiKeysTypesMismatchDataRelationCheck());
                    this._checks.push(new Check.StiNoNameDataRelationCheck());
                    this._checks.push(new Check.StiNoNameInSourceDataRelationCheck());
                    this._checks.push(new Check.StiNoNameDataSourceCheck());
                    this._checks.push(new Check.StiNoNameInSourceDataSourceCheck());
                    this._checks.push(new Check.StiUndefinedDataSourceCheck());
                    this._checks.push(new Check.StiCalculatedColumnRecursionCheck());
                    this._checks.push(new Check.StiVariableRecursionCheck());
                    this._checks.push(new Check.StiLostPointsOnPageCheck());
                    this._checks.push(new Check.StiOrientationPageCheck());
                    this._checks.push(new Check.StiColumnsWidthGreaterPageWidthCheck());
                    this._checks.push(new Check.StiAllowHtmlTagsInTextCheck());
                    this._checks.push(new Check.StiCanBreakComponentInContainerCheck());
                    this._checks.push(new Check.StiCanGrowComponentInContainerCheck());
                    this._checks.push(new Check.StiGrowToHeightOverlappingCheck());
                    this._checks.push(new Check.StiComponentStyleIsNotFoundAtComponentCheck());
                    this._checks.push(new Check.StiCorruptedCrossLinePrimitiveCheck());
                    this._checks.push(new Check.StiPrintOnDoublePassCheck());
                    this._checks.push(new Check.StiLocationOutsidePageCheck());
                    this._checks.push(new Check.StiNegativeSizesOfComponentsCheck());
                    this._checks.push(new Check.StiWordWrapCanGrowTextDoesNotFitCheck());
                    this._checks.push(new Check.StiDuplicatedNameInSourceInDataRelationReportCheck());
                    this._checks.push(new Check.StiSourcesInAbsentDataRelationCheck());
                    this._checks.push(new Check.StiIsFirstPageIsLastPageDoublePassCheck());
                    this._checks.push(new Check.StiComponentBoundsAreOutOfBand());
                    this._checks.push(new Check.StiComponentDataColumnCheck());
                    this._checks.push(new Check.StiComponentExpressionCheck());
                    this._checks.push(new Check.StiTextTextFormatCheck());
                    this._checks.push(new Check.StiChartSeriesValueCheck());
                    this._checks.push(new Check.StiTotalPageCountDoublePassCheck());
                    this._checks.push(new Check.StiIsFirstPassIsSecondPassCheck());
                    this._checks.push(new Check.StiPrintHeadersAndFootersFromPreviousPageCheck());
                    this._checks.push(new Check.StiPrintOnPreviousPageCheck());
                    this._checks.push(new Check.StiPrintOnPreviousPageCheck2());
                    this._checks.push(new Check.StiResetPageNumberCheck());
                    this._checks.push(new Check.StiVerySmallSizesOfComponentsCheck());
                    this._checks.push(new Check.StiShowInsteadNullValuesCheck());
                    this._checks.push(new Check.StiMinRowsInColumnsCheck());
                    this._checks.push(new Check.StiDataSourcesForImageCheck());
                    this._checks.push(new Check.StiCanGrowGrowToHeightComponentInContainerCheck());
                    this._checks.push(new Check.StiCanGrowWordWrapTextAndWysiwygCheck());
                    this._checks.push(new Check.StiColumnsWidthGreaterContainerWidthCheck());
                    this._checks.push(new Check.StiGroupHeaderNotEqualToGroupFooterOnPageCheck());
                    this._checks.push(new Check.StiCrossGroupHeaderNotEqualToCrossGroupFooterOnPageCheck());
                    this._checks.push(new Check.StiGroupHeaderNotEqualToGroupFooterOnContainerCheck());
                    this._checks.push(new Check.StiCrossGroupHeaderNotEqualToCrossGroupFooterOnContainerCheck());
                    this._checks.push(new Check.StiContainerInEngineV2Check());
                    this._checks.push(new Check.StiSystemTextObsoleteCheck());
                    this._checks.push(new Check.StiContourTextObsoleteCheck());
                    this._checks.push(new Check.StiCountDataDataSourceAtDataBandCheck());
                    this._checks.push(new Check.StiNoConditionAtGroupCheck());
                    this._checks.push(new Check.StiWidthHeightZeroComponentCheck());
                    this._checks.push(new Check.StiTextColorEqualToBackColorCheck());
                    this._checks.push(new Check.StiTextColorEqualToBackColorCheck());
                    this._checks.push(new Check.StiFontMissingCheck());
                }
                checkReport(report) {
                    let results = [];
                    try {
                        let checksReport = [];
                        let checksReportCompilation = [];
                        let checksPage = [];
                        let checksComponent = [];
                        let checksDatabase = [];
                        let checksDataSource = [];
                        let checksDataRelation = [];
                        let checksDataColumn = [];
                        let checksVariable = [];
                        for (let check of StiCheckEngine.checks) {
                            if (!check.enabled)
                                continue;
                            switch (check.objectType) {
                                case Check.StiCheckObjectType.Report:
                                    checksReport.push(check);
                                    break;
                                case Check.StiCheckObjectType.Page:
                                    checksPage.push(check);
                                    break;
                                case Check.StiCheckObjectType.Component:
                                    checksComponent.push(check);
                                    break;
                                case Check.StiCheckObjectType.Database:
                                    checksDatabase.push(check);
                                    break;
                                case Check.StiCheckObjectType.DataSource:
                                    checksDataSource.push(check);
                                    break;
                                case Check.StiCheckObjectType.DataRelation:
                                    checksDataRelation.push(check);
                                    break;
                                case Check.StiCheckObjectType.DataColumn:
                                    checksDataColumn.push(check);
                                    break;
                                case Check.StiCheckObjectType.Variable:
                                    checksVariable.push(check);
                                    break;
                            }
                        }
                        StiCheckEngine.checkObject(report, report, results, checksReport);
                        let hasErrors = false;
                        for (let repCheck of results) {
                            if (repCheck.stimulsoft().is(Check.StiDuplicatedNameCheck))
                                hasErrors = true;
                        }
                        if (!hasErrors)
                            StiCheckEngine.checkObject(report, report, results, checksReportCompilation);
                        this.invokeFinishCheckingReport();
                        this._progressMaximum = report.pages.count;
                        this._progressValue = 0;
                        this.invokeStartCheckingPages();
                        for (let page of report.pages.list) {
                            this._progressInformation = page.name;
                            this._progressValue++;
                            this.invokeCheckingPages();
                            StiCheckEngine.checkObject(report, page, results, checksPage);
                        }
                        this.invokeFinishCheckingPages();
                        this._progressMaximum = report.getComponentsCount();
                        this._progressValue = 0;
                        this.invokeStartCheckingComponents();
                        for (let page of report.pages.list) {
                            let comps = page.getComponents();
                            for (let comp of comps.list) {
                                this._progressInformation = comp.name;
                                this._progressValue++;
                                this.invokeCheckingComponents();
                                StiCheckEngine.checkObject(report, comp, results, checksComponent);
                            }
                        }
                        this.invokeFinishCheckingComponents();
                        this._progressMaximum = report.dictionary.databases.count;
                        this._progressValue = 0;
                        this.invokeStartCheckingDatabases();
                        for (let database of report.dictionary.databases.list) {
                            this._progressInformation = StiString.format("{0} - '{1}'", StiLocalization.get("QueryBuilder", "Database"), database.name);
                            this._progressValue++;
                            this.invokeCheckingDatabases();
                            StiCheckEngine.checkObject(report, database, results, checksDatabase);
                        }
                        this.invokeFinishCheckingDatabases();
                        this._progressMaximum = report.dictionary.dataSources.count;
                        this._progressValue = 0;
                        this.invokeStartCheckingDataSource();
                        for (let dataSource of report.dictionary.dataSources.list) {
                            this._progressInformation = StiString.format("{0} - '{1}'", StiLocalization.get("PropertyMain", "DataSource"), dataSource.name);
                            this._progressValue++;
                            this.invokeCheckingDataSource();
                            StiCheckEngine.checkObject(report, dataSource, results, checksDataSource);
                            for (let dataColumn of dataSource.columns.list) {
                                StiCheckEngine.checkObject(report, dataColumn, results, checksDataColumn);
                            }
                        }
                        this.invokeFinishCheckingDataSource();
                        this._progressMaximum = report.dictionary.relations.count;
                        this._progressValue = 0;
                        this.invokeStartCheckingRelations();
                        for (let dataRelation of report.dictionary.relations.list) {
                            this._progressInformation = StiString.format("{0} - '{1}'", StiLocalization.get("PropertyMain", "DataRelation"), dataRelation.name);
                            this._progressValue++;
                            this.invokeCheckingRelations();
                            StiCheckEngine.checkObject(report, dataRelation, results, checksDataRelation);
                        }
                        this.invokeFinishCheckingRelations();
                        this._progressMaximum = report.dictionary.variables.count;
                        this._progressValue = 0;
                        this.invokeStartCheckingVariables();
                        for (let variable of report.dictionary.variables.list) {
                            this._progressInformation = StiString.format("{0} - '{1}'", StiLocalization.get("PropertyMain", "Variable"), variable.name);
                            this._progressValue++;
                            this.invokeCheckingVariables();
                            StiCheckEngine.checkObject(report, variable, results, checksVariable);
                        }
                        this.invokeFinishCheckingVariables();
                        results.stimulsoft().sort2(new CheckComparer());
                    }
                    catch (_a) {
                    }
                    finally {
                        return results;
                    }
                }
                static checkObject(report, obj, results, checksObject) {
                    for (let check of checksObject) {
                        let checkObject = check.processCheck(report, obj);
                        if (checkObject != null) {
                            let createdCheck = checkObject.stimulsoft().as(Check.StiCheck);
                            if (createdCheck != null) {
                                results.push(createdCheck);
                            }
                            if (StiArray.isArray(checkObject)) {
                                for (let createdCheck2 of checkObject) {
                                    results.push(createdCheck2);
                                }
                            }
                        }
                    }
                }
                constructor() {
                    this._progressValue = 0;
                    this._progressMaximum = 0;
                    this._progressInformation = "";
                }
            }
            StiCheckEngine._checks = null;
            Check.StiCheckEngine = StiCheckEngine;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            class StiCheckHelper {
                constructor() {
                    this._errorsCount = 0;
                    this._warningsCount = 0;
                    this._informationMessagesCount = 0;
                    this._reportRenderingMessagesCount = 0;
                }
                get errorsCount() {
                    return this._errorsCount;
                }
                get warningsCount() {
                    return this._warningsCount;
                }
                get informationMessagesCount() {
                    return this._informationMessagesCount;
                }
                get reportRenderingMessagesCount() {
                    return this._reportRenderingMessagesCount;
                }
                get checks() {
                    return this._checks;
                }
                get reportRenderingMessagesChecks() {
                    return this._reportRenderingMessagesChecks;
                }
                get isMessagesPresent() {
                    return this.errorsCount > 0 ||
                        this.informationMessagesCount > 0 ||
                        this.reportRenderingMessagesCount > 0 ||
                        this.warningsCount > 0;
                }
                buildChecks(report) {
                    let targetInvocationException = null;
                    let engine = new Check.StiCheckEngine();
                    this._checks = engine.checkReport(report);
                    if (targetInvocationException != null) {
                    }
                    this._errorsCount = 0;
                    this._warningsCount = 0;
                    this._informationMessagesCount = 0;
                    for (let check of this._checks) {
                        switch (check.status) {
                            case Check.StiCheckStatus.Error:
                                this._errorsCount++;
                                break;
                            case Check.StiCheckStatus.Warning:
                                this._warningsCount++;
                                break;
                            case Check.StiCheckStatus.Information:
                                this._informationMessagesCount++;
                                break;
                        }
                    }
                }
                buildReportRenderingMessages(report) {
                    this._reportRenderingMessagesCount = 0;
                    this._reportRenderingMessagesChecks = [];
                    report = report.compiledReport != null ? report.compiledReport : report;
                    if (report.reportRenderingMessages != null && report.reportRenderingMessages.length > 0) {
                        for (let message of report.reportRenderingMessages) {
                            let check = new Check.StiReportRenderingMessageCheck();
                            check.setMessage(message);
                            this._checks.push(check);
                            this._reportRenderingMessagesChecks.push(check);
                        }
                        this._reportRenderingMessagesCount = report.reportRenderingMessages.length;
                    }
                }
            }
            Check.StiCheckHelper = StiCheckHelper;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiSettings = Stimulsoft.Base.StiSettings;
            var XmlConverter = Stimulsoft.System.Xml.XmlConverter;
            class StiLocalizationExt {
                static get cultureName() {
                    if (StiString.isNullOrEmpty(StiLocalizationExt._cultureName)) {
                        StiLocalizationExt._cultureName = StiSettings.get("cultureName", "English");
                        if (StiLocalizationExt.languages[StiLocalizationExt._cultureName] == null)
                            StiLocalizationExt._cultureName = "English";
                    }
                    return StiLocalizationExt._cultureName;
                }
                static set cultureName(value) {
                    if (StiLocalizationExt.languages[value] == null)
                        StiLocalizationExt._cultureName = "English";
                    else
                        StiLocalizationExt._cultureName = value;
                }
                static addLocalizationFile(filePath, load = false, language = "") {
                    if (load) {
                        let cultureName = StiLocalizationExt.loadLocalizationFile(filePath);
                        let language = StiLocalizationExt[cultureName]["@language"];
                        StiLocalizationExt.languages[cultureName] = {
                            language: language,
                            cultureName: cultureName,
                            filePath: filePath,
                            jsonString: JSON.stringify(StiLocalizationExt[cultureName])
                        };
                        return language;
                    }
                    else {
                        if (StiString.isNullOrEmpty(language))
                            StiLocalizationExt.languages[filePath] = { language: language, cultureName: "", filePath: filePath };
                        else
                            StiLocalizationExt.languages[language] = { language: language, cultureName: "", filePath: filePath };
                    }
                    return "";
                }
                static setLocalizationFile(filePath, onlyThis = false) {
                    if (onlyThis)
                        StiLocalizationExt.languages = {};
                    else {
                        for (let languageName in StiLocalizationExt.languages) {
                            let language = StiLocalizationExt.languages[languageName];
                            if (language.filePath == filePath) {
                                if (StiString.isNullOrEmpty(language.cultureName)) {
                                    delete StiLocalizationExt.languages[languageName];
                                    break;
                                }
                                else
                                    StiLocalizationExt.cultureName = language.cultureName;
                                return;
                            }
                        }
                    }
                    StiLocalizationExt.cultureName = StiLocalizationExt.addLocalizationFile(filePath, true);
                }
                static loadLocalization(localizationXml, extension = false) {
                    try {
                        if (localizationXml != null)
                            if (localizationXml.trim().stimulsoft().startsWith("<?xml")) {
                                let xml = XmlConverter.toXml(localizationXml);
                                return StiLocalizationExt.loadLocalizationXmlInternal(xml);
                            }
                    }
                    catch (exception) {
                        Stimulsoft.System.StiError.showError(exception.message, false);
                    }
                    return "";
                }
                static loadLocalizationFile(filePath) {
                    let file = Stimulsoft.System.IO.File.getFile(filePath);
                    if (file) {
                        return this.loadLocalization(file);
                    }
                    return "";
                }
                static loadLocalizationXmlInternal(xml) {
                    try {
                        let cultureName = xml.firstChild.getAttribute("language");
                        let enLanguage = StiLocalizationExt["English"];
                        if (enLanguage.jsonString == null)
                            enLanguage.jsonString = JSON.stringify(StiLocalizationExt["English"]);
                        StiLocalizationExt[cultureName] = JSON.parse(enLanguage.jsonString.replace(/"/gi, '"'));
                        StiLocalizationExt[cultureName]["@language"] = xml.firstChild.getAttribute("language");
                        StiLocalizationExt[cultureName]["@description"] = xml.firstChild.getAttribute("description");
                        StiLocalizationExt[cultureName]["@cultureName"] = xml.firstChild.getAttribute("cultureName");
                        for (let categoryIndex = 0; categoryIndex < xml.firstChild.childNodes.length; categoryIndex++) {
                            let categoryName = xml.firstChild.childNodes[categoryIndex].nodeName;
                            if (StiLocalizationExt[cultureName][categoryName] == null)
                                StiLocalizationExt[cultureName][categoryName] = {};
                            for (let keyIndex = 0; keyIndex < xml.firstChild.childNodes[categoryIndex].childNodes.length; keyIndex++) {
                                let keyName = xml.firstChild.childNodes[categoryIndex].childNodes[keyIndex].nodeName;
                                StiLocalizationExt[cultureName][categoryName][keyName] = xml.firstChild.childNodes[categoryIndex].childNodes[keyIndex].textContent;
                            }
                        }
                        StiLocalizationExt.languages[cultureName] = {
                            language: cultureName,
                            cultureName: StiLocalizationExt[cultureName]["@cultureName"]
                        };
                        return cultureName;
                    }
                    catch (e) {
                        Stimulsoft.System.StiError.showError(e, false);
                        return "";
                    }
                }
                static get(category, key) {
                    let language = StiLocalizationExt.languages[StiLocalizationExt.cultureName];
                    if (StiLocalizationExt[language.language] == null) {
                        StiLocalizationExt.setLocalizationFile(language.filePath);
                        delete StiLocalizationExt.languages[language.filePath];
                        language = StiLocalizationExt.languages[StiLocalizationExt.cultureName];
                    }
                    if (typeof StiLocalizationExt[StiLocalizationExt.cultureName] != "undefined") {
                        if (typeof StiLocalizationExt[StiLocalizationExt.cultureName][category] != "undefined") {
                            if (typeof StiLocalizationExt[StiLocalizationExt.cultureName][category][key] != "undefined") {
                                if (!StiString.isNullOrEmpty(StiLocalizationExt[StiLocalizationExt.cultureName][category][key]))
                                    return StiLocalizationExt[StiLocalizationExt.cultureName][category][key];
                            }
                        }
                    }
                    return key;
                }
            }
            StiLocalizationExt.languages = { English: { language: "English", cultureName: "en", filePath: "" } };
            StiLocalizationExt._cultureName = "";
            Check.StiLocalizationExt = StiLocalizationExt;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            class StiAction {
                get name() {
                    return null;
                }
                get description() {
                    return null;
                }
                invoke(report, element, elementName) {
                }
            }
            Check.StiAction = StiAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiReport = Stimulsoft.Report.StiReport;
            var StiComponent = Stimulsoft.Report.Components.StiComponent;
            class StiAllowDoublePassAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "On");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiAllowOnDoublePassActionLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    if (element.stimulsoft().is(StiReport)) {
                        element.numberOfPass = Report.StiNumberOfPass.DoublePass;
                    }
                    else if (element.stimulsoft().is(StiComponent)) {
                        element.report.numberOfPass = Report.StiNumberOfPass.DoublePass;
                    }
                }
            }
            Check.StiAllowDoublePassAction = StiAllowDoublePassAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiText = Stimulsoft.Report.Components.StiText;
            class StiAllowHtmlTagsInTextAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "On");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiAllowHtmlTagsInTextActionLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    if (element.stimulsoft().is(StiText)) {
                        element.allowHtmlTags = true;
                    }
                }
            }
            Check.StiAllowHtmlTagsInTextAction = StiAllowHtmlTagsInTextAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiText = Stimulsoft.Report.Components.StiText;
            var StiGeneralFormatService = Stimulsoft.Report.Components.TextFormats.StiGeneralFormatService;
            class StiApplyGeneralTextFormat extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiApplyGeneralTextFormatShort");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiApplyGeneralTextFormatLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    if (element.stimulsoft().is(StiText)) {
                        element.textFormat = new StiGeneralFormatService();
                    }
                }
            }
            Check.StiApplyGeneralTextFormat = StiApplyGeneralTextFormat;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiContainer = Stimulsoft.Report.Components.StiContainer;
            class StiCanBreakComponentInContainerAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "On");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiCanBreakComponentInContainerActionLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    if (element.stimulsoft().is(StiContainer)) {
                        element.canBreak = true;
                    }
                }
            }
            Check.StiCanBreakComponentInContainerAction = StiCanBreakComponentInContainerAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiContainer = Stimulsoft.Report.Components.StiContainer;
            class StiCanGrowComponentInContainerAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "On");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiCanGrowComponentInContainerActionLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    if (element.stimulsoft().is(StiContainer)) {
                        element.canGrow = true;
                    }
                }
            }
            Check.StiCanGrowComponentInContainerAction = StiCanGrowComponentInContainerAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiContainer = Stimulsoft.Report.Components.StiContainer;
            class StiCanGrowGrowToHeightComponentInContainerAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "SetGrowToHeightToTrue");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiCanGrowGrowToHeightComponentInContainerLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    let container = element.stimulsoft().as(StiContainer);
                    if (container != null) {
                        for (let comp of container.components.list) {
                            comp.growToHeight = true;
                        }
                    }
                }
            }
            Check.StiCanGrowGrowToHeightComponentInContainerAction = StiCanGrowGrowToHeightComponentInContainerAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiText = Stimulsoft.Report.Components.StiText;
            var StiTextQuality = Stimulsoft.Report.Components.StiTextQuality;
            class StiCanGrowWordWrapTextAndWysiwygAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "Change");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiCanGrowWordWrapTextAndWysiwygActionLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    if (element.stimulsoft().is(StiText)) {
                        element.textQuality = StiTextQuality.Wysiwyg;
                    }
                }
            }
            Check.StiCanGrowWordWrapTextAndWysiwygAction = StiCanGrowWordWrapTextAndWysiwygAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiPage = Stimulsoft.Report.Components.StiPage;
            var StiDataBand = Stimulsoft.Report.Components.StiDataBand;
            var StiPanel = Stimulsoft.Report.Components.StiPanel;
            class StiColumnsWidthGreaterContainerWidthAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "Change");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiColumnsWidthGreaterContainerWidthActionLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    let page = element.stimulsoft().as(StiPage);
                    let band = element.stimulsoft().as(StiDataBand);
                    let panel = element.stimulsoft().as(StiPanel);
                    if (page != null) {
                        let restWidth = page.width - (page.columns * page.columnGaps);
                        if (restWidth > 0) {
                            page.columnWidth = restWidth / page.columns;
                        }
                        else {
                            page.columnGaps = 0;
                            page.columnWidth = page.width / page.columns;
                        }
                    }
                    else if (band != null) {
                        let restWidth = band.width - (band.columns * band.columnGaps);
                        if (restWidth > 0) {
                            band.columnWidth = restWidth / band.columns;
                        }
                        else {
                            band.columnGaps = 0;
                            band.columnWidth = band.width / band.columns;
                        }
                    }
                    else if (panel != null) {
                        let restWidth = panel.width - (panel.columns * panel.columnGaps);
                        if (restWidth > 0) {
                            panel.columnWidth = restWidth / panel.columns;
                        }
                        else {
                            panel.columnGaps = 0;
                            panel.columnWidth = panel.width / panel.columns;
                        }
                    }
                }
            }
            Check.StiColumnsWidthGreaterContainerWidthAction = StiColumnsWidthGreaterContainerWidthAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiComponent = Stimulsoft.Report.Components.StiComponent;
            var IStiOddEvenStyles = Stimulsoft.Report.Components.IStiOddEvenStyles;
            class StiComponentStyleIsNotFoundAtComponentAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "Fix");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiComponentStyleIsNotFoundOnComponentActionLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    if (element.stimulsoft().is(StiComponent)) {
                        let comp = element.stimulsoft().as(StiComponent);
                        if (comp.componentStyle != "" && !comp.report.styles.contains(comp.componentStyle)) {
                            comp.componentStyle = "";
                        }
                        let oddEvenStyles = comp.stimulsoft().as(IStiOddEvenStyles);
                        if (oddEvenStyles != null) {
                            if (oddEvenStyles.evenStyle != "" && !comp.report.styles.contains(oddEvenStyles.evenStyle)) {
                                oddEvenStyles.evenStyle = "";
                            }
                            if (oddEvenStyles.oddStyle != "" && !comp.report.styles.contains(oddEvenStyles.oddStyle)) {
                                oddEvenStyles.oddStyle = "";
                            }
                        }
                    }
                }
            }
            Check.StiComponentStyleIsNotFoundAtComponentAction = StiComponentStyleIsNotFoundAtComponentAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiContainer = Stimulsoft.Report.Components.StiContainer;
            var StiPanel = Stimulsoft.Report.Components.StiPanel;
            class StiConversionContainerInPanelAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "Convert");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "Convert");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    let container = element.stimulsoft().as(StiContainer);
                    if (container != null) {
                        let panel = new StiPanel();
                        panel.parent = container.parent;
                        panel.page = container.page;
                        panel.setPaintRectangle(container.getPaintRectangle());
                        panel.clientRectangle = container.clientRectangle.clone();
                        panel.minSize = container.minSize;
                        panel.maxSize = container.maxSize;
                        panel.border = container.border;
                        panel.brush = container.brush;
                        panel.conditions = container.conditions;
                        panel.componentStyle = container.componentStyle;
                        panel.useParentStyles = container.useParentStyles;
                        panel.canGrow = container.canGrow;
                        panel.canShrink = container.canShrink;
                        panel.growToHeight = container.growToHeight;
                        panel.dockStyle = container.dockStyle;
                        panel.enabled = container.enabled;
                        panel.interaction = container.interaction;
                        panel.printable = container.printable;
                        panel.printOn = container.printOn;
                        panel.shiftMode = container.shiftMode;
                        panel.name = container.name;
                        panel.alias = container.alias;
                        panel.restrictions = container.restrictions;
                        panel.locked = container.locked;
                        panel.linked = container.linked;
                        for (let comp of container.components.list) {
                            comp.parent = panel;
                            panel.components.add(comp);
                        }
                        let parent = container.parent;
                        if (parent != null) {
                            let index = parent.components.indexOf(container);
                            if (index != -1) {
                                parent.components.removeAt(index);
                                parent.components.insert(index, panel);
                            }
                        }
                        let page = panel.page;
                        if (page != null) {
                        }
                    }
                }
            }
            Check.StiConversionContainerInPanelAction = StiConversionContainerInPanelAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiContourText = Stimulsoft.Report.Components.StiContourText;
            var StiText = Stimulsoft.Report.Components.StiText;
            class StiConversionContourTextInTextAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "Convert");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "Convert");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    let contourText = element.stimulsoft().as(StiContourText);
                    if (contourText != null) {
                        let text = new StiText();
                        text.text = contourText.text;
                        text.textBrush = contourText.textBrush;
                        text.textFormat = contourText.textFormat;
                        text.textOptions = contourText.textOptions;
                        text.textQuality = contourText.textQuality;
                        text.wordWrap = contourText.wordWrap;
                        text.renderTo = contourText.renderTo;
                        text.processAt = contourText.processAt;
                        text.processAtEnd = contourText.processAtEnd;
                        text.processingDuplicates = contourText.processingDuplicates;
                        text.nullValue = contourText.nullValue;
                        text.linesOfUnderline = contourText.linesOfUnderline;
                        text.linesOfUnderlining = contourText.linesOfUnderlining;
                        text.format = contourText.format;
                        text.font = contourText.font;
                        text.excelValue = contourText.excelValue;
                        text.angle = contourText.angle;
                        text.allowHtmlTags = contourText.allowHtmlTags;
                        text.horAlignment = contourText.horAlignment;
                        text.vertAlignment = contourText.vertAlignment;
                        text.parent = contourText.parent;
                        text.page = contourText.page;
                        text.setPaintRectangle(contourText.getPaintRectangle());
                        text.clientRectangle = contourText.clientRectangle.clone();
                        text.minSize = contourText.minSize;
                        text.maxSize = contourText.maxSize;
                        text.border = contourText.border;
                        text.brush = contourText.brush;
                        text.conditions = contourText.conditions;
                        text.componentStyle = contourText.componentStyle;
                        text.useParentStyles = contourText.useParentStyles;
                        text.canGrow = contourText.canGrow;
                        text.canShrink = contourText.canShrink;
                        text.growToHeight = contourText.growToHeight;
                        text.dockStyle = contourText.dockStyle;
                        text.enabled = contourText.enabled;
                        text.interaction = contourText.interaction;
                        text.printable = contourText.printable;
                        text.printOn = contourText.printOn;
                        text.shiftMode = contourText.shiftMode;
                        text.name = contourText.name;
                        text.alias = contourText.alias;
                        text.restrictions = contourText.restrictions;
                        text.locked = contourText.locked;
                        text.linked = contourText.linked;
                        let parent = contourText.parent;
                        if (parent != null) {
                            let index = parent.components.indexOf(contourText);
                            if (index != -1) {
                                parent.components.removeAt(index);
                                parent.components.insert(index, text);
                            }
                        }
                    }
                }
            }
            Check.StiConversionContourTextInTextAction = StiConversionContourTextInTextAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            class StiConversionSystemTextInTextAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "Convert");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "Convert");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                }
            }
            Check.StiConversionSystemTextInTextAction = StiConversionSystemTextInTextAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiComponent = Stimulsoft.Report.Components.StiComponent;
            class StiDeleteComponentAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "Delete");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiDeleteComponentActionLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    let component = element.stimulsoft().as(StiComponent);
                    if (component != null && component.parent != null) {
                        component.parent.components.remove(component);
                    }
                }
            }
            Check.StiDeleteComponentAction = StiDeleteComponentAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiCrossLinePrimitive = Stimulsoft.Report.Components.StiCrossLinePrimitive;
            var StiStartPointPrimitive = Stimulsoft.Report.Components.StiStartPointPrimitive;
            var StiEndPointPrimitive = Stimulsoft.Report.Components.StiEndPointPrimitive;
            class StiFixCrossLinePrimitiveAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "Fix");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiFixCrossLinePrimitiveActionLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    let line = element.stimulsoft().as(StiCrossLinePrimitive);
                    if (line != null) {
                        let startPoint = line.getStartPoint(line.page);
                        let endPoint = line.getEndPoint(line.page);
                        let left = line.left;
                        let top = line.top;
                        let bottom = line.bottom;
                        let right = line.right;
                        if (startPoint == null) {
                            startPoint = new StiStartPointPrimitive();
                            startPoint.referenceToGuid = line.guid;
                            startPoint.left = left;
                            startPoint.top = top;
                            startPoint.name = Report.StiNameCreation.createName(report, Report.StiNameCreation.generateName(startPoint), true, true, true);
                            line.page.components.add(startPoint);
                        }
                        if (endPoint == null) {
                            endPoint = new StiEndPointPrimitive();
                            endPoint.referenceToGuid = line.guid;
                            endPoint.left = right;
                            endPoint.top = bottom;
                            endPoint.name = Report.StiNameCreation.createName(report, Report.StiNameCreation.generateName(endPoint), true, true, true);
                            line.page.components.add(endPoint);
                        }
                        line.page.correct();
                    }
                }
            }
            Check.StiFixCrossLinePrimitiveAction = StiFixCrossLinePrimitiveAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiComponent = Stimulsoft.Report.Components.StiComponent;
            class StiGenerateNewNameComponentAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "NewName");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiGenerateNewNameComponentActionLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    let component = element.stimulsoft().as(StiComponent);
                    if (component != null) {
                        component.name = Report.StiNameCreation.createName(report, Report.StiNameCreation.generateName(component), true, true, true);
                    }
                }
            }
            Check.StiGenerateNewNameComponentAction = StiGenerateNewNameComponentAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiComponent = Stimulsoft.Report.Components.StiComponent;
            class StiGrowToHeightOverlappingAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "SetGrowToHeightToFalse");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiGrowToHeightOverlappingLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    let comp = element.stimulsoft().as(StiComponent);
                    if (comp != null) {
                        comp.growToHeight = false;
                    }
                }
            }
            Check.StiGrowToHeightOverlappingAction = StiGrowToHeightOverlappingAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiDataBand = Stimulsoft.Report.Components.StiDataBand;
            class StiMinRowsInColumnsAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "Zero");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiMinRowsInColumnsActionLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    if (element.stimulsoft().is(StiDataBand)) {
                        element.minRowsInColumn = 0;
                    }
                }
            }
            Check.StiMinRowsInColumnsAction = StiMinRowsInColumnsAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiComponent = Stimulsoft.Report.Components.StiComponent;
            class StiMoveComponentToPageAreaAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiMoveComponentToPageAreaActionShort");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiMoveComponentToPageAreaActionLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    let comp = element.stimulsoft().as(StiComponent);
                    if (comp == null)
                        return;
                    let page = comp.page;
                    if (page == null)
                        return;
                    let compRect = comp.getPaintRectangle(false, false);
                    let pageRect = page.displayRectangle.clone();
                    pageRect.x -= page.margins.left;
                    pageRect.y -= page.margins.top;
                    if (compRect.left < pageRect.left) {
                        comp.left += pageRect.left - compRect.left;
                    }
                    if (compRect.top < pageRect.top) {
                        comp.top += pageRect.top - compRect.top;
                    }
                    if (compRect.right > pageRect.right) {
                        comp.left -= compRect.right - pageRect.right;
                    }
                    if (compRect.bottom > pageRect.bottom) {
                        comp.top -= compRect.bottom - pageRect.bottom;
                    }
                }
            }
            Check.StiMoveComponentToPageAreaAction = StiMoveComponentToPageAreaAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiComponent = Stimulsoft.Report.Components.StiComponent;
            class StiMoveComponentToPrintablePageAreaAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiMoveComponentToPrintablePageAreaActionShort");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiMoveComponentToPrintablePageAreaActionLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    let comp = element.stimulsoft().as(StiComponent);
                    if (comp == null)
                        return;
                    let page = comp.page;
                    if (page == null)
                        return;
                    let compRect = comp.getPaintRectangle(false, false);
                    let pageRect = page.clientRectangle.clone();
                    if (compRect.left < pageRect.left) {
                        comp.left = 0;
                    }
                    if (compRect.top < pageRect.top) {
                        comp.top = 0;
                    }
                    if (compRect.right > pageRect.right) {
                        comp.left -= compRect.right - pageRect.right;
                    }
                    if (compRect.bottom > pageRect.bottom) {
                        comp.top -= compRect.bottom - pageRect.bottom;
                    }
                }
            }
            Check.StiMoveComponentToPrintablePageAreaAction = StiMoveComponentToPrintablePageAreaAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiComponent = Stimulsoft.Report.Components.StiComponent;
            class StiNegativeSizesOfComponentsAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "Fix");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiNegativeSizesOfComponentsActionLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    let comp = element.stimulsoft().as(StiComponent);
                    if (comp == null)
                        return;
                    if (comp.width < 0) {
                        comp.width *= -1;
                    }
                    if (comp.height < 0) {
                        comp.height *= -1;
                    }
                }
            }
            Check.StiNegativeSizesOfComponentsAction = StiNegativeSizesOfComponentsAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            class StiVerySmallSizesOfComponentsAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiVerySmallSizesOfComponentsShort");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiVerySmallSizesOfComponentsLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                }
            }
            Check.StiVerySmallSizesOfComponentsAction = StiVerySmallSizesOfComponentsAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiText = Stimulsoft.Report.Components.StiText;
            class StiWordWrapCanGrowTextDoesNotFitAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "On");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiWordWrapCanGrowTextDoesNotFitActionLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    if (element.stimulsoft().is(StiText)) {
                        element.canGrow = true;
                    }
                }
            }
            Check.StiWordWrapCanGrowTextDoesNotFitAction = StiWordWrapCanGrowTextDoesNotFitAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiDatabase = Stimulsoft.Report.Dictionary.StiDatabase;
            class StiDeleteConnectionAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "Delete");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiDeleteConnectionActionLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    let connection = element.stimulsoft().as(StiDatabase);
                    if (connection != null)
                        report.dictionary.databases.remove(connection);
                }
            }
            Check.StiDeleteConnectionAction = StiDeleteConnectionAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiDataRelation = Stimulsoft.Report.Dictionary.StiDataRelation;
            class StiDeleteDataRelationAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "Delete");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiDeleteDataRelationActionLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    let dataRelation = element.stimulsoft().as(StiDataRelation);
                    if (dataRelation != null)
                        report.dictionary.relations.remove(dataRelation);
                }
            }
            Check.StiDeleteDataRelationAction = StiDeleteDataRelationAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiDataRelation = Stimulsoft.Report.Dictionary.StiDataRelation;
            class StiGenerateNewNameRelationAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "NewName");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiGenerateNewNameRelationActionLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    let relation = element.stimulsoft().as(StiDataRelation);
                    if (relation != null) {
                        relation.name = Report.StiNameCreation.createName(report, Report.StiNameCreation.generateName4(relation), true, true, true);
                    }
                }
            }
            Check.StiGenerateNewNameRelationAction = StiGenerateNewNameRelationAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiDataSource = Stimulsoft.Report.Dictionary.StiDataSource;
            class StiDeleteDataSourceAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "Delete");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiDeleteDataSourceActionLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    let dataSource = element.stimulsoft().as(StiDataSource);
                    if (dataSource != null)
                        report.dictionary.dataSources.remove(dataSource);
                }
            }
            Check.StiDeleteDataSourceAction = StiDeleteDataSourceAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiDataSource = Stimulsoft.Report.Dictionary.StiDataSource;
            class StiGenerateNewNameDataSourceAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "NewName");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiGenerateNewNameDataSourceActionLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    let dataSource = element.stimulsoft().as(StiDataSource);
                    if (dataSource != null)
                        dataSource.name = Report.StiNameCreation.createName(report, Report.StiNameCreation.generateName5(dataSource), true, true, true);
                }
            }
            Check.StiGenerateNewNameDataSourceAction = StiGenerateNewNameDataSourceAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiPage = Stimulsoft.Report.Components.StiPage;
            class StiDeleteLostPointsAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "Delete");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiDeleteLostPointsActionLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    let page = element.stimulsoft().as(StiPage);
                    if (page != null) {
                        let points = Check.StiLostPointsOnPageCheck.getLostPointsOnPage(page);
                        for (let point of points) {
                            point.parent.components.remove(point);
                        }
                    }
                }
            }
            Check.StiDeleteLostPointsAction = StiDeleteLostPointsAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiPage = Stimulsoft.Report.Components.StiPage;
            class StiDeletePageAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "Delete");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiDeletePageActionLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    if (element.stimulsoft().is(StiPage))
                        report.pages.remove(element);
                }
            }
            Check.StiDeletePageAction = StiDeletePageAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiComponent = Stimulsoft.Report.Components.StiComponent;
            class StiGenerateNewNamePageAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "NewName");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiGenerateNewNamePageActionLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    let component = element.stimulsoft().as(StiComponent);
                    if (component != null)
                        component.name = Report.StiNameCreation.createName(report, Report.StiNameCreation.generateName(component), true, true, true);
                }
            }
            Check.StiGenerateNewNamePageAction = StiGenerateNewNamePageAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiPage = Stimulsoft.Report.Components.StiPage;
            class StiLargeHeightAtPageAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "On");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiLargeHeightAtPageActionLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    let page = element.stimulsoft().as(StiPage);
                    if (page != null) {
                        if (page.largeHeightFactor == 1)
                            page.largeHeightFactor = 2;
                        page.largeHeight = true;
                    }
                }
            }
            Check.StiLargeHeightAtPageAction = StiLargeHeightAtPageAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiPage = Stimulsoft.Report.Components.StiPage;
            var StiPageOrientation = Stimulsoft.Report.Components.StiPageOrientation;
            class StiOrientationPageToLandscapeAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiOrientationPageToLandscapeActionShort");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiOrientationPageToLandscapeActionLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    let page = element.stimulsoft().as(StiPage);
                    if (page != null) {
                        let width = page.width;
                        let height = page.height;
                        page.orientation = StiPageOrientation.Landscape;
                        page.width = width;
                        page.height = height;
                    }
                }
            }
            Check.StiOrientationPageToLandscapeAction = StiOrientationPageToLandscapeAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiPage = Stimulsoft.Report.Components.StiPage;
            var StiPageOrientation = Stimulsoft.Report.Components.StiPageOrientation;
            class StiOrientationPageToPortraitAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiOrientationPageToPortraitActionShort");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiOrientationPageToPortraitActionLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    let page = element.stimulsoft().as(StiPage);
                    if (page != null) {
                        let width = page.width;
                        let height = page.height;
                        page.orientation = StiPageOrientation.Portrait;
                        page.width = width;
                        page.height = height;
                    }
                }
            }
            Check.StiOrientationPageToPortraitAction = StiOrientationPageToPortraitAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiPage = Stimulsoft.Report.Components.StiPage;
            class StiPrintHeadersFootersFromPreviousPageAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "Off");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiPrintHeadersFootersFromPreviousPageLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    if (element.stimulsoft().is(StiPage))
                        element.printHeadersFootersFromPreviousPage = false;
                }
            }
            Check.StiPrintHeadersFootersFromPreviousPageAction = StiPrintHeadersFootersFromPreviousPageAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiPage = Stimulsoft.Report.Components.StiPage;
            class StiPrintOnPreviousPageAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "Off");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiPrintOnPreviousPageLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    if (element.stimulsoft().is(StiPage))
                        element.printOnPreviousPage = false;
                }
            }
            Check.StiPrintOnPreviousPageAction = StiPrintOnPreviousPageAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiPage = Stimulsoft.Report.Components.StiPage;
            class StiResetPageNumberAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "Off");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiResetPageNumberActionLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    if (element.stimulsoft().is(StiPage))
                        element.resetPageNumber = false;
                }
            }
            Check.StiResetPageNumberAction = StiResetPageNumberAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiPage = Stimulsoft.Report.Components.StiPage;
            var StiPageOrientation = Stimulsoft.Report.Components.StiPageOrientation;
            class StiSwitchWidthAndHeightOfPageAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "Change");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "StiSwitchWidthAndHeightOfPageActionLong");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                    let page = element.stimulsoft().as(StiPage);
                    if (page != null) {
                        if (page.orientation == StiPageOrientation.Landscape) {
                            page.orientation = StiPageOrientation.Portrait;
                        }
                        else {
                            page.orientation = StiPageOrientation.Landscape;
                        }
                        let temp = page.pageWidth;
                        page.pageWidth = page.pageHeight;
                        page.pageHeight = temp;
                    }
                }
            }
            Check.StiSwitchWidthAndHeightOfPageAction = StiSwitchWidthAndHeightOfPageAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            class StiEditNameAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "Edit");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "Edit");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                }
            }
            Check.StiEditNameAction = StiEditNameAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            class StiEditPropertyAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "Edit");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "Edit");
                }
                invoke(report, element, elementName) {
                    super.invoke(report, null, null);
                }
            }
            Check.StiEditPropertyAction = StiEditPropertyAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            class StiGoToCodeAction extends Check.StiAction {
                get name() {
                    return Check.StiLocalizationExt.get("CheckActions", "Code");
                }
                get description() {
                    return Check.StiLocalizationExt.get("CheckActions", "GotoCodeLong");
                }
                invoke(report, element, elementName) {
                }
            }
            Check.StiGoToCodeAction = StiGoToCodeAction;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            class StiComponentCheck extends Check.StiCheck {
                constructor() {
                    super(...arguments);
                    this.objectType = Check.StiCheckObjectType.Component;
                }
                get elementName() {
                    if (this.element == null)
                        return null;
                    return this.element.name;
                }
            }
            Check.StiComponentCheck = StiComponentCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiText = Stimulsoft.Report.Components.StiText;
            class StiAllowHtmlTagsInTextCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Warning;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiAllowHtmlTagsInTextCheckShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiAllowHtmlTagsInTextCheckLong"), this.elementName);
                }
                check() {
                    let comp = this.element.stimulsoft().as(StiText);
                    if (comp != null && !comp.allowHtmlTags && comp.text.length > 0) {
                        if (comp.text.stimulsoft().contains("&amp;") || comp.text.stimulsoft().contains("&lt;") || comp.text.stimulsoft().contains("&gt;") ||
                            comp.text.stimulsoft().contains("&quot;") || comp.text.stimulsoft().contains("&nbsp;")) {
                            return true;
                        }
                        let startIndex = 0;
                        let endIndex = 0;
                        while (startIndex < comp.text.length) {
                            startIndex = comp.text.indexOf("<", startIndex);
                            if (startIndex == -1)
                                break;
                            endIndex = comp.text.indexOf(">", startIndex);
                            if (endIndex == -1)
                                break;
                            if (endIndex > startIndex + 1) {
                                let tag = comp.text.substr(startIndex + 1, endIndex - startIndex - 1).trim();
                                if ((tag.length > 0) && ((tag.length < 10) && (tag == "b" || tag == "/b" || tag == "i" || tag == "/i" || tag == "u" || tag == "/u" || tag == "s" || tag == "/s" ||
                                    tag == "sub" || tag == "/sub" || tag == "sup" || tag == "/sup" ||
                                    tag == "br" || tag == "/br" || tag == "strong" || tag == "/strong" ||
                                    tag == "p" || tag == "/p" || tag == "em" || tag == "/em") ||
                                    tag.stimulsoft().startsWith("font ") || tag.stimulsoft().startsWith("/font") ||
                                    tag.stimulsoft().startsWith("font-face") || tag.stimulsoft().startsWith("/font-face") ||
                                    tag.stimulsoft().startsWith("font-name") || tag.stimulsoft().startsWith("/font-name") ||
                                    tag.stimulsoft().startsWith("font-family") || tag.stimulsoft().startsWith("/font-family") ||
                                    tag.stimulsoft().startsWith("font-size") || tag.stimulsoft().startsWith("/font-size") ||
                                    tag.stimulsoft().startsWith("font-color") || tag.stimulsoft().startsWith("/font-color") ||
                                    tag.stimulsoft().startsWith("color") || tag.stimulsoft().startsWith("/color") ||
                                    tag.stimulsoft().startsWith("background-color") || tag.stimulsoft().startsWith("/background-color") ||
                                    tag.stimulsoft().startsWith("letter-spacing") || tag.stimulsoft().startsWith("/letter-spacing") ||
                                    tag.stimulsoft().startsWith("word-spacing") || tag.stimulsoft().startsWith("/word-spacing") ||
                                    tag.stimulsoft().startsWith("line-height") || tag.stimulsoft().startsWith("/line-height") ||
                                    tag.stimulsoft().startsWith("text-align") || tag.stimulsoft().startsWith("/text-align"))) {
                                    return true;
                                }
                            }
                            startIndex++;
                        }
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    try {
                        let failed = this.check();
                        if (failed) {
                            let check = new StiAllowHtmlTagsInTextCheck();
                            check.element = obj;
                            check.actions.push(new Check.StiAllowHtmlTagsInTextAction());
                            return check;
                        }
                        else
                            return null;
                    }
                    finally {
                        this.element = null;
                    }
                }
            }
            Check.StiAllowHtmlTagsInTextCheck = StiAllowHtmlTagsInTextCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiContainer = Stimulsoft.Report.Components.StiContainer;
            var IStiBreakable = Stimulsoft.Report.Components.IStiBreakable;
            class StiCanBreakComponentInContainerCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Warning;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiCanBreakComponentInContainerCheckShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiCanBreakComponentInContainerCheckLong"), this.elementName);
                }
                check() {
                    let container = this.element.stimulsoft().as(StiContainer);
                    if (container != null && !container.canBreak) {
                        for (let comp of container.components.list) {
                            if (comp.is(IStiBreakable) && comp.canBreak) {
                                return true;
                            }
                        }
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    try {
                        let failed = this.check();
                        if (failed) {
                            let check = new StiCanBreakComponentInContainerCheck();
                            check.element = obj;
                            check.actions.push(new Check.StiCanBreakComponentInContainerAction());
                            return check;
                        }
                        else
                            return null;
                    }
                    finally {
                        this.element = null;
                    }
                }
            }
            Check.StiCanBreakComponentInContainerCheck = StiCanBreakComponentInContainerCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiClone = Stimulsoft.Report.Components.StiClone;
            var StiString = Stimulsoft.System.StiString;
            var StiContainer = Stimulsoft.Report.Components.StiContainer;
            class StiCanGrowComponentInContainerCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Warning;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiCanGrowComponentInContainerCheckShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiCanGrowComponentInContainerCheckLong"), this.elementName);
                }
                check() {
                    let container = this.element.stimulsoft().as(StiContainer);
                    if (container != null && !container.canGrow && !(container.parent == container.page)) {
                        if (container.is(StiClone))
                            return false;
                        for (let comp of container.components.list) {
                            if (comp.canGrow) {
                                return true;
                            }
                        }
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    try {
                        let failed = this.check();
                        if (failed) {
                            let check = new StiCanGrowComponentInContainerCheck();
                            check.element = obj;
                            check.actions.push(new Check.StiCanGrowComponentInContainerAction());
                            return check;
                        }
                        else
                            return null;
                    }
                    finally {
                        this.element = null;
                    }
                }
            }
            Check.StiCanGrowComponentInContainerCheck = StiCanGrowComponentInContainerCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiContainer = Stimulsoft.Report.Components.StiContainer;
            var StiBand = Stimulsoft.Report.Components.StiBand;
            class StiCanGrowGrowToHeightComponentInContainerCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.defaultStateEnabled = false;
                    this.status = Check.StiCheckStatus.Information;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiCanGrowGrowToHeightComponentInContainerShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiCanGrowGrowToHeightComponentInContainerLong"), this.elementName);
                }
                check() {
                    let container = this.element.stimulsoft().as(StiContainer);
                    if (container != null && container.components.count > 1) {
                        let stateCanGrow = false;
                        let stateGrowToHeight = false;
                        for (let comp of container.components.list) {
                            if (comp.is(StiBand))
                                continue;
                            if (comp.canGrow) {
                                stateCanGrow = true;
                            }
                            if (!comp.growToHeight) {
                                stateGrowToHeight = true;
                            }
                            if (stateCanGrow && stateGrowToHeight) {
                                return true;
                            }
                        }
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    try {
                        let failed = this.check();
                        if (failed) {
                            let check = new StiCanGrowGrowToHeightComponentInContainerCheck();
                            check.element = obj;
                            check.actions.push(new Check.StiCanGrowGrowToHeightComponentInContainerAction());
                            return check;
                        }
                        else
                            return null;
                    }
                    finally {
                        this.element = null;
                    }
                }
            }
            Check.StiCanGrowGrowToHeightComponentInContainerCheck = StiCanGrowGrowToHeightComponentInContainerCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiText = Stimulsoft.Report.Components.StiText;
            var StiTextQuality = Stimulsoft.Report.Components.StiTextQuality;
            class StiCanGrowWordWrapTextAndWysiwygCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.defaultStateEnabled = false;
                    this.status = Check.StiCheckStatus.Information;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiCanGrowWordWrapTextAndWysiwygCheckShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiCanGrowWordWrapTextAndWysiwygCheckLong"), this.elementName);
                }
                check() {
                    let comp = this.element.stimulsoft().as(StiText);
                    if (comp != null && comp.canGrow && comp.wordWrap && comp.textQuality != StiTextQuality.Wysiwyg) {
                        if (comp.getPaintRectangle(true, true).height > Stimulsoft.System.Drawing.Graphics.measureString("p\ni", comp.font).height + 2)
                            return true;
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    try {
                        let failed = this.check();
                        if (failed) {
                            let check = new StiCanGrowWordWrapTextAndWysiwygCheck();
                            check.element = obj;
                            check.actions.push(new Check.StiCanGrowWordWrapTextAndWysiwygAction());
                            return check;
                        }
                        else
                            return null;
                    }
                    finally {
                        this.element = null;
                    }
                }
            }
            Check.StiCanGrowWordWrapTextAndWysiwygCheck = StiCanGrowWordWrapTextAndWysiwygCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var IStiChart = Stimulsoft.Report.Chart.IStiChart;
            class StiChartSeriesValueCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Warning;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiCountDataDataSourceAtDataBandShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiChartSeriesValueCheckLong"), this.elementName);
                }
                check() {
                    let chart = this.element.as(IStiChart);
                    if (chart != null && chart.dataSource == null && chart.series.count > 0) {
                        for (let ser of chart.series.list) {
                            if (!StiString.isNullOrEmpty(ser.value)) {
                                return true;
                            }
                        }
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    try {
                        let failed = this.check();
                        if (failed) {
                            let check = new StiChartSeriesValueCheck();
                            check.element = obj;
                            return check;
                        }
                        else
                            return null;
                    }
                    finally {
                        this.element = null;
                    }
                }
            }
            Check.StiChartSeriesValueCheck = StiChartSeriesValueCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiDataBand = Stimulsoft.Report.Components.StiDataBand;
            var StiPanel = Stimulsoft.Report.Components.StiPanel;
            class StiColumnsWidthGreaterContainerWidthCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Information;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiColumnsWidthGreaterContainerWidthCheckShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiColumnsWidthGreaterContainerWidthCheckLong"), this.elementName);
                }
                check() {
                    let band = this.element.stimulsoft().as(StiDataBand);
                    let panel = this.element.stimulsoft().as(StiPanel);
                    if (band != null && band.columns > 0) {
                        let sumWidth = band.columnWidth * band.columns + band.columnGaps * (band.columns - 1);
                        if (sumWidth > band.width)
                            return true;
                    }
                    else if (panel != null && panel.columns > 0) {
                        let sumWidth = panel.columnWidth * panel.columns + panel.columnGaps * (panel.columns - 1);
                        if (sumWidth > panel.width)
                            return true;
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    try {
                        let failed = this.check();
                        if (failed) {
                            let check = new StiColumnsWidthGreaterContainerWidthCheck();
                            check.element = obj;
                            check.actions.push(new Check.StiColumnsWidthGreaterContainerWidthAction());
                            return check;
                        }
                        else
                            return null;
                    }
                    finally {
                        this.element = null;
                    }
                }
            }
            Check.StiColumnsWidthGreaterContainerWidthCheck = StiColumnsWidthGreaterContainerWidthCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiMath = Stimulsoft.System.StiMath;
            var StiTableCell = Stimulsoft.Report.Components.Table.StiTableCell;
            var StiString = Stimulsoft.System.StiString;
            var StiBand = Stimulsoft.Report.Components.StiBand;
            var StiContainer = Stimulsoft.Report.Components.StiContainer;
            var StiHorizontalLinePrimitive = Stimulsoft.Report.Components.StiHorizontalLinePrimitive;
            var StiVerticalLinePrimitive = Stimulsoft.Report.Components.StiVerticalLinePrimitive;
            class StiComponentBoundsAreOutOfBand extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Warning;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiComponentBoundsAreOutOfBandShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiComponentBoundsAreOutOfBandLong"), this.elementName);
                }
                check() {
                    let parentBand = this.element.parent.stimulsoft().as(StiBand);
                    if (parentBand != null && !(this.element.stimulsoft().is(StiContainer))) {
                        let compRect = this.element.getPaintRectangle(false, false);
                        if (this.element.stimulsoft().is(StiHorizontalLinePrimitive))
                            compRect.height = 0;
                        if (this.element.stimulsoft().is(StiVerticalLinePrimitive))
                            compRect.width = 0;
                        let bandRect = parentBand.getPaintRectangle(false, false);
                        if (StiMath.round2(compRect.left, 4) < StiMath.round2(bandRect.left, 4) ||
                            StiMath.round2(compRect.top, 4) < StiMath.round2(bandRect.top, 4) ||
                            StiMath.round2(compRect.right, 4) > StiMath.round2(bandRect.right, 4) ||
                            StiMath.round2(compRect.bottom, 4) > StiMath.round2(bandRect.bottom, 4)) {
                            return true;
                        }
                    }
                    return false;
                }
                get notAllowToDelete() {
                    return this.element.is(StiTableCell);
                }
                processCheck(report, obj) {
                    this.element = obj;
                    try {
                        let failed = this.check();
                        if (failed) {
                            let check = new StiComponentBoundsAreOutOfBand();
                            check.element = obj;
                            if (!this.notAllowToDelete)
                                check.actions.push(new Check.StiDeleteComponentAction());
                            return check;
                        }
                        else
                            return null;
                    }
                    finally {
                        this.element = null;
                    }
                }
            }
            Check.StiComponentBoundsAreOutOfBand = StiComponentBoundsAreOutOfBand;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiImage = Stimulsoft.Report.Components.StiImage;
            var StiRichText = Stimulsoft.Report.Components.StiRichText;
            class StiComponentDataColumnCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Warning;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiComponentExpressionCheckShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiComponentExpressionCheckLong"), "DataColumn", this.elementName);
                }
                check() {
                    let image = this.element.stimulsoft().as(StiImage);
                    if (image != null && image.dataColumn != null && image.dataColumn.length > 0) {
                        try {
                            let refStoreToPrint = { ref: false };
                            let obj = Stimulsoft.Report.Engine.StiParser.StiParser.parseTextValue("{" + image.dataColumn + "}", image, null, refStoreToPrint, false, true);
                            let list = obj;
                            if (list != null) {
                                if (list.length > 1)
                                    return true;
                            }
                        }
                        catch (e) {
                            return true;
                        }
                    }
                    let richText = this.element.stimulsoft().as(StiRichText);
                    if (richText != null && richText.dataColumn != null && richText.dataColumn.length > 0) {
                        try {
                            let refStoreToPrint = { ref: false };
                            let obj = Stimulsoft.Report.Engine.StiParser.StiParser.parseTextValue("{" + richText.dataColumn + "}", richText, refStoreToPrint, false, true);
                            let list = obj;
                            if (list != null) {
                                if (list.length > 1)
                                    return true;
                            }
                        }
                        catch (e) {
                            return true;
                        }
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    try {
                        let failed = this.check();
                        if (failed) {
                            let check = new StiComponentDataColumnCheck();
                            check.element = obj;
                            return check;
                        }
                        else
                            return null;
                    }
                    finally {
                        this.element = null;
                    }
                }
            }
            Check.StiComponentDataColumnCheck = StiComponentDataColumnCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiCondition = Stimulsoft.Report.Components.StiCondition;
            var XMLConvert = Stimulsoft.System.Text.XMLConvert;
            var StiRichText = Stimulsoft.Report.Components.StiRichText;
            var StiText = Stimulsoft.Report.Components.StiText;
            var StiComponent = Stimulsoft.Report.Components.StiComponent;
            var StiString = Stimulsoft.System.StiString;
            class StiComponentExpressionCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Warning;
                    this.componentName = null;
                    this.propertyName = null;
                    this.message = null;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiComponentExpressionCheckShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiComponentExpressionCheckLong") + " " + this.message, this.propertyName, this.elementName);
                }
                checkExpression(report, comp, expression) {
                    let result = Stimulsoft.Report.Engine.StiParser.StiParser.checkExpression(expression, comp);
                    if (result != null) {
                        return result.message;
                    }
                    return null;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    try {
                        if (report == null || obj == null)
                            return null;
                        let result = null;
                        let stiText = obj.as(StiText);
                        if (stiText != null) {
                            result = this.checkExpression(report, stiText, stiText.text);
                            if (result != null) {
                                let check = new StiComponentExpressionCheck();
                                check.element = obj;
                                check.propertyName = "Text";
                                check.componentName = stiText.name;
                                check.message = result;
                                check.actions.push(new Check.StiEditPropertyAction());
                                return check;
                            }
                        }
                        let richText = obj.as(StiRichText);
                        if (richText != null) {
                            let textToParse = XMLConvert.decodeName(richText.text).stimulsoft().replaceAll("\x00", ' ');
                            result = this.checkExpression(report, richText, textToParse);
                            if (result != null) {
                                let check = new StiComponentExpressionCheck();
                                check.element = obj;
                                check.propertyName = "Text";
                                check.componentName = richText.name;
                                check.message = result;
                                check.actions.push(new Check.StiEditPropertyAction());
                                return check;
                            }
                        }
                        let comp = obj.as(StiComponent);
                        if (comp != null && comp.conditions.count > 0) {
                            for (let condition of comp.conditions.list) {
                                let cond = condition.stimulsoft().as(StiCondition);
                                if (cond != null) {
                                    let stCond = cond.expression.trim();
                                    if (stCond.startsWith("{") && stCond.endsWith("}"))
                                        stCond = stCond.substr(1, stCond.length - 2);
                                    result = this.checkExpression(report, comp, "{" + stCond + "}");
                                    if (result != null) {
                                        let check = new StiComponentExpressionCheck();
                                        check.element = obj;
                                        check.propertyName = "Condition.Expression";
                                        check.componentName = comp.name;
                                        check.message = result;
                                        return check;
                                    }
                                    if (cond.canAssignExpression) {
                                        result = this.checkExpression(report, comp, "{" + cond.assignExpression + "}");
                                        if (result != null) {
                                            let check = new StiComponentExpressionCheck();
                                            check.element = obj;
                                            check.propertyName = "Condition.AssignExpression";
                                            check.componentName = comp.name;
                                            check.message = result;
                                            return check;
                                        }
                                    }
                                }
                            }
                        }
                        return null;
                    }
                    finally {
                        this.element = null;
                    }
                }
            }
            Check.StiComponentExpressionCheck = StiComponentExpressionCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiComponent = Stimulsoft.Report.Components.StiComponent;
            var IStiOddEvenStyles = Stimulsoft.Report.Components.IStiOddEvenStyles;
            class StiComponentStyleIsNotFoundAtComponentCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Warning;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiComponentStyleIsNotFoundCheckShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiComponentStyleIsNotFoundCheckLong"), this.elementName);
                }
                check() {
                    let comp = this.element.stimulsoft().as(StiComponent);
                    if (comp != null) {
                        if (comp.componentStyle != "" && !comp.report.styles.contains(comp.componentStyle))
                            return true;
                        let oddEvenStyles = comp.stimulsoft().as(IStiOddEvenStyles);
                        if (oddEvenStyles != null) {
                            if (oddEvenStyles.evenStyle != "" && !comp.report.styles.contains(oddEvenStyles.evenStyle))
                                return true;
                            if (oddEvenStyles.oddStyle != "" && !comp.report.styles.contains(oddEvenStyles.oddStyle))
                                return true;
                        }
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    try {
                        let failed = this.check();
                        if (failed) {
                            let check = new StiComponentStyleIsNotFoundAtComponentCheck();
                            check.element = obj;
                            check.actions.push(new Check.StiComponentStyleIsNotFoundAtComponentAction());
                            return check;
                        }
                        else
                            return null;
                    }
                    finally {
                        this.element = null;
                    }
                }
            }
            Check.StiComponentStyleIsNotFoundAtComponentCheck = StiComponentStyleIsNotFoundAtComponentCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiContainer = Stimulsoft.Report.Components.StiContainer;
            class StiContainerInEngineV2Check extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Information;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiContainerInEngineV2CheckShort"), this.elementName);
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiContainerInEngineV2CheckLong"), this.elementName);
                }
                processCheck(report, obj) {
                    this.element = obj;
                    if (obj.stimulsoft().getType() == StiContainer) {
                        let check = new StiContainerInEngineV2Check();
                        check.element = obj;
                        check.actions.push(new Check.StiConversionContainerInPanelAction());
                        return check;
                    }
                    return null;
                }
            }
            Check.StiContainerInEngineV2Check = StiContainerInEngineV2Check;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiContourText = Stimulsoft.Report.Components.StiContourText;
            class StiContourTextObsoleteCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Information;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiContourTextObsoleteCheckShort"), this.elementName);
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiContourTextObsoleteCheckLong"), this.elementName);
                }
                processCheck(report, obj) {
                    this.element = obj;
                    if (obj.stimulsoft().getType() == StiContourText) {
                        let check = new StiContourTextObsoleteCheck();
                        check.element = obj;
                        check.actions.push(new Check.StiConversionContourTextInTextAction());
                        return check;
                    }
                    return null;
                }
            }
            Check.StiContourTextObsoleteCheck = StiContourTextObsoleteCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiCrossLinePrimitive = Stimulsoft.Report.Components.StiCrossLinePrimitive;
            class StiCorruptedCrossLinePrimitiveCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Warning;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiCorruptedCrossLinePrimitiveCheckShort"), this.elementName);
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiCorruptedCrossLinePrimitiveCheckLong"), this.elementName);
                }
                processCheck(report, obj) {
                    this.element = obj;
                    let line = obj.stimulsoft().as(StiCrossLinePrimitive);
                    if (line != null) {
                        let startPoint = line.getStartPoint(line.page);
                        let endPoint = line.getEndPoint(line.page);
                        if (startPoint == null || endPoint == null) {
                            let check = new StiCorruptedCrossLinePrimitiveCheck();
                            check.element = obj;
                            check.actions.push(new Check.StiFixCrossLinePrimitiveAction());
                            check.actions.push(new Check.StiDeleteComponentAction());
                            return check;
                        }
                        else
                            return null;
                    }
                    return null;
                }
            }
            Check.StiCorruptedCrossLinePrimitiveCheck = StiCorruptedCrossLinePrimitiveCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiDataBand = Stimulsoft.Report.Components.StiDataBand;
            class StiCountDataDataSourceAtDataBandCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Information;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiCountDataDataSourceAtDataBandShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiCountDataDataSourceAtDataBandLong"), this.elementName);
                }
                check() {
                    let band = this.element.stimulsoft().as(StiDataBand);
                    if (band != null && band.countData == 0 && band.dataSource == null && band.businessObject == null) {
                        return true;
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    try {
                        let failed = this.check();
                        if (failed) {
                            let check = new StiCountDataDataSourceAtDataBandCheck();
                            check.element = obj;
                            return check;
                        }
                        else
                            return null;
                    }
                    finally {
                        this.element = null;
                    }
                }
            }
            Check.StiCountDataDataSourceAtDataBandCheck = StiCountDataDataSourceAtDataBandCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiContainer = Stimulsoft.Report.Components.StiContainer;
            class StiCrossGroupHeaderNotEqualToCrossGroupFooterOnContainerCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Information;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiGroupHeaderNotEqualToGroupFooterShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiCrossGroupHeaderNotEqualToGroupCrossFooterOnContainerLong"), this.elementName);
                }
                check() {
                    let container = this.element.stimulsoft().as(StiContainer);
                    if (container != null) {
                        let countCrossGroupHeader = 0;
                        let countCrossGroupFooter = 0;
                        for (let index = 0; index < container.components.count; index++) {
                        }
                        if (countCrossGroupHeader < countCrossGroupFooter) {
                            return true;
                        }
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    try {
                        let failed = this.check();
                        if (failed) {
                            let check = new StiCrossGroupHeaderNotEqualToCrossGroupFooterOnContainerCheck();
                            check.element = obj;
                            return check;
                        }
                        else
                            return null;
                    }
                    finally {
                        this.element = null;
                    }
                }
            }
            Check.StiCrossGroupHeaderNotEqualToCrossGroupFooterOnContainerCheck = StiCrossGroupHeaderNotEqualToCrossGroupFooterOnContainerCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiImage = Stimulsoft.Report.Components.StiImage;
            class StiDataSourcesForImageCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Information;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiDataSourcesForImageCheckShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiDataSourcesForImageCheckLong"), this.elementName);
                }
                check() {
                    let image = this.element.stimulsoft().as(StiImage);
                    if (image != null) {
                        let count = 0;
                        if (image.image != null)
                            count++;
                        if (!StiString.isNullOrEmpty(image.dataColumn))
                            count++;
                        if (image.imageData != null && image.imageData != "")
                            count++;
                        if (image.imageURL != null && image.imageURL != "")
                            count++;
                        if (image.file != "")
                            count++;
                        if (count > 1)
                            return true;
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    try {
                        let failed = this.check();
                        if (failed) {
                            let check = new StiDataSourcesForImageCheck();
                            check.element = obj;
                            return check;
                        }
                        else
                            return null;
                    }
                    finally {
                        this.element = null;
                    }
                }
            }
            Check.StiDataSourcesForImageCheck = StiDataSourcesForImageCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var IStiFont = Stimulsoft.Report.Components.IStiFont;
            class StiFontMissingCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Information;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiFontMissingCheckShort");
                }
                get longMessage() {
                    let font = this.element.stimulsoft().as(IStiFont);
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiFontMissingCheckLong"), font.font.name, this.elementName);
                }
                check() {
                    let comp = this.element.stimulsoft().as(IStiFont);
                    if (comp != null) {
                        if (!StiString.isNullOrEmpty(comp.font.name) && comp.font.name != comp.font.name)
                            return true;
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    try {
                        let failed = this.check();
                        if (failed) {
                            let check = new StiFontMissingCheck();
                            check.element = obj;
                            return check;
                        }
                        else
                            return null;
                    }
                    finally {
                        this.element = null;
                    }
                }
            }
            Check.StiFontMissingCheck = StiFontMissingCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiContainer = Stimulsoft.Report.Components.StiContainer;
            var StiGroupHeaderBand = Stimulsoft.Report.Components.StiGroupHeaderBand;
            var StiGroupFooterBand = Stimulsoft.Report.Components.StiGroupFooterBand;
            class StiGroupHeaderNotEqualToGroupFooterOnContainerCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Information;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiGroupHeaderNotEqualToGroupFooterShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiGroupHeaderNotEqualToGroupFooterOnContainerLong"), this.elementName);
                }
                check() {
                    let container = this.element.stimulsoft().as(StiContainer);
                    if (container != null) {
                        let countGroupHeader = 0;
                        let countGroupFooter = 0;
                        for (let index = 0; index < container.components.count; index++) {
                            if (container.components.getByIndex(index).is(StiGroupHeaderBand)) {
                                countGroupHeader++;
                            }
                            else if (container.components.getByIndex(index).is(StiGroupFooterBand)) {
                                countGroupFooter++;
                            }
                        }
                        if (countGroupHeader < countGroupFooter) {
                            return true;
                        }
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    try {
                        let failed = this.check();
                        if (failed) {
                            let check = new StiGroupHeaderNotEqualToGroupFooterOnContainerCheck();
                            check.element = obj;
                            return check;
                        }
                        else
                            return null;
                    }
                    finally {
                        this.element = null;
                    }
                }
            }
            Check.StiGroupHeaderNotEqualToGroupFooterOnContainerCheck = StiGroupHeaderNotEqualToGroupFooterOnContainerCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiComponent = Stimulsoft.Report.Components.StiComponent;
            var StiPage = Stimulsoft.Report.Components.StiPage;
            var RectangleD = Stimulsoft.System.Drawing.Rectangle;
            var StiTable = Stimulsoft.Report.Components.Table.StiTable;
            class StiGrowToHeightOverlappingCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Warning;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiGrowToHeightOverlappingShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiGrowToHeightOverlappingLong"), this.elementName);
                }
                check() {
                    let component = this.element.stimulsoft().as(StiComponent);
                    if (component.growToHeight && component.parent != null && !(component.parent.is(StiPage)) && !(component.parent.is(StiTable)) && component.parent.components.count > 1) {
                        let baseRect = component.clientRectangle.clone();
                        let baseRectGrow = new RectangleD(baseRect.x, baseRect.y, baseRect.width, component.parent.height - baseRect.y);
                        for (let comp of component.parent.components.list) {
                            if (comp == component)
                                continue;
                            if (comp.top < baseRect.bottom)
                                continue;
                            if (baseRectGrow.intersectsWith(comp.clientRectangle))
                                return true;
                        }
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    try {
                        let failed = this.check();
                        if (failed) {
                            let check = new StiGrowToHeightOverlappingCheck();
                            check.element = obj;
                            check.actions.push(new Check.StiGrowToHeightOverlappingAction());
                            return check;
                        }
                        else
                            return null;
                    }
                    finally {
                        this.element = null;
                    }
                }
            }
            Check.StiGrowToHeightOverlappingCheck = StiGrowToHeightOverlappingCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiTableCell = Stimulsoft.Report.Components.Table.StiTableCell;
            var StiString = Stimulsoft.System.StiString;
            var StiComponent = Stimulsoft.Report.Components.StiComponent;
            var RectangleD = Stimulsoft.System.Drawing.Rectangle;
            var StiHorizontalLinePrimitive = Stimulsoft.Report.Components.StiHorizontalLinePrimitive;
            var StiVerticalLinePrimitive = Stimulsoft.Report.Components.StiVerticalLinePrimitive;
            var StiBand = Stimulsoft.Report.Components.StiBand;
            class StiLocationOutsidePageCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Warning;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    if (this.isOutsidePage)
                        return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiLocationOutsidePageCheckShort"), this.elementName);
                    else
                        return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiLocationOutsidePrintableAreaCheckShort"), this.elementName);
                }
                get longMessage() {
                    if (this.element != null) {
                        if (this.isOutsidePage)
                            return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiLocationOutsidePageCheckLong"), this.elementName);
                        else
                            return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiLocationOutsidePrintableAreaCheckLong"), this.elementName);
                    }
                    else {
                        return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiLocationOutsidePageCheckLong"), this.elementName);
                    }
                }
                get isOutsidePage() {
                    let comp = this.element.stimulsoft().as(StiComponent);
                    let page = comp.page;
                    if (page == null)
                        return false;
                    let compRect = comp.getPaintRectangle(false, false);
                    if (comp.is(StiHorizontalLinePrimitive))
                        compRect.height = 0;
                    if (comp.is(StiVerticalLinePrimitive))
                        compRect.width = 0;
                    let pageHeight = (page.pageHeight - page.margins.top - page.margins.bottom) * page.segmentPerHeight;
                    pageHeight *= page.largeHeightAutoFactor;
                    let pageRect = new RectangleD(-page.margins.left, -page.margins.top, page.width + page.margins.left + page.margins.right, pageHeight + page.margins.top + page.margins.bottom);
                    if ((compRect.left < pageRect.left) ||
                        (compRect.top < pageRect.top) ||
                        (compRect.right > pageRect.right)) {
                        return true;
                    }
                    if (compRect.bottom > pageRect.bottom) {
                        if (comp.is(StiBand))
                            return false;
                        if ((comp.parent != comp.page) && (comp.bottom < pageHeight))
                            return false;
                        return true;
                    }
                    return false;
                }
                get isOutsidePrintableArea() {
                    let comp = this.element.stimulsoft().as(StiComponent);
                    let page = comp.page;
                    if (page == null)
                        return false;
                    let compRect = comp.getPaintRectangle(false, false);
                    if (comp.is(StiHorizontalLinePrimitive))
                        compRect.height = 0;
                    if (comp.is(StiVerticalLinePrimitive))
                        compRect.width = 0;
                    let pageHeight = (page.pageHeight - page.margins.top - page.margins.bottom) * page.segmentPerHeight;
                    pageHeight *= page.largeHeightAutoFactor;
                    let pageRect = new RectangleD(0, 0, page.width, pageHeight);
                    if (compRect.left < pageRect.left ||
                        compRect.top < pageRect.top ||
                        compRect.right > (pageRect.right * 1.01)) {
                        return true;
                    }
                    if (compRect.bottom > (pageRect.bottom * 1.01)) {
                        if (comp.is(StiBand))
                            return false;
                        if ((comp.parent != comp.page) && (comp.bottom < pageHeight))
                            return false;
                        return true;
                    }
                    return false;
                }
                get notAllowToDelete() {
                    return this.element.is(StiTableCell);
                }
                processCheck(report, obj) {
                    this.element = obj;
                    try {
                        let failed = this.isOutsidePage || this.isOutsidePrintableArea;
                        if (failed) {
                            let check = new StiLocationOutsidePageCheck();
                            check.element = obj;
                            if (this.isOutsidePage)
                                check.actions.push(new Check.StiMoveComponentToPageAreaAction());
                            check.actions.push(new Check.StiMoveComponentToPrintablePageAreaAction());
                            if (!this.notAllowToDelete)
                                check.actions.push(new Check.StiDeleteComponentAction());
                            return check;
                        }
                        else
                            return null;
                    }
                    finally {
                        this.element = null;
                    }
                }
            }
            Check.StiLocationOutsidePageCheck = StiLocationOutsidePageCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiDataBand = Stimulsoft.Report.Components.StiDataBand;
            class StiMinRowsInColumnsCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Information;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiMinRowsInColumnsCheckShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiMinRowsInColumnsCheckLong"), this.elementName);
                }
                check() {
                    let band = this.element.stimulsoft().as(StiDataBand);
                    if (band != null && band.minRowsInColumn > 0 && band.columns == 0) {
                        return true;
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    try {
                        let failed = this.check();
                        if (failed) {
                            let check = new StiMinRowsInColumnsCheck();
                            check.element = obj;
                            check.actions.push(new Check.StiMinRowsInColumnsAction());
                            return check;
                        }
                        else
                            return null;
                    }
                    finally {
                        this.element = null;
                    }
                }
            }
            Check.StiMinRowsInColumnsCheck = StiMinRowsInColumnsCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiComponent = Stimulsoft.Report.Components.StiComponent;
            class StiNegativeSizesOfComponentsCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Warning;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiNegativeSizesOfComponentsCheckShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiNegativeSizesOfComponentsCheckLong"), this.elementName);
                }
                check() {
                    let comp = this.element.stimulsoft().as(StiComponent);
                    if (comp != null && (comp.width < 0 || comp.height < 0)) {
                        return true;
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    try {
                        let failed = this.check();
                        if (failed) {
                            let check = new StiNegativeSizesOfComponentsCheck();
                            check.element = obj;
                            check.actions.push(new Check.StiNegativeSizesOfComponentsAction());
                            return check;
                        }
                        else
                            return null;
                    }
                    finally {
                        this.element = null;
                    }
                }
            }
            Check.StiNegativeSizesOfComponentsCheck = StiNegativeSizesOfComponentsCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var IStiGroup = Stimulsoft.Report.Components.IStiGroup;
            class StiNoConditionAtGroupCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Information;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiNoConditionAtGroupCheckShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiNoConditionAtGroupCheckLong"), this.elementName);
                }
                check() {
                    if (this.element.stimulsoft().is(IStiGroup)) {
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    try {
                        let failed = this.check();
                        if (failed) {
                            let check = new StiNoConditionAtGroupCheck();
                            check.element = obj;
                            check.actions.push(new Check.StiDeleteComponentAction());
                            return check;
                        }
                        else
                            return null;
                    }
                    finally {
                        this.element = null;
                    }
                }
            }
            Check.StiNoConditionAtGroupCheck = StiNoConditionAtGroupCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiComponent = Stimulsoft.Report.Components.StiComponent;
            class StiNoNameComponentCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Error;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiNoNameComponentCheckShort"), this.elementName);
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiNoNameComponentCheckLong"), this.elementName);
                }
                processCheck(report, obj) {
                    let comp = obj.stimulsoft().as(StiComponent);
                    let failed = false;
                    if (StiString.isNullOrEmpty(comp.name)) {
                        failed = true;
                    }
                    if (failed) {
                        let check = new StiNoNameComponentCheck();
                        check.element = obj;
                        check.actions.push(new Check.StiGenerateNewNameComponentAction());
                        check.actions.push(new Check.StiDeleteComponentAction());
                        return check;
                    }
                    else
                        return null;
                }
            }
            Check.StiNoNameComponentCheck = StiNoNameComponentCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiComponent = Stimulsoft.Report.Components.StiComponent;
            var IStiPrintOn = Stimulsoft.Report.Components.IStiPrintOn;
            var StiPrintOnType = Stimulsoft.Report.Components.StiPrintOnType;
            class StiPrintOnDoublePassCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Warning;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiPrintOnDoublePassCheckShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiPrintOnDoublePassCheckLong"), this.elementName);
                }
                check() {
                    let comp = this.element.stimulsoft().as(StiComponent);
                    if (comp.report != null && comp.report.numberOfPass != Report.StiNumberOfPass.DoublePass && comp.is(IStiPrintOn) && comp.printOn != StiPrintOnType.AllPages &&
                        (StiOptions.Engine.useAdvancedPrintOnEngine)) {
                        return true;
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    try {
                        let failed = this.check();
                        if (failed) {
                            let check = new StiPrintOnDoublePassCheck();
                            check.element = obj;
                            check.actions.push(new Check.StiAllowDoublePassAction());
                            return check;
                        }
                        else
                            return null;
                    }
                    finally {
                        this.element = null;
                    }
                }
            }
            Check.StiPrintOnDoublePassCheck = StiPrintOnDoublePassCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiText = Stimulsoft.Report.Components.StiText;
            class StiShowInsteadNullValuesCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Information;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiShowInsteadNullValuesCheckShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiShowInsteadNullValuesCheckLong"), this.elementName);
                }
                check() {
                    let comp = this.element.stimulsoft().as(StiText);
                    if (comp != null && comp.nullValue != null && comp.nullValue.length > 0 && comp.text.length == 0) {
                        return true;
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    try {
                        let failed = this.check();
                        if (failed) {
                            let check = new StiShowInsteadNullValuesCheck();
                            check.element = obj;
                            return check;
                        }
                        else
                            return null;
                    }
                    finally {
                        this.element = null;
                    }
                }
            }
            Check.StiShowInsteadNullValuesCheck = StiShowInsteadNullValuesCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            class StiSystemTextObsoleteCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Information;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiSystemTextObsoleteCheckShort"), this.elementName);
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiSystemTextObsoleteCheckLong"), this.elementName);
                }
                processCheck(report, obj) {
                    this.element = obj;
                    return null;
                }
            }
            Check.StiSystemTextObsoleteCheck = StiSystemTextObsoleteCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiText = Stimulsoft.Report.Components.StiText;
            var StiEmptyBrush = Stimulsoft.Base.Drawing.StiEmptyBrush;
            var StiSolidBrush = Stimulsoft.Base.Drawing.StiSolidBrush;
            var StiHatchBrush = Stimulsoft.Base.Drawing.StiHatchBrush;
            var StiGradientBrush = Stimulsoft.Base.Drawing.StiGradientBrush;
            var StiGlareBrush = Stimulsoft.Base.Drawing.StiGlareBrush;
            var StiGlassBrush = Stimulsoft.Base.Drawing.StiGlassBrush;
            class StiTextColorEqualToBackColorCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Information;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiTextColorEqualToBackColorCheckShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiTextColorEqualToBackColorCheckLong"), this.elementName);
                }
                check() {
                    let comp = this.element.stimulsoft().as(StiText);
                    if (comp != null) {
                        if (comp.brush.is(StiEmptyBrush) && comp.textBrush.is(StiEmptyBrush)) {
                            return true;
                        }
                        if (comp.brush.is(StiSolidBrush) && comp.textBrush.is(StiSolidBrush)) {
                            if (comp.brush.color.equals(comp.textBrush.color))
                                return true;
                            else
                                return false;
                        }
                        if (comp.brush.is(StiHatchBrush) && comp.textBrush.is(StiHatchBrush)) {
                            if (comp.brush.foreColor.equals(comp.textBrush.foreColor) &&
                                comp.brush.backColor.equals(comp.textBrush.backColor))
                                return true;
                            else
                                return false;
                        }
                        if (comp.brush.is(StiGradientBrush) && comp.textBrush.is(StiGradientBrush)) {
                            if (comp.brush.startColor.equals(comp.textBrush.startColor) &&
                                comp.brush.endColor.equals(comp.textBrush.endColor))
                                return true;
                            else
                                return false;
                        }
                        if (comp.brush.is(StiGlareBrush) && comp.textBrush.is(StiGlareBrush)) {
                            if (comp.brush.startColor.equals(comp.textBrush.startColor) &&
                                comp.brush.endColor.equals(comp.textBrush.endColor))
                                return true;
                            else
                                return false;
                        }
                        if (comp.brush.is(StiGlassBrush) && comp.textBrush.is(StiGlassBrush) &&
                            comp.brush.color.equals(comp.textBrush.color)) {
                            return true;
                        }
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    try {
                        let failed = this.check();
                        if (failed) {
                            let check = new StiTextColorEqualToBackColorCheck();
                            check.element = obj;
                            return check;
                        }
                        else
                            return null;
                    }
                    finally {
                        this.element = null;
                    }
                }
            }
            Check.StiTextColorEqualToBackColorCheck = StiTextColorEqualToBackColorCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiText = Stimulsoft.Report.Components.StiText;
            var StiGeneralFormatService = Stimulsoft.Report.Components.TextFormats.StiGeneralFormatService;
            class StiTextTextFormatCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Warning;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiTextTextFormatCheckShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiTextTextFormatCheckLong"), this.elementName);
                }
                check() {
                    let comp = this.element.stimulsoft().as(StiText);
                    if (comp != null && !(comp.textFormat.is(StiGeneralFormatService))) {
                        let text = comp.text;
                        if (!StiString.isNullOrEmpty(text) &&
                            text.stimulsoft().contains("{") && text.stimulsoft().contains("}") &&
                            !(text.stimulsoft().startsWith("{") && text.stimulsoft().endsWith("}"))) {
                            return true;
                        }
                        if (!StiString.isNullOrEmpty(text) &&
                            text.stimulsoft().contains("{") && text.stimulsoft().contains("}") && text.stimulsoft().contains("ToString")) {
                            return true;
                        }
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    try {
                        let failed = this.check();
                        if (failed) {
                            let check = new StiTextTextFormatCheck();
                            check.element = obj;
                            check.actions.push(new Check.StiApplyGeneralTextFormat());
                            return check;
                        }
                        else
                            return null;
                    }
                    finally {
                        this.element = null;
                    }
                }
            }
            Check.StiTextTextFormatCheck = StiTextTextFormatCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            class StiUndefinedComponentCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Error;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiUndefinedComponentCheckShort"), this.elementName);
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiUndefinedComponentCheckLong"), this.elementName);
                }
                processCheck(report, obj) {
                    this.element = obj;
                    return null;
                }
            }
            Check.StiUndefinedComponentCheck = StiUndefinedComponentCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            class StiVerySmallSizesOfComponentsCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Information;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiVerySmallSizesOfComponentsCheckShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiVerySmallSizesOfComponentsCheckLong"), this.elementName);
                }
                processCheck(report, obj) {
                    let failed = false;
                    try {
                        if (failed) {
                            let check = new StiVerySmallSizesOfComponentsCheck();
                            check.element = obj;
                            check.actions.push(new Check.StiDeleteComponentAction());
                            check.actions.push(new Check.StiVerySmallSizesOfComponentsAction());
                            return check;
                        }
                        else
                            return null;
                    }
                    finally {
                        this.element = null;
                    }
                }
            }
            Check.StiVerySmallSizesOfComponentsCheck = StiVerySmallSizesOfComponentsCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiBand = Stimulsoft.Report.Components.StiBand;
            var StiPointPrimitive = Stimulsoft.Report.Components.StiPointPrimitive;
            class StiWidthHeightZeroComponentCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Information;
                }
                get shortMessage() {
                    let comp = this.element;
                    if (comp.width == 0 && comp.height == 0)
                        return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiWidthHeightZeroComponentCheckShortWidthHeight"), this.elementName);
                    else if (comp.width == 0)
                        return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiWidthHeightZeroComponentCheckShortWidth"), this.elementName);
                    else
                        return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiWidthHeightZeroComponentCheckShortHeight"), this.elementName);
                }
                get longMessage() {
                    let comp = this.element;
                    if (comp != null) {
                        if (comp.width == 0 && comp.height == 0)
                            return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiWidthHeightZeroComponentCheckLongWidthHeight"), this.elementName);
                        else if (comp.width == 0)
                            return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiWidthHeightZeroComponentCheckLongWidth"), this.elementName);
                        else
                            return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiWidthHeightZeroComponentCheckLongHeight"), this.elementName);
                    }
                    else {
                        return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiWidthHeightZeroComponentCheckLongWidthHeight"), this.elementName);
                    }
                }
                processCheck(report, obj) {
                    let comp = obj;
                    let failed = false;
                    if ((comp.width == 0 || comp.height == 0) && (!(comp.is(StiBand))) && (!(comp.is(StiPointPrimitive)))) {
                        failed = true;
                    }
                    if (failed) {
                        let check = new StiWidthHeightZeroComponentCheck();
                        check.element = obj;
                        check.actions.push(new Check.StiDeleteComponentAction());
                        return check;
                    }
                    else
                        return null;
                }
            }
            Check.StiWidthHeightZeroComponentCheck = StiWidthHeightZeroComponentCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiText = Stimulsoft.Report.Components.StiText;
            class StiWordWrapCanGrowTextDoesNotFitCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Warning;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiWordWrapCanGrowTextDoesNotFitShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiWordWrapCanGrowTextDoesNotFitLong"), this.elementName);
                }
                check() {
                    let comp = this.element.stimulsoft().as(StiText);
                    if (comp != null && (comp.onlyText || !comp.text.stimulsoft().contains("{"))) {
                        return false;
                    }
                    if (comp != null && comp.wordWrap && !comp.canGrow && StiString.isNullOrEmpty(comp.renderTo)) {
                        return true;
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    try {
                        let failed = this.check();
                        if (failed) {
                            let check = new StiWordWrapCanGrowTextDoesNotFitCheck();
                            check.element = obj;
                            check.actions.push(new Check.StiWordWrapCanGrowTextDoesNotFitAction());
                            return check;
                        }
                        else
                            return null;
                    }
                    finally {
                        this.element = null;
                    }
                }
            }
            Check.StiWordWrapCanGrowTextDoesNotFitCheck = StiWordWrapCanGrowTextDoesNotFitCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            class StiConnectionCheck extends Check.StiCheck {
                constructor() {
                    super(...arguments);
                    this.objectType = Check.StiCheckObjectType.Database;
                }
                get elementName() {
                    if (this.element == null)
                        return null;
                    return this.element.name;
                }
            }
            Check.StiConnectionCheck = StiConnectionCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            class StiUndefinedConnectionCheck extends Check.StiConnectionCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Error;
                }
                get previewVisible() {
                    return false;
                }
                get shortMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckConnection", "StiUndefinedConnectionCheckShort"), this.elementName);
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckConnection", "StiUndefinedConnectionCheckLong"), this.elementName);
                }
                processCheck(report, obj) {
                    this.element = obj;
                    return null;
                }
            }
            Check.StiUndefinedConnectionCheck = StiUndefinedConnectionCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var IStiElement = Stimulsoft.Report.Dashboard.IStiElement;
            var StiExpressionHelper = Stimulsoft.Data.Helpers.StiExpressionHelper;
            class StiExpressionElementCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.defaultStateEnabled = true;
                    this.status = Check.StiCheckStatus.Warning;
                    this._expression = "";
                    this._message = "";
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiExpressionElementCheckShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiExpressionElementCheckLong"), this.expression, this.elementName, this.message);
                }
                get expression() {
                    return this._expression;
                }
                set expression(value) {
                    if (value != null)
                        this._expression = value;
                }
                get message() {
                    return this._message;
                }
                set message(value) {
                    if (value != null)
                        this._message = value;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    let check = null;
                    try {
                        let element = this.element.stimulsoft().as(IStiElement);
                        if (element == null)
                            return null;
                        let meters = element === null || element === void 0 ? void 0 : element.fetchAllMeters();
                        if (meters == null)
                            return null;
                        for (let meter of meters) {
                            if (StiString.isNullOrWhiteSpace(meter.expression))
                                continue;
                            try {
                                StiExpressionHelper.compile(meter.expression);
                            }
                            catch (e) {
                                check = new StiExpressionElementCheck();
                                check.element = obj;
                                check.message = e.message;
                                check.expression = meter.expression;
                                check.actions.push(new Check.StiEditPropertyAction());
                                return check;
                            }
                        }
                    }
                    finally {
                        this.element = null;
                    }
                    return null;
                }
            }
            Check.StiExpressionElementCheck = StiExpressionElementCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var IStiFilterElement = Stimulsoft.Report.Dashboard.IStiFilterElement;
            var StiCrossLinkedFilterHelper = Stimulsoft.Report.Dashboard.Helpers.StiCrossLinkedFilterHelper;
            class StiFilterCircularDependencyElementCheck extends Check.StiComponentCheck {
                constructor() {
                    super(...arguments);
                    this.defaultStateEnabled = true;
                    this.status = Check.StiCheckStatus.Warning;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiFilterCircularDependencyElementCheckShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiFilterCircularDependencyElementCheckLong"), this.elementName);
                }
                check() {
                    return StiCrossLinkedFilterHelper.isCrossLinkedFilter(this.element.stimulsoft().as(IStiFilterElement));
                }
                processCheck(report, obj) {
                    this.element = obj;
                    try {
                        let failed = this.check();
                        if (failed) {
                            let check = new StiFilterCircularDependencyElementCheck();
                            check.element = obj;
                            return check;
                        }
                        else
                            return null;
                    }
                    finally {
                        this.element = null;
                    }
                }
            }
            Check.StiFilterCircularDependencyElementCheck = StiFilterCircularDependencyElementCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiDataBuilder = Stimulsoft.Report.Dictionary.StiDataBuilder;
            class StiDataRelationCheck extends Check.StiCheck {
                constructor() {
                    super(...arguments);
                    this.dataBuilder = new StiDataBuilder();
                    this.objectType = Check.StiCheckObjectType.DataRelation;
                }
                get elementName() {
                    if (this.element == null)
                        return null;
                    return this.element.name;
                }
            }
            Check.StiDataRelationCheck = StiDataRelationCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiDataRelation = Stimulsoft.Report.Dictionary.StiDataRelation;
            class StiDifferentAmountOfKeysInDataRelationCheck extends Check.StiDataRelationCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Error;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckDataRelation", "StiDifferentAmountOfKeysInDataRelationCheckShort"), this.elementName);
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckDataRelation", "StiDifferentAmountOfKeysInDataRelationCheckLong"), this.elementName);
                }
                processCheck(report, obj) {
                    this.element = obj;
                    let relation = obj.stimulsoft().as(StiDataRelation);
                    if (relation != null && relation.parentColumns.length != relation.childColumns.length) {
                        let check = new StiDifferentAmountOfKeysInDataRelationCheck();
                        check.element = obj;
                        return check;
                    }
                    return null;
                }
            }
            Check.StiDifferentAmountOfKeysInDataRelationCheck = StiDifferentAmountOfKeysInDataRelationCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiDataRelation = Stimulsoft.Report.Dictionary.StiDataRelation;
            class StiKeysInAbsentDataRelationCheck extends Check.StiDataRelationCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Error;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckDataRelation", "StiKeysInAbsentDataRelationCheckShort"), this.elementName);
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckDataRelation", "StiKeysInAbsentDataRelationCheckLong"), this.elementName);
                }
                processCheck(report, obj) {
                    this.element = obj;
                    let relation = obj.stimulsoft().as(StiDataRelation);
                    if (relation != null && relation.parentColumns.length == 0 && relation.childColumns.length == 0) {
                        let check = new StiKeysInAbsentDataRelationCheck();
                        check.element = obj;
                        return check;
                    }
                    return null;
                }
            }
            Check.StiKeysInAbsentDataRelationCheck = StiKeysInAbsentDataRelationCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            class StiKeysNotFoundRelationCheck extends Check.StiDataRelationCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Error;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckDataRelation", "StiKeysNotFoundRelationCheckShort"), this.elementName, this.columns);
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckDataRelation", "StiKeysNotFoundRelationCheckLong"), this.elementName, this.columns);
                }
                isColumnsExist(relation) {
                    for (let column of relation.parentColumns) {
                        if (!relation.parentSource.columns.contains(column))
                            return false;
                    }
                    for (let column of relation.childColumns) {
                        if (!relation.childSource.columns.contains(column))
                            return false;
                    }
                    return true;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    let relation = obj;
                    if (relation != null &&
                        relation.parentSource != null && relation.childSource != null &&
                        (!this.isColumnsExist(relation))) {
                        let finded = false;
                        let columns = "";
                        for (let column of relation.parentColumns) {
                            if (!relation.parentSource.columns.contains(column)) {
                                if (columns.length == 0)
                                    columns += column;
                                else
                                    columns += "; " + column;
                                finded = true;
                            }
                        }
                        for (let column of relation.childColumns) {
                            if (!relation.childSource.columns.contains(column)) {
                                if (columns.length == 0)
                                    columns += column;
                                else
                                    columns += "; " + column;
                                finded = true;
                            }
                        }
                        if (finded) {
                            let check = new StiKeysNotFoundRelationCheck();
                            check.columns = columns;
                            check.element = obj;
                            return check;
                        }
                    }
                    return null;
                }
            }
            Check.StiKeysNotFoundRelationCheck = StiKeysNotFoundRelationCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiDataRelation = Stimulsoft.Report.Dictionary.StiDataRelation;
            class StiKeysTypesMismatchDataRelationCheck extends Check.StiDataRelationCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Error;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckDataRelation", "StiKeysTypesMismatchDataRelationCheckShort"), this.elementName, this.columns);
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckDataRelation", "StiKeysTypesMismatchDataRelationCheckLong"), this.elementName, this.columns);
                }
                isColumnsExist(relation) {
                    for (let column of relation.parentColumns) {
                        if (!relation.parentSource.columns.contains(column))
                            return false;
                    }
                    for (let column of relation.childColumns) {
                        if (!relation.childSource.columns.contains(column))
                            return false;
                    }
                    return true;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    let relation = obj.stimulsoft().as(StiDataRelation);
                    if (relation != null &&
                        relation.parentSource != null && relation.childSource != null &&
                        relation.parentColumns.length == relation.childColumns.length &&
                        this.isColumnsExist(relation)) {
                        let finded = false;
                        let columns = "";
                        for (let index = 0; index < relation.parentColumns.length; index++) {
                            let parentColumn = relation.parentColumns[index];
                            let childColumn = relation.childColumns[index];
                            let parentType = relation.parentSource.columns.getByName(parentColumn).type;
                            let childType = relation.childSource.columns.getByName(childColumn).type;
                            if (parentType != childType) {
                                if (columns.length == 0)
                                    columns = parentColumn + "-" + childColumn;
                                else
                                    columns += "; " + parentColumn + "-" + childColumn;
                                finded = true;
                            }
                        }
                        if (finded) {
                            let check = new StiKeysTypesMismatchDataRelationCheck();
                            check.columns = columns;
                            check.element = obj;
                            return check;
                        }
                    }
                    return null;
                }
            }
            Check.StiKeysTypesMismatchDataRelationCheck = StiKeysTypesMismatchDataRelationCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiDataRelation = Stimulsoft.Report.Dictionary.StiDataRelation;
            class StiNoNameDataRelationCheck extends Check.StiDataRelationCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Error;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckDataRelation", "StiNoNameDataRelationCheckShort"), this.dataSources);
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckDataRelation", "StiNoNameDataRelationCheckLong"), this.dataSources);
                }
                processCheck(report, obj) {
                    this.element = obj;
                    let relation = obj.stimulsoft().as(StiDataRelation);
                    if (relation != null && StiString.isNullOrEmpty(relation.name)) {
                        let check = new StiNoNameDataRelationCheck();
                        check.element = obj;
                        check.dataSources = "";
                        if (relation.parentSource != null)
                            check.dataSources = relation.parentSource.name;
                        if (relation.parentSource != null) {
                            if (StiString.isNullOrEmpty(check.dataSources))
                                check.dataSources = relation.childSource.name;
                            else
                                check.dataSources += "; " + relation.childSource.name;
                        }
                        check.actions.push(new Check.StiGenerateNewNameRelationAction());
                        check.actions.push(new Check.StiDeleteDataRelationAction());
                        return check;
                    }
                    return null;
                }
            }
            Check.StiNoNameDataRelationCheck = StiNoNameDataRelationCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiDataRelation = Stimulsoft.Report.Dictionary.StiDataRelation;
            class StiNoNameInSourceDataRelationCheck extends Check.StiDataRelationCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Error;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckDataRelation", "StiNoNameInSourceDataRelationCheckShort"), this.elementName);
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckDataRelation", "StiNoNameInSourceDataRelationCheckLong"), this.elementName);
                }
                processCheck(report, obj) {
                    this.element = obj;
                    let relation = obj.stimulsoft().as(StiDataRelation);
                    if (relation != null && StiString.isNullOrEmpty(relation.nameInSource)) {
                        let check = new StiNoNameInSourceDataRelationCheck();
                        check.actions.push(new Check.StiDeleteDataRelationAction());
                        check.element = obj;
                        return check;
                    }
                    return null;
                }
            }
            Check.StiNoNameInSourceDataRelationCheck = StiNoNameInSourceDataRelationCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiDataRelation = Stimulsoft.Report.Dictionary.StiDataRelation;
            class StiSourcesInAbsentDataRelationCheck extends Check.StiDataRelationCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Warning;
                }
                get previewVisible() {
                    return false;
                }
                get shortMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckDataRelation", "StiSourcesInAbsentDataRelationCheckShort"), this.elementName);
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckDataRelation", "StiSourcesInAbsentDataRelationCheckLong"), this.elementName);
                }
                processCheck(report, obj) {
                    this.element = obj;
                    let relation = obj.stimulsoft().as(StiDataRelation);
                    if (relation != null && (relation.parentSource == null || relation.childSource == null)) {
                        let check = new StiSourcesInAbsentDataRelationCheck();
                        check.actions.push(new Check.StiDeleteDataRelationAction());
                        check.element = obj;
                        return check;
                    }
                    return null;
                }
            }
            Check.StiSourcesInAbsentDataRelationCheck = StiSourcesInAbsentDataRelationCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiDataBuilder = Stimulsoft.Report.Dictionary.StiDataBuilder;
            class StiDataColumnCheck extends Check.StiCheck {
                constructor() {
                    super(...arguments);
                    this.dataBuilder = new StiDataBuilder();
                    this.objectType = Check.StiCheckObjectType.DataColumn;
                }
                get previewVisible() {
                    return false;
                }
                get elementName() {
                    if (this.element == null)
                        return null;
                    return this.element.name;
                }
            }
            Check.StiDataColumnCheck = StiDataColumnCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiCalcDataColumn = Stimulsoft.Report.Dictionary.StiCalcDataColumn;
            var StiText = Stimulsoft.Report.Components.StiText;
            var StiParser = Stimulsoft.Report.Engine.StiParser;
            class StiCalculatedColumnRecursionCheck extends Check.StiDataColumnCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Error;
                }
                get shortMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckDataSource", "StiCalculatedColumnRecursionCheckShort"), this.elementName);
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckDataSource", "StiCalculatedColumnRecursionCheckLong"), this.elementName, this.element.dataSource.name);
                }
                checkForRecursion(column, report) {
                    try {
                        let fullColumnName = column.dataSource.name + "." + column.name;
                        let REFstoreToPrint = { ref: false };
                        let comp = new StiText();
                        comp.name = column.name;
                        comp.page = report.pages.getByIndex(0);
                        let result = StiParser.StiParser.parseTextValue("{" + column.expression + "}", comp, comp, REFstoreToPrint, false, true);
                        let list = result;
                        if (list != null) {
                            for (let command of list) {
                                if ((command.type == Stimulsoft.Report.Engine.StiAsmCommandType.PushDataSourceField) && (fullColumnName == command.parameter1)) {
                                    return true;
                                }
                            }
                        }
                    }
                    catch (e) {
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    if (obj.stimulsoft().is(StiCalcDataColumn)) {
                        if (this.checkForRecursion(obj, report)) {
                            let check = new StiCalculatedColumnRecursionCheck();
                            check.element = obj;
                            check.actions.push(new Check.StiEditPropertyAction());
                            return check;
                        }
                    }
                    return null;
                }
            }
            Check.StiCalculatedColumnRecursionCheck = StiCalculatedColumnRecursionCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiDataBuilder = Stimulsoft.Report.Dictionary.StiDataBuilder;
            class StiDataSourceCheck extends Check.StiCheck {
                constructor() {
                    super(...arguments);
                    this.dataBuilder = new StiDataBuilder();
                    this.objectType = Check.StiCheckObjectType.DataSource;
                }
                get elementName() {
                    if (this.element == null)
                        return null;
                    return this.element.name;
                }
            }
            Check.StiDataSourceCheck = StiDataSourceCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiDataSource = Stimulsoft.Report.Dictionary.StiDataSource;
            class StiNoNameDataSourceCheck extends Check.StiDataSourceCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Error;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckDataSource", "StiNoNameDataSourceCheckShort"));
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckDataSource", "StiNoNameDataSourceCheckLong"));
                }
                processCheck(report, obj) {
                    this.element = obj;
                    let dataSource = obj.stimulsoft().as(StiDataSource);
                    if (dataSource != null && StiString.isNullOrEmpty(dataSource.name)) {
                        let check = new StiNoNameDataSourceCheck();
                        check.element = obj;
                        check.actions.push(new Check.StiGenerateNewNameDataSourceAction());
                        check.actions.push(new Check.StiDeleteDataSourceAction());
                        return check;
                    }
                    return null;
                }
            }
            Check.StiNoNameDataSourceCheck = StiNoNameDataSourceCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiDataStoreSource = Stimulsoft.Report.Dictionary.StiDataStoreSource;
            class StiNoNameInSourceDataSourceCheck extends Check.StiDataSourceCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Error;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckDataSource", "StiNoNameInSourceDataSourceCheckShort"), this.elementName);
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckDataSource", "StiNoNameInSourceDataSourceCheckLong"), this.elementName);
                }
                processCheck(report, obj) {
                    this.element = obj;
                    let dataSource = obj.stimulsoft().as(StiDataStoreSource);
                    if (dataSource != null && StiString.isNullOrEmpty(dataSource.nameInSource)) {
                        let check = new StiNoNameInSourceDataSourceCheck();
                        check.actions.push(new Check.StiDeleteDataSourceAction());
                        check.element = obj;
                        return check;
                    }
                    return null;
                }
            }
            Check.StiNoNameInSourceDataSourceCheck = StiNoNameInSourceDataSourceCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiUndefinedDataSource = Stimulsoft.Report.Dictionary.StiUndefinedDataSource;
            class StiUndefinedDataSourceCheck extends Check.StiDataSourceCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Error;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckDataSource", "StiUndefinedDataSourceCheckShort"), this.elementName);
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckDataSource", "StiUndefinedDataSourceCheckLong"), this.elementName);
                }
                processCheck(report, obj) {
                    this.element = obj;
                    if (obj.stimulsoft().is(StiUndefinedDataSource)) {
                        let check = new StiUndefinedDataSourceCheck();
                        check.element = obj;
                        check.actions.push(new Check.StiDeleteDataSourceAction());
                        return check;
                    }
                    return null;
                }
            }
            Check.StiUndefinedDataSourceCheck = StiUndefinedDataSourceCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiResourceType = Stimulsoft.Report.Dictionary.StiResourceType;
            var StiXmlDatabase = Stimulsoft.Report.Dictionary.StiXmlDatabase;
            var StiImage = Stimulsoft.Report.Components.StiImage;
            var StiRichText = Stimulsoft.Report.Components.StiRichText;
            var StiSubReport = Stimulsoft.Report.Components.StiSubReport;
            var StiString = Stimulsoft.System.StiString;
            class StiUsedResourceHelper {
                static getDatabasesUsedResource(report, resource) {
                    let resName = StiString.format("resource://{0}", resource.name);
                    let fileDatabases = [];
                    for (let database of report.dictionary.databases.list) {
                        let fileDatabase = null;
                        switch (resource.type) {
                            case StiResourceType.Csv:
                                fileDatabase = database;
                                break;
                            case StiResourceType.Excel:
                                fileDatabase = database;
                                break;
                            case StiResourceType.Json:
                                fileDatabase = database;
                                break;
                            case StiResourceType.Xml:
                            case StiResourceType.Xsd:
                                fileDatabase = database;
                                break;
                        }
                        if (database.is(StiXmlDatabase) && resource.type == StiResourceType.Xsd) {
                            if (database.pathSchema == resName)
                                fileDatabases.push(fileDatabase);
                        }
                        else if (fileDatabase != null) {
                            if (fileDatabase.pathData == resName)
                                fileDatabases.push(fileDatabase);
                        }
                    }
                    return fileDatabases;
                }
                static getComponentsUsedResource(report, resource) {
                    let usedComponents = [];
                    switch (resource.type) {
                        case StiResourceType.Image: {
                            let usedImages = StiUsedResourceHelper.getImageComponentsUsedResource(report, resource);
                            usedComponents.stimulsoft().addRange(usedImages);
                            break;
                        }
                        case StiResourceType.Rtf:
                        case StiResourceType.Txt: {
                            let usedRichText = StiUsedResourceHelper.getRichTextComponentsUsedResource(report, resource);
                            usedComponents.stimulsoft().addRange(usedRichText);
                            break;
                        }
                        case StiResourceType.Report:
                        case StiResourceType.ReportSnapshot: {
                            let usedReports = StiUsedResourceHelper.getReportsUsedResource(report, resource);
                            usedComponents.stimulsoft().addRange(usedReports);
                            break;
                        }
                    }
                    return usedComponents;
                }
                static getImageComponentsUsedResource(report, resource) {
                    let resName = StiString.format("resource://{0}", resource.name);
                    let images = report.getComponents().toList().where(d => d != null && d.is(StiImage));
                    let usedImages = images.where(d => d.imageURL == resName);
                    return usedImages;
                }
                static getRichTextComponentsUsedResource(report, resource) {
                    let resName = StiString.format("resource://{0}", resource.name);
                    let images = report.getComponents().toList().where(d => d != null && d.is(StiRichText));
                    let usedImages = images.where(d => d.dataUrl == resName);
                    return usedImages;
                }
                static getReportsUsedResource(report, resource) {
                    let resName = StiString.format("resource://{0}", resource.name);
                    let images = report.getComponents().toList().where(d => d != null && d.is(StiSubReport));
                    let usedImages = images.where(d => d.subReportUrl == resName);
                    return usedImages;
                }
            }
            Check.StiUsedResourceHelper = StiUsedResourceHelper;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            class StiPageCheck extends Check.StiCheck {
                constructor() {
                    super(...arguments);
                    this.objectType = Check.StiCheckObjectType.Page;
                }
                get previewVisible() {
                    return true;
                }
                get elementName() {
                    if (this.element == null)
                        return null;
                    return this.element.name;
                }
            }
            Check.StiPageCheck = StiPageCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiPage = Stimulsoft.Report.Components.StiPage;
            class StiColumnsWidthGreaterPageWidthCheck extends Stimulsoft.Report.Check.StiPageCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Warning;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiColumnsWidthGreaterPageWidthCheckShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiColumnsWidthGreaterPageWidthCheckLong"), this.elementName);
                }
                check() {
                    let page = this.element.stimulsoft().as(StiPage);
                    if (page != null && page.columns > 0) {
                        let sumWidth = page.columnWidth * page.columns + page.columnGaps * (page.columns - 1);
                        if (sumWidth * 0.99 > page.width)
                            return true;
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    try {
                        let failed = this.check();
                        if (failed) {
                            let check = new StiColumnsWidthGreaterPageWidthCheck();
                            check.element = obj;
                            check.actions.push(new Check.StiColumnsWidthGreaterContainerWidthAction());
                            return check;
                        }
                        else
                            return null;
                    }
                    finally {
                        this.element = null;
                    }
                }
            }
            Check.StiColumnsWidthGreaterPageWidthCheck = StiColumnsWidthGreaterPageWidthCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiPage = Stimulsoft.Report.Components.StiPage;
            class StiComponentStyleIsNotFoundAtPageCheck extends Check.StiPageCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Warning;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiComponentStyleIsNotFoundCheckShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiComponentStyleIsNotFoundCheckAtPageLong"), this.elementName);
                }
                check() {
                    let page = this.element.stimulsoft().as(StiPage);
                    if (page != null && !StiString.isNullOrWhiteSpace(page.componentStyle) && !page.report.styles.contains(page.componentStyle)) {
                        return true;
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    let failed = this.check();
                    if (failed) {
                        let check = new StiComponentStyleIsNotFoundAtPageCheck();
                        check.element = obj;
                        check.actions.push(new Check.StiComponentStyleIsNotFoundAtComponentAction());
                        return check;
                    }
                    else
                        return null;
                }
            }
            Check.StiComponentStyleIsNotFoundAtPageCheck = StiComponentStyleIsNotFoundAtPageCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiPage = Stimulsoft.Report.Components.StiPage;
            class StiCrossGroupHeaderNotEqualToCrossGroupFooterOnPageCheck extends Check.StiPageCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Information;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiGroupHeaderNotEqualToGroupFooterShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiCrossGroupHeaderNotEqualToCrossGroupFooterOnPageLong"), this.elementName);
                }
                check() {
                    let page = this.element.stimulsoft().as(StiPage);
                    if (page != null) {
                        let countCrossGroupHeader = 0;
                        let countCrossGroupFooter = 0;
                        for (let index = 0; index < page.components.count; index++) {
                        }
                        if (countCrossGroupHeader != countCrossGroupFooter) {
                            return true;
                        }
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    try {
                        let failed = this.check();
                        if (failed) {
                            let check = new StiCrossGroupHeaderNotEqualToCrossGroupFooterOnPageCheck();
                            check.element = obj;
                            return check;
                        }
                        else
                            return null;
                    }
                    finally {
                        this.element = null;
                    }
                }
            }
            Check.StiCrossGroupHeaderNotEqualToCrossGroupFooterOnPageCheck = StiCrossGroupHeaderNotEqualToCrossGroupFooterOnPageCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiPage = Stimulsoft.Report.Components.StiPage;
            var StiGroupHeaderBand = Stimulsoft.Report.Components.StiGroupHeaderBand;
            var StiGroupFooterBand = Stimulsoft.Report.Components.StiGroupFooterBand;
            class StiGroupHeaderNotEqualToGroupFooterOnPageCheck extends Check.StiPageCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Information;
                }
                get previewVisible() {
                    return true;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiGroupHeaderNotEqualToGroupFooterShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiGroupHeaderNotEqualToGroupFooterOnPageLong"), this.elementName);
                }
                check() {
                    let page = this.element.stimulsoft().as(StiPage);
                    if (page != null) {
                        let countGroupHeader = 0;
                        let countGroupFooter = 0;
                        for (let index = 0; index < page.components.count; index++) {
                            if (page.components.getByIndex(index).is(StiGroupHeaderBand)) {
                                countGroupHeader++;
                            }
                            else if (page.components.getByIndex(index).is(StiGroupFooterBand)) {
                                countGroupFooter++;
                            }
                        }
                        if (countGroupHeader < countGroupFooter) {
                            return true;
                        }
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    try {
                        let failed = this.check();
                        if (failed) {
                            let check = new StiGroupHeaderNotEqualToGroupFooterOnPageCheck();
                            check.element = obj;
                            return check;
                        }
                        else
                            return null;
                    }
                    finally {
                        this.element = null;
                    }
                }
            }
            Check.StiGroupHeaderNotEqualToGroupFooterOnPageCheck = StiGroupHeaderNotEqualToGroupFooterOnPageCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiPage = Stimulsoft.Report.Components.StiPage;
            var StiBand = Stimulsoft.Report.Components.StiBand;
            class StiLargeHeightAtPageCheck extends Check.StiPageCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Information;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiLargeHeightAtPageCheckShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiLargeHeightAtPageCheckLong"), this.element);
                }
                check() {
                    let page = this.element.stimulsoft().as(StiPage);
                    if (true) {
                        let sumHeight = 0;
                        let count = 0;
                        for (let comp of page.components.list) {
                            if (comp.is(StiBand) && !comp.isCross) {
                                sumHeight += comp.height;
                                count++;
                            }
                        }
                        if (page.report.info.showHeaders && count > 0)
                            sumHeight += page.gridSize * count * 2;
                        sumHeight += count > 1 ? page.gridSize * count - 1 : page.gridSize;
                        let pageHeight = page.height;
                        pageHeight *= page.largeHeight ? page.largeHeightFactor : 1;
                        if (pageHeight - sumHeight <= pageHeight * 0.1) {
                            return true;
                        }
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    let failed = this.check();
                    if (failed) {
                        let check = new StiLargeHeightAtPageCheck();
                        check.element = obj;
                        check.actions.push(new Check.StiLargeHeightAtPageAction());
                        return check;
                    }
                    else
                        return null;
                }
            }
            Check.StiLargeHeightAtPageCheck = StiLargeHeightAtPageCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiPage = Stimulsoft.Report.Components.StiPage;
            var StiPointPrimitive = Stimulsoft.Report.Components.StiPointPrimitive;
            var StiCrossLinePrimitive = Stimulsoft.Report.Components.StiCrossLinePrimitive;
            var Hashtable = Stimulsoft.System.Collections.Hashtable;
            class StiLostPointsOnPageCheck extends Check.StiPageCheck {
                constructor() {
                    super(...arguments);
                    this.lostPointsNames = "";
                    this.status = Check.StiCheckStatus.Warning;
                }
                get previewVisible() {
                    return false;
                }
                get page() {
                    return this.element;
                }
                get shortMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckPage", "StiLostPointsOnPageCheckShort"), this.elementName, this.lostPointsNames);
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckPage", "StiLostPointsOnPageCheckLong"), this.elementName, this.lostPointsNames);
                }
                static getLostPointsOnPage(page) {
                    let comps = page.getComponents();
                    let points = new Hashtable();
                    let lines = [];
                    for (let comp of comps.list) {
                        if (comp.is(StiPointPrimitive))
                            points.add(comp, comp);
                        else if (comp.is(StiCrossLinePrimitive))
                            lines.push(comp);
                    }
                    for (let line of lines) {
                        let startPoint = line.getStartPoint(line.page);
                        let endPoint = line.getEndPoint(line.page);
                        if (startPoint != null && points.containsKey(startPoint)) {
                            points.remove(startPoint);
                        }
                        if (endPoint != null && points.containsKey(endPoint)) {
                            points.remove(endPoint);
                        }
                    }
                    let lostPoints = [];
                    for (let point of points.values) {
                        lostPoints.push(point);
                    }
                    return lostPoints;
                }
                processCheck(report, obj) {
                    let page = obj.stimulsoft().as(StiPage);
                    let points = StiLostPointsOnPageCheck.getLostPointsOnPage(page);
                    if (points != null && points.length > 0) {
                        let sb = "";
                        for (let point of points) {
                            if (sb.length > 0)
                                sb += ", ";
                            sb += point.name;
                        }
                        let check = new StiLostPointsOnPageCheck();
                        check.element = obj;
                        check.lostPointsNames = sb;
                        check.actions.push(new Check.StiDeleteLostPointsAction());
                        return check;
                    }
                    else
                        return null;
                }
            }
            Check.StiLostPointsOnPageCheck = StiLostPointsOnPageCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiComponent = Stimulsoft.Report.Components.StiComponent;
            class StiNoNamePageCheck extends Check.StiPageCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Error;
                }
                get page() {
                    return this.element;
                }
                get shortMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiNoNamePageCheckShort"), this.page.report.pages.indexOf(this.page));
                }
                get longMessage() {
                    if (this.page != null)
                        return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiNoNamePageCheckLong"), this.page.report.pages.indexOf(this.page));
                    else
                        return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiNoNamePageCheckLong"), "");
                }
                processCheck(report, obj) {
                    let comp = obj.stimulsoft().as(StiComponent);
                    let failed = false;
                    if (StiString.isNullOrEmpty(comp.name)) {
                        failed = true;
                    }
                    if (failed) {
                        let check = new StiNoNamePageCheck();
                        check.element = obj;
                        check.actions.push(new Check.StiGenerateNewNamePageAction());
                        check.actions.push(new Check.StiDeletePageAction());
                        return check;
                    }
                    else
                        return null;
                }
            }
            Check.StiNoNamePageCheck = StiNoNamePageCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiPage = Stimulsoft.Report.Components.StiPage;
            var StiPageOrientation = Stimulsoft.Report.Components.StiPageOrientation;
            class StiOrientationPageCheck extends Check.StiPageCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Warning;
                }
                get shortMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckPage", "StiOrientationPageCheckShort"), this.elementName);
                }
                get longMessage() {
                    let page = this.element.stimulsoft().as(StiPage);
                    if (page != null) {
                        if (page.orientation == StiPageOrientation.Portrait)
                            return StiString.format(Check.StiLocalizationExt.get("CheckPage", "StiOrientationPageCheckLongPortrait"), this.elementName);
                        else
                            return StiString.format(Check.StiLocalizationExt.get("CheckPage", "StiOrientationPageCheckLongLandscape"), this.elementName);
                    }
                    else {
                        return StiString.format(Check.StiLocalizationExt.get("CheckPage", "StiOrientationPageCheckLongPortrait"), this.elementName);
                    }
                }
                processCheck(report, obj) {
                    let page = obj.stimulsoft().as(StiPage);
                    let failed = false;
                    if (page.orientation == StiPageOrientation.Portrait) {
                        if (page.pageWidth > page.pageHeight)
                            failed = true;
                    }
                    else {
                        if (page.pageWidth < page.pageHeight)
                            failed = true;
                    }
                    if (failed) {
                        let check = new StiOrientationPageCheck();
                        check.element = obj;
                        if (page.orientation == StiPageOrientation.Portrait)
                            check.actions.push(new Check.StiOrientationPageToLandscapeAction());
                        else
                            check.actions.push(new Check.StiOrientationPageToPortraitAction());
                        check.actions.push(new Check.StiSwitchWidthAndHeightOfPageAction());
                        return check;
                    }
                    else
                        return null;
                }
            }
            Check.StiOrientationPageCheck = StiOrientationPageCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiPage = Stimulsoft.Report.Components.StiPage;
            class StiPrintHeadersAndFootersFromPreviousPageCheck extends Check.StiPageCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Information;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiPrintHeadersAndFootersFromPreviousPageShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiPrintHeadersAndFootersFromPreviousPageLong"), this.element);
                }
                getPageCount(report) {
                    let count = 0;
                    report.pages.list.forEach(page => count++);
                    return count;
                }
                check() {
                    let page = this.element.stimulsoft().as(StiPage);
                    let report = page.report;
                    if (page != null && page.printHeadersFootersFromPreviousPage && (this.getPageCount(report) == 1 || report.pages.indexOf(page) == 0)) {
                        return true;
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    let failed = this.check();
                    if (failed) {
                        let check = new StiPrintHeadersAndFootersFromPreviousPageCheck();
                        check.element = obj;
                        check.actions.push(new Check.StiPrintHeadersFootersFromPreviousPageAction());
                        return check;
                    }
                    else
                        return null;
                }
            }
            Check.StiPrintHeadersAndFootersFromPreviousPageCheck = StiPrintHeadersAndFootersFromPreviousPageCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiPage = Stimulsoft.Report.Components.StiPage;
            class StiPrintOnPreviousPageCheck extends Check.StiPageCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Information;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiPrintOnPreviousPageCheckShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiPrintOnPreviousPageCheckLong"), this.element);
                }
                getPageCount(report) {
                    let count = 0;
                    report.pages.list.forEach(page => count++);
                    return count;
                }
                check() {
                    let page = this.element.stimulsoft().as(StiPage);
                    let report = page.report;
                    if (page != null && page.printOnPreviousPage && (this.getPageCount(report) == 1 || report.pages.indexOf(page) == 0)) {
                        return true;
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    let failed = this.check();
                    if (failed) {
                        let check = new StiPrintOnPreviousPageCheck();
                        check.element = obj;
                        check.actions.push(new Check.StiPrintOnPreviousPageAction());
                        return check;
                    }
                    else
                        return null;
                }
            }
            Check.StiPrintOnPreviousPageCheck = StiPrintOnPreviousPageCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiPage = Stimulsoft.Report.Components.StiPage;
            var StiBand = Stimulsoft.Report.Components.StiBand;
            class StiPrintOnPreviousPageCheck2 extends Check.StiPageCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Information;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiPrintOnPreviousPageCheck2Short");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiPrintOnPreviousPageCheck2Long"), this.element);
                }
                check() {
                    let page = this.element.stimulsoft().as(StiPage);
                    if (page != null && page.printOnPreviousPage) {
                        for (let comp of page.components.list) {
                            if (comp.is(StiBand))
                                continue;
                            return true;
                        }
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    let failed = this.check();
                    if (failed) {
                        let check = new StiPrintOnPreviousPageCheck2();
                        check.element = obj;
                        return check;
                    }
                    else
                        return null;
                }
            }
            Check.StiPrintOnPreviousPageCheck2 = StiPrintOnPreviousPageCheck2;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiPage = Stimulsoft.Report.Components.StiPage;
            class StiResetPageNumberCheck extends Check.StiPageCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Information;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiResetPageNumberCheckShort");
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckComponent", "StiResetPageNumberCheckLong"), this.element);
                }
                getPageCount(report) {
                    let count = 0;
                    report.pages.list.forEach(page => count++);
                    return count;
                }
                check() {
                    let page = this.element.stimulsoft().as(StiPage);
                    let report = page.report;
                    if (page != null && page.resetPageNumber && (this.getPageCount(report) == 1 || report.pages.indexOf(page) == 0)) {
                        return true;
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    let failed = this.check();
                    if (failed) {
                        let check = new StiResetPageNumberCheck();
                        check.element = obj;
                        check.actions.push(new Check.StiResetPageNumberAction());
                        return check;
                    }
                    else
                        return null;
                }
            }
            Check.StiResetPageNumberCheck = StiResetPageNumberCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            class StiReportCheck extends Check.StiCheck {
                constructor() {
                    super(...arguments);
                    this.objectType = Check.StiCheckObjectType.Report;
                }
                get elementName() {
                    if (this.element == null)
                        return null;
                    return this.element.reportName;
                }
            }
            Check.StiReportCheck = StiReportCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiDataSource = Stimulsoft.Report.Dictionary.StiDataSource;
            var StiDataRelation = Stimulsoft.Report.Dictionary.StiDataRelation;
            var StiComponent = Stimulsoft.Report.Components.StiComponent;
            var StiDataBuilder = Stimulsoft.Report.Dictionary.StiDataBuilder;
            var Hashtable = Stimulsoft.System.Collections.Hashtable;
            class StiDuplicatedNameCheck extends Check.StiReportCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Error;
                    this.dataBuilder = new StiDataBuilder();
                }
                get elementName() {
                    if (this.element == null)
                        return null;
                    if (this.element.stimulsoft().is(StiDataSource))
                        return this.element.name;
                    if (this.element.stimulsoft().is(StiDataRelation))
                        return this.element.name;
                    if (this.element.stimulsoft().is(StiComponent))
                        return this.element.name;
                    return null;
                }
                get previewVisible() {
                    return this.element.stimulsoft().is(StiComponent) || this.element.stimulsoft().is(StiDataSource);
                }
                get shortMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckReport", "StiDuplicatedNameCheckShort"));
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckReport", this.isDataSource ? "StiDuplicatedName2CheckLong" : "StiDuplicatedNameCheckLong"), this.elementName);
                }
                get isDataSource() {
                    return this._isDataSource;
                }
                set isDataSource(value) {
                    this._isDataSource = value;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    let checks = null;
                    let hash = new Hashtable();
                    for (let dataSource of report.dictionary.dataSources.list) {
                        if (hash.containsKey(dataSource.name)) {
                            let check = new StiDuplicatedNameCheck();
                            check.element = dataSource;
                            check.isDataSource = true;
                            check.actions.push(new Check.StiEditNameAction());
                            check.actions.push(new Check.StiGenerateNewNameDataSourceAction());
                            check.actions.push(new Check.StiDeleteDataSourceAction());
                            if (checks == null)
                                checks = [];
                            checks.push(check);
                        }
                        else {
                            hash.set(dataSource.name, null);
                        }
                    }
                    if (report.calculationMode == Report.StiCalculationMode.Interpretation)
                        return null;
                    let comps = report.getComponents();
                    for (let comp of comps.list) {
                        if (hash.containsKey(comp.name)) {
                            let check = new StiDuplicatedNameCheck();
                            check.element = comp;
                            check.actions.push(new Check.StiEditNameAction());
                            check.actions.push(new Check.StiGenerateNewNameComponentAction());
                            if (checks == null)
                                checks = [];
                            checks.push(check);
                        }
                        else {
                            hash.set(comp.name, null);
                        }
                    }
                    return checks;
                }
            }
            Check.StiDuplicatedNameCheck = StiDuplicatedNameCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiDataSource = Stimulsoft.Report.Dictionary.StiDataSource;
            var Hashtable = Stimulsoft.System.Collections.Hashtable;
            class StiDuplicatedNameInSourceCheck extends Check.StiReportCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Warning;
                }
                get elementName() {
                    if (this.element == null)
                        return null;
                    if (this.element.stimulsoft().is(StiDataSource))
                        return this.element.name;
                    return null;
                }
                get previewVisible() {
                    return false;
                }
                get shortMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckReport", "StiDuplicatedNameInSourceCheckShort"));
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckReport", "StiDuplicatedName3CheckLong"), this.elementName);
                }
                get isDataSource() {
                    return this._isDataSource;
                }
                set isDataSource(value) {
                    this._isDataSource = value;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    let checks = null;
                    let hash = new Hashtable();
                    for (let dataSource of report.dictionary.dataSources.list) {
                        if (hash.containsKey(dataSource.name.toLowerCase())) {
                            let check = new StiDuplicatedNameInSourceCheck();
                            check.element = dataSource;
                            check.isDataSource = true;
                            check.actions.push(new Check.StiEditNameAction());
                            check.actions.push(new Check.StiDeleteDataSourceAction());
                            if (checks == null)
                                checks = [];
                            checks.push(check);
                        }
                        else {
                            hash.set(dataSource.name.toLowerCase(), null);
                        }
                    }
                    return checks;
                }
            }
            Check.StiDuplicatedNameInSourceCheck = StiDuplicatedNameInSourceCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var Hashtable = Stimulsoft.System.Collections.Hashtable;
            class StiDuplicatedNameInSourceInDataRelationReportCheck extends Check.StiReportCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Warning;
                }
                get previewVisible() {
                    return false;
                }
                get shortMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckReport", "StiDuplicatedNameInSourceInDataRelationReportCheckShort"), this.relationsNames, this.relationsNameInSource);
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckReport", "StiDuplicatedNameInSourceInDataRelationReportCheckLong"), this.relationsNames, this.relationsNameInSource);
                }
                processCheck(report, obj) {
                    this.element = obj;
                    let checks = null;
                    let rels = new Hashtable();
                    for (let relation of report.dictionary.relations.list) {
                        let list = rels.get(relation.nameInSource);
                        if (list == null) {
                            list = [];
                            rels.set(relation.nameInSource, list);
                        }
                        list.push(relation);
                    }
                    for (let list of rels.values) {
                        if (list.length > 1) {
                            let names = "";
                            let nameInSource = "";
                            for (let relation of list) {
                                if (names.length == 0)
                                    names += relation.name;
                                else
                                    names += "; " + relation.name;
                                nameInSource = relation.nameInSource;
                            }
                            if (!StiString.isNullOrEmpty(nameInSource)) {
                                let check = new StiDuplicatedNameInSourceInDataRelationReportCheck();
                                check.element = obj;
                                check.relationsNames = names;
                                check.relationsNameInSource = nameInSource;
                                if (checks == null)
                                    checks = [];
                                checks.push(check);
                            }
                        }
                    }
                    return checks;
                }
            }
            Check.StiDuplicatedNameInSourceInDataRelationReportCheck = StiDuplicatedNameInSourceInDataRelationReportCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            class StiIsFirstPageIsLastPageDoublePassCheck extends Check.StiReportCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Warning;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiIsFirstPageIsLastPageDoublePassCheckShort");
                }
                get longMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiIsFirstPageIsLastPageDoublePassCheckLong");
                }
                check() {
                    let report = this.element.stimulsoft().as(Report.StiReport);
                    if (report.numberOfPass != Report.StiNumberOfPass.DoublePass) {
                        let variables = [
                            "IsFirstPage",
                            "IsFirstPageThrough",
                            "IsLastPage",
                            "IsLastPageThrough"
                        ];
                        for (let index = 0; index < variables.length; index++) {
                            if (report.script.indexOf(variables[index]) != -1)
                                return true;
                        }
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    let failed = this.check();
                    if (failed) {
                        let check = new StiIsFirstPageIsLastPageDoublePassCheck();
                        check.element = obj;
                        check.actions.push(new Check.StiAllowDoublePassAction());
                        return check;
                    }
                    else
                        return null;
                }
            }
            Check.StiIsFirstPageIsLastPageDoublePassCheck = StiIsFirstPageIsLastPageDoublePassCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            class StiIsFirstPassIsSecondPassCheck extends Check.StiReportCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Information;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiIsFirstPassIsSecondPassCheckShort");
                }
                get longMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiIsFirstPassIsSecondPassCheckLong");
                }
                check() {
                    let report = this.element.stimulsoft().as(Report.StiReport);
                    if (report.numberOfPass == Report.StiNumberOfPass.SinglePass) {
                        let variables = ["IsFirstPass", "IsSecondPass"];
                        for (let index = 0; index < variables.length; index++) {
                            if (report.script.indexOf(variables[index]) != -1)
                                return true;
                        }
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    let failed = this.check();
                    if (failed) {
                        let check = new StiIsFirstPassIsSecondPassCheck();
                        check.element = obj;
                        check.actions.push(new Check.StiAllowDoublePassAction());
                        return check;
                    }
                    else
                        return null;
                }
            }
            Check.StiIsFirstPassIsSecondPassCheck = StiIsFirstPassIsSecondPassCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            class StiReportRenderingMessageCheck extends Check.StiReportCheck {
                constructor() {
                    super(...arguments);
                    this._longMessage = null;
                    this.status = Check.StiCheckStatus.ReportRenderingMessage;
                }
                get shortMessage() {
                    return "";
                }
                get longMessage() {
                    return this._longMessage;
                }
                setMessage(message) {
                    this._longMessage = message;
                }
                processCheck(report, msg) {
                }
            }
            Check.StiReportRenderingMessageCheck = StiReportRenderingMessageCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            class StiTotalPageCountDoublePassCheck extends Check.StiReportCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Information;
                }
                get shortMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiIsFirstPageIsLastPageDoublePassCheckShort");
                }
                get longMessage() {
                    return Check.StiLocalizationExt.get("CheckComponent", "StiTotalPageCountDoublePassCheckLong");
                }
                check() {
                    let report = this.element.stimulsoft().as(Report.StiReport);
                    if (report.numberOfPass != Report.StiNumberOfPass.DoublePass) {
                        let variables = [
                            "TotalPageCount",
                            "TotalPageCountThrough"
                        ];
                        for (let index = 0; index < variables.length; index++) {
                            if (report.script.indexOf(variables[index]) != -1)
                                return true;
                        }
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    let failed = this.check();
                    if (failed) {
                        let check = new StiTotalPageCountDoublePassCheck();
                        check.element = obj;
                        check.actions.push(new Check.StiAllowDoublePassAction());
                        return check;
                    }
                    else
                        return null;
                }
            }
            Check.StiTotalPageCountDoublePassCheck = StiTotalPageCountDoublePassCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            class StiVariableCheck extends Check.StiCheck {
                constructor() {
                    super(...arguments);
                    this.objectType = Check.StiCheckObjectType.Variable;
                }
                get previewVisible() {
                    return false;
                }
                get elementName() {
                    if (this.element == null)
                        return null;
                    return this.element.name;
                }
            }
            Check.StiVariableCheck = StiVariableCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
var Stimulsoft;
(function (Stimulsoft) {
    var Report;
    (function (Report) {
        var Check;
        (function (Check) {
            var StiString = Stimulsoft.System.StiString;
            var StiVariable = Stimulsoft.Report.Dictionary.StiVariable;
            var StiText = Stimulsoft.Report.Components.StiText;
            var StiParser = Stimulsoft.Report.Engine.StiParser;
            class StiVariableRecursionCheck extends Check.StiVariableCheck {
                constructor() {
                    super(...arguments);
                    this.status = Check.StiCheckStatus.Error;
                }
                get shortMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckVariable", "StiVariableRecursionCheckShort"), this.elementName);
                }
                get longMessage() {
                    return StiString.format(Check.StiLocalizationExt.get("CheckVariable", "StiVariableRecursionCheckLong"), this.elementName);
                }
                checkForRecursion(variable, report) {
                    try {
                        let REFstoreToPrint = { ref: false };
                        let comp = new StiText();
                        comp.name = variable.name;
                        comp.page = report.pages.getByIndex(0);
                        let result = StiParser.StiParser.parseTextValue("{" + variable.value + "}", comp, comp, REFstoreToPrint, false, true);
                        let list = result;
                        if (list != null) {
                            for (let command of list) {
                                if ((command.type == Stimulsoft.Report.Engine.StiAsmCommandType.PushVariable) && (variable.name == command.parameter1)) {
                                    return true;
                                }
                            }
                        }
                    }
                    catch (e) {
                    }
                    return false;
                }
                processCheck(report, obj) {
                    this.element = obj;
                    if (obj.stimulsoft().is(StiVariable)) {
                        let variable = obj;
                        if ((variable.initBy == Stimulsoft.Report.Dictionary.StiVariableInitBy.Expression) && this.checkForRecursion(variable, report)) {
                            let check = new StiVariableRecursionCheck();
                            check.element = obj;
                            check.actions.push(new Check.StiEditPropertyAction());
                            return check;
                        }
                    }
                    return null;
                }
            }
            Check.StiVariableRecursionCheck = StiVariableRecursionCheck;
        })(Check = Report.Check || (Report.Check = {}));
    })(Report = Stimulsoft.Report || (Stimulsoft.Report = {}));
})(Stimulsoft || (Stimulsoft = {}));
