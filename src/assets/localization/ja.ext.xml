<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<Localization language="Japanese" description="Japanese" cultureName="ja">
  <CheckActions>
    <ApplyEngineV2Long>すべてのレポートツール機能にアクセスするには、ページのEngineVersionプロパティをEngineV2に設定します。</ApplyEngineV2Long>
    <Change>変更</Change>
    <ChangeReportToInterpretationMode>レポートを解析モードに変更する</ChangeReportToInterpretationMode>
    <Code>コード</Code>
    <Convert>t変換</Convert>
    <Delete>削除</Delete>
    <Edit>編集</Edit>
    <Fix>修正</Fix>
    <GotoCodeLong>コードに移動</GotoCodeLong>
    <Hide>これらのメッセージを非表示</Hide>
    <NewName>新しい名前</NewName>
    <Off>オフ</Off>
    <On>オ</On>
    <SetGrowToHeightToFalse>GrowToHeightプロパティをfalseに設定します。</SetGrowToHeightToFalse>
    <SetGrowToHeightToTrue>GrowToHeightプロパティをtrueに設定します。</SetGrowToHeightToTrue>
    <StiAllowHtmlTagsInTextActionLong>HTMLタグを正しく表示するために、コンポーネントのAllowHtmlTagsプロパティの値をtrueに設定します。</StiAllowHtmlTagsInTextActionLong>
    <StiAllowOnDoublePassActionLong>変数の正しい計算のためにNumberOfPassプロパティをDoublePassに設定します。</StiAllowOnDoublePassActionLong>
    <StiApplyGeneralTextFormatLong>'{0}'コンポーネントの '一般的な'形式を設定します。</StiApplyGeneralTextFormatLong>
    <StiApplyGeneralTextFormatShort>一般的な適用</StiApplyGeneralTextFormatShort>
    <StiCanBreakComponentInContainerActionLong>コンポーネントのCanBreakプロパティの値をtrueに設定します。</StiCanBreakComponentInContainerActionLong>
    <StiCanGrowComponentInContainerActionLong>コンテナのCanGrowプロパティの値をtrueに設定します。</StiCanGrowComponentInContainerActionLong>
    <StiCanGrowGrowToHeightComponentInContainerLong>GrowToHeightプロパティは、コンテナ内のすべてのコンポーネントに対してtrueに設定されます。この操作では、100％の修正が保証されるわけではありません！</StiCanGrowGrowToHeightComponentInContainerLong>
    <StiCanGrowWordWrapTextAndWysiwygActionLong>コンポーネントのTextQualityプロパティの値をWYSIWYGに設定します。</StiCanGrowWordWrapTextAndWysiwygActionLong>
    <StiColumnsWidthGreaterContainerWidthActionLong>コンポーネントの幅に応じて列の幅を自動的に変更します。</StiColumnsWidthGreaterContainerWidthActionLong>
    <StiComponentStyleIsNotFoundOnComponentActionLong>ComponentStyleプロパティの値がクリアされます。</StiComponentStyleIsNotFoundOnComponentActionLong>
    <StiDeleteComponentActionLong>コンポーネントをレポートから削除します。</StiDeleteComponentActionLong>
    <StiDeleteConnectionActionLong>レポートを接続から削除します。</StiDeleteConnectionActionLong>
    <StiDeleteDataRelationActionLong>レポートからリレーションを削除します。</StiDeleteDataRelationActionLong>
    <StiDeleteDataSourceActionLong>レポートからデータソースを削除します。</StiDeleteDataSourceActionLong>
    <StiDeleteLostPointsActionLong>ページ上の失われたポイントをすべて削除する</StiDeleteLostPointsActionLong>
    <StiDeletePageActionLong>レポートからページを削除します。</StiDeletePageActionLong>
    <StiFixCrossLinePrimitiveActionLong>失われたポイントの代わりに新しいポイントを作成します。この操作では、100％正しい修正が保証されていません！</StiFixCrossLinePrimitiveActionLong>
    <StiGenerateNewNameComponentActionLong>コンポーネントの新しい名前を生成します。</StiGenerateNewNameComponentActionLong>
    <StiGenerateNewNameDataSourceActionLong>データソースの新しい名前を生成します。</StiGenerateNewNameDataSourceActionLong>
    <StiGenerateNewNamePageActionLong>ページの新しい名前を生成します。</StiGenerateNewNamePageActionLong>
    <StiGenerateNewNameRelationActionLong>リレーションの新しい名前を生成します。</StiGenerateNewNameRelationActionLong>
    <StiGrowToHeightOverlappingLong>コンポーネントのGrowToHeightプロパティの値をfalseに設定します。</StiGrowToHeightOverlappingLong>
    <StiLargeHeightAtPageActionLong>便利なコンポーネントの表示と編集のために、ページのLargeHeightプロパティがtrueに設定されます。</StiLargeHeightAtPageActionLong>
    <StiMinRowsInColumnsActionLong>MinRowsInColumnsプロパティをゼロに設定します。</StiMinRowsInColumnsActionLong>
    <StiMoveComponentToPageAreaActionLong>コンポーネントは、ページ上に完全に配置されるように配置されます。この場合、ページフィールドは考慮されません。</StiMoveComponentToPageAreaActionLong>
    <StiMoveComponentToPageAreaActionShort>ページに移動する</StiMoveComponentToPageAreaActionShort>
    <StiMoveComponentToPrintablePageAreaActionLong>コンポーネントをページ印刷領域に移動します。ページのマージンが考慮されます。</StiMoveComponentToPrintablePageAreaActionLong>
    <StiMoveComponentToPrintablePageAreaActionShort>領域を印刷するには</StiMoveComponentToPrintablePageAreaActionShort>
    <StiNegativeSizesOfComponentsActionLong>レポートをレンダリングするときに問題が発生しないように、コンポーネントのサイズを修正します。この操作では、100％の修正が保証されるわけではありません！</StiNegativeSizesOfComponentsActionLong>
    <StiOrientationPageToLandscapeActionLong>ページをランドスケープに切り替えます。ページサイズは変更されません。</StiOrientationPageToLandscapeActionLong>
    <StiOrientationPageToLandscapeActionShort>ランドスケープレイアウト</StiOrientationPageToLandscapeActionShort>
    <StiOrientationPageToPortraitActionLong>ページレイアウトをポートレートに変更します。ページサイズは変更されません。</StiOrientationPageToPortraitActionLong>
    <StiOrientationPageToPortraitActionShort>ポートレートのレイアウト</StiOrientationPageToPortraitActionShort>
    <StiPrintHeadersFootersFromPreviousPageLong>ページのPrintHeadersFootersFromPreviousPageプロパティをfalseに設定します。</StiPrintHeadersFootersFromPreviousPageLong>
    <StiPrintOnPreviousPageLong>ページのPrintOnPreviousPageプロパティをfalseに設定します。</StiPrintOnPreviousPageLong>
    <StiPropertiesOnlyEngineV1ActionLong>EngineV2でサポートされていない '{0}'コンポーネントのプロパティを無効にします。</StiPropertiesOnlyEngineV1ActionLong>
    <StiPropertiesOnlyEngineV2ActionLong>EngineV1でサポートされていないコンポーネントのプロパティを無効にします。</StiPropertiesOnlyEngineV2ActionLong>
    <StiResetPageNumberActionLong>ページのResetPageNumberプロパティをfalseに設定します。</StiResetPageNumberActionLong>
    <StiSwitchWidthAndHeightOfPageActionLong>ページの高さと幅を変更します。</StiSwitchWidthAndHeightOfPageActionLong>
    <StiVerySmallSizesOfComponentsLong>コンポーネントのサイズは、レポートのGridSize値に増加します。この操作では、100％正しい修正が保証されるわけではありません！</StiVerySmallSizesOfComponentsLong>
    <StiVerySmallSizesOfComponentsShort>サイズを大きくする</StiVerySmallSizesOfComponentsShort>
    <StiWordWrapCanGrowTextDoesNotFitActionLong>コンポーネントのCanGrowプロパティの値をtrueに設定します。</StiWordWrapCanGrowTextDoesNotFitActionLong>
    <Zero>ゼロ</Zero>
  </CheckActions>
  <CheckComponent>
    <StiAllowHtmlTagsInTextCheckLong>テキストにHTMLタグが使用されています。ただし、AllowHtmlTagsプロパティはfalseに設定されています。 {0}コンポーネントの内容が正しく印刷されません。</StiAllowHtmlTagsInTextCheckLong>
    <StiAllowHtmlTagsInTextCheckShort>AllowHtmlTagsプロパティが設定されていません。</StiAllowHtmlTagsInTextCheckShort>
    <StiCanBreakComponentInContainerCheckLong>'{0}'コンテナのCanBreakプロパティがfalseに設定されています。ネストされたコンポーネントの中には、CanBreakプロパティがtrueに設定されているものがあります。</StiCanBreakComponentInContainerCheckLong>
    <StiCanBreakComponentInContainerCheckShort>コンテナのCanBreakプロパティがfalseに設定されている</StiCanBreakComponentInContainerCheckShort>
    <StiCanGrowComponentInContainerCheckLong>'{0}'コンポーネントのCanGrowプロパティがfalseに設定されています。ネストされたコンポーネントの中には、CanGrowプロパティがtrueに設定されているものがあります。これにより、レポートのレンダリングが正しく行われないことがあります。</StiCanGrowComponentInContainerCheckLong>
    <StiCanGrowComponentInContainerCheckShort>コンテナのCanGrowプロパティがfalseに設定されている</StiCanGrowComponentInContainerCheckShort>
    <StiCanGrowGrowToHeightComponentInContainerLong>'{0}'コンテナの一部のコンポーネントのCanGrowプロパティがtrueに設定されています。しかし、GrowToHeightプロパティは、すべてのコンポーネントに対してtrueに設定されていません。レポートをレンダリングするとき、これらのコンポーネントはコンテナの下余白と揃えられます。</StiCanGrowGrowToHeightComponentInContainerLong>
    <StiCanGrowGrowToHeightComponentInContainerShort>コンポーネントの表示が正しくない可能性があります。</StiCanGrowGrowToHeightComponentInContainerShort>
    <StiCanGrowWordWrapTextAndWysiwygCheckLong>TextQualityプロパティがStandardまたはTypographicに設定されている場合。テキストプレビュー、異なるズームモード、およびGDI +ライブラリの丸め誤差のため印刷が異なる場合があります。</StiCanGrowWordWrapTextAndWysiwygCheckLong>
    <StiCanGrowWordWrapTextAndWysiwygCheckShort>不正なテキストラップがある可能性があります</StiCanGrowWordWrapTextAndWysiwygCheckShort>
    <StiChartSeriesValueCheckLong>Valueプロパティは、チャート "{0}"からシリーズに指定されていますが、チャートにはデータソースプロパティが設定されていません。</StiChartSeriesValueCheckLong>
    <StiColumnsWidthGreaterContainerWidthCheckLong>合計列幅は、'{0}'コンポーネントの幅より大きくなります。これにより、レポートのレンダリングが正しく行われないことがあります。また、レポートを他の形式にエクスポートする際にいくつか問題が発生する可能性があります。</StiColumnsWidthGreaterContainerWidthCheckLong>
    <StiColumnsWidthGreaterContainerWidthCheckShort>合計列幅は、コンポーネントの幅より大きくなります。</StiColumnsWidthGreaterContainerWidthCheckShort>
    <StiColumnsWidthGreaterPageWidthCheckLong>'{0}'ページの列の合計幅がページ幅より大きくなっています。これにより、レポートのレンダリングが正しく行われないことがあります。また、レポートを他の形式にエクスポートする際にいくつかの問題が発生する可能性があります。</StiColumnsWidthGreaterPageWidthCheckLong>
    <StiColumnsWidthGreaterPageWidthCheckShort>合計列幅がページ幅より大きい</StiColumnsWidthGreaterPageWidthCheckShort>
    <StiComponentBoundsAreOutOfBandLong>'{0}'コンポーネントは完全にまたは部分的にバンドの外側にあります。このコンポーネントは正しく印刷されません。しかし、場合によっては、コンポーネントのそのような位置は許容可能です。また、レポートを他の形式にエクスポートする際にいくつかの問題が発生する可能性があります。</StiComponentBoundsAreOutOfBandLong>
    <StiComponentBoundsAreOutOfBandShort>コンポーネントが部分的にバンド外にあります。</StiComponentBoundsAreOutOfBandShort>
    <StiComponentExpressionCheckLong>'{1}'の{0}プロパティの式は評価できません！</StiComponentExpressionCheckLong>
    <StiComponentExpressionCheckShort>式を評価できません！</StiComponentExpressionCheckShort>
    <StiComponentResourceCheckLong>コンポーネント '{1}'に指定されたリソース '{0}'がレポート辞書に見つかりませんでした。</StiComponentResourceCheckLong>
    <StiComponentResourceCheckShort>指定されたリソースが見つかりませんでした！</StiComponentResourceCheckShort>
    <StiComponentStyleIsNotFoundCheckAtPageLong>'{0}'ページに指定されたスタイルが存在しません。</StiComponentStyleIsNotFoundCheckAtPageLong>
    <StiComponentStyleIsNotFoundCheckLong>'{0}'コンポーネントに指定されたスタイルが存在しません。</StiComponentStyleIsNotFoundCheckLong>
    <StiComponentStyleIsNotFoundCheckShort>指定されたスタイルは存在しません。</StiComponentStyleIsNotFoundCheckShort>
    <StiContainerInEngineV2CheckLong>'{0}'コンテナは、EngineV2が使用されているレポートにあります。 EngineV2では、Containerコンポーネントの代わりにPanelコンポーネントが使用されます。 Panelコンポーネントは、Containerと比較してより多くの機能を備えています。</StiContainerInEngineV2CheckLong>
    <StiContainerInEngineV2CheckShort>EngineV2のコンテナ</StiContainerInEngineV2CheckShort>
    <StiContourTextObsoleteCheckLong>'{0}'コンポーネントにはStiContourText型があります。 StiContourTextコンポーネントは廃止され、サポートされなくなりましたので、使用することはお勧めしません。</StiContourTextObsoleteCheckLong>
    <StiContourTextObsoleteCheckShort>コンポーネントは廃止されました</StiContourTextObsoleteCheckShort>
    <StiCorruptedCrossLinePrimitiveCheckLong>'{0}'プリミティブの開始点と終了点はありません。このプリミティブは表示されないか、正しく表示されません。</StiCorruptedCrossLinePrimitiveCheckLong>
    <StiCorruptedCrossLinePrimitiveCheckShort>破損したプリミティブ</StiCorruptedCrossLinePrimitiveCheckShort>
    <StiCountDataDataSourceAtDataBandLong>CountData、DataSourceおよびBusinessObjectのプロパティは、 '{0}'コンポーネントに設定されていません。場合によってはそれが許容されます。</StiCountDataDataSourceAtDataBandLong>
    <StiCountDataDataSourceAtDataBandShort>プロパティの値を設定するのを忘れた可能性があります</StiCountDataDataSourceAtDataBandShort>
    <StiCrossGroupHeaderNotEqualToCrossGroupFooterOnPageLong>CrossGroupFooterの数が '{0}'ページのCrossGroupHeaderの数に対応していません。奇数成分はレンダリングされません。</StiCrossGroupHeaderNotEqualToCrossGroupFooterOnPageLong>
    <StiCrossGroupHeaderNotEqualToGroupCrossFooterOnContainerLong>CrossGroupFooterの数が '{0}'コンテナのCrossGroupHeaderの数に対応していません。奇数成分はレンダリングされません。</StiCrossGroupHeaderNotEqualToGroupCrossFooterOnContainerLong>
    <StiDataSourcesForImageCheckLong>'{0}'コンポーネントにいくつかのデータソースが設定されています。</StiDataSourcesForImageCheckLong>
    <StiDataSourcesForImageCheckShort>いくつかのデータソースが設定されています。</StiDataSourcesForImageCheckShort>
    <StiEventsAtInterpretationCheckLong>'{1}'コンポーネントの '{0}'イベントのスクリプトは、解析モードでは機能しません。</StiEventsAtInterpretationCheckLong>
    <StiEventsAtInterpretationCheckShort>解析イベント</StiEventsAtInterpretationCheckShort>
    <StiExpressionElementCheckLong>'{1}'コンポーネントの式 '{0}'は正しく機能しません。 次のメッセージがパーサーから受信されました： '{2}'</StiExpressionElementCheckLong>
    <StiExpressionElementCheckShort>式が正しく機能しません！</StiExpressionElementCheckShort>
    <StiFilterCircularDependencyElementCheckLong>フィルターの循環依存関係が、要素 '{0}'で見つかりました。 {0}要素の「親キー」プロパティを確認してください。</StiFilterCircularDependencyElementCheckLong>
    <StiFilterCircularDependencyElementCheckShort>円形フィルターの依存関係</StiFilterCircularDependencyElementCheckShort>
    <StiFilterValueCheckLong>'{0}'のフィルター値が指定されていません。 このフィルターは処理できません。</StiFilterValueCheckLong>
    <StiFilterValueCheckShort>フィルター値が指定されていません</StiFilterValueCheckShort>
    <StiFontMissingCheckLong>コンポーネント '{1}'に指定されたフォント '{0}'が見つかりません。</StiFontMissingCheckLong>
    <StiFontMissingCheckShort>フォントが見つかりません。</StiFontMissingCheckShort>
    <StiFunctionsOnlyForEngineV2CheckLong>EngineV1で使用できないシステム変数がレポートで使用されます。 EngineV2を使用しますか？</StiFunctionsOnlyForEngineV2CheckLong>
    <StiFunctionsOnlyForEngineV2CheckShort>EngineV1で使用できない変数が使用されます。</StiFunctionsOnlyForEngineV2CheckShort>
    <StiGroupHeaderNotEqualToGroupFooterOnContainerLong>GroupFooterの数が '{0}'コンテナのGroupHeaderの数に対応していません。奇数のコンポーネントはレンダリングされません。</StiGroupHeaderNotEqualToGroupFooterOnContainerLong>
    <StiGroupHeaderNotEqualToGroupFooterOnPageLong>GroupFooterの数が '{0}'ページのGroupHeaderの数に対応していません。奇数成分はレンダリングされません。</StiGroupHeaderNotEqualToGroupFooterOnPageLong>
    <StiGroupHeaderNotEqualToGroupFooterShort>一部のコンポーネントはレンダリングされません。</StiGroupHeaderNotEqualToGroupFooterShort>
    <StiGrowToHeightOverlappingLong>'{0}'コンポーネントのGrowToHeightプロパティがtrueに設定されています。レポートをレンダリングするとき、このコンポーネントは1つまたは複数の基本コンポーネントと重なり合うことがあります。</StiGrowToHeightOverlappingLong>
    <StiGrowToHeightOverlappingShort>コンポーネントをオーバーラップさせることができます。</StiGrowToHeightOverlappingShort>
    <StiIsFirstPageIsLastPageDoublePassCheckLong>IsFirstPage、IsFirstPageThrough、IsLastPage、IsLastPageThroughシステム変数は、1回通過すると不正な結果を返します。 NumberOfPassプロパティをDoublePassに設定する必要があります。</StiIsFirstPageIsLastPageDoublePassCheckLong>
    <StiIsFirstPageIsLastPageDoublePassCheckShort>ダブルパスが必要なシステム変数は、レポート</StiIsFirstPageIsLastPageDoublePassCheckShort>
    <StiIsFirstPassIsSecondPassCheckLong>IsFirstPassの正しい作業のために、IsSecondPass関数はレポートのレンダリングのダブルパスが必要です。レポートのNumberOfPassプロパティをDoublePassに設定するのを忘れていた可能性があります。</StiIsFirstPassIsSecondPassCheckLong>
    <StiIsFirstPassIsSecondPassCheckShort>IsFirstPass、IsSecondPass関数はレポートをレンダリングするときにダブルパスが必要です。</StiIsFirstPassIsSecondPassCheckShort>
    <StiLargeHeightAtPageCheckLong>'{0}'ページの空き容量が不足しています。これにより、コンポーネントの更なる編集やプレビューが妨げられることがあります。 LargeHeightプロパティをtrueに設定することをお勧めします。</StiLargeHeightAtPageCheckLong>
    <StiLargeHeightAtPageCheckShort>ページの空き容量不足</StiLargeHeightAtPageCheckShort>
    <StiLocationOutsidePageCheckLong>'{0}'コンポーネントは部分的にページ外にあります。このコンポーネントは正しく印刷されません。また、レポートを他の形式にエクスポートする際にいくつかの問題が発生する可能性があります。</StiLocationOutsidePageCheckLong>
    <StiLocationOutsidePageCheckShort>コンポーネントが部分的にページ外にあります。</StiLocationOutsidePageCheckShort>
    <StiLocationOutsidePrintableAreaCheckLong>'{0}'コンポーネントは、部分的または完全に印刷領域の外側にあります。このコンポーネントは正しく印刷されません。しかし、場合によっては、コンポーネントのそのような位置は許容可能です。また、レポートを他の形式にエクスポートする際にいくつかの問題が発生する可能性があります。</StiLocationOutsidePrintableAreaCheckLong>
    <StiLocationOutsidePrintableAreaCheckShort>コンポーネントが部分的に印刷領域外にある</StiLocationOutsidePrintableAreaCheckShort>
    <StiMinRowsInColumnsCheckLong>バンドのMinRowsInColumnsプロパティは0に設定されていませんが、Columnsプロパティの値は0に設定されています。これは無意味です。</StiMinRowsInColumnsCheckLong>
    <StiMinRowsInColumnsCheckShort>MinRowsInColumnsプロパティが正しく設定されていない可能性があります</StiMinRowsInColumnsCheckShort>
    <StiNegativeSizesOfComponentsCheckLong>'{0}'コンポーネントの幅と高さに負の値があります。これにより、レポートのレンダリングが正しく行われなくなる可能性があります。</StiNegativeSizesOfComponentsCheckLong>
    <StiNegativeSizesOfComponentsCheckShort>コンポーネントの幅と高さに負の値があります。</StiNegativeSizesOfComponentsCheckShort>
    <StiNoConditionAtGroupCheckLong>'{0}'コンポーネントに条件が設定されていません。それなしではグループ化は行われません</StiNoConditionAtGroupCheckLong>
    <StiNoConditionAtGroupCheckShort>グループの条件が設定されていません。</StiNoConditionAtGroupCheckShort>
    <StiNoNameComponentCheckLong>コンポーネントに名前がありません。コンポーネントの名前を指定すると、レポートエンジンはレポートをコンパイルできなくなります。</StiNoNameComponentCheckLong>
    <StiNoNameComponentCheckShort>コンポーネントに名前がありません。</StiNoNameComponentCheckShort>
    <StiNoNamePageCheckLong>'{0}'インデックスを持つページの名前が指定されていません。ページの名前が指定されていない場合、レポートエンジンはレポートをコンパイルしません。</StiNoNamePageCheckLong>
    <StiNoNamePageCheckShort>ページに名前がありません。</StiNoNamePageCheckShort>
    <StiPanelInEngineV1CheckLong>'{0}'パネルは、EngineV1が使用されているレポートにあります。このコンポーネントはEngineV1では使用できません。したがって、レポートツールはコンテナコンポーネントとしてレンダリングしようとします。しかしPanelコンポーネントはContainerと比較してより多くの機能を持ち、不正なレポートレンダリングを引き起こす可能性があります。</StiPanelInEngineV1CheckLong>
    <StiPanelInEngineV1CheckShort>EngineV1のパネル</StiPanelInEngineV1CheckShort>
    <StiPrintHeadersAndFootersFromPreviousPageLong>'{0}'ページのPrintHeadersAndFootersFromPreviousPageプロパティがtrueに設定されています。しかし最初の唯一のページでは意味がありません。</StiPrintHeadersAndFootersFromPreviousPageLong>
    <StiPrintHeadersAndFootersFromPreviousPageShort>PrintHeadersAndFootersFromPreviousPageプロパティが正しく設定されていない可能性があります。</StiPrintHeadersAndFootersFromPreviousPageShort>
    <StiPrintOnDoublePassCheckLong>'{0}'コンポーネントのPrintOnプロパティがAllPagesに設定されていません。レポートを正しく表示するには、ダブルパスを有効にする必要があります。</StiPrintOnDoublePassCheckLong>
    <StiPrintOnDoublePassCheckShort>PrintOnプロパティがAllPagesに設定されていない</StiPrintOnDoublePassCheckShort>
    <StiPrintOnPreviousPageCheck2Long>'{0}'ページのPrintOnPreviousPageプロパティがtrueに設定されています。このページにあるすべてのコンポーネント（バンドを除く）は、次の新しいページからのみ印刷されるか、新しいページがない場合は全く印刷されません。</StiPrintOnPreviousPageCheck2Long>
    <StiPrintOnPreviousPageCheck2Short>一部のコンポーネントが印刷されないことがあります。</StiPrintOnPreviousPageCheck2Short>
    <StiPrintOnPreviousPageCheckLong>'{0}'ページのPrintOnPreviousPageプロパティがtrueに設定されています。しかし、最初の唯一のページでは意味がありません。</StiPrintOnPreviousPageCheckLong>
    <StiPrintOnPreviousPageCheckShort>PrintOnPreviousPageプロパティが正しく設定されていない可能性があります。</StiPrintOnPreviousPageCheckShort>
    <StiPropertiesOnlyEngineV1CheckLong>'{0}'コンポーネントのStartNewPageプロパティはEngineV1のみでサポートされています。</StiPropertiesOnlyEngineV1CheckLong>
    <StiPropertiesOnlyEngineV1CheckShort>EngineV2でサポートされていないプロパティがいくつか設定されています。</StiPropertiesOnlyEngineV1CheckShort>
    <StiPropertiesOnlyEngineV2CheckLong>'{0}'コンポーネントには、NewPageBefore、NewPageAfter、NewColumnBefore、NewColumnAfter、SkipFirstのいずれかのプロパティが使用されます。 EngineV2ではサポートされていません。</StiPropertiesOnlyEngineV2CheckLong>
    <StiPropertiesOnlyEngineV2CheckShort>EngineV1でサポートされていないプロパティがいくつか設定されています。</StiPropertiesOnlyEngineV2CheckShort>
    <StiResetPageNumberCheckLong>'{0}'ページのResetPageNumberプロパティがtrueに設定されています。しかし、最初の唯一のページでは意味がありません。</StiResetPageNumberCheckLong>
    <StiResetPageNumberCheckShort>ResetPageNumberプロパティが正しく設定されていない可能性があります</StiResetPageNumberCheckShort>
    <StiShowInsteadNullValuesCheckLong>'{0}'コンポーネントにNullValuesプロパティが設定されています。しかし、DataColumnプロパティは設定されていません。これは無意味です。</StiShowInsteadNullValuesCheckLong>
    <StiShowInsteadNullValuesCheckShort>NullValuesプロパティが設定されている</StiShowInsteadNullValuesCheckShort>
    <StiSubReportPageZeroCheckLong>SubReportコンポーネントのSubReportPageプロパティが設定されていない場合、レポートはレンダリングできません。</StiSubReportPageZeroCheckLong>
    <StiSubReportPageZeroCheckShort>SubReportPageプロパティが設定されていません。</StiSubReportPageZeroCheckShort>
    <StiSystemTextObsoleteCheckLong>'{0}'コンポーネントにはStiSystemText型があります。 StiSystemTextコンポーネントは廃止され、サポートされなくなりましたので、使用することはお勧めしません。</StiSystemTextObsoleteCheckLong>
    <StiSystemTextObsoleteCheckShort>コンポーネントは廃止されました</StiSystemTextObsoleteCheckShort>
    <StiTextColorEqualToBackColorCheckLong>'{0}'コンポーネントのTextBrushプロパティとBrushプロパティの値が正しく設定されていない可能性があります。テキストは表示されません。</StiTextColorEqualToBackColorCheckLong>
    <StiTextColorEqualToBackColorCheckShort>TextBrushプロパティは、Brushプロパティ</StiTextColorEqualToBackColorCheckShort>
    <StiTextTextFormatCheckLong>式の結果が既にテキスト文字列であるため、コンポーネント '{0}'のテキスト形式を使用できません。</StiTextTextFormatCheckLong>
    <StiTextTextFormatCheckShort>テキスト形式を適用できません</StiTextTextFormatCheckShort>
    <StiTotalPageCountDoublePassCheckLong>スクリプトと条件のTotalPageCount、TotalPageCountThroughシステム変数は、1回通過すると不正な結果を返します。 NumberOfPassプロパティをDoublePassに設定する必要があります。</StiTotalPageCountDoublePassCheckLong>
    <StiUndefinedComponentCheckLong>未定義の '{0}'コンポーネントがレポートに見つかりました。レポートツールはこのコンポーネントを識別できません。たぶんこれはカスタムコンポーネントです。レポートは表示されません！</StiUndefinedComponentCheckLong>
    <StiUndefinedComponentCheckShort>未定義のコンポーネント</StiUndefinedComponentCheckShort>
    <StiVerySmallSizesOfComponentsCheckLong>'{0}'コンポーネントの高さの幅の値が、レポートのGridSizeの値よりも小さい。たぶんあなたはそのサイズを間違って設定しています。これにより、他の形式にエクスポートするときに問題が発生する可能性があります。</StiVerySmallSizesOfComponentsCheckLong>
    <StiVerySmallSizesOfComponentsCheckShort>コンポーネントのサイズが非常に小さい</StiVerySmallSizesOfComponentsCheckShort>
    <StiWidthHeightZeroComponentCheckLongHeight>'{0}'コンポーネントの高さはゼロです。これは、このコンポーネントをページに表示する際に問題を引き起こす可能性があります。また、レポートを他の形式にエクスポートするときに問題が発生することもあります。</StiWidthHeightZeroComponentCheckLongHeight>
    <StiWidthHeightZeroComponentCheckLongWidth>'{0}'コンポーネントの幅はゼロです。このコンポーネントをページに表示すると、いくつか問題が発生することがあります。また、レポートを他の形式にエクスポートするときにいくつかの問題が発生する可能性があります。</StiWidthHeightZeroComponentCheckLongWidth>
    <StiWidthHeightZeroComponentCheckLongWidthHeight>'{0}'コンポーネントの高さと幅は0です。このコンポーネントをページに表示する際に問題が発生することがあります。また、レポートを他の形式にエクスポートするときにいくつかの問題が発生することがあります。</StiWidthHeightZeroComponentCheckLongWidthHeight>
    <StiWidthHeightZeroComponentCheckShortHeight>'{0}'コンポーネントの高さは0です。</StiWidthHeightZeroComponentCheckShortHeight>
    <StiWidthHeightZeroComponentCheckShortWidth>'{0}'コンポーネントの幅は0です。</StiWidthHeightZeroComponentCheckShortWidth>
    <StiWidthHeightZeroComponentCheckShortWidthHeight>'{0}'コンポーネントの高さと幅は0です。</StiWidthHeightZeroComponentCheckShortWidthHeight>
    <StiWordWrapCanGrowTextDoesNotFitLong>WordWrapプロパティがtrueに設定され、CanGrowプロパティがfalseに設定されている場合、'{0}'コンポーネントのテキストを完全に表示することはできません。 CanGrowプロパティをtrueに設定することをお勧めします。</StiWordWrapCanGrowTextDoesNotFitLong>
    <StiWordWrapCanGrowTextDoesNotFitShort>テキストが収まりません</StiWordWrapCanGrowTextDoesNotFitShort>
  </CheckComponent>
  <CheckConnection>
    <StiUndefinedConnectionCheckLong>未定義の '{0}'接続が見つかりました。レポートツールは、データベースとのこの種の接続を正しく処理できません。顧客の種類の接続がレポートで使用されている可能性があります。このレポートはレンダリングできません！</StiUndefinedConnectionCheckLong>
    <StiUndefinedConnectionCheckShort>未定義の接続タイプ</StiUndefinedConnectionCheckShort>
    <StiUnsupportedConnectionCheckLong>レポートに、選択したプラットフォームでサポートされていない接続 '{0}'が含まれています。この接続のオプションを変更してください。</StiUnsupportedConnectionCheckLong>
    <StiUnsupportedConnectionCheckShort>サポートされていない接続タイプ</StiUnsupportedConnectionCheckShort>
  </CheckConnection>
  <CheckDataRelation>
    <StiDifferentAmountOfKeysInDataRelationCheckLong>'{0}'リレーションの親キーの数は、子キーの数に対応しません。レポートをレンダリングするときに、次のリレーションは作成できません！</StiDifferentAmountOfKeysInDataRelationCheckLong>
    <StiDifferentAmountOfKeysInDataRelationCheckShort>キーの数が正しくありません</StiDifferentAmountOfKeysInDataRelationCheckShort>
    <StiKeysInAbsentDataRelationCheckLong>'{0}'リレーションのキーは指定されていません。このようなリレーションは、レポートのレンダリング時には作成されません！</StiKeysInAbsentDataRelationCheckLong>
    <StiKeysInAbsentDataRelationCheckShort>キーなし</StiKeysInAbsentDataRelationCheckShort>
    <StiKeysNotFoundRelationCheckLong>次の列： '{0}'のキーとして指定された '{1}'は、リレーションの親データソースまたは子データソースで見つかりませんでした。このリレーションは、レポートをレンダリングするときには作成できません！</StiKeysNotFoundRelationCheckLong>
    <StiKeysNotFoundRelationCheckShort>リレーションが見つからない</StiKeysNotFoundRelationCheckShort>
    <StiKeysTypesMismatchDataRelationCheckLong>'{0}'リレーションのキーとして指定された '{1}'列の型は一致しません。このようなリレーションは、レポートのレンダリング時には作成できません！</StiKeysTypesMismatchDataRelationCheckLong>
    <StiKeysTypesMismatchDataRelationCheckShort>リレーションのキータイプが一致しません。</StiKeysTypesMismatchDataRelationCheckShort>
    <StiNoNameDataRelationCheckLong>（データソース '{0}'の間に作成された）リレーションの名前が指定されていません。この名前は、このリレーションのレポートコードでクラスを作成するために使用されます。名前がなければ、レポートツールはレポートのレポートコードを作成することができず、レポートはコンパイルされません！</StiNoNameDataRelationCheckLong>
    <StiNoNameDataRelationCheckShort>関連</StiNoNameDataRelationCheckShort>
    <StiNoNameInSourceDataRelationCheckLong>'{0}'リレーションのNameInSourceフィールドが入力されていません。このフィールドがなければ、レポートツールはデータベース内のリレーションを見つけることができず、レポートはレンダリングされません！</StiNoNameInSourceDataRelationCheckLong>
    <StiNoNameInSourceDataRelationCheckShort>NameInSourceフィールドがリレーションに対して満たされていない</StiNoNameInSourceDataRelationCheckShort>
    <StiSourcesInAbsentDataRelationCheckLong>親データソースおよび/または子データソースが '{0}'リレーションに指定されていません。両方のデータソースを指定する必要があります！</StiSourcesInAbsentDataRelationCheckLong>
    <StiSourcesInAbsentDataRelationCheckShort>データソースがありません。</StiSourcesInAbsentDataRelationCheckShort>
  </CheckDataRelation>
  <CheckDataSource>
    <StiCalculatedColumnRecursionCheckLong>'{1}'データソースの '{0}'計算カラムには、再帰を伴う式があります。 Calculated Columnを独自の式で参照することは不可能です。</StiCalculatedColumnRecursionCheckLong>
    <StiCalculatedColumnRecursionCheckShort>再帰式</StiCalculatedColumnRecursionCheckShort>
    <StiNoNameDataSourceCheckLong>データソース名が指定されていません。この名前は、データソースのレポートコードにクラスを作成するために使用されます。レポートツールは、名前を明示せずにレポートコードを作成することはできず、レポートはコンパイルされません！</StiNoNameDataSourceCheckLong>
    <StiNoNameDataSourceCheckShort>データソースの名前はありません</StiNoNameDataSourceCheckShort>
    <StiNoNameInSourceDataSourceCheckLong>'{0}'データソースのNameInSourceフィールドが入力されていません。このフィールドを埋めることなく、レポートツールはデータベース内のデータソースのデータを見つけることができず、レポートはレンダリングされません！</StiNoNameInSourceDataSourceCheckLong>
    <StiNoNameInSourceDataSourceCheckShort>データソースフィールドのNameInSourceが満たされていない</StiNoNameInSourceDataSourceCheckShort>
    <StiUndefinedDataSourceCheckLong>未定義の '{0}'データソースがレポートに見つかりました。レポートツールは、このような種類のデータソースを正しく処理できません。たぶん、カスタムデータソースがレポートで使用されることがあります。このレポートはレンダリングできません！</StiUndefinedDataSourceCheckLong>
    <StiUndefinedDataSourceCheckShort>未定義のデータソース</StiUndefinedDataSourceCheckShort>
  </CheckDataSource>
  <CheckGlobal>
    <Error>エラー！ </Error>
    <Information>情報</Information>
    <RenderingMessage>レンダリングメッセージを報告する。 </RenderingMessage>
    <Warning>警告！ </Warning>
  </CheckGlobal>
  <CheckLicense>
    <StiLicenseTrialCheckLong>Stimulsoft Reports と Dashboardsの試用版を使用しています。 本番環境でソフトウェアを使用するには、ライセンスを購入する必要があります。</StiLicenseTrialCheckLong>
    <StiValidSubscriptioRequiredCheckLong>有効なサブスクリプションが必要です。 Stimulsoft Designerの完全に機能するバージョンを使用するには、サブスクリプションを更新してください。</StiValidSubscriptioRequiredCheckLong>
  </CheckLicense>
  <CheckPage>
    <StiLostPointsOnPageCheckLong>'{0}'ページで失われたポイントが見つかりました：'{1}'点は不可視であり、以下のプリミティブで使用されます：垂直線、矩形、および丸みのある矩形。ポイントがどのコンポーネントにも一致しない場合は、そのポイントを削除することができます。</StiLostPointsOnPageCheckLong>
    <StiLostPointsOnPageCheckShort>失われたポイント</StiLostPointsOnPageCheckShort>
    <StiOrientationPageCheckLongLandscape>'{0}'ページの向きが、指定されたページサイズに一致しません。通常、横向きの場合、ページの幅はページの高さよりも大きくなければなりません。このページの幅は高さよりも小さい。このページを印刷するときに問題が発生する可能性があります。</StiOrientationPageCheckLongLandscape>
    <StiOrientationPageCheckLongPortrait>'{0}'ページの向きが、指定されたページサイズに一致しません。通常、縦向きの場合、ページの幅はページの高さより小さくする必要があります。このページの幅は高さよりも大きい。このページを印刷するときに問題が発生することがあります。</StiOrientationPageCheckLongPortrait>
    <StiOrientationPageCheckShort>ページの向きが{0}で間違っています。</StiOrientationPageCheckShort>
  </CheckPage>
  <CheckReport>
    <StiCloudCompilationModeCheckLong>Stimulsoft Cloudは、このレポートをコンパイルモードでプレビューできません。レポートを通訳モードに変更します。</StiCloudCompilationModeCheckLong>
    <StiCloudCompilationModeCheckShort>このレポートはコンパイルモードです</StiCloudCompilationModeCheckShort>
    <StiCompilationErrorAssemblyCheckLong>ファイルまたはアセンブリ '{0}'またはその依存関係の1つを読み込めませんでした。</StiCompilationErrorAssemblyCheckLong>
    <StiCompilationErrorCheck2Long>コンパイルエラーは、 '{2}'コンポーネントの '{1}'プロパティにあります：</StiCompilationErrorCheck2Long>
    <StiCompilationErrorCheck3Long>コンパイルエラーは、 '{2}'コンポーネントの '{1}'イベントで見つかりました：</StiCompilationErrorCheck3Long>
    <StiCompilationErrorCheckLong>コンパイルエラーが '{0}'レポートに見つかりました：</StiCompilationErrorCheckLong>
    <StiCompilationErrorCheckShort>コンパイルエラー</StiCompilationErrorCheckShort>
    <StiDuplicatedName2CheckLong>'{0}'という名前のデータソースがいくつかあります。</StiDuplicatedName2CheckLong>
    <StiDuplicatedNameCheckLong>'{0}'という名前のコンポーネントがいくつかあります。</StiDuplicatedNameCheckLong>
    <StiDuplicatedNameCheckShort>重複する名前のコンポーネントが見つかりました</StiDuplicatedNameCheckShort>
    <StiDuplicatedNameInSourceInDataRelationReportCheckLong>レポートに'{0}'リレーションがあります。彼らはNameInSourceの等しい（ '{1}'）値を持っています。 NameInSource内のすべての1つの関係から1つのデータベースの範囲で一意である必要があります。これらのリレーションフィールドを修正する必要があります。</StiDuplicatedNameInSourceInDataRelationReportCheckLong>
    <StiDuplicatedNameInSourceInDataRelationReportCheckShort>リレーションで重複します。</StiDuplicatedNameInSourceInDataRelationReportCheckShort>
    <StiDuplicatedReportName2CheckLong>レポート名 '{0}'はデータソース名と同じです。</StiDuplicatedReportName2CheckLong>
    <StiDuplicatedReportNameCheckLong>レポート名 '{0}'はコンポーネント名と同じです。</StiDuplicatedReportNameCheckLong>
    <StiNetCoreCompilationModeCheckLong>Stimulsoft .NET Coreエンジンは、このレポートをコンパイルモードでレンダリングできません。 レポートを解釈モードに変更します。</StiNetCoreCompilationModeCheckLong>
    <StiNetCoreCompilationModeCheckShort>このレポートはコンパイルモードです</StiNetCoreCompilationModeCheckShort>
  </CheckReport>
  <CheckVariable>
    <StiVariableInitializationCheckLong>'{0}'変数のデフォルト値が間違っています。 指定された値で変数を初期化することはできません。</StiVariableInitializationCheckLong>
    <StiVariableInitializationCheckShort>初期化値</StiVariableInitializationCheckShort>
    <StiVariableRecursionCheckLong>'{0}'変数には再帰を伴う式があります。 Variableを独自の式で参照することは不可能です。</StiVariableRecursionCheckLong>
    <StiVariableRecursionCheckShort>再帰式</StiVariableRecursionCheckShort>
    <StiVariableTypeCheckLong>従属変数が正しく機能するためには、それらの型と列の型が一致する必要があります。</StiVariableTypeCheckLong>
    <StiVariableTypeCheckShort>変数のタイプが一致しません。</StiVariableTypeCheckShort>
  </CheckVariable>
  <Font>
    <Bold>このFontが太字かどうかを示す値</Bold>
    <Italic>このフォントがイタリック体であるかどうかを示す値</Italic>
    <Name>このFontのフェースネーム</Name>
    <Size>このフォントのサイズ</Size>
    <Strikeout>このFontがフォントを通過する水平線を指定するかどうかを示す値</Strikeout>
    <Underline>このフォントに下線が引かれているかどうかを示す値</Underline>
  </Font>
  <Publish>
    <ActionDesign>デザイン</ActionDesign>
    <ActionExport>エクスポート</ActionExport>
    <ActionShow>表示</ActionShow>
    <AddNewConnection>新しい接続を追加する</AddNewConnection>
    <Cancel>キャンセル</Cancel>
    <Close>閉じる</Close>
    <ConnectionsFromReport>レポートからの接続を使用する</ConnectionsFromReport>
    <ConnectionsFromReportToolTip>レポートテンプレートに保存された接続は、変更なしで使用されます。</ConnectionsFromReportToolTip>
    <ConnectionsRegData>コードからデータを登録する</ConnectionsRegData>
    <ConnectionsRegDataToolTip>XMLまたはJSONデータの形式で、または可能であればビジネスオブジェクトの形式で、コードからデータを接続するように求められます。</ConnectionsRegDataToolTip>
    <ConnectionsReplace>接続文字列を置換する</ConnectionsReplace>
    <ConnectionsReplaceToolTip>ファイルパス、データベース接続文字列などのタイプに応じて、コードから接続を変更するように求められます。</ConnectionsReplaceToolTip>
    <Copy>コピー</Copy>
    <CopyingLibraries>プロジェクトファイルのコピー... </CopyingLibraries>
    <DeployReportPlatform>プラットフォームへのレポートのデプロイ：</DeployReportPlatform>
    <DownloadingLibraries>ライブラリのダウンロード... </DownloadingLibraries>
    <EmbedAllDataToResources>レポートには、選択したプラットフォームでサポートされていないデータ接続が含まれています。 利用可能なデータをリソースとしてレポートに埋め込みますか？ この場合、すべての接続はXMLリソースに置き換えられます。</EmbedAllDataToResources>
    <EmbedAllDataToResourcesToolTip>すべてのデータが読み込まれ、リソースとしてレポートテンプレートに追加されます。 これにより、データベースにクエリを実行しなくてもレポートを表示できます。</EmbedAllDataToResourcesToolTip>
    <ExportFormat>レポートのエクスポート先：</ExportFormat>
    <ExportFormatData>データ</ExportFormatData>
    <ExportFormatDataType>データ型：</ExportFormatDataType>
    <ExportFormatImage>イメージ</ExportFormatImage>
    <ExportFormatImageType>イメージタイプ：</ExportFormatImageType>
    <FullScreenViewer>フルブラウザウィンドウにビューアを表示する</FullScreenViewer>
    <FullScreenViewerToolTip>レポートのあるビューアは、ブラウザウィンドウの使用可能なスペース全体に表示されます。</FullScreenViewerToolTip>
    <GetLibrariesFrom>{0}からStimulsoftライブラリを取得する</GetLibrariesFrom>
    <GetLibrariesFromToolTip>必要なすべてのライブラリとスクリプトは、リポジトリから接続されます。 それ以外の場合は、プロジェクトにコピーされ、直接接続されます。</GetLibrariesFromToolTip>
    <GroupAddons>アドオン</GroupAddons>
    <GroupConnections>データ接続</GroupConnections>
    <GroupParameters>レポートパラメータ</GroupParameters>
    <HideOptions>オプションを隠す</HideOptions>
    <IncludeFonts>フォントを含める</IncludeFonts>
    <IncludeFontsToolTip>レポートコンポーネントのサイズを正しく計算するために必要なフォントがコピーされ、プロジェクトに接続されます。</IncludeFontsToolTip>
    <IncludeLibrariesToStandalone>Stimulsoftライブラリを実行可能ファイルに含める</IncludeLibrariesToStandalone>
    <IncludeLibrariesToStandaloneToolTip>必要なすべてのライブラリとファイルが実行可能ファイルに含まれます。 それ以外の場合は、ZIPアーカイブが作成されます。</IncludeLibrariesToStandaloneToolTip>
    <IncludeLicenseKey>ライセンスキーを含める</IncludeLicenseKey>
    <IncludeLicenseKeyToolTip>現在許可されているユーザーのアカウントのライセンスキーがプロジェクトに統合されます。</IncludeLicenseKeyToolTip>
    <IncludeLocalization>ローカライゼーションを含める</IncludeLocalization>
    <IncludeLocalizationToolTip>選択したローカリゼーションがコピーされ、プロジェクトに接続されます。</IncludeLocalizationToolTip>
    <IncludeReportPackedStringToCode>コードにPacked Stringとしてレポートを含める</IncludeReportPackedStringToCode>
    <IncludeReportPackedStringToCodeToolTip>レポートは、プロジェクトコードにBase64文字列として保存されます。 これにより、追加のファイルを取り除くことができます。</IncludeReportPackedStringToCodeToolTip>
    <IncludeUITheme>UIテーマを含める</IncludeUITheme>
    <IncludeUIThemeToolTip>選択したユーザーインターフェースのテーマは、コンポーネントの設定でセットされます。</IncludeUIThemeToolTip>
    <JavaScriptFramework>フレームワークタイプ：</JavaScriptFramework>
    <LicenseKeyTypeFile>ファイル</LicenseKeyTypeFile>
    <LicenseKeyTypeString>String </LicenseKeyTypeString>
    <LoadReport>からレポートをロードする：</LoadReport>
    <LoadReportAssembly>アセンブリ</LoadReportAssembly>
    <LoadReportByteArray>バイト配列</LoadReportByteArray>
    <LoadReportClass>クラス</LoadReportClass>
    <LoadReportFile>ファイル</LoadReportFile>
    <LoadReportHyperlink>ハイパーリンク</LoadReportHyperlink>
    <LoadReportResource>リソース</LoadReportResource>
    <LoadReportStream>ストリーム</LoadReportStream>
    <LoadReportString>String </LoadReportString>
    <ParametersFromReport>レポートの値を使用する</ParametersFromReport>
    <ParametersFromReportToolTip>レポートテンプレートで設定されたパラメータの値が使用されます。</ParametersFromReportToolTip>
    <ParametersReplace>{0}コードの値を置換する</ParametersReplace>
    <ParametersReplacePhpInfo>パラメータはレポート辞書から削除され、SQLクエリでのみ使用できます。</ParametersReplacePhpInfo>
    <ParametersReplaceToolTip>コードからレポートパラメータの値を変更するように求められます。</ParametersReplaceToolTip>
    <ParametersRequestFromUser>ユーザーからのリクエスト</ParametersRequestFromUser>
    <ParametersRequestFromUserToolTip>レポートパラメータは、ビューアウィンドウでレポートを表示するときにユーザーから要求されます。</ParametersRequestFromUserToolTip>
    <Publish>Publish </Publish>
    <PublishType>レポートのパブリッシュタイプ：</PublishType>
    <PublishTypeProject>プロジェクト</PublishTypeProject>
    <PublishTypeProjectToolTip>完成したプロジェクトと、さらにコンパイルして起動するために必要なすべてのファイルを使用して、ZIPアーカイブが作成されます。</PublishTypeProjectToolTip>
    <PublishTypeStandalone>スタンドアロン</PublishTypeStandalone>
    <PublishTypeStandaloneToolTip>レポートを表示するためのコンパイル済みのアプリケーションが作成されます。</PublishTypeStandaloneToolTip>
    <ReadMore>続きを読む</ReadMore>
    <RegDataOnlyForPreview>レポートプレビュー専用に使用</RegDataOnlyForPreview>
    <RegDataOnlyForPreviewToolTip>データは、プレビューの時点でのみレポートに接続されます。</RegDataOnlyForPreviewToolTip>
    <RegDataSynchronize>レポート辞書の同期化</RegDataSynchronize>
    <RegDataSynchronizeToolTip>データをレポートに接続した後、データディクショナリが同期されます。- データソース・列とそのタイプ・データリンク</RegDataSynchronizeToolTip>
    <ReplaceConnectionString>置換接続文字列</ReplaceConnectionString>
    <ReplacePathToData>データをパスに置き換える</ReplacePathToData>
    <ReportAction>レポートの処理：</ReportAction>
    <ReportErrors>残念ながら、エラーのためこのレポートは公開できません！</ReportErrors>
    <SaveProjectPackage>プロジェクトパッケージを保存する</SaveProjectPackage>
    <SaveStandalone>スタンドアロンを保存する</SaveStandalone>
    <SaveStandalonePackage>スタンドアロンパッケージを保存する</SaveStandalonePackage>
    <SearchLibraries>ライブラリを検索... </SearchLibraries>
    <ShowMore>もっと見る</ShowMore>
    <ShowOptions>オプションを表示する</ShowOptions>
    <ThemeBackground>背景：</ThemeBackground>
    <ThemeStyle>テーマスタイル：</ThemeStyle>
    <TrialVersion>試用版</TrialVersion>
    <Use>Use </Use>
    <UseCompilationCache>コンパイルキャッシュを使用する</UseCompilationCache>
    <UseCompilationCacheToolTip>レンダリングされたレポートは一時ディレクトリに保存されます。これにより、レンダリングを高速化し、メモリ使用量を減らすことができます。</UseCompilationCacheToolTip>
    <UseCompressedScripts>圧縮スクリプトを使用する</UseCompressedScripts>
    <UseCompressedScriptsToolTip>通常のスクリプトの代わりに、パックされたスクリプトが使用されます。 これにより、サイズを小さくすることができますが、実行時に解凍するのに時間がかかります。</UseCompressedScriptsToolTip>
    <UseLatestVersion>ライブラリの最新バージョンを使用</UseLatestVersion>
    <UseLatestVersionToolTip>ライブラリとスクリプトを接続する場合、現在インストールされているバージョンの代わりに、製品の最新の公式バージョンが使用されます。</UseLatestVersionToolTip>
    <UseWpfDesignerV2>Designer V2を使用する</UseWpfDesignerV2>
    <UseWpfDesignerV2ToolTip>更新されたWPFレポートデザイナーが使用されます。 新しいUI、速度の向上、その他の最適化が含まれます。</UseWpfDesignerV2ToolTip>
  </Publish>
  <ReportComparer>
    <Change>変更</Change>
    <Copy>コピー</Copy>
    <CopyAll>すべてコピー</CopyAll>
    <Delete>削除</Delete>
    <SaveChangesInReports>レポートに変更を保存しますか？</SaveChangesInReports>
    <StiBusinessObjectDifferentColumnCompareComment>同じBusiness Objectsのどの列が1つのレポート内にあるかを示す値</StiBusinessObjectDifferentColumnCompareComment>
    <StiBusinessObjectDifferentColumnCompareLong>レポート '{2}'のビジネスオブジェクト '{1}'の列 '{0}'が、レポート '{4}'のビジネスオブジェクト '{3}'に存在しません。</StiBusinessObjectDifferentColumnCompareLong>
    <StiBusinessObjectDifferentColumnCompareShort>ビジネス・オブジェクトの列はありません。</StiBusinessObjectDifferentColumnCompareShort>
    <StiBusinessObjectPropertiesCompareComment>同一Business Objectsの異なるプロパティ値を表示す値</StiBusinessObjectPropertiesCompareComment>
    <StiBusinessObjectPropertiesCompareLong>レポート '{0}'で、ビジネスオブジェクト '{1}'の '{2}'プロパティがビジネスオブジェクト '{4}'の '{5}'プロパティの値と異なりますレポート '{3}'。</StiBusinessObjectPropertiesCompareLong>
    <StiBusinessObjectPropertiesCompareShort>Business Objectsの異なるプロパティ値。</StiBusinessObjectPropertiesCompareShort>
    <StiChangeInReportPropertyActionDescription>両方のプロパティ値を '{0}'と等しくする</StiChangeInReportPropertyActionDescription>
    <StiComponentPropertiesCompareComment>同一のコンポーネントの異なるプロパティ値を表示す値</StiComponentPropertiesCompareComment>
    <StiComponentPropertiesCompareLong>レポート '{0}'のコンポーネント '{1}'の '{2}'プロパティが、レポート '{0}'のコンポーネント '{4}'の '{3}'。</StiComponentPropertiesCompareLong>
    <StiComponentPropertiesCompareShort>コンポーネントプロパティの異なる値。</StiComponentPropertiesCompareShort>
    <StiCopyAllActionDescription>レポート '{0}'にすべてをコピー</StiCopyAllActionDescription>
    <StiCopyBusinessDataColumnActionsDescription>列をビジネスオブジェクトにコピーする</StiCopyBusinessDataColumnActionsDescription>
    <StiCopyBusinessObjectActionDescription>ビジネスオブジェクトのコピー</StiCopyBusinessObjectActionDescription>
    <StiCopyComponentActionDescription>コンポーネントをコピーします。</StiCopyComponentActionDescription>
    <StiCopyDataColumnActionDescription>列をデータソースにコピーする</StiCopyDataColumnActionDescription>
    <StiCopyDataRelationActionDescription>データリレーションのコピー</StiCopyDataRelationActionDescription>
    <StiCopyDataSourceActionDescription>データソースのコピー</StiCopyDataSourceActionDescription>
    <StiCopyStyleActionDescription>スタイルのコピー</StiCopyStyleActionDescription>
    <StiCopyVariableActionDescription>変数のコピー</StiCopyVariableActionDescription>
    <StiDataRelationPropertiesCompareComment>同一リレーションの異なるプロパティ値を示す値</StiDataRelationPropertiesCompareComment>
    <StiDataRelationPropertiesCompareLong>レポート '{0}'のリレーション{1}の '{2}'プロパティが、レポート '{0}'のリレーション '{4}'の '{3}'。</StiDataRelationPropertiesCompareLong>
    <StiDataRelationPropertiesCompareShort>リレーションの異なるプロパティ値。</StiDataRelationPropertiesCompareShort>
    <StiDataSourceDifferentColumnCompareComment>同じデータソースのどの列が1つのレポート内にあるかを示す値</StiDataSourceDifferentColumnCompareComment>
    <StiDataSourceDifferentColumnCompareLong>レポート '{2}'のデータソース '{1}'からの '{0}'列が、レポート '{4}'のデータソース '{3}'に存在しません。</StiDataSourceDifferentColumnCompareLong>
    <StiDataSourceDifferentColumnCompareShort>データソースの列がありません。</StiDataSourceDifferentColumnCompareShort>
    <StiDataSourcePropertiesCompareComment>同一のデータソースの異なるプロパティ値を表示す値</StiDataSourcePropertiesCompareComment>
    <StiDataSourcePropertiesCompareLong>レポート '{0}'のデータソース '{1}'の '{2}'プロパティが、データソース '{4}'の '{5}'プロパティの値と異なりますレポート '{3}'。</StiDataSourcePropertiesCompareLong>
    <StiDataSourcePropertiesCompareShort>データソースプロパティの異なる値。</StiDataSourcePropertiesCompareShort>
    <StiDeleteBusinessDataColumnActionsDescription>ビジネスオブジェクトから列を削除する</StiDeleteBusinessDataColumnActionsDescription>
    <StiDeleteBusinessObjectActionDescription>ビジネスオブジェクトの削除</StiDeleteBusinessObjectActionDescription>
    <StiDeleteComponentActionDescription>レポートからコンポーネントを削除する</StiDeleteComponentActionDescription>
    <StiDeleteDataColumnActionDescription>データソースから列を削除する</StiDeleteDataColumnActionDescription>
    <StiDeleteDataRelationActionDescription>データソース間のリレーションを削除する</StiDeleteDataRelationActionDescription>
    <StiDeleteDataSourceActionDescription>データソースの削除</StiDeleteDataSourceActionDescription>
    <StiDeleteStyleActionDescription>スタイルの削除</StiDeleteStyleActionDescription>
    <StiDeleteVariableActionDescription>変数をレポートから削除する</StiDeleteVariableActionDescription>
    <StiReportDifferentBusinessObjectCompareComment>1つのレポートのみに存在するビジネスオブジェクトを表示す値</StiReportDifferentBusinessObjectCompareComment>
    <StiReportDifferentBusinessObjectCompareLong>ビジネスオブジェクト '{0}'はレポート '{1}'に存在しません。</StiReportDifferentBusinessObjectCompareLong>
    <StiReportDifferentBusinessObjectCompareShort>ビジネスオブジェクトなし</StiReportDifferentBusinessObjectCompareShort>
    <StiReportDifferentComponentsCompareComment>1つのレポートにのみ存在するコンポーネントを表示す値</StiReportDifferentComponentsCompareComment>
    <StiReportDifferentComponentsCompareLong>コンポーネント '{0}'はレポート '{1}'に存在しません。</StiReportDifferentComponentsCompareLong>
    <StiReportDifferentComponentsCompareMessag1>StiClone '{0}'のコンテナプロパティのコンポーネント '{1}'はありません。</StiReportDifferentComponentsCompareMessag1>
    <StiReportDifferentComponentsCompareMessag2>StiDataBand '{0}'のマスターコンポーネントプロパティのコンポーネント '{1}'はありません。</StiReportDifferentComponentsCompareMessag2>
    <StiReportDifferentComponentsCompareShort>コンポーネントがありません。</StiReportDifferentComponentsCompareShort>
    <StiReportDifferentDataRelationCompareComment>データソース間のリレーションが1つのレポートにのみ表示されます。</StiReportDifferentDataRelationCompareComment>
    <StiReportDifferentDataRelationCompareLong>'{1}'と '{2}'のリレーション '{0}'はレポート '{3}'に存在しません。</StiReportDifferentDataRelationCompareLong>
    <StiReportDifferentDataRelationCompareShort>リレーションなし</StiReportDifferentDataRelationCompareShort>
    <StiReportDifferentDataSourceCompareComment>1つのレポートのみに存在するデータソースを示す値</StiReportDifferentDataSourceCompareComment>
    <StiReportDifferentDataSourceCompareLong>データ '{0}'はレポート '{1}'に存在しません。</StiReportDifferentDataSourceCompareLong>
    <StiReportDifferentDataSourceCompareShort>データソースがありません。</StiReportDifferentDataSourceCompareShort>
    <StiReportDifferentStyleCompareComment>1つのレポートにのみ存在するスタイルを表示す値</StiReportDifferentStyleCompareComment>
    <StiReportDifferentStyleCompareLong>スタイル '{0}'はレポート '{1}'に存在しません。</StiReportDifferentStyleCompareLong>
    <StiReportDifferentStyleCompareShort>スタイルなし</StiReportDifferentStyleCompareShort>
    <StiReportDifferentVariableCompareComment>1つのレポートにのみ存在する変数を表示す値</StiReportDifferentVariableCompareComment>
    <StiReportDifferentVariableCompareLong>変数 '{0}'はレポート '{1}'に存在しません。</StiReportDifferentVariableCompareLong>
    <StiReportDifferentVariableCompareShort>変数なし。</StiReportDifferentVariableCompareShort>
    <StiReportPropertiesCompareComment>レポートプロパティのさまざまな値を表示す値</StiReportPropertiesCompareComment>
    <StiReportPropertiesCompareLong>レポート '{0}'の '{1}'プロパティは、レポート '{2}'の '{3}'プロパティの値に対応していません。</StiReportPropertiesCompareLong>
    <StiReportPropertiesCompareShort>レポートプロパティの異なる値。</StiReportPropertiesCompareShort>
    <StiStylePropertiesCompareComment>同一スタイルの異なるプロパティ値を表示す値</StiStylePropertiesCompareComment>
    <StiStylePropertiesCompareLong>レポート '{0}'のスタイル '{1}'の '{2}'プロパティが、レポート '{0}'のスタイル '{4}'の '{3}'。</StiStylePropertiesCompareLong>
    <StiStylePropertiesCompareShort>スタイルの異なるプロパティ値。</StiStylePropertiesCompareShort>
    <StiVariablePropertiesCompareComment>同一変数のプロパティの異なる値を表示す値</StiVariablePropertiesCompareComment>
    <StiVariablePropertiesCompareLong>レポート '{0}'の変数 '{1}'の '{2}'プロパティが、レポート '{0}'の変数 '{4}'の '{3}'。</StiVariablePropertiesCompareLong>
    <StiVariablePropertiesCompareShort>変数の異なるプロパティ値。</StiVariablePropertiesCompareShort>
  </ReportComparer>
  <ReportComparerViewer>
    <Browse>ブラウズ ... </Browse>
    <BusinessObjects>ビジネスオブジェクト</BusinessObjects>
    <BusinessObjectsDataColumns>Business Objectsデータ列</BusinessObjectsDataColumns>
    <Cancel>キャンセル</Cancel>
    <Compare>比較</Compare>
    <CompareList>比較リスト</CompareList>
    <CompareReports>レポートの比較</CompareReports>
    <Compares>比較</Compares>
    <Components>コンポーネント</Components>
    <DataRelations>データリレーション</DataRelations>
    <DataSources>データソース</DataSources>
    <DataSourcesDataColumns>データソースのデータ列</DataSourcesDataColumns>
    <EnterPassword>ファイルを開くためのパスワードを入力します。</EnterPassword>
    <FirstReport>最初のレポート</FirstReport>
    <LongMessage>長いメッセージ</LongMessage>
    <Next>次へ</Next>
    <OK>OK</OK>
    <OpenReports>レポートを開く</OpenReports>
    <Previous>前へ</Previous>
    <Report>レポート</Report>
    <ReportToCompare>比較するレポート</ReportToCompare>
    <SaveReports>レポートの保存</SaveReports>
    <SecondReport>2番目のレポート</SecondReport>
    <SelectReports>レポートの選択</SelectReports>
    <StatusHigh>High </StatusHigh>
    <StatusLow>Low </StatusLow>
    <StatusMiddle>Middle </StatusMiddle>
    <Styles>スタイル</Styles>
    <Variables>変数</Variables>
    <WarningFile1EqualFile2>警告！ファイル 'First Report'は 'Second Report'と等しくなります。</WarningFile1EqualFile2>
    <WarningFirstAndSecondReportNotFound>警告！ファイル 'First Report'と 'Second Report'が見つかりません。</WarningFirstAndSecondReportNotFound>
    <WarningFirstReportNotFound>警告！ファイル 'First Report'が見つかりません。</WarningFirstReportNotFound>
    <WarningSecondReportNotFound>警告！ファイル 'Second Report'が見つかりません。</WarningSecondReportNotFound>
  </ReportComparerViewer>
  <StiAdvancedBorder>
    <BottomSide>下側のフレーム</BottomSide>
    <LeftSide>左側のフレーム</LeftSide>
    <RightSide>右側のフレーム</RightSide>
    <TopSide>上部のフレーム</TopSide>
  </StiAdvancedBorder>
  <StiArea>
    <BorderColor>この領域の境界線の色です。</BorderColor>
    <Brush>領域を埋めるブラシ</Brush>
    <ColorEach>各系列が独自の色で描画されることを示す値</ColorEach>
    <GridLinesHor>左軸の水平グリッド線</GridLinesHor>
    <GridLinesHorRight>右軸の水平グリッド線</GridLinesHorRight>
    <GridLinesVert>垂直軸のグリッド線</GridLinesVert>
    <InterlacingHor>水平軸のインターレース設定</InterlacingHor>
    <InterlacingVert>縦軸のインターレース設定</InterlacingVert>
    <RadarStyle>レーダー領域のスタイル</RadarStyle>
    <ReverseHor>水平軸のすべての値が逆であることを示す値</ReverseHor>
    <ReverseVert>縦軸のすべての値が逆であることを示す値</ReverseVert>
    <ShowShadow>必要な描画シャドウまたはノーを示す値</ShowShadow>
    <XAxis>XAxisの設定</XAxis>
    <XTopAxis>XTopAxisの設定</XTopAxis>
    <YAxis>YAxisの設定</YAxis>
    <YRightAxis>YRightAxisの設定</YRightAxis>
    <インターレース>水平軸のインターレース設定</インターレース>
  </StiArea>
  <StiAreaSeries>
    <Brush>領域を埋めるために使用されるブラシ</Brush>
  </StiAreaSeries>
  <StiArrowShapeType>
    <ArrowHeight>矢印の高さ係数</ArrowHeight>
    <ArrowWidth>矢印の幅の要素</ArrowWidth>
  </StiArrowShapeType>
  <StiAustraliaPost4StateBarCodeType>
    <Height>バーコードの高さを設定します。</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定します。</Module>
  </StiAustraliaPost4StateBarCodeType>
  <StiAxis>
    <ArrowStyle>軸の矢印のスタイル。</ArrowStyle>
    <Interaction>このコンポーネントの対話オプション。</Interaction>
    <Labels>軸ラベルの設定。</Labels>
    <LineColor>軸の描画に使用された線の色です。</LineColor>
    <LineStyle>軸の線スタイル</LineStyle>
    <LineWidth>軸の描画に使用された線幅</LineWidth>
    <LogarithmicScale>対数スケールが使用されることを示す値</LogarithmicScale>
    <Range>軸の範囲設定</Range>
    <RangeScrollEnabled>軸の範囲をスクロールできるかどうかを示す値</RangeScrollEnabled>
    <ShowEdgeValues>軸の最初と最後の引数がとにかく表示されることを示す値</ShowEdgeValues>
    <ShowScrollBar>スクロールバーが表示されることを示す値。</ShowScrollBar>
    <ShowXAxis>描画の種類X軸</ShowXAxis>
    <ShowYAxis>描画Y軸の種類</ShowYAxis>
    <StartFromZero>すべての引数がゼロからのショーになることを示す値</StartFromZero>
    <Step>どのステップで軸にラベルを表示するかを示す値</Step>
    <Ticks>ティック設定</Ticks>
    <Title>軸のタイトル設定</Title>
    <Visible>軸の可視性</Visible>
  </StiAxis>
  <StiAxisDateTimeStep>
    <Interpolation>この値が補間されることを示す値</Interpolation>
    <NumberOfValues>このタイムステップで表示する値の数を示す値</NumberOfValues>
    <Step>このタイムステップ値が表示されることを示す値</Step>
  </StiAxisDateTimeStep>
  <StiAxisInteraction>
    <RangeScrollEnabled>軸の範囲をスクロールできるかどうかを示す値</RangeScrollEnabled>
  </StiAxisInteraction>
  <StiAxisLabels>
    <Angle>ラベルの回転角度</Angle>
    <Antialiasing>アンチエイリアス描画モードを制御する値。</Antialiasing>
    <Color>ラベル描画の色</Color>
    <Font>軸ラベル描画に使用されるフォント</Font>
    <Format>引数の値を書式化するための書式文字列</Format>
    <Placement>軸にラベルを配置するモード</Placement>
    <Step>どのステップで軸にラベルを表示するかを示す値</Step>
    <TextAfter>引数の文字列表現の後に出力される文字列</TextAfter>
    <TextAlignment>ラベルテキストの配置</TextAlignment>
    <TextBefore>引数文字列表現の前に出力される文字列</TextBefore>
    <Width>軸ラベルの固定幅</Width>
    <WordWrap>ワードラップ</WordWrap>
  </StiAxisLabels>
  <StiAxisRange>
    <Auto>最小値と最大値が自動的に計算されることを示す値</Auto>
    <Maximum>軸範囲の最大値</Maximum>
    <Minimum>軸範囲の最小値</Minimum>
  </StiAxisRange>
  <StiAxisTicks>
    <Length>1つのメジャーティックの長さ</Length>
    <LengthUnderLabels>ラベルの下の1つのメジャーティックの長さ</LengthUnderLabels>
    <MinorCount>2つのメジャーティック間のマイナーティック数</MinorCount>
    <MinorLength>マイナーティック1つの長さ</MinorLength>
    <MinorVisible>小目盛りの視認性</MinorVisible>
    <Step>メジャーティックをどのステップで表示するかを示す値</Step>
    <Visible>大目盛りの視認性</Visible>
  </StiAxisTicks>
  <StiAxisTitle>
    <Alignment>タイトルテキストの配置</Alignment>
    <Antialiasing>アンチエイリアス描画モードを制御する値</Antialiasing>
    <Color>タイトル描画に使用される色</Color>
    <Direction>軸タイトル描画のテキスト方向</Direction>
    <Font>軸のタイトルの描画に使用されるフォント</Font>
    <Position>タイトルテキストの位置</Position>
    <Text>タイトルテキスト</Text>
  </StiAxisTitle>
  <StiBand>
    <MaxHeight>バンドの最大高さ</MaxHeight>
    <MinHeight>バンドの最小高さ</MinHeight>
    <PrintOnEvenOddPages>コンポーネントが偶数ページに印刷されることを示す値</PrintOnEvenOddPages>
    <ResetPageNumber>このバンドのページ番号をリセットできます。</ResetPageNumber>
    <StartNewPage>新しいページに新しい文字列をすべて印刷する必要があることを示す値</StartNewPage>
    <StartNewPageIfLessThan>新しいページを作成するために、ページ上の空き領域（パーセント単位）を予約する必要があることを示す値。値は0〜100の範囲で設定する必要があります。値が100の場合は、いずれの場合も新しいページが作成されます。このプロパティは、StartNewPageプロパティと共に使用されます。</StartNewPageIfLessThan>
  </StiBand>
  <StiBandInteraction>
    <Collapsed>レンダリング時にグループを折りたたむかどうかを示すブール式</Collapsed>
    <CollapseGroupFooter>必要なGroupFooterが折りたたまれているかどうかを示す値</CollapseGroupFooter>
    <CollapsingEnabled>レポートビューアでデータの折りたたみを許可するかどうかを示す値</CollapsingEnabled>
    <SelectionEnabled>このDataBandが出力する1つのデータ行を選択できるかどうかを示す値</SelectionEnabled>
  </StiBandInteraction>
  <StiBarCode>
    <Angle>バーコードの回転角度</Angle>
    <AutoScale>バーコードのサイズを変更する方法を示す値</AutoScale>
    <BackColor>バーコードの背景色</BackColor>
    <BarCodeType>バーコードのタイプ</BarCodeType>
    <Code>バーコードのコードを記入する式</Code>
    <Font>バーコードのフォント</Font>
    <ForeColor>バーコードの色</ForeColor>
    <GetBarCodeEvent>バーコードのコードを取得するときに発生</GetBarCodeEvent>
    <ShowLabelText>このバーコードがラベルテキストを表示するかどうかを示す値</ShowLabelText>
    <ShowQuietZones>このバーコードが静かなゾーンまたはいいえを表示することを示す値</ShowQuietZones>
    <Zoom>バーコードサイズを掛ける値</Zoom>
  </StiBarCode>
  <StiBarCodeTypeService>
    <AddClearZone>Clear Zoneが表示されることを示す値</AddClearZone>
    <AspectRatio>バーコードの横と縦の縦横比を設定する値</AspectRatio>
    <AutoDataColumns>列の量が自動的に計算されることを示す値</AutoDataColumns>
    <AutoDataRows>行の量が自動的に計算されることを示す値</AutoDataRows>
    <Checksum>チェックサムのモード</Checksum>
    <CheckSum>チェックサムのモード</CheckSum>
    <CheckSum1>CheckSum1のモード</CheckSum1>
    <CheckSum2>CheckSum2のモード</CheckSum2>
    <DataColumns>データ列の量</DataColumns>
    <DataRows>データ行の量</DataRows>
    <EncodingMode>エンコードのモード</EncodingMode>
    <EncodingType>エンコードの種類</EncodingType>
    <ErrorsCorrectionLevel>エラー修正レベル。レベルが高いほど、復元するバーコードに多くの情報が追加されます。</ErrorsCorrectionLevel>
    <Height>バーコードの高さ係数</Height>
    <MatrixSize>行列サイズ</MatrixSize>
    <Module>バーコードの最も細かい要素の幅</Module>
    <PrintVerticalBars>垂直セクションを印刷するかどうかを示す値</PrintVerticalBars>
    <Ratio>WideToNarrow比を示す値</Ratio>
    <RatioY>バーコードの縦比率。値は2〜5でなければなりません。</RatioY>
    <ShowQuietZoneIndicator>Quiet Zone IndicatorまたはNoを示す値</ShowQuietZoneIndicator>
    <Space>バーコード要素間のスペース</Space>
    <SupplementCode>コンポーネント補足バーコード</SupplementCode>
    <SupplementType>補足コードのタイプ</SupplementType>
    <UseRectangularSymbols>RectangularSymbolsを使用するかどうかを示す値</UseRectangularSymbols>
  </StiBarCodeTypeService>
  <StiBaseStyle>
    <AllowUseBackColor>レポートエンジンがダイアログコントロールにBackColorを使用できるかどうかを示す値</AllowUseBackColor>
    <AllowUseBorderFormatting>レポートエンジンが境界整形を使用できるかどうかを示す値</AllowUseBorderFormatting>
    <AllowUseBorderSides>レポートエンジンが境界面を使用できるかどうかを示す値</AllowUseBorderSides>
    <AllowUseBorderSidesFromLocation>レポートエンジンがコンポーネントの位置に応じてコンポーネントの境界線を設定できるかどうかを示す値</AllowUseBorderSidesFromLocation>
    <AllowUseBrush>レポートエンジンがブラシの書式設定を使用できるかどうかを示す値</AllowUseBrush>
    <AllowUseFont>レポートエンジンがフォントの書式設定を使用できるかどうかを示す値</AllowUseFont>
    <AllowUseForeColor>レポートエンジンがダイアログコントロールにForeColorを使用できるかどうかを示す値</AllowUseForeColor>
    <AllowUseHorAlignment>レポートエンジンがHorAlignmentの書式設定を使用できるかどうかを示す値</AllowUseHorAlignment>
    <AllowUseImage>レポートエンジンがイメージの書式設定を使用できるかどうかを示す値</AllowUseImage>
    <AllowUseTextBrush>レポートエンジンがTextBrushの書式設定を使用できるかどうかを示す値</AllowUseTextBrush>
    <AllowUseTextOptions>レポートエンジンがTextOptionsの書式設定を使用できるかどうかを示す値</AllowUseTextOptions>
    <AllowUseVertAlignment>レポートエンジンがVertAlignmentの書式設定を使用できるかどうかを示す値</AllowUseVertAlignment>
    <AxisLabelsColor>ラベル描画の色</AxisLabelsColor>
    <AxisLineColor>軸の描画に使用された線の色</AxisLineColor>
    <AxisTitleColor>タイトルの描画に使用される色</AxisTitleColor>
    <BackColor>このスタイルを描画するための背景色</BackColor>
    <BasicStyleColor>このスタイルを描画するための基本色</BasicStyleColor>
    <Border>コンポーネントの境界</Border>
    <Brush>スタイルを塗りつぶすブラシ</Brush>
    <BrushType>このスタイルを描画するために使用するブラシのタイプを示す値</BrushType>
    <ChartAreaBorderColor>領域の境界線の色</ChartAreaBorderColor>
    <ChartAreaBrush>領域を塗りつぶすブラシ</ChartAreaBrush>
    <CollectionName>スタイルコレクションの名前</CollectionName>
    <Color>スタイルの色</Color>
    <Conditions>スタイル条件の集合</Conditions>
    <Description>スタイルの説明</Description>
    <Font>このスタイルを描画するためのフォント</Font>
    <ForeColor>このスタイルを描画するための前景色</ForeColor>
    <GridLinesHorColor>水平グリッド線を描画するために使用される色</GridLinesHorColor>
    <GridLinesVertColor>垂直グリッド線を描画するために使用される色</GridLinesVertColor>
    <HorAlignment>スタイルの水平方向の配置</HorAlignment>
    <Image>ImageコンポーネントのImageプロパティを満たすイメージ</Image>
    <InterlacingHorBrush>水平インターレースバーを描画するために使用されるブラシ</InterlacingHorBrush>
    <InterlacingVertBrush>垂直インターレースバーを描画するために使用されるブラシ</InterlacingVertBrush>
    <LegendBorderColor>ボーダーカラー</LegendBorderColor>
    <LegendBrush>凡例の背景ブラシ</LegendBrush>
    <LegendLabelsColor>ラベルの色</LegendLabelsColor>
    <LegendTitleColor>凡例のタイトルカラー</LegendTitleColor>
    <Name>スタイル名</Name>
    <SeriesLabelsBorderColor>シリーズラベルの境界線の色</SeriesLabelsBorderColor>
    <SeriesLabelsBrush>シリーズラベルの領域を塗りつぶすために使用されるブラシ</SeriesLabelsBrush>
    <SeriesLabelsColor>シリーズラベルの前景色</SeriesLabelsColor>
    <StyleColors>グラフ系列を描画するために使用される色のリスト</StyleColors>
    <TextBrush>テキストを描画するブラシ</TextBrush>
    <VertAlignment>スタイルの垂直方向の配置</VertAlignment>
  </StiBaseStyle>
  <StiBorder>
    <Color>境界線の色</Color>
    <DropShadow>ドロップシャドウが表示されるかどうかを示す値</DropShadow>
    <ShadowBrush>ボーダーシャドウブラシ</ShadowBrush>
    <ShadowSize>シャドウサイズ</ShadowSize>
    <Side>フレームの枠線</Side>
    <Size>枠のサイズ</Size>
    <Style>ボーダースタイル</Style>
    <Topmost>ボーダーサイドがすべてのコンポーネントの上に描画されることを示す値</Topmost>
  </StiBorder>
  <StiBorderSide>
    <Color>境界線の色</Color>
    <Size>枠のサイズ</Size>
    <Style>ボーダースタイル</Style>
  </StiBorderSide>
  <StiBusinessObject>
    <Alias>ビジネスオブジェクトのエイリアス</Alias>
    <Category>ビジネスオブジェクトのカテゴリ名</Category>
    <Columns>ビジネスオブジェクトの列コレクション</Columns>
    <Name>ビジネス・オブジェクトの名前</Name>
  </StiBusinessObject>
  <StiButtonControl>
    <Cancel>ユーザーがESCAPEキーを押したときにクリックされるボタンを示す値</Cancel>
    <Default>ユーザーがEnterキーを押したときにクリックされるボタンを示す値</Default>
    <DialogResult>ボタンがクリックされたときに親フォームに返される値</DialogResult>
    <Image>ボタンコントロールに表示される画像</Image>
    <ImageAlign>ボタンコントロール上の画像の位置揃え</ImageAlign>
    <Text>このコントロールに関連付けられたテキスト</Text>
    <TextAlign>ボタンコントロール上のテキストの配置</TextAlign>
  </StiButtonControl>
  <StiCandlestickSeries>
    <ListOfValuesClose>閉じる値のリストを満たす式。例：1; 2; 3 </ListOfValuesClose>
    <ListOfValuesHigh>高い値のリストを満たす式。例：1; 2; 3 </ListOfValuesHigh>
    <ListOfValuesLow>低い値のリストを満たす式。例：1; 2; 3 </ListOfValuesLow>
    <ListOfValuesOpen>開いている値のリストを埋める式。例：1; 2; 3 </ListOfValuesOpen>
    <ValueClose>close値式です。例：{Order.Value} </ValueClose>
    <ValueDataColumnClose>閉じる値を含む列名列。</ValueDataColumnClose>
    <ValueDataColumnHigh>高い値を含む列名。</ValueDataColumnHigh>
    <ValueDataColumnLow>低い値を含む列名。</ValueDataColumnLow>
    <ValueDataColumnOpen>開いている値を含む列名。</ValueDataColumnOpen>
    <ValueHigh>高い値の式。例：{Order.Value} </ValueHigh>
    <ValueLow>低い値の式。例：{Order.Value} </ValueLow>
    <ValueOpen>開いている値の式。例：{Order.Value} </ValueOpen>
  </StiCandlestickSeries>
  <StiCap>
    <Color>キャップの色</Color>
    <Fill>キャップの塗りつぶしモード</Fill>
    <Height>キャップの高さ</Height>
    <Style>キャップスタイル</Style>
    <Width>キャップの幅</Width>
  </StiCap>
  <StiChart>
    <Area>チャートのエリア</Area>
    <ChartType>チャートのタイプ</ChartType>
    <ConstantLines>グラフの定数行の設定</ConstantLines>
    <HorSpacing>グラフの境界線とグラフの間の水平方向の間隔</HorSpacing>
    <Legend>凡例の設定</Legend>
    <ProcessAtEnd>レポートの実行終了時にチャートが処理されることを示す値</ProcessAtEnd>
    <ProcessChartEvent>ProcessChartを取得するときに発生</ProcessChartEvent>
    <Rotation>出力前にグラフを回転させる方法を示す値。</Rotation>
    <Series>シリーズのリスト</Series>
    <SeriesLabels>シリーズのラベル</SeriesLabels>
    <Strips>グラフの設定を削除</Strips>
    <Style>チャートのスタイル</Style>
    <Table>グラフ表の設定</Table>
    <Title>チャートのタイトル設定</Title>
    <VertSpacing>グラフの境界線とグラフの間の垂直方向の間隔</VertSpacing>
  </StiChart>
  <StiChartArea>
    <ColorEach>各系列が独自の色で描画されることを示す値</ColorEach>
    <GridLinesHor>左軸の水平グリッド線</GridLinesHor>
    <GridLinesVert>垂直軸のグリッド線</GridLinesVert>
    <InterlacingHor>水平軸のインターレース設定</InterlacingHor>
    <InterlacingVert>縦軸のインターレース設定</InterlacingVert>
    <ReverseHor>水平軸のすべての値が逆であることを示す値</ReverseHor>
    <ReverseVert>縦軸のすべての値が逆であることを示す値</ReverseVert>
    <XAxis>XAxisの設定</XAxis>
    <XTopAxis>XTopAxisの設定</XTopAxis>
    <YAxis>YAxisの設定</YAxis>
    <YRightAxis>YRightAxisの設定</YRightAxis>
  </StiChartArea>
  <StiChartAxis>
    <Visible>軸の可視性</Visible>
  </StiChartAxis>
  <StiChartAxisLabels>
    <Angle>ラベルの回転角度</Angle>
    <Color>ラベル描画の色</Color>
    <Font>軸ラベル描画に使用されるフォント</Font>
    <Placement>軸にラベルを配置するモード</Placement>
    <Step>どのステップで軸にラベルを表示するかを示す値</Step>
    <TextAfter>引数の文字列表現の後に出力される文字列</TextAfter>
    <TextAlignment>ラベルテキストの配置</TextAlignment>
    <TextBefore>引数文字列表現の前に出力される文字列</TextBefore>
  </StiChartAxisLabels>
  <StiChartAxisRange>
    <Auto>最小値と最大値が自動的に計算されることを示す値</Auto>
    <Maximum>軸範囲の最大値</Maximum>
    <Minimum>軸範囲の最小値</Minimum>
  </StiChartAxisRange>
  <StiChartAxisTitle>
    <Alignment>タイトルテキストの配置</Alignment>
    <Color>タイトル描画に使用される色</Color>
    <Font>軸のタイトルの描画に使用されるフォント</Font>
    <Position>タイトルテキストの位置</Position>
    <Text>タイトルテキスト</Text>
    <Visible>タイトルの可視性</Visible>
  </StiChartAxisTitle>
  <StiChartElement>
    <Area>チャートのエリア</Area>
    <Arguments>チャートの引数</Arguments>
    <CloseValues>チャートの値を閉じます。</CloseValues>
    <ColorEach />
    <ConstantLines>グラフの定数行の設定</ConstantLines>
    <EndValues>チャートの終了値</EndValues>
    <Group />
    <HighValues>チャートの最高値</HighValues>
    <Labels>シリーズのラベル</Labels>
    <Legend>凡例の設定</Legend>
    <LowValues>チャートの最低値</LowValues>
    <Series>チャートシリーズ</Series>
    <Style>チャートのスタイル</Style>
    <TextFormat>テキスト形式</TextFormat>
    <Title>チャートのタイトル</Title>
    <TrendLines>トレンドラインの設定</TrendLines>
    <Values>チャート値</Values>
    <Weights>チャートの重み値</Weights>
  </StiChartElement>
  <StiChartGridLines>
    <Color>メジャーグリッド線の描画に使用される色</Color>
  </StiChartGridLines>
  <StiChartInterlacing>
    <Visible>インターレースバーの可視性</Visible>
  </StiChartInterlacing>
  <StiChartLabels>
    <AutoRotate>シリーズラベルの自動回転モード描画を有効または無効にする値</AutoRotate>
    <Font>シリーズラベルを描画するために使用されるフォント</Font>
    <ForeColor>ラベル描画の色</ForeColor>
    <TextAfter>ラベルテキストの後に表示されるテキスト</TextAfter>
    <TextBefore>ラベルテキストの前に表示されるテキスト</TextBefore>
  </StiChartLabels>
  <StiChartLegend>
    <Columns>列の量</Columns>
    <Direction>凡例の連続描画に使用された方向</Direction>
    <HorAlignment>凡例配置の水平方向の整列</HorAlignment>
    <VertAlignment>凡例配置の垂直方向の配置</VertAlignment>
    <Visible>グラフの凡例の可視性</Visible>
  </StiChartLegend>
  <StiChartLegendLabels>
    <Color>ラベルの色</Color>
    <Font>グラフの凡例でシリーズタイトルの描画に使用されたフォント</Font>
  </StiChartLegendLabels>
  <StiChartLegendTitle>
    <Color>凡例のタイトルカラー</Color>
    <Font>チャートの凡例のタイトルフォント</Font>
  </StiChartLegendTitle>
  <StiChartTable>
    <Font>グラフ表のフォント</Font>
    <GridLineColor>グリッド線の色</GridLineColor>
    <GridLinesHor>水平グリッド線の可視性</GridLinesHor>
    <GridLinesVert>垂直グリッド線の可視性</GridLinesVert>
    <GridOutline>グリッドアウトラインの可視性</GridOutline>
    <MarkerVisible>マーカーの可視性</MarkerVisible>
    <Visible>グラフ表の可視性</Visible>
  </StiChartTable>
  <StiChartTitle>
    <Alignment>チャートタイトルの整列</Alignment>
    <Antialiasing>チャートタイトルのアンチエイリアス描画モードを制御する値</Antialiasing>
    <Brush>チャートタイトルのテキストブラシ</Brush>
    <Dock>ドッキングチャートタイトル</Dock>
    <Font>グラフのタイトルのフォント</Font>
    <Spacing>チャートタイトルとチャートエリアの間隔</Spacing>
    <Text>グラフタイトルのテキスト</Text>
    <Visible>グラフタイトルの可視性</Visible>
  </StiChartTitle>
  <StiCheckBox>
    <Checked>チェック状態の計算に使用される式</Checked>
    <CheckStyleForFalse>false値のスタイルをチェック</CheckStyleForFalse>
    <CheckStyleForTrue>真の値のスタイルをチェック</CheckStyleForTrue>
    <ContourColor>輪郭線の色</ContourColor>
    <ExcelValue>Excelにデータをエクスポートするために使用される式</ExcelValue>
    <GetCheckedEvent>状態がチェックされているときに発生</GetCheckedEvent>
    <Size>輪郭のサイズ</Size>
    <Values>真偽値を記述する文字列</Values>
    <サイズ>輪郭サイズ</サイズ>
  </StiCheckBox>
  <StiCheckBoxControl>
    <Checked>チェックボックスがチェックされた状態にあるかどうかを示す値</Checked>
    <CheckedBinding>チェックされたデータバインディング</CheckedBinding>
    <CheckedChangedEvent>CheckedChangedイベントのスクリプト</CheckedChangedEvent>
    <Text>このコントロールに関連付けられたテキスト</Text>
    <TextBinding>テキストのデータバインディング</TextBinding>
  </StiCheckBoxControl>
  <StiCheckedListBoxControl>
    <CheckOnClick>項目が選択されているときにチェックボックスを切り替える必要があるかどうかを示す値</CheckOnClick>
    <ItemHeight>アイテム領域の高さ</ItemHeight>
    <Items>このCheckedListBox内の項目のコレクション</Items>
    <ItemsBinding>アイテムのデータバインディング</ItemsBinding>
    <SelectedIndexBinding>選択したインデックスのデータバインディング</SelectedIndexBinding>
    <SelectedIndexChangedEvent>SelectedIndexプロパティが変更されたときに発生</SelectedIndexChangedEvent>
    <SelectedItemBinding>選択した項目のデータバインディング</SelectedItemBinding>
    <SelectedValueBinding>選択した値のデータバインディング</SelectedValueBinding>
    <SelectionMode>選択モードを指定する値</SelectionMode>
    <Sorted>ListBox内の項目がアルファベット順にソートされているかどうかを示す値</Sorted>
  </StiCheckedListBoxControl>
  <StiChildBand>
    <KeepChildTogether>子をまとめて保持することを示す値</KeepChildTogether>
    <PrintIfParentDisabled>親バンドが無効で、子バンドがとにかく印刷されることを示す値を取得または設定します。</PrintIfParentDisabled>
  </StiChildBand>
  <StiClone>
    <Container>クローンコンテナ</Container>
    <ScaleHor>コンテナの内容が縮小または拡大されることを示す値です。</ScaleHor>
  </StiClone>
  <StiClusteredColumnSeries>
    <Width>1小節の幅の倍数です。値1は100％です。</Width>
  </StiClusteredColumnSeries>
  <StiCodabarBarCodeType>
    <Height>バーコードの高さを設定します。</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定します。</Module>
    <Ratio>WideToNarrow比を示す値を取得または設定します。</Ratio>
  </StiCodabarBarCodeType>
  <StiCode11BarCodeType>
    <Checksum>チェックサムのモードを取得または設定します。</Checksum>
    <Height>バーコードの高さを設定します。</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定します。</Module>
  </StiCode11BarCodeType>
  <StiCode128aBarCodeType>
    <Height>バーコードの高さを設定します。</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定します。</Module>
  </StiCode128aBarCodeType>
  <StiCode128AutoBarCodeType>
    <Height>バーコードの高さを設定します。</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定します。</Module>
  </StiCode128AutoBarCodeType>
  <StiCode128BarCodeType>
    <Height>バーコードの高さを設定します。</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定します。</Module>
  </StiCode128BarCodeType>
  <StiCode128bBarCodeType>
    <Height>バーコードの高さを設定します。</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定します。</Module>
  </StiCode128bBarCodeType>
  <StiCode128cBarCodeType>
    <Height>バーコードの高さを設定します。</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定します。</Module>
  </StiCode128cBarCodeType>
  <StiCode39BarCodeType>
    <CheckSum>チェックサムのモードを取得または設定します。</CheckSum>
    <Height>バーコードの高さを設定します。</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定します。</Module>
    <Ratio>WideToNarrow比を示す値を取得または設定します。</Ratio>
  </StiCode39BarCodeType>
  <StiCode39ExtBarCodeType>
    <CheckSum>チェックサムのモードを取得または設定します。</CheckSum>
    <Height>バーコードの高さを設定します。</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定します。</Module>
    <Ratio>WideToNarrow比を示す値を取得または設定します。</Ratio>
  </StiCode39ExtBarCodeType>
  <StiCode93BarCodeType>
    <Height>バーコードの高さを設定します。</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定します。</Module>
    <Ratio>WideToNarrow比を示す値を取得または設定します。</Ratio>
  </StiCode93BarCodeType>
  <StiCode93ExtBarCodeType>
    <Height>バーコードの高さを設定します。</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定します。</Module>
    <Ratio>WideToNarrow比を示す値を取得または設定します。</Ratio>
  </StiCode93ExtBarCodeType>
  <StiColumnSize>
    <MaxWidth>テーブル列の最大幅を取得または設定します。</MaxWidth>
    <MinWidth>テーブル列の最小幅を取得または設定します。</MinWidth>
    <Width>テーブル列に指定された幅を取得または設定します。 値がゼロに等しい場合、幅はテーブル要素によって自動的に計算されます。</Width>
  </StiColumnSize>
  <StiComboBoxControl>
    <DropDownStyle>スタイルを指定する値。</DropDownStyle>
    <DropDownWidth>ドロップダウン部分の幅</DropDownWidth>
    <ItemHeight>アイテムの高さ</ItemHeight>
    <Items>アイテムのコレクションを表すオブジェクト</Items>
    <ItemsBinding>アイテムのデータバインディング</ItemsBinding>
    <MaxDropDownItems>最大数</MaxDropDownItems>
    <MaxLength>許可される最大文字数</MaxLength>
    <SelectedIndexChangedEvent>SelectedIndexChangedイベントのスクリプト</SelectedIndexChangedEvent>
    <SelectedItemBinding>選択した項目のデータバインディング</SelectedItemBinding>
    <SelectedValueBinding>選択した値のデータバインディング</SelectedValueBinding>
    <Sorted>項目がソートされているかどうかを示します</Sorted>
    <Text>このコントロールに関連付けられたテキスト</Text>
    <TextBinding>テキストのデータバインディング</TextBinding>
  </StiComboBoxControl>
  <StiComponent>
    <AfterPrintEvent>コンポーネント印刷後に発生</AfterPrintEvent>
    <Alias>コンポーネント名の代わりに表示されるテキスト。テキストが表示されていない場合、名前が表示されます。</Alias>
    <Anchor>コンポーネントの位置を親コンポーネントのサイズにリンクするモードを示す値を取得または設定します。</Anchor>
    <BeforePrintEvent>コンポーネントを印刷する前に発生</BeforePrintEvent>
    <Bookmark>コンポーネントを埋め込む式Bookmark</Bookmark>
    <Border>コンポーネントのフレーム</Border>
    <Brush>コンポーネントを塗りつぶすブラシ</Brush>
    <BusinessObject>データの取得に使用されるビジネスオブジェクト</BusinessObject>
    <CanBreak>コンポーネントが複数のページで内容を破ることができるかどうかを示す値</CanBreak>
    <CanGrow>このオブジェクトが大きくなることを示す値</CanGrow>
    <CanShrink>このオブジェクトが縮小できることを示す値</CanShrink>
    <CellDockStyle>セルドッキングのタイプ</CellDockStyle>
    <CellType>セルコンテンツのタイプ</CellType>
    <ClickEvent>ユーザーがビューアのウィンドウでコンポーネントをクリックすると発生</ClickEvent>
    <ComponentStyle>コンポーネントのスタイル</ComponentStyle>
    <Conditions>条件の集計</Conditions>
    <CountData>仮想データの行数</CountData>
    <CrossFiltering>現在のページからその要素へのクロスフィルターの適用を制御する値を取得または設定します。</CrossFiltering>
    <DataRelation>マスター詳細レポートのレンダリングに使用されるリンク</DataRelation>
    <DataSource>データを取得するために使用されるデータソース</DataSource>
    <DockStyle>コンポーネントドッキングのタイプ</DockStyle>
    <DoubleClickEvent>ユーザーがレポートビューアでコンポーネントをダブルクリックしたときに発生</DoubleClickEvent>
    <Editable>ビューアのウィンドウでコンポーネントを編集できることを示す値</Editable>
    <Enabled>このコンポーネントが使用可能かどうかを示す値</Enabled>
    <FilterMode>フィルタモード</FilterMode>
    <FilterOn>フィルタがオンになっていることを示す値</FilterOn>
    <Filters>データフィルタのコレクション</Filters>
    <FixedWidth>セルが固定幅を持つことを示す値</FixedWidth>
    <GetBookmarkEvent>コンポーネントのブックマークを取得するときに発生</GetBookmarkEvent>
    <GetDrillDownReportEvent>ドリルダウン操作のレポートを取得する必要がある場合に発生</GetDrillDownReportEvent>
    <GetHyperlinkEvent>コンポーネントのハイパーリンクを取得するときに発生</GetHyperlinkEvent>
    <GetTagEvent>コンポーネントのタグを取得するときに発生</GetTagEvent>
    <GetToolTipEvent>コンポーネントのヒントを取得するときに発生</GetToolTipEvent>
    <GrowToHeight>このコンポーネントの高さがコンテナの底部に向かって増減することを示す値</GrowToHeight>
    <Height>コンポーネントの高さ</Height>
    <HorAlignment>水平方向の配置</HorAlignment>
    <Hyperlink>コンポーネントのハイパーリンクを満たす式</Hyperlink>
    <Interaction>このコンポーネントの対話オプション</Interaction>
    <Left>コンポーネントの左位置</Left>
    <Linked>コンテナへのオブジェクトスナップがオンになっていることを示す値</Linked>
    <Locked>移動がロックされていることを示す値</Locked>
    <MasterComponent>マスターコンポーネント</MasterComponent>
    <MaxSize>最大サイズ</MaxSize>
    <MinSize>最小サイズ</MinSize>
    <MouseEnterEvent>ユーザーがレポートビューアのコンポーネントの領域にマウスを置いたときに発生</MouseEnterEvent>
    <MouseLeaveEvent>ユーザーがレポートビューアのコンポーネントの領域からマウスを離したときに発生</MouseLeaveEvent>
    <Name>コンポーネント名</Name>
    <Printable>コンポーネントが印刷可能かどうかを示す値</Printable>
    <PrintOn>コンポーネントをどのページに印刷するかを示す値</PrintOn>
    <Restrictions>コンポーネントの制限を示す値</Restrictions>
    <ShiftMode>コンポーネントのシフトモードを示す値</ShiftMode>
    <Sort>ソートの規則を記述する文字列の配列</Sort>
    <Tag>コンポーネントタグを満たす式</Tag>
    <TextBrush>テキストを描画するブラシ</TextBrush>
    <ToolTip>コンポーネントのToolTipを塗りつぶす式</ToolTip>
    <Top>コンポーネントの上部位置</Top>
    <TotalLabel>合計のラベルとして表示される値を取得または設定します。</TotalLabel>
    <UseParentStyles>このコンポーネントが親コンポーネントのスタイルを使用する必要があることを示す値</UseParentStyles>
    <VertAlignment>オブジェクトの垂直方向の配置</VertAlignment>
    <Width>コンポーネントの幅</Width>
  </StiComponent>
  <StiConstantLines>
    <Antialiasing>アンチエイリアス描画モードを制御する値</Antialiasing>
    <AxisValue>定数線を描画する軸の値</AxisValue>
    <Font>固定線テキストを描画するために使用されるフォント</Font>
    <LineColor>定数線を描画するために使用される色</LineColor>
    <LineStyle>一定の線スタイル</LineStyle>
    <LineWidth>一定線幅</LineWidth>
    <Orientation>定線の水平または垂直方向</Orientation>
    <Position>対話行のテキスト位置</Position>
    <ShowBehind>定数系列がチャートシリーズの裏側またはチャートシリーズの前に表示されることを示す値</ShowBehind>
    <ShowInLegend>グラフの凡例の定数行</ShowInLegend>
    <Text>定数のテキスト</Text>
    <TitleVisible>定数行の可視性</TitleVisible>
    <Visible>定数行の可視性</Visible>
  </StiConstantLines>
  <StiCrossDataBand>
    <MaxWidth>バンドの最大幅</MaxWidth>
    <MinWidth>バンドの最小幅</MinWidth>
  </StiCrossDataBand>
  <StiCrossField>
    <DisplayValue>表に出力されるセル値の計算に使用される式</DisplayValue>
    <EnumeratorSeparator>列挙子の区切り記号</EnumeratorSeparator>
    <EnumeratorType>列挙型</EnumeratorType>
    <GetCrossValueEvent>イベントGetValueEventのスクリプト</GetCrossValueEvent>
    <GetDisplayCrossValueEvent>イベントGetDisplayCrossValueEventのスクリプト</GetDisplayCrossValueEvent>
    <HideZeros>ゼロを表示する必要がないことを示す値</HideZeros>
    <KeepMergedCellsTogether>結合されたセルが次のページに分割されないことを示す値</KeepMergedCellsTogether>
    <MergeHeaders>ヘッダーのすべての等しい値が1つにマージされることを示す値</MergeHeaders>
    <PrintOnAllPages>コンポーネントがすべてのページに印刷されていることを示す値</PrintOnAllPages>
    <ShowPercents>セル内の値をパーセントで表示する必要があることを示す値</ShowPercents>
    <ShowTotal>合計を出力する必要があるかどうかを示す値</ShowTotal>
    <SortDirection>ソート方向</SortDirection>
    <SortType>ソートのタイプ</SortType>
    <Summary>値の集計のタイプ</Summary>
    <SummaryValues>ゼロのタイプとNull値の合計</SummaryValues>
    <UseStyleOfSummaryInColumnTotal>集計セルのスタイルが列合計で使用されることを示す値</UseStyleOfSummaryInColumnTotal>
    <UseStyleOfSummaryInRowTotal>要約セルのスタイルが行の合計で使用されることを示す値</UseStyleOfSummaryInRowTotal>
    <Value>セル値の計算に使用される式</Value>
  </StiCrossField>
  <StiCrossFooterBand>
    <MaxWidth>バンドの最大幅</MaxWidth>
    <MinWidth>バンドの最小幅</MinWidth>
  </StiCrossFooterBand>
  <StiCrossGroupFooterBand>
    <MaxWidth>バンドの最大幅</MaxWidth>
    <MinWidth>バンドの最小幅</MinWidth>
  </StiCrossGroupFooterBand>
  <StiCrossGroupHeaderBand>
    <MaxWidth>バンドの最大幅</MaxWidth>
    <MinWidth>バンドの最小幅</MinWidth>
  </StiCrossGroupHeaderBand>
  <StiCrossHeaderBand>
    <MaxWidth>バンドの最大幅</MaxWidth>
    <MinWidth>バンドの最小幅</MinWidth>
  </StiCrossHeaderBand>
  <StiCrossTab>
    <EmptyValue>空のセルにクロス集計を表示するために使用する文字列値</EmptyValue>
    <HorAlignment>クロスタブの水平方向の配置</HorAlignment>
    <KeepCrossTabTogether>CrossTabが配置されている場所にDataBandを保持する必要があることを示す値</KeepCrossTabTogether>
    <PrintIfEmpty>データが存在しない場合にクロス集計が印刷されることを示す値</PrintIfEmpty>
    <RightToLeft>水平クロスタブ方向</RightToLeft>
    <Wrap>クロスタブを1つの列に出力することを示す値。同時に、幅に収まらないものはすべて出力されます。このプロパティを使用するには、クロスタブが配置されているバンドのCanBreakプロパティを有効にする必要があります（バンドに配置されている場合）。</Wrap>
    <WrapGap>ラップされたクロスタブの2つの部分の間のスペース。このプロパティはWrapプロパティと共同で使用されます。</WrapGap>
  </StiCrossTab>
  <StiDataBand>
    <BeginRenderEvent>バンドがレンダー開始時に発生</BeginRenderEvent>
    <CalcInvisible>集計関数の計算時に、目に見えないデータバンドを考慮に入れることを意味します。</CalcInvisible>
    <ColumnDirection>レンダリング列の方向</ColumnDirection>
    <ColumnGaps>2つの列間の距離</ColumnGaps>
    <Columns>列の数</Columns>
    <ColumnWidth>列の幅</ColumnWidth>
    <EndRenderEvent>バンドレンダリングが終了したときに発生</EndRenderEvent>
    <EvenStyle>偶数行のスタイルを示す値</EvenStyle>
    <FilterEngine>フィルタがデータにどのように適用されるかを示す値。レポートジェネレータの手段であるか、SQLクエリを変更するかによって決まります。</FilterEngine>
    <FilterMode>フィルタモード</FilterMode>
    <GetCollapsedEvent>折りたたみ値が計算されたときに発生</GetCollapsedEvent>
    <KeepChildTogether>子をまとめて保持することを示す値</KeepChildTogether>
    <KeepDetailsTogether>このDataBandと共に詳細を保持することを示す値</KeepDetailsTogether>
    <KeepFooterTogether>フッターにデータが印刷されていることを示す値</KeepFooterTogether>
    <KeepGroupTogether>グループをまとめて保持することを示す値</KeepGroupTogether>
    <KeepHeaderTogether>ヘッダーにデータが一緒に印刷されていることを示す値</KeepHeaderTogether>
    <MinRowsInColumn>1列の最小行数</MinRowsInColumn>
    <OddStyle>奇数行のスタイルを示す値</OddStyle>
    <PrintIfDetailEmpty>詳細が空であればマスターデータを印刷する必要があることを示す値</PrintIfDetailEmpty>
    <PrintOnAllPages>コンポーネントがすべてのページに印刷されていることを示す値</PrintOnAllPages>
    <RenderingEvent>データ行のレンダリング時に発生</RenderingEvent>
    <ResetDataSource>レンダリングの準備を開始するときにデータソースの位置をリセットすることを示す値</ResetDataSource>
    <RightToLeft>水平方向の列方向</RightToLeft>
  </StiDataBand>
  <StiDatabase>
    <Alias>データベースのエイリアス</Alias>
    <ConnectedEvent>接続がアクティブになったときに発生</ConnectedEvent>
    <ConnectingEvent>接続がアクティブになっているときに発生</ConnectingEvent>
    <ConnectionString>SQL接続パラメータを含む接続文字列</ConnectionString>
    <DisconnectedEvent>接続が無効になったときに発生</DisconnectedEvent>
    <DisconnectingEvent>接続が無効になっているときに発生</DisconnectingEvent>
    <Name>データベースの名前</Name>
    <PathData>XMLデータへのパス</PathData>
    <PathSchema>XMLスキーマへのパス</PathSchema>
    <PromptUserNameAndPassword>UserNameパラメータとPasswordパラメータをユーザーから要求する必要があることを示す値</PromptUserNameAndPassword>
    <エイリアス>データベースのエイリアス</エイリアス>
  </StiDatabase>
  <StiDataColumn>
    <Alias>列データのエイリアス</Alias>
    <Expression>計算列の式</Expression>
    <Name>レポートで使用される列名</Name>
    <NameInSource>データベース内の列元の名前</NameInSource>
    <Type>列データのタイプ</Type>
  </StiDataColumn>
  <StiDataMatrixBarCodeType>
    <EncodingType>エンコーディングタイプのタイプを取得または設定</EncodingType>
    <Height>バーコードの高さを設定</Height>
    <MatrixSize>マトリックスサイズを取得または設定</MatrixSize>
    <Module>バーコードの最も細かい要素の幅を取得または設定</Module>
    <UseRectangularSymbols>RectangularSymbolsを使用するかどうかを示す値を取得または設定</UseRectangularSymbols>
  </StiDataMatrixBarCodeType>
  <StiDataParameter>
    <Alias>dataパラメータのエイリアス</Alias>
    <Expression>計算列の式</Expression>
    <Name>データパラメータの名前</Name>
    <Size>パラメータのサイズ</Size>
    <Type>データパラメータの型</Type>
  </StiDataParameter>
  <StiDataRelation>
    <Alias>リレーションのエイリアス</Alias>
    <ChildColumns>子列名のコレクション</ChildColumns>
    <ChildSource>子データソース</ChildSource>
    <Name>レポートで使用されるリレーション名</Name>
    <NameInSource>データベース内のリレーションの名前</NameInSource>
    <ParentColumns>親列名のコレクション</ParentColumns>
    <ParentSource>親データソース</ParentSource>
  </StiDataRelation>
  <StiDataSource>
    <Alias>データソースエイリアス</Alias>
    <AllowExpressions>データソースのsqlクエリにスクリプト式またはnoを含めることができることを示す値</AllowExpressions>
    <CodePage>コードページ</CodePage>
    <Columns>列コレクション</Columns>
    <CommandTimeout>コマンドの実行中に、試行をキャンセルしてエラーを生成するまで待機する秒数。デフォルトは30です。</CommandTimeout>
    <ConnectionOrder>データソースがデータにどの順序で接続するかを示す値</ConnectionOrder>
    <ConnectOnStart>データソースが自動的にデータに接続されていないことを示す値</ConnectOnStart>
    <Name>レポートで使用されるデータソース名</Name>
    <NameInSource>データベース内のデータソースの名前</NameInSource>
    <Parameters>SQLクエリのパラメータコレクション</Parameters>
    <Path>データファイルへのパス</Path>
    <ReconnectOnEachRow>マスター・ディテール・レポートの各マスター行でデータ・ソースが再接続されたことを示す値</ReconnectOnEachRow>
    <SqlCommand>データソースで実行するSQL文</SqlCommand>
    <Type>SQLデータソースのタイプを示す値</Type>
    <エイリアス>データソースエイリアス</エイリアス>
    <名前>レポートで使用されるデータソース名</名前>
  </StiDataSource>
  <StiDateTimePickerControl>
    <CustomFormat>カスタム日時書式文字列</CustomFormat>
    <DropDownAlign>日付/時刻選択コントロールのドロップダウンカレンダーの位置揃え</DropDownAlign>
    <Format>コントロールに表示される日付と時刻の書式</Format>
    <MaxDate>コントロールで選択可能な最大日時</MaxDate>
    <MaxDateBinding>最大日付のデータバインディング</MaxDateBinding>
    <MinDate>コントロールで選択できる最小日時</MinDate>
    <MinDateBinding>最小日付のデータバインディング</MinDateBinding>
    <ShowUpDown>アップダウンコントロールを使用して日付/時刻値を調整するかどうかを示す値</ShowUpDown>
    <Today>日付が現在の日付と等しいことを示す値</Today>
    <Value>コントロールに割り当てられた日時の値</Value>
    <ValueBinding>値のデータバインディング</ValueBinding>
    <ValueChangedEvent>イベントValueChangedのスクリプト</ValueChangedEvent>
  </StiDateTimePickerControl>
  <StiDialogStyle>
    <GlyphColor>スタイルのグリフ色</GlyphColor>
    <HotBackColor>スタイルのホット背景色</HotBackColor>
    <HotForeColor>スタイルのホットアイテムの前景色</HotForeColor>
    <HotGlyphColor>スタイルのホットアイテムの絵文字色</HotGlyphColor>
    <HotSelectedBackColor>スタイルのホット選択アイテムの背景色</HotSelectedBackColor>
    <HotSelectedForeColor>スタイルのホット選択アイテムの前景色</HotSelectedForeColor>
    <HotSelectedGlyphColor>スタイルのホット選択アイテムの絵文字色</HotSelectedGlyphColor>
    <SelectedBackColor>スタイルの選択されたアイテムの背景色</SelectedBackColor>
    <SelectedForeColor>スタイルの選択されたアイテムの前景色</SelectedForeColor>
    <SelectedGlyphColor>スタイルの選択されたアイテムのグリフ色</SelectedGlyphColor>
    <SeparatorColor>スタイル描画時の色</SeparatorColor>
  </StiDialogStyle>
  <StiDoughnutSeries>
    <Diameter>ドーナツシリーズの直径の固定サイズ</Diameter>
  </StiDoughnutSeries>
  <StiDrillDownParameter>
    <Expression>ドリルダウンパラメータを満たす式</Expression>
    <Name>ドリルダウンパラメータの名前</Name>
  </StiDrillDownParameter>
  <StiDutchKIXBarCodeType>
    <Height>バーコードの高さを設定</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定</Module>
  </StiDutchKIXBarCodeType>
  <StiDynamicBand>
    <BreakIfLessThan>ページ上の空き領域の割合（パーセント単位）を、新しいページまたは新しい列の作成に予約する必要があることを示す値。値は0〜100の範囲で設定する必要があります。値が100の場合は、いずれの場合も新しいページまたは新しい列が作成されます。このプロパティは、NewPageBefore、NewPageAfter、NewColumnBefore、NewColumnAfterプロパティと共に使用されます。</BreakIfLessThan>
    <NewColumnAfter>このプロパティの値がtrueの場合、バンドの出力後に新しい列が生成されます。</NewColumnAfter>
    <NewColumnBefore>このプロパティの値がtrueの場合、バンドを出力する前に、新しい列が生成されます。</NewColumnBefore>
    <NewPageAfter>このプロパティの値がtrueの場合、バンドの出力後に新しいページが生成</NewPageAfter>
    <NewPageBefore>このプロパティの値がtrueの場合、バンドの出力前に新しいページが生成　次のページでバンドの出力を続けます。</NewPageBefore>
    <PrintAtBottom>フッターがページの下部に印刷されていることを示す値</PrintAtBottom>
    <SkipFirst>このプロパティの値がtrueの場合、新しいページ/列は2番目のケースからのみ生成</SkipFirst>
  </StiDynamicBand>
  <StiEAN128aBarCodeType>
    <Height>バーコードの高さを設定</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定</Module>
  </StiEAN128aBarCodeType>
  <StiEAN128AutoBarCodeType>
    <Height>バーコードの高さを設定</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定</Module>
  </StiEAN128AutoBarCodeType>
  <StiEAN128bBarCodeType>
    <Height>バーコードの高さを設定</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定</Module>
  </StiEAN128bBarCodeType>
  <StiEAN128cBarCodeType>
    <Height>バーコードの高さを設定</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定</Module>
  </StiEAN128cBarCodeType>
  <StiEAN13BarCodeType>
    <Height>バーコードの高さを設定</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定</Module>
    <ShowQuietZoneIndicator>Quiet Zone IndicatorまたはNoを示す値を取得または設定</ShowQuietZoneIndicator>
    <SupplementCode>コンポーネント補足バーコードを取得または設定</SupplementCode>
    <SupplementType>補足コードのタイプを取得または設定</SupplementType>
  </StiEAN13BarCodeType>
  <StiEAN8BarCodeType>
    <Height>バーコードの高さを設定</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定</Module>
    <ShowQuietZoneIndicator>Quiet Zone IndicatorまたはNoを示す値を取得または設定</ShowQuietZoneIndicator>
    <SupplementCode>コンポーネント補足バーコードを取得または設定</SupplementCode>
    <SupplementType>補足コードのタイプを取得または設定</SupplementType>
  </StiEAN8BarCodeType>
  <StiElement>
    <BackColor>要素の背景色</BackColor>
    <Margin>要素のマージン</Margin>
    <Padding>要素のパディング</Padding>
  </StiElement>
  <StiEmptyBand>
    <BeginRenderEvent>バンドがレンダー開始時に発生</BeginRenderEvent>
    <EndRenderEvent>バンドレンダリングが終了したときに発生</EndRenderEvent>
    <EvenStyle>偶数行のスタイルを示す値</EvenStyle>
    <OddStyle>奇数行のスタイルを示す値</OddStyle>
    <RenderingEvent>データ行のレンダリング時に発生</RenderingEvent>
    <SizeMode>このプロパティを使用すると、ページ上の最後の行のサイズを変更する方法を指定できます。</SizeMode>
  </StiEmptyBand>
  <StiFIMBarCodeType>
    <AddClearZone>Clear ZoneまたはNoを示す値を取得または設定</AddClearZone>
    <Height>バーコードの高さを設定</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定</Module>
  </StiFIMBarCodeType>
  <StiFooterBand>
    <KeepFooterTogether>フッターにデータが印刷されていることを示す値</KeepFooterTogether>
    <PrintIfEmpty>データが存在しない場合のフッターを示す値</PrintIfEmpty>
    <PrintOnAllPages>コンポーネントがすべてのページに印刷されていることを示す値</PrintOnAllPages>
  </StiFooterBand>
  <StiForm>
    <BackColor>コントロールの背景色</BackColor>
    <ClickEvent>Clickイベントのスクリプト</ClickEvent>
    <ClosedFormEvent>ClosedFormイベントのスクリプト</ClosedFormEvent>
    <ClosingFormEvent>ClosingFormイベントのスクリプト</ClosingFormEvent>
    <Font>コントロールのフォント</Font>
    <LoadFormEvent>LoadFormイベントのスクリプト</LoadFormEvent>
    <Location>フォームの左上隅の座標</Location>
    <RightToLeft>コントロールの要素が右から左のフォントを使用してロケールをサポートするように整列されているかどうかを示す値</RightToLeft>
    <Size>フォームのサイズ</Size>
    <StartMode>フォームが表示される時刻を示す値</StartMode>
    <StartPosition>実行時のフォームの開始位置</StartPosition>
    <Text>このコントロールに関連付けられたテキスト</Text>
    <Visible>コントロールが表示されているかどうかを示す値</Visible>
    <WindowState>フォームのウィンドウの状態</WindowState>
  </StiForm>
  <StiGanttSeries>
    <GetListOfValuesEndEvent>終了値のリストを取得するときに発生</GetListOfValuesEndEvent>
    <GetValueEndEvent>プロパティの値を取得するときに発生</GetValueEndEvent>
    <ListOfValues>開始値のリストを満たす式。例：1; 2; 3 </ListOfValues>
    <ListOfValuesEnd>終了値のリストを埋める式。例：1; 2; 3 </ListOfValuesEnd>
    <Value>開始値の式。例：{Order.Value} </Value>
    <ValueDataColumn>開始値を含む列の名前</ValueDataColumn>
    <ValueDataColumnEnd>終了値を含む列の名前</ValueDataColumnEnd>
    <ValueEnd>終了値の式。例：{Order.Value} </ValueEnd>
  </StiGanttSeries>
  <StiGaugeElement>
    <Font>フォント</Font>
    <ForeColor>フォアカラー</ForeColor>
    <Series>ゲージシリーズ</Series>
    <Style>ゲージスタイル</Style>
    <Title>ゲージタイトル</Title>
  </StiGaugeElement>
  <StiGlareBrush>
    <Angle>グラジエントの方向線のx軸から時計回りに測った角度</Angle>
    <EndColor>グラデーションの終了色</EndColor>
    <Focus>グラデーションの中心（グラデーションが終了色のみで構成される点）を指定する0〜1の値</Focus>
    <Scale>0から1までの値で、色がフォーカスからどのくらい速く落ちるかを指定</Scale>
    <StartColor>グラデーションの開始色</StartColor>
  </StiGlareBrush>
  <StiGlassBrush>
    <Blend>ブレンド係数</Blend>
    <Color>ブラシの色</Color>
    <DrawHatch>バックグラウンドで描画ハッチを示す値</DrawHatch>
  </StiGlassBrush>
  <StiGradientBrush>
    <Angle>グラジエントの方向線のx軸から時計回りに測った角度</Angle>
    <EndColor>グラデーションの終了色</EndColor>
    <StartColor>グラデーションの開始色</StartColor>
  </StiGradientBrush>
  <StiGridColumn>
    <Alignment>列の内容の水平方向の整列</Alignment>
    <DataTextField>表示するデータ列の名前</DataTextField>
    <HeaderText>列のヘッダーテキスト</HeaderText>
    <NullText>null値の代わりに表示されるテキスト</NullText>
    <Visible>列の可視性</Visible>
    <Width>列の幅</Width>
  </StiGridColumn>
  <StiGridControl>
    <AlternatingBackColor>元帳の外観のための交互行の背景色</AlternatingBackColor>
    <BackColor>グリッドの背景色</BackColor>
    <BackgroundColor>グリッドの非行領域の色</BackgroundColor>
    <ColumnHeadersVisible>表の列見出しが表示されるかどうかを示す値</ColumnHeadersVisible>
    <Columns>列コレクション</Columns>
    <Filter>フィルタ文字列</Filter>
    <ForeColor>前景色（通常はテキストの色）プロパティ</ForeColor>
    <GridLineColor>グリッド線の色</GridLineColor>
    <GridLineStyle>グリッドの線スタイル</GridLineStyle>
    <HeaderBackColor>すべての行ヘッダーと列ヘッダーの背景色</HeaderBackColor>
    <HeaderFont>列見出しに使用されるフォント</HeaderFont>
    <HeaderForeColor>ヘッダーの前景色</HeaderForeColor>
    <PositionChangedEvent>イベントのスクリプトPositionChanged</PositionChangedEvent>
    <PreferredColumnWidth>グリッド列のデフォルトの幅（ピクセル単位）</PreferredColumnWidth>
    <PreferredRowHeight>優先行の高さ</PreferredRowHeight>
    <RowHeadersVisible>行ヘッダーを表示するかどうかを指定する値</RowHeadersVisible>
    <RowHeaderWidth>行ヘッダーの幅</RowHeaderWidth>
    <SelectionBackColor>選択した行の背景色</SelectionBackColor>
    <SelectionForeColor>選択した行の前景色</SelectionForeColor>
  </StiGridControl>
  <StiGridLines>
    <Color>メジャーグリッド線の描画に使用される色</Color>
    <MinorColor>細いグリッド線の描画に使用される色</MinorColor>
    <MinorCount>各メジャーグリッド線ごとのマイナーグリッド線の数</MinorCount>
    <MinorStyle>小さなグリッド線の描画に使用されるスタイル</MinorStyle>
    <MinorVisible>マイナーグリッド線の可視性</MinorVisible>
    <Style>主要なグリッド線の描画に使用されるスタイル</Style>
    <Visible>主要なグリッド線の可視性</Visible>
  </StiGridLines>
  <StiGroupBoxControl>
    <Text>このコントロールに関連付けられたテキスト</Text>
    <TextBinding>テキストのデータバインディング</TextBinding>
  </StiGroupBoxControl>
  <StiGroupFooterBand>
    <KeepGroupFooterTogether>グループフッターにデータが印刷されていることを示す値</KeepGroupFooterTogether>
  </StiGroupFooterBand>
  <StiGroupHeaderBand>
    <BeginRenderEvent>バンドがレンダリングを開始すると発生</BeginRenderEvent>
    <Condition>グループ化条件</Condition>
    <EndRenderEvent>バンドの描画が終了すると発生</EndRenderEvent>
    <GetCollapsedEvent>折りたたみ値が計算されたときに発生</GetCollapsedEvent>
    <GetSummaryExpressionEvent>グループサマリー式が計算されたときに発生</GetSummaryExpressionEvent>
    <GetValueEvent>グループの条件がチェックされたときに発生</GetValueEvent>
    <KeepGroupHeaderTogether>グループヘッダーがデータと共に印刷されることを示す値</KeepGroupHeaderTogether>
    <KeepGroupTogether>グループをまとめて保持することを示す値</KeepGroupTogether>
    <PrintOnAllPages>コンポーネントがすべてのページに印刷されていることを示す値</PrintOnAllPages>
    <RenderingEvent>1行のデータをレンダリングするときに発生</RenderingEvent>
    <SortDirection>グループ化されたデータの方向のソート</SortDirection>
    <SummaryExpression>集計集計の計算に使用される集計式を取得または設定</SummaryExpression>
    <SummarySortDirection>合計によるソートのグループ合計を計算する関数を取得または設定</SummarySortDirection>
    <SummaryType>グループ化されたデータのソート方向を取得または設定</SummaryType>
  </StiGroupHeaderBand>
  <StiHatchBrush>
    <BackColor>このブラシによって描画されるハッチング線の間のスペースの色</BackColor>
    <ForeColor>このブラシによって描画されるハッチング線の色</ForeColor>
    <Style>このブラシのハッチスタイル</Style>
  </StiHatchBrush>
  <StiHeaderBand>
    <KeepHeaderTogether>ヘッダーが一緒にデータと共に印刷されることを示す値</KeepHeaderTogether>
    <PrintIfEmpty>データが存在しない場合のヘッダーを示す値</PrintIfEmpty>
    <PrintOnAllPages>コンポーネントがすべてのページに印刷されていることを示す値</PrintOnAllPages>
  </StiHeaderBand>
  <StiHierarchicalBand>
    <Footers>階層バンドのフッターのリスト</Footers>
    <Headers>階層バンドのヘッダーのリスト</Headers>
    <Indent>データレベルのオフセットのためにバンドの左側からインデント</Indent>
    <KeyDataColumn>データキーを含む列</KeyDataColumn>
    <MasterKeyDataColumn>データマスターキーを含む列</MasterKeyDataColumn>
    <ParentValue>親行を識別する親値を含む列</ParentValue>
  </StiHierarchicalBand>
  <StiHorChartGridLines>
    <Visible>主要なグリッド線の可視性</Visible>
  </StiHorChartGridLines>
  <StiImage>
    <DataColumn>イメージを含む列の名前</DataColumn>
    <File>画像を含むファイルへのパス</File>
    <GetImageDataEvent>コンポーネントのイメージを取得するときに発生</GetImageDataEvent>
    <GetImageURLEvent>コンポーネントのイメージURLを取得するときに発生</GetImageURLEvent>
    <GlobalizedName>グローバリゼーション・マネージャのキーとして使用される値</GlobalizedName>
    <Image>画像</Image>
    <ImageData>コンポーネントイメージプロパティを満たす式</ImageData>
    <ImageRotation>出力前にイメージを回転する方法を示す値</ImageRotation>
    <ImageURL>コンポーネントイメージURLを埋める式</ImageURL>
    <ProcessingDuplicates>レポートエンジンが重複したイメージをどのように処理するかを示す値</ProcessingDuplicates>
  </StiImage>
  <StiInteraction>
    <AllowSeries>Seriesでドリルダウン操作を実行できるかどうかを示す値</AllowSeries>
    <AllowSeriesElements>Series Elementsでドリルダウン操作を実行できるかどうかを示す値</AllowSeriesElements>
    <Bookmark>コンポーネントのブックマークを埋めるための式</Bookmark>
    <DrillDownEnabled>ドリルダウン操作を実行できるかどうかを示す値</DrillDownEnabled>
    <DrillDownMode>ドリルダウンページを開くモード</DrillDownMode>
    <DrillDownPage>ドリルダウン操作のページ</DrillDownPage>
    <DrillDownParameter1>最初のドリルダウンパラメータ</DrillDownParameter1>
    <DrillDownParameter2>2番目のドリルダウンパラメータ</DrillDownParameter2>
    <DrillDownParameter3>3番目のドリルダウンパラメータ</DrillDownParameter3>
    <DrillDownParameter4>4番目のドリルダウンパラメータ</DrillDownParameter4>
    <DrillDownParameter5>第5ドリルダウンパラメータ</DrillDownParameter5>
    <DrillDownReport>ドリルダウン操作のレポートへのパス</DrillDownReport>
    <Hyperlink>コンポーネントハイパーリンクを満たす式</Hyperlink>
    <SortingColumn>レポートビューアで再ソートされるデータの列</SortingColumn>
    <SortingEnabled>指定したコンポーネントを使用して、レポートビューアでデータの並べ替えを許可するかどうかを示す値</SortingEnabled>
    <Tag>コンポーネントタグを埋めるための式</Tag>
    <ToolTip>コンポーネントのヒントを埋めるための式</ToolTip>
  </StiInteraction>
  <StiInterlacing>
    <InterlacedBrush>インターレースバーを描画するために使用されるブラシ</InterlacedBrush>
    <Visible>インターレースバーの可視性</Visible>
  </StiInterlacing>
  <StiInterleaved2of5BarCodeType>
    <Height>バーコードの高さを設定</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定</Module>
    <Ratio>WideToNarrow比を示す値を取得または設定</Ratio>
  </StiInterleaved2of5BarCodeType>
  <StiIsbn10BarCodeType>
    <Height>バーコードの高さを設定</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定</Module>
    <ShowQuietZoneIndicator>Quiet Zone IndicatorまたはNoを示す値を取得または設定</ShowQuietZoneIndicator>
    <SupplementCode>コンポーネント補足バーコードを取得または設定</SupplementCode>
    <SupplementType>補足コードのタイプを取得または設定</SupplementType>
  </StiIsbn10BarCodeType>
  <StiIsbn13BarCodeType>
    <Height>バーコードの高さを設定</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定</Module>
    <ShowQuietZoneIndicator>Quiet Zone IndicatorまたはNoを示す値を取得または設定</ShowQuietZoneIndicator>
    <SupplementCode>コンポーネント補足バーコードを取得または設定</SupplementCode>
    <SupplementType>補足コードのタイプを取得または設定</SupplementType>
  </StiIsbn13BarCodeType>
  <StiITF14BarCodeType>
    <Height>バーコードの高さを設定</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定</Module>
    <PrintVerticalBars>垂直セクションを印刷するかどうかを示す値を取得または設定</PrintVerticalBars>
    <Ratio>WideToNarrow比を示す値を取得または設定</Ratio>
  </StiITF14BarCodeType>
  <StiJan13BarCodeType>
    <Height>バーコードの高さを設定</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定</Module>
    <ShowQuietZoneIndicator>Quiet Zone IndicatorまたはNoを示す値を取得または設定</ShowQuietZoneIndicator>
    <SupplementCode>コンポーネント補足バーコードを取得または設定</SupplementCode>
    <SupplementType>補足コードのタイプを取得または設定</SupplementType>
  </StiJan13BarCodeType>
  <StiJan8BarCodeType>
    <Height>バーコードの高さを設定</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定</Module>
    <ShowQuietZoneIndicator>Quiet Zone IndicatorまたはNoを示す値を取得または設定</ShowQuietZoneIndicator>
    <SupplementCode>コンポーネント補足バーコードを取得または設定</SupplementCode>
    <SupplementType>補足コードのタイプを取得または設定</SupplementType>
  </StiJan8BarCodeType>
  <StiLabelControl>
    <Text>このコントロールに関連付けられたテキスト</Text>
    <TextAlign>ラベル内のテキストの配置</TextAlign>
    <TextBinding>テキストのデータバインディング</TextBinding>
  </StiLabelControl>
  <StiLegend>
    <BorderColor>ボーダーカラー</BorderColor>
    <Brush>伝説の背景ブラシ</Brush>
    <Columns>列の量</Columns>
    <Direction>凡例の連続描画に使用された方向</Direction>
    <Font>グラフの凡例でシリーズタイトルの描画に使用されたフォント</Font>
    <HideSeriesWithEmptyTitle>空のタイトルの系列を表示/非表示する値</HideSeriesWithEmptyTitle>
    <HorAlignment>凡例配置の水平方向の整列</HorAlignment>
    <HorSpacing>凡例の項目間の水平間隔</HorSpacing>
    <LabelsColor>ラベルの色</LabelsColor>
    <MarkerAlignment>シリーズタイトルに関連するマーカーの整列</MarkerAlignment>
    <MarkerBorder>マーカー枠が表示されることを示す値</MarkerBorder>
    <MarkerSize>マーカーのサイズ</MarkerSize>
    <MarkerVisible>マーカーの可視性</MarkerVisible>
    <ShowShadow>影を描画するかどうかを示す値</ShowShadow>
    <Size>凡例のサイズ</Size>
    <Title>凡例のタイトル</Title>
    <TitleColor>凡例のタイトルカラー</TitleColor>
    <TitleFont>チャートの凡例のタイトルフォント</TitleFont>
    <VertAlignment>凡例配置の垂直方向の配置</VertAlignment>
    <VertSpacing>凡例の項目間の垂直間隔</VertSpacing>
    <Visible>グラフの凡例の可視性</Visible>
  </StiLegend>
  <StiLinePrimitive>
    <Color>線の色</Color>
    <EndCap>エンドキャップの設定</EndCap>
    <Size>行のサイズ</Size>
    <StartCap>エンドキャップの設定</StartCap>
    <Style>ペンスタイル</Style>
  </StiLinePrimitive>
  <StiListBoxControl>
    <ItemHeight>ListBox内の項目の高さ</ItemHeight>
    <Items>ListBoxの項目</Items>
    <ItemsBinding>アイテムのデータバインディング</ItemsBinding>
    <SelectedIndexBinding>選択したインデックスのデータバインディング</SelectedIndexBinding>
    <SelectedIndexChangedEvent>イベントSelectedIndexChangedのスクリプト</SelectedIndexChangedEvent>
    <SelectedItemBinding>選択した項目のデータバインディング</SelectedItemBinding>
    <SelectedValueBinding>選択した値のデータバインディング</SelectedValueBinding>
    <SelectionMode>ListBoxで選択されている項目のメソッド</SelectionMode>
    <Sorted>ListBox内の項目がアルファベット順にソートされているかどうかを示す値</Sorted>
  </StiListBoxControl>
  <StiListViewControl>
    <SelectedIndexChangedEvent>SelectedIndexプロパティが変更されたときに発生</SelectedIndexChangedEvent>
  </StiListViewControl>
  <StiLookUpBoxControl>
    <Keys>キーのコレクションを表すオブジェクト</Keys>
    <KeysBinding>キーのデータバインディング</KeysBinding>
    <SelectedKeyBinding>選択したキーのデータバインディング</SelectedKeyBinding>
  </StiLookUpBoxControl>
  <StiMargin>
    <Bottom>値は、要素の下端からマージンを設定します。</Bottom>
    <Left>値は、要素の左端からマージンを設定します。</Left>
    <Right>値は、要素の右端からマージンを設定します。</Right>
    <Top>値は、要素の上端からマージンを設定します。</Top>
  </StiMargin>
  <StiMarker>
    <Angle>マーカーの回転角度</Angle>
    <BorderColor>マーカーの境界線の色</BorderColor>
    <Brush>マーカー領域を塗りつぶすブラシ</Brush>
    <ShowInLegend>マーカーが凡例マーカーに表示されることを示す値</ShowInLegend>
    <Size>マーカーのサイズ</Size>
    <Step>ラインマーカーのステップ</Step>
    <Type>マーカーのタイプ</Type>
    <Visible>マーカーの可視性</Visible>
  </StiMarker>
  <StiMsiBarCodeType>
    <CheckSum1>CheckSum1のモードを取得または設定</CheckSum1>
    <CheckSum2>CheckSum2のモードを取得または設定</CheckSum2>
    <Height>バーコードの高さを設定</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定</Module>
  </StiMsiBarCodeType>
  <StiNumericUpDownControl>
    <Increment>上または下のボタンをクリックしたときのアップダウン制御を増減する値</Increment>
    <Maximum>アップダウン制御の最大値</Maximum>
    <MaximumBinding>最大限のデータバインディング</MaximumBinding>
    <Minimum>アップダウン制御の許容最小値</Minimum>
    <MinimumBinding>最小限のデータバインディング</MinimumBinding>
    <Value>アップダウン制御に割り当てられた値</Value>
    <ValueBinding>値のデータバインディング</ValueBinding>
    <ValueChangedEvent>イベントValueChangedのスクリプト</ValueChangedEvent>
  </StiNumericUpDownControl>
  <StiOutsidePieLabels>
    <LineLength>系列ラベルの境界と円の境界線の間の線の長さ</LineLength>
  </StiOutsidePieLabels>
  <StiPadding>
    <Bottom>この値は、下の境界線とそのコンテンツから要素内のパディングを設定します。</Bottom>
    <Left>値は、左境界線とそのコンテンツから要素内のパディングを設定します。</Left>
    <Right>値は、右境界線とそのコンテンツから要素内のパディングを設定します。</Right>
    <Top>この値は、上部の境界線とそのコンテンツから要素内のパディングを設定します。</Top>
  </StiPadding>
  <StiPage>
    <BeginRenderEvent>ページのレンダリングが開始されたときに発生</BeginRenderEvent>
    <ColumnBeginRenderEvent>列のレンダリングを開始するときに発生</ColumnBeginRenderEvent>
    <ColumnEndRenderEvent>列のレンダリングが終了したときに発生</ColumnEndRenderEvent>
    <EndRenderEvent>ページのレンダリングが終了したときに発生</EndRenderEvent>
    <ExcelSheet>Excelシートの生成名に使用される式</ExcelSheet>
    <GetExcelSheetEvent>ExcelSheetが計算されると発生</GetExcelSheetEvent>
    <LargeHeight>このページの高さがデザイナーで大きいことを示す値</LargeHeight>
    <LargeHeightFactor>このページのLargeHeightプロパティの大きな高さ係数</LargeHeightFactor>
    <Margins>ページの余白</Margins>
    <MirrorMargins>ページが奇数か偶数かに応じてページの余白を反映する値</MirrorMargins>
    <NumberOfCopies>現在のページのコピー数の値</NumberOfCopies>
    <Orientation>ページオリエンテーション</Orientation>
    <PageHeight>ページの高さの合計</PageHeight>
    <PageWidth>ページの合計幅</PageWidth>
    <PaperSize>ページサイズ</PaperSize>
    <PaperSourceOfFirstPage>最初のページの給紙元。一部のプリンタはこの機能をサポートしていません。</PaperSourceOfFirstPage>
    <PaperSourceOfOtherPages>最初のページの給紙元。一部のプリンタはこの機能をサポートしていません。</PaperSourceOfOtherPages>
    <PrintHeadersFootersFromPreviousPage>このページで、前のページのヘッダーとフッターを印刷する必要があることを示す値</PrintHeadersFootersFromPreviousPage>
    <PrintOnPreviousPage>ページが前のページの空き領域にレンダリングされ始めることを示す値</PrintOnPreviousPage>
    <RenderingEvent>ページレンダリング時に発生</RenderingEvent>
    <ResetPageNumber>このページのページ番号をリセット</ResetPageNumber>
    <SegmentPerHeight>高さあたりのセグメント数</SegmentPerHeight>
    <SegmentPerWidth>幅あたりのセグメント数</SegmentPerWidth>
    <StopBeforePrint>ページ番号。それに達すると、レンダリングを停止します。プロパティが0の場合、レポートのレンダリングは停止します。</StopBeforePrint>
    <StretchToPrintArea>印刷時にページが印刷領域に引き伸ばされることを示す値</StretchToPrintArea>
    <TitleBeforeHeader>ページタイトルの前にレポートタイトルを配置する必要があることを示す値</TitleBeforeHeader>
    <UnlimitedBreakable>複数のクロスレポートを印刷するときに、列と文字列が壊れることを示す値</UnlimitedBreakable>
    <UnlimitedHeight>ページの高さが無制限であることを示す値</UnlimitedHeight>
    <UnlimitedWidth>ページの幅が無制限であることを示す値</UnlimitedWidth>
    <Watermark>ページのウォーターマーク</Watermark>
  </StiPage>
  <StiPanel>
    <ColumnGaps>2つの列間の距離</ColumnGaps>
    <Columns>列の数</Columns>
    <ColumnWidth>列の幅</ColumnWidth>
    <RightToLeft>水平方向の列方向</RightToLeft>
  </StiPanel>
  <StiPanelControl>
    <BorderStyle>コントロールの境界線のスタイルを表示</BorderStyle>
  </StiPanelControl>
  <StiPdf417BarCodeType>
    <AspectRatio>バーコードの横と縦の縦横比を設定する値を取得または設定</AspectRatio>
    <AutoDataColumns>列の量が自動的に計算されることを示す値を取得または設定</AutoDataColumns>
    <AutoDataRows>行の量が自動的に計算されることを示す値を取得または設定</AutoDataRows>
    <DataColumns>データ列の量を取得または設定</DataColumns>
    <DataRows>データ行のアンラウンドを取得または設定</DataRows>
    <EncodingMode>エンコーディングタイプのタイプを取得または設定</EncodingMode>
    <ErrorsCorrectionLevel>エラー修正レベルを取得または設定　より高いレベルは、復元するためにバーコードに追加される情報が多くなることです。</ErrorsCorrectionLevel>
    <Height>バーコードの高さを設定</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定</Module>
    <RatioY>バーコードの垂直比率を取得または設定　値は2〜5でなければなりません。</RatioY>
  </StiPdf417BarCodeType>
  <StiPharmacodeBarCodeType>
    <Height>バーコードの高さを設定</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定</Module>
  </StiPharmacodeBarCodeType>
  <StiPictureBoxControl>
    <BorderStyle>コントロールの境界線のスタイルを表示</BorderStyle>
    <Image>PictureBoxが表示する画像</Image>
    <SizeMode>イメージのディスプレイ方法を表示</SizeMode>
    <TransparentColor>画像の透明色</TransparentColor>
  </StiPictureBoxControl>
  <StiPieSeries>
    <AllowApplyBorderColor>系列設定の枠線の色を使用できる値</AllowApplyBorderColor>
    <AllowApplyBrush>系列設定のブラシを使用できる値</AllowApplyBrush>
    <CutPieList>カットされた円グラフのリストを埋めるための式。例：1; 4; 6 </CutPieList>
    <Diameter>円シリーズの直径の固定サイズ</Diameter>
    <Distance>シリーズの中心と各セグメントの中心との距離</Distance>
    <GetCutPieListEvent>切断された円のリストを取得するときに発生</GetCutPieListEvent>
    <StartAngle>シリーズの回転角度</StartAngle>
  </StiPieSeries>
  <StiPlesseyBarCodeType>
    <CheckSum1>CheckSum1のモードを取得または設定</CheckSum1>
    <CheckSum2>CheckSum2のモードを取得または設定</CheckSum2>
    <Height>バーコードの高さを設定</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定</Module>
  </StiPlesseyBarCodeType>
  <StiPostnetBarCodeType>
    <Height>バーコードの高さを設定</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定</Module>
    <Space>バーコード要素間の間隔を取得または設定</Space>
  </StiPostnetBarCodeType>
  <StiPrinterSettings>
    <Collate>印刷の丁合いモードが使用されるかどうかを示す値</Collate>
    <Copies>印刷するレポートのコピー数</Copies>
    <Duplex>両面印刷のモード</Duplex>
    <PrinterName>レポート印刷に使用されるプリンタの名前</PrinterName>
    <ShowDialog>印刷ダイアログが表示されるかどうかを示す値</ShowDialog>
  </StiPrinterSettings>
  <StiQRCodeBarCodeType>
    <ErrorCorrectionLevel>エラー修正レベルを取得または設定</ErrorCorrectionLevel>
    <Height>バーコードの高さを設定</Height>
    <MatrixSize>マトリックスサイズを取得または設定</MatrixSize>
    <Module>バーコードの最も細かい要素の幅を取得または設定</Module>
  </StiQRCodeBarCodeType>
  <StiRadarAxis>
    <Visible>軸の可視性</Visible>
  </StiRadarAxis>
  <StiRadarAxisLabels>
    <Brush>ラベル領域を塗りつぶすブラシ</Brush>
    <DrawBorder>ラベルの境界線が表示されることを示す値</DrawBorder>
  </StiRadarAxisLabels>
  <StiRadarGridLines>
    <Color>メジャーグリッド線の描画に使用される色</Color>
    <MinorColor>細いグリッド線の描画に使用される色</MinorColor>
    <MinorCount>各メジャーグリッド線ごとのマイナーグリッド線の数</MinorCount>
    <MinorStyle>小さなグリッド線の描画に使用されるスタイル</MinorStyle>
    <MinorVisible>マイナーグリッド線の可視性</MinorVisible>
    <Style>主要なグリッド線の描画に使用されるスタイル</Style>
    <Visible>主要なグリッド線の可視性</Visible>
  </StiRadarGridLines>
  <StiRadioButtonControl>
    <Checked>コントロールがチェックされているかどうかを示す値</Checked>
    <CheckedBinding>チェックされたデータバインディング</CheckedBinding>
    <CheckedChangedEvent>取得</CheckedChangedEvent>
    <Text>このコントロールに関連付けられたテキスト</Text>
    <TextBinding>テキストのデータバインディング</TextBinding>
  </StiRadioButtonControl>
  <StiRectanglePrimitive>
    <BottomSide>四角形の下端を描画するか否かを示すプロパティ</BottomSide>
    <LeftSide>四角形の左端を描画するかどうかを示すプロパティ</LeftSide>
    <RightSide>四角形のフライ側または無しを描画することを示すプロパティ</RightSide>
    <TopSide>四角形の上端または下端を描画することを示すプロパティ</TopSide>
  </StiRectanglePrimitive>
  <StiReport>
    <AutoLocalizeReportOnRun>実行開始時にレポートの自動ローカライズを可能にするプロパティ</AutoLocalizeReportOnRun>
    <BeginRenderEvent>レポートのレンダリングが開始されたときに発生</BeginRenderEvent>
    <CacheAllData>1つのデータセットにレポートのすべてのデータをキャッシュする必要があるかどうかを示す値</CacheAllData>
    <CacheTotals>このプロパティには、Totalsプレフィックスとともに使用されるレポートの合計がキャッシュされます。このプロパティは、1つのバンドで同じ入力パラメータを持つ複数の合計を使用する場合にレポートのレンダリングを高速化します。</CacheTotals>
    <CalculationMode>レポート・レンダリングでの式計算の方法</CalculationMode>
    <Collate>ページの照合に使用できる値。 プロパティの値は1未満にはできません。</Collate>
    <ConvertNulls>nullまたはDBNullを変換する必要があるかどうかを示す値</ConvertNulls>
    <EndRenderEvent>レポートのレンダリングが完了したときに発生</EndRenderEvent>
    <EngineVersion>現在のレポートエンジン</EngineVersion>
    <GlobalizationStrings>グローバリゼーション文字列で構成されるコレクション</GlobalizationStrings>
    <NumberOfPass>レポート生成時にレポートジェネレータが作成するパスの数</NumberOfPass>
    <ParametersOrientation>パラメータのパネル方向を示す値</ParametersOrientation>
    <PreviewMode>レポートのプレビューモード</PreviewMode>
    <PreviewSettings>プレビューウィンドウに表示されるコントロール</PreviewSettings>
    <PrinterSettings>プリンタ設定</PrinterSettings>
    <ReferencedAssemblies>参照されるアセンブリの配列</ReferencedAssemblies>
    <RenderingEvent>ページがレンダリングされたときに発生</RenderingEvent>
    <ReportAlias>レポートエイリアス</ReportAlias>
    <ReportAuthor>レポート作成者</ReportAuthor>
    <ReportCacheMode>レポートエンジンがレポートキャッシュをどのように使用するかを示す値</ReportCacheMode>
    <ReportDescription>レポートの説明</ReportDescription>
    <ReportName>レポート名</ReportName>
    <ReportUnit>レポートの現在の単位</ReportUnit>
    <RequestParameters>ユーザーがレポートをレンダリングする前にパラメータを要求するか、デフォルト値のパラメータを使用してレポートをレンダリングするかを示す値を取得または設定します。</RequestParameters>
    <ScriptLanguage>現在のスクリプト言語</ScriptLanguage>
    <StopBeforePage>ページ番号　このページに達すると、レポートのレンダリングが停止</StopBeforePage>
    <StoreImagesInResources>レポートのイメージがアセンブリリソースとして格納されることを示す値</StoreImagesInResources>
    <Styles>スタイルで構成されるコレクション</Styles>
  </StiReport>
  <StiReportControl>
    <BackColor>コントロールの背景色</BackColor>
    <ClickEvent>Clickイベントのスクリプト</ClickEvent>
    <DataBindings>コントロールのデータバインディング</DataBindings>
    <DoubleClickEvent>DoubleClickイベントのスクリプト</DoubleClickEvent>
    <Enabled>コントロールがユーザーの操作に応答できるかどうかを示す値</Enabled>
    <EnterEvent>Enterイベントのスクリプト</EnterEvent>
    <Font>コントロールによって表示されるテキストのフォント</Font>
    <ForeColor>コントロールの前景色</ForeColor>
    <GetTagEvent>GetTagイベントのスクリプト</GetTagEvent>
    <GetToolTipEvent>GetToolTipイベントのスクリプト</GetToolTipEvent>
    <LeaveEvent>Leaveイベントのスクリプト</LeaveEvent>
    <Location>コンテナの左上隅を基準にしたコントロールの左上隅の座標</Location>
    <MouseDownEvent>MouseDownイベントのスクリプト</MouseDownEvent>
    <MouseEnterEvent>MouseEnterイベントのスクリプト</MouseEnterEvent>
    <MouseLeaveEvent>MouseLeaveイベントのスクリプト</MouseLeaveEvent>
    <MouseMoveEvent>MouseMoveイベントのスクリプト</MouseMoveEvent>
    <MouseUpEvent>MouseUpイベントのスクリプト</MouseUpEvent>
    <RightToLeft>コントロールの要素が右から左のフォントを使用してロケールをサポートするように整列されているかどうかを示す値</RightToLeft>
    <Size>コントロールの高さと幅</Size>
    <TagValueBinding>タグ値のデータバインディング</TagValueBinding>
    <Visible>コントロールが表示されているかどうかを示す値</Visible>
  </StiReportControl>
  <StiReportSummaryBand>
    <KeepReportSummaryTogether>レポートサマリーにデータが印刷されていることを示す値</KeepReportSummaryTogether>
    <PrintIfEmpty>フッターがページの下部に印刷されていることを示す値</PrintIfEmpty>
  </StiReportSummaryBand>
  <StiReportTitleBand>
    <PrintIfEmpty>データが存在しない場合は、レポートタイトルバンドが印刷されることを示す値</PrintIfEmpty>
  </StiReportTitleBand>
  <StiRichText>
    <BackColor>バックカラー</BackColor>
    <DataColumn>RTFテキストを含む列の名前</DataColumn>
    <DataUrl>コンポーネントのRTFテキストを満たす式</DataUrl>
    <DetectUrls>URLの検出</DetectUrls>
    <FullConvertExpression>式をRTF形式に完全に変換する必要があることを示す値　式を完全に変換すると、レポートのレンダリングが遅くなります。</FullConvertExpression>
    <Margins>テキストマージン</Margins>
    <WordWrap>ワードラップ</WordWrap>
    <Wysiwyg>レンダリングのWysiwygモードを使用する必要があることを示す値</Wysiwyg>
  </StiRichText>
  <StiRichTextBoxControl>
    <Text>リッチテキストボックス内の現在のテキスト</Text>
  </StiRichTextBoxControl>
  <StiRoundedRectanglePrimitive>
    <Round>丸め係数</Round>
  </StiRoundedRectanglePrimitive>
  <StiRoundedRectangleShapeType>
    <Round>丸め係数</Round>
  </StiRoundedRectangleShapeType>
  <StiRoyalMail4StateBarCodeType>
    <CheckSum>チェックサムのモードを取得または設定</CheckSum>
    <Height>バーコードの高さを設定</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定</Module>
  </StiRoyalMail4StateBarCodeType>
  <StiSeries>
    <AllowApplyBrushNegative>負の値を埋め込むための特定のブラシが使用されることを示す値</AllowApplyBrushNegative>
    <AllowApplyColorNegative>負の値の特定の色が使用されることを示す値</AllowApplyColorNegative>
    <Argument>引数式　例：{Order.Argument} </Argument>
    <ArgumentDataColumn>引数の値を含む列の名前</ArgumentDataColumn>
    <AutoSeriesColorDataColumn>自動作成シリーズの色を含む列の名前。色は文字列で指定する必要があります。</AutoSeriesColorDataColumn>
    <AutoSeriesKeyDataColumn>自動作成シリーズのキーを含む列の名前</AutoSeriesKeyDataColumn>
    <AutoSeriesTitleDataColumn>自動作成シリーズのタイトルを含む列の名前</AutoSeriesTitleDataColumn>
    <BorderColor>シリーズの境界線色</BorderColor>
    <Brush>シリーズを塗りつぶすブラシ</Brush>
    <BrushNegative>負の値を塗りつぶすブラシ</BrushNegative>
    <Conditions>系列の振る舞いを変更するための条件の集合</Conditions>
    <FilterMode>系列のフィルタモード</FilterMode>
    <Filters>系列値をフィルタリングするために使用されるフィルタ</Filters>
    <Format>系列値の書式設定に使用される文字列</Format>
    <GetArgumentEvent>プロパティの引数を取得するときに発生</GetArgumentEvent>
    <GetHyperlinkEvent>シリーズのハイパーリンクを取得するときに発生</GetHyperlinkEvent>
    <GetListOfArgumentsEvent>引数のリストを取得するときに発生</GetListOfArgumentsEvent>
    <GetListOfHyperlinksEvent>ハイパーリンクのリストを取得するときに発生</GetListOfHyperlinksEvent>
    <GetListOfTagsEvent>タグのリストを取得するときに発生</GetListOfTagsEvent>
    <GetListOfToolTipsEvent>ツールヒントのリストを取得するときに発生</GetListOfToolTipsEvent>
    <GetListOfValuesEvent>値のリストを取得するときに発生</GetListOfValuesEvent>
    <GetListOfWeightsEvent>重みのリストを取得するときに発生</GetListOfWeightsEvent>
    <GetTagEvent>系列のタグを取得するときに発生</GetTagEvent>
    <GetTitleEvent>プロパティTitleを取得するときに発生</GetTitleEvent>
    <GetToolTipEvent>シリーズのツールチップを取得するときに発生</GetToolTipEvent>
    <GetValueEvent>プロパティValueを取得するときに発生</GetValueEvent>
    <GetWeightEvent>プロパティWeightを取得するときに発生</GetWeightEvent>
    <Interaction>このコンポーネントの対話オプション</Interaction>
    <LabelsOffset>垂直ラベルのオフセット</LabelsOffset>
    <Lighting>ライト効果が表示されることを示す値</Lighting>
    <LineColor>系列の線色</LineColor>
    <LineColorNegative>負の値の系列の線色</LineColorNegative>
    <LineMarker>ラインマーカー設定</LineMarker>
    <LineStyle>系列の線スタイル</LineStyle>
    <LineWidth>系列の線幅</LineWidth>
    <ListOfArguments>引数のリストを埋めるための式。例：1; 2; 3 </ListOfArguments>
    <ListOfValues>値のリストを満たす式。例：1; 2; 3 </ListOfValues>
    <ListOfWeights>値のリストを満たす式。例：1; 2; 3 </ListOfWeights>
    <Marker>マーカー設定</Marker>
    <NewAutoSeriesEvent>新しいautoシリーズが作成されたときに発生</NewAutoSeriesEvent>
    <PointAtCenter>シリーズの中心を配置する場所を示す値</PointAtCenter>
    <SeriesLabels>シリーズのラベル設定</SeriesLabels>
    <ShowInLegend>系列を凡例で表示する必要があることを示す値</ShowInLegend>
    <ShowNulls>値がnullの場合、series要素を表示する必要があるかどうかを示す値</ShowNulls>
    <ShowSeriesLabels>シリーズラベルの出力モード</ShowSeriesLabels>
    <ShowShadow>影を描画するかどうかを示す値</ShowShadow>
    <ShowZeros>値が0の場合、series要素を表示する必要があるかどうかを示す値</ShowZeros>
    <SortBy>直列値の並べ替えのモード</SortBy>
    <SortDirection>方向の並べ替え</SortDirection>
    <Tension>シリーズの張力係数</Tension>
    <Title>シリーズのタイトル</Title>
    <TopmostLine>行を最上位として表示するかどうかを示す値</TopmostLine>
    <TopN>上位結果を表示するパラメータ</TopN>
    <TrendLine>トレンドラインの設定</TrendLine>
    <Value>ポイント値式。例：{Order.Value} </Value>
    <ValueDataColumn>値を含む列の名前。</ValueDataColumn>
    <Weight>重み値式。例：{Order.Value} </Weight>
    <WeightDataColumn>重み値を含む列の名前</WeightDataColumn>
    <YAxis>引数の文字列表現を出力する系列のY軸</YAxis>
  </StiSeries>
  <StiSeriesInteraction>
    <AllowSeries>Seriesでドリルダウン操作を実行できることを示す値</AllowSeries>
    <AllowSeriesElements>Series Elementsのドリルダウン操作を実行できることを示す値</AllowSeriesElements>
    <DrillDownEnabled>ドリルダウン操作を実行できるかどうかを示す値</DrillDownEnabled>
    <DrillDownPage>ドリルダウン操作のページ</DrillDownPage>
    <DrillDownReport>ドリルダウン操作のレポートへのパス</DrillDownReport>
    <Hyperlink>シリーズのハイパーリンクを埋める式</Hyperlink>
    <HyperlinkDataColumn>ハイパーリンク値を含む列の名前</HyperlinkDataColumn>
    <ListOfHyperlinks>ハイパーリンクのリストを埋めるための式。例：1; 2; 3 </ListOfHyperlinks>
    <ListOfTags>タグのリストを満たす式。例：1; 2; 3 </ListOfTags>
    <ListOfToolTips>ツールヒントのリストを埋める式。例：1; 2; 3 </ListOfToolTips>
    <Tag>シリーズタグを埋めるための式</Tag>
    <TagDataColumn>タグ値を含む列の名前</TagDataColumn>
    <ToolTip>一連のツールヒントを塗りつぶす式</ToolTip>
    <ToolTipDataColumn>ツールヒント値を含む列の名前</ToolTipDataColumn>
  </StiSeriesInteraction>
  <StiSeriesLabels>
    <Angle>テキストの回転角度</Angle>
    <Antialiasing>チャートタイトルのアンチエイリアシング描画モードを制御する値</Antialiasing>
    <AutoRotate>シリーズラベルの自動回転モード描画を有効または無効にする値</AutoRotate>
    <BorderColor>シリーズラベルの境界線色</BorderColor>
    <Brush>シリーズラベルの領域を塗りつぶすために使用されるブラシ</Brush>
    <Conditions>シリーズラベルの動作を変更するために使用できる条件の集合</Conditions>
    <DrawBorder>その枠を描画する値を描画するかどうかを指定</DrawBorder>
    <Font>シリーズラベルを描画するために使用されるフォント</Font>
    <Format>系列値の書式化に使用する書式文字列（該当する場合）</Format>
    <LabelColor>シリーズラベルの前景色</LabelColor>
    <LegendValueType>凡例に表示される情報の種類</LegendValueType>
    <LineColor>線の色</LineColor>
    <LineLength>系列ラベルの境界線と直列要素の境界線の間の線の長さ</LineLength>
    <MarkerAlignment>ラベルテキストに関連するマーカーの配置</MarkerAlignment>
    <MarkerSize>マーカーのサイズ</MarkerSize>
    <MarkerVisible>マーカーの視認性</MarkerVisible>
    <PreventIntersection>系列ラベルの境界と系列の境界との交差を避ける必要があるかどうかを示す値</PreventIntersection>
    <ShowInPercent>系列の値をパーセントで表示する必要があることを示す値</ShowInPercent>
    <ShowNulls>値がnullの場合、系列ラベルを表示する必要があるかどうかを示す値</ShowNulls>
    <ShowValue>系列の値が系列ラベルで表示されることを示す値</ShowValue>
    <ShowZeros>値が0の場合に系列ラベルを表示する必要があるかどうかを示す値</ShowZeros>
    <Step>どのステップでラベルを表示するかを示す値</Step>
    <TextAfter>ラベルテキストの後に表示されるテキスト</TextAfter>
    <TextBefore>ラベルテキストの前に表示されるテキスト</TextBefore>
    <UseSeriesColor>系列の色を使用する必要があることを示す値</UseSeriesColor>
    <ValueType>系列ラベルに表示される情報の種類</ValueType>
    <ValueTypeSeparator>値情報のセパレータを含む文字列（適用されている場合）</ValueTypeSeparator>
    <Visible>シリーズラベルの可視性</Visible>
    <Width>シリーズラベルの固定幅</Width>
    <WordWrap>ワードラップ</WordWrap>
  </StiSeriesLabels>
  <StiSeriesTopN>
    <Count>出力値の数</Count>
    <Mode>出力値モード</Mode>
    <OtherText>他の値のシグネチャ</OtherText>
    <ShowOther>異なる値を表示するかどうかを設定</ShowOther>
  </StiSeriesTopN>
  <StiShape>
    <BorderColor>ボーダーカラー</BorderColor>
    <ShapeType>シェイプのタイプ</ShapeType>
    <Size>枠のサイズ</Size>
    <Style>ペンスタイル</Style>
  </StiShape>
  <StiShapeTypeService>
    <Direction>矢印方向</Direction>
  </StiShapeTypeService>
  <StiSimpleBorder>
    <Color>境界線の色</Color>
    <Side>フレームの境界線</Side>
    <Size>境界線のサイズ</Size>
    <Style>ボーダースタイル</Style>
  </StiSimpleBorder>
  <StiSimpleText>
    <GetValueEvent>テキストがレンダリングの準備中であるときに発生</GetValueEvent>
    <GlobalizedName>グローバリゼーション・マネージャのキーとして使用される値</GlobalizedName>
    <HideZeros>ゼロを表示する必要がないことを示す値</HideZeros>
    <LinesOfUnderline>行に下線が必要であることを示す値</LinesOfUnderline>
    <MaxNumberOfLines>高さの伸びの限界を指定する行の最大数</MaxNumberOfLines>
    <OnlyText>テキスト式にテキストのみが含まれていることを示す値</OnlyText>
    <ProcessAt>ページレンダリングの終了時またはレポートレンダリングの終了時に、このコンポーネントのテキスト式を処理するかどうかを示す値</ProcessAt>
    <ProcessAtEnd>レポートの実行終了時にテキストが処理されることを示す値</ProcessAtEnd>
    <ProcessingDuplicates>レポートエンジンが重複した値をどのように処理するかを示す値</ProcessingDuplicates>
    <Text>テキスト式</Text>
  </StiSimpleText>
  <StiSolidBrush>
    <Color>このブラシの色</Color>
  </StiSolidBrush>
  <StiSSCC18BarCodeType>
    <CompanyPrefix>GS1会社プレフィックス（7〜10桁）を取得または設定</CompanyPrefix>
    <ExtensionDigit>拡張ディジットを取得または設定</ExtensionDigit>
    <Height>バーコードの高さを設定</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定</Module>
    <SerialNumber>シリアル参照番号（6〜9桁）を取得または設定</SerialNumber>
  </StiSSCC18BarCodeType>
  <StiStandard2of5BarCodeType>
    <Height>バーコードの高さを設定</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定</Module>
    <Ratio>WideToNarrow比を示す値を取得または設定</Ratio>
  </StiStandard2of5BarCodeType>
  <StiStrips>
    <Antialiasing>アンチエイリアス描画モードを制御する値</Antialiasing>
    <Font>タイトルテキストのフォント</Font>
    <MaxValue>ストリップの最大範囲</MaxValue>
    <MinValue>ストリップの最小範囲</MinValue>
    <Orientation>ストリップの水平方向または垂直方向</Orientation>
    <ShowBehind>ストリップがチャートシリーズの裏側またはチャートシリーズの前に表示されることを示す値</ShowBehind>
    <ShowInLegend>ストリップがチャートの凡例に表示されることを示す値</ShowInLegend>
    <StripBrush>ストリップの描画に使用されるブラシ</StripBrush>
    <Text>タイトルテキスト</Text>
    <TitleColor>タイトルの前景色</TitleColor>
    <TitleVisible>タイトルの可視性</TitleVisible>
    <Visible>ストリップの可視性</Visible>
  </StiStrips>
  <StiSubReport>
    <KeepSubReportTogether>サブレポートが配置されているデータバンドと一緒に保持する必要があることを示す値</KeepSubReportTogether>
    <SubReportPage>サブレポートを含むページ</SubReportPage>
    <UseExternalReport>このサブレポートに外部レポートが必要であることを示す値</UseExternalReport>
  </StiSubReport>
  <StiTable>
    <AutoWidth>列幅を調整するためにテーブルコンポーネントを使用する範囲</AutoWidth>
    <AutoWidthType>列コンポーネントの幅を調整する方法</AutoWidthType>
    <ColumnCount>テーブル内のいくつかの列</ColumnCount>
    <DockableTable>テーブルが親コンポーネント領域の上部に調整されることを示す値</DockableTable>
    <FooterCanBreak>このフッターがコンテンツを分割できることを示す値</FooterCanBreak>
    <FooterCanGrow>表のフッターがその高さを拡大できることを示す値</FooterCanGrow>
    <FooterCanShrink>このフッターが高さを縮小できることを示す値</FooterCanShrink>
    <FooterPrintAtBottom>ページの一番下に表のフッターが印刷されることを示す値</FooterPrintAtBottom>
    <FooterPrintIfEmpty>表にデータが存在しない場合にフッターが印刷されることを示す値</FooterPrintIfEmpty>
    <FooterPrintOn>表のフッターをページに印刷する方法を示す値</FooterPrintOn>
    <FooterPrintOnAllPages>表のフッターがすべてのページに印刷されることを示す値</FooterPrintOnAllPages>
    <FooterPrintOnEvenOddPages>偶数ページに表のフッターを印刷する方法を示す値</FooterPrintOnEvenOddPages>
    <FooterRowsCount>テーブル内のいくつかのフッター行</FooterRowsCount>
    <HeaderCanBreak>このヘッダーがコンテンツを破損する可能性があることを示す値</HeaderCanBreak>
    <HeaderCanGrow>表のヘッダーがその高さを拡大できることを示す値</HeaderCanGrow>
    <HeaderCanShrink>このヘッダーが高さを縮小できることを示す値</HeaderCanShrink>
    <HeaderPrintAtBottom>ページの下部に表のヘッダーが印刷されることを示す値</HeaderPrintAtBottom>
    <HeaderPrintIfEmpty>表にデータが存在しない場合にヘッダーが印刷されることを示す値</HeaderPrintIfEmpty>
    <HeaderPrintOn>表のヘッダーをページに印刷する方法を示す値</HeaderPrintOn>
    <HeaderPrintOnAllPages>表のヘッダーがすべてのページに印刷されることを示す値</HeaderPrintOnAllPages>
    <HeaderPrintOnEvenOddPages>偶数ページで表のヘッダーをどのように印刷するかを示す値</HeaderPrintOnEvenOddPages>
    <HeaderRowsCount>表内のいくつかのヘッダー行</HeaderRowsCount>
    <RowCount>テーブル内のいくつかの行</RowCount>
  </StiTable>
  <StiTableElement>
    <Columns>テーブルに表示される列</Columns>
    <Group />
    <SizeMode />
    <Style>テーブルスタイル。</Style>
    <Title>テーブルのタイトル</Title>
  </StiTableElement>
  <StiTableOfContents>
    <RightToLeft>水平出力方向を取得または設定します。</RightToLeft>
  </StiTableOfContents>
  <StiText>
    <AllowHtmlTags>このコンポーネントがテキスト内のHTMLタグを許可することを示す値</AllowHtmlTags>
    <Angle>テキスト回転の角度</Angle>
    <AutoWidth>このオブジェクトが自動的に幅を変更できることを示す値</AutoWidth>
    <ExcelValue>Excelにデータをエクスポートするために使用される式。数値のみ。</ExcelValue>
    <ExportAsImage>テキストの内容をイメージまたはテキストとしてどのようにエクスポートするかを示す値</ExportAsImage>
    <Font>コンポーネントのフォント</Font>
    <GetExcelValueEvent>ExcelValueが計算されると発生</GetExcelValueEvent>
    <HorAlignment>テキストの水平方向の配置</HorAlignment>
    <Margins>テキストマージン</Margins>
    <RenderTo>このプロパティでは、現在のTextコンポーネントバインドの外にあるテキストを引き続き出力するTextコンポーネントを指定します。</RenderTo>
    <ShrinkFontToFit>このコンポーネントがコンポーネントの内容に合わせてフォントサイズを下げることを示す値</ShrinkFontToFit>
    <ShrinkFontToFitMinimumSize>ShrinkFontToFit操作の最小フォントサイズを示す値</ShrinkFontToFitMinimumSize>
    <TextFormat>テキストの形式</TextFormat>
    <TextOptions>表示するテキストを制御するオプション</TextOptions>
    <TextQuality>テキスト品質を示す値</TextQuality>
    <WordWrap>ワードラップ</WordWrap>
  </StiText>
  <StiTextBoxControl>
    <AcceptsReturn>複数行TextBoxコントロールでEnterキーを押すと、コントロールに新しいテキスト行を作成するか、フォームの既定のボタンをアクティブにするかどうかを示す値</AcceptsReturn>
    <AcceptsTab>複数行のテキストボックスコントロールでTabキーを押すと、タブ順序で次のコントロールにフォーカスを移動するのではなく、コントロール内のTab文字を入力するかどうかを示す値</AcceptsTab>
    <MaxLength>ユーザーがテキストボックスコントロールに入力できる最大文字数</MaxLength>
    <Multiline>これが複数行テキストボックスコントロールかどうかを示す値</Multiline>
    <PasswordChar>単一行のTextBoxコントロールでパスワードの文字をマスクするために使用される文字</PasswordChar>
    <Text>テキストボックス内の現在のテキスト</Text>
    <TextBinding>テキストのデータバインディング</TextBinding>
    <WordWrap>複数行のテキストボックスコントロールが、必要に応じて単語を次の行の先頭に自動的に折り返すかどうかを示す値</WordWrap>
  </StiTextBoxControl>
  <StiTextInCells>
    <CellHeight>セルの高さ</CellHeight>
    <CellWidth>セルの幅</CellWidth>
    <ContinuousText>連続テキストフラグ</ContinuousText>
    <HorSpacing>セル間の水平間隔</HorSpacing>
    <RightToLeft>水平出力方向</RightToLeft>
    <VertSpacing>セル間の垂直方向の間隔</VertSpacing>
    <WordWrap>ワードラップ</WordWrap>
  </StiTextInCells>
  <StiTextOptions>
    <Angle>テキスト回転の角度</Angle>
    <DistanceBetweenTabs>タブ間の距離</DistanceBetweenTabs>
    <FirstTabOffset>最初のタブオフセット</FirstTabOffset>
    <HotkeyPrefix>描画ホットキーの一種</HotkeyPrefix>
    <LineLimit>補完された行のみを表示する値</LineLimit>
    <RightToLeft>水平出力方向</RightToLeft>
    <Trimming>ラインの終わりをトリミングするタイプ</Trimming>
    <WordWrap>ワードラップ</WordWrap>
  </StiTextOptions>
  <StiTitle>
    <BackColor>タイトルの背景色</BackColor>
    <Font>タイトルのフォント</Font>
    <ForeColor>タイトルの前景色</ForeColor>
    <HorAlignment>テキストの水平方向の配置</HorAlignment>
    <Text>タイトルのテキスト</Text>
    <Visible>タイトルの可視性</Visible>
  </StiTitle>
  <StiTreeViewControl>
    <AfterSelectEvent>AfterSelectプロパティが変更されたときに発生</AfterSelectEvent>
  </StiTreeViewControl>
  <StiTrendLine>
    <LineColor>トレンドラインの描画に使用される色</LineColor>
    <LineStyle>トレンドラインスタイル</LineStyle>
    <LineWidth>トレンド線幅</LineWidth>
    <ShowShadow>影を描画するかどうかを示す値</ShowShadow>
  </StiTrendLine>
  <StiUpcABarCodeType>
    <Height>バーコードの高さを設定</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定</Module>
    <ShowQuietZoneIndicator>Quiet Zone IndicatorまたはNoを示す値を取得または設定</ShowQuietZoneIndicator>
    <SupplementCode>コンポーネント補足バーコードを取得または設定</SupplementCode>
    <SupplementType>補足コードのタイプを取得または設定</SupplementType>
  </StiUpcABarCodeType>
  <StiUpcEBarCodeType>
    <Height>バーコードの高さを設定</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定</Module>
    <SupplementCode>コンポーネント補足バーコードを取得または設定</SupplementCode>
    <SupplementType>補足コードのタイプを取得または設定</SupplementType>
  </StiUpcEBarCodeType>
  <StiUpcSup2BarCodeType>
    <Height>バーコードの高さを設定</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定</Module>
    <ShowQuietZoneIndicator>Quiet Zone IndicatorまたはNoを示す値を取得または設定</ShowQuietZoneIndicator>
  </StiUpcSup2BarCodeType>
  <StiUpcSup5BarCodeType>
    <Height>バーコードの高さを設定</Height>
    <Module>バーコードの最も細かい要素の幅を取得または設定</Module>
    <ShowQuietZoneIndicator>Quiet Zone IndicatorまたはNoを示す値を取得または設定</ShowQuietZoneIndicator>
  </StiUpcSup5BarCodeType>
  <StiVariable>
    <Alias>変数のエイリアス</Alias>
    <Category>変数のカテゴリ</Category>
    <Description>変数の説明</Description>
    <Name>変数の名前</Name>
  </StiVariable>
  <StiVertChartGridLines>
    <Visible>主要なグリッド線の可視性</Visible>
  </StiVertChartGridLines>
  <StiView>
    <AspectRatio>画像がアスペクト比を保存することを示す値</AspectRatio>
    <MultipleFactor>画像サイズを乗算する値</MultipleFactor>
    <Smoothing>画像を描画するスムージングモード</Smoothing>
    <Stretch>イメージがページのサイズと同じサイズになるまで、このコンポーネントがイメージをストレッチすることを示す値</Stretch>
  </StiView>
  <StiWatermark>
    <Angle>ウォーターマークの角度</Angle>
    <AspectRatio>このウォーターマークの画像がアスペクト比を保存することを示す値</AspectRatio>
    <Enabled>ウォーターマークを描画するかどうかを示す値</Enabled>
    <Font>ウォーターマークのフォント</Font>
    <Image>ウォーターマークの画像の値</Image>
    <ImageAlignment>ウォーターマークのイメージアライメント</ImageAlignment>
    <ImageMultipleFactor>画像サイズを掛ける値</ImageMultipleFactor>
    <ImageStretch>このウォーターマークの画像がページ上に引き伸ばされることを示す値</ImageStretch>
    <ImageTiling>ウォーターマークの画像をタイル表示する必要があります。</ImageTiling>
    <ImageTransparency>ウォーターマークの画像の透明度</ImageTransparency>
    <RightToLeft>ウォーターマークの出力方向</RightToLeft>
    <ShowBehind>ウォーターマークをページの後ろまたは前に描画するかどうかを示す値</ShowBehind>
    <ShowImageBehind>ウォーターマークイメージをページの後ろまたは前に描画するかどうかを示す値</ShowImageBehind>
    <Text>ウォーターマークのテキスト</Text>
    <TextBrush>ウォーターマークへのブラシ</TextBrush>
  </StiWatermark>
  <StiWinControl>
    <BackColor>コントロールの背景色</BackColor>
    <Font>コントロールによって表示されるテキストのフォント</Font>
    <ForeColor>コントロールの前景色</ForeColor>
    <Text>このコントロールに関連付けられたテキスト</Text>
    <TypeName>型名</TypeName>
  </StiWinControl>
  <StiXAxis>
    <DateTimeStep>日付タイムステップ設定</DateTimeStep>
  </StiXAxis>
  <StiXChartAxis>
    <ShowEdgeValues>軸の最初と最後の引数がとにかく表示されることを示す値</ShowEdgeValues>
    <StartFromZero>すべての引数がゼロからのショーになることを示す値</StartFromZero>
    <Title>軸のタイトル設定</Title>
  </StiXChartAxis>
  <StiXChartAxisTitle>
    <Direction>軸タイトル描画のテキスト方向</Direction>
  </StiXChartAxisTitle>
  <StiYChartAxis>
    <StartFromZero>すべての引数がゼロからのショーになることを示す値</StartFromZero>
  </StiYChartAxis>
  <StiYChartAxisTitle>
    <Direction>軸タイトル描画のテキスト方向</Direction>
  </StiYChartAxisTitle>
  <StiZipCode>
    <Code>郵便番号のコードを記入する式</Code>
    <ForeColor>前景色</ForeColor>
    <GetZipCodeEvent>郵便番号のコードを取得するときに発生</GetZipCodeEvent>
    <Ratio>幅と高さの比率を示す値</Ratio>
    <Size>輪郭サイズ</Size>
  </StiZipCode>
  <Universal>
    <AllowApplyStyle>値はチャートスタイルが使用されることを示す値</AllowApplyStyle>
    <Key>変数をGUIで選択したときに変数で使用されるキー</Key>
    <Label>GUIでキーの代わりに表示されるラベル。</Label>
    <Value>GUIのキー値の代わりに表示される値</Value>
  </Universal>
</Localization>