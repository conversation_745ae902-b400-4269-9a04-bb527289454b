import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { AssetListPageRoutingModule } from './asset-list-routing.module';

import { AssetListPage } from './asset-list.page';
import { File } from '@awesome-cordova-plugins/file/ngx';
import { ScanService } from 'src/app/services/scan/scan.service';
import { CommonDirectiveModule } from 'src/app/directives/common-directives.module';

@NgModule({
  imports: [CommonModule, FormsModule, IonicModule, AssetListPageRoutingModule, CommonDirectiveModule],
  declarations: [AssetListPage],
  providers: [ScanService, File],
})
export class AssetListPageModule {}
