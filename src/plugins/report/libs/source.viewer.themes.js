
    var Stimulsoft = Stimulsoft || {};
    Stimulsoft.Viewer = Stimulsoft.Viewer || {};
    Stimulsoft.Viewer.Themes = Stimulsoft.System.decompressLiteral({"Office2003":{stylesText:[190,189,188,187,186,185,184,183,182,181,180,179,178,177,176,175,174,173,172,171,170,169,168,167,166,165,164,163,162,161,160,159,158,157,156,155,154,153,152,151,150,149,148,147,146,145,144,143,142,141,140,139,138,137,136,135,134,133,132,131,130,129,128,127,126,125,124,123,122,121,120,119,118,117,116,115,114,113,112,111,110,109,108,107,106,105,104,103,102,101,100,99,98,97,96,95,94,93,92,91,90,89,88,87,86,85,84,83,82,81,80,79,78,77,76,75,74,73,72,71,70,69,68,67,66,65,64,63,62,61,60,59,58,57,56,55,54,53,52,51,50,49,48,47,46,45,"@-webkit-keyframes load8 { 0% { -webkit-transform: rotate(0deg); transform: rotate(0deg); } 100% { -webkit-transform: rotate(360deg); transform: rotate(360deg); }\n}","@keyframes load8 { 0% { -webkit-transform: rotate(0deg); transform: rotate(0deg); } 100% { -webkit-transform: rotate(360deg); transform: rotate(360deg); }\n}",44,43,42,41,40,39,38,37,36,35,34,33,32,31,30,29,28,27,26,25,24,23,22,21,20,19,18,17,16,15,14,13,12,11,10,9,8,7,6,5,4,3,2,1],accents:{"Office2003":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerMainPanelsBorderColor","#a0a0a0"],["@JsViewerMainPanelsBackgroundColor","#f0f0f0"],["@JsViewerMainPanelsBackgroundUpColor","#f0f0f0"],["@JsViewerMainPanelsBackgroundDownColor","#f0f0f0"],["@JsViewerToolbarSeparatorLeftColor","#bdbdbd"],["@JsViewerToolbarSeparatorRightColor","#fdfdfd"],["@JsViewerTooltipBackgroundColor","#fdfdfd"],["@JsViewerButtonOverBackgroundColor","#c0ddfc"],["@JsViewerButtonOverBackgroundUpColor","#c0ddfc"],["@JsViewerButtonOverBackgroundDownColor","#c0ddfc"],["@JsViewerButtonSelectedBackgroundColor","#e4eef8"],["@JsViewerButtonSelectedBackgroundUpColor","#e4eef8"],["@JsViewerButtonSelectedBackgroundDownColor","#e4eef8"],["@JsViewerButtonDisabledBackgroundUpColor","transparent"],["@JsViewerButtonDisabledBackgroundDownColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#3399ff"],["@JsViewerButtonSelectedBorderColor","#3399ff"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerMenuItemOverBackgroundUpColor","#c0ddfc"],["@JsViewerMenuItemOverBackgroundDownColor","#c0ddfc"],["@JsViewerMenuItemSelectedBackgroundUpColor","#c0ddfc"],["@JsViewerMenuItemSelectedBackgroundDownColor","#c0ddfc"],["@JsViewerMenuItemDisabledBackgroundUpColor","transparent"],["@JsViewerMenuItemDisabledBackgroundDownColor","transparent"],["@JsViewerMenuItemBorderColor","transparent"],["@JsViewerMenuItemOverBorderColor","#3399ff"],["@JsViewerMenuItemSelectedBorderColor","#3399ff"],["@JsViewerMenuItemDisabledBorderColor","transparent"],["@JsViewerMenuSeparatorUpColor","#bdbdbd"],["@JsViewerMenuSeparatorDownColor","#ffffff"],["@JsViewerMenuBorderColor","#969696"],["@JsViewerMenuBackgroundColor","#f0f0f0"],["@JsViewerMenuIconsBackgroundColor","#e8e8e8"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#d5e1f2"],["@JsViewerControlsFontSelectedColor","#c2d5f2"],["@JsViewerControlsFontDisabledColor","#a6a3a6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#ffffff"],["@JsViewerControlsSelectedBackgroundColor","#ffffff"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#3399ff"],["@JsViewerControlsSelectedBorderColor","#3399ff"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerHyperlinkButtonFontColor","#5a83ad"],["@JsViewerGroupPanelBorderColor","#d3d3d3"],["@JsViewerGroupPanelBackgroundColor","#fdfdfd"],["@JsViewerGroupPanelCaptionBorderColor","#d3d3d3"],["@JsViewerGroupPanelCaptionBackgroundColor","#fdfdfd"],["@JsViewerFormBorderColor","#a0a0a0"],["@JsViewerFormBackgroundColor","#f0f0f0"],["@JsViewerFormContainerBackgroundColor","#f0f0f0"],["@JsViewerFormHeaderBackgroundColor","#f0f0f0"],["@JsViewerFormHeaderFontColor","#404040"],["@JsViewerFormSeparatorColor","#d3d3d3"],["@JsViewerNavigatePanelBackgroundColor","#f0f0f0"],["@JsViewerNavigatePanelSeparatorColor","#bdbdbd"],["@JsViewerNavigateButtonOverBackgroundColor","#c0ddfc"],["@JsViewerNavigateButtonSelectedBackgroundColor","#e4eef8"]]}},"Office2007":{stylesText:[190,189,237,187,186,185,184,183,182,181,180,179,178,177,176,175,174,236,235,234,233,232,168,167,166,165,164,163,162,161,160,159,158,157,156,155,154,153,152,151,150,149,148,147,231,145,230,229,142,228,227,226,225,137,136,135,224,133,132,131,130,129,128,127,223,222,124,123,122,121,120,119,221,117,220,219,114,218,112,217,216,109,108,107,106,105,104,103,102,215,214,213,212,97,96,95,94,93,92,91,90,89,88,87,86,85,211,83,82,81,80,79,78,77,76,75,74,73,210,71,70,209,208,67,66,65,64,63,62,61,207,206,58,205,56,55,54,53,52,204,203,202,48,47,46,45,"@-webkit-keyframes load8 { 0% { -webkit-transform: rotate(0deg); transform: rotate(0deg); } 100% { -webkit-transform: rotate(360deg); transform: rotate(360deg); }\n}","@keyframes load8 { 0% { -webkit-transform: rotate(0deg); transform: rotate(0deg); } 100% { -webkit-transform: rotate(360deg); transform: rotate(360deg); }\n}",44,43,42,41,40,39,38,201,36,35,34,200,32,199,198,29,28,27,26,25,24,197,22,21,20,19,18,17,196,15,14,13,12,11,195,9,194,7,6,193,192,191,2,1],accents:{"Office2007Silver":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerMainPanelsBorderColor","#bebfc0"],["@JsViewerMainPanelsBackgroundColor","#e2eaf0"],["@JsViewerMainPanelsBackgroundUpColor","#d5dbe7"],["@JsViewerMainPanelsBackgroundDownColor","#f0f9fa"],["@JsViewerToolbarSeparatorLeftColor","#96989a"],["@JsViewerToolbarSeparatorRightColor","#eceef2"],["@JsViewerTooltipBackgroundColor","#fafafa"],["@JsViewerButtonOverBackgroundColor","#ffd857"],["@JsViewerButtonOverBackgroundUpColor","#fffad3"],["@JsViewerButtonOverBackgroundMiddleColor","#ffe577"],["@JsViewerButtonOverBackgroundDownColor","#ffd343"],["@JsViewerButtonSelectedBackgroundColor","#fff6c2"],["@JsViewerButtonSelectedBackgroundUpColor","#fff6c2"],["@JsViewerButtonSelectedBackgroundDownColor","#fff6c2"],["@JsViewerButtonDisabledBackgroundUpColor","transparent"],["@JsViewerButtonDisabledBackgroundDownColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#cdb886"],["@JsViewerButtonSelectedBorderColor","#cdb886"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerMenuItemOverBackgroundUpColor","#fffffb"],["@JsViewerMenuItemOverBackgroundMiddleColor","#ffe6a1"],["@JsViewerMenuItemOverBackgroundDownColor","#ffd770"],["@JsViewerMenuItemSelectedBackgroundUpColor","#fff6c2"],["@JsViewerMenuItemSelectedBackgroundDownColor","#fff6c2"],["@JsViewerMenuItemDisabledBackgroundUpColor","transparent"],["@JsViewerMenuItemDisabledBackgroundDownColor","transparent"],["@JsViewerMenuItemBorderColor","transparent"],["@JsViewerMenuItemOverBorderColor","#dbce99"],["@JsViewerMenuItemSelectedBorderColor","#dbce99"],["@JsViewerMenuItemDisabledBorderColor","transparent"],["@JsViewerMenuSeparatorUpColor","#c5c5c5"],["@JsViewerMenuSeparatorDownColor","#fafafa"],["@JsViewerMenuBorderColor","#868686"],["@JsViewerMenuBackgroundColor","#fafafa"],["@JsViewerMenuIconsBackgroundColor","#e9eeee"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#d5e1f2"],["@JsViewerControlsFontSelectedColor","#c2d5f2"],["@JsViewerControlsFontDisabledColor","#a6a3a6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#fffce8"],["@JsViewerControlsSelectedBackgroundColor","#fffce8"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#ffd343"],["@JsViewerControlsSelectedBorderColor","#ffd343"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerHyperlinkButtonFontColor","#5a83ad"],["@JsViewerLoadingPanelColor","#808080"],["@JsViewerGroupPanelBorderColor","#d3d3d3"],["@JsViewerGroupPanelBackgroundColor","#fafafa"],["@JsViewerGroupPanelCaptionBorderColor","#d3d3d3"],["@JsViewerGroupPanelCaptionBackgroundColor","#fafafa"],["@JsViewerFormBorderColor","#303030"],["@JsViewerFormBackgroundColor","#d7dde9"],["@JsViewerFormContainerBackgroundColor","#e1e9ef"],["@JsViewerFormHeaderBackgroundColor","#d7dde9"],["@JsViewerFormHeaderFontColor","#303030"],["@JsViewerFormSeparatorColor","#d3d3d3"],["@JsViewerNavigatePanelBackgroundColor","#f0f9fa"],["@JsViewerNavigatePanelSeparatorColor","#96989a"],["@JsViewerNavigateButtonOverBackgroundColor","#ffd857"],["@JsViewerNavigateButtonSelectedBackgroundColor","#fff6c2"]],"Office2007Blue":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerMainPanelsBorderColor","#8db2e3"],["@JsViewerMainPanelsBackgroundColor","#cddcee"],["@JsViewerMainPanelsBackgroundUpColor","#c9d9ed"],["@JsViewerMainPanelsBackgroundDownColor","#e1f3fe"],["@JsViewerToolbarSeparatorLeftColor","#a9bfd3"],["@JsViewerToolbarSeparatorRightColor","#e3eaf4"],["@JsViewerTooltipBackgroundColor","#fafafa"],["@JsViewerButtonOverBackgroundColor","#ffd857"],["@JsViewerButtonOverBackgroundUpColor","#fff6c2"],["@JsViewerButtonOverBackgroundMiddleColor","#ffe791"],["@JsViewerButtonOverBackgroundDownColor","#ffd646"],["@JsViewerButtonSelectedBackgroundColor","#fff6c2"],["@JsViewerButtonSelectedBackgroundUpColor","#fff6c2"],["@JsViewerButtonSelectedBackgroundDownColor","#fff6c2"],["@JsViewerButtonDisabledBackgroundUpColor","transparent"],["@JsViewerButtonDisabledBackgroundDownColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#cdb886"],["@JsViewerButtonSelectedBorderColor","#cdb886"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerMenuItemOverBackgroundUpColor","#fffffb"],["@JsViewerMenuItemOverBackgroundMiddleColor","#ffebb4"],["@JsViewerMenuItemOverBackgroundDownColor","#ffd770"],["@JsViewerMenuItemSelectedBackgroundUpColor","#fff6c2"],["@JsViewerMenuItemSelectedBackgroundDownColor","#fff6c2"],["@JsViewerMenuItemDisabledBackgroundUpColor","transparent"],["@JsViewerMenuItemDisabledBackgroundDownColor","transparent"],["@JsViewerMenuItemBorderColor","transparent"],["@JsViewerMenuItemOverBorderColor","#dbce99"],["@JsViewerMenuItemSelectedBorderColor","#dbce99"],["@JsViewerMenuItemDisabledBorderColor","transparent"],["@JsViewerMenuSeparatorUpColor","#c5c5c5"],["@JsViewerMenuSeparatorDownColor","#fafafa"],["@JsViewerMenuBorderColor","#868686"],["@JsViewerMenuBackgroundColor","#fafafa"],["@JsViewerMenuIconsBackgroundColor","#e9eeee"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#d5e1f2"],["@JsViewerControlsFontSelectedColor","#c2d5f2"],["@JsViewerControlsFontDisabledColor","#a6a3a6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#fffce8"],["@JsViewerControlsSelectedBackgroundColor","#fffce8"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#ffd343"],["@JsViewerControlsSelectedBorderColor","#ffd343"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerHyperlinkButtonFontColor","#5a83ad"],["@JsViewerLoadingPanelColor","#5a83ad"],["@JsViewerGroupPanelBorderColor","#d3d3d3"],["@JsViewerGroupPanelBackgroundColor","#fafafa"],["@JsViewerGroupPanelCaptionBorderColor","#d3d3d3"],["@JsViewerGroupPanelCaptionBackgroundColor","#fafafa"],["@JsViewerFormBorderColor","#303050"],["@JsViewerFormBackgroundColor","#d3e4f4"],["@JsViewerFormContainerBackgroundColor","#d6e7f6"],["@JsViewerFormHeaderBackgroundColor","#c9d9ee"],["@JsViewerFormHeaderFontColor","#303050"],["@JsViewerFormSeparatorColor","#d3d3d3"],["@JsViewerNavigatePanelBackgroundColor","#e1f3fe"],["@JsViewerNavigatePanelSeparatorColor","#a9bfd3"],["@JsViewerNavigateButtonOverBackgroundColor","#ffd857"],["@JsViewerNavigateButtonSelectedBackgroundColor","#fff6c2"]],"Office2007Black":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerMainPanelsBorderColor","#000000"],["@JsViewerMainPanelsBackgroundColor","#c5cbd2"],["@JsViewerMainPanelsBackgroundUpColor","#b4bbc5"],["@JsViewerMainPanelsBackgroundDownColor","#e3e9e9"],["@JsViewerToolbarSeparatorLeftColor","#96989a"],["@JsViewerToolbarSeparatorRightColor","#eceef2"],["@JsViewerTooltipBackgroundColor","#fafafa"],["@JsViewerButtonOverBackgroundColor","#ffd857"],["@JsViewerButtonOverBackgroundUpColor","#fffad3"],["@JsViewerButtonOverBackgroundMiddleColor","#ffe577"],["@JsViewerButtonOverBackgroundDownColor","#ffd343"],["@JsViewerButtonSelectedBackgroundColor","#fff6c2"],["@JsViewerButtonSelectedBackgroundUpColor","#fff6c2"],["@JsViewerButtonSelectedBackgroundDownColor","#fff6c2"],["@JsViewerButtonDisabledBackgroundUpColor","transparent"],["@JsViewerButtonDisabledBackgroundDownColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#cdb886"],["@JsViewerButtonSelectedBorderColor","#cdb886"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerMenuItemOverBackgroundUpColor","#fffffb"],["@JsViewerMenuItemOverBackgroundMiddleColor","#ffe6a1"],["@JsViewerMenuItemOverBackgroundDownColor","#ffd770"],["@JsViewerMenuItemSelectedBackgroundUpColor","#fff6c2"],["@JsViewerMenuItemSelectedBackgroundDownColor","#fff6c2"],["@JsViewerMenuItemDisabledBackgroundUpColor","transparent"],["@JsViewerMenuItemDisabledBackgroundDownColor","transparent"],["@JsViewerMenuItemBorderColor","transparent"],["@JsViewerMenuItemOverBorderColor","#dbce99"],["@JsViewerMenuItemSelectedBorderColor","#dbce99"],["@JsViewerMenuItemDisabledBorderColor","transparent"],["@JsViewerMenuSeparatorUpColor","#c5c5c5"],["@JsViewerMenuSeparatorDownColor","#fafafa"],["@JsViewerMenuBorderColor","#868686"],["@JsViewerMenuBackgroundColor","#fafafa"],["@JsViewerMenuIconsBackgroundColor","#e9eeee"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#d5e1f2"],["@JsViewerControlsFontSelectedColor","#c2d5f2"],["@JsViewerControlsFontDisabledColor","#a6a3a6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#fffce8"],["@JsViewerControlsSelectedBackgroundColor","#fffce8"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#ffd343"],["@JsViewerControlsSelectedBorderColor","#ffd343"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerHyperlinkButtonFontColor","#5a83ad"],["@JsViewerLoadingPanelColor","#404040"],["@JsViewerGroupPanelBorderColor","#d3d3d3"],["@JsViewerGroupPanelBackgroundColor","#fafafa"],["@JsViewerGroupPanelCaptionBorderColor","#d3d3d3"],["@JsViewerGroupPanelCaptionBackgroundColor","#fafafa"],["@JsViewerFormBorderColor","#000000"],["@JsViewerFormBackgroundColor","#c8cfd5"],["@JsViewerFormContainerBackgroundColor","#d4dbde"],["@JsViewerFormHeaderBackgroundColor","#c4cad1"],["@JsViewerFormHeaderFontColor","#000000"],["@JsViewerFormSeparatorColor","#bebfc0"],["@JsViewerNavigatePanelBackgroundColor","#e3e9e9"],["@JsViewerNavigatePanelSeparatorColor","#96989a"],["@JsViewerNavigateButtonOverBackgroundColor","#ffd857"],["@JsViewerNavigateButtonSelectedBackgroundColor","#fff6c2"]]}},"Office2010":{stylesText:[190,189,237,187,256,186,185,184,183,182,181,180,179,178,177,176,175,174,236,235,234,233,232,168,167,166,165,164,163,162,161,160,159,158,157,156,155,154,153,152,151,150,149,148,147,231,145,255,143,142,228,227,226,225,137,254,253,224,133,132,131,130,129,128,127,252,251,124,123,122,121,120,119,221,117,250,115,114,218,112,249,110,109,108,107,106,105,104,103,102,215,248,213,247,97,96,95,94,93,92,91,90,89,88,87,86,85,211,83,82,81,80,79,78,77,76,75,74,73,246,71,70,245,244,67,66,65,64,63,62,61,207,206,58,205,56,55,54,53,52,243,242,202,48,47,46,45,"@-webkit-keyframes load8 { 0% { -webkit-transform: rotate(0deg); transform: rotate(0deg); } 100% { -webkit-transform: rotate(360deg); transform: rotate(360deg); }\n}","@keyframes load8 { 0% { -webkit-transform: rotate(0deg); transform: rotate(0deg); } 100% { -webkit-transform: rotate(360deg); transform: rotate(360deg); }\n}",44,43,42,41,40,39,38,201,36,35,34,200,32,241,30,29,28,27,26,25,24,197,22,21,20,19,18,17,196,15,14,13,12,11,240,9,194,7,6,239,238,191,2,1],accents:{"Office2010Silver":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerMainPanelsBorderColor","#b6babf"],["@JsViewerMainPanelsBackgroundColor","#f7f8f9"],["@JsViewerMainPanelsBackgroundUpColor","#fefefe"],["@JsViewerMainPanelsBackgroundDownColor","#eef0f2"],["@JsViewerToolbarSeparatorLeftColor","#f9fafa"],["@JsViewerToolbarSeparatorRightColor","#dfe6ef"],["@JsViewerTooltipBackgroundColor","#ffffff"],["@JsViewerButtonOverBackgroundColor","#ffd86b"],["@JsViewerButtonOverBackgroundUpColor","#ffec79"],["@JsViewerButtonOverBackgroundDownColor","#ffd86b"],["@JsViewerButtonSelectedBackgroundColor","#fff6c2"],["@JsViewerButtonSelectedBackgroundUpColor","#fff6c2"],["@JsViewerButtonSelectedBackgroundDownColor","#fff6c2"],["@JsViewerButtonDisabledBackgroundUpColor","transparent"],["@JsViewerButtonDisabledBackgroundDownColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#c28a30"],["@JsViewerButtonSelectedBorderColor","#c9b482"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerMenuItemOverBackgroundUpColor","#fdfae0"],["@JsViewerMenuItemOverBackgroundDownColor","#fde38a"],["@JsViewerMenuItemSelectedBackgroundUpColor","#fff6c2"],["@JsViewerMenuItemSelectedBackgroundDownColor","#fff6c2"],["@JsViewerMenuItemDisabledBackgroundUpColor","transparent"],["@JsViewerMenuItemDisabledBackgroundDownColor","transparent"],["@JsViewerMenuItemBorderColor","transparent"],["@JsViewerMenuItemOverBorderColor","#f1ca58"],["@JsViewerMenuItemSelectedBorderColor","#ddcf9b"],["@JsViewerMenuItemDisabledBorderColor","transparent"],["@JsViewerMenuSeparatorUpColor","#ffffff"],["@JsViewerMenuSeparatorDownColor","#c7c8c9"],["@JsViewerMenuBorderColor","#b6babf"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuIconsBackgroundColor","#f4f4f4"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#d5e1f2"],["@JsViewerControlsFontSelectedColor","#c2d5f2"],["@JsViewerControlsFontDisabledColor","#a6a3a6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#fdf9d9"],["@JsViewerControlsSelectedBackgroundColor","#fdf9d9"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#b6babf"],["@JsViewerControlsOverBorderColor","#f1ca58"],["@JsViewerControlsSelectedBorderColor","#c28a30"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerHyperlinkButtonFontColor","#5a83ad"],["@JsViewerLoadingPanelColor","#808080"],["@JsViewerGroupPanelBorderColor","#b6babf"],["@JsViewerGroupPanelBackgroundColor","#f7f8f9"],["@JsViewerGroupPanelCaptionBorderColor","#b6babf"],["@JsViewerGroupPanelCaptionBackgroundColor","#f7f8f9"],["@JsViewerFormBorderColor","#b6babf"],["@JsViewerFormBackgroundColor","#eef0f2"],["@JsViewerFormContainerBackgroundColor","#eef0f2"],["@JsViewerFormHeaderBackgroundColor","#eef0f2"],["@JsViewerFormHeaderFontColor","#404040"],["@JsViewerFormSeparatorColor","#a8a8a8"],["@JsViewerNavigatePanelBackgroundColor","#eef0f2"],["@JsViewerNavigatePanelSeparatorColor","#f9fafa"],["@JsViewerNavigateButtonOverBackgroundColor","#ffd86b"],["@JsViewerNavigateButtonSelectedBackgroundColor","#fff6c2"]],"Office2010Blue":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerMainPanelsBorderColor","#a9b9cc"],["@JsViewerMainPanelsBackgroundColor","#e2ebf7"],["@JsViewerMainPanelsBackgroundUpColor","#eef5fd"],["@JsViewerMainPanelsBackgroundDownColor","#d8e4f2"],["@JsViewerToolbarSeparatorLeftColor","#d7e4f2"],["@JsViewerToolbarSeparatorRightColor","#eff6ff"],["@JsViewerTooltipBackgroundColor","#ffffff"],["@JsViewerButtonOverBackgroundColor","#ffd86b"],["@JsViewerButtonOverBackgroundUpColor","#ffec79"],["@JsViewerButtonOverBackgroundDownColor","#ffd86b"],["@JsViewerButtonSelectedBackgroundColor","#fff6c2"],["@JsViewerButtonSelectedBackgroundUpColor","#fff6c2"],["@JsViewerButtonSelectedBackgroundDownColor","#fff6c2"],["@JsViewerButtonDisabledBackgroundUpColor","transparent"],["@JsViewerButtonDisabledBackgroundDownColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#c28a30"],["@JsViewerButtonSelectedBorderColor","#c9b482"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerMenuItemOverBackgroundUpColor","#fdfae0"],["@JsViewerMenuItemOverBackgroundDownColor","#fde38a"],["@JsViewerMenuItemSelectedBackgroundUpColor","#fff6c2"],["@JsViewerMenuItemSelectedBackgroundDownColor","#fff6c2"],["@JsViewerMenuItemDisabledBackgroundUpColor","transparent"],["@JsViewerMenuItemDisabledBackgroundDownColor","transparent"],["@JsViewerMenuItemBorderColor","transparent"],["@JsViewerMenuItemOverBorderColor","#f1ca58"],["@JsViewerMenuItemSelectedBorderColor","#ddcf9b"],["@JsViewerMenuItemDisabledBorderColor","transparent"],["@JsViewerMenuSeparatorUpColor","#ffffff"],["@JsViewerMenuSeparatorDownColor","#c7c8c9"],["@JsViewerMenuBorderColor","#9a9da0"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuIconsBackgroundColor","#f4f4f4"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#d5e1f2"],["@JsViewerControlsFontSelectedColor","#c2d5f2"],["@JsViewerControlsFontDisabledColor","#a6a3a6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#fdf9d9"],["@JsViewerControlsSelectedBackgroundColor","#fdf9d9"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#f1ca58"],["@JsViewerControlsSelectedBorderColor","#c28a30"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerHyperlinkButtonFontColor","#5a83ad"],["@JsViewerLoadingPanelColor","#5a83ad"],["@JsViewerGroupPanelBorderColor","#d3d3d3"],["@JsViewerGroupPanelBackgroundColor","#ffffff"],["@JsViewerGroupPanelCaptionBorderColor","#d3d3d3"],["@JsViewerGroupPanelCaptionBackgroundColor","#ffffff"],["@JsViewerFormBorderColor","#a9b9cc"],["@JsViewerFormBackgroundColor","#d8e4f2"],["@JsViewerFormContainerBackgroundColor","#e6eff9"],["@JsViewerFormHeaderBackgroundColor","#ccdef4"],["@JsViewerFormHeaderFontColor","#303050"],["@JsViewerFormSeparatorColor","#c7c8c9"],["@JsViewerNavigatePanelBackgroundColor","#d8e4f2"],["@JsViewerNavigatePanelSeparatorColor","#d7e4f2"],["@JsViewerNavigateButtonOverBackgroundColor","#ffd86b"],["@JsViewerNavigateButtonSelectedBackgroundColor","#fff6c2"]],"Office2010Black":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerMainPanelsBorderColor","#1c261e"],["@JsViewerMainPanelsBackgroundColor","#c0c0c0"],["@JsViewerMainPanelsBackgroundUpColor","#c8c8c8"],["@JsViewerMainPanelsBackgroundDownColor","#a2a2a2"],["@JsViewerToolbarSeparatorLeftColor","#bfbfbf"],["@JsViewerToolbarSeparatorRightColor","#a8a8a8"],["@JsViewerTooltipBackgroundColor","#ffffff"],["@JsViewerButtonOverBackgroundColor","#ffd86b"],["@JsViewerButtonOverBackgroundUpColor","#ffec79"],["@JsViewerButtonOverBackgroundDownColor","#ffd86b"],["@JsViewerButtonSelectedBackgroundColor","#fff6c2"],["@JsViewerButtonSelectedBackgroundUpColor","#fff6c2"],["@JsViewerButtonSelectedBackgroundDownColor","#fff6c2"],["@JsViewerButtonDisabledBackgroundUpColor","transparent"],["@JsViewerButtonDisabledBackgroundDownColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#c28a30"],["@JsViewerButtonSelectedBorderColor","#c9b482"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerMenuItemOverBackgroundUpColor","#fdfae0"],["@JsViewerMenuItemOverBackgroundDownColor","#fde38a"],["@JsViewerMenuItemSelectedBackgroundUpColor","#fff6c2"],["@JsViewerMenuItemSelectedBackgroundDownColor","#fff6c2"],["@JsViewerMenuItemDisabledBackgroundUpColor","transparent"],["@JsViewerMenuItemDisabledBackgroundDownColor","transparent"],["@JsViewerMenuItemBorderColor","transparent"],["@JsViewerMenuItemOverBorderColor","#f1ca58"],["@JsViewerMenuItemSelectedBorderColor","#ddcf9b"],["@JsViewerMenuItemDisabledBorderColor","transparent"],["@JsViewerMenuSeparatorUpColor","#ffffff"],["@JsViewerMenuSeparatorDownColor","#c7c8c9"],["@JsViewerMenuBorderColor","#1c261e"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuIconsBackgroundColor","#f4f4f4"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#d5e1f2"],["@JsViewerControlsFontSelectedColor","#c2d5f2"],["@JsViewerControlsFontDisabledColor","#868386"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#fdf9d9"],["@JsViewerControlsSelectedBackgroundColor","#fdf9d9"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#1c261e"],["@JsViewerControlsOverBorderColor","#c28a30"],["@JsViewerControlsSelectedBorderColor","#c28a30"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerHyperlinkButtonFontColor","#5a83ad"],["@JsViewerLoadingPanelColor","#404040"],["@JsViewerGroupPanelBorderColor","#a2a2a2"],["@JsViewerGroupPanelBackgroundColor","#e8e8e8"],["@JsViewerGroupPanelCaptionBorderColor","#a2a2a2"],["@JsViewerGroupPanelCaptionBackgroundColor","#e8e8e8"],["@JsViewerFormBorderColor","#1c261e"],["@JsViewerFormBackgroundColor","#c0c0c0"],["@JsViewerFormContainerBackgroundColor","#c8c8c8"],["@JsViewerFormHeaderBackgroundColor","#a2a2a2"],["@JsViewerFormHeaderFontColor","#000000"],["@JsViewerFormSeparatorColor","#a8a8a8"],["@JsViewerNavigatePanelBackgroundColor","#a2a2a2"],["@JsViewerNavigatePanelSeparatorColor","#bfbfbf"],["@JsViewerNavigateButtonOverBackgroundColor","#ffd86b"],["@JsViewerNavigateButtonSelectedBackgroundColor","#fff6c2"]]}},"Office2013":{stylesText:[330,329,328,327,326,325,324,323,322,190,189,321,187,186,185,184,183,182,181,180,179,178,177,176,175,174,173,172,171,170,169,168,167,166,165,164,163,162,161,160,159,158,157,156,155,154,153,152,151,150,149,148,147,146,145,144,320,142,319,318,317,138,137,134,133,132,131,130,129,128,127,316,315,314,313,306,312,311,310,309,308,307,306,305,"@-moz-keyframes spin { 100% { -moz-transform: rotate(180deg); }\n}","@-webkit-keyframes spin { 100% { -webkit-transform: rotate(180deg); }\n}","@keyframes spin { 100% { -webkit-transform: rotate(180deg); transform: rotate(180deg); }\n}",126,125,124,123,122,121,120,119,304,303,302,301,300,299,298,297,296,295,294,107,106,105,104,103,102,293,292,291,290,289,288,95,94,93,92,91,90,89,88,87,86,85,287,83,82,81,80,79,78,77,76,75,74,73,286,285,70,284,283,282,281,65,64,63,62,61,280,59,58,279,56,55,278,53,52,277,276,275,48,274,46,45,"@-webkit-keyframes load8 { 0% { -webkit-transform: rotate(0deg); transform: rotate(0deg); } 100% { -webkit-transform: rotate(360deg); transform: rotate(360deg); }\n}","@keyframes load8 { 0% { -webkit-transform: rotate(0deg); transform: rotate(0deg); } 100% { -webkit-transform: rotate(360deg); transform: rotate(360deg); }\n}",44,43,42,41,40,39,38,273,272,271,270,269,37,36,35,34,33,268,267,266,265,28,27,26,25,24,23,22,21,20,19,18,17,264,16,15,14,13,12,11,263,262,261,260,6,259,258,257,2,1],accents:{"Office2013WhiteViolet":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#eed8ec"],["@JsViewerButtonSelectedBackgroundColor","#d6abd3"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#eed8ec"],["@JsViewerButtonSelectedBorderColor","#d6abd3"],["@JsViewerButtonDisabledBorderColor","#b7b4b7"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#444444"],["@JsViewerControlsFontSelectedColor","#444444"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#eed8ec"],["@JsViewerControlsSelectedBackgroundColor","#d6abd3"],["@JsViewerControlsDisabledBackgroundColor","transparent"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#d6abd3"],["@JsViewerControlsSelectedBorderColor","#d6abd3"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerFormBorderColor","#ababab"],["@JsViewerFormBackgroundColor","#ffffff"],["@JsViewerFormContainerBackgroundColor","#ffffff"],["@JsViewerFormHeaderBackgroundColor","#ffffff"],["@JsViewerFormHeaderFontColor","#9d6b9a"],["@JsViewerFormSeparatorColor","#c8c8c8"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#ffffff"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#fdebfb"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMainPanelsBorderColor","#c6c6c6"],["@JsViewerHyperlinkButtonFontColor","#6b5479"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerToolbarBackgroundColor","#ffffff"],["@JsViewerProgressColorRGB","109,48,105"],["@JsViewerNavigatePanelBackgroundColor","#6d3069"],["@JsViewerNavigatePanelSeparatorColor","#c5acc3"],["@JsViewerNavigateButtonOverBackgroundColor","#834f7f"],["@JsViewerNavigateButtonSelectedBackgroundColor","#4b2148"],["@JsViewerGroupHeaderButtonBackgroundColor","#f3f3f3"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#e3e3e3"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d3d3d3"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]],"Office2013WhiteTeal":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#d3f0ec"],["@JsViewerButtonSelectedBackgroundColor","#7dbbb3"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#d3f0ec"],["@JsViewerButtonSelectedBorderColor","#7dbbb3"],["@JsViewerButtonDisabledBorderColor","#b7b4b7"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#444444"],["@JsViewerControlsFontSelectedColor","#444444"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#d3f0ec"],["@JsViewerControlsSelectedBackgroundColor","#7dbbb3"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#7dbbb3"],["@JsViewerControlsSelectedBorderColor","#7dbbb3"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerFormBorderColor","#ababab"],["@JsViewerFormBackgroundColor","#ffffff"],["@JsViewerFormContainerBackgroundColor","#ffffff"],["@JsViewerFormHeaderBackgroundColor","#ffffff"],["@JsViewerFormHeaderFontColor","#317e74"],["@JsViewerFormSeparatorColor","#c8c8c8"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#ffffff"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#eefdfb"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMainPanelsBorderColor","#c6c6c6"],["@JsViewerHyperlinkButtonFontColor","#4d716c"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerToolbarBackgroundColor","#ffffff"],["@JsViewerProgressColorRGB","35,100,92"],["@JsViewerNavigatePanelBackgroundColor","#23645c"],["@JsViewerNavigatePanelSeparatorColor","#a7c1be"],["@JsViewerNavigateButtonOverBackgroundColor","#447b74"],["@JsViewerNavigateButtonSelectedBackgroundColor","#18453f"],["@JsViewerGroupHeaderButtonBackgroundColor","#f3f3f3"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#e3e3e3"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d3d3d3"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]],"Office2013WhitePurple":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#eadcf2"],["@JsViewerButtonSelectedBackgroundColor","#c3a9d4"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#eadcf2"],["@JsViewerButtonSelectedBorderColor","#c3a9d4"],["@JsViewerButtonDisabledBorderColor","#b7b4b7"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#444444"],["@JsViewerControlsFontSelectedColor","#444444"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#eadcf2"],["@JsViewerControlsSelectedBackgroundColor","#c3a9d4"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#c3a9d4"],["@JsViewerControlsSelectedBorderColor","#c3a9d4"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerFormBorderColor","#ababab"],["@JsViewerFormBackgroundColor","#ffffff"],["@JsViewerFormContainerBackgroundColor","#ffffff"],["@JsViewerFormHeaderBackgroundColor","#ffffff"],["@JsViewerFormHeaderFontColor","#9273a6"],["@JsViewerFormSeparatorColor","#c8c8c8"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#ffffff"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#f9efff"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMainPanelsBorderColor","#c6c6c6"],["@JsViewerHyperlinkButtonFontColor","#755574"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerToolbarBackgroundColor","#ffffff"],["@JsViewerProgressColorRGB","134,83,165"],["@JsViewerNavigatePanelBackgroundColor","#8653a5"],["@JsViewerNavigatePanelSeparatorColor","#cfbadb"],["@JsViewerNavigateButtonOverBackgroundColor","#986db2"],["@JsViewerNavigateButtonSelectedBackgroundColor","#5c3971"],["@JsViewerGroupHeaderButtonBackgroundColor","#f3f3f3"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#e3e3e3"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d3d3d3"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]],"Office2013WhiteOrange":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#fce4dc"],["@JsViewerButtonSelectedBackgroundColor","#f5ba9d"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#fce4dc"],["@JsViewerButtonSelectedBorderColor","#f5ba9d"],["@JsViewerButtonDisabledBorderColor","#b7b4b7"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#444444"],["@JsViewerControlsFontSelectedColor","#444444"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#fce4dc"],["@JsViewerControlsSelectedBackgroundColor","#f5ba9d"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#f5ba9d"],["@JsViewerControlsSelectedBorderColor","#f5ba9d"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerFormBorderColor","#ababab"],["@JsViewerFormBackgroundColor","#ffffff"],["@JsViewerFormContainerBackgroundColor","#ffffff"],["@JsViewerFormHeaderBackgroundColor","#ffffff"],["@JsViewerFormHeaderFontColor","#b8795a"],["@JsViewerFormSeparatorColor","#c8c8c8"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#ffffff"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#fff4f0"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMainPanelsBorderColor","#c6c6c6"],["@JsViewerHyperlinkButtonFontColor","#9d5b4a"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerToolbarBackgroundColor","#ffffff"],["@JsViewerProgressColorRGB","183,58,28"],["@JsViewerNavigatePanelBackgroundColor","#b73a1c"],["@JsViewerNavigatePanelSeparatorColor","#e2b0a4"],["@JsViewerNavigateButtonOverBackgroundColor","#c2573e"],["@JsViewerNavigateButtonSelectedBackgroundColor","#7e2813"]],"Office2013WhiteGreen":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#d4f1e1"],["@JsViewerButtonSelectedBackgroundColor","#86bfa0"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#d4f1e1"],["@JsViewerButtonSelectedBorderColor","#86bfa0"],["@JsViewerButtonDisabledBorderColor","#b7b4b7"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#444444"],["@JsViewerControlsFontSelectedColor","#444444"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#d4f1e1"],["@JsViewerControlsSelectedBackgroundColor","#86bfa0"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#86bfa0"],["@JsViewerControlsSelectedBorderColor","#86bfa0"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerFormBorderColor","#ababab"],["@JsViewerFormBackgroundColor","#ffffff"],["@JsViewerFormContainerBackgroundColor","#ffffff"],["@JsViewerFormHeaderBackgroundColor","#ffffff"],["@JsViewerFormHeaderFontColor","#539672"],["@JsViewerFormSeparatorColor","#c8c8c8"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#ffffff"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#e6fcf0"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMainPanelsBorderColor","#c6c6c6"],["@JsViewerHyperlinkButtonFontColor","#437258"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerToolbarBackgroundColor","#ffffff"],["@JsViewerProgressColorRGB","11,100,51"],["@JsViewerNavigatePanelBackgroundColor","#0b6433"],["@JsViewerNavigatePanelSeparatorColor","#9dc1ad"],["@JsViewerNavigateButtonOverBackgroundColor","#2f7b51"],["@JsViewerNavigateButtonSelectedBackgroundColor","#084523"],["@JsViewerGroupHeaderButtonBackgroundColor","#f3f3f3"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#e3e3e3"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d3d3d3"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]],"Office2013WhiteCarmine":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#fbddde"],["@JsViewerButtonSelectedBackgroundColor","#f7a8ab"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#fbddde"],["@JsViewerButtonSelectedBorderColor","#f7a8ab"],["@JsViewerButtonDisabledBorderColor","#b7b4b7"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#444444"],["@JsViewerControlsFontSelectedColor","#444444"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#fbddde"],["@JsViewerControlsSelectedBackgroundColor","#f7a8ab"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#f7a8ab"],["@JsViewerControlsSelectedBorderColor","#f7a8ab"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerFormBorderColor","#ababab"],["@JsViewerFormBackgroundColor","#ffffff"],["@JsViewerFormContainerBackgroundColor","#ffffff"],["@JsViewerFormHeaderBackgroundColor","#ffffff"],["@JsViewerFormHeaderFontColor","#cf7d80"],["@JsViewerFormSeparatorColor","#c8c8c8"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#ffffff"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#fceff0"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMainPanelsBorderColor","#c6c6c6"],["@JsViewerHyperlinkButtonFontColor","#8a5556"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerToolbarBackgroundColor","#ffffff"],["@JsViewerProgressColorRGB","145,44,47"],["@JsViewerNavigatePanelBackgroundColor","#912c2f"],["@JsViewerNavigatePanelSeparatorColor","#d3abac"],["@JsViewerNavigateButtonOverBackgroundColor","#a14b4e"],["@JsViewerNavigateButtonSelectedBackgroundColor","#641e20"],["@JsViewerGroupHeaderButtonBackgroundColor","#f3f3f3"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#e3e3e3"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d3d3d3"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]],"Office2013WhiteBlue":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#d5e1f2"],["@JsViewerButtonSelectedBackgroundColor","#c2d5f2"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#d5e1f2"],["@JsViewerButtonSelectedBorderColor","#c2d5f2"],["@JsViewerButtonDisabledBorderColor","#b7b4b7"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#d5e1f2"],["@JsViewerControlsFontSelectedColor","#c2d5f2"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#d5e1f2"],["@JsViewerControlsSelectedBackgroundColor","#c2d5f2"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#a3bde3"],["@JsViewerControlsSelectedBorderColor","#a3bde3"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerFormBorderColor","#ababab"],["@JsViewerFormBackgroundColor","#ffffff"],["@JsViewerFormContainerBackgroundColor","#ffffff"],["@JsViewerFormHeaderBackgroundColor","#ffffff"],["@JsViewerFormHeaderFontColor","#587cb1"],["@JsViewerFormSeparatorColor","#c8c8c8"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#ffffff"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#f8f8f8"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMainPanelsBorderColor","#c6c6c6"],["@JsViewerHyperlinkButtonFontColor","#5a83ad"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerToolbarBackgroundColor","#ffffff"],["@JsViewerProgressColorRGB","25,71,138"],["@JsViewerNavigatePanelBackgroundColor","#19478a"],["@JsViewerNavigatePanelSeparatorColor","#a3b5d0"],["@JsViewerNavigateButtonOverBackgroundColor","#3b629b"],["@JsViewerNavigateButtonSelectedBackgroundColor","#11315f"],["@JsViewerGroupHeaderButtonBackgroundColor","#f3f3f3"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#e3e3e3"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d3d3d3"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]],"Office2013VeryDarkGrayViolet":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#eed8ec"],["@JsViewerButtonSelectedBackgroundColor","#d6abd3"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#eed8ec"],["@JsViewerButtonSelectedBorderColor","#d6abd3"],["@JsViewerButtonDisabledBorderColor","#b7b4b7"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#444444"],["@JsViewerControlsFontSelectedColor","#444444"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#eed8ec"],["@JsViewerControlsSelectedBackgroundColor","#ce89c9"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#5e5e5e"],["@JsViewerControlsOverBorderColor","#ce89c9"],["@JsViewerControlsSelectedBorderColor","#ce89c9"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerFormBorderColor","#5e5e5e"],["@JsViewerFormBackgroundColor","#b2b2b2"],["@JsViewerFormContainerBackgroundColor","#b2b2b2"],["@JsViewerFormHeaderBackgroundColor","#b2b2b2"],["@JsViewerFormHeaderFontColor","#3e3e3e"],["@JsViewerFormSeparatorColor","#e0e0e0"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#e8e8e8"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#fdebfb"],["@JsViewerMenuBorderColor","#5e5e5e"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMainPanelsBorderColor","#5e5e5e"],["@JsViewerHyperlinkButtonFontColor","#6b5479"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerToolbarBackgroundColor","#b2b2b2"],["@JsViewerProgressColorRGB","109,48,105"],["@JsViewerNavigatePanelBackgroundColor","#6d3069"],["@JsViewerNavigatePanelSeparatorColor","#c5acc3"],["@JsViewerNavigateButtonOverBackgroundColor","#834f7f"],["@JsViewerNavigateButtonSelectedBackgroundColor","#4b2148"],["@JsViewerGroupHeaderButtonBackgroundColor","#cacaca"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#bcbcbc"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#c0c0c0"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#d0d0d0"]],"Office2013VeryDarkGrayTeal":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#d3f0ec"],["@JsViewerButtonSelectedBackgroundColor","#7dbbb3"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#d3f0ec"],["@JsViewerButtonSelectedBorderColor","#7dbbb3"],["@JsViewerButtonDisabledBorderColor","#b7b4b7"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#444444"],["@JsViewerControlsFontSelectedColor","#444444"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#d3f0ec"],["@JsViewerControlsSelectedBackgroundColor","#4ba99d"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#5e5e5e"],["@JsViewerControlsOverBorderColor","#4ba99d"],["@JsViewerControlsSelectedBorderColor","#4ba99d"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerFormBorderColor","#5e5e5e"],["@JsViewerFormBackgroundColor","#b2b2b2"],["@JsViewerFormContainerBackgroundColor","#b2b2b2"],["@JsViewerFormHeaderBackgroundColor","#b2b2b2"],["@JsViewerFormHeaderFontColor","#3e3e3e"],["@JsViewerFormSeparatorColor","#e0e0e0"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#e8e8e8"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#eefdfb"],["@JsViewerMenuBorderColor","#5e5e5e"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMainPanelsBorderColor","#5e5e5e"],["@JsViewerHyperlinkButtonFontColor","#4d716c"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerToolbarBackgroundColor","#b2b2b2"],["@JsViewerProgressColorRGB","35,100,92"],["@JsViewerNavigatePanelBackgroundColor","#23645c"],["@JsViewerNavigatePanelSeparatorColor","#a7c1be"],["@JsViewerNavigateButtonOverBackgroundColor","#447b74"],["@JsViewerNavigateButtonSelectedBackgroundColor","#18453f"],["@JsViewerGroupHeaderButtonBackgroundColor","#cacaca"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#bcbcbc"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#c0c0c0"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#d0d0d0"]],"Office2013VeryDarkGrayPurple":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#eadcf2"],["@JsViewerButtonSelectedBackgroundColor","#c3a9d4"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#eadcf2"],["@JsViewerButtonSelectedBorderColor","#c3a9d4"],["@JsViewerButtonDisabledBorderColor","#b7b4b7"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#444444"],["@JsViewerControlsFontSelectedColor","#444444"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#eadcf2"],["@JsViewerControlsSelectedBackgroundColor","#a77bc3"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#5e5e5e"],["@JsViewerControlsOverBorderColor","#a77bc3"],["@JsViewerControlsSelectedBorderColor","#a77bc3"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerFormBorderColor","#5e5e5e"],["@JsViewerFormBackgroundColor","#b2b2b2"],["@JsViewerFormContainerBackgroundColor","#b2b2b2"],["@JsViewerFormHeaderBackgroundColor","#b2b2b2"],["@JsViewerFormHeaderFontColor","#3e3e3e"],["@JsViewerFormSeparatorColor","#e0e0e0"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#e8e8e8"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#f9efff"],["@JsViewerMenuBorderColor","#5e5e5e"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMainPanelsBorderColor","#5e5e5e"],["@JsViewerHyperlinkButtonFontColor","#755574"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerToolbarBackgroundColor","#b2b2b2"],["@JsViewerProgressColorRGB","134,83,165"],["@JsViewerNavigatePanelBackgroundColor","#8653a5"],["@JsViewerNavigatePanelSeparatorColor","#cfbadb"],["@JsViewerNavigateButtonOverBackgroundColor","#986db2"],["@JsViewerNavigateButtonSelectedBackgroundColor","#5c3971"],["@JsViewerGroupHeaderButtonBackgroundColor","#cacaca"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#bcbcbc"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#c0c0c0"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#d0d0d0"]],"Office2013VeryDarkGrayOrange":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#fce4dc"],["@JsViewerButtonSelectedBackgroundColor","#f5ba9d"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#fce4dc"],["@JsViewerButtonSelectedBorderColor","#f5ba9d"],["@JsViewerButtonDisabledBorderColor","#b7b4b7"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#444444"],["@JsViewerControlsFontSelectedColor","#444444"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#fce4dc"],["@JsViewerControlsSelectedBackgroundColor","#de936e"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#5e5e5e"],["@JsViewerControlsOverBorderColor","#de936e"],["@JsViewerControlsSelectedBorderColor","#de936e"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerFormBorderColor","#5e5e5e"],["@JsViewerFormBackgroundColor","#b2b2b2"],["@JsViewerFormContainerBackgroundColor","#b2b2b2"],["@JsViewerFormHeaderBackgroundColor","#b2b2b2"],["@JsViewerFormHeaderFontColor","#3e3e3e"],["@JsViewerFormSeparatorColor","#e0e0e0"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#e8e8e8"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#fff4f0"],["@JsViewerMenuBorderColor","#5e5e5e"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMainPanelsBorderColor","#5e5e5e"],["@JsViewerHyperlinkButtonFontColor","#9d5b4a"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerToolbarBackgroundColor","#b2b2b2"],["@JsViewerProgressColorRGB","183,58,28"],["@JsViewerNavigatePanelBackgroundColor","#b73a1c"],["@JsViewerNavigatePanelSeparatorColor","#e2b0a4"],["@JsViewerNavigateButtonOverBackgroundColor","#c2573e"],["@JsViewerNavigateButtonSelectedBackgroundColor","#7e2813"],["@JsViewerGroupHeaderButtonBackgroundColor","#cacaca"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#bcbcbc"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#c0c0c0"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#d0d0d0"]],"Office2013VeryDarkGrayGreen":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#d4f1e1"],["@JsViewerButtonSelectedBackgroundColor","#86bfa0"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#d4f1e1"],["@JsViewerButtonSelectedBorderColor","#86bfa0"],["@JsViewerButtonDisabledBorderColor","#b7b4b7"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#444444"],["@JsViewerControlsFontSelectedColor","#444444"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#d4f1e1"],["@JsViewerControlsSelectedBackgroundColor","#52ad7b"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#5e5e5e"],["@JsViewerControlsOverBorderColor","#52ad7b"],["@JsViewerControlsSelectedBorderColor","#52ad7b"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerFormBorderColor","#5e5e5e"],["@JsViewerFormBackgroundColor","#b2b2b2"],["@JsViewerFormContainerBackgroundColor","#b2b2b2"],["@JsViewerFormHeaderBackgroundColor","#b2b2b2"],["@JsViewerFormHeaderFontColor","#3e3e3e"],["@JsViewerFormSeparatorColor","#e0e0e0"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#e8e8e8"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#e6fcf0"],["@JsViewerMenuBorderColor","#5e5e5e"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMainPanelsBorderColor","#5e5e5e"],["@JsViewerHyperlinkButtonFontColor","#437258"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerToolbarBackgroundColor","#b2b2b2"],["@JsViewerProgressColorRGB","11,100,51"],["@JsViewerNavigatePanelBackgroundColor","#0b6433"],["@JsViewerNavigatePanelSeparatorColor","#9dc1ad"],["@JsViewerNavigateButtonOverBackgroundColor","#2f7b51"],["@JsViewerNavigateButtonSelectedBackgroundColor","#084523"],["@JsViewerGroupHeaderButtonBackgroundColor","#cacaca"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#bcbcbc"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#c0c0c0"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#d0d0d0"]],"Office2013VeryDarkGrayCarmine":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#fbddde"],["@JsViewerButtonSelectedBackgroundColor","#f7a8ab"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#fbddde"],["@JsViewerButtonSelectedBorderColor","#f7a8ab"],["@JsViewerButtonDisabledBorderColor","#b7b4b7"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#444444"],["@JsViewerControlsFontSelectedColor","#444444"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#fbddde"],["@JsViewerControlsSelectedBackgroundColor","#dd797d"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#5e5e5e"],["@JsViewerControlsOverBorderColor","#dd797d"],["@JsViewerControlsSelectedBorderColor","#dd797d"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerFormBorderColor","#5e5e5e"],["@JsViewerFormBackgroundColor","#b2b2b2"],["@JsViewerFormContainerBackgroundColor","#b2b2b2"],["@JsViewerFormHeaderBackgroundColor","#b2b2b2"],["@JsViewerFormHeaderFontColor","#3e3e3e"],["@JsViewerFormSeparatorColor","#e0e0e0"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#e8e8e8"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#fceff0"],["@JsViewerMenuBorderColor","#5e5e5e"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMainPanelsBorderColor","#5e5e5e"],["@JsViewerHyperlinkButtonFontColor","#8a5556"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerToolbarBackgroundColor","#b2b2b2"],["@JsViewerProgressColorRGB","145,44,47"],["@JsViewerNavigatePanelBackgroundColor","#912c2f"],["@JsViewerNavigatePanelSeparatorColor","#d3abac"],["@JsViewerNavigateButtonOverBackgroundColor","#a14b4e"],["@JsViewerNavigateButtonSelectedBackgroundColor","#641e20"],["@JsViewerGroupHeaderButtonBackgroundColor","#cacaca"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#bcbcbc"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#c0c0c0"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#d0d0d0"]],"Office2013VeryDarkGrayBlue":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#d5e1f2"],["@JsViewerButtonSelectedBackgroundColor","#c2d5f2"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#d5e1f2"],["@JsViewerButtonSelectedBorderColor","#c2d5f2"],["@JsViewerButtonDisabledBorderColor","#b7b4b7"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#d5e1f2"],["@JsViewerControlsFontSelectedColor","#c2d5f2"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#d5e1f2"],["@JsViewerControlsSelectedBackgroundColor","#6d96d2"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#5e5e5e"],["@JsViewerControlsOverBorderColor","#6d96d2"],["@JsViewerControlsSelectedBorderColor","#6d96d2"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerFormBorderColor","#5e5e5e"],["@JsViewerFormBackgroundColor","#b2b2b2"],["@JsViewerFormContainerBackgroundColor","#b2b2b2"],["@JsViewerFormHeaderBackgroundColor","#b2b2b2"],["@JsViewerFormHeaderFontColor","#3e3e3e"],["@JsViewerFormSeparatorColor","#e0e0e0"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#e8e8e8"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#f8f8f8"],["@JsViewerMenuBorderColor","#5e5e5e"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMainPanelsBorderColor","#5e5e5e"],["@JsViewerHyperlinkButtonFontColor","#5a83ad"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerToolbarBackgroundColor","#b2b2b2"],["@JsViewerProgressColorRGB","25,71,138"],["@JsViewerNavigatePanelBackgroundColor","#19478a"],["@JsViewerNavigatePanelSeparatorColor","#a3b5d0"],["@JsViewerNavigateButtonOverBackgroundColor","#3b629b"],["@JsViewerNavigateButtonSelectedBackgroundColor","#11315f"],["@JsViewerGroupHeaderButtonBackgroundColor","#cacaca"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#bcbcbc"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#c0c0c0"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#d0d0d0"]],"Office2013LightGrayViolet":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#eed8ec"],["@JsViewerButtonSelectedBackgroundColor","#d6abd3"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#eed8ec"],["@JsViewerButtonSelectedBorderColor","#d6abd3"],["@JsViewerButtonDisabledBorderColor","#b7b4b7"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#444444"],["@JsViewerControlsFontSelectedColor","#444444"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#eed8ec"],["@JsViewerControlsSelectedBackgroundColor","#d6abd3"],["@JsViewerControlsDisabledBackgroundColor","transparent"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#d6abd3"],["@JsViewerControlsSelectedBorderColor","#d6abd3"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerFormBorderColor","#ababab"],["@JsViewerFormBackgroundColor","#fafafa"],["@JsViewerFormContainerBackgroundColor","#fafafa"],["@JsViewerFormHeaderBackgroundColor","#fafafa"],["@JsViewerFormHeaderFontColor","#9d6b9a"],["@JsViewerFormSeparatorColor","#c8c8c8"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#ffffff"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#fdebfb"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMainPanelsBorderColor","#c6c6c6"],["@JsViewerHyperlinkButtonFontColor","#6b5479"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerToolbarBackgroundColor","#fafafa"],["@JsViewerProgressColorRGB","109,48,105"],["@JsViewerNavigatePanelBackgroundColor","#6d3069"],["@JsViewerNavigatePanelSeparatorColor","#c5acc3"],["@JsViewerNavigateButtonOverBackgroundColor","#834f7f"],["@JsViewerNavigateButtonSelectedBackgroundColor","#4b2148"],["@JsViewerGroupHeaderButtonBackgroundColor","#e5e5e5"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#d5d5d5"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]],"Office2013LightGrayTeal":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#d3f0ec"],["@JsViewerButtonSelectedBackgroundColor","#7dbbb3"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#d3f0ec"],["@JsViewerButtonSelectedBorderColor","#7dbbb3"],["@JsViewerButtonDisabledBorderColor","#b7b4b7"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#444444"],["@JsViewerControlsFontSelectedColor","#444444"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#d3f0ec"],["@JsViewerControlsSelectedBackgroundColor","#7dbbb3"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#7dbbb3"],["@JsViewerControlsSelectedBorderColor","#7dbbb3"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerFormBorderColor","#ababab"],["@JsViewerFormBackgroundColor","#fafafa"],["@JsViewerFormContainerBackgroundColor","#fafafa"],["@JsViewerFormHeaderBackgroundColor","#fafafa"],["@JsViewerFormHeaderFontColor","#317e74"],["@JsViewerFormSeparatorColor","#c8c8c8"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#ffffff"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#eefdfb"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMainPanelsBorderColor","#c6c6c6"],["@JsViewerHyperlinkButtonFontColor","#4d716c"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerToolbarBackgroundColor","#fafafa"],["@JsViewerProgressColorRGB","35,100,92"],["@JsViewerNavigatePanelBackgroundColor","#23645c"],["@JsViewerNavigatePanelSeparatorColor","#a7c1be"],["@JsViewerNavigateButtonOverBackgroundColor","#447b74"],["@JsViewerNavigateButtonSelectedBackgroundColor","#18453f"],["@JsViewerGroupHeaderButtonBackgroundColor","#e5e5e5"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#d5d5d5"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]],"Office2013LightGrayPurple":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#eadcf2"],["@JsViewerButtonSelectedBackgroundColor","#c3a9d4"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#eadcf2"],["@JsViewerButtonSelectedBorderColor","#c3a9d4"],["@JsViewerButtonDisabledBorderColor","#b7b4b7"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#444444"],["@JsViewerControlsFontSelectedColor","#444444"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#eadcf2"],["@JsViewerControlsSelectedBackgroundColor","#c3a9d4"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#c3a9d4"],["@JsViewerControlsSelectedBorderColor","#c3a9d4"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerFormBorderColor","#ababab"],["@JsViewerFormBackgroundColor","#fafafa"],["@JsViewerFormContainerBackgroundColor","#fafafa"],["@JsViewerFormHeaderBackgroundColor","#fafafa"],["@JsViewerFormHeaderFontColor","#9273a6"],["@JsViewerFormSeparatorColor","#c8c8c8"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#ffffff"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#f9efff"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMainPanelsBorderColor","#c6c6c6"],["@JsViewerHyperlinkButtonFontColor","#755574"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerToolbarBackgroundColor","#fafafa"],["@JsViewerProgressColorRGB","134,83,165"],["@JsViewerNavigatePanelBackgroundColor","#8653a5"],["@JsViewerNavigatePanelSeparatorColor","#cfbadb"],["@JsViewerNavigateButtonOverBackgroundColor","#986db2"],["@JsViewerNavigateButtonSelectedBackgroundColor","#5c3971"],["@JsViewerGroupHeaderButtonBackgroundColor","#e5e5e5"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#d5d5d5"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]],"Office2013LightGrayOrange":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#fce4dc"],["@JsViewerButtonSelectedBackgroundColor","#f5ba9d"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#fce4dc"],["@JsViewerButtonSelectedBorderColor","#f5ba9d"],["@JsViewerButtonDisabledBorderColor","#b7b4b7"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#444444"],["@JsViewerControlsFontSelectedColor","#444444"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#fce4dc"],["@JsViewerControlsSelectedBackgroundColor","#f5ba9d"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#f5ba9d"],["@JsViewerControlsSelectedBorderColor","#f5ba9d"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerFormBorderColor","#ababab"],["@JsViewerFormBackgroundColor","#fafafa"],["@JsViewerFormContainerBackgroundColor","#fafafa"],["@JsViewerFormHeaderBackgroundColor","#fafafa"],["@JsViewerFormHeaderFontColor","#b8795a"],["@JsViewerFormSeparatorColor","#c8c8c8"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#ffffff"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#fff4f0"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMainPanelsBorderColor","#c6c6c6"],["@JsViewerHyperlinkButtonFontColor","#9d5b4a"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerToolbarBackgroundColor","#fafafa"],["@JsViewerProgressColorRGB","183,58,28"],["@JsViewerNavigatePanelBackgroundColor","#b73a1c"],["@JsViewerNavigatePanelSeparatorColor","#e2b0a4"],["@JsViewerNavigateButtonOverBackgroundColor","#c2573e"],["@JsViewerNavigateButtonSelectedBackgroundColor","#7e2813"],["@JsViewerGroupHeaderButtonBackgroundColor","#e5e5e5"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#d5d5d5"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]],"Office2013LightGrayGreen":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#d4f1e1"],["@JsViewerButtonSelectedBackgroundColor","#86bfa0"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#d4f1e1"],["@JsViewerButtonSelectedBorderColor","#86bfa0"],["@JsViewerButtonDisabledBorderColor","#b7b4b7"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#444444"],["@JsViewerControlsFontSelectedColor","#444444"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#d4f1e1"],["@JsViewerControlsSelectedBackgroundColor","#86bfa0"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#86bfa0"],["@JsViewerControlsSelectedBorderColor","#86bfa0"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerFormBorderColor","#ababab"],["@JsViewerFormBackgroundColor","#fafafa"],["@JsViewerFormContainerBackgroundColor","#fafafa"],["@JsViewerFormHeaderBackgroundColor","#fafafa"],["@JsViewerFormHeaderFontColor","#539672"],["@JsViewerFormSeparatorColor","#c8c8c8"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#ffffff"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#e6fcf0"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMainPanelsBorderColor","#c6c6c6"],["@JsViewerHyperlinkButtonFontColor","#437258"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerToolbarBackgroundColor","#fafafa"],["@JsViewerProgressColorRGB","11,100,51"],["@JsViewerNavigatePanelBackgroundColor","#0b6433"],["@JsViewerNavigatePanelSeparatorColor","#9dc1ad"],["@JsViewerNavigateButtonOverBackgroundColor","#2f7b51"],["@JsViewerNavigateButtonSelectedBackgroundColor","#084523"],["@JsViewerGroupHeaderButtonBackgroundColor","#e5e5e5"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#d5d5d5"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]],"Office2013LightGrayCarmine":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#fbddde"],["@JsViewerButtonSelectedBackgroundColor","#f7a8ab"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#fbddde"],["@JsViewerButtonSelectedBorderColor","#f7a8ab"],["@JsViewerButtonDisabledBorderColor","#b7b4b7"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#444444"],["@JsViewerControlsFontSelectedColor","#444444"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#fbddde"],["@JsViewerControlsSelectedBackgroundColor","#f7a8ab"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#f7a8ab"],["@JsViewerControlsSelectedBorderColor","#f7a8ab"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerFormBorderColor","#ababab"],["@JsViewerFormBackgroundColor","#fafafa"],["@JsViewerFormContainerBackgroundColor","#fafafa"],["@JsViewerFormHeaderBackgroundColor","#fafafa"],["@JsViewerFormHeaderFontColor","#cf7d80"],["@JsViewerFormSeparatorColor","#c8c8c8"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#ffffff"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#fceff0"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMainPanelsBorderColor","#c6c6c6"],["@JsViewerHyperlinkButtonFontColor","#8a5556"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerToolbarBackgroundColor","#fafafa"],["@JsViewerProgressColorRGB","145,44,47"],["@JsViewerNavigatePanelBackgroundColor","#912c2f"],["@JsViewerNavigatePanelSeparatorColor","#d3abac"],["@JsViewerNavigateButtonOverBackgroundColor","#a14b4e"],["@JsViewerNavigateButtonSelectedBackgroundColor","#641e20"],["@JsViewerGroupHeaderButtonBackgroundColor","#e5e5e5"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#d5d5d5"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]],"Office2013LightGrayBlue":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#d5e1f2"],["@JsViewerButtonSelectedBackgroundColor","#c2d5f2"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#d5e1f2"],["@JsViewerButtonSelectedBorderColor","#c2d5f2"],["@JsViewerButtonDisabledBorderColor","#b7b4b7"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#d5e1f2"],["@JsViewerControlsFontSelectedColor","#c2d5f2"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#d5e1f2"],["@JsViewerControlsSelectedBackgroundColor","#c2d5f2"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#a3bde3"],["@JsViewerControlsSelectedBorderColor","#c2d5f2"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerFormBorderColor","#ababab"],["@JsViewerFormBackgroundColor","#fafafa"],["@JsViewerFormContainerBackgroundColor","#fafafa"],["@JsViewerFormHeaderBackgroundColor","#fafafa"],["@JsViewerFormHeaderFontColor","#587cb1"],["@JsViewerFormSeparatorColor","#c8c8c8"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#ffffff"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#f8f8f8"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMainPanelsBorderColor","#c6c6c6"],["@JsViewerHyperlinkButtonFontColor","#5a83ad"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerToolbarBackgroundColor","#fafafa"],["@JsViewerProgressColorRGB","25,71,138"],["@JsViewerNavigatePanelBackgroundColor","#19478a"],["@JsViewerNavigatePanelSeparatorColor","#a3b5d0"],["@JsViewerNavigateButtonOverBackgroundColor","#3b629b"],["@JsViewerNavigateButtonSelectedBackgroundColor","#11315f"],["@JsViewerGroupHeaderButtonBackgroundColor","#e5e5e5"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#d5d5d5"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]],"Office2013DarkGrayViolet":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#eed8ec"],["@JsViewerButtonSelectedBackgroundColor","#d6abd3"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#eed8ec"],["@JsViewerButtonSelectedBorderColor","#d6abd3"],["@JsViewerButtonDisabledBorderColor","#b7b4b7"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#444444"],["@JsViewerControlsFontSelectedColor","#444444"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#eed8ec"],["@JsViewerControlsSelectedBackgroundColor","#d6abd3"],["@JsViewerControlsDisabledBackgroundColor","transparent"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#d6abd3"],["@JsViewerControlsSelectedBorderColor","#d6abd3"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerFormBorderColor","#ababab"],["@JsViewerFormBackgroundColor","#f3f3f3"],["@JsViewerFormContainerBackgroundColor","#f3f3f3"],["@JsViewerFormHeaderBackgroundColor","#f3f3f3"],["@JsViewerFormHeaderFontColor","#9d6b9a"],["@JsViewerFormSeparatorColor","#c8c8c8"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#ffffff"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#fdebfb"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMainPanelsBorderColor","#c6c6c6"],["@JsViewerHyperlinkButtonFontColor","#6b5479"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerToolbarBackgroundColor","#f3f3f3"],["@JsViewerProgressColorRGB","109,48,105"],["@JsViewerNavigatePanelBackgroundColor","#6d3069"],["@JsViewerNavigatePanelSeparatorColor","#c5acc3"],["@JsViewerNavigateButtonOverBackgroundColor","#834f7f"],["@JsViewerNavigateButtonSelectedBackgroundColor","#4b2148"],["@JsViewerGroupHeaderButtonBackgroundColor","#e5e5e5"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#d5d5d5"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]],"Office2013DarkGrayTeal":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#d3f0ec"],["@JsViewerButtonSelectedBackgroundColor","#7dbbb3"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#d3f0ec"],["@JsViewerButtonSelectedBorderColor","#7dbbb3"],["@JsViewerButtonDisabledBorderColor","#b7b4b7"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#444444"],["@JsViewerControlsFontSelectedColor","#444444"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#d3f0ec"],["@JsViewerControlsSelectedBackgroundColor","#7dbbb3"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#7dbbb3"],["@JsViewerControlsSelectedBorderColor","#7dbbb3"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerFormBorderColor","#ababab"],["@JsViewerFormBackgroundColor","#f3f3f3"],["@JsViewerFormContainerBackgroundColor","#f3f3f3"],["@JsViewerFormHeaderBackgroundColor","#f3f3f3"],["@JsViewerFormHeaderFontColor","#317e74"],["@JsViewerFormSeparatorColor","#c8c8c8"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#ffffff"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#eefdfb"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMainPanelsBorderColor","#c6c6c6"],["@JsViewerHyperlinkButtonFontColor","#4d716c"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerToolbarBackgroundColor","#f3f3f3"],["@JsViewerProgressColorRGB","35,100,92"],["@JsViewerNavigatePanelBackgroundColor","#23645c"],["@JsViewerNavigatePanelSeparatorColor","#a7c1be"],["@JsViewerNavigateButtonOverBackgroundColor","#447b74"],["@JsViewerNavigateButtonSelectedBackgroundColor","#18453f"],["@JsViewerGroupHeaderButtonBackgroundColor","#e5e5e5"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#d5d5d5"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]],"Office2013DarkGrayPurple":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#eadcf2"],["@JsViewerButtonSelectedBackgroundColor","#c3a9d4"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#eadcf2"],["@JsViewerButtonSelectedBorderColor","#c3a9d4"],["@JsViewerButtonDisabledBorderColor","#b7b4b7"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#444444"],["@JsViewerControlsFontSelectedColor","#444444"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#eadcf2"],["@JsViewerControlsSelectedBackgroundColor","#c3a9d4"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#c3a9d4"],["@JsViewerControlsSelectedBorderColor","#c3a9d4"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerFormBorderColor","#ababab"],["@JsViewerFormBackgroundColor","#f3f3f3"],["@JsViewerFormContainerBackgroundColor","#f3f3f3"],["@JsViewerFormHeaderBackgroundColor","#f3f3f3"],["@JsViewerFormHeaderFontColor","#9273a6"],["@JsViewerFormSeparatorColor","#c8c8c8"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#ffffff"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#f9efff"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMainPanelsBorderColor","#c6c6c6"],["@JsViewerHyperlinkButtonFontColor","#755574"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerToolbarBackgroundColor","#f3f3f3"],["@JsViewerProgressColorRGB","134,83,165"],["@JsViewerNavigatePanelBackgroundColor","#8653a5"],["@JsViewerNavigatePanelSeparatorColor","#cfbadb"],["@JsViewerNavigateButtonOverBackgroundColor","#986db2"],["@JsViewerNavigateButtonSelectedBackgroundColor","#5c3971"],["@JsViewerGroupHeaderButtonBackgroundColor","#e5e5e5"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#d5d5d5"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]],"Office2013DarkGrayOrange":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#fce4dc"],["@JsViewerButtonSelectedBackgroundColor","#f5ba9d"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#fce4dc"],["@JsViewerButtonSelectedBorderColor","#f5ba9d"],["@JsViewerButtonDisabledBorderColor","#b7b4b7"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#444444"],["@JsViewerControlsFontSelectedColor","#444444"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#fce4dc"],["@JsViewerControlsSelectedBackgroundColor","#f5ba9d"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#f5ba9d"],["@JsViewerControlsSelectedBorderColor","#f5ba9d"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerFormBorderColor","#ababab"],["@JsViewerFormBackgroundColor","#f3f3f3"],["@JsViewerFormContainerBackgroundColor","#f3f3f3"],["@JsViewerFormHeaderBackgroundColor","#f3f3f3"],["@JsViewerFormHeaderFontColor","#b8795a"],["@JsViewerFormSeparatorColor","#c8c8c8"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#ffffff"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#fff4f0"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMainPanelsBorderColor","#c6c6c6"],["@JsViewerHyperlinkButtonFontColor","#9d5b4a"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerToolbarBackgroundColor","#f3f3f3"],["@JsViewerProgressColorRGB","183,58,28"],["@JsViewerNavigatePanelBackgroundColor","#b73a1c"],["@JsViewerNavigatePanelSeparatorColor","#e2b0a4"],["@JsViewerNavigateButtonOverBackgroundColor","#c2573e"],["@JsViewerNavigateButtonSelectedBackgroundColor","#7e2813"],["@JsViewerGroupHeaderButtonBackgroundColor","#e5e5e5"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#d5d5d5"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]],"Office2013DarkGrayGreen":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#d4f1e1"],["@JsViewerButtonSelectedBackgroundColor","#86bfa0"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#d4f1e1"],["@JsViewerButtonSelectedBorderColor","#86bfa0"],["@JsViewerButtonDisabledBorderColor","#b7b4b7"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#444444"],["@JsViewerControlsFontSelectedColor","#444444"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#d4f1e1"],["@JsViewerControlsSelectedBackgroundColor","#86bfa0"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#86bfa0"],["@JsViewerControlsSelectedBorderColor","#86bfa0"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerFormBorderColor","#ababab"],["@JsViewerFormBackgroundColor","#f3f3f3"],["@JsViewerFormContainerBackgroundColor","#f3f3f3"],["@JsViewerFormHeaderBackgroundColor","#f3f3f3"],["@JsViewerFormHeaderFontColor","#539672"],["@JsViewerFormSeparatorColor","#c8c8c8"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#ffffff"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#e6fcf0"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMainPanelsBorderColor","#c6c6c6"],["@JsViewerHyperlinkButtonFontColor","#437258"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerToolbarBackgroundColor","#f3f3f3"],["@JsViewerProgressColorRGB","11,100,51"],["@JsViewerNavigatePanelBackgroundColor","#0b6433"],["@JsViewerNavigatePanelSeparatorColor","#9dc1ad"],["@JsViewerNavigateButtonOverBackgroundColor","#2f7b51"],["@JsViewerNavigateButtonSelectedBackgroundColor","#084523"],["@JsViewerGroupHeaderButtonBackgroundColor","#e5e5e5"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#d5d5d5"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]],"Office2013DarkGrayCarmine":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#fbddde"],["@JsViewerButtonSelectedBackgroundColor","#f7a8ab"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#fbddde"],["@JsViewerButtonSelectedBorderColor","#f7a8ab"],["@JsViewerButtonDisabledBorderColor","#b7b4b7"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#444444"],["@JsViewerControlsFontSelectedColor","#444444"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#fbddde"],["@JsViewerControlsSelectedBackgroundColor","#f7a8ab"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#f7a8ab"],["@JsViewerControlsSelectedBorderColor","#f7a8ab"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerFormBorderColor","#ababab"],["@JsViewerFormBackgroundColor","#f3f3f3"],["@JsViewerFormContainerBackgroundColor","#f3f3f3"],["@JsViewerFormHeaderBackgroundColor","#f3f3f3"],["@JsViewerFormHeaderFontColor","#cf7d80"],["@JsViewerFormSeparatorColor","#c8c8c8"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#ffffff"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#fceff0"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMainPanelsBorderColor","#c6c6c6"],["@JsViewerHyperlinkButtonFontColor","#8a5556"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerToolbarBackgroundColor","#f3f3f3"],["@JsViewerProgressColorRGB","145,44,47"],["@JsViewerNavigatePanelBackgroundColor","#912c2f"],["@JsViewerNavigatePanelSeparatorColor","#d3abac"],["@JsViewerNavigateButtonOverBackgroundColor","#a14b4e"],["@JsViewerNavigateButtonSelectedBackgroundColor","#641e20"],["@JsViewerGroupHeaderButtonBackgroundColor","#e5e5e5"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#d5d5d5"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]],"Office2013DarkGrayBlue":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#d5e1f2"],["@JsViewerButtonSelectedBackgroundColor","#c2d5f2"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#d5e1f2"],["@JsViewerButtonSelectedBorderColor","#c2d5f2"],["@JsViewerButtonDisabledBorderColor","#b7b4b7"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#d5e1f2"],["@JsViewerControlsFontSelectedColor","#c2d5f2"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#d5e1f2"],["@JsViewerControlsSelectedBackgroundColor","#c2d5f2"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#a3bde3"],["@JsViewerControlsSelectedBorderColor","#c2d5f2"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerFormBorderColor","#ababab"],["@JsViewerFormBackgroundColor","#f3f3f3"],["@JsViewerFormContainerBackgroundColor","#f3f3f3"],["@JsViewerFormHeaderBackgroundColor","#f3f3f3"],["@JsViewerFormHeaderFontColor","#587cb1"],["@JsViewerFormSeparatorColor","#c8c8c8"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#ffffff"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#f8f8f8"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMainPanelsBorderColor","#c6c6c6"],["@JsViewerHyperlinkButtonFontColor","#5a83ad"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerToolbarBackgroundColor","#f3f3f3"],["@JsViewerProgressColorRGB","25,71,138"],["@JsViewerNavigatePanelBackgroundColor","#19478a"],["@JsViewerNavigatePanelSeparatorColor","#a3b5d0"],["@JsViewerNavigateButtonOverBackgroundColor","#3b629b"],["@JsViewerNavigateButtonSelectedBackgroundColor","#11315f"],["@JsViewerGroupHeaderButtonBackgroundColor","#e5e5e5"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#d5d5d5"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]]}},"Office2022":{stylesText:[330,329,328,327,390,325,324,323,322,389,189,321,187,186,185,184,183,182,181,180,388,178,387,386,385,174,236,235,234,233,232,168,167,166,165,164,163,162,161,160,159,384,383,156,155,382,381,152,151,150,149,148,147,380,145,144,320,142,379,378,377,376,137,375,133,132,131,130,129,128,374,316,315,314,313,306,312,311,310,309,308,307,306,305,"@-moz-keyframes spin { 100% { -moz-transform: rotate(180deg); }\n}","@-webkit-keyframes spin { 100% { -webkit-transform: rotate(180deg); }\n}","@keyframes spin { 100% { -webkit-transform: rotate(180deg); transform: rotate(180deg); }\n}",373,372,124,123,122,121,120,119,371,370,369,368,367,366,365,364,363,362,361,298,297,296,295,360,107,106,105,104,103,102,359,292,358,290,289,288,95,94,93,92,91,90,89,88,357,356,355,287,83,82,81,80,79,78,77,76,75,74,73,286,285,70,284,283,282,281,354,64,63,62,61,280,59,58,279,353,55,278,53,52,352,351,350,48,274,46,45,"@-webkit-keyframes load8 { 0% { -webkit-transform: rotate(0deg); transform: rotate(0deg); } 100% { -webkit-transform: rotate(360deg); transform: rotate(360deg); }\n}","@keyframes load8 { 0% { -webkit-transform: rotate(0deg); transform: rotate(0deg); } 100% { -webkit-transform: rotate(360deg); transform: rotate(360deg); }\n}",44,349,42,41,40,39,38,201,36,35,34,348,347,346,345,344,343,268,267,266,265,28,27,26,25,24,197,22,21,20,19,18,17,342,341,340,339,338,337,11,336,262,335,334,6,333,332,331,2,1],accents:{"Office2022WhiteViolet":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#dadada"],["@JsViewerButtonSelectedBackgroundColor","#e5e5e5"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#dadada"],["@JsViewerButtonSelectedBorderColor","#e5e5e5"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerButtonThemeBackgroundColor","#6d3069"],["@JsViewerButtonThemeOverBackgroundColor","#834f7f"],["@JsViewerButtonThemeSelectedBackgroundColor","#4b2148"],["@JsViewerButtonThemeDisabledBorderColor","transparent"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#d5e1f2"],["@JsViewerControlsFontSelectedColor","#c2d5f2"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#eaeaea"],["@JsViewerControlsSelectedBackgroundColor","#eaeaea"],["@JsViewerControlsDisabledBackgroundColor","#f7f7f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#6b6b6b"],["@JsViewerControlsSelectedBorderColor","#6b6b6b"],["@JsViewerControlsDisabledBorderColor","#e1e1e1"],["@JsViewerFormButtonBackgroundColor","#dddddd"],["@JsViewerFormButtonOverBackgroundColor","#d5d5d5"],["@JsViewerFormButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerFormButtonDisabledBackgroundColor","#fafafa"],["@JsViewerFormBorderColor","#6d3069"],["@JsViewerFormBackgroundColor","#ffffff"],["@JsViewerFormContainerBackgroundColor","#ffffff"],["@JsViewerFormHeaderBackgroundColor","#ffffff"],["@JsViewerFormHeaderFontColor","#9d6b9a"],["@JsViewerFormSeparatorColor","#c8c8c8"],["@JsViewerFormFontColor","#202020"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#ffffff"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#fdebfb"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMenuFontColor","#202020"],["@JsViewerMainPanelsBorderColor","#c6c6c6"],["@JsViewerHyperlinkButtonFontColor","#6b5479"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerProgressColorRGB","109,48,105"],["@JsViewerToolbarBackgroundColor","#ffffff"],["@JsViewerToolbarFontColor","#202020"],["@JsViewerNavigatePanelFontColor","#ffffff"],["@JsViewerNavigatePanelBackgroundColor","#6d3069"],["@JsViewerNavigatePanelSeparatorColor","#c5acc3"],["@JsViewerNavigateButtonOverBackgroundColor","#834f7f"],["@JsViewerNavigateButtonSelectedBackgroundColor","#4b2148"],["@JsViewerGroupHeaderButtonBackgroundColor","#f3f3f3"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#e3e3e3"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d3d3d3"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]],"Office2022WhiteTeal":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#dadada"],["@JsViewerButtonSelectedBackgroundColor","#e5e5e5"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#dadada"],["@JsViewerButtonSelectedBorderColor","#e5e5e5"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerButtonThemeBackgroundColor","#23645c"],["@JsViewerButtonThemeOverBackgroundColor","#447b74"],["@JsViewerButtonThemeSelectedBackgroundColor","#18453f"],["@JsViewerButtonThemeDisabledBorderColor","transparent"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#d5e1f2"],["@JsViewerControlsFontSelectedColor","#c2d5f2"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#eaeaea"],["@JsViewerControlsSelectedBackgroundColor","#eaeaea"],["@JsViewerControlsDisabledBackgroundColor","#f7f7f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#6b6b6b"],["@JsViewerControlsSelectedBorderColor","#6b6b6b"],["@JsViewerControlsDisabledBorderColor","#e1e1e1"],["@JsViewerFormButtonBackgroundColor","#dddddd"],["@JsViewerFormButtonOverBackgroundColor","#d5d5d5"],["@JsViewerFormButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerFormButtonDisabledBackgroundColor","#fafafa"],["@JsViewerFormBorderColor","#23645c"],["@JsViewerFormBackgroundColor","#ffffff"],["@JsViewerFormContainerBackgroundColor","#ffffff"],["@JsViewerFormHeaderBackgroundColor","#ffffff"],["@JsViewerFormHeaderFontColor","#317e74"],["@JsViewerFormSeparatorColor","#c8c8c8"],["@JsViewerFormFontColor","#202020"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#ffffff"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#eefdfb"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMenuFontColor","#202020"],["@JsViewerMainPanelsBorderColor","#c6c6c6"],["@JsViewerHyperlinkButtonFontColor","#4d716c"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerProgressColorRGB","35,100,92"],["@JsViewerToolbarBackgroundColor","#ffffff"],["@JsViewerToolbarFontColor","#202020"],["@JsViewerNavigatePanelFontColor","#ffffff"],["@JsViewerNavigatePanelBackgroundColor","#23645c"],["@JsViewerNavigatePanelSeparatorColor","#a7c1be"],["@JsViewerNavigateButtonOverBackgroundColor","#447b74"],["@JsViewerNavigateButtonSelectedBackgroundColor","#18453f"],["@JsViewerGroupHeaderButtonBackgroundColor","#f3f3f3"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#e3e3e3"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d3d3d3"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]],"Office2022WhitePurple":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#dadada"],["@JsViewerButtonSelectedBackgroundColor","#e5e5e5"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#dadada"],["@JsViewerButtonSelectedBorderColor","#e5e5e5"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerButtonThemeBackgroundColor","#8653a5"],["@JsViewerButtonThemeOverBackgroundColor","#986db2"],["@JsViewerButtonThemeSelectedBackgroundColor","#5c3971"],["@JsViewerButtonThemeDisabledBorderColor","transparent"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#d5e1f2"],["@JsViewerControlsFontSelectedColor","#c2d5f2"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#eaeaea"],["@JsViewerControlsSelectedBackgroundColor","#eaeaea"],["@JsViewerControlsDisabledBackgroundColor","#f7f7f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#6b6b6b"],["@JsViewerControlsSelectedBorderColor","#6b6b6b"],["@JsViewerControlsDisabledBorderColor","#e1e1e1"],["@JsViewerFormButtonBackgroundColor","#dddddd"],["@JsViewerFormButtonOverBackgroundColor","#d5d5d5"],["@JsViewerFormButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerFormButtonDisabledBackgroundColor","#fafafa"],["@JsViewerFormBorderColor","#8653a5"],["@JsViewerFormBackgroundColor","#ffffff"],["@JsViewerFormContainerBackgroundColor","#ffffff"],["@JsViewerFormHeaderBackgroundColor","#ffffff"],["@JsViewerFormHeaderFontColor","#9273a6"],["@JsViewerFormSeparatorColor","#c8c8c8"],["@JsViewerFormFontColor","#202020"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#ffffff"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#f9efff"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMenuFontColor","#202020"],["@JsViewerMainPanelsBorderColor","#c6c6c6"],["@JsViewerHyperlinkButtonFontColor","#755574"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerProgressColorRGB","134,83,165"],["@JsViewerToolbarBackgroundColor","#ffffff"],["@JsViewerToolbarFontColor","#202020"],["@JsViewerNavigatePanelFontColor","#ffffff"],["@JsViewerNavigatePanelBackgroundColor","#8653a5"],["@JsViewerNavigatePanelSeparatorColor","#cfbadb"],["@JsViewerNavigateButtonOverBackgroundColor","#986db2"],["@JsViewerNavigateButtonSelectedBackgroundColor","#5c3971"],["@JsViewerGroupHeaderButtonBackgroundColor","#f3f3f3"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#e3e3e3"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d3d3d3"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]],"Office2022WhiteOrange":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#dadada"],["@JsViewerButtonSelectedBackgroundColor","#e5e5e5"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#dadada"],["@JsViewerButtonSelectedBorderColor","#e5e5e5"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerButtonThemeBackgroundColor","#b73a1c"],["@JsViewerButtonThemeOverBackgroundColor","#c2573e"],["@JsViewerButtonThemeSelectedBackgroundColor","#7e2813"],["@JsViewerButtonThemeDisabledBorderColor","transparent"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#d5e1f2"],["@JsViewerControlsFontSelectedColor","#c2d5f2"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#eaeaea"],["@JsViewerControlsSelectedBackgroundColor","#eaeaea"],["@JsViewerControlsDisabledBackgroundColor","#f7f7f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#6b6b6b"],["@JsViewerControlsSelectedBorderColor","#6b6b6b"],["@JsViewerControlsDisabledBorderColor","#e1e1e1"],["@JsViewerFormButtonBackgroundColor","#dddddd"],["@JsViewerFormButtonOverBackgroundColor","#d5d5d5"],["@JsViewerFormButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerFormButtonDisabledBackgroundColor","#fafafa"],["@JsViewerFormBorderColor","#b73a1c"],["@JsViewerFormBackgroundColor","#ffffff"],["@JsViewerFormContainerBackgroundColor","#ffffff"],["@JsViewerFormHeaderBackgroundColor","#ffffff"],["@JsViewerFormHeaderFontColor","#b8795a"],["@JsViewerFormSeparatorColor","#c8c8c8"],["@JsViewerFormFontColor","#202020"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#ffffff"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#fff4f0"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMenuFontColor","#202020"],["@JsViewerMainPanelsBorderColor","#c6c6c6"],["@JsViewerHyperlinkButtonFontColor","#9d5b4a"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerProgressColorRGB","183,58,28"],["@JsViewerToolbarBackgroundColor","#ffffff"],["@JsViewerToolbarFontColor","#202020"],["@JsViewerNavigatePanelFontColor","#ffffff"],["@JsViewerNavigatePanelBackgroundColor","#b73a1c"],["@JsViewerNavigatePanelSeparatorColor","#e2b0a4"],["@JsViewerNavigateButtonOverBackgroundColor","#c2573e"],["@JsViewerNavigateButtonSelectedBackgroundColor","#7e2813"],["@JsViewerGroupHeaderButtonBackgroundColor","#f3f3f3"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#e3e3e3"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d3d3d3"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]],"Office2022WhiteGreen":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#dadada"],["@JsViewerButtonSelectedBackgroundColor","#e5e5e5"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#dadada"],["@JsViewerButtonSelectedBorderColor","#e5e5e5"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerButtonThemeBackgroundColor","#0b6433"],["@JsViewerButtonThemeOverBackgroundColor","#2f7b51"],["@JsViewerButtonThemeSelectedBackgroundColor","#084523"],["@JsViewerButtonThemeDisabledBorderColor","transparent"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#d5e1f2"],["@JsViewerControlsFontSelectedColor","#c2d5f2"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#eaeaea"],["@JsViewerControlsSelectedBackgroundColor","#eaeaea"],["@JsViewerControlsDisabledBackgroundColor","#f7f7f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#6b6b6b"],["@JsViewerControlsSelectedBorderColor","#6b6b6b"],["@JsViewerControlsDisabledBorderColor","#e1e1e1"],["@JsViewerFormButtonBackgroundColor","#dddddd"],["@JsViewerFormButtonOverBackgroundColor","#d5d5d5"],["@JsViewerFormButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerFormButtonDisabledBackgroundColor","#fafafa"],["@JsViewerFormBorderColor","#0b6433"],["@JsViewerFormBackgroundColor","#ffffff"],["@JsViewerFormContainerBackgroundColor","#ffffff"],["@JsViewerFormHeaderBackgroundColor","#ffffff"],["@JsViewerFormHeaderFontColor","#539672"],["@JsViewerFormSeparatorColor","#c8c8c8"],["@JsViewerFormFontColor","#202020"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#ffffff"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#e6fcf0"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMenuFontColor","#202020"],["@JsViewerMainPanelsBorderColor","#c6c6c6"],["@JsViewerHyperlinkButtonFontColor","#437258"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerProgressColorRGB","11,100,51"],["@JsViewerToolbarBackgroundColor","#ffffff"],["@JsViewerToolbarFontColor","#202020"],["@JsViewerNavigatePanelFontColor","#ffffff"],["@JsViewerNavigatePanelBackgroundColor","#0b6433"],["@JsViewerNavigatePanelSeparatorColor","#9dc1ad"],["@JsViewerNavigateButtonOverBackgroundColor","#2f7b51"],["@JsViewerNavigateButtonSelectedBackgroundColor","#084523"],["@JsViewerGroupHeaderButtonBackgroundColor","#f3f3f3"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#e3e3e3"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d3d3d3"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]],"Office2022WhiteCarmine":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#dadada"],["@JsViewerButtonSelectedBackgroundColor","#e5e5e5"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#dadada"],["@JsViewerButtonSelectedBorderColor","#e5e5e5"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerButtonThemeBackgroundColor","#912c2f"],["@JsViewerButtonThemeOverBackgroundColor","#a14b4e"],["@JsViewerButtonThemeSelectedBackgroundColor","#641e20"],["@JsViewerButtonThemeDisabledBorderColor","transparent"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#d5e1f2"],["@JsViewerControlsFontSelectedColor","#c2d5f2"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#eaeaea"],["@JsViewerControlsSelectedBackgroundColor","#eaeaea"],["@JsViewerControlsDisabledBackgroundColor","#f7f7f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#6b6b6b"],["@JsViewerControlsSelectedBorderColor","#6b6b6b"],["@JsViewerControlsDisabledBorderColor","#e1e1e1"],["@JsViewerFormButtonBackgroundColor","#dddddd"],["@JsViewerFormButtonOverBackgroundColor","#d5d5d5"],["@JsViewerFormButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerFormButtonDisabledBackgroundColor","#fafafa"],["@JsViewerFormBorderColor","#912c2f"],["@JsViewerFormBackgroundColor","#ffffff"],["@JsViewerFormContainerBackgroundColor","#ffffff"],["@JsViewerFormHeaderBackgroundColor","#ffffff"],["@JsViewerFormHeaderFontColor","#cf7d80"],["@JsViewerFormSeparatorColor","#c8c8c8"],["@JsViewerFormFontColor","#202020"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#ffffff"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#fceff0"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMenuFontColor","#202020"],["@JsViewerMainPanelsBorderColor","#c6c6c6"],["@JsViewerHyperlinkButtonFontColor","#8a5556"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerProgressColorRGB","145,44,47"],["@JsViewerToolbarBackgroundColor","#ffffff"],["@JsViewerToolbarFontColor","#202020"],["@JsViewerNavigatePanelFontColor","#ffffff"],["@JsViewerNavigatePanelBackgroundColor","#912c2f"],["@JsViewerNavigatePanelSeparatorColor","#d3abac"],["@JsViewerNavigateButtonOverBackgroundColor","#a14b4e"],["@JsViewerNavigateButtonSelectedBackgroundColor","#641e20"],["@JsViewerGroupHeaderButtonBackgroundColor","#f3f3f3"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#e3e3e3"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d3d3d3"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]],"Office2022WhiteBlue":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#dadada"],["@JsViewerButtonSelectedBackgroundColor","#e5e5e5"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#dadada"],["@JsViewerButtonSelectedBorderColor","#e5e5e5"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerButtonThemeBackgroundColor","#19478a"],["@JsViewerButtonThemeOverBackgroundColor","#3b629b"],["@JsViewerButtonThemeSelectedBackgroundColor","#11315f"],["@JsViewerButtonThemeDisabledBorderColor","transparent"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#d5e1f2"],["@JsViewerControlsFontSelectedColor","#c2d5f2"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#eaeaea"],["@JsViewerControlsSelectedBackgroundColor","#eaeaea"],["@JsViewerControlsDisabledBackgroundColor","#f7f7f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#6b6b6b"],["@JsViewerControlsSelectedBorderColor","#6b6b6b"],["@JsViewerControlsDisabledBorderColor","#e1e1e1"],["@JsViewerFormButtonBackgroundColor","#dddddd"],["@JsViewerFormButtonOverBackgroundColor","#d5d5d5"],["@JsViewerFormButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerFormButtonDisabledBackgroundColor","#fafafa"],["@JsViewerFormBorderColor","#19478a"],["@JsViewerFormBackgroundColor","#ffffff"],["@JsViewerFormContainerBackgroundColor","#ffffff"],["@JsViewerFormHeaderBackgroundColor","#ffffff"],["@JsViewerFormHeaderFontColor","#587cb1"],["@JsViewerFormSeparatorColor","#c8c8c8"],["@JsViewerFormFontColor","#202020"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#ffffff"],["@JsViewerGroupPanelCaptionBorderColor","#c6c6c6"],["@JsViewerGroupPanelCaptionBackgroundColor","#f8f8f8"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMenuFontColor","#202020"],["@JsViewerMainPanelsBorderColor","#c6c6c6"],["@JsViewerHyperlinkButtonFontColor","#5a83ad"],["@JsViewerMainPanelsBackgroundColor","#ffffff"],["@JsViewerProgressColorRGB","25,71,138"],["@JsViewerToolbarBackgroundColor","#ffffff"],["@JsViewerToolbarFontColor","#202020"],["@JsViewerNavigatePanelFontColor","#ffffff"],["@JsViewerNavigatePanelBackgroundColor","#19478a"],["@JsViewerNavigatePanelSeparatorColor","#a3b5d0"],["@JsViewerNavigateButtonOverBackgroundColor","#3b629b"],["@JsViewerNavigateButtonSelectedBackgroundColor","#11315f"],["@JsViewerGroupHeaderButtonBackgroundColor","#f3f3f3"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#e3e3e3"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d3d3d3"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]],"Office2022LightGrayViolet":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#dadada"],["@JsViewerButtonSelectedBackgroundColor","#e5e5e5"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#dadada"],["@JsViewerButtonSelectedBorderColor","#e5e5e5"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerButtonThemeBackgroundColor","#6d3069"],["@JsViewerButtonThemeOverBackgroundColor","#834f7f"],["@JsViewerButtonThemeSelectedBackgroundColor","#4b2148"],["@JsViewerButtonThemeDisabledBorderColor","transparent"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#d5e1f2"],["@JsViewerControlsFontSelectedColor","#c2d5f2"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#eaeaea"],["@JsViewerControlsSelectedBackgroundColor","#eaeaea"],["@JsViewerControlsDisabledBackgroundColor","#f7f7f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#6b6b6b"],["@JsViewerControlsSelectedBorderColor","#6b6b6b"],["@JsViewerControlsDisabledBorderColor","#e1e1e1"],["@JsViewerFormButtonBackgroundColor","#dddddd"],["@JsViewerFormButtonOverBackgroundColor","#d5d5d5"],["@JsViewerFormButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerFormButtonDisabledBackgroundColor","#fafafa"],["@JsViewerFormBorderColor","#6d3069"],["@JsViewerFormBackgroundColor","#f3f2f1"],["@JsViewerFormContainerBackgroundColor","#f3f2f1"],["@JsViewerFormHeaderBackgroundColor","#eeeeee"],["@JsViewerFormHeaderFontColor","#6d3069"],["@JsViewerFormSeparatorColor","#aaaaaa"],["@JsViewerFormFontColor","#202020"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#f3f2f1"],["@JsViewerGroupPanelCaptionBorderColor","#aaaaaa"],["@JsViewerGroupPanelCaptionBackgroundColor","#f8f8f8"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMenuFontColor","#202020"],["@JsViewerMainPanelsBorderColor","#aaaaaa"],["@JsViewerHyperlinkButtonFontColor","#8c5c89"],["@JsViewerMainPanelsBackgroundColor","#f3f2f1"],["@JsViewerProgressColorRGB","109,48,105"],["@JsViewerToolbarBackgroundColor","#f3f2f1"],["@JsViewerToolbarFontColor","#202020"],["@JsViewerNavigatePanelFontColor","#ffffff"],["@JsViewerNavigatePanelBackgroundColor","#6d3069"],["@JsViewerNavigatePanelSeparatorColor","#834f7f"],["@JsViewerNavigateButtonOverBackgroundColor","#4b2148"],["@JsViewerNavigateButtonSelectedBackgroundColor","#11315f"],["@JsViewerGroupHeaderButtonBackgroundColor","#e5e5e5"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#d5d5d5"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]],"Office2022LightGrayTeal":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#dadada"],["@JsViewerButtonSelectedBackgroundColor","#e5e5e5"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#dadada"],["@JsViewerButtonSelectedBorderColor","#e5e5e5"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerButtonThemeBackgroundColor","#19478a"],["@JsViewerButtonThemeOverBackgroundColor","#3b629b"],["@JsViewerButtonThemeSelectedBackgroundColor","#11315f"],["@JsViewerButtonThemeDisabledBorderColor","transparent"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#d5e1f2"],["@JsViewerControlsFontSelectedColor","#c2d5f2"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#eaeaea"],["@JsViewerControlsSelectedBackgroundColor","#eaeaea"],["@JsViewerControlsDisabledBackgroundColor","#f7f7f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#6b6b6b"],["@JsViewerControlsSelectedBorderColor","#6b6b6b"],["@JsViewerControlsDisabledBorderColor","#e1e1e1"],["@JsViewerFormButtonBackgroundColor","#dddddd"],["@JsViewerFormButtonOverBackgroundColor","#d5d5d5"],["@JsViewerFormButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerFormButtonDisabledBackgroundColor","#fafafa"],["@JsViewerFormBorderColor","#23645c"],["@JsViewerFormBackgroundColor","#f3f2f1"],["@JsViewerFormContainerBackgroundColor","#f3f2f1"],["@JsViewerFormHeaderBackgroundColor","#eeeeee"],["@JsViewerFormHeaderFontColor","#23645c"],["@JsViewerFormSeparatorColor","#aaaaaa"],["@JsViewerFormFontColor","#202020"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#f3f2f1"],["@JsViewerGroupPanelCaptionBorderColor","#aaaaaa"],["@JsViewerGroupPanelCaptionBackgroundColor","#f8f8f8"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMenuFontColor","#202020"],["@JsViewerMainPanelsBorderColor","#aaaaaa"],["@JsViewerHyperlinkButtonFontColor","#5ea69d"],["@JsViewerMainPanelsBackgroundColor","#f3f2f1"],["@JsViewerProgressColorRGB","35,100,92"],["@JsViewerToolbarBackgroundColor","#f3f2f1"],["@JsViewerToolbarFontColor","#202020"],["@JsViewerNavigatePanelFontColor","#ffffff"],["@JsViewerNavigatePanelBackgroundColor","#23645c"],["@JsViewerNavigatePanelSeparatorColor","#a7c1be"],["@JsViewerNavigateButtonOverBackgroundColor","#447b74"],["@JsViewerNavigateButtonSelectedBackgroundColor","#18453f"],["@JsViewerGroupHeaderButtonBackgroundColor","#e5e5e5"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#d5d5d5"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]],"Office2022LightGrayPurple":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#dadada"],["@JsViewerButtonSelectedBackgroundColor","#e5e5e5"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#dadada"],["@JsViewerButtonSelectedBorderColor","#e5e5e5"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerButtonThemeBackgroundColor","#8653a5"],["@JsViewerButtonThemeOverBackgroundColor","#986db2"],["@JsViewerButtonThemeSelectedBackgroundColor","#5c3971"],["@JsViewerButtonThemeDisabledBorderColor","transparent"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#d5e1f2"],["@JsViewerControlsFontSelectedColor","#c2d5f2"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#eaeaea"],["@JsViewerControlsSelectedBackgroundColor","#eaeaea"],["@JsViewerControlsDisabledBackgroundColor","#f7f7f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#6b6b6b"],["@JsViewerControlsSelectedBorderColor","#6b6b6b"],["@JsViewerControlsDisabledBorderColor","#e1e1e1"],["@JsViewerFormButtonBackgroundColor","#dddddd"],["@JsViewerFormButtonOverBackgroundColor","#d5d5d5"],["@JsViewerFormButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerFormButtonDisabledBackgroundColor","#fafafa"],["@JsViewerFormBorderColor","#8653a5"],["@JsViewerFormBackgroundColor","#f3f2f1"],["@JsViewerFormContainerBackgroundColor","#f3f2f1"],["@JsViewerFormHeaderBackgroundColor","#eeeeee"],["@JsViewerFormHeaderFontColor","#8653a5"],["@JsViewerFormSeparatorColor","#aaaaaa"],["@JsViewerFormFontColor","#202020"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#f3f2f1"],["@JsViewerGroupPanelCaptionBorderColor","#aaaaaa"],["@JsViewerGroupPanelCaptionBackgroundColor","#f8f8f8"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMenuFontColor","#202020"],["@JsViewerMainPanelsBorderColor","#aaaaaa"],["@JsViewerHyperlinkButtonFontColor","#9c72b5"],["@JsViewerMainPanelsBackgroundColor","#f3f2f1"],["@JsViewerProgressColorRGB","134,83,165"],["@JsViewerToolbarBackgroundColor","#f3f2f1"],["@JsViewerToolbarFontColor","#202020"],["@JsViewerNavigatePanelFontColor","#ffffff"],["@JsViewerNavigatePanelBackgroundColor","#8653a5"],["@JsViewerNavigatePanelSeparatorColor","#cfbadb"],["@JsViewerNavigateButtonOverBackgroundColor","#5c3971"],["@JsViewerNavigateButtonSelectedBackgroundColor","#11315f"],["@JsViewerGroupHeaderButtonBackgroundColor","#e5e5e5"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#d5d5d5"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]],"Office2022LightGrayOrange":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#dadada"],["@JsViewerButtonSelectedBackgroundColor","#e5e5e5"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#dadada"],["@JsViewerButtonSelectedBorderColor","#e5e5e5"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerButtonThemeBackgroundColor","#b73a1c"],["@JsViewerButtonThemeOverBackgroundColor","#c2573e"],["@JsViewerButtonThemeSelectedBackgroundColor","#7e2813"],["@JsViewerButtonThemeDisabledBorderColor","transparent"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#d5e1f2"],["@JsViewerControlsFontSelectedColor","#c2d5f2"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#eaeaea"],["@JsViewerControlsSelectedBackgroundColor","#eaeaea"],["@JsViewerControlsDisabledBackgroundColor","#f7f7f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#6b6b6b"],["@JsViewerControlsSelectedBorderColor","#6b6b6b"],["@JsViewerControlsDisabledBorderColor","#e1e1e1"],["@JsViewerFormButtonBackgroundColor","#dddddd"],["@JsViewerFormButtonOverBackgroundColor","#d5d5d5"],["@JsViewerFormButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerFormButtonDisabledBackgroundColor","#fafafa"],["@JsViewerFormBorderColor","#b73a1c"],["@JsViewerFormBackgroundColor","#f3f2f1"],["@JsViewerFormContainerBackgroundColor","#f3f2f1"],["@JsViewerFormHeaderBackgroundColor","#eeeeee"],["@JsViewerFormHeaderFontColor","#b73a1c"],["@JsViewerFormSeparatorColor","#aaaaaa"],["@JsViewerFormFontColor","#202020"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#f3f2f1"],["@JsViewerGroupPanelCaptionBorderColor","#aaaaaa"],["@JsViewerGroupPanelCaptionBackgroundColor","#f8f8f8"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMenuFontColor","#202020"],["@JsViewerMainPanelsBorderColor","#aaaaaa"],["@JsViewerHyperlinkButtonFontColor","#c6634b"],["@JsViewerMainPanelsBackgroundColor","#f3f2f1"],["@JsViewerProgressColorRGB","183,58,28"],["@JsViewerToolbarBackgroundColor","#f3f2f1"],["@JsViewerToolbarFontColor","#202020"],["@JsViewerNavigatePanelFontColor","#ffffff"],["@JsViewerNavigatePanelBackgroundColor","#b73a1c"],["@JsViewerNavigatePanelSeparatorColor","#e2b0a4"],["@JsViewerNavigateButtonOverBackgroundColor","#c2573e"],["@JsViewerNavigateButtonSelectedBackgroundColor","#7e2813"],["@JsViewerGroupHeaderButtonBackgroundColor","#e5e5e5"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#d5d5d5"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]],"Office2022LightGrayGreen":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#dadada"],["@JsViewerButtonSelectedBackgroundColor","#e5e5e5"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#dadada"],["@JsViewerButtonSelectedBorderColor","#e5e5e5"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerButtonThemeBackgroundColor","#0b6433"],["@JsViewerButtonThemeOverBackgroundColor","#2f7b51"],["@JsViewerButtonThemeSelectedBackgroundColor","#084523"],["@JsViewerButtonThemeDisabledBorderColor","transparent"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#d5e1f2"],["@JsViewerControlsFontSelectedColor","#c2d5f2"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#eaeaea"],["@JsViewerControlsSelectedBackgroundColor","#eaeaea"],["@JsViewerControlsDisabledBackgroundColor","#f7f7f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#6b6b6b"],["@JsViewerControlsSelectedBorderColor","#6b6b6b"],["@JsViewerControlsDisabledBorderColor","#e1e1e1"],["@JsViewerFormButtonBackgroundColor","#dddddd"],["@JsViewerFormButtonOverBackgroundColor","#d5d5d5"],["@JsViewerFormButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerFormButtonDisabledBackgroundColor","#fafafa"],["@JsViewerFormBorderColor","#0b6433"],["@JsViewerFormBackgroundColor","#f3f2f1"],["@JsViewerFormContainerBackgroundColor","#f3f2f1"],["@JsViewerFormHeaderBackgroundColor","#eeeeee"],["@JsViewerFormHeaderFontColor","#0b6433"],["@JsViewerFormSeparatorColor","#aaaaaa"],["@JsViewerFormFontColor","#202020"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#f3f2f1"],["@JsViewerGroupPanelCaptionBorderColor","#aaaaaa"],["@JsViewerGroupPanelCaptionBackgroundColor","#f8f8f8"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMenuFontColor","#202020"],["@JsViewerMainPanelsBorderColor","#aaaaaa"],["@JsViewerHyperlinkButtonFontColor","#6fa386"],["@JsViewerMainPanelsBackgroundColor","#f3f2f1"],["@JsViewerProgressColorRGB","11,100,51"],["@JsViewerToolbarBackgroundColor","#f3f2f1"],["@JsViewerToolbarFontColor","#202020"],["@JsViewerNavigatePanelFontColor","#ffffff"],["@JsViewerNavigatePanelBackgroundColor","#0b6433"],["@JsViewerNavigatePanelSeparatorColor","#9dc1ad"],["@JsViewerNavigateButtonOverBackgroundColor","#2f7b51"],["@JsViewerNavigateButtonSelectedBackgroundColor","#084523"],["@JsViewerGroupHeaderButtonBackgroundColor","#e5e5e5"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#d5d5d5"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]],"Office2022LightGrayCarmine":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#dadada"],["@JsViewerButtonSelectedBackgroundColor","#e5e5e5"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#dadada"],["@JsViewerButtonSelectedBorderColor","#e5e5e5"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerButtonThemeBackgroundColor","#912c2f"],["@JsViewerButtonThemeOverBackgroundColor","#a14b4e"],["@JsViewerButtonThemeSelectedBackgroundColor","#641e20"],["@JsViewerButtonThemeDisabledBorderColor","transparent"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#d5e1f2"],["@JsViewerControlsFontSelectedColor","#c2d5f2"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#eaeaea"],["@JsViewerControlsSelectedBackgroundColor","#eaeaea"],["@JsViewerControlsDisabledBackgroundColor","#f7f7f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#6b6b6b"],["@JsViewerControlsSelectedBorderColor","#6b6b6b"],["@JsViewerControlsDisabledBorderColor","#e1e1e1"],["@JsViewerFormButtonBackgroundColor","#dddddd"],["@JsViewerFormButtonOverBackgroundColor","#d5d5d5"],["@JsViewerFormButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerFormButtonDisabledBackgroundColor","#fafafa"],["@JsViewerFormBorderColor","#912c2f"],["@JsViewerFormBackgroundColor","#f3f2f1"],["@JsViewerFormContainerBackgroundColor","#f3f2f1"],["@JsViewerFormHeaderBackgroundColor","#eeeeee"],["@JsViewerFormHeaderFontColor","#912c2f"],["@JsViewerFormSeparatorColor","#aaaaaa"],["@JsViewerFormFontColor","#202020"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#f3f2f1"],["@JsViewerGroupPanelCaptionBorderColor","#aaaaaa"],["@JsViewerGroupPanelCaptionBackgroundColor","#f8f8f8"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMenuFontColor","#202020"],["@JsViewerMainPanelsBorderColor","#aaaaaa"],["@JsViewerHyperlinkButtonFontColor","#c75659"],["@JsViewerMainPanelsBackgroundColor","#f3f2f1"],["@JsViewerProgressColorRGB","145,44,47"],["@JsViewerToolbarBackgroundColor","#f3f2f1"],["@JsViewerToolbarFontColor","#202020"],["@JsViewerNavigatePanelFontColor","#ffffff"],["@JsViewerNavigatePanelBackgroundColor","#912c2f"],["@JsViewerNavigatePanelSeparatorColor","#d3abac"],["@JsViewerNavigateButtonOverBackgroundColor","#a14b4e"],["@JsViewerNavigateButtonSelectedBackgroundColor","#641e20"],["@JsViewerGroupHeaderButtonBackgroundColor","#e5e5e5"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#d5d5d5"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]],"Office2022LightGrayBlue":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#dadada"],["@JsViewerButtonSelectedBackgroundColor","#e5e5e5"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#dadada"],["@JsViewerButtonSelectedBorderColor","#e5e5e5"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerButtonThemeBackgroundColor","#19478a"],["@JsViewerButtonThemeOverBackgroundColor","#3b629b"],["@JsViewerButtonThemeSelectedBackgroundColor","#11315f"],["@JsViewerButtonThemeDisabledBorderColor","transparent"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#d5e1f2"],["@JsViewerControlsFontSelectedColor","#c2d5f2"],["@JsViewerControlsFontDisabledColor","#d6d3d6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#eaeaea"],["@JsViewerControlsSelectedBackgroundColor","#eaeaea"],["@JsViewerControlsDisabledBackgroundColor","#f7f7f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#6b6b6b"],["@JsViewerControlsSelectedBorderColor","#6b6b6b"],["@JsViewerControlsDisabledBorderColor","#e1e1e1"],["@JsViewerFormButtonBackgroundColor","#dddddd"],["@JsViewerFormButtonOverBackgroundColor","#d5d5d5"],["@JsViewerFormButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerFormButtonDisabledBackgroundColor","#fafafa"],["@JsViewerFormBorderColor","#19478a"],["@JsViewerFormBackgroundColor","#f3f2f1"],["@JsViewerFormContainerBackgroundColor","#f3f2f1"],["@JsViewerFormHeaderBackgroundColor","#eeeeee"],["@JsViewerFormHeaderFontColor","#19478a"],["@JsViewerFormSeparatorColor","#aaaaaa"],["@JsViewerFormFontColor","#202020"],["@JsViewerGroupPanelBorderColor","#ababab"],["@JsViewerGroupPanelBackgroundColor","#f3f2f1"],["@JsViewerGroupPanelCaptionBorderColor","#aaaaaa"],["@JsViewerGroupPanelCaptionBackgroundColor","#f8f8f8"],["@JsViewerMenuBorderColor","#ababab"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuSeparatorColor","#d4d4d4"],["@JsViewerMenuFontColor","#202020"],["@JsViewerMainPanelsBorderColor","#aaaaaa"],["@JsViewerHyperlinkButtonFontColor","#5a83ad"],["@JsViewerMainPanelsBackgroundColor","#f3f2f1"],["@JsViewerProgressColorRGB","25,71,138"],["@JsViewerToolbarBackgroundColor","#f3f2f1"],["@JsViewerToolbarFontColor","#202020"],["@JsViewerNavigatePanelFontColor","#ffffff"],["@JsViewerNavigatePanelBackgroundColor","#19478a"],["@JsViewerNavigatePanelSeparatorColor","#a3b5d0"],["@JsViewerNavigateButtonOverBackgroundColor","#3b629b"],["@JsViewerNavigateButtonSelectedBackgroundColor","#11315f"],["@JsViewerGroupHeaderButtonBackgroundColor","#e5e5e5"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#d5d5d5"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#d1d1d1"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#fafafa"]],"Office2022DarkGrayViolet":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#cacaca"],["@JsViewerButtonSelectedBackgroundColor","#bcbcbc"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#cacaca"],["@JsViewerButtonSelectedBorderColor","#bcbcbc"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerButtonThemeBackgroundColor","#6d3069"],["@JsViewerButtonThemeOverBackgroundColor","#834f7f"],["@JsViewerButtonThemeSelectedBackgroundColor","#4b2148"],["@JsViewerButtonThemeDisabledBorderColor","transparent"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#444444"],["@JsViewerControlsFontSelectedColor","#444444"],["@JsViewerControlsFontDisabledColor","#bcbcbc"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#c0c0c0"],["@JsViewerControlsSelectedBackgroundColor","#cacaca"],["@JsViewerControlsDisabledBackgroundColor","#d0d0d0"],["@JsViewerControlsBorderColor","#999999"],["@JsViewerControlsOverBorderColor","#6b6b6b"],["@JsViewerControlsSelectedBorderColor","#6b6b6b"],["@JsViewerControlsDisabledBorderColor","#d0d0d0"],["@JsViewerFormButtonBackgroundColor","#bcbcbc"],["@JsViewerFormButtonOverBackgroundColor","#cacaca"],["@JsViewerFormButtonSelectedBackgroundColor","#c0c0c0"],["@JsViewerFormButtonDisabledBackgroundColor","#d0d0d0"],["@JsViewerFormBorderColor","#6d3069"],["@JsViewerFormBackgroundColor","#f0f0f0"],["@JsViewerFormContainerBackgroundColor","#f0f0f0"],["@JsViewerFormHeaderBackgroundColor","#dedede"],["@JsViewerFormHeaderFontColor","#6d3069"],["@JsViewerFormSeparatorColor","#999999"],["@JsViewerFormFontColor","#444444"],["@JsViewerGroupPanelBorderColor","#6d3069"],["@JsViewerGroupPanelBackgroundColor","#f0f0f0"],["@JsViewerGroupPanelCaptionBorderColor","#6d3069"],["@JsViewerGroupPanelCaptionBackgroundColor","#dedede"],["@JsViewerMenuBorderColor","#999999"],["@JsViewerMenuBackgroundColor","#f0f0f0"],["@JsViewerMenuSeparatorColor","#999999"],["@JsViewerMenuFontColor","#444444"],["@JsViewerMainPanelsBorderColor","#999999"],["@JsViewerHyperlinkButtonFontColor","#8c5c89"],["@JsViewerMainPanelsBackgroundColor","#dedede"],["@JsViewerProgressColorRGB","109,48,105"],["@JsViewerToolbarBackgroundColor","#dedede"],["@JsViewerToolbarFontColor","#444444"],["@JsViewerNavigatePanelFontColor","#ffffff"],["@JsViewerNavigatePanelBackgroundColor","#6d3069"],["@JsViewerNavigatePanelSeparatorColor","#c5acc3"],["@JsViewerNavigateButtonOverBackgroundColor","#834f7f"],["@JsViewerNavigateButtonSelectedBackgroundColor","#4b2148"],["@JsViewerGroupHeaderButtonBackgroundColor","#cacaca"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#bcbcbc"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#c0c0c0"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#d0d0d0"]],"Office2022DarkGrayTeal":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#cacaca"],["@JsViewerButtonSelectedBackgroundColor","#bcbcbc"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#cacaca"],["@JsViewerButtonSelectedBorderColor","#bcbcbc"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerButtonThemeBackgroundColor","#19478a"],["@JsViewerButtonThemeOverBackgroundColor","#3b629b"],["@JsViewerButtonThemeSelectedBackgroundColor","#11315f"],["@JsViewerButtonThemeDisabledBorderColor","transparent"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#444444"],["@JsViewerControlsFontSelectedColor","#444444"],["@JsViewerControlsFontDisabledColor","#bcbcbc"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#c0c0c0"],["@JsViewerControlsSelectedBackgroundColor","#cacaca"],["@JsViewerControlsDisabledBackgroundColor","#d0d0d0"],["@JsViewerControlsBorderColor","#999999"],["@JsViewerControlsOverBorderColor","#6b6b6b"],["@JsViewerControlsSelectedBorderColor","#6b6b6b"],["@JsViewerControlsDisabledBorderColor","#d0d0d0"],["@JsViewerFormButtonBackgroundColor","#bcbcbc"],["@JsViewerFormButtonOverBackgroundColor","#cacaca"],["@JsViewerFormButtonSelectedBackgroundColor","#c0c0c0"],["@JsViewerFormButtonDisabledBackgroundColor","#d0d0d0"],["@JsViewerFormBorderColor","#23645c"],["@JsViewerFormBackgroundColor","#f0f0f0"],["@JsViewerFormContainerBackgroundColor","#f0f0f0"],["@JsViewerFormHeaderBackgroundColor","#dedede"],["@JsViewerFormHeaderFontColor","#23645c"],["@JsViewerFormSeparatorColor","#999999"],["@JsViewerFormFontColor","#444444"],["@JsViewerGroupPanelBorderColor","#23645c"],["@JsViewerGroupPanelBackgroundColor","#f0f0f0"],["@JsViewerGroupPanelCaptionBorderColor","#23645c"],["@JsViewerGroupPanelCaptionBackgroundColor","#dedede"],["@JsViewerMenuBorderColor","#999999"],["@JsViewerMenuBackgroundColor","#f0f0f0"],["@JsViewerMenuSeparatorColor","#999999"],["@JsViewerMenuFontColor","#444444"],["@JsViewerMainPanelsBorderColor","#999999"],["@JsViewerHyperlinkButtonFontColor","#5ea69d"],["@JsViewerMainPanelsBackgroundColor","#dedede"],["@JsViewerProgressColorRGB","35,100,92"],["@JsViewerToolbarBackgroundColor","#dedede"],["@JsViewerToolbarFontColor","#444444"],["@JsViewerNavigatePanelFontColor","#ffffff"],["@JsViewerNavigatePanelBackgroundColor","#23645c"],["@JsViewerNavigatePanelSeparatorColor","#a7c1be"],["@JsViewerNavigateButtonOverBackgroundColor","#447b74"],["@JsViewerNavigateButtonSelectedBackgroundColor","#18453f"],["@JsViewerGroupHeaderButtonBackgroundColor","#cacaca"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#bcbcbc"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#c0c0c0"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#d0d0d0"]],"Office2022DarkGrayPurple":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#cacaca"],["@JsViewerButtonSelectedBackgroundColor","#bcbcbc"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#cacaca"],["@JsViewerButtonSelectedBorderColor","#bcbcbc"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerButtonThemeBackgroundColor","#8653a5"],["@JsViewerButtonThemeOverBackgroundColor","#986db2"],["@JsViewerButtonThemeSelectedBackgroundColor","#5c3971"],["@JsViewerButtonThemeDisabledBorderColor","transparent"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#444444"],["@JsViewerControlsFontSelectedColor","#444444"],["@JsViewerControlsFontDisabledColor","#bcbcbc"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#c0c0c0"],["@JsViewerControlsSelectedBackgroundColor","#cacaca"],["@JsViewerControlsDisabledBackgroundColor","#d0d0d0"],["@JsViewerControlsBorderColor","#999999"],["@JsViewerControlsOverBorderColor","#6b6b6b"],["@JsViewerControlsSelectedBorderColor","#6b6b6b"],["@JsViewerControlsDisabledBorderColor","#d0d0d0"],["@JsViewerFormButtonBackgroundColor","#bcbcbc"],["@JsViewerFormButtonOverBackgroundColor","#cacaca"],["@JsViewerFormButtonSelectedBackgroundColor","#c0c0c0"],["@JsViewerFormButtonDisabledBackgroundColor","#d0d0d0"],["@JsViewerFormBorderColor","#8653a5"],["@JsViewerFormBackgroundColor","#f0f0f0"],["@JsViewerFormContainerBackgroundColor","#f0f0f0"],["@JsViewerFormHeaderBackgroundColor","#dedede"],["@JsViewerFormHeaderFontColor","#8653a5"],["@JsViewerFormSeparatorColor","#999999"],["@JsViewerFormFontColor","#444444"],["@JsViewerGroupPanelBorderColor","#8653a5"],["@JsViewerGroupPanelBackgroundColor","#f0f0f0"],["@JsViewerGroupPanelCaptionBorderColor","#8653a5"],["@JsViewerGroupPanelCaptionBackgroundColor","#dedede"],["@JsViewerMenuBorderColor","#999999"],["@JsViewerMenuBackgroundColor","#f0f0f0"],["@JsViewerMenuSeparatorColor","#999999"],["@JsViewerMenuFontColor","#444444"],["@JsViewerMainPanelsBorderColor","#999999"],["@JsViewerHyperlinkButtonFontColor","#9c72b5"],["@JsViewerMainPanelsBackgroundColor","#dedede"],["@JsViewerProgressColorRGB","134,83,165"],["@JsViewerToolbarBackgroundColor","#dedede"],["@JsViewerToolbarFontColor","#444444"],["@JsViewerNavigatePanelFontColor","#ffffff"],["@JsViewerNavigatePanelBackgroundColor","#8653a5"],["@JsViewerNavigatePanelSeparatorColor","#cfbadb"],["@JsViewerNavigateButtonOverBackgroundColor","#986db2"],["@JsViewerNavigateButtonSelectedBackgroundColor","#5c3971"],["@JsViewerGroupHeaderButtonBackgroundColor","#cacaca"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#bcbcbc"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#c0c0c0"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#d0d0d0"]],"Office2022DarkGrayOrange":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#cacaca"],["@JsViewerButtonSelectedBackgroundColor","#bcbcbc"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#cacaca"],["@JsViewerButtonSelectedBorderColor","#bcbcbc"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerButtonThemeBackgroundColor","#b73a1c"],["@JsViewerButtonThemeOverBackgroundColor","#c2573e"],["@JsViewerButtonThemeSelectedBackgroundColor","#7e2813"],["@JsViewerButtonThemeDisabledBorderColor","transparent"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#444444"],["@JsViewerControlsFontSelectedColor","#444444"],["@JsViewerControlsFontDisabledColor","#bcbcbc"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#c0c0c0"],["@JsViewerControlsSelectedBackgroundColor","#cacaca"],["@JsViewerControlsDisabledBackgroundColor","#d0d0d0"],["@JsViewerControlsBorderColor","#999999"],["@JsViewerControlsOverBorderColor","#6b6b6b"],["@JsViewerControlsSelectedBorderColor","#6b6b6b"],["@JsViewerControlsDisabledBorderColor","#d0d0d0"],["@JsViewerFormButtonBackgroundColor","#bcbcbc"],["@JsViewerFormButtonOverBackgroundColor","#cacaca"],["@JsViewerFormButtonSelectedBackgroundColor","#c0c0c0"],["@JsViewerFormButtonDisabledBackgroundColor","#d0d0d0"],["@JsViewerFormBorderColor","#b73a1c"],["@JsViewerFormBackgroundColor","#f0f0f0"],["@JsViewerFormContainerBackgroundColor","#f0f0f0"],["@JsViewerFormHeaderBackgroundColor","#dedede"],["@JsViewerFormHeaderFontColor","#b73a1c"],["@JsViewerFormSeparatorColor","#999999"],["@JsViewerFormFontColor","#444444"],["@JsViewerGroupPanelBorderColor","#b73a1c"],["@JsViewerGroupPanelBackgroundColor","#f0f0f0"],["@JsViewerGroupPanelCaptionBorderColor","#b73a1c"],["@JsViewerGroupPanelCaptionBackgroundColor","#dedede"],["@JsViewerMenuBorderColor","#999999"],["@JsViewerMenuBackgroundColor","#f0f0f0"],["@JsViewerMenuSeparatorColor","#999999"],["@JsViewerMenuFontColor","#444444"],["@JsViewerMainPanelsBorderColor","#999999"],["@JsViewerHyperlinkButtonFontColor","#c6634b"],["@JsViewerMainPanelsBackgroundColor","#dedede"],["@JsViewerProgressColorRGB","183,58,28"],["@JsViewerToolbarBackgroundColor","#dedede"],["@JsViewerToolbarFontColor","#444444"],["@JsViewerNavigatePanelFontColor","#ffffff"],["@JsViewerNavigatePanelBackgroundColor","#b73a1c"],["@JsViewerNavigatePanelSeparatorColor","#e2b0a4"],["@JsViewerNavigateButtonOverBackgroundColor","#c2573e"],["@JsViewerNavigateButtonSelectedBackgroundColor","#7e2813"],["@JsViewerGroupHeaderButtonBackgroundColor","#cacaca"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#bcbcbc"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#c0c0c0"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#d0d0d0"]],"Office2022DarkGrayGreen":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#cacaca"],["@JsViewerButtonSelectedBackgroundColor","#bcbcbc"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#cacaca"],["@JsViewerButtonSelectedBorderColor","#bcbcbc"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerButtonThemeBackgroundColor","#0b6433"],["@JsViewerButtonThemeOverBackgroundColor","#2f7b51"],["@JsViewerButtonThemeSelectedBackgroundColor","#084523"],["@JsViewerButtonThemeDisabledBorderColor","transparent"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#444444"],["@JsViewerControlsFontSelectedColor","#444444"],["@JsViewerControlsFontDisabledColor","#bcbcbc"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#c0c0c0"],["@JsViewerControlsSelectedBackgroundColor","#cacaca"],["@JsViewerControlsDisabledBackgroundColor","#d0d0d0"],["@JsViewerControlsBorderColor","#999999"],["@JsViewerControlsOverBorderColor","#6b6b6b"],["@JsViewerControlsSelectedBorderColor","#6b6b6b"],["@JsViewerControlsDisabledBorderColor","#d0d0d0"],["@JsViewerFormButtonBackgroundColor","#bcbcbc"],["@JsViewerFormButtonOverBackgroundColor","#cacaca"],["@JsViewerFormButtonSelectedBackgroundColor","#c0c0c0"],["@JsViewerFormButtonDisabledBackgroundColor","#d0d0d0"],["@JsViewerFormBorderColor","#0b6433"],["@JsViewerFormBackgroundColor","#f0f0f0"],["@JsViewerFormContainerBackgroundColor","#f0f0f0"],["@JsViewerFormHeaderBackgroundColor","#dedede"],["@JsViewerFormHeaderFontColor","#0b6433"],["@JsViewerFormSeparatorColor","#999999"],["@JsViewerFormFontColor","#444444"],["@JsViewerGroupPanelBorderColor","#0b6433"],["@JsViewerGroupPanelBackgroundColor","#f0f0f0"],["@JsViewerGroupPanelCaptionBorderColor","#0b6433"],["@JsViewerGroupPanelCaptionBackgroundColor","#dedede"],["@JsViewerMenuBorderColor","#999999"],["@JsViewerMenuBackgroundColor","#f0f0f0"],["@JsViewerMenuSeparatorColor","#999999"],["@JsViewerMenuFontColor","#444444"],["@JsViewerMainPanelsBorderColor","#999999"],["@JsViewerHyperlinkButtonFontColor","#6fa386"],["@JsViewerMainPanelsBackgroundColor","#dedede"],["@JsViewerProgressColorRGB","11,100,51"],["@JsViewerToolbarBackgroundColor","#dedede"],["@JsViewerToolbarFontColor","#444444"],["@JsViewerNavigatePanelFontColor","#ffffff"],["@JsViewerNavigatePanelBackgroundColor","#0b6433"],["@JsViewerNavigatePanelSeparatorColor","#9dc1ad"],["@JsViewerNavigateButtonOverBackgroundColor","#2f7b51"],["@JsViewerNavigateButtonSelectedBackgroundColor","#084523"],["@JsViewerGroupHeaderButtonBackgroundColor","#cacaca"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#bcbcbc"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#c0c0c0"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#d0d0d0"]],"Office2022DarkGrayCarmine":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#cacaca"],["@JsViewerButtonSelectedBackgroundColor","#bcbcbc"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#cacaca"],["@JsViewerButtonSelectedBorderColor","#bcbcbc"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerButtonThemeBackgroundColor","#912c2f"],["@JsViewerButtonThemeOverBackgroundColor","#a14b4e"],["@JsViewerButtonThemeSelectedBackgroundColor","#641e20"],["@JsViewerButtonThemeDisabledBorderColor","transparent"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#444444"],["@JsViewerControlsFontSelectedColor","#444444"],["@JsViewerControlsFontDisabledColor","#bcbcbc"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#c0c0c0"],["@JsViewerControlsSelectedBackgroundColor","#cacaca"],["@JsViewerControlsDisabledBackgroundColor","#d0d0d0"],["@JsViewerControlsBorderColor","#999999"],["@JsViewerControlsOverBorderColor","#6b6b6b"],["@JsViewerControlsSelectedBorderColor","#6b6b6b"],["@JsViewerControlsDisabledBorderColor","#d0d0d0"],["@JsViewerFormButtonBackgroundColor","#bcbcbc"],["@JsViewerFormButtonOverBackgroundColor","#cacaca"],["@JsViewerFormButtonSelectedBackgroundColor","#c0c0c0"],["@JsViewerFormButtonDisabledBackgroundColor","#d0d0d0"],["@JsViewerFormBorderColor","#912c2f"],["@JsViewerFormBackgroundColor","#f0f0f0"],["@JsViewerFormContainerBackgroundColor","#f0f0f0"],["@JsViewerFormHeaderBackgroundColor","#dedede"],["@JsViewerFormHeaderFontColor","#912c2f"],["@JsViewerFormSeparatorColor","#999999"],["@JsViewerFormFontColor","#444444"],["@JsViewerGroupPanelBorderColor","#912c2f"],["@JsViewerGroupPanelBackgroundColor","#f0f0f0"],["@JsViewerGroupPanelCaptionBorderColor","#912c2f"],["@JsViewerGroupPanelCaptionBackgroundColor","#dedede"],["@JsViewerMenuBorderColor","#999999"],["@JsViewerMenuBackgroundColor","#f0f0f0"],["@JsViewerMenuSeparatorColor","#999999"],["@JsViewerMenuFontColor","#444444"],["@JsViewerMainPanelsBorderColor","#999999"],["@JsViewerHyperlinkButtonFontColor","#c75659"],["@JsViewerMainPanelsBackgroundColor","#dedede"],["@JsViewerProgressColorRGB","145,44,47"],["@JsViewerToolbarBackgroundColor","#dedede"],["@JsViewerToolbarFontColor","#444444"],["@JsViewerNavigatePanelFontColor","#ffffff"],["@JsViewerNavigatePanelBackgroundColor","#912c2f"],["@JsViewerNavigatePanelSeparatorColor","#d3abac"],["@JsViewerNavigateButtonOverBackgroundColor","#a14b4e"],["@JsViewerNavigateButtonSelectedBackgroundColor","#641e20"],["@JsViewerGroupHeaderButtonBackgroundColor","#cacaca"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#bcbcbc"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#c0c0c0"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#d0d0d0"]],"Office2022DarkGrayBlue":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#cacaca"],["@JsViewerButtonSelectedBackgroundColor","#bcbcbc"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#cacaca"],["@JsViewerButtonSelectedBorderColor","#bcbcbc"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerButtonThemeBackgroundColor","#19478a"],["@JsViewerButtonThemeOverBackgroundColor","#3b629b"],["@JsViewerButtonThemeSelectedBackgroundColor","#11315f"],["@JsViewerButtonThemeDisabledBorderColor","transparent"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#444444"],["@JsViewerControlsFontSelectedColor","#444444"],["@JsViewerControlsFontDisabledColor","#bcbcbc"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#c0c0c0"],["@JsViewerControlsSelectedBackgroundColor","#cacaca"],["@JsViewerControlsDisabledBackgroundColor","#d0d0d0"],["@JsViewerControlsBorderColor","#999999"],["@JsViewerControlsOverBorderColor","#6b6b6b"],["@JsViewerControlsSelectedBorderColor","#6b6b6b"],["@JsViewerControlsDisabledBorderColor","#d0d0d0"],["@JsViewerFormButtonBackgroundColor","#bcbcbc"],["@JsViewerFormButtonOverBackgroundColor","#cacaca"],["@JsViewerFormButtonSelectedBackgroundColor","#c0c0c0"],["@JsViewerFormButtonDisabledBackgroundColor","#d0d0d0"],["@JsViewerFormBorderColor","#19478a"],["@JsViewerFormBackgroundColor","#f0f0f0"],["@JsViewerFormContainerBackgroundColor","#f0f0f0"],["@JsViewerFormHeaderBackgroundColor","#dedede"],["@JsViewerFormHeaderFontColor","#19478a"],["@JsViewerFormSeparatorColor","#999999"],["@JsViewerFormFontColor","#444444"],["@JsViewerGroupPanelBorderColor","#19478a"],["@JsViewerGroupPanelBackgroundColor","#f0f0f0"],["@JsViewerGroupPanelCaptionBorderColor","#19478a"],["@JsViewerGroupPanelCaptionBackgroundColor","#dedede"],["@JsViewerMenuBorderColor","#999999"],["@JsViewerMenuBackgroundColor","#f0f0f0"],["@JsViewerMenuSeparatorColor","#999999"],["@JsViewerMenuFontColor","#444444"],["@JsViewerMainPanelsBorderColor","#999999"],["@JsViewerHyperlinkButtonFontColor","#5a83ad"],["@JsViewerMainPanelsBackgroundColor","#dedede"],["@JsViewerProgressColorRGB","25,71,138"],["@JsViewerToolbarBackgroundColor","#dedede"],["@JsViewerToolbarFontColor","#444444"],["@JsViewerNavigatePanelFontColor","#ffffff"],["@JsViewerNavigatePanelBackgroundColor","#19478a"],["@JsViewerNavigatePanelSeparatorColor","#a3b5d0"],["@JsViewerNavigateButtonOverBackgroundColor","#3b629b"],["@JsViewerNavigateButtonSelectedBackgroundColor","#11315f"],["@JsViewerGroupHeaderButtonBackgroundColor","#cacaca"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#bcbcbc"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#c0c0c0"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#d0d0d0"]],"Office2022BlackViolet":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#555555"],["@JsViewerButtonSelectedBackgroundColor","#4e4e4e"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#a2a2a2"],["@JsViewerButtonSelectedBorderColor","#999999"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerButtonThemeBackgroundColor","#6d3069"],["@JsViewerButtonThemeOverBackgroundColor","#834f7f"],["@JsViewerButtonThemeSelectedBackgroundColor","#4b2148"],["@JsViewerButtonThemeDisabledBorderColor","transparent"],["@JsViewerControlsFontColor","#ffffff"],["@JsViewerControlsFontOverColor","#19478a"],["@JsViewerControlsFontSelectedColor","#19478a"],["@JsViewerControlsFontDisabledColor","#6e6e6e"],["@JsViewerControlsBackgroundColor","#2b2b2b"],["@JsViewerControlsOverBackgroundColor","#5e5e5e"],["@JsViewerControlsSelectedBackgroundColor","#7e7e7e"],["@JsViewerControlsDisabledBackgroundColor","#2b2b2b"],["@JsViewerControlsBorderColor","#bebebe"],["@JsViewerControlsOverBorderColor","#a2a2a2"],["@JsViewerControlsSelectedBorderColor","#999999"],["@JsViewerControlsDisabledBorderColor","#4e4e4e"],["@JsViewerFormButtonBackgroundColor","#4e4e4e"],["@JsViewerFormButtonOverBackgroundColor","#555555"],["@JsViewerFormButtonSelectedBackgroundColor","#404040"],["@JsViewerFormButtonDisabledBackgroundColor","#444444"],["@JsViewerFormBorderColor","#bebebe"],["@JsViewerFormBackgroundColor","#333333"],["@JsViewerFormContainerBackgroundColor","#333333"],["@JsViewerFormHeaderBackgroundColor","#4e4e4e"],["@JsViewerFormHeaderFontColor","#ffffff"],["@JsViewerFormSeparatorColor","#4b4b4b"],["@JsViewerFormFontColor","#bebebe"],["@JsViewerGroupPanelBorderColor","#bebebe"],["@JsViewerGroupPanelBackgroundColor","#333333"],["@JsViewerGroupPanelCaptionBorderColor","#bebebe"],["@JsViewerGroupPanelCaptionBackgroundColor","#333333"],["@JsViewerMenuBorderColor","#bebebe"],["@JsViewerMenuBackgroundColor","#333333"],["@JsViewerMenuSeparatorColor","#4b4b4b"],["@JsViewerMenuFontColor","#ffffff"],["@JsViewerMainPanelsBorderColor","#4b4b4b"],["@JsViewerHyperlinkButtonFontColor","#8c5c89"],["@JsViewerMainPanelsBackgroundColor","#333333"],["@JsViewerProgressColorRGB","109,48,105"],["@JsViewerToolbarBackgroundColor","#333333"],["@JsViewerToolbarFontColor","#ffffff"],["@JsViewerNavigatePanelFontColor","#ffffff"],["@JsViewerNavigatePanelBackgroundColor","#333333"],["@JsViewerNavigatePanelSeparatorColor","#4b4b4b"],["@JsViewerNavigateButtonOverBackgroundColor","#555555"],["@JsViewerNavigateButtonSelectedBackgroundColor","#404040"],["@JsViewerGroupHeaderButtonBackgroundColor","#4e4e4e"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#555555"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#404040"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#444444"]],"Office2022BlackTeal":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#555555"],["@JsViewerButtonSelectedBackgroundColor","#4e4e4e"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#a2a2a2"],["@JsViewerButtonSelectedBorderColor","#999999"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerButtonThemeBackgroundColor","#19478a"],["@JsViewerButtonThemeOverBackgroundColor","#3b629b"],["@JsViewerButtonThemeSelectedBackgroundColor","#11315f"],["@JsViewerButtonThemeDisabledBorderColor","transparent"],["@JsViewerControlsFontColor","#ffffff"],["@JsViewerControlsFontOverColor","#19478a"],["@JsViewerControlsFontSelectedColor","#19478a"],["@JsViewerControlsFontDisabledColor","#6e6e6e"],["@JsViewerControlsBackgroundColor","#2b2b2b"],["@JsViewerControlsOverBackgroundColor","#5e5e5e"],["@JsViewerControlsSelectedBackgroundColor","#7e7e7e"],["@JsViewerControlsDisabledBackgroundColor","#2b2b2b"],["@JsViewerControlsBorderColor","#bebebe"],["@JsViewerControlsOverBorderColor","#a2a2a2"],["@JsViewerControlsSelectedBorderColor","#999999"],["@JsViewerControlsDisabledBorderColor","#4e4e4e"],["@JsViewerFormButtonBackgroundColor","#4e4e4e"],["@JsViewerFormButtonOverBackgroundColor","#555555"],["@JsViewerFormButtonSelectedBackgroundColor","#404040"],["@JsViewerFormButtonDisabledBackgroundColor","#444444"],["@JsViewerFormBorderColor","#bebebe"],["@JsViewerFormBackgroundColor","#333333"],["@JsViewerFormContainerBackgroundColor","#333333"],["@JsViewerFormHeaderBackgroundColor","#4e4e4e"],["@JsViewerFormHeaderFontColor","#ffffff"],["@JsViewerFormSeparatorColor","#4b4b4b"],["@JsViewerFormFontColor","#bebebe"],["@JsViewerGroupPanelBorderColor","#bebebe"],["@JsViewerGroupPanelBackgroundColor","#333333"],["@JsViewerGroupPanelCaptionBorderColor","#bebebe"],["@JsViewerGroupPanelCaptionBackgroundColor","#333333"],["@JsViewerMenuBorderColor","#bebebe"],["@JsViewerMenuBackgroundColor","#333333"],["@JsViewerMenuSeparatorColor","#4b4b4b"],["@JsViewerMenuFontColor","#ffffff"],["@JsViewerMainPanelsBorderColor","#4b4b4b"],["@JsViewerHyperlinkButtonFontColor","#5ea69d"],["@JsViewerMainPanelsBackgroundColor","#333333"],["@JsViewerProgressColorRGB","35,100,92"],["@JsViewerToolbarBackgroundColor","#333333"],["@JsViewerToolbarFontColor","#ffffff"],["@JsViewerNavigatePanelFontColor","#ffffff"],["@JsViewerNavigatePanelBackgroundColor","#333333"],["@JsViewerNavigatePanelSeparatorColor","#4b4b4b"],["@JsViewerNavigateButtonOverBackgroundColor","#555555"],["@JsViewerNavigateButtonSelectedBackgroundColor","#404040"],["@JsViewerGroupHeaderButtonBackgroundColor","#4e4e4e"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#555555"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#404040"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#444444"]],"Office2022BlackPurple":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#555555"],["@JsViewerButtonSelectedBackgroundColor","#4e4e4e"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#a2a2a2"],["@JsViewerButtonSelectedBorderColor","#999999"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerButtonThemeBackgroundColor","#8653a5"],["@JsViewerButtonThemeOverBackgroundColor","#986db2"],["@JsViewerButtonThemeSelectedBackgroundColor","#5c3971"],["@JsViewerButtonThemeDisabledBorderColor","transparent"],["@JsViewerControlsFontColor","#ffffff"],["@JsViewerControlsFontOverColor","#19478a"],["@JsViewerControlsFontSelectedColor","#19478a"],["@JsViewerControlsFontDisabledColor","#6e6e6e"],["@JsViewerControlsBackgroundColor","#2b2b2b"],["@JsViewerControlsOverBackgroundColor","#5e5e5e"],["@JsViewerControlsSelectedBackgroundColor","#7e7e7e"],["@JsViewerControlsDisabledBackgroundColor","#2b2b2b"],["@JsViewerControlsBorderColor","#bebebe"],["@JsViewerControlsOverBorderColor","#a2a2a2"],["@JsViewerControlsSelectedBorderColor","#999999"],["@JsViewerControlsDisabledBorderColor","#4e4e4e"],["@JsViewerFormButtonBackgroundColor","#4e4e4e"],["@JsViewerFormButtonOverBackgroundColor","#555555"],["@JsViewerFormButtonSelectedBackgroundColor","#404040"],["@JsViewerFormButtonDisabledBackgroundColor","#444444"],["@JsViewerFormBorderColor","#bebebe"],["@JsViewerFormBackgroundColor","#333333"],["@JsViewerFormContainerBackgroundColor","#333333"],["@JsViewerFormHeaderBackgroundColor","#4e4e4e"],["@JsViewerFormHeaderFontColor","#ffffff"],["@JsViewerFormSeparatorColor","#4b4b4b"],["@JsViewerFormFontColor","#bebebe"],["@JsViewerGroupPanelBorderColor","#bebebe"],["@JsViewerGroupPanelBackgroundColor","#333333"],["@JsViewerGroupPanelCaptionBorderColor","#bebebe"],["@JsViewerGroupPanelCaptionBackgroundColor","#333333"],["@JsViewerMenuBorderColor","#bebebe"],["@JsViewerMenuBackgroundColor","#333333"],["@JsViewerMenuSeparatorColor","#4b4b4b"],["@JsViewerMenuFontColor","#ffffff"],["@JsViewerMainPanelsBorderColor","#4b4b4b"],["@JsViewerHyperlinkButtonFontColor","#9c72b5"],["@JsViewerMainPanelsBackgroundColor","#333333"],["@JsViewerProgressColorRGB","134,83,165"],["@JsViewerToolbarBackgroundColor","#333333"],["@JsViewerToolbarFontColor","#ffffff"],["@JsViewerNavigatePanelFontColor","#ffffff"],["@JsViewerNavigatePanelBackgroundColor","#333333"],["@JsViewerNavigatePanelSeparatorColor","#4b4b4b"],["@JsViewerNavigateButtonOverBackgroundColor","#555555"],["@JsViewerNavigateButtonSelectedBackgroundColor","#404040"],["@JsViewerGroupHeaderButtonBackgroundColor","#4e4e4e"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#555555"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#404040"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#444444"]],"Office2022BlackOrange":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#555555"],["@JsViewerButtonSelectedBackgroundColor","#4e4e4e"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#a2a2a2"],["@JsViewerButtonSelectedBorderColor","#999999"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerButtonThemeBackgroundColor","#b73a1c"],["@JsViewerButtonThemeOverBackgroundColor","#c2573e"],["@JsViewerButtonThemeSelectedBackgroundColor","#7e2813"],["@JsViewerButtonThemeDisabledBorderColor","transparent"],["@JsViewerControlsFontColor","#ffffff"],["@JsViewerControlsFontOverColor","#19478a"],["@JsViewerControlsFontSelectedColor","#19478a"],["@JsViewerControlsFontDisabledColor","#6e6e6e"],["@JsViewerControlsBackgroundColor","#2b2b2b"],["@JsViewerControlsOverBackgroundColor","#5e5e5e"],["@JsViewerControlsSelectedBackgroundColor","#7e7e7e"],["@JsViewerControlsDisabledBackgroundColor","#2b2b2b"],["@JsViewerControlsBorderColor","#bebebe"],["@JsViewerControlsOverBorderColor","#a2a2a2"],["@JsViewerControlsSelectedBorderColor","#999999"],["@JsViewerControlsDisabledBorderColor","#4e4e4e"],["@JsViewerFormButtonBackgroundColor","#4e4e4e"],["@JsViewerFormButtonOverBackgroundColor","#555555"],["@JsViewerFormButtonSelectedBackgroundColor","#404040"],["@JsViewerFormButtonDisabledBackgroundColor","#444444"],["@JsViewerFormBorderColor","#bebebe"],["@JsViewerFormBackgroundColor","#333333"],["@JsViewerFormContainerBackgroundColor","#333333"],["@JsViewerFormHeaderBackgroundColor","#4e4e4e"],["@JsViewerFormHeaderFontColor","#ffffff"],["@JsViewerFormSeparatorColor","#4b4b4b"],["@JsViewerFormFontColor","#bebebe"],["@JsViewerGroupPanelBorderColor","#bebebe"],["@JsViewerGroupPanelBackgroundColor","#333333"],["@JsViewerGroupPanelCaptionBorderColor","#bebebe"],["@JsViewerGroupPanelCaptionBackgroundColor","#333333"],["@JsViewerMenuBorderColor","#bebebe"],["@JsViewerMenuBackgroundColor","#333333"],["@JsViewerMenuSeparatorColor","#4b4b4b"],["@JsViewerMenuFontColor","#ffffff"],["@JsViewerMainPanelsBorderColor","#4b4b4b"],["@JsViewerHyperlinkButtonFontColor","#c6634b"],["@JsViewerMainPanelsBackgroundColor","#333333"],["@JsViewerProgressColorRGB","183,58,28"],["@JsViewerToolbarBackgroundColor","#333333"],["@JsViewerToolbarFontColor","#ffffff"],["@JsViewerNavigatePanelFontColor","#ffffff"],["@JsViewerNavigatePanelBackgroundColor","#333333"],["@JsViewerNavigatePanelSeparatorColor","#4b4b4b"],["@JsViewerNavigateButtonOverBackgroundColor","#555555"],["@JsViewerNavigateButtonSelectedBackgroundColor","#404040"],["@JsViewerGroupHeaderButtonBackgroundColor","#4e4e4e"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#555555"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#404040"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#444444"]],"Office2022BlackGreen":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#555555"],["@JsViewerButtonSelectedBackgroundColor","#4e4e4e"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#a2a2a2"],["@JsViewerButtonSelectedBorderColor","#999999"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerButtonThemeBackgroundColor","#0b6433"],["@JsViewerButtonThemeOverBackgroundColor","#2f7b51"],["@JsViewerButtonThemeSelectedBackgroundColor","#084523"],["@JsViewerButtonThemeDisabledBorderColor","transparent"],["@JsViewerControlsFontColor","#ffffff"],["@JsViewerControlsFontOverColor","#19478a"],["@JsViewerControlsFontSelectedColor","#19478a"],["@JsViewerControlsFontDisabledColor","#6e6e6e"],["@JsViewerControlsBackgroundColor","#2b2b2b"],["@JsViewerControlsOverBackgroundColor","#5e5e5e"],["@JsViewerControlsSelectedBackgroundColor","#7e7e7e"],["@JsViewerControlsDisabledBackgroundColor","#2b2b2b"],["@JsViewerControlsBorderColor","#bebebe"],["@JsViewerControlsOverBorderColor","#a2a2a2"],["@JsViewerControlsSelectedBorderColor","#999999"],["@JsViewerControlsDisabledBorderColor","#4e4e4e"],["@JsViewerFormButtonBackgroundColor","#4e4e4e"],["@JsViewerFormButtonOverBackgroundColor","#555555"],["@JsViewerFormButtonSelectedBackgroundColor","#404040"],["@JsViewerFormButtonDisabledBackgroundColor","#444444"],["@JsViewerFormBorderColor","#bebebe"],["@JsViewerFormBackgroundColor","#333333"],["@JsViewerFormContainerBackgroundColor","#333333"],["@JsViewerFormHeaderBackgroundColor","#4e4e4e"],["@JsViewerFormHeaderFontColor","#ffffff"],["@JsViewerFormSeparatorColor","#4b4b4b"],["@JsViewerFormFontColor","#bebebe"],["@JsViewerGroupPanelBorderColor","#bebebe"],["@JsViewerGroupPanelBackgroundColor","#333333"],["@JsViewerGroupPanelCaptionBorderColor","#bebebe"],["@JsViewerGroupPanelCaptionBackgroundColor","#333333"],["@JsViewerMenuBorderColor","#bebebe"],["@JsViewerMenuBackgroundColor","#333333"],["@JsViewerMenuSeparatorColor","#4b4b4b"],["@JsViewerMenuFontColor","#ffffff"],["@JsViewerMainPanelsBorderColor","#4b4b4b"],["@JsViewerHyperlinkButtonFontColor","#6fa386"],["@JsViewerMainPanelsBackgroundColor","#333333"],["@JsViewerProgressColorRGB","11,100,51"],["@JsViewerToolbarBackgroundColor","#333333"],["@JsViewerToolbarFontColor","#ffffff"],["@JsViewerNavigatePanelFontColor","#ffffff"],["@JsViewerNavigatePanelBackgroundColor","#333333"],["@JsViewerNavigatePanelSeparatorColor","#4b4b4b"],["@JsViewerNavigateButtonOverBackgroundColor","#555555"],["@JsViewerNavigateButtonSelectedBackgroundColor","#404040"],["@JsViewerGroupHeaderButtonBackgroundColor","#4e4e4e"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#555555"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#404040"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#444444"]],"Office2022BlackCarmine":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#555555"],["@JsViewerButtonSelectedBackgroundColor","#4e4e4e"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#a2a2a2"],["@JsViewerButtonSelectedBorderColor","#999999"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerButtonThemeBackgroundColor","#912c2f"],["@JsViewerButtonThemeOverBackgroundColor","#a14b4e"],["@JsViewerButtonThemeSelectedBackgroundColor","#641e20"],["@JsViewerButtonThemeDisabledBorderColor","transparent"],["@JsViewerControlsFontColor","#ffffff"],["@JsViewerControlsFontOverColor","#19478a"],["@JsViewerControlsFontSelectedColor","#19478a"],["@JsViewerControlsFontDisabledColor","#6e6e6e"],["@JsViewerControlsBackgroundColor","#2b2b2b"],["@JsViewerControlsOverBackgroundColor","#5e5e5e"],["@JsViewerControlsSelectedBackgroundColor","#7e7e7e"],["@JsViewerControlsDisabledBackgroundColor","#2b2b2b"],["@JsViewerControlsBorderColor","#bebebe"],["@JsViewerControlsOverBorderColor","#a2a2a2"],["@JsViewerControlsSelectedBorderColor","#999999"],["@JsViewerControlsDisabledBorderColor","#4e4e4e"],["@JsViewerFormButtonBackgroundColor","#4e4e4e"],["@JsViewerFormButtonOverBackgroundColor","#555555"],["@JsViewerFormButtonSelectedBackgroundColor","#404040"],["@JsViewerFormButtonDisabledBackgroundColor","#444444"],["@JsViewerFormBorderColor","#bebebe"],["@JsViewerFormBackgroundColor","#333333"],["@JsViewerFormContainerBackgroundColor","#333333"],["@JsViewerFormHeaderBackgroundColor","#4e4e4e"],["@JsViewerFormHeaderFontColor","#ffffff"],["@JsViewerFormSeparatorColor","#4b4b4b"],["@JsViewerFormFontColor","#bebebe"],["@JsViewerGroupPanelBorderColor","#bebebe"],["@JsViewerGroupPanelBackgroundColor","#333333"],["@JsViewerGroupPanelCaptionBorderColor","#bebebe"],["@JsViewerGroupPanelCaptionBackgroundColor","#333333"],["@JsViewerMenuBorderColor","#bebebe"],["@JsViewerMenuBackgroundColor","#333333"],["@JsViewerMenuSeparatorColor","#4b4b4b"],["@JsViewerMenuFontColor","#ffffff"],["@JsViewerMainPanelsBorderColor","#4b4b4b"],["@JsViewerHyperlinkButtonFontColor","#c75659"],["@JsViewerMainPanelsBackgroundColor","#333333"],["@JsViewerProgressColorRGB","145,44,47"],["@JsViewerToolbarBackgroundColor","#333333"],["@JsViewerToolbarFontColor","#ffffff"],["@JsViewerNavigatePanelFontColor","#ffffff"],["@JsViewerNavigatePanelBackgroundColor","#333333"],["@JsViewerNavigatePanelSeparatorColor","#4b4b4b"],["@JsViewerNavigateButtonOverBackgroundColor","#555555"],["@JsViewerNavigateButtonSelectedBackgroundColor","#404040"],["@JsViewerGroupHeaderButtonBackgroundColor","#4e4e4e"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#555555"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#404040"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#444444"]],"Office2022BlackBlue":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonOverBackgroundColor","#555555"],["@JsViewerButtonSelectedBackgroundColor","#4e4e4e"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#a2a2a2"],["@JsViewerButtonSelectedBorderColor","#999999"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerButtonThemeBackgroundColor","#19478a"],["@JsViewerButtonThemeOverBackgroundColor","#3b629b"],["@JsViewerButtonThemeSelectedBackgroundColor","#11315f"],["@JsViewerButtonThemeDisabledBorderColor","transparent"],["@JsViewerControlsFontColor","#ffffff"],["@JsViewerControlsFontOverColor","#19478a"],["@JsViewerControlsFontSelectedColor","#19478a"],["@JsViewerControlsFontDisabledColor","#6e6e6e"],["@JsViewerControlsBackgroundColor","#2b2b2b"],["@JsViewerControlsOverBackgroundColor","#5e5e5e"],["@JsViewerControlsSelectedBackgroundColor","#7e7e7e"],["@JsViewerControlsDisabledBackgroundColor","#2b2b2b"],["@JsViewerControlsBorderColor","#bebebe"],["@JsViewerControlsOverBorderColor","#a2a2a2"],["@JsViewerControlsSelectedBorderColor","#999999"],["@JsViewerControlsDisabledBorderColor","#4e4e4e"],["@JsViewerFormButtonBackgroundColor","#4e4e4e"],["@JsViewerFormButtonOverBackgroundColor","#555555"],["@JsViewerFormButtonSelectedBackgroundColor","#404040"],["@JsViewerFormButtonDisabledBackgroundColor","#444444"],["@JsViewerFormBorderColor","#bebebe"],["@JsViewerFormBackgroundColor","#333333"],["@JsViewerFormContainerBackgroundColor","#333333"],["@JsViewerFormHeaderBackgroundColor","#4e4e4e"],["@JsViewerFormHeaderFontColor","#ffffff"],["@JsViewerFormSeparatorColor","#4b4b4b"],["@JsViewerFormFontColor","#bebebe"],["@JsViewerGroupPanelBorderColor","#bebebe"],["@JsViewerGroupPanelBackgroundColor","#333333"],["@JsViewerGroupPanelCaptionBorderColor","#bebebe"],["@JsViewerGroupPanelCaptionBackgroundColor","#333333"],["@JsViewerMenuBorderColor","#bebebe"],["@JsViewerMenuBackgroundColor","#333333"],["@JsViewerMenuSeparatorColor","#4b4b4b"],["@JsViewerMenuFontColor","#ffffff"],["@JsViewerMainPanelsBorderColor","#4b4b4b"],["@JsViewerHyperlinkButtonFontColor","#5a83ad"],["@JsViewerMainPanelsBackgroundColor","#333333"],["@JsViewerProgressColorRGB","25,71,138"],["@JsViewerToolbarBackgroundColor","#333333"],["@JsViewerToolbarFontColor","#ffffff"],["@JsViewerNavigatePanelFontColor","#ffffff"],["@JsViewerNavigatePanelBackgroundColor","#333333"],["@JsViewerNavigatePanelSeparatorColor","#4b4b4b"],["@JsViewerNavigateButtonOverBackgroundColor","#555555"],["@JsViewerNavigateButtonSelectedBackgroundColor","#404040"],["@JsViewerGroupHeaderButtonBackgroundColor","#4e4e4e"],["@JsViewerGroupHeaderButtonOverBackgroundColor","#555555"],["@JsViewerGroupHeaderButtonSelectedBackgroundColor","#404040"],["@JsViewerGroupHeaderButtonDisabledBackgroundColor","#444444"]]}},"Simple":{stylesText:[190,189,188,187,186,185,184,183,182,181,180,179,178,177,176,175,174,173,172,171,170,169,168,167,166,165,164,163,162,161,160,159,158,157,156,155,154,153,152,151,150,149,148,147,146,145,144,143,142,141,140,139,138,137,136,135,134,133,132,131,130,129,128,127,126,125,124,123,122,121,120,119,118,117,116,115,114,113,112,111,110,109,108,107,106,105,104,103,102,215,214,99,98,97,96,95,94,93,92,91,90,89,88,87,86,85,84,83,82,81,80,79,78,77,76,75,74,73,72,71,70,69,68,67,66,65,64,63,62,61,60,59,58,391,56,55,54,53,52,51,50,49,48,47,46,45,"@-webkit-keyframes load8 { 0% { -webkit-transform: rotate(0deg); transform: rotate(0deg); } 100% { -webkit-transform: rotate(360deg); transform: rotate(360deg); }\n}","@keyframes load8 { 0% { -webkit-transform: rotate(0deg); transform: rotate(0deg); } 100% { -webkit-transform: rotate(360deg); transform: rotate(360deg); }\n}",44,43,42,41,40,39,38,37,36,35,34,33,32,31,30,29,28,27,26,25,24,23,22,21,20,19,18,17,16,15,14,13,12,11,10,9,8,7,6,5,4,3,2,1],accents:{"SimpleGray":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerMainPanelsBorderColor","#c0c0c0"],["@JsViewerMainPanelsBackgroundColor","#e8e8e8"],["@JsViewerMainPanelsBackgroundUpColor","#e8e8e8"],["@JsViewerMainPanelsBackgroundDownColor","#e8e8e8"],["@JsViewerToolbarSeparatorLeftColor","#bdbdbd"],["@JsViewerToolbarSeparatorRightColor","#fdfdfd"],["@JsViewerTooltipBackgroundColor","#fdfdfd"],["@JsViewerButtonOverBackgroundColor","#cfe5fc"],["@JsViewerButtonOverBackgroundUpColor","#cfe5fc"],["@JsViewerButtonOverBackgroundDownColor","#cfe5fc"],["@JsViewerButtonSelectedBackgroundColor","#c0ddfc"],["@JsViewerButtonSelectedBackgroundUpColor","#c0ddfc"],["@JsViewerButtonSelectedBackgroundDownColor","#c0ddfc"],["@JsViewerButtonDisabledBackgroundUpColor","transparent"],["@JsViewerButtonDisabledBackgroundDownColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#3399ff"],["@JsViewerButtonSelectedBorderColor","#3399ff"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerMenuItemOverBackgroundUpColor","#c0ddfc"],["@JsViewerMenuItemOverBackgroundDownColor","#c0ddfc"],["@JsViewerMenuItemSelectedBackgroundUpColor","#c0ddfc"],["@JsViewerMenuItemSelectedBackgroundDownColor","#c0ddfc"],["@JsViewerMenuItemDisabledBackgroundUpColor","transparent"],["@JsViewerMenuItemDisabledBackgroundDownColor","transparent"],["@JsViewerMenuItemBorderColor","transparent"],["@JsViewerMenuItemOverBorderColor","#3399ff"],["@JsViewerMenuItemSelectedBorderColor","#3399ff"],["@JsViewerMenuItemDisabledBorderColor","transparent"],["@JsViewerMenuSeparatorUpColor","#c0c0c0"],["@JsViewerMenuSeparatorDownColor","#ffffff"],["@JsViewerMenuBorderColor","#c0c0c0"],["@JsViewerMenuBackgroundColor","#e8e8e8"],["@JsViewerMenuIconsBackgroundColor","#d8d8d8"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#d5e1f2"],["@JsViewerControlsFontSelectedColor","#c2d5f2"],["@JsViewerControlsFontDisabledColor","#a6a3a6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#ffffff"],["@JsViewerControlsSelectedBackgroundColor","#ffffff"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#3399ff"],["@JsViewerControlsSelectedBorderColor","#3399ff"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerHyperlinkButtonFontColor","#5a83ad"],["@JsViewerGroupPanelBorderColor","#c0c0c0"],["@JsViewerGroupPanelBackgroundColor","#f2f2f2"],["@JsViewerGroupPanelCaptionBorderColor","#c0c0c0"],["@JsViewerGroupPanelCaptionBackgroundColor","#f2f2f2"],["@JsViewerFormBorderColor","#b8b8b8"],["@JsViewerFormBackgroundColor","#d8d8d8"],["@JsViewerFormContainerBackgroundColor","#d8d8d8"],["@JsViewerFormHeaderBackgroundColor","#d8d8d8"],["@JsViewerFormHeaderFontColor","#303030"],["@JsViewerFormSeparatorColor","#b8b8b8"],["@JsViewerNavigatePanelBackgroundColor","#e8e8e8"],["@JsViewerNavigatePanelSeparatorColor","#bdbdbd"],["@JsViewerNavigateButtonOverBackgroundColor","#cfe5fc"],["@JsViewerNavigateButtonSelectedBackgroundColor","#c0ddfc"]]}},"Windows7":{stylesText:[190,189,188,416,186,185,415,414,182,181,180,179,178,177,176,175,174,236,235,234,233,232,168,167,166,165,164,163,162,161,160,159,158,157,156,155,154,153,152,151,150,149,413,412,411,145,144,229,142,228,227,226,225,137,136,135,224,133,132,131,130,129,128,127,223,410,124,123,122,121,120,119,221,409,408,407,114,218,406,405,404,109,108,107,106,105,104,103,102,215,214,213,403,402,96,95,94,93,92,91,90,89,88,87,86,85,287,83,82,81,80,79,78,77,76,75,74,73,246,71,70,209,401,400,66,65,64,63,62,61,60,206,58,399,56,55,54,53,52,243,398,397,48,47,46,45,"@-webkit-keyframes load8 { 0% { -webkit-transform: rotate(0deg); transform: rotate(0deg); } 100% { -webkit-transform: rotate(360deg); transform: rotate(360deg); }\n}","@keyframes load8 { 0% { -webkit-transform: rotate(0deg); transform: rotate(0deg); } 100% { -webkit-transform: rotate(360deg); transform: rotate(360deg); }\n}",44,43,42,41,40,39,38,201,36,35,34,200,32,396,395,29,28,27,26,25,24,197,22,21,20,19,18,17,196,15,14,13,12,11,240,9,194,7,6,394,393,392,2,1],accents:{"Windows7":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerMainPanelsBorderColor","#7092be"],["@JsViewerMainPanelsBackgroundColor","#dce6f4"],["@JsViewerMainPanelsBackgroundUpColor","#fdfeff"],["@JsViewerMainPanelsBackgroundDownColor","#cddaea"],["@JsViewerToolbarSeparatorLeftColor","#d7e4f4"],["@JsViewerToolbarSeparatorRightColor","#eff6ff"],["@JsViewerTooltipBackgroundColor","#ffffff"],["@JsViewerButtonOverBackgroundColor","#d5e1f2"],["@JsViewerButtonOverBackgroundUpColor","#fdfeff"],["@JsViewerButtonOverBackgroundDownColor","#bbcee6"],["@JsViewerButtonSelectedBackgroundColor","#c2d5f2"],["@JsViewerButtonSelectedBackgroundUpColor","#e8eef7"],["@JsViewerButtonSelectedBackgroundDownColor","#a6c3e8"],["@JsViewerButtonDisabledBackgroundUpColor","transparent"],["@JsViewerButtonDisabledBackgroundDownColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#bbcadb"],["@JsViewerButtonSelectedBorderColor","#bbcadb"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerMenuItemOverBackgroundUpColor","#f1f3f6"],["@JsViewerMenuItemOverBackgroundDownColor","#e6edf6"],["@JsViewerMenuItemSelectedBackgroundUpColor","#f1f3f6"],["@JsViewerMenuItemSelectedBackgroundDownColor","#e6edf6"],["@JsViewerMenuItemDisabledBackgroundUpColor","transparent"],["@JsViewerMenuItemDisabledBackgroundDownColor","transparent"],["@JsViewerMenuItemBorderColor","transparent"],["@JsViewerMenuItemOverBorderColor","#aecff7"],["@JsViewerMenuItemSelectedBorderColor","#aecff7"],["@JsViewerMenuItemDisabledBorderColor","transparent"],["@JsViewerMenuSeparatorUpColor","#e0e0e0"],["@JsViewerMenuSeparatorDownColor","#ffffff"],["@JsViewerMenuBorderColor","#979797"],["@JsViewerMenuBackgroundColor","#f1f1f1"],["@JsViewerMenuIconsBackgroundColor","#e8e8e8"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#d5e1f2"],["@JsViewerControlsFontSelectedColor","#c2d5f2"],["@JsViewerControlsFontDisabledColor","#a6a3a6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#d5e1f2"],["@JsViewerControlsSelectedBackgroundColor","#c2d5f2"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#a3bde3"],["@JsViewerControlsSelectedBorderColor","#c2d5f2"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerHyperlinkButtonFontColor","#5a83ad"],["@JsViewerGroupPanelBorderColor","#d3d3d3"],["@JsViewerGroupPanelBackgroundColor","#f1f1f1"],["@JsViewerGroupPanelCaptionBorderColor","#d3d3d3"],["@JsViewerGroupPanelCaptionBackgroundColor","#f1f1f1"],["@JsViewerFormBorderColor","#7092be"],["@JsViewerFormBackgroundColor","#deeaf7"],["@JsViewerFormContainerBackgroundColor","#e5eef9"],["@JsViewerFormHeaderBackgroundColor","#edf4fc"],["@JsViewerFormHeaderFontColor","#5579ae"],["@JsViewerFormSeparatorColor","#cfdbeb"],["@JsViewerNavigatePanelBackgroundColor","#dce6f4"],["@JsViewerNavigatePanelSeparatorColor","#d7e4f4"],["@JsViewerNavigateButtonOverBackgroundColor","#d5e1f2"],["@JsViewerNavigateButtonSelectedBackgroundColor","#c2d5f2"]]}},"WindowsXP":{stylesText:[190,189,188,187,186,185,415,414,182,181,180,179,178,177,176,175,174,236,235,234,233,232,168,167,166,165,164,163,162,161,160,159,158,157,156,155,154,153,152,151,150,149,148,147,434,145,433,432,142,141,140,139,138,137,254,253,134,133,431,430,429,129,128,127,126,125,124,123,122,121,120,119,221,117,428,407,114,218,112,427,404,109,108,107,106,105,104,103,102,426,214,99,425,424,96,95,94,93,92,91,90,89,88,87,86,85,84,83,82,81,80,79,78,77,76,75,74,73,246,71,70,245,423,67,66,65,64,63,62,61,60,59,58,391,56,55,54,53,52,51,422,421,48,47,46,45,"@-webkit-keyframes load8 { 0% { -webkit-transform: rotate(0deg); transform: rotate(0deg); } 100% { -webkit-transform: rotate(360deg); transform: rotate(360deg); }\n}","@keyframes load8 { 0% { -webkit-transform: rotate(0deg); transform: rotate(0deg); } 100% { -webkit-transform: rotate(360deg); transform: rotate(360deg); }\n}",44,43,42,41,40,39,38,201,36,35,34,200,32,420,395,29,28,27,26,25,24,197,22,21,20,19,18,17,16,15,14,13,12,11,419,9,8,7,6,5,418,417,2,1],accents:{"WindowsXP":[["@JsViewerButtonBackgroundColor","transparent"],["@JsViewerButtonDisabledBackgroundColor","transparent"],["@JsViewerMainPanelsBorderColor","#d8d2bd"],["@JsViewerMainPanelsBackgroundColor","#f0f0ea"],["@JsViewerMainPanelsBackgroundUpColor","#f1f1eb"],["@JsViewerMainPanelsBackgroundDownColor","#efefe6"],["@JsViewerToolbarSeparatorLeftColor","#aca899"],["@JsViewerToolbarSeparatorRightColor","#ffffff"],["@JsViewerTooltipBackgroundColor","#ffffff"],["@JsViewerButtonOverBackgroundColor","#f2f2eb"],["@JsViewerButtonOverBackgroundUpColor","#fdfdfb"],["@JsViewerButtonOverBackgroundDownColor","#e3e0d5"],["@JsViewerButtonSelectedBackgroundColor","#f1f1ef"],["@JsViewerButtonSelectedBackgroundUpColor","#f1f1ed"],["@JsViewerButtonSelectedBackgroundDownColor","#f1f1ed"],["@JsViewerButtonDisabledBackgroundUpColor","transparent"],["@JsViewerButtonDisabledBackgroundDownColor","transparent"],["@JsViewerButtonBorderColor","transparent"],["@JsViewerButtonOverBorderColor","#cecec3"],["@JsViewerButtonSelectedBorderColor","#cecec3"],["@JsViewerButtonDisabledBorderColor","transparent"],["@JsViewerMenuItemOverBackgroundUpColor","#316ac5"],["@JsViewerMenuItemOverBackgroundDownColor","#316ac5"],["@JsViewerMenuItemSelectedBackgroundUpColor","#316ac5"],["@JsViewerMenuItemSelectedBackgroundDownColor","#316ac5"],["@JsViewerMenuItemDisabledBackgroundUpColor","transparent"],["@JsViewerMenuItemDisabledBackgroundDownColor","transparent"],["@JsViewerMenuItemBorderColor","transparent"],["@JsViewerMenuItemOverBorderColor","#ffffff"],["@JsViewerMenuItemSelectedBorderColor","#ffffff"],["@JsViewerMenuItemDisabledBorderColor","transparent"],["@JsViewerMenuSeparatorUpColor","#aca899"],["@JsViewerMenuSeparatorDownColor","#ffffff"],["@JsViewerMenuBorderColor","#9e9b91"],["@JsViewerMenuBackgroundColor","#ffffff"],["@JsViewerMenuIconsBackgroundColor","#e8e8e8"],["@JsViewerControlsFontColor","#444444"],["@JsViewerControlsFontOverColor","#d5e1f2"],["@JsViewerControlsFontSelectedColor","#c2d5f2"],["@JsViewerControlsFontDisabledColor","#a6a3a6"],["@JsViewerControlsBackgroundColor","#ffffff"],["@JsViewerControlsOverBackgroundColor","#ffffff"],["@JsViewerControlsSelectedBackgroundColor","#ffffff"],["@JsViewerControlsDisabledBackgroundColor","#f7f2f7"],["@JsViewerControlsBorderColor","#ababab"],["@JsViewerControlsOverBorderColor","#316ac6"],["@JsViewerControlsSelectedBorderColor","#316ac6"],["@JsViewerControlsDisabledBorderColor","#b7b4b7"],["@JsViewerHyperlinkButtonFontColor","#5a83ad"],["@JsViewerGroupPanelBorderColor","#d3d3d3"],["@JsViewerGroupPanelBackgroundColor","#ffffff"],["@JsViewerGroupPanelCaptionBorderColor","#d3d3d3"],["@JsViewerGroupPanelCaptionBackgroundColor","#ffffff"],["@JsViewerFormBorderColor","#d7d2be"],["@JsViewerFormBackgroundColor","#f0f0ec"],["@JsViewerFormContainerBackgroundColor","#f0f0ec"],["@JsViewerFormHeaderBackgroundColor","#f0eee8"],["@JsViewerFormHeaderFontColor","#000000"],["@JsViewerFormSeparatorColor","#d3d3d3"],["@JsViewerNavigatePanelBackgroundColor","#f1f1eb"],["@JsViewerNavigatePanelSeparatorColor","#aca899"],["@JsViewerNavigateButtonOverBackgroundColor","#f2f2eb"],["@JsViewerNavigateButtonSelectedBackgroundColor","#efefed"]]}},"__global":{stylesText:"",accents:{"1":".stiJsViewerTreeItemCaption{margin:0px;border:0px;text-align:left;padding:2px 7px 2px 7px}","2":".stiJsViewerTreeItemImage{border:0px;padding:0px;margin:1px 2px 1px 2px}","3":".stiJsViewerTreeItemSelected{box-sizing:border-box;border:1px solid @JsViewerMenuItemSelectedBorderColor;background:@JsViewerMenuItemSelectedBackgroundDownColor;color:@JsViewerControlsFontColor;cursor:default;font-family:Arial;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none}","4":".stiJsViewerTreeItemOver{box-sizing:border-box;border:1px solid @JsViewerMenuItemOverBorderColor;background:@JsViewerMenuItemOverBackgroundDownColor;color:@JsViewerControlsFontColor;cursor:default;font-family:Arial;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none}","5":".stiJsViewerTreeItem{box-sizing:border-box;border:1px solid @JsViewerMenuItemBorderColor;color:@JsViewerControlsFontColor;cursor:default;font-family:Arial;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none}","6":".stiJsViewerTreeItemIconOpening{margin:0 3px 0 0}","7":".stiJsViewerToolTipTextCell{font-size:12px;font-family:Arial;color:#444444;padding:10px;border:0;border-bottom:1px solid @JsViewerMainPanelsBorderColor;white-space:normal}","8":".stiJsViewerToolTip{position:absolute;border:1px solid @JsViewerMainPanelsBorderColor;background:@JsViewerTooltipBackgroundColor;cursor:default;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none;z-index:50;max-width:250px;-moz-box-shadow:0px 0px 7px rgba(0,0,0,0.6);-webkit-box-shadow:0px 0px 7px rgba(0,0,0,0.6);box-shadow:0 0 7px rgba(0,0,0,0.3)}","9":".stiJsViewerToolBarSeparator{border:0;border-right:1px solid @JsViewerToolbarSeparatorRightColor;background:@JsViewerToolbarSeparatorLeftColor}","10":".stiJsViewerToolBarTable{cursor:default;font-size:12px;border:1px solid @JsViewerMainPanelsBorderColor;background:@JsViewerMainPanelsBackgroundColor;width:100%;border-collapse:separate;color:#202020}","11":".stiJsViewerToolBarSeparated{border-bottom:1px solid @JsViewerMainPanelsBorderColor;background:@JsViewerToolbarBackgroundColor}","12":".stiJsViewerToolBar{overflow-x:auto;overflow-y:hidden;width:100%;position:relative;z-index:2;color:#202020}","13":".stiJsViewerTextBoxDisabled{border:1px solid @JsViewerControlsDisabledBorderColor;background:@JsViewerControlsDisabledBackgroundColor}","14":".stiJsViewerTextBoxOver{border:1px solid @JsViewerControlsOverBorderColor}","15":".stiJsViewerTextBoxDefault{border:1px solid @JsViewerControlsBorderColor}","16":".stiJsViewerTextBox{font-size:12px;margin:0px;padding:0px 0px 0px 4px;-webkit-border-radius:0px;outline:none;background-image:-webkit-gradient(linear, 0% 0%, 0% 100%, from(hsla(0,0%,100%,0)), to(hsla(0,0%,100%,0)));background-image:-webkit-linear-gradient(hsla(0,0%,100%,0), hsla(0,0%,100%,0));background:@JsViewerControlsBackgroundColor;color:#202020}","17":".stiJsViewerStandartTabDisabled{background:transparent;border:1px solid transparent;color:@JsViewerControlsFontDisabledColor}","18":".stiJsViewerStandartTabSelected{color:@JsViewerControlsFontColor;background:@JsViewerButtonSelectedBackgroundColor;border:1px solid @JsViewerButtonSelectedBackgroundColor}","19":".stiJsViewerStandartTabOver{color:@JsViewerControlsFontColor;background:@JsViewerButtonOverBackgroundColor;border:1px solid @JsViewerButtonOverBackgroundColor}","20":".stiJsViewerStandartTabDefault{background:transparent;border:1px solid transparent;color:@JsViewerControlsFontColor}","21":".stiJsViewerStandartTab_Touch{}","22":".stiJsViewerStandartTab_Mouse{}","23":".stiJsViewerStandartTab{white-space:nowrap;cursor:default;font-family:Arial;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none}","24":".stiJsViewerStandartTabFooter{background:@JsViewerButtonThemeBackgroundColor}","25":".stiJsViewerTabbedPanePanel{}","26":".stiJsViewerTabbedPaneContainer{}","27":".stiJsViewerTabsPanel{}","28":".stiJsViewerTabbedPane{}","29":".stiJsViewerStandartSmallButtonDisabled{border:1px solid @JsViewerButtonDisabledBorderColor;color:@JsViewerControlsFontDisabledColor}","30":".stiJsViewerStandartSmallButtonSelected{border:1px solid @JsViewerButtonSelectedBorderColor;background:@JsViewerButtonSelectedBackgroundDownColor}","31":".stiJsViewerStandartSmallButtonOver{border:1px solid @JsViewerButtonOverBorderColor;background:@JsViewerButtonOverBackgroundDownColor}","32":".stiJsViewerStandartSmallButtonDefault{border:1px solid @JsViewerButtonBorderColor}","33":".stiJsViewerStandartSmallButton{cursor:default;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none}","34":".stiJsViewerDragAndDropHintText{color:@JsViewerFormFontColor;text-align:center}","35":".stiJsViewerTextContainer{display:inline-block;white-space:normal;border:0px;font-family:Arial;font-size:12px;color:@JsViewerFormFontColor}","36":".stiJsViewerSignatureFormDrawContainer{border:1px solid @JsViewerControlsBorderColor;position:absolute;top:0;left:0;right:0;bottom:0;overflow:hidden;line-height:1}","37":".stiJsViewerSimpleContainerWithBorder{border:1px solid @JsViewerControlsBorderColor}","38":".stiJsViewerRadioButtonOutCircleDisabled{border:1px solid @JsViewerControlsDisabledBorderColor;background:@JsViewerControlsDisabledBackgroundColor;-moz-border-radius:10px;-webkit-border-radius:10px;border-radius:10px}","39":".stiJsViewerRadioButtonOutCircleOver{border:1px solid @JsViewerControlsOverBorderColor;background:@JsViewerControlsOverBackgroundColor;-moz-border-radius:10px;-webkit-border-radius:10px;border-radius:10px}","40":".stiJsViewerRadioButtonOutCircle{border:1px solid @JsViewerControlsBorderColor;background:@JsViewerControlsBackgroundColor;-moz-border-radius:10px;-webkit-border-radius:10px;border-radius:10px}","41":".stiJsViewerRadioButtonInnerCircle{border:1px solid @JsViewerControlsBorderColor;background:@JsViewerControlsBorderColor;-moz-border-radius:10px;-webkit-border-radius:10px;border-radius:10px}","42":".stiJsViewerRadioButtonDisabled{padding:0px;margin:0px;border:0px;margin-top:0px;margin-bottom:0px;margin-left:0px;margin-right:0px;font-size:12px;color:@JsViewerControlsFontDisabledColor;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none}","43":".stiJsViewerRadioButton{padding:0px;margin:0px;border:0px;margin-top:0px;margin-bottom:0px;margin-left:0px;margin-right:0px;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none}","44":".stiProgressText{position:absolute;top:88px;left:0px;height:40px;width:100%;font-family:'Arial';font-size:20px;text-align:center;color:#444444}","45":".js_viewer_loader,.js_viewer_loader:after{border-radius:50%;width:64px;height:64px}","46":".js_viewer_loader_light{border-top:6px solid rgba(238, 238, 238, 0.2);border-right:6px solid rgba(238, 238, 238, 0.2);border-bottom:6px solid rgba(238, 238, 238, 0.2);border-left:6px solid #eeeeee}","47":".js_viewer_loader_default{border-top:6px solid rgba(25,71,138, 0.2);border-right:6px solid rgba(25,71,138, 0.2);border-bottom:6px solid rgba(25,71,138, 0.2);border-left:6px solid #0070b8}","48":".js_viewer_loader{margin:60px auto;font-size:10px;position:relative;text-indent:-9999em;-webkit-transform:translateZ(0);-ms-transform:translateZ(0);transform:translateZ(0);-webkit-animation:load8 1.1s infinite linear;animation:load8 1.1s infinite linear;box-sizing:border-box}","49":".stiJsViewerParametersMenuItemPressed .stiJsViewerParametersMenuItemInnerTable{height:100%;width:100%;font-size:12px;border:1px solid @JsViewerMenuItemSelectedBorderColor;background:@JsViewerMenuItemSelectedBackgroundDownColor;white-space:nowrap;overflow:hidden;border-collapse:separate;box-sizing:border-box}","50":".stiJsViewerParametersMenuItemOver .stiJsViewerParametersMenuItemInnerTable{height:100%;width:100%;font-size:12px;border:1px solid @JsViewerMenuItemOverBorderColor;background:@JsViewerMenuItemOverBackgroundDownColor;white-space:nowrap;overflow:hidden;border-collapse:separate;box-sizing:border-box}","51":".stiJsViewerParametersMenuItem .stiJsViewerParametersMenuItemInnerTable{height:100%;width:100%;font-size:12px;white-space:nowrap;overflow:hidden;border:1px solid @JsViewerMenuItemBorderColor;border-collapse:separate;box-sizing:border-box}","52":".stiJsViewerParametersMenuItem,.stiJsViewerParametersMenuItemOver,.stiJsViewerParametersMenuItemPressed{padding:1px}","53":".stiJsViewerParametersMenuItem table,.stiJsViewerParametersMenuItem td,.stiJsViewerParametersMenuItemOver table,.stiJsViewerParametersMenuItemOver td,.stiJsViewerParametersMenuItemPressed table,.stiJsViewerParametersMenuItemPressed td{border:0px;padding:0px}","54":".stiJsViewerParametersMenuSeparator{height:1px;width:100%;background:@JsViewerMenuSeparatorUpColor;border-bottom:1px solid @JsViewerMenuSeparatorDownColor}","55":".stiJsViewerParametersMenuInnerTable td{padding:0px}","56":".stiJsViewerParametersMenuInnerTable{border:0px;padding:0px;border-collapse:separate;color:#202020}","57":".stiJsViewerInnerContainerParametersPanel{font-size:12px;padding:10px;background:@JsViewerMainPanelsBackgroundColor;border-collapse:separate;overflow:auto}","58":".stiJsViewerInnerParametersPanelSeparatedLeft{border-right:1px solid @JsViewerMainPanelsBorderColor}","59":".stiJsViewerInnerParametersPanelSimple{border:1px solid @JsViewerMainPanelsBorderColor}","60":".stiJsViewerInnerParametersPanelLeft{position:absolute;top:0px;bottom:0px;background:@JsViewerMainPanelsBackgroundColor;overflow:auto}","61":".stiJsViewerParametersPanel table{padding:0px;margin:0px;border:0px;margin-top:0px;margin-bottom:0px;margin-left:0px;margin-right:0px}","62":".stiJsViewerParametersPanel td{padding:0px;margin:0px;border:0px;margin-top:0px;margin-bottom:0px;margin-left:0px;margin-right:0px}","63":".stiJsViewerParametersPanelSeparatedTop{border-bottom:1px solid @JsViewerMainPanelsBorderColor}","64":".stiJsViewerParametersPanelTop{width:100%}","65":".stiJsViewerParametersPanel{cursor:default;position:absolute;z-index:2;color:#202020}","66":".stiJsViewerNavigateButtonDisabled{border:1px solid @JsViewerButtonDisabledBorderColor;color:@JsViewerControlsFontDisabledColor}","67":".stiJsViewerNavigateButtonSelected{border:1px solid @JsViewerButtonSelectedBorderColor;background:@JsViewerNavigateButtonSelectedBackgroundColor}","68":".stiJsViewerNavigateButtonOver{border:1px solid @JsViewerButtonOverBorderColor;background:@JsViewerNavigateButtonOverBackgroundColor}","69":".stiJsViewerNavigateButton{cursor:default;font-size:12px;border:1px solid transparent;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none}","70":".stiJsViewerNavigatePanelDisabledPanel{z-index:101;top:0px;left:0px;right:0px;bottom:0px;position:absolute;background:white;filter:alpha(Opacity=10);opacity:0;-moz-opacity:0;-khtml-opacity:0}","71":".stiJsViewerNavigatePanelSeparator{border-right:1px dashed @JsViewerNavigatePanelSeparatorColor;width:1px;margin:0 2px 0 1px}","72":".stiJsViewerNavigatePanel{position:absolute;bottom:0px;right:0px;left:0px;z-index:1;background:@JsViewerNavigatePanelBackgroundColor;font-family:Arial;font-size:12px;color:#444444}","73":".stiJsViewerScrollContainer::-webkit-scrollbar-track{background-color:rgba(191, 191, 191, 0.2)}","74":".stiJsViewerScrollContainer::-webkit-scrollbar-thumb{border:2px solid transparent;background-clip:content-box;background-color:rgba(191, 191, 191, 0.8)}","75":".stiJsViewerScrollContainer::-webkit-scrollbar{width:12px;height:12px}","76":".stiJsViewerContainerHideScroll::-webkit-scrollbar{width:0px}","77":".stiJsViewerContainerHideScroll{scrollbar-width:none;-ms-overflow-style:none}","78":".stiJsViewerFindLabel{position:absolute;z-index:150;border:2px solid #8a8a8a;background:yellow;opacity:0.6;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px}","79":".stiEditableTextArea{background:#f5efd5;border:0px;top:0px;left:0px;position:absolute;outline:0px}","80":".stiEditableFieldSelected{background:#f5efd5;cursor:pointer;-webkit-box-shadow:0 0 8px 0 rgba(0,0,0,0.5);-moz-box-shadow:0 0 8px 0px rgba(0,0,0,0.5);box-shadow:0 0 8px 0 rgba(0,0,0,0.5)}","81":".stiEditableField{-webkit-transition:all 0.5s ease;-moz-transition:all 0.5s ease;transition:all 0.5s ease;position:relative}","82":".stiJsViewerCaptionControls{white-space:nowrap;padding:0 25px 0 15px;border:0px;margin:0px;font-size:12px;font-family:Arial}","83":".stiJsViewerPageShadow{-moz-box-shadow:0px 0px 5px rgba(0,0,0,0.6);-webkit-box-shadow:0px 0px 5px rgba(0,0,0,0.6);box-shadow:0 0 7px rgba(0,0,0,0.3)}","84":".stiJsViewerProcessImage{z-index:45;position:absolute;border:1px solid #909090;background:#ffffff;font-size:12px;color:#303030;-moz-box-shadow:0px 0px 5px rgba(0,0,0,0.6);-webkit-box-shadow:0px 0px 5px rgba(0,0,0,0.6);box-shadow:0 0 7px rgba(0,0,0,0.3)}","85":".stiJsViewerAboutPanelStiLink{cursor:pointer;width:100%;text-align:center;font-family:Arial;font-size:12px;margin:10px 0 12px 0;color:#19478a;text-decoration:underline}","86":".stiJsViewerAboutPanelVersion{width:100%;text-align:center;font-family:Arial;font-size:12px;color:#444444;margin-top:20px}","87":".stiJsViewerAboutPanelCopyright{width:100%;text-align:center;font-family:Arial;font-size:12px;color:#444444;margin-top:30px}","88":".stiJsViewerAboutPanelHeader{width:100%;font-family:Arial;font-size:24px;text-align:center;color:@JsViewerFormHeaderFontColor;margin-top:35px}","89":".stiJsViewerAboutPanel{width:520px;z-index:500;text-align:center;position:absolute;border:1px solid @JsViewerFormBorderColor;background:@JsViewerFormBackgroundColor;-moz-box-shadow:0px 0px 5px rgba(0,0,0,0.6);-webkit-box-shadow:0px 0px 5px rgba(0,0,0,0.6);box-shadow:0 0 7px rgba(0,0,0,0.3)}","90":".stiJsViewerDisabledPanel{top:0px;left:0px;right:0px;bottom:0px;position:absolute;background:white;filter:alpha(Opacity=10);opacity:0;-moz-opacity:0;-khtml-opacity:0}","91":".stiJsViewerReportPanel table,.stiJsViewerReportPanel td{padding:0px;margin:0px;margin-top:0px;margin-bottom:0px;margin-left:0px;margin-right:0px}","92":".stiJsViewerReportPanel{z-index:1;bottom:0px;right:0px;left:0px;min-height:100px;border-collapse:separate}","93":".stiJsViewerMainPanel *,.stiJsViewerMainPanel *:after,.stiJsViewerMainPanel *:before{box-sizing:content-box;border-collapse:initial}","94":".stiJsViewerMainPanel{overflow:visible;width:100%;height:100%;z-index:2;position:relative}","95":".stiJsViewerClearAllStyles{padding:0px;margin:0px;border:0px;margin-top:0px;margin-bottom:0px;margin-left:0px;margin-right:0px;line-height:normal}","96":".stiJsViewerMenuStandartItemDisabled{border:1px solid @JsViewerMenuItemDisabledBorderColor;color:@JsViewerControlsFontDisabledColor}","97":".stiJsViewerMenuStandartItemSelected{border:1px solid @JsViewerMenuItemSelectedBorderColor;background:@JsViewerMenuItemSelectedBackgroundDownColor}","98":".stiJsViewerMenuStandartItemOver{border:1px solid @JsViewerMenuItemOverBorderColor;background:@JsViewerMenuItemOverBackgroundDownColor}","99":".stiJsViewerMenuStandartItem{white-space:nowrap;cursor:default;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none;border:1px solid @JsViewerMenuItemBorderColor}","100":".stiJsViewerVerticalMenuSeparator{height:1px;background:transparent;border:0;border-top:1px solid @JsViewerMenuSeparatorUpColor;margin:1px 2px 0 30px}","101":".stiJsViewerMenu{position:absolute;padding:1px;font-size:12px;border:1px solid @JsViewerMenuBorderColor;-moz-box-shadow:0px 0px 7px rgba(0,0,0,0.6);-webkit-box-shadow:0px 0px 7px rgba(0,0,0,0.6);box-shadow:0 0 7px rgba(0,0,0,0.3);cursor:default;background:@JsViewerMenuSeparatorDownColor;background:-moz-linear-gradient(to right, @JsViewerMenuSeparatorDownColor 0px, @JsViewerMenuBackgroundColor 25px, @JsViewerMenuSeparatorDownColor 25px);background:-o-linear-gradient(to right, @JsViewerMenuSeparatorDownColor 0px, @JsViewerMenuBackgroundColor 25px, @JsViewerMenuSeparatorDownColor 25px)}","102":".stiJsViewerParentMenu{z-index:35;position:absolute}","103":".stiJsViewerHyperlinkButtonDisabled{color:@JsViewerHyperlinkButtonFontColor}","104":".stiJsViewerHyperlinkButtonSelected{color:@JsViewerHyperlinkButtonFontColor}","105":".stiJsViewerHyperlinkButtonOver{text-decoration:underline;color:@JsViewerHyperlinkButtonFontColor}","106":".stiJsViewerHyperlinkButtonDefault{color:@JsViewerHyperlinkButtonFontColor}","107":".stiJsViewerHyperlinkButton{cursor:pointer;font-family:Arial;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none}","108":".stiJsViewerGroupPanelContainer{background:@JsViewerControlsBackgroundColor;border-left:1px solid @JsViewerControlsBorderColor;border-right:1px solid @JsViewerControlsBorderColor;border-bottom:1px solid @JsViewerControlsBorderColor}","109":".stiJsViewerGroupHeaderButtonDisabled{background:@JsViewerButtonDisabledBackgroundColor;border:1px solid @JsViewerControlsDisabledBorderColor}","110":".stiJsViewerGroupHeaderButtonSelected{border:1px solid @JsViewerButtonSelectedBorderColor;background:@JsViewerButtonSelectedBackgroundDownColor}","111":".stiJsViewerGroupHeaderButtonOver{border:1px solid @JsViewerButtonOverBorderColor;background:@JsViewerButtonOverBackgroundDownColor}","112":".stiJsViewerGroupHeaderButtonDefault{background:@JsViewerControlsBackgroundColor;border:1px solid @JsViewerControlsBorderColor}","113":".stiJsViewerGroupHeaderButton{height:23px;cursor:default;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none}","114":".stiJsViewerFormButtonDisabled,.stiJsViewerFormButtonThemeDisabled,.stiJsViewerSmallButtonWithBorderDisabled{background:@JsViewerButtonDisabledBackgroundColor;border:1px solid @JsViewerControlsDisabledBorderColor}","115":".stiJsViewerFormButtonSelected,.stiJsViewerFormButtonThemeSelected,.stiJsViewerSmallButtonWithBorderSelected{border:1px solid @JsViewerButtonSelectedBorderColor;background:@JsViewerButtonSelectedBackgroundDownColor}","116":".stiJsViewerFormButtonOver,.stiJsViewerFormButtonThemeOver,.stiJsViewerSmallButtonWithBorderOver{border:1px solid @JsViewerButtonOverBorderColor;background:@JsViewerButtonOverBackgroundDownColor}","117":".stiJsViewerFormButtonDefault,.stiJsViewerFormButtonThemeDefault,.stiJsViewerSmallButtonWithBorderDefault{background:@JsViewerControlsBackgroundColor;border:1px solid @JsViewerControlsBorderColor}","118":".stiJsViewerFormButton,.stiJsViewerFormButtonTheme,.stiJsViewerSmallButtonWithBorder{height:23px;cursor:default;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none;color:@JsViewerControlsFontColor !important}","119":".stiJsViewerNotificationFormMessage{text-align:center;font-family:Arial;font-size:16px;line-height:1.3;color:@JsViewerFormHeaderFontColor;padding:10px 50px 10px 50px}","120":".stiJsViewerNotificationFormDescription{text-align:center;font-family:Arial;font-size:14px;line-height:1.3;color:#888888;padding:10px 50px 10px 50px}","121":".stiJsViewerMessagesFormDescription{line-height:1.2;border:0px;margin:0px;padding:20px 30px 20px 0;overflow:hidden;min-width:350px;max-width:650px;font-family:Arial;font-size:12px;text-align:left;cursor:default}","122":".stiJsViewerFormButtonsPanel{float:right}","123":".stiJsViewerFormSeparator{height:0px;border-top:1px dashed @JsViewerFormSeparatorColor;margin-left:1px;margin-right:1px}","124":".stiJsViewerFormContainer{padding:5px 15px 15px 15px;background:@JsViewerFormContainerBackgroundColor}","125":".stiJsViewerFormHeader{background:@JsViewerFormHeaderBackgroundColor;font-family:Corbel;font-size:25px;color:@JsViewerFormHeaderFontColor;text-align:center;cursor:default}","126":".stiJsViewerForm{position:absolute;border:1px solid @JsViewerFormBorderColor;background:@JsViewerFormBackgroundColor;-moz-box-shadow:0px 0px 7px rgba(0,0,0,0.6);-webkit-box-shadow:0px 0px 7px rgba(0,0,0,0.6);box-shadow:0 0 7px rgba(0,0,0,0.3);cursor:default;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none;text-align:left;color:#202020}","127":".stiJsViewerFilterPanel{border:1px solid @JsViewerMainPanelsBorderColor;background:@JsViewerFormContainerBackgroundColor;color:@JsViewerControlsFontColor;font-family:Arial;font-size:12px;margin:4px;-moz-box-shadow:0px 0px 7px rgba(0,0,0,0.6);-webkit-box-shadow:0px 0px 7px rgba(0,0,0,0.6);box-shadow:0 0 7px rgba(0,0,0,0.3)}","128":".stiJsViewerFilterPanelHeader{border-bottom:1px solid @JsViewerMainPanelsBorderColor}","129":".stiJsViewerFilterContainer{background:@JsViewerFormContainerBackgroundColor;overflow:auto}","130":".stiJsViewerDropDownListButtonDisabled{color:@JsViewerControlsFontDisabledColor}","131":".stiJsViewerDropDownListButtonSelected{background:@JsViewerButtonSelectedBackgroundColor}","132":".stiJsViewerDropDownListButtonOver{background:@JsViewerButtonOverBackgroundColor}","133":".stiJsViewerDropDownListButtonDefault{background:@JsViewerControlsBackgroundColor}","134":".stiJsViewerDropDownListButton{cursor:default;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none}","135":".stiJsViewerDropdownPanel{position:absolute;padding:1px;font-size:12px;border:1px solid @JsViewerMenuBorderColor;-moz-box-shadow:0px 0px 7px rgba(0,0,0,0.6);-webkit-box-shadow:0px 0px 7px rgba(0,0,0,0.6);box-shadow:0 0 7px rgba(0,0,0,0.3);cursor:default;background:@JsViewerGroupPanelBackgroundColor;color:#202020}","136":".stiJsViewerDropdownMenu{position:absolute;padding:1px;font-size:12px;border:1px solid @JsViewerMenuBorderColor;-moz-box-shadow:0px 0px 7px rgba(0,0,0,0.6);-webkit-box-shadow:0px 0px 7px rgba(0,0,0,0.6);box-shadow:0 0 7px rgba(0,0,0,0.3);cursor:default;background:@JsViewerControlsBackgroundColor;color:#202020}","137":".stiJsViewerDropDownListImage{height:17px;width:32px;margin:0 3px 0 3px}","138":".stiJsViewerDropDownList_TextBox{font-size:12px;border:0px;margin:0px;padding:0px 0px 0px 4px;-webkit-border-radius:0px;outline:none;background-image:-webkit-gradient(linear, 0% 0%, 0% 100%, from(hsla(0,0%,100%,0)), to(hsla(0,0%,100%,0)));background-image:-webkit-linear-gradient(hsla(0,0%,100%,0), hsla(0,0%,100%,0));background:@JsViewerControlsBackgroundColor;color:#202020}","139":".stiJsViewerDropDownListDisabled{border:1px solid @JsViewerControlsDisabledBorderColor;background:@JsViewerControlsBackgroundColor;border-collapse:separate}","140":".stiJsViewerDropDownListOver{border:1px solid @JsViewerControlsOverBorderColor;background:@JsViewerControlsBackgroundColor;border-collapse:separate}","141":".stiJsViewerDropDownList{border:1px solid @JsViewerControlsBorderColor;background:@JsViewerControlsBackgroundColor;border-collapse:separate}","142":".stiJsViewerDatePickerDayButtonDisabled{color:@JsViewerControlsFontDisabledColor}","143":".stiJsViewerDatePickerDayButtonSelected{background:@JsViewerButtonSelectedBackgroundColor;border:1px solid @JsViewerMenuItemSelectedBorderColor}","144":".stiJsViewerDatePickerDayButtonOver{background:@JsViewerButtonOverBackgroundColor;border:1px solid @JsViewerButtonOverBorderColor}","145":".stiJsViewerDatePickerDayButtonDefault{background:@JsViewerButtonBackgroundColor}","146":".stiJsViewerDatePickerDayButton{cursor:default;border:1px solid @JsViewerButtonBorderColor;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none}","147":".stiJsViewerDatePickerDayOfWeekCell{white-space:nowrap;cursor:default;font-size:12px;padding:3px;text-align:center;font-weight:bold;border:0 none;border-bottom:1px solid @JsViewerMainPanelsBorderColor}","148":".stiJsViewerDatePickerSeparator{border-top:1px solid @JsViewerMainPanelsBorderColor}","149":".stiJsViewerItemForDragDrop{position:absolute;z-index:10000;border:1px solid #ababab;padding:0px;background:#eeeeee;padding:6px 12px 6px 12px;color:#444444;cursor:default;font-family:Arial;font-size:12px;opacity:0.6;-moz-border-radius:5px;-webkit-border-radius:5px;border-radius:5px;box-shadow:0 0 7px rgba(0,0,0,0.3)}","150":".stiJsViewerNoResultText{color:#a9a9a9;font-family:Arial;font-size:12px;padding:10px;text-align:center}","151":".stiJsViewerDbsDarkMenuItemDisabled{color:#666666}","152":".stiJsViewerDbsDarkMenuItemOver,.stiJsViewerDbsDarkMenuItemSelected{background:#414141}","153":".stiJsViewerDbsDarkMenuItem{white-space:nowrap;cursor:default;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none;color:#dddddd}","154":".stiJsViewerDbsDarkMenu{position:absolute;padding:1px;font-size:12px;border:1px solid #a0a0a0;background:#2b2b2b;-moz-box-shadow:0px 0px 7px rgba(0,0,0,0.6);-webkit-box-shadow:0px 0px 7px rgba(0,0,0,0.6);box-shadow:0 0 7px rgba(0,0,0,0.3);cursor:default;color:#dddddd}","155":".stiJsViewerDbsLightMenuItemDisabled{color:@JsViewerControlsFontDisabledColor}","156":".stiJsViewerDbsLightMenuItemOver,.stiJsViewerDbsLightMenuItemSelected{background:#e2e2e2}","157":".stiJsViewerDbsLightMenuItem{white-space:nowrap;cursor:default;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none;color:#202020}","158":".stiJsViewerDbsLightMenu{position:absolute;padding:1px;font-size:12px;border:1px solid #ababab;background:#ffffff;-moz-box-shadow:0px 0px 7px rgba(0,0,0,0.6);-webkit-box-shadow:0px 0px 7px rgba(0,0,0,0.6);box-shadow:0 0 7px rgba(0,0,0,0.3);cursor:default;color:#202020}","159":".stiJsViewerDashboardElementButtonsPanel .stiJsViewerStandartSmallButton{margin-left:2px}","160":".stiJsViewerDashboardBackPanel{width:100%;text-align:center;color:black;font-family:Arial;font-size:100px;font-weight:bold;position:absolute;display:block !important;top:calc(50% - 50px);z-index:9999;opacity:0.3;pointer-events:none;transform:rotate(-45deg)}","161":".stiJsViewerFiltersStringText{font-family:Arial;font-size:11px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}","162":".stiJsViewerDashboardElementButtonsPanel{position:absolute;top:5px;right:5px;opacity:0;transition:opacity .2s ease;z-index:1}","163":".stiJsViewerColorDialogButtonOver{border:1px solid #f29436;cursor:default}","164":".stiJsViewerColorDialogButton{border:1px solid #e2e4e7;cursor:default}","165":".stiJsViewerColorDialogHeader{background:@JsViewerGroupPanelCaptionBackgroundColor;font-family:Arial;font-size:12px;color:@JsViewerControlsFontColor;cursor:default;padding:7px}","166":".stiColorFormWebColorPanel{overflow-x:hidden;overflow-y:auto}","167":".stiJsViewerColorFormColorBar{border:1px solid @JsViewerMainPanelsBorderColor;width:128px;height:128px;display:inline-block}","168":".stiJsViewerColorFormControlsTable{font-family:Arial;font-size:12px;color:@JsViewerControlsFontColor;cursor:default}","169":".stiJsViewerColorControlWithImage_ColorBar{border:1px solid transparent;width:14px;height:2px;cursor:default}","170":".stiJsViewerColorControlImage div{}","171":".stiJsViewerColorControlImage{border:1px solid @JsViewerControlsBorderColor;width:64px;cursor:default;overflow:hidden}","172":".stiJsViewerColorControlWithBorderDisabled{border:1px solid @JsViewerControlsDisabledBorderColor}","173":".stiJsViewerColorControlWithBorder{border:1px solid @JsViewerControlsBorderColor;background:@JsViewerControlsBackgroundColor}","174":".stiJsViewerCheckBoxNullBlock{position:absolute;border-top:1px solid @JsViewerControlsBorderColor}","175":".stiJsViewerCheckBoxImageBlockDisabled{border:1px solid @JsViewerControlsDisabledBorderColor;background:@JsViewerControlsDisabledBackgroundColor}","176":".stiJsViewerCheckBoxImageBlockOver{border:1px solid @JsViewerControlsOverBorderColor;background:@JsViewerControlsOverBackgroundColor}","177":".stiJsViewerCheckBoxImageBlock{border:1px solid @JsViewerControlsBorderColor;background:@JsViewerControlsBackgroundColor}","178":".stiJsViewerCheckBoxDisabled{font-size:12px;color:@JsViewerControlsFontDisabledColor;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none;border:0}","179":".stiJsViewerCheckBox{padding:0px;margin:0px;border:0px;margin-top:0px;margin-bottom:0px;margin-left:0px;margin-right:0px;font-size:12px;color:@JsViewerControlsFontColor;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none;color:#202020}","180":".stiJsViewerBookmarksLabel{position:absolute;z-index:150;border:2px solid #8a8a8a;opacity:0.7;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px;-webkit-box-sizing:content-box;box-sizing:content-box}","181":".stiJsViewerBookmarksContainer a.clip{overflow:hidden}","182":".stiJsViewerBookmarksContainer a.nodeSel{background-color:@JsViewerButtonSelectedBackgroundColor}","183":".stiJsViewerBookmarksContainer a.node:hover,.stiJsViewerBookmarksContainer a.nodeSel:hover{color:@JsViewerControlsFontColor;text-decoration:underline}","184":".stiJsViewerBookmarksContainer a.node,.stiJsViewerBookmarksContainer a.nodeSel{white-space:nowrap;padding:2px}","185":".stiJsViewerBookmarksContainer a{color:@JsViewerControlsFontColor;text-decoration:none;cursor:pointer}","186":".stiJsViewerBookmarksContainer img{border:0px;vertical-align:middle}","187":".stiJsViewerBookmarksContainerSimple{border:1px solid @JsViewerMainPanelsBorderColor;margin-top:2px}","188":".stiJsViewerBookmarksContainer{top:0px;left:0px;right:0px;bottom:0px;z-index:1;position:absolute;overflow:auto;font-family:Arial;font-size:12px;white-space:nowrap;background:@JsViewerMainPanelsBackgroundColor;padding:3px 0px 0px 3px}","189":".stiJsViewerBookmarksPanelSeparated{border-right:1px solid @JsViewerMainPanelsBorderColor}","190":".stiJsViewerBookmarksPanel{z-index:2;left:0;float:left;position:absolute;overflow:visible;white-space:nowrap;color:#202020}","191":".stiJsViewerTreeItemSelected{box-sizing:border-box;border:1px solid @JsViewerMenuItemSelectedBorderColor;background:@JsViewerMenuItemSelectedBackgroundDownColor;color:@JsViewerControlsFontColor;cursor:default;font-family:Arial;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px}","192":".stiJsViewerTreeItemOver{box-sizing:border-box;border:1px solid @JsViewerMenuItemOverBorderColor;background:@JsViewerMenuItemOverBackgroundDownColor;background:-moz-linear-gradient(top, @JsViewerMenuItemOverBackgroundUpColor, @JsViewerMenuItemOverBackgroundMiddleColor 50%, @JsViewerMenuItemOverBackgroundDownColor 50%, @JsViewerMenuItemOverBackgroundMiddleColor);background:-o-linear-gradient(top, @JsViewerMenuItemOverBackgroundUpColor, @JsViewerMenuItemOverBackgroundMiddleColor 50%, @JsViewerMenuItemOverBackgroundDownColor 50%, @JsViewerMenuItemOverBackgroundMiddleColor);cursor:default;font-family:Arial;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px}","193":".stiJsViewerTreeItem{box-sizing:border-box;border:1px solid @JsViewerMenuItemBorderColor;color:@JsViewerControlsFontColor;cursor:default;font-family:Arial;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px}","194":".stiJsViewerToolTip{position:absolute;border:1px solid @JsViewerMainPanelsBorderColor;background:@JsViewerTooltipBackgroundColor;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px;cursor:default;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none;z-index:50;max-width:250px;-moz-box-shadow:0px 0px 7px rgba(0,0,0,0.6);-webkit-box-shadow:0px 0px 7px rgba(0,0,0,0.6);box-shadow:0 0 7px rgba(0,0,0,0.3)}","195":".stiJsViewerToolBarTable{cursor:default;font-size:12px;border:1px solid @JsViewerMainPanelsBorderColor;background:@JsViewerMainPanelsBackgroundColor;background:-moz-linear-gradient(top, @JsViewerMainPanelsBackgroundDownColor, @JsViewerMainPanelsBackgroundUpColor 15%, @JsViewerMainPanelsBackgroundUpColor 50%, @JsViewerMainPanelsBackgroundDownColor);background:-o-linear-gradient(top, @JsViewerMainPanelsBackgroundDownColor, @JsViewerMainPanelsBackgroundUpColor 15%, @JsViewerMainPanelsBackgroundUpColor 50%, @JsViewerMainPanelsBackgroundDownColor);border-collapse:separate;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px;color:#202020}","196":".stiJsViewerTextBox{font-size:12px;margin:0px;padding:0px 0px 0px 4px;-webkit-border-radius:0px;outline:none;background-image:-webkit-gradient(linear, 0% 0%, 0% 100%, from(hsla(0,0%,100%,0)), to(hsla(0,0%,100%,0)));background-image:-webkit-linear-gradient(hsla(0,0%,100%,0), hsla(0,0%,100%,0));background:@JsViewerControlsBackgroundColor;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px;color:#202020}","197":".stiJsViewerStandartTab{border-radius:3px;white-space:nowrap;cursor:default;font-family:Arial;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none}","198":".stiJsViewerStandartSmallButtonSelected{border:1px solid @JsViewerButtonSelectedBorderColor;background:@JsViewerButtonSelectedBackgroundColor}","199":".stiJsViewerStandartSmallButtonOver{border:1px solid @JsViewerButtonOverBorderColor;background:@JsViewerButtonOverBackgroundDownColor;background:-moz-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor, @JsViewerButtonOverBackgroundMiddleColor 30%, @JsViewerButtonOverBackgroundDownColor 30%, @JsViewerButtonOverBackgroundUpColor);background:-o-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor, @JsViewerButtonOverBackgroundMiddleColor 30%, @JsViewerButtonOverBackgroundDownColor 30%, @JsViewerButtonOverBackgroundUpColor)}","200":".stiJsViewerStandartSmallButton{cursor:default;font-size:12px;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none}","201":".stiJsViewerSimpleContainerWithBorder{border:1px solid @JsViewerControlsBorderColor;border-radius:3px}","202":".stiJsViewerParametersMenuItemPressed .stiJsViewerParametersMenuItemInnerTable{height:100%;width:100%;font-size:12px;border:1px solid @JsViewerMenuItemSelectedBorderColor;background:@JsViewerMenuItemSelectedBackgroundDownColor;white-space:nowrap;overflow:hidden;border-collapse:separate;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px;box-sizing:border-box}","203":".stiJsViewerParametersMenuItemOver .stiJsViewerParametersMenuItemInnerTable{height:100%;width:100%;font-size:12px;border:1px solid @JsViewerMenuItemOverBorderColor;background:@JsViewerMenuItemOverBackgroundDownColor;background:-moz-linear-gradient(top, @JsViewerMenuItemOverBackgroundUpColor, @JsViewerMenuItemOverBackgroundMiddleColor 50%, @JsViewerMenuItemOverBackgroundDownColor 50%, @JsViewerMenuItemOverBackgroundMiddleColor);background:-o-linear-gradient(top, @JsViewerMenuItemOverBackgroundUpColor, @JsViewerMenuItemOverBackgroundMiddleColor 50%, @JsViewerMenuItemOverBackgroundDownColor 50%, @JsViewerMenuItemOverBackgroundMiddleColor);overflow:hidden;border-collapse:separate;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px;box-sizing:border-box}","204":".stiJsViewerParametersMenuItem .stiJsViewerParametersMenuItemInnerTable{height:100%;width:100%;font-size:12px;white-space:nowrap;overflow:hidden;border:1px solid @JsViewerMenuItemBorderColor;border-collapse:separate;-moz-border-radius:5px;-webkit-border-radius:5px;border-radius:5px;box-sizing:border-box}","205":".stiJsViewerInnerContainerParametersPanel{font-size:12px;padding:10px;border-collapse:separate;background:@JsViewerMainPanelsBackgroundDownColor;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px;overflow:auto}","206":".stiJsViewerInnerParametersPanelSimple{border:1px solid @JsViewerMainPanelsBorderColor;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px}","207":".stiJsViewerInnerParametersPanelLeft{position:absolute;top:0px;bottom:0px;background:@JsViewerMainPanelsBackgroundDownColor;overflow:auto}","208":".stiJsViewerNavigateButtonOver{border:1px solid @JsViewerButtonOverBorderColor;background:@JsViewerNavigateButtonOverBackgroundColor;background:-moz-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor, @JsViewerButtonOverBackgroundMiddleColor 30%, @JsViewerButtonOverBackgroundDownColor 30%, @JsViewerButtonOverBackgroundUpColor);background:-o-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor, @JsViewerButtonOverBackgroundMiddleColor 30%, @JsViewerButtonOverBackgroundDownColor 30%, @JsViewerButtonOverBackgroundUpColor)}","209":".stiJsViewerNavigateButton{cursor:default;font-size:12px;border:1px solid transparent;border-radius:3px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none}","210":".stiJsViewerNavigatePanel{position:absolute;bottom:0px;right:0px;left:0px;z-index:1;background:@JsViewerNavigatePanelBackgroundColor;background:-moz-linear-gradient(top, @JsViewerMainPanelsBackgroundDownColor, @JsViewerMainPanelsBackgroundUpColor 15%, @JsViewerMainPanelsBackgroundUpColor 50%, @JsViewerMainPanelsBackgroundDownColor);background:-o-linear-gradient(top, @JsViewerMainPanelsBackgroundDownColor, @JsViewerMainPanelsBackgroundUpColor 15%, @JsViewerMainPanelsBackgroundUpColor 50%, @JsViewerMainPanelsBackgroundDownColor);font-size:12px;color:#444444}","211":".stiJsViewerProcessImage{z-index:45;position:absolute;border:1px solid @JsViewerLoadingPanelColor;background:#ffffff;font-size:12px;color:@JsViewerLoadingPanelColor;-moz-box-shadow:0px 0px 5px rgba(0,0,0,0.6);-webkit-box-shadow:0px 0px 5px rgba(0,0,0,0.6);box-shadow:0 0 7px rgba(0,0,0,0.3)}","212":".stiJsViewerMenuStandartItemOver{border:1px solid @JsViewerMenuItemOverBorderColor;background:@JsViewerMenuItemOverBackgroundDownColor;background:-moz-linear-gradient(top, @JsViewerMenuItemOverBackgroundUpColor, @JsViewerMenuItemOverBackgroundMiddleColor 50%, @JsViewerMenuItemOverBackgroundDownColor 50%, @JsViewerMenuItemOverBackgroundMiddleColor);background:-o-linear-gradient(top, @JsViewerMenuItemOverBackgroundUpColor, @JsViewerMenuItemOverBackgroundMiddleColor 50%, @JsViewerMenuItemOverBackgroundDownColor 50%, @JsViewerMenuItemOverBackgroundMiddleColor)}","213":".stiJsViewerMenuStandartItem{white-space:nowrap;cursor:default;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px;border:1px solid @JsViewerMenuItemBorderColor}","214":".stiJsViewerVerticalMenuSeparator{height:1px;background:@JsViewerMenuSeparatorUpColor;border-bottom:1px solid @JsViewerMenuSeparatorDownColor;margin:1px 2px 0 30px}","215":".stiJsViewerMenu{position:absolute;padding:1px;font-size:12px;border:1px solid @JsViewerMenuBorderColor;background:@JsViewerMenuBackgroundColor;background:-moz-linear-gradient(to right, @JsViewerMenuIconsBackgroundColor 25px, @JsViewerMenuBackgroundColor 25px);background:-o-linear-gradient(to right, @JsViewerMenuIconsBackgroundColor 25px, @JsViewerMenuBackgroundColor 25px);-webkit-box-shadow:0px 0px 7px rgba(0,0,0,0.6);box-shadow:0 0 7px rgba(0,0,0,0.3);cursor:default;color:#202020}","216":".stiJsViewerGroupHeaderButtonSelected{border:1px solid @JsViewerButtonSelectedBorderColor;background:@JsViewerButtonSelectedBackgroundColor}","217":".stiJsViewerGroupHeaderButtonOver{border:1px solid @JsViewerButtonOverBorderColor;background:@JsViewerButtonOverBackgroundDownColor;background:-moz-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor, @JsViewerButtonOverBackgroundMiddleColor 30%, @JsViewerButtonOverBackgroundDownColor 30%, @JsViewerButtonOverBackgroundUpColor);background:-o-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor, @JsViewerButtonOverBackgroundMiddleColor 30%, @JsViewerButtonOverBackgroundDownColor 30%, @JsViewerButtonOverBackgroundUpColor)}","218":".stiJsViewerGroupHeaderButton{height:23px;cursor:default;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px}","219":".stiJsViewerFormButtonSelected,.stiJsViewerFormButtonThemeSelected,.stiJsViewerSmallButtonWithBorderSelected{border:1px solid @JsViewerButtonSelectedBorderColor;background:@JsViewerButtonSelectedBackgroundColor}","220":".stiJsViewerFormButtonOver,.stiJsViewerFormButtonThemeOver,.stiJsViewerSmallButtonWithBorderOver{border:1px solid @JsViewerButtonOverBorderColor;background:@JsViewerButtonOverBackgroundDownColor;background:-moz-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor, @JsViewerButtonOverBackgroundMiddleColor 30%, @JsViewerButtonOverBackgroundDownColor 30%, @JsViewerButtonOverBackgroundUpColor);background:-o-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor, @JsViewerButtonOverBackgroundMiddleColor 30%, @JsViewerButtonOverBackgroundDownColor 30%, @JsViewerButtonOverBackgroundUpColor)}","221":".stiJsViewerFormButton,.stiJsViewerFormButtonTheme,.stiJsViewerSmallButtonWithBorder{height:23px;cursor:default;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px;color:@JsViewerControlsFontColor !important}","222":".stiJsViewerFormHeader{background:@JsViewerFormHeaderBackgroundColor;background:-moz-linear-gradient(top, @JsViewerFormContainerBackgroundColor, @JsViewerFormHeaderBackgroundColor);background:-o-linear-gradient(top, @JsViewerFormContainerBackgroundColor, @JsViewerFormHeaderBackgroundColor);font-size:25px;color:@JsViewerFormHeaderFontColor;text-align:center;cursor:default;-moz-border-radius:5px 5px 0 0;-webkit-border-radius:5px 5px 0 0;border-radius:5px 5px 0 0}","223":".stiJsViewerForm{position:absolute;border:1px solid @JsViewerFormBorderColor;background:@JsViewerFormBackgroundColor;-moz-box-shadow:0px 0px 7px rgba(0,0,0,0.6);-webkit-box-shadow:0px 0px 7px rgba(0,0,0,0.6);box-shadow:0 0 7px rgba(0,0,0,0.3);cursor:default;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none;text-align:left;-moz-border-radius:5px;-webkit-border-radius:5px;border-radius:5px;color:#202020}","224":".stiJsViewerDropDownListButton{cursor:default;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none;-moz-border-radius:0 3px 3px 0;-webkit-border-radius:0 3px 3px 0;border-radius:0 3px 3px 0}","225":".stiJsViewerDropDownList_TextBox{font-size:12px;border:0px;margin:0px;padding:0px 0px 0px 4px;-webkit-border-radius:0px;outline:none;background-image:-webkit-gradient(linear, 0% 0%, 0% 100%, from(hsla(0,0%,100%,0)), to(hsla(0,0%,100%,0)));background-image:-webkit-linear-gradient(hsla(0,0%,100%,0), hsla(0,0%,100%,0));background:@JsViewerControlsBackgroundColor;-moz-border-radius:3px 0 3px 0;-webkit-border-radius:3px 0 3px 0;border-radius:3px 0 3px 0;color:#202020}","226":".stiJsViewerDropDownListDisabled{border:1px solid @JsViewerControlsDisabledBorderColor;background:@JsViewerControlsBackgroundColor;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px;border-collapse:separate}","227":".stiJsViewerDropDownListOver{border:1px solid @JsViewerControlsOverBorderColor;background:@JsViewerControlsBackgroundColor;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px;border-collapse:separate}","228":".stiJsViewerDropDownList{border:1px solid @JsViewerControlsBorderColor;background:@JsViewerControlsBackgroundColor;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px;border-collapse:separate}","229":".stiJsViewerDatePickerDayButtonSelected{background:@JsViewerButtonSelectedBackgroundColor;border:1px solid @JsViewerButtonSelectedBorderColor}","230":".stiJsViewerDatePickerDayButtonOver{background:@JsViewerButtonOverBackgroundColor;background:-moz-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor, @JsViewerButtonOverBackgroundMiddleColor 30%, @JsViewerButtonOverBackgroundDownColor 30%, @JsViewerButtonOverBackgroundUpColor);background:-o-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor, @JsViewerButtonOverBackgroundMiddleColor 30%, @JsViewerButtonOverBackgroundDownColor 30%, @JsViewerButtonOverBackgroundUpColor)}","231":".stiJsViewerDatePickerDayButton{cursor:default;border:1px solid @JsViewerButtonBorderColor;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px}","232":".stiJsViewerColorControlWithImage_ColorBar{border:1px solid transparent;border-radius:3px;width:14px;height:2px;cursor:default}","233":".stiJsViewerColorControlImage div{border-radius:3px}","234":".stiJsViewerColorControlImage{border:1px solid @JsViewerControlsBorderColor;border-radius:3px;width:64px;cursor:default;overflow:hidden}","235":".stiJsViewerColorControlWithBorderDisabled{border:1px solid @JsViewerControlsDisabledBorderColor;border-radius:3px}","236":".stiJsViewerColorControlWithBorder{border:1px solid @JsViewerControlsBorderColor;border-radius:3px;background:@JsViewerControlsBackgroundColor}","237":".stiJsViewerBookmarksContainer{top:0px;left:0px;right:0px;bottom:0px;z-index:1;position:absolute;overflow:auto;font-family:Arial;font-size:12px;white-space:nowrap;background:@JsViewerMainPanelsBackgroundDownColor;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px;padding:3px 0px 0px 3px}","238":".stiJsViewerTreeItemOver{box-sizing:border-box;border:1px solid @JsViewerMenuItemOverBorderColor;background:@JsViewerMenuItemOverBackgroundDownColor;background:-moz-linear-gradient(top, @JsViewerMenuItemOverBackgroundUpColor 10%, @JsViewerMenuItemOverBackgroundDownColor 40%, @JsViewerMenuItemOverBackgroundDownColor 70%, @JsViewerMenuItemOverBackgroundUpColor);background:-o-linear-gradient(top, @JsViewerMenuItemOverBackgroundUpColor 10%, @JsViewerMenuItemOverBackgroundDownColor 40%, @JsViewerMenuItemOverBackgroundDownColor 70%, @JsViewerMenuItemOverBackgroundUpColor);cursor:default;font-family:Arial;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px}","239":".stiJsViewerTreeItem{box-sizing:border-box;border:1px solid transparent;color:@JsViewerControlsFontColor;cursor:default;font-family:Arial;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px}","240":".stiJsViewerToolBarTable{cursor:default;font-size:12px;border:1px solid @JsViewerMainPanelsBorderColor;background:@JsViewerMainPanelsBackgroundColor;background:-moz-linear-gradient(top, @JsViewerMainPanelsBackgroundUpColor, @JsViewerMainPanelsBackgroundDownColor);background:-o-linear-gradient(top, @JsViewerMainPanelsBackgroundUpColor, @JsViewerMainPanelsBackgroundDownColor);border-collapse:separate;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px;color:#202020}","241":".stiJsViewerStandartSmallButtonOver{border:1px solid @JsViewerButtonOverBorderColor;background:@JsViewerButtonOverBackgroundDownColor;background:-moz-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor 10%, @JsViewerButtonOverBackgroundDownColor 40%, @JsViewerButtonOverBackgroundDownColor 70%, @JsViewerButtonOverBackgroundUpColor);background:-o-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor 10%, @JsViewerButtonOverBackgroundDownColor 40%, @JsViewerButtonOverBackgroundDownColor 70%, @JsViewerButtonOverBackgroundUpColor)}","242":".stiJsViewerParametersMenuItemOver .stiJsViewerParametersMenuItemInnerTable{height:100%;width:100%;font-size:12px;border:1px solid @JsViewerMenuItemOverBorderColor;background:@JsViewerMenuItemOverBackgroundDownColor;background:-moz-linear-gradient(top, @JsViewerMenuItemOverBackgroundUpColor 10%, @JsViewerMenuItemOverBackgroundDownColor 40%, @JsViewerMenuItemOverBackgroundDownColor 70%, @JsViewerMenuItemOverBackgroundUpColor);background:-o-linear-gradient(top, @JsViewerMenuItemOverBackgroundUpColor 10%, @JsViewerMenuItemOverBackgroundDownColor 40%, @JsViewerMenuItemOverBackgroundDownColor 70%, @JsViewerMenuItemOverBackgroundUpColor);overflow:hidden;border-collapse:separate;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px;box-sizing:border-box}","243":".stiJsViewerParametersMenuItem .stiJsViewerParametersMenuItemInnerTable{height:100%;width:100%;font-size:12px;white-space:nowrap;overflow:hidden;border:1px solid @JsViewerMenuItemBorderColor;border-collapse:separate;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px;box-sizing:border-box}","244":".stiJsViewerNavigateButtonOver{border:1px solid @JsViewerButtonOverBorderColor;background:@JsViewerNavigateButtonOverBackgroundColor;background:-moz-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor 10%, @JsViewerButtonOverBackgroundDownColor 40%, @JsViewerButtonOverBackgroundDownColor 70%, @JsViewerButtonOverBackgroundUpColor);background:-o-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor 10%, @JsViewerButtonOverBackgroundDownColor 40%, @JsViewerButtonOverBackgroundDownColor 70%, @JsViewerButtonOverBackgroundUpColor)}","245":".stiJsViewerNavigateButton{cursor:default;font-size:12px;border-radius:3px;border:1px solid transparent;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none}","246":".stiJsViewerNavigatePanel{position:absolute;bottom:0px;right:0px;left:0px;z-index:1;background:@JsViewerNavigatePanelBackgroundColor;background:-moz-linear-gradient(top, @JsViewerMainPanelsBackgroundUpColor, @JsViewerMainPanelsBackgroundDownColor);background:-o-linear-gradient(top, @JsViewerMainPanelsBackgroundUpColor, @JsViewerMainPanelsBackgroundDownColor);font-size:12px;color:#444444}","247":".stiJsViewerMenuStandartItemOver{border:1px solid @JsViewerMenuItemOverBorderColor;background:@JsViewerMenuItemOverBackgroundDownColor;background:-moz-linear-gradient(top, @JsViewerMenuItemOverBackgroundUpColor 10%, @JsViewerMenuItemOverBackgroundDownColor 40%, @JsViewerMenuItemOverBackgroundDownColor 70%, @JsViewerMenuItemOverBackgroundUpColor);background:-o-linear-gradient(top, @JsViewerMenuItemOverBackgroundUpColor 10%, @JsViewerMenuItemOverBackgroundDownColor 40%, @JsViewerMenuItemOverBackgroundDownColor 70%, @JsViewerMenuItemOverBackgroundUpColor)}","248":".stiJsViewerVerticalMenuSeparator{height:1px;background:@JsViewerMenuSeparatorUpColor;border-bottom:1px dashed @JsViewerMenuSeparatorDownColor;margin:0 2px 1px 30px}","249":".stiJsViewerGroupHeaderButtonOver{border:1px solid @JsViewerButtonOverBorderColor;background:@JsViewerButtonOverBackgroundDownColor;background:-moz-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor 10%, @JsViewerButtonOverBackgroundDownColor 40%, @JsViewerButtonOverBackgroundDownColor 70%, @JsViewerButtonOverBackgroundUpColor);background:-o-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor 10%, @JsViewerButtonOverBackgroundDownColor 40%, @JsViewerButtonOverBackgroundDownColor 70%, @JsViewerButtonOverBackgroundUpColor)}","250":".stiJsViewerFormButtonOver,.stiJsViewerFormButtonThemeOver,.stiJsViewerSmallButtonWithBorderOver{border:1px solid @JsViewerButtonOverBorderColor;background:@JsViewerButtonOverBackgroundDownColor;background:-moz-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor 10%, @JsViewerButtonOverBackgroundDownColor 40%, @JsViewerButtonOverBackgroundDownColor 70%, @JsViewerButtonOverBackgroundUpColor);background:-o-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor 10%, @JsViewerButtonOverBackgroundDownColor 40%, @JsViewerButtonOverBackgroundDownColor 70%, @JsViewerButtonOverBackgroundUpColor)}","251":".stiJsViewerFormHeader{background:@JsViewerFormHeaderBackgroundColor;font-family:Corbel;font-size:25px;color:@JsViewerFormHeaderFontColor;text-align:center;cursor:default;-moz-border-radius:3px 3px 0 0;-webkit-border-radius:3px 3px 0 0;border-radius:3px 3px 0 0}","252":".stiJsViewerForm{position:absolute;border:1px solid @JsViewerFormBorderColor;background:@JsViewerFormBackgroundColor;-moz-box-shadow:0px 0px 7px rgba(0,0,0,0.6);-webkit-box-shadow:0px 0px 7px rgba(0,0,0,0.6);box-shadow:0 0 7px rgba(0,0,0,0.3);cursor:default;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none;text-align:left;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px;color:#202020}","253":".stiJsViewerDropdownPanel{position:absolute;padding:1px;font-size:12px;border:1px solid @JsViewerMenuBorderColor;background:@JsViewerGroupPanelBackgroundColor;-moz-box-shadow:0px 0px 7px rgba(0,0,0,0.6);-webkit-box-shadow:0px 0px 7px rgba(0,0,0,0.6);box-shadow:0 0 7px rgba(0,0,0,0.3);cursor:default;color:#202020}","254":".stiJsViewerDropdownMenu{position:absolute;padding:1px;font-size:12px;border:1px solid @JsViewerMenuBorderColor;background:@JsViewerControlsBackgroundColor;-moz-box-shadow:0px 0px 7px rgba(0,0,0,0.6);-webkit-box-shadow:0px 0px 7px rgba(0,0,0,0.6);box-shadow:0 0 7px rgba(0,0,0,0.3);cursor:default;color:#202020}","255":".stiJsViewerDatePickerDayButtonOver{background:@JsViewerButtonOverBackgroundColor;background:-moz-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor 10%, @JsViewerButtonOverBackgroundDownColor 40%, @JsViewerButtonOverBackgroundDownColor 70%, @JsViewerButtonOverBackgroundUpColor);background:-o-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor 10%, @JsViewerButtonOverBackgroundDownColor 40%, @JsViewerButtonOverBackgroundDownColor 70%, @JsViewerButtonOverBackgroundUpColor)}","256":".stiJsViewerBookmarksContainerSeparated{padding:2px 0px 0px 2px}","257":".stiJsViewerTreeItemSelected{background:@JsViewerButtonSelectedBackgroundColor;color:@JsViewerControlsFontColor;cursor:default;font-family:Arial;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none}","258":".stiJsViewerTreeItemOver{background:@JsViewerButtonOverBackgroundColor;color:@JsViewerControlsFontColor;cursor:default;font-family:Arial;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none}","259":".stiJsViewerTreeItem{color:@JsViewerControlsFontColor;cursor:default;font-family:Arial;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none}","260":".stiJsViewerToolTipTextCell{font-size:12px;font-family:Arial;color:@JsViewerControlsFontColor;padding:10px;border:0;border-bottom:1px solid @JsViewerMainPanelsBorderColor;white-space:normal}","261":".stiJsViewerToolTip{position:absolute;border:1px solid @JsViewerMainPanelsBorderColor;background:@JsViewerMainPanelsBackgroundColor;cursor:default;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none;z-index:50;max-width:250px;-moz-box-shadow:0px 0px 7px rgba(0,0,0,0.6);-webkit-box-shadow:0px 0px 7px rgba(0,0,0,0.6);box-shadow:0 0 7px rgba(0,0,0,0.3)}","262":".stiJsViewerToolBarSeparator{border:0;background:@JsViewerMenuSeparatorColor}","263":".stiJsViewerToolBarTable{cursor:default;font-size:12px;border:1px solid @JsViewerMainPanelsBorderColor;background:@JsViewerToolbarBackgroundColor;width:100%;border-collapse:separate;color:#202020}","264":".stiJsViewerTextBoxEditButton{border:1px solid @JsViewerControlsBorderColor;background:@JsViewerControlsBackgroundColor;border-left:0px}","265":".stiJsViewerStandartSmallButtonDisabled{color:@JsViewerControlsFontDisabledColor;border:1px solid transparent}","266":".stiJsViewerStandartSmallButtonSelected{background:@JsViewerButtonSelectedBackgroundColor;border:1px solid transparent}","267":".stiJsViewerStandartSmallButtonOver{background:@JsViewerButtonOverBackgroundColor;border:1px solid transparent}","268":".stiJsViewerStandartSmallButtonDefault{border:1px solid transparent}","269":".stiJsViewerResourceDropDownButtonDisabled{background:@JsViewerButtonDisabledBackgroundColor;border:1px solid @JsViewerControlsDisabledBorderColor}","270":".stiJsViewerResourceDropDownButtonSelected{background:@JsViewerButtonSelectedBackgroundColor;border:1px solid @JsViewerControlsSelectedBorderColor}","271":".stiJsViewerResourceDropDownButtonOver{background:@JsViewerButtonOverBackgroundColor;border:1px solid @JsViewerControlsOverBorderColor}","272":".stiJsViewerResourceDropDownButtonDefault{background:@JsViewerControlsBackgroundColor;border:1px solid transparent}","273":".stiJsViewerResourceDropDownButton{height:15px;width:15px;cursor:default;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none}","274":".js_viewer_loader_default{border-top:6px solid rgba(@JsViewerProgressColorRGB, 0.2);border-right:6px solid rgba(@JsViewerProgressColorRGB, 0.2);border-bottom:6px solid rgba(@JsViewerProgressColorRGB, 0.2);border-left:6px solid @JsViewerNavigatePanelBackgroundColor}","275":".stiJsViewerParametersMenuItemPressed .stiJsViewerParametersMenuItemInnerTable{height:100%;width:100%;font-size:12px;background:@JsViewerButtonSelectedBackgroundColor;border:1px solid @JsViewerButtonSelectedBackgroundColor;white-space:nowrap;overflow:hidden;border-collapse:separate;box-sizing:border-box}","276":".stiJsViewerParametersMenuItemOver .stiJsViewerParametersMenuItemInnerTable{height:100%;width:100%;font-size:12px;background:@JsViewerButtonOverBackgroundColor;border:1px solid @JsViewerButtonOverBackgroundColor;white-space:nowrap;overflow:hidden;border-collapse:separate;box-sizing:border-box}","277":".stiJsViewerParametersMenuItem .stiJsViewerParametersMenuItemInnerTable{height:100%;width:100%;font-size:12px;white-space:nowrap;overflow:hidden;border:1px solid transparent;border-collapse:separate;box-sizing:border-box}","278":".stiJsViewerParametersMenuSeparator{height:1px;width:100%;background:@JsViewerMenuSeparatorColor}","279":".stiJsViewerInnerContainerParametersPanel{font-size:12px;padding:10px;background:@JsViewerToolbarBackgroundColor;border-collapse:separate;overflow:auto}","280":".stiJsViewerInnerParametersPanelLeft{position:absolute;top:0px;bottom:0px;background:@JsViewerToolbarBackgroundColor;overflow:auto}","281":".stiJsViewerNavigateButtonDisabled{color:@JsViewerControlsFontDisabledColor}","282":".stiJsViewerNavigateButtonSelected{background:@JsViewerNavigateButtonSelectedBackgroundColor}","283":".stiJsViewerNavigateButtonOver{background:@JsViewerNavigateButtonOverBackgroundColor}","284":".stiJsViewerNavigateButton{cursor:default;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none}","285":".stiJsViewerNavigatePanelSeparator{border-right:1px solid @JsViewerNavigatePanelSeparatorColor;width:1px;margin:0 4px 0 3px}","286":".stiJsViewerNavigatePanel{position:absolute;bottom:0px;right:0px;left:0px;z-index:1;background:@JsViewerNavigatePanelBackgroundColor;font-family:Arial;font-size:12px;color:#ffffff}","287":".stiJsViewerProcessImage{z-index:45;position:absolute;border:1px solid #0070b8;background:#ffffff;font-size:12px;color:#0070b8;-moz-box-shadow:0px 0px 5px rgba(0,0,0,0.6);-webkit-box-shadow:0px 0px 5px rgba(0,0,0,0.6);box-shadow:0 0 7px rgba(0,0,0,0.4)}","288":".stiJsViewerMenuStandartItemDisabled{color:@JsViewerControlsFontDisabledColor}","289":".stiJsViewerMenuStandartItemSelected{background:@JsViewerButtonSelectedBackgroundColor}","290":".stiJsViewerMenuStandartItemOver{background:@JsViewerButtonOverBackgroundColor}","291":".stiJsViewerMenuStandartItem{white-space:nowrap;cursor:default;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none}","292":".stiJsViewerVerticalMenuSeparator{height:1px;background:@JsViewerMenuSeparatorColor;margin:1px 2px 1px 30px}","293":".stiJsViewerDropdownMenu,.stiJsViewerDropdownPanel,.stiJsViewerMenu{position:absolute;padding:1px;font-size:12px;border:1px solid @JsViewerMenuBorderColor;background:@JsViewerMenuBackgroundColor;-moz-box-shadow:0px 0px 7px rgba(0,0,0,0.6);-webkit-box-shadow:0px 0px 7px rgba(0,0,0,0.6);box-shadow:0 0 7px rgba(0,0,0,0.3);cursor:default;color:#202020}","294":".stiJsViewerGroupPanelContainer{background:@JsViewerGroupPanelBackgroundColor;border-left:1px solid @JsViewerControlsBorderColor;border-right:1px solid @JsViewerControlsBorderColor;border-bottom:1px solid @JsViewerControlsBorderColor}","295":".stiJsViewerGroupHeaderButtonDisabled{background:@JsViewerGroupHeaderButtonDisabledBackgroundColor;border:1px solid @JsViewerGroupHeaderButtonDisabledBackgroundColor;color:@JsViewerControlsFontDisabledColor}","296":".stiJsViewerGroupHeaderButtonSelected{background:@JsViewerGroupHeaderButtonSelectedBackgroundColor;border:1px solid @JsViewerGroupHeaderButtonSelectedBackgroundColor;color:@JsViewerControlsFontColor}","297":".stiJsViewerGroupHeaderButtonOver{background:@JsViewerGroupHeaderButtonOverBackgroundColor;border:1px solid @JsViewerGroupHeaderButtonOverBackgroundColor;color:@JsViewerControlsFontColor}","298":".stiJsViewerGroupHeaderButtonDefault{background:@JsViewerGroupHeaderButtonBackgroundColor;border:1px solid @JsViewerGroupHeaderButtonBackgroundColor;color:@JsViewerControlsFontColor}","299":".stiJsViewerGroupHeaderButton{height:23px;cursor:default;font-size:12px;font-weight:600;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none}","300":".stiJsViewerFormButtonDisabled,.stiJsViewerFormButtonThemeDisabled,.stiJsViewerSmallButtonWithBorderDisabled{background:@JsViewerButtonDisabledBackgroundColor;border:1px solid @JsViewerControlsDisabledBorderColor;color:@JsViewerControlsFontDisabledColor !important}","301":".stiJsViewerFormButtonSelected,.stiJsViewerFormButtonThemeSelected,.stiJsViewerSmallButtonWithBorderSelected{background:@JsViewerButtonSelectedBackgroundColor;border:1px solid @JsViewerControlsSelectedBorderColor;color:@JsViewerControlsFontColor !important}","302":".stiJsViewerFormButtonOver,.stiJsViewerFormButtonThemeOver,.stiJsViewerSmallButtonWithBorderOver{background:@JsViewerButtonOverBackgroundColor;border:1px solid @JsViewerControlsOverBorderColor;color:@JsViewerControlsFontColor !important}","303":".stiJsViewerFormButtonDefault,.stiJsViewerFormButtonThemeDefault,.stiJsViewerSmallButtonWithBorderDefault{background:@JsViewerControlsBackgroundColor;border:1px solid @JsViewerControlsBorderColor;color:@JsViewerControlsFontColor !important}","304":".stiJsViewerFormButton,.stiJsViewerFormButtonTheme,.stiJsViewerSmallButtonWithBorder{height:23px;cursor:default;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none}","305":".stiJsViewerFolderReportsPanelItemImageRotate{position:absolute;width:16px;height:16px;-webkit-animation:spin 0.2s linear 1;-moz-animation:spin 0.2s linear 1;animation:spin 0.2s linear 1}","306":".stiJsViewerFolderReportsPanelSeparator{background:@JsViewerNavigateButtonOverBackgroundColor;height:1px}","307":".stiJsViewerReportButtonCaption{width:140px;margin:0 5px 13px 5px;font-family:'Open Sans', Arial;font-size:13px;color:@JsViewerMainPanelsBackgroundColor;display:inline-block;text-align:center;text-overflow:ellipsis;overflow:hidden}","308":".stiJsViewerReportButtonImage{margin:15px 5px 0px 5px;max-width:110px;max-height:115px;background:#ffffff}","309":".stiJsViewerReportButtonSelected{background:@JsViewerNavigateButtonSelectedBackgroundColor}","310":".stiJsViewerReportButton: hover{background:@JsViewerNavigateButtonOverBackgroundColor}","311":".stiJsViewerReportButton{width:100%;cursor:pointer}","312":".stiJsViewerFolderReportsPanelItem:hover{background:@JsViewerNavigateButtonOverBackgroundColor}","313":".stiJsViewerFolderReportsPanelItem{height:40px;width:44px;cursor:pointer;position:relative}","314":".stiJsViewerFolderReportsPanelContainer{overflow:auto;float:left;width:170px;height:calc(100% - 40px)}","315":".stiJsViewerFolderReportsPanelLogo{font-size:18px;white-space:nowrap;background:@JsViewerNavigateButtonSelectedBackgroundColor;height:40px;font-family:Arial;cursor:pointer;width:170px;overflow:hidden}","316":".stiJsViewerFolderReportsPanel{font-family:Arial;font-size:12px;float:left;width:170px;top:0px;left:0px;bottom:0px;position:absolute;background:@JsViewerNavigatePanelBackgroundColor;color:@JsViewerMainPanelsBackgroundColor}","317":".stiJsViewerDropDownListDisabled{border:1px solid @JsViewerControlsDisabledBorderColor;background:@JsViewerControlsBackgroundColor}","318":".stiJsViewerDropDownListOver{border:1px solid @JsViewerControlsOverBorderColor;background:@JsViewerControlsBackgroundColor}","319":".stiJsViewerDropDownList{border:1px solid @JsViewerControlsBorderColor;background:@JsViewerControlsBackgroundColor}","320":".stiJsViewerDatePickerDayButtonSelected{background:@JsViewerButtonSelectedBackgroundColor;border:1px solid @JsViewerButtonSelectedBackgroundColor}","321":".stiJsViewerBookmarksContainer{top:0px;left:0px;right:0px;bottom:0px;z-index:1;position:absolute;overflow:auto;font-family:Arial;font-size:12px;white-space:nowrap;background:@JsViewerToolbarBackgroundColor;padding:3px 0px 0px 3px}","322":".stiJsViewerLoginButtonDisabled{background:@JsViewerNavigateButtonOverBackgroundColor}","323":".stiJsViewerLoginButtonSelected{background:@JsViewerNavigateButtonSelectedBackgroundColor}","324":".stiJsViewerLoginButtonOver{background:@JsViewerNavigateButtonOverBackgroundColor}","325":".stiJsViewerLoginButtonDefault{background:@JsViewerNavigatePanelBackgroundColor}","326":".stiJsViewerLoginButton{border:none;height:40px;color:white;cursor:default;font-family:Arial;font-size:19px;width:100%;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none}","327":".stiJsViewerAuthFormTextCell{white-space:nowrap;color:@JsViewerControlsFontColor;cursor:default;font-family:Arial;font-size:12px;padding:3px 4px 3px 0}","328":".stiJsViewerAuthFormContainer{padding:5px 10px 0px 10px;background:@JsViewerFormBackgroundColor;overflow-y:auto;overflow-x:hidden}","329":".stiJsViewerAuthFormHeader{background:@JsViewerFormBackgroundColor;font-family:Corbel;font-size:25px;color:@JsViewerFormHeaderBackgroundColor;text-align:center;cursor:default}","330":".stiJsViewerAuthFormCaption{font-family:Arial;color:#7a8d8e;font-size:28px}","331":".stiJsViewerTreeItemSelected{border-radius:3px;background:@JsViewerButtonSelectedBackgroundColor;color:@JsViewerControlsFontColor;cursor:default;font-family:Arial;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none}","332":".stiJsViewerTreeItemOver{border-radius:3px;background:@JsViewerButtonOverBackgroundColor;color:@JsViewerControlsFontColor;cursor:default;font-family:Arial;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none}","333":".stiJsViewerTreeItem{border-radius:3px;color:@JsViewerControlsFontColor;cursor:default;font-family:Arial;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none}","334":".stiJsViewerToolTipTextCell{font-size:12px;font-family:Arial;color:@JsViewerFormFontColor;padding:10px;border:0;border-bottom:1px solid @JsViewerMainPanelsBorderColor;white-space:normal}","335":".stiJsViewerToolTip{position:absolute;border-radius:3px;border:1px solid @JsViewerMainPanelsBorderColor;background:@JsViewerMainPanelsBackgroundColor;cursor:default;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none;z-index:50;max-width:250px;-moz-box-shadow:0px 0px 7px rgba(0,0,0,0.6);-webkit-box-shadow:0px 0px 7px rgba(0,0,0,0.6);box-shadow:0 0 7px rgba(0,0,0,0.3);color:@JsViewerFormFontColor}","336":".stiJsViewerToolBarTable{cursor:default;font-size:12px;border-radius:3px;border:1px solid @JsViewerMainPanelsBorderColor;background:@JsViewerToolbarBackgroundColor;width:100%;border-collapse:separate;color:@JsViewerToolbarFontColor}","337":".stiJsViewerToolBar{overflow-x:auto;overflow-y:hidden;width:100%;position:relative;z-index:2;color:@JsViewerToolbarFontColor}","338":".stiJsViewerTextBoxDisabled{border-radius:3px;border:1px solid @JsViewerControlsDisabledBorderColor;background:@JsViewerControlsDisabledBackgroundColor}","339":".stiJsViewerTextBoxOver{border-radius:3px;border:1px solid @JsViewerControlsOverBorderColor}","340":".stiJsViewerTextBoxDefault{border-radius:3px;border:1px solid @JsViewerControlsBorderColor}","341":".stiJsViewerTextBox{font-size:12px;margin:0px;padding:0px 0px 0px 4px;-webkit-border-radius:0px;outline:none;background-image:-webkit-gradient(linear, 0% 0%, 0% 100%, from(hsla(0,0%,100%,0)), to(hsla(0,0%,100%,0)));background-image:-webkit-linear-gradient(hsla(0,0%,100%,0), hsla(0,0%,100%,0));background:@JsViewerControlsBackgroundColor;color:@JsViewerMenuFontColor}","342":".stiJsViewerTextBoxEditButton{border-radius:0 3px 3px 0;border:1px solid @JsViewerControlsBorderColor;background:@JsViewerControlsBackgroundColor;border-left:0px}","343":".stiJsViewerStandartSmallButton{border-radius:3px;cursor:default;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none}","344":".stiJsViewerSmallButtonWithBorderDisabled{background:JsViewerControlsDisabledBackgroundColor;border:1px solid @JsViewerControlsDisabledBorderColor}","345":".stiJsViewerSmallButtonWithBorderSelected{background:@JsViewerControlsSelectedBackgroundColor;border:1px solid @JsViewerControlsSelectedBorderColor}","346":".stiJsViewerSmallButtonWithBorderOver{background:@JsViewerControlsOverBackgroundColor;border:1px solid @JsViewerControlsOverBorderColor}","347":".stiJsViewerSmallButtonWithBorderDefault{background:@JsViewerControlsBackgroundColor;border:1px solid @JsViewerControlsBorderColor}","348":".stiJsViewerSmallButtonWithBorder{border-radius:3px;color:@JsViewerControlsFontColor;cursor:default;font-family:Arial;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none}","349":".stiJsViewerRadioButton{padding:0px;margin:0px;border:0px;margin-top:0px;margin-bottom:0px;margin-left:0px;margin-right:0px;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none;color:@JsViewerFormFontColor}","350":".stiJsViewerParametersMenuItemPressed .stiJsViewerParametersMenuItemInnerTable{height:100%;width:100%;font-size:12px;background:@JsViewerButtonSelectedBackgroundColor;border-radius:3px;border:1px solid @JsViewerButtonSelectedBackgroundColor;white-space:nowrap;overflow:hidden;border-collapse:separate;box-sizing:border-box}","351":".stiJsViewerParametersMenuItemOver .stiJsViewerParametersMenuItemInnerTable{height:100%;width:100%;font-size:12px;background:@JsViewerButtonOverBackgroundColor;border-radius:3px;border:1px solid @JsViewerButtonOverBackgroundColor;white-space:nowrap;overflow:hidden;border-collapse:separate;box-sizing:border-box}","352":".stiJsViewerParametersMenuItem .stiJsViewerParametersMenuItemInnerTable{height:100%;width:100%;font-size:12px;white-space:nowrap;overflow:hidden;border-radius:3px;border:1px solid transparent;border-collapse:separate;box-sizing:border-box}","353":".stiJsViewerParametersMenuInnerTable{border:0px;padding:0px;border-collapse:separate;color:@JsViewerMenuFontColor}","354":".stiJsViewerParametersPanel{cursor:default;position:absolute;z-index:2;color:@JsViewerMenuFontColor}","355":".stiJsViewerAboutPanelStiLink{cursor:pointer;width:100%;text-align:center;font-family:Arial;font-size:12px;margin:10px 0 12px 0;color:@JsViewerHyperlinkButtonFontColor;text-decoration:underline}","356":".stiJsViewerAboutPanelVersion{width:100%;text-align:center;font-family:Arial;font-size:12px;color:@JsViewerFormFontColor;margin-top:20px}","357":".stiJsViewerAboutPanelCopyright{width:100%;text-align:center;font-family:Arial;font-size:12px;color:@JsViewerFormFontColor;margin-top:30px}","358":".stiJsViewerMenuStandartItem{margin:1px;border-radius:3px;white-space:nowrap;cursor:default;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none}","359":".stiJsViewerDropdownMenu,.stiJsViewerDropdownPanel,.stiJsViewerMenu{position:absolute;padding:1px;font-size:12px;border-radius:3px;border:1px solid @JsViewerMenuBorderColor;background:@JsViewerMenuBackgroundColor;-moz-box-shadow:0px 0px 7px rgba(0,0,0,0.6);-webkit-box-shadow:0px 0px 7px rgba(0,0,0,0.6);box-shadow:0 0 7px rgba(0,0,0,0.3);cursor:default;color:@JsViewerMenuFontColor}","360":".stiJsViewerGroupPanelContainer{border-radius:0 0 3px 3px;background:@JsViewerGroupPanelBackgroundColor;border-left:1px solid @JsViewerControlsBorderColor;border-right:1px solid @JsViewerControlsBorderColor;border-bottom:1px solid @JsViewerControlsBorderColor}","361":".stiJsViewerGroupHeaderButton{border-radius:3px;height:23px;cursor:default;font-size:12px;font-weight:600;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none}","362":".stiJsViewerFormButtonThemeDisabled{background:@JsViewerButtonThemeDisabledBorderColor;border:1px solid @JsViewerButtonThemeDisabledBorderColor;color:@JsViewerControlsFontDisabledColor !important}","363":".stiJsViewerFormButtonThemeSelected{background:@JsViewerButtonThemeSelectedBackgroundColor;border:1px solid @JsViewerButtonThemeSelectedBackgroundColor;color:#ffffff !important}","364":".stiJsViewerFormButtonThemeOver{background:@JsViewerButtonThemeOverBackgroundColor;border:1px solid @JsViewerButtonThemeOverBackgroundColor;color:#ffffff !important}","365":".stiJsViewerFormButtonThemeDefault{background:@JsViewerButtonThemeBackgroundColor;border:1px solid @JsViewerButtonThemeBackgroundColor;color:#ffffff !important}","366":".stiJsViewerFormButtonTheme{cursor:default;font-family:Arial;font-size:12px;border-radius:3px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none}","367":".stiJsViewerFormButtonDisabled{background:@JsViewerFormButtonDisabledBackgroundColor;border:1px solid @JsViewerFormButtonBackgroundColor;color:@JsViewerControlsFontDisabledColor !important}","368":".stiJsViewerFormButtonSelected{background:@JsViewerFormButtonSelectedBackgroundColor;border:1px solid @JsViewerFormButtonSelectedBackgroundColor;color:@JsViewerControlsFontColor !important}","369":".stiJsViewerFormButtonOver{background:@JsViewerFormButtonOverBackgroundColor;border:1px solid @JsViewerFormButtonOverBackgroundColor;color:@JsViewerControlsFontColor !important}","370":".stiJsViewerFormButtonDefault{background:@JsViewerFormButtonBackgroundColor;border:1px solid @JsViewerFormButtonBackgroundColor;color:@JsViewerControlsFontColor !important}","371":".stiJsViewerFormButton{border-radius:3px;height:23px;cursor:default;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none}","372":".stiJsViewerFormHeader{background:@JsViewerFormHeaderBackgroundColor;font-family:Arial;font-size:13px;font-weight:bold;color:@JsViewerFormHeaderFontColor;text-align:center;cursor:default;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none;user-select:none}","373":".stiJsViewerForm{position:absolute;border:1px solid @JsViewerFormBorderColor;background:@JsViewerFormBackgroundColor;-moz-box-shadow:0px 0px 7px rgba(0,0,0,0.6);-webkit-box-shadow:0px 0px 7px rgba(0,0,0,0.6);box-shadow:0 0 7px rgba(0,0,0,0.3);cursor:default;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none;text-align:left;color:@JsViewerFormFontColor}","374":".stiJsViewerFilterPanel{border-radius:3px;border:1px solid @JsViewerMainPanelsBorderColor;background:@JsViewerFormContainerBackgroundColor;color:@JsViewerControlsFontColor;font-family:Arial;font-size:12px;margin:4px;-moz-box-shadow:0px 0px 7px rgba(0,0,0,0.6);-webkit-box-shadow:0px 0px 7px rgba(0,0,0,0.6);box-shadow:0 0 7px rgba(0,0,0,0.3)}","375":".stiJsViewerDropDownListButton{border-radius:0 3px 3px 0;cursor:default;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none}","376":".stiJsViewerDropDownList_TextBox{font-size:12px;border-radius:3px 0 0 3px;border:0px;margin:0px;padding:0px 0px 0px 4px;-webkit-border-radius:0px;outline:none;background-image:-webkit-gradient(linear, 0% 0%, 0% 100%, from(hsla(0,0%,100%,0)), to(hsla(0,0%,100%,0)));background-image:-webkit-linear-gradient(hsla(0,0%,100%,0), hsla(0,0%,100%,0));background:@JsViewerControlsBackgroundColor;color:@JsViewerMenuFontColor}","377":".stiJsViewerDropDownListDisabled{border-radius:3px;border:1px solid @JsViewerControlsDisabledBorderColor;background:@JsViewerControlsBackgroundColor}","378":".stiJsViewerDropDownListOver{border-radius:3px;border:1px solid @JsViewerControlsOverBorderColor;background:@JsViewerControlsBackgroundColor}","379":".stiJsViewerDropDownList{border-radius:3px;border:1px solid @JsViewerControlsBorderColor;background:@JsViewerControlsBackgroundColor}","380":".stiJsViewerDatePickerDayButton{cursor:default;border-radius:3px;border:1px solid @JsViewerButtonBorderColor;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none}","381":".stiJsViewerDbsDarkMenuItem{margin:1px;border-radius:3px;white-space:nowrap;cursor:default;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none;color:#dddddd}","382":".stiJsViewerDbsDarkMenu{position:absolute;padding:1px;font-size:12px;border-radius:3px;border:1px solid #a0a0a0;background:#2b2b2b;-moz-box-shadow:0px 0px 7px rgba(0,0,0,0.6);-webkit-box-shadow:0px 0px 7px rgba(0,0,0,0.6);box-shadow:0 0 7px rgba(0,0,0,0.3);cursor:default;color:#dddddd}","383":".stiJsViewerDbsLightMenuItem{margin:1px;border-radius:3px;white-space:nowrap;cursor:default;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none;color:#202020}","384":".stiJsViewerDbsLightMenu{position:absolute;padding:1px;font-size:12px;border-radius:3px;border:1px solid #ababab;background:#ffffff;-moz-box-shadow:0px 0px 7px rgba(0,0,0,0.6);-webkit-box-shadow:0px 0px 7px rgba(0,0,0,0.6);box-shadow:0 0 7px rgba(0,0,0,0.3);cursor:default;color:#202020}","385":".stiJsViewerCheckBoxImageBlockDisabled{border:1px solid @JsViewerControlsDisabledBorderColor;background:@JsViewerControlsDisabledBackgroundColor;border-radius:3px}","386":".stiJsViewerCheckBoxImageBlockOver{border:1px solid @JsViewerControlsOverBorderColor;background:@JsViewerControlsOverBackgroundColor;border-radius:3px}","387":".stiJsViewerCheckBoxImageBlock{border:1px solid @JsViewerControlsBorderColor;background:@JsViewerControlsBackgroundColor;border-radius:3px}","388":".stiJsViewerCheckBox{padding:0px;margin:0px;border:0px;margin-top:0px;margin-bottom:0px;margin-left:0px;margin-right:0px;font-size:12px;color:@JsViewerControlsFontColor;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none;color:@JsViewerFormFontColor}","389":".stiJsViewerBookmarksPanel{z-index:2;left:0;float:left;position:absolute;overflow:visible;white-space:nowrap;color:@JsViewerMenuFontColor}","390":".stiJsViewerLoginButton{border:none;border-radius:3px;height:40px;color:white;cursor:default;font-family:Arial;font-size:19px;width:100%;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none}","391":".stiJsViewerInnerContainerParametersPanel{font-size:12px;padding:10px;border-collapse:separate;background:@JsViewerMainPanelsBackgroundColor;overflow:auto}","392":".stiJsViewerTreeItemSelected{box-sizing:border-box;border:1px solid @JsViewerMenuItemSelectedBorderColor;background:@JsViewerMenuItemSelectedBackgroundDownColor;background:-moz-linear-gradient(top, @JsViewerMenuItemSelectedBackgroundUpColor, @JsViewerMenuItemSelectedBackgroundDownColor);background:-o-linear-gradient(top, @JsViewerMenuItemSelectedBackgroundUpColor, @JsViewerMenuItemSelectedBackgroundDownColor);cursor:default;font-family:Arial;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px}","393":".stiJsViewerTreeItemOver{box-sizing:border-box;border:1px solid @JsViewerMenuItemOverBorderColor;background:@JsViewerMenuItemOverBackgroundDownColor;background:-moz-linear-gradient(top, @JsViewerMenuItemOverBackgroundUpColor, @JsViewerMenuItemOverBackgroundDownColor);background:-o-linear-gradient(top, @JsViewerMenuItemOverBackgroundUpColor, @JsViewerMenuItemOverBackgroundDownColor);cursor:default;font-family:Arial;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px}","394":".stiJsViewerTreeItem{border:1px solid @JsViewerMenuItemBorderColor;box-sizing:border-box;color:@JsViewerControlsFontColor;cursor:default;font-family:Arial;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px}","395":".stiJsViewerStandartSmallButtonSelected{border:1px solid @JsViewerButtonSelectedBorderColor;background:@JsViewerButtonSelectedBackgroundDownColor;background:-moz-linear-gradient(top, @JsViewerButtonSelectedBackgroundUpColor, @JsViewerButtonSelectedBackgroundDownColor);background:-o-linear-gradient(top, @JsViewerButtonSelectedBackgroundUpColor, @JsViewerButtonSelectedBackgroundDownColor)}","396":".stiJsViewerStandartSmallButtonOver{border:1px solid @JsViewerButtonOverBorderColor;background:@JsViewerButtonOverBackgroundDownColor;background:-moz-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor 20%, @JsViewerButtonOverBackgroundDownColor);background:-o-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor 20%, @JsViewerButtonOverBackgroundDownColor)}","397":".stiJsViewerParametersMenuItemPressed .stiJsViewerParametersMenuItemInnerTable{height:100%;width:100%;font-size:12px;border:1px solid @JsViewerMenuItemSelectedBorderColor;background:@JsViewerMenuItemSelectedBackgroundDownColor;background:-moz-linear-gradient(top, @JsViewerMenuItemSelectedBackgroundUpColor, @JsViewerMenuItemSelectedBackgroundDownColor);background:-o-linear-gradient(top, @JsViewerMenuItemSelectedBackgroundUpColor, @JsViewerMenuItemSelectedBackgroundDownColor);overflow:hidden;border-collapse:separate;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px;box-sizing:border-box}","398":".stiJsViewerParametersMenuItemOver .stiJsViewerParametersMenuItemInnerTable{height:100%;width:100%;font-size:12px;border:1px solid @JsViewerMenuItemOverBorderColor;background:@JsViewerMenuItemOverBackgroundDownColor;background:-moz-linear-gradient(top, @JsViewerMenuItemOverBackgroundUpColor, @JsViewerMenuItemOverBackgroundDownColor);background:-o-linear-gradient(top, @JsViewerMenuItemOverBackgroundUpColor, @JsViewerMenuItemOverBackgroundDownColor);overflow:hidden;border-collapse:separate;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px;box-sizing:border-box}","399":".stiJsViewerInnerContainerParametersPanel{font-size:12px;padding:10px;border-collapse:separate;background:@JsViewerMainPanelsBackgroundColor;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px;overflow:auto}","400":".stiJsViewerNavigateButtonSelected{border:1px solid @JsViewerButtonSelectedBorderColor;background:@JsViewerNavigateButtonSelectedBackgroundColor;background:-moz-linear-gradient(top, @JsViewerButtonSelectedBackgroundUpColor, @JsViewerButtonSelectedBackgroundDownColor);background:-o-linear-gradient(top, @JsViewerButtonSelectedBackgroundUpColor, @JsViewerButtonSelectedBackgroundDownColor)}","401":".stiJsViewerNavigateButtonOver{border:1px solid @JsViewerButtonOverBorderColor;background:@JsViewerNavigateButtonOverBackgroundColor;background:-moz-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor 20%, @JsViewerButtonOverBackgroundDownColor);background:-o-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor 20%, @JsViewerButtonOverBackgroundDownColor)}","402":".stiJsViewerMenuStandartItemSelected{border:1px solid @JsViewerMenuItemSelectedBorderColor;background:@JsViewerMenuItemSelectedBackgroundDownColor;background:-moz-linear-gradient(top, @JsViewerMenuItemSelectedBackgroundUpColor, @JsViewerMenuItemSelectedBackgroundDownColor);background:-o-linear-gradient(top, @JsViewerMenuItemSelectedBackgroundUpColor, @JsViewerMenuItemSelectedBackgroundDownColor)}","403":".stiJsViewerMenuStandartItemOver{border:1px solid @JsViewerMenuItemOverBorderColor;background:@JsViewerMenuItemOverBackgroundDownColor;background:-moz-linear-gradient(top, @JsViewerMenuItemOverBackgroundUpColor, @JsViewerMenuItemOverBackgroundDownColor);background:-o-linear-gradient(top, @JsViewerMenuItemOverBackgroundUpColor, @JsViewerMenuItemOverBackgroundDownColor)}","404":".stiJsViewerGroupHeaderButtonSelected{border:1px solid @JsViewerButtonSelectedBorderColor;background:@JsViewerButtonSelectedBackgroundDownColor;background:-moz-linear-gradient(top, @JsViewerButtonSelectedBackgroundUpColor, @JsViewerButtonSelectedBackgroundDownColor);background:-o-linear-gradient(top, @JsViewerButtonSelectedBackgroundUpColor, @JsViewerButtonSelectedBackgroundDownColor)}","405":".stiJsViewerGroupHeaderButtonOver{border:1px solid @JsViewerButtonOverBorderColor;background:@JsViewerButtonOverBackgroundDownColor;background:-moz-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor 20%, @JsViewerButtonOverBackgroundDownColor);background:-o-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor 20%, @JsViewerButtonOverBackgroundDownColor)}","406":".stiJsViewerGroupHeaderButtonDefault{background:@JsViewerMainPanelsBackgroundUpColor;border:1px solid @JsViewerControlsBorderColor}","407":".stiJsViewerFormButtonSelected,.stiJsViewerFormButtonThemeSelected,.stiJsViewerSmallButtonWithBorderSelected{border:1px solid @JsViewerButtonSelectedBorderColor;background:@JsViewerButtonSelectedBackgroundDownColor;background:-moz-linear-gradient(top, @JsViewerButtonSelectedBackgroundUpColor, @JsViewerButtonSelectedBackgroundDownColor);background:-o-linear-gradient(top, @JsViewerButtonSelectedBackgroundUpColor, @JsViewerButtonSelectedBackgroundDownColor)}","408":".stiJsViewerFormButtonOver,.stiJsViewerFormButtonThemeOver,.stiJsViewerSmallButtonWithBorderOver{border:1px solid @JsViewerButtonOverBorderColor;background:@JsViewerButtonOverBackgroundDownColor;background:-moz-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor 20%, @JsViewerButtonOverBackgroundDownColor);background:-o-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor 20%, @JsViewerButtonOverBackgroundDownColor)}","409":".stiJsViewerFormButtonDefault,.stiJsViewerFormButtonThemeDefault,.stiJsViewerSmallButtonWithBorderDefault{background:@JsViewerMainPanelsBackgroundUpColor;border:1px solid @JsViewerControlsBorderColor}","410":".stiJsViewerFormHeader{background:@JsViewerFormHeaderBackgroundColor;font-family:Corbel;font-size:25px;color:@JsViewerFormHeaderFontColor;text-align:center;cursor:default;-moz-border-radius:5px 5px 0 0;-webkit-border-radius:5px 5px 0 0;border-radius:5px 5px 0 0}","411":".stiJsViewerDatePickerDayButton{cursor:default;border:1px solid @JsViewerButtonBorderColor;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none}","412":".stiJsViewerDatePickerDayOfWeekCell{white-space:nowrap;cursor:default;font-size:12px;padding:3px;text-align:center;font-weight:bold;border:0 none;border-bottom:1px solid @JsViewerButtonOverBorderColor}","413":".stiJsViewerDatePickerSeparator{border-top:1px solid @JsViewerButtonOverBorderColor}","414":".stiJsViewerBookmarksContainer a.node:hover,.stiJsViewerBookmarksContainer a.nodeSel:hover{color:@JsViewerControlsFontColor;text-decoration:underline;background-color:@JsViewerButtonOverBackgroundColor}","415":".stiJsViewerBookmarksContainer a.node,.stiJsViewerBookmarksContainer a.nodeSel{white-space:nowrap;padding:1px 2px 1px 2px}","416":".stiJsViewerBookmarksContainerSimple{border:1px solid @JsViewerMainPanelsBorderColor;margin-top:2px;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px}","417":".stiJsViewerTreeItemSelected{box-sizing:border-box;border:1px solid @JsViewerMenuItemSelectedBorderColor;background:@JsViewerMenuItemSelectedBackgroundDownColor;color:#ffffff;cursor:default;font-family:Arial;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none}","418":".stiJsViewerTreeItemOver{box-sizing:border-box;border:1px solid @JsViewerMenuItemOverBorderColor;background:@JsViewerMenuItemOverBackgroundDownColor;color:#ffffff;cursor:default;font-family:Arial;font-size:12px;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none}","419":".stiJsViewerToolBarTable{cursor:default;font-size:12px;border:1px solid @JsViewerMainPanelsBorderColor;background:@JsViewerMainPanelsBackgroundColor;background:-moz-linear-gradient(top, @JsViewerMainPanelsBackgroundUpColor, @JsViewerMainPanelsBackgroundDownColor);background:-o-linear-gradient(top, @JsViewerMainPanelsBackgroundUpColor, @JsViewerMainPanelsBackgroundDownColor);border-collapse:separate;color:#202020}","420":".stiJsViewerStandartSmallButtonOver{border:1px solid @JsViewerButtonOverBorderColor;background:@JsViewerButtonOverBackgroundDownColor;background:-moz-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor 15%, @JsViewerButtonOverBackgroundDownColor 93%, @JsViewerButtonOverBorderColor);background:-o-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor 15%, @JsViewerButtonOverBackgroundDownColor 93%, @JsViewerButtonOverBorderColor)}","421":".stiJsViewerParametersMenuItemPressed .stiJsViewerParametersMenuItemInnerTable{height:100%;width:100%;font-size:12px;border:1px solid @JsViewerMenuItemSelectedBorderColor;background:@JsViewerMenuItemSelectedBackgroundDownColor;white-space:nowrap;overflow:hidden;border-collapse:separate;color:#ffffff;box-sizing:border-box}","422":".stiJsViewerParametersMenuItemOver .stiJsViewerParametersMenuItemInnerTable{height:100%;width:100%;font-size:12px;border:1px solid @JsViewerMenuItemOverBorderColor;background:@JsViewerMenuItemOverBackgroundDownColor;white-space:nowrap;overflow:hidden;border-collapse:separate;color:#ffffff;box-sizing:border-box}","423":".stiJsViewerNavigateButtonOver{border:1px solid @JsViewerButtonOverBorderColor;background:@JsViewerNavigateButtonOverBackgroundColor;background:-moz-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor 15%, @JsViewerButtonOverBackgroundDownColor 93%, @JsViewerButtonOverBorderColor);background:-o-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor 15%, @JsViewerButtonOverBackgroundDownColor 93%, @JsViewerButtonOverBorderColor)}","424":".stiJsViewerMenuStandartItemSelected{border:1px solid @JsViewerMenuItemSelectedBorderColor;background:@JsViewerMenuItemSelectedBackgroundDownColor;color:#ffffff}","425":".stiJsViewerMenuStandartItemOver{border:1px solid @JsViewerMenuItemOverBorderColor;background:@JsViewerMenuItemOverBackgroundDownColor;color:#ffffff}","426":".stiJsViewerMenu{position:absolute;padding:1px;font-size:12px;border:1px solid @JsViewerMenuBorderColor;background:@JsViewerMenuBackgroundColor;-moz-box-shadow:0px 0px 7px rgba(0,0,0,0.6);-webkit-box-shadow:0px 0px 7px rgba(0,0,0,0.6);box-shadow:0 0 7px rgba(0,0,0,0.3);cursor:default;color:#202020}","427":".stiJsViewerGroupHeaderButtonOver{border:1px solid @JsViewerButtonOverBorderColor;background:@JsViewerButtonOverBackgroundDownColor;background:-moz-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor 15%, @JsViewerButtonOverBackgroundDownColor 93%, @JsViewerButtonOverBorderColor);background:-o-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor 15%, @JsViewerButtonOverBackgroundDownColor 93%, @JsViewerButtonOverBorderColor)}","428":".stiJsViewerFormButtonOver,.stiJsViewerFormButtonThemeOver,.stiJsViewerSmallButtonWithBorderOver{border:1px solid @JsViewerButtonOverBorderColor;background:@JsViewerButtonOverBackgroundDownColor;background:-moz-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor 15%, @JsViewerButtonOverBackgroundDownColor 93%, @JsViewerButtonOverBorderColor);background:-o-linear-gradient(top, @JsViewerButtonOverBackgroundUpColor 15%, @JsViewerButtonOverBackgroundDownColor 93%, @JsViewerButtonOverBorderColor)}","429":".stiJsViewerDropDownListButtonDisabled{color:@JsViewerControlsFontDisabledColor;color:#606060}","430":".stiJsViewerDropDownListButtonSelected{background:@JsViewerButtonSelectedBackgroundColor;color:#ffffff}","431":".stiJsViewerDropDownListButtonOver{background:@JsViewerButtonOverBackgroundColor;color:#ffffff}","432":".stiJsViewerDatePickerDayButtonSelected{background:@JsViewerMenuItemSelectedBackgroundDownColor;border:1px solid @JsViewerMenuItemSelectedBackgroundDownColor;color:#ffffff}","433":".stiJsViewerDatePickerDayButtonOver{background:@JsViewerMenuItemOverBackgroundDownColor;border:1px solid @JsViewerMenuItemOverBackgroundDownColor;color:#ffffff}","434":".stiJsViewerDatePickerDayButton{cursor:default;font-size:12px;border:1px solid @JsViewerButtonBorderColor;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;-ms-user-select:none}"}},});