import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { SubformPageRoutingModule } from './subform-routing.module';

import { SubformPage } from './subform.page';
import { ComponentsModule } from 'src/app/component/component.module';
import { CommonDirectiveModule } from '../../../../directives/common-directives.module';

@NgModule({
  imports: [CommonModule, FormsModule, IonicModule, SubformPageRoutingModule, ComponentsModule, CommonDirectiveModule],
  declarations: [SubformPage],
})
export class SubformPageModule {}
