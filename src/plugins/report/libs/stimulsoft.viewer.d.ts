declare namespace Stimulsoft.Viewer {
    enum StiContentAlignment {
        Left = 0,
        Center = 1,
        Right = 2,
        Default = 3
    }
    enum StiInterfaceType {
        Auto = 0,
        Mouse = 1,
        Touch = 2,
        Mobile = 3
    }
    enum StiChartRenderType {
        Vector = 2,
        AnimatedVector = 3
    }
    enum StiPrintDestination {
        Default = 0,
        Pdf = 1,
        Direct = 2,
        WithPreview = 3
    }
    enum StiReportType {
        Auto = 0,
        Report = 1,
        Dashboard = 2
    }
    enum StiViewerTheme {
        SimpleGray = 0,
        WindowsXP = 1,
        Windows7 = 2,
        Office2003 = 3,
        Office2007Blue = 4,
        Office2007Black = 5,
        Office2007Silver = 6,
        Office2010Blue = 7,
        Office2010Black = 8,
        Office2010Silver = 9,
        Office2013WhiteBlue = 10,
        Office2013WhiteCarmine = 11,
        Office2013WhiteGreen = 12,
        Office2013WhiteOrange = 13,
        Office2013WhitePurple = 14,
        Office2013WhiteTeal = 15,
        Office2013WhiteViolet = 16,
        Office2013LightGrayBlue = 17,
        Office2013LightGrayCarmine = 18,
        Office2013LightGrayGreen = 19,
        Office2013LightGrayOrange = 20,
        Office2013LightGrayPurple = 21,
        Office2013LightGrayTeal = 22,
        Office2013LightGrayViolet = 23,
        Office2013DarkGrayBlue = 24,
        Office2013DarkGrayCarmine = 25,
        Office2013DarkGrayGreen = 26,
        Office2013DarkGrayOrange = 27,
        Office2013DarkGrayPurple = 28,
        Office2013DarkGrayTeal = 29,
        Office2013DarkGrayViolet = 30,
        Office2013VeryDarkGrayBlue = 31,
        Office2013VeryDarkGrayCarmine = 32,
        Office2013VeryDarkGrayGreen = 33,
        Office2013VeryDarkGrayOrange = 34,
        Office2013VeryDarkGrayPurple = 35,
        Office2013VeryDarkGrayTeal = 36,
        Office2013VeryDarkGrayViolet = 37,
        Office2022WhiteBlue = 38,
        Office2022WhiteCarmine = 39,
        Office2022WhiteGreen = 40,
        Office2022WhiteOrange = 41,
        Office2022WhitePurple = 42,
        Office2022WhiteTeal = 43,
        Office2022WhiteViolet = 44,
        Office2022LightGrayBlue = 45,
        Office2022LightGrayCarmine = 46,
        Office2022LightGrayGreen = 47,
        Office2022LightGrayOrange = 48,
        Office2022LightGrayPurple = 49,
        Office2022LightGrayTeal = 50,
        Office2022LightGrayViolet = 51,
        Office2022DarkGrayBlue = 52,
        Office2022DarkGrayCarmine = 53,
        Office2022DarkGrayGreen = 54,
        Office2022DarkGrayOrange = 55,
        Office2022DarkGrayPurple = 56,
        Office2022DarkGrayTeal = 57,
        Office2022DarkGrayViolet = 58,
        Office2022BlackBlue = 59,
        Office2022BlackCarmine = 60,
        Office2022BlackGreen = 61,
        Office2022BlackOrange = 62,
        Office2022BlackPurple = 63,
        Office2022BlackTeal = 64,
        Office2022BlackViolet = 65
    }
    enum StiWebViewMode {
        SinglePage = 0,
        Continuous = 1,
        MultiplePages = 2,
        OnePage = 3,
        WholeReport = 4,
        MultiPage = 5
    }
    enum StiShowMenuMode {
        Click = 0,
        Hover = 1
    }
    enum StiZoomMode {
        PageWidth = -1,
        PageHeight = -2
    }
    enum StiExportAction {
        ExportReport = 1,
        SendEmail = 2
    }
    enum StiFirstDayOfWeek {
        Auto = 0,
        Monday = 1,
        Sunday = 2
    }
    enum StiParametersPanelPosition {
        Top = 0,
        Left = 1,
        FromReport = 2
    }
    enum StiToolbarDisplayMode {
        Simple = 0,
        Separated = 1
    }
    enum StiWebUIIconSet {
        Auto = 0,
        Monoline = 1,
        Regular = 2
    }
}
declare namespace Stimulsoft.Viewer {
    class StiEmailSettings {
        email: string;
        subject: string;
        message: string;
    }
}
declare namespace Stimulsoft.Viewer.Helpers.Dashboards {
    import StiPromise = Stimulsoft.System.StiPromise;
    import IStiElement = Stimulsoft.Report.Dashboard.IStiElement;
    import StiExportFormat = Stimulsoft.Report.StiExportFormat;
    class StiDashboardsSvgHelper {
        private static getSvgImageValue;
        private static saveElementToVectorStringAsync;
        static saveElementToStringAsync(element: IStiElement, scaleX?: number, scaleY?: number, designMode?: boolean, exportFormat?: StiExportFormat, requestParams?: any, refNeedToScroll?: any): StiPromise<string>;
        static saveElementToBase64Async(element: IStiElement, scaleX?: number, scaleY?: number, designMode?: boolean, exportFormat?: StiExportFormat, requestParams?: any): StiPromise<string>;
        private static applyTransparencyToComponents;
        private static setCulture;
        private static restoreCulture;
    }
}
declare namespace Stimulsoft.Viewer.Helpers.Dashboards {
    import StiCornerRadius = Stimulsoft.Base.Drawing.StiCornerRadius;
    import StiReport = Stimulsoft.Report.StiReport;
    import Hashtable = Stimulsoft.System.Collections.Hashtable;
    import KeyObjectType = Stimulsoft.System.KeyObjectType;
    import IStiControlElement = Stimulsoft.Report.Dashboard.IStiControlElement;
    import IStiElement = Stimulsoft.Report.Dashboard.IStiElement;
    import Font = Stimulsoft.System.Drawing.Font;
    import StiSimpleBorder = Stimulsoft.Base.Drawing.StiSimpleBorder;
    class StiDashboardElementViewHelper {
        static getElementContentAttributesAsync(element: IStiElement, scaleX: number, scaleY: number, requestParams: any): Promise<KeyObjectType>;
        static getForeColor(element: IStiElement): string;
        static getBackColor(element: IStiElement): string;
        static getBorder(element: IStiElement): any;
        static getBorderJson(border: StiSimpleBorder): KeyObjectType;
        static getFont(element: IStiElement): any;
        static getFontJson(font: Font): KeyObjectType;
        static getTitle(element: IStiElement): Promise<KeyObjectType>;
        private static getBrushStr;
        private static getStyleName;
        static applyComponentStyleToElement(element: IStiElement): void;
        private static getButtonIconSetProperty;
        private static getButtonVisualStatesProperty;
        private static getButtonStyleColors;
        static getControlElementSettings(element: IStiElement): KeyObjectType;
        static getLayout(element: IStiElement): KeyObjectType;
        private static fixColor;
        static getActionColors(element: IStiElement): KeyObjectType;
        static getBingMapScript(element: IStiElement, showTitle: boolean): Promise<string>;
        static getDashboardInteractionAsync(element: any): Promise<KeyObjectType>;
        private static getToolTipStyles;
        static getShadow(element: IStiElement): any;
        static getCornerRadius(cornerRadius: StiCornerRadius): any;
        static getDashboardWatermark(element: IStiElement): any;
        private static getWeaveWatermarkImages;
        private static getHyperlinkTextAsync;
        static format(element: IStiControlElement, value: any): string;
        static getConstants(value: string, cells: any): Hashtable;
        static parseDashboardDrillDownParameters(drillDownParameters: any[], report: StiReport): Promise<void>;
        private static imageToBase64;
    }
}
declare namespace Stimulsoft.Viewer.Helpers.Dashboards {
    import Rectangle = Stimulsoft.System.Drawing.Rectangle;
    class StiRangeBand {
        top: number;
        bottom: number;
        get height(): number;
        originalTop: number;
        originalBottom: number;
        get originalHeight(): number;
        isFixed: boolean;
        toString(): string;
        intersect(rect: Rectangle): boolean;
        constructor(top: number, bottom: number);
    }
}
declare namespace Stimulsoft.Viewer {
    import StiHtmlExportMode = Stimulsoft.Report.Export.StiHtmlExportMode;
    import StiBrush = Stimulsoft.Base.Drawing.StiBrush;
    import KeyObjectType = Stimulsoft.System.KeyObjectType;
    import StiPromise = Stimulsoft.System.StiPromise;
    import StiRangeBand = Stimulsoft.Viewer.Helpers.Dashboards.StiRangeBand;
    import IStiElement = Stimulsoft.Report.Dashboard.IStiElement;
    import List = Stimulsoft.System.Collections.List;
    import StiPage = Stimulsoft.Report.Components.StiPage;
    import StiReport = Stimulsoft.Report.StiReport;
    import StiComponent = Stimulsoft.Report.Components.StiComponent;
    import Color = Stimulsoft.System.Drawing.Color;
    class StiReportHelper {
        static getHtmlColor(color: Color): string;
        private static round;
        private static round2;
        static getNestedPages(report: StiReport): List<StiPage>;
        static getDashboards(report: StiReport, combineReportPages: boolean): KeyObjectType[];
        private static getElementsPositions;
        private static correctElementLocations;
        static getDashboardPageAsync(report: StiReport, pageIndex: number, requestParams: any): StiPromise<KeyObjectType>;
        static getElementAttributesAsync(page: StiPage, element: IStiElement, renderSingleElement: boolean, requestParams: any, elementOldHeights: any, bands?: List<StiRangeBand>, totalFixedHeight?: number): Promise<KeyObjectType>;
        static getSingleElementContent(report: StiReport, requestParams: any): Promise<KeyObjectType>;
        static calculatePositionForEachBand(requestParams: any, elements: List<IStiElement>, page: StiPage, bands: {
            ref: List<StiRangeBand>;
        }, totalFixedHeight: {
            ref: number;
        }, component?: StiComponent): void;
        static applySignatures(report: StiReport, signatures: any[]): void;
        static applySorting(report: StiReport, parameters: any): void;
        static applyCollapsing(report: StiReport, parameters: any): void;
        static applyDrillDown(report: StiReport, renderedReport: StiReport, parameters: any, drillDownParameters: any, params: any, viewer: StiViewer): Promise<StiReport>;
        static applyDashboardDrillDown(report: StiReport, drillDownParameters: any, action: string): Promise<StiReport>;
        private static addBookmarkNode;
        static getBookmarksContent(report: StiReport, viewerId: string, pageNumber: number): string;
        private static getBookmarksPageIndexes;
        static getTableOfContentsPointers(report: StiReport, requestParams: StiRequestParams): any[];
        private static getBookmarkPointers;
        static getReportPreviewSettings(report: StiReport): KeyObjectType;
        static getReportDisplayModeFromReport(report: StiReport): StiHtmlExportMode;
        static getPagesCount(report: StiReport, originalPageNumber: number, combineReportPages: boolean): number;
        static brushToStr(brush: StiBrush): string;
        static isMixedReport(report: StiReport): boolean;
    }
    class StiBookmarkTreeNode {
        parent: number;
        title: string;
        url: string;
        used: boolean;
        componentGuid: string;
    }
}
declare namespace Stimulsoft.Viewer.Helpers.Dashboards {
    import IStiElement = Stimulsoft.Report.Dashboard.IStiElement;
    import StiDataSortRule = Stimulsoft.Data.Engine.StiDataSortRule;
    import StiReport = Stimulsoft.Report.StiReport;
    class StiDataSortsHelper {
        static applySorting(report: StiReport, parameters: any): void;
        static applySortsToElement(element: IStiElement, sorts: any[]): void;
        static getElementSorts(element: IStiElement): any[];
        static sortRuleItem(sortRule: StiDataSortRule): any;
        static getSortMenuItems(element: IStiElement): any[];
        private static getSortDirection;
        private static fetchAllArguments;
        private static fetchAllValues;
        private static getSeries;
        private static getSortBy;
        private static getManualDataTable;
        private static isValuePresentedInManualData;
        private static isArgumentPresentedInManualData;
        private static isSeriesPresentedInManualData;
    }
}
declare namespace Stimulsoft.Viewer.Helpers.Dashboards {
    import StiPage = Stimulsoft.Report.Components.StiPage;
    import KeyObjectType = Stimulsoft.System.KeyObjectType;
    import IStiDrillDownElement = Stimulsoft.Data.Engine.IStiDrillDownElement;
    import StiReport = Stimulsoft.Report.StiReport;
    import List = Stimulsoft.System.Collections.List;
    import StiDataFilterRule = Stimulsoft.Data.Engine.StiDataFilterRule;
    import Type = Stimulsoft.System.Type;
    import IStiQueryObject = Stimulsoft.Data.Engine.IStiQueryObject;
    import IStiMeter = Stimulsoft.Base.Meters.IStiMeter;
    import StiDataSortRule = Stimulsoft.Data.Engine.StiDataSortRule;
    import IStiElement = Stimulsoft.Report.Dashboard.IStiElement;
    class StiDataFiltersHelper {
        static applyFiltering(report: StiReport, parameters: any): void;
        static applyFiltersToElement(element: IStiElement, filters: any[]): void;
        static getElementFilters(element: IStiElement): KeyObjectType[];
        static getFilterItems(report: StiReport, requestParams: any): any;
        private static getDataTable;
        static getViewData(report: StiReport, requestParams: any): Promise<any>;
        private static processRowValue;
        private static removeSystemMeters;
        static getDataTableFilterQueryStringRepresentation(element: IStiElement): string;
        static getDrillDownFilters(drillDownElement: IStiDrillDownElement): any[];
        static getDrillDownFiltersList(drillDownElement: IStiDrillDownElement): KeyObjectType[][];
        static isStringColumnType(element: IStiElement): boolean;
        static isBlankData(data: any): boolean;
        static resetAllFilters(report: StiReport, pageNumber: number): Promise<void>;
        static filterRuleItem(filterRule: StiDataFilterRule): KeyObjectType;
        private static sortFilterMenuItem;
        static getFilterItemsHelper(query: IStiQueryObject, meters: List<IStiMeter>, columnIndex: number, sorts: List<StiDataSortRule>, filters: List<StiDataFilterRule>, element?: IStiElement): any;
        static typeToString(type: Type): string;
        static toFilterString(value: any, type?: Type): string;
        static toDisplayString(value: any, type?: Type): string;
        private static distinct;
        private static isValueCanBeFiltered;
        private static getLevel;
        static applyDefaultFiltersForFilterElements(report: StiReport): Promise<void>;
        static applyDefaultFiltersForFilterElements2(dashboard: StiPage): Promise<void>;
        private static applyDatePickerFiltersToVariable;
    }
}
declare namespace Stimulsoft.Viewer.Helpers.Dashboards {
    import IStiElement = Stimulsoft.Report.Dashboard.IStiElement;
    import StiReport = Stimulsoft.Report.StiReport;
    class StiDashboardElementDrillDownHelper {
        static applyDashboardElementDrillDown(report: StiReport, parameters: any): void;
        static applyDrillDownToElement(element: IStiElement, filters: any[]): void;
        static applyDashboardElementDrillUp(report: StiReport, parameters: any): void;
        static applyDrillUpToElement(element: IStiElement): void;
    }
}
declare namespace Stimulsoft.Viewer.Helpers.Dashboards {
    import KeyObjectType = Stimulsoft.System.KeyObjectType;
    import StiReport = Stimulsoft.Report.StiReport;
    import StiPage = Stimulsoft.Report.Components.StiPage;
    import List = Stimulsoft.System.Collections.List;
    import IStiChart = Stimulsoft.Report.Chart.IStiChart;
    import IStiChartElement = Stimulsoft.Report.Dashboard.IStiChartElement;
    class StiChartElementViewHelper {
        private static isAllowInteractive;
        static getArgumentColumnPath(chartElement: IStiChartElement): string;
        static getSeriesColumnPath(chartElement: IStiChartElement): string;
        static getBubleXColumnPath(chartElement: IStiChartElement): string;
        static getBubleYColumnPath(chartElement: IStiChartElement): string;
        static getSeriesValues(chart: IStiChart): List<number[]>;
        static getChartValuesFromCache(cacheGuid: string, requestParams: any): any;
        static saveChartValuesToCache(cacheGuid: string, page: StiPage, requestParams: any): void;
        static getChartAnimationsFromCache(element: IStiChartElement): any;
        static saveChartAnimationsToCache(element: IStiChartElement, page: StiPage): void;
        static isBubble(chartElement: IStiChartElement): boolean;
        static getUserViewStates(chartElement: IStiChartElement): any[];
        static changeChartElementViewState(report: StiReport, requestParams: any): Promise<KeyObjectType>;
    }
}
declare namespace Stimulsoft.Viewer.Helpers.Dashboards {
    import StiReport = Stimulsoft.Report.StiReport;
    import IStiDatePickerElement = Stimulsoft.Report.Dashboard.IStiDatePickerElement;
    class StiDatePickerElementViewHelper {
        private static storedCulture;
        static getAutoRangeValues(datePickerElement: IStiDatePickerElement): Promise<any>;
        static getVariableRangeValues(datePickerElement: IStiDatePickerElement): Promise<any>;
        static getVariableValue(datePickerElement: IStiDatePickerElement): Promise<string>;
        static isVariablePresent(datePickerElement: IStiDatePickerElement): boolean;
        static isRangeVariablePresent(datePickerElement: IStiDatePickerElement): boolean;
        static getFormattedValues(report: StiReport, requestParams: any): any;
        static getColumnPath(datePickerElement: IStiDatePickerElement): string;
        static getSettings(datePickerElement: IStiDatePickerElement): any;
    }
}
declare namespace Stimulsoft.Viewer.Helpers.Dashboards {
    import StiReport = Stimulsoft.Report.StiReport;
    import KeyObjectType = Stimulsoft.System.KeyObjectType;
    import IStiTableElement = Stimulsoft.Report.Dashboard.IStiTableElement;
    class StiTableElementViewHelper {
        static getTableData(tableElement: IStiTableElement): Promise<KeyObjectType[][]>;
        static getTableHiddenData(tableElement: IStiTableElement): Promise<KeyObjectType[][]>;
        static getTableSettings(tableElement: IStiTableElement): KeyObjectType;
        private static getCellForeColor;
        private static getHeaderForeColor;
        private static getFooterForeColor;
        private static getCellAlignment;
        private static getSortLabel;
        private static getFilterLabel;
        static changeTableElementSelectColumns(report: StiReport, requestParams: any): Promise<KeyObjectType>;
    }
}
declare namespace Stimulsoft.Viewer.Helpers.Dashboards {
    import StiReport = Stimulsoft.Report.StiReport;
    class StiButtonElementHelper {
        static applyButtonEvent(report: StiReport, parameters: any): void;
    }
}
declare namespace Stimulsoft.Viewer {
    import StiReport = Stimulsoft.Report.StiReport;
    import StiHtmlExportSettings = Stimulsoft.Report.Export.StiHtmlExportSettings;
    import StiExportFormat = Stimulsoft.Report.StiExportFormat;
    import StiExportSettings = Stimulsoft.Report.Export.StiExportSettings;
    type PrintReportEventArgs = {
        sender: "Viewer";
        event: "PrintReport";
        preventDefault: boolean;
        fileName: string;
        printAction: string;
        report: StiReport;
    };
    type BeginExportReportArgs = {
        sender: "Viewer";
        event: "BeginExportReport";
        action: StiExportAction;
        preventDefault: boolean;
        async: boolean;
        settings: StiExportSettings;
        format: StiExportFormat;
        formatName: string;
        fileName: string;
        openAfterExport: boolean;
        report: StiReport;
    };
    type EndExportReportArgs = {
        sender: "Viewer";
        event: "EndExportReport";
        preventDefault: false;
        format: StiExportFormat;
        formatName: string;
        fileName: string;
        openAfterExport: boolean;
        data: string | number[];
        report: StiReport;
        settings: StiHtmlExportSettings;
    };
    type InteractionArgs = {
        sender: "Viewer";
        event: "Interaction";
        async: boolean;
        action: string;
        variables: any;
        sortingParameters: any;
        collapsingParameters: any;
        drillDownParameters: any;
        report: StiReport;
    };
    type EmailReportArgs = {
        sender: "Viewer";
        event: "EmailReport";
        settings: StiEmailSettings;
        format: StiExportFormat;
        formatName: string;
        fileName: string;
        data: string | number[];
        report: StiReport;
    };
    type DesignReportArgs = {
        sender: "Viewer";
        event: "DesignReport";
        fileName: string;
        report: StiReport;
    };
    type ShowReportArgs = {
        sender: "Viewer";
        event: "ShowReport";
        report: StiReport;
    };
    type LoadDocumentArgs = {
        sender: "Viewer";
        event: "LoadReport";
        report: StiReport;
        async: boolean;
    };
    type GetReportArgs = {
        report: null | StiReport;
    };
    class StiJsViewer {
        options: any;
        defaultParameters: any;
        controls: any;
        reportParams: any;
        assignReport(report: StiReport): any;
        initAutoUpdateCache(jsText: any, jsObject: any): any;
        postAjax(url: string, data: any, callback?: any): any;
        postAction(action: string, bookmarkPage?: any, bookmarkAnchor?: any): any;
        postEmail(format: string, settingsObject: any): any;
        postExport(format: string, settingsObject: any, action: StiExportAction, s3Callback?: any): any;
        postReportResource(resourceName: string, viewType: string): any;
        postPrint(action: string): any;
        postOpen(fileName: string, content: string): any;
        postInteraction(params: any): any;
        postDesign(): any;
        getReportParameters(action: string): any;
        viewer: StiViewer;
        InitializeErrorMessageForm(): any;
        updateVisibleState(): any;
        showParametersPanel(data: any, jsObject: any): any;
        openNewWindow(url: any, name?: any, specs?: any): any;
        removeAllEvents(): any;
        stopAllTimers(): any;
        sortPropsInDrillDownParameters(obj: any): any;
        InitializeBookmarksPanel(): any;
        createPostParameters(data: any, asObject: boolean): any;
        dashboardProcessTimeout: number;
        showReportPage(data: any, jsObject: StiJsViewer): any;
        InitializePasswordForm(): any;
        collections: {
            loc: {};
        };
        static setImageSource(htmlImage: any, options: any, collections: any, name: string, transform: boolean): any;
        static checkImageSource(options: any, collections: any, name: string): boolean;
        static getImageSource(options: any, collections: any, name: string): string;
        constructor(parameters: any, collections: any);
    }
    class StiBase64 {
    }
    class StiViewer {
        drillDownReportCache: any;
        private _renderAfterCreate;
        onPrepareVariables: (args: Report.PrepareVariablesArgs, callback: Report.PrepareVariablesContinuationCallback) => void;
        onBeginProcessData: (args: Report.BeginProcessDataArgs, callback: (args: Report.BeginProcessDataArgs) => void) => void;
        onEndProcessData: (args: Report.EndProcessDataArgs) => void;
        onPrintReport: (args: PrintReportEventArgs) => void;
        onBeginExportReport: (args: BeginExportReportArgs, callback: (args: BeginExportReportArgs) => void) => void;
        onEndExportReport: (args: EndExportReportArgs) => void;
        onInteraction: (args: InteractionArgs, callback: (args: InteractionArgs) => (void | Promise<void>)) => void;
        onEmailReport: (args: EmailReportArgs) => void;
        onDesignReport: (args: DesignReportArgs) => void;
        onShowReport: (args: ShowReportArgs) => void;
        onLoadDocument: (args: LoadDocumentArgs, callback: () => (void | Promise<void>)) => void;
        onGetReport: (args: GetReportArgs) => void;
        onGetSubReport: Function;
        private reportCache;
        viewerId: string;
        options: StiViewerOptions;
        jsObject: StiJsViewer;
        currentReportGuid: string;
        get reportTemplate(): StiReport;
        get report(): StiReport;
        set report(value: StiReport);
        private _visible;
        get visible(): boolean;
        set visible(value: boolean);
        private _element;
        renderHtml(element?: string | HTMLElement): void;
        invokeComponentsEvents(params: any): void;
        private invokePrepareVariables;
        private invokeBeginProcessData;
        private invokeEndProcessData;
        private invokePrintReport;
        private invokeBeginExportReport;
        private invokeEndExportReport;
        private invokeInteraction;
        private invokeEmailReport;
        private invokeDesignReport;
        private invokeShowReport;
        private invokeLoadDocument;
        private invokeGetReport;
        private invokeOnGetSubReport;
        private getReportPageAsync;
        private getPagesArray;
        private getReportFileName;
        showProcessIndicator(): void;
        hideProcessIndicator(): void;
        refreshViewer(): void;
        dispatch(removeEvent?: boolean): void;
        applyTheme(theme: StiViewerTheme): void;
        setTheme(theme: StiViewerTheme): void;
        constructor(options?: StiViewerOptions, viewerId?: string, renderAfterCreate?: boolean);
    }
}
declare namespace Stimulsoft.Viewer {
    class StiViewerOptions {
        private requestResourcesUrl;
        private requestStylesUrl;
        private requestAbsoluteUrl;
        private productVersion;
        private actions;
        private server;
        private buildDate;
        private shortProductVersion;
        private cultureName;
        private localization;
        private cloudMode;
        private serverMode;
        private formValues;
        private requestUrl;
        appearance: StiAppearanceOptions;
        toolbar: StiToolbarOptions;
        exports: StiExportsOptions;
        email: StiEmailOptions;
        width: string;
        height: string;
        viewerId: string;
        reportDesignerMode: boolean;
        toParameters(): any;
        private getDefaultExportSettings;
        private serializeObject;
    }
}
declare namespace Stimulsoft.Viewer {
    class StiRequestParams {
        interaction: StiInteractionParams;
    }
    class StiInteractionParams {
        variables: {};
        sorting: {};
        collapsing: {};
        drillDown: any[];
        editable: {};
        dashboardFiltering: {};
        dashboardSorting: {};
    }
}
declare namespace Stimulsoft.Viewer {
    class StiThemesHelper {
        static getThemeName(themeType: {}, themeValue: number): string;
        static getAccentTheme(themeType: {}, themeValue: number): string;
        private static replaceStyleConstants;
        static applyTheme(themeName: string, accentName: string, appName: string): void;
        static getImage(obj: any, path: string[]): any;
        static getImageSource(sourceObject: any, options: any, name: string): {
            data: string;
            scale: number;
        };
        private static getImageSourceInternal;
        static setImageSource(sourceObject: any, htmlImage: any, options: any, name: string, transform: boolean): void;
    }
}
declare namespace Stimulsoft.Viewer {
    class StiCollectionsHelper {
        static getLocalizationItems(): any;
    }
}
declare namespace Stimulsoft.Viewer {
    import StiReport = Stimulsoft.Report.StiReport;
    class StiEditableFieldsHelper {
        static checkEditableReport(report: StiReport): boolean;
        static applyEditableFieldsToReport(report: StiReport, parameters: any): void;
    }
}
declare namespace Stimulsoft.Viewer {
    import Font = Stimulsoft.System.Drawing.Font;
    import Color = Stimulsoft.System.Drawing.Color;
    import StiReport = Stimulsoft.Report.StiReport;
    class StiElectronicSignatureHelper {
        static getSignatureData(report: StiReport, requestParams: StiRequestParams): any;
        private static getSignatureComponents;
        static getStylesForSignature(): any[];
        static checkSignedReport(report: StiReport): boolean;
        private static colorToString;
        static stringToColor(colorStr: string): Color;
        static jsonToFont(fontJson: any): Font;
        private static imageToBase64;
        static base64ToByteArray(base64String: string): number[];
    }
}
declare namespace Stimulsoft.Viewer {
    import StiPromise = Stimulsoft.System.StiPromise;
    import StiReport = Stimulsoft.Report.StiReport;
    import StiExportFormat = Stimulsoft.Report.StiExportFormat;
    import IStiDashboardExportSettings = Stimulsoft.Report.Dashboard.Export.IStiDashboardExportSettings;
    class StiExportsHelper {
        static getReportFileName(report: StiReport): string;
        static applyExportSettings(exportFormat: StiExportFormat, settingsObject: any, settings: any): void;
        static getDashboardExportSettings(exportFormat: StiExportFormat, settingsObject: any): IStiDashboardExportSettings;
        private static getPdfDashboardExportSettings;
        private static getExcelDashboardExportSettings;
        private static getDataDashboardExportSettings;
        private static getImageDashboardExportSettings;
        private static getHtmlDashboardExportSettings;
        static exportDashboardAsync(requestParams: any, report: StiReport, exportSettings: IStiDashboardExportSettings): StiPromise<number[]>;
    }
}
declare namespace Stimulsoft.Viewer {
    class StiFontsHelper {
        static getFontNames(): any[];
    }
}
declare namespace Stimulsoft.Viewer {
    import StiReport = Stimulsoft.Report.StiReport;
    class StiReportContainer {
        report: StiReport;
        resourcesIncluded: boolean;
        command: string;
        constructor(report: StiReport, resourcesIncluded: boolean, command?: string);
    }
}
declare namespace Stimulsoft.Viewer {
    import StiReport = Stimulsoft.Report.StiReport;
    class StiReportCopier {
        static cloneReport(report: StiReport, withResources: boolean): StiReport;
        private static cloneResources;
        static copyReportDictionary(reportFrom: StiReport, reportTo: StiReport): void;
        static copyElementsDrillDown(reportFrom: StiReport, reportTo: StiReport): void;
        static copyFilterElementsUserFilters(reportFrom: StiReport, reportTo: StiReport): void;
        static copyEventsFunction(reportFrom: StiReport, reportTo: StiReport): void;
    }
}
declare namespace Stimulsoft.Viewer {
    import StiResourcesCollection = Stimulsoft.Report.Dictionary.StiResourcesCollection;
    import StiResourceType = Stimulsoft.Report.Dictionary.StiResourceType;
    import StiReport = Stimulsoft.Report.StiReport;
    class StiReportResourceHelper {
        static getResourcesItems(report: StiReport): any[];
        static isFontResourceType(resourceType: StiResourceType): boolean;
        static getFontResourcesArray(report: StiReport): any[];
        static getBase64DataFromFontResourceContent(resourceType: StiResourceType, content: number[]): string;
        static loadResourcesToReport(report: StiReport, resources: StiResourcesCollection): void;
    }
}
declare namespace Stimulsoft.Viewer {
    import KeyObjectType = Stimulsoft.System.KeyObjectType;
    import StiReport = Stimulsoft.Report.StiReport;
    class StiVariablesHelper {
        private en_us_culture;
        static fillDialogInfoItems(report: StiReport): Promise<void>;
        private static getVariableAlias;
        private static getItems;
        private static containsBindingVariableValue;
        private static getDateTimeObject;
        private static getTimeSpanStringValue;
        private static getBasicType;
        private static getStiType;
        static applyReportParameters(report: StiReport, values: any): void;
        static transferParametersValuesToReport(report: StiReport, values: any): void;
        static applyReportBindingVariables(report: StiReport, values: any): void;
        private static getLabelValue;
        private static setVariableLabel;
        private static setVariableValue;
        static getVariables(report: StiReport, values: any, sortDataItems: boolean): Promise<any>;
        static getVariablesValues(report: StiReport): KeyObjectType;
    }
}
declare namespace Stimulsoft.Viewer.Helpers.Dashboards {
    import KeyObjectType = Stimulsoft.System.KeyObjectType;
    import IStiComboBoxElement = Stimulsoft.Report.Dashboard.IStiComboBoxElement;
    import List = Stimulsoft.System.Collections.List;
    import StiDataTable = Stimulsoft.Data.Engine.StiDataTable;
    class StiComboBoxElementViewHelper {
        static getElementItems(comboBoxElement: IStiComboBoxElement): Promise<List<any>>;
        static comboBoxItem(label: string, value: any): KeyObjectType;
        static getSettings(comboBoxElement: IStiComboBoxElement): any;
        protected static getNameMeterIndex(table: StiDataTable): number;
        protected static getKeyMeterIndex(table: StiDataTable): number;
        static getColumnPath(comboBoxElement: IStiComboBoxElement): string;
    }
}
declare namespace Stimulsoft.Viewer.Helpers.Dashboards {
    import IStiImageElement = Stimulsoft.Report.Dashboard.IStiImageElement;
    import KeyObjectType = Stimulsoft.System.KeyObjectType;
    class StiImageElementViewHelper {
        static getImageSvgContent(imageElement: IStiImageElement, scaleX?: number, scaleY?: number, requestParams?: any): Promise<KeyObjectType>;
    }
}
declare namespace Stimulsoft.Viewer.Helpers.Dashboards {
    import KeyObjectType = Stimulsoft.System.KeyObjectType;
    import IStiListBoxElement = Stimulsoft.Report.Dashboard.IStiListBoxElement;
    import List = Stimulsoft.System.Collections.List;
    import StiDataTable = Stimulsoft.Data.Engine.StiDataTable;
    class StiListBoxElementViewHelper {
        static getElementItems(listBoxElement: IStiListBoxElement): Promise<List<any>>;
        static listBoxItem(label: string, value: any): KeyObjectType;
        static getSettings(listBoxElement: IStiListBoxElement): any;
        protected static getNameMeterIndex(table: StiDataTable): number;
        protected static getKeyMeterIndex(table: StiDataTable): number;
        static getColumnPath(listBoxElement: IStiListBoxElement): string;
    }
}
declare namespace Stimulsoft.Viewer.Helpers.Dashboards {
    import KeyObjectType = Stimulsoft.System.KeyObjectType;
    import IStiPivotTableElement = Stimulsoft.Report.Dashboard.IStiPivotTableElement;
    class StiPivotTableElementViewHelper {
        static getPivotTableData(pivotElement: IStiPivotTableElement): Promise<any>;
        static getPivotTableSettings(tableElement: IStiPivotTableElement): KeyObjectType;
        private static getCellAlignment;
    }
}
declare namespace Stimulsoft.Viewer.Helpers.Dashboards {
    import IStiRegionMapElement = Stimulsoft.Report.Dashboard.IStiRegionMapElement;
    class StiRegionMapElementViewHelper {
        static getColumnPath(regionMapElement: IStiRegionMapElement): string;
    }
}
declare namespace Stimulsoft.Viewer.Helpers.Dashboards {
    import KeyObjectType = Stimulsoft.System.KeyObjectType;
    import IStiTreeViewBoxElement = Stimulsoft.Report.Dashboard.IStiTreeViewBoxElement;
    import List = Stimulsoft.System.Collections.List;
    import IStiMeter = Stimulsoft.Base.Meters.IStiMeter;
    class StiTreeViewBoxElementViewHelper {
        static getElementItems(treeViewBoxElement: IStiTreeViewBoxElement): Promise<List<KeyObjectType>>;
        static treeViewBoxItem(treeViewBoxElement: IStiTreeViewBoxElement, key?: any, meter?: IStiMeter): KeyObjectType;
        static getSettings(treeViewBoxElement: IStiTreeViewBoxElement): any;
        static getColumnPath(treeViewBoxElement: IStiTreeViewBoxElement): string;
        static getMeterKey(treeViewBoxElement: IStiTreeViewBoxElement): string;
    }
}
declare namespace Stimulsoft.Viewer.Helpers.Dashboards {
    import KeyObjectType = Stimulsoft.System.KeyObjectType;
    import IStiTreeViewElement = Stimulsoft.Report.Dashboard.IStiTreeViewElement;
    import List = Stimulsoft.System.Collections.List;
    import IStiMeter = Stimulsoft.Base.Meters.IStiMeter;
    class StiTreeViewElementViewHelper {
        static getElementItems(treeViewElement: IStiTreeViewElement): Promise<List<KeyObjectType>>;
        static treeViewItem(treeViewElement: IStiTreeViewElement, key?: any, meter?: IStiMeter): KeyObjectType;
        static getSettings(treeViewElement: IStiTreeViewElement): any;
        static getColumnPath(treeViewElement: IStiTreeViewElement): string;
        static getMeterKey(treeViewElement: IStiTreeViewElement): string;
    }
}
declare namespace Stimulsoft.Viewer {
    import Color = Stimulsoft.System.Drawing.Color;
    import StiHtmlExportMode = Stimulsoft.Report.Export.StiHtmlExportMode;
    class StiAppearanceOptions {
        private storeExportSettings;
        private showReportIsNotSpecifiedMessage;
        private customStylesUrl;
        private saveMenuImageSize;
        private designWindow;
        private printToPdfMode;
        private imagesQuality;
        backgroundColor: Color;
        pageBorderColor: Color;
        rightToLeft: boolean;
        fullScreenMode: boolean;
        scrollbarsMode: boolean;
        openLinksWindow: string;
        openExportedReportWindow: string;
        showTooltips: boolean;
        showTooltipsHelp: boolean;
        showDialogsHelp: boolean;
        pageAlignment: StiContentAlignment;
        showPageShadow: boolean;
        bookmarksPrint: boolean;
        bookmarksTreeWidth: number;
        parametersPanelPosition: StiParametersPanelPosition;
        parametersPanelMaxHeight: number;
        parametersPanelColumnsCount: number;
        parametersPanelDateFormat: string;
        parametersPanelSortDataItems: boolean;
        interfaceType: StiInterfaceType;
        chartRenderType: StiChartRenderType;
        reportDisplayMode: StiHtmlExportMode;
        datePickerFirstDayOfWeek: StiFirstDayOfWeek;
        datePickerIncludeCurrentDayForRanges: boolean;
        allowTouchZoom: boolean;
        allowMobileMode: boolean;
        combineReportPages: boolean;
        htmlRenderMode: StiHtmlExportMode;
        theme: StiViewerTheme;
        iconSet: StiWebUIIconSet;
    }
}
declare namespace Stimulsoft.Viewer {
    class StiEmailOptions {
        showEmailDialog: boolean;
        showExportDialog: boolean;
        defaultEmailAddress: string;
        defaultEmailSubject: string;
        defaultEmailMessage: string;
    }
}
declare namespace Stimulsoft.Viewer {
    class StiExportsOptions {
        private showExportToMht;
        private showExportToRtf;
        private showExportToExcel;
        private showExportToExcelXml;
        private showExportToDbf;
        private showExportToXml;
        private showExportToDif;
        private showExportToSylk;
        private showExportToImageBmp;
        private showExportToImageGif;
        private showExportToImageJpeg;
        private showExportToImagePcx;
        private showExportToImagePng;
        private showExportToImageTiff;
        private showExportToImageMetafile;
        private showExportToImageSvgz;
        private openAfterExport;
        private showOpenAfterExport;
        storeExportSettings: boolean;
        showExportDialog: boolean;
        showExportToDocument: boolean;
        showExportToPdf: boolean;
        showExportToHtml: boolean;
        showExportToHtml5: boolean;
        showExportToWord2007: boolean;
        showExportToExcel2007: boolean;
        showExportToCsv: boolean;
        showExportToJson: boolean;
        showExportToText: boolean;
        showExportToOpenDocumentWriter: boolean;
        showExportToOpenDocumentCalc: boolean;
        showExportToPowerPoint: boolean;
        showExportToImageSvg: boolean;
        showExportToXps: boolean;
    }
}
declare namespace Stimulsoft.Viewer {
    import Color = Stimulsoft.System.Drawing.Color;
    class StiToolbarOptions {
        visible: boolean;
        displayMode: StiToolbarDisplayMode;
        backgroundColor: Color;
        borderColor: Color;
        fontColor: Color;
        fontFamily: string;
        alignment: StiContentAlignment;
        showButtonCaptions: boolean;
        showPrintButton: boolean;
        showOpenButton: boolean;
        showSaveButton: boolean;
        showSendEmailButton: boolean;
        showFindButton: boolean;
        showSignatureButton: boolean;
        showBookmarksButton: boolean;
        showParametersButton: boolean;
        showResourcesButton: boolean;
        showEditorButton: boolean;
        showFullScreenButton: boolean;
        showRefreshButton: boolean;
        showFirstPageButton: boolean;
        showPreviousPageButton: boolean;
        showCurrentPageControl: boolean;
        showNextPageButton: boolean;
        showLastPageButton: boolean;
        showZoomButton: boolean;
        showViewModeButton: boolean;
        showDesignButton: boolean;
        showAboutButton: boolean;
        showPinToolbarButton: boolean;
        printDestination: StiPrintDestination;
        viewMode: StiWebViewMode;
        multiPageWidthCount: number;
        multiPageHeightCount: number;
        private _zoom;
        get zoom(): number;
        set zoom(value: number);
        menuAnimation: boolean;
        showMenuMode: StiShowMenuMode;
        autoHide: boolean;
    }
}
