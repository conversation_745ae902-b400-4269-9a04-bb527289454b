import {ChangeDete<PERSON><PERSON><PERSON>, Component, ElementRef, NgZone, OnInit, Renderer2, ViewChild} from '@angular/core';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>er, DomController, NavController, Platform} from '@ionic/angular';
import {ActivatedRoute, NavigationExtras, ParamMap} from '@angular/router';
import {Subject} from 'rxjs';
import {Asset} from 'src/app/models/asset';
import {ScanService} from 'src/app/services/scan/scan.service';
import {WorkflowEngineTask} from 'src/app/models/workflow/WorkflowEngineTask';
import {
  WorkflowGetApplicationWorkflowListService
} from 'src/app/services/workflow/get-applicationWorkflowList/workflow-getApplicationWorkflowList.service';
import {AssetTypeItem} from 'src/app/models/assetTypeItem';
import {StorageUtils} from 'src/app/utils/storage-utils';
import {WorkflowActionsService} from 'src/app/services/workflow/workflow-actions.service';
import {WfPageWidgetService} from 'src/app/services/workflow/workflow-service/other/wf-page-widget.service';
import {WfConstService} from 'src/app/services/workflow/workflow-service/other/wf-const.service';
import {
  ApprovalAssetPageService
} from 'src/app/services/workflow/workflow-service/approval/scan-list/approval-asset-page.service';
import {AssetService} from 'src/app/services/asset/asset.service';
import {HttpGetWorkflowListService} from 'src/app/httpUtils/workflow/http-getWorkflowList.service';
import {HttpAssetService} from 'src/app/httpUtils/asset/http-aseet.service';
import {WorkflowAssetColumn} from 'src/app/models/workflow/WorkflowAssetColumn';
import {Throttle} from 'src/app/utils/throttle.decorator';
import {AssetDetailService} from 'src/app/services/assetdetail/asset-detail.service';
import {AppComponent} from '../../../../app.component';
import {
  AssetsByKeywordInWfAssetListForAssignScanAPI,
  ChosenAsseDataInterface,
  MANUAL_TYPE_ENUM,
  ScanAssetDataInterface,
  ScanDataInterface,
  WFDataNumAndNextPageType,
  WorkflowexecDataInterface,
} from 'src/app/models/workflow/AssetsByKeywordInWfAssetListForAssignScan';
import * as _ from 'lodash';
import {HttpUtilsService} from '../../../../utils/http-utils.service';
import {LoginService} from '../../../../services/login/login.service';
import {KeyboardUtil} from 'src/app/utils/KeyboardUtil';
import {VListPage} from '../../../../v-list/v-list.page';
import { Keyboard } from '@capacitor/keyboard';
import { validateNumericAndCurrencyField } from '../../../../utils/utils';

@Component({
  selector: 'app-list',
  templateUrl: './list.page.html',
  styleUrls: ['./list.page.scss'],
})
export class ListPage extends VListPage<any> implements OnInit {
  @ViewChild('searchBox') searchBox: ElementRef; // 検索ボックス
  /** 是否显示回到顶部按钮 */
  isShowScrollToTop: boolean = false;
  isScanComplete: boolean = false; // すべてのスキャンが完了した
  isReleaseMode: boolean = false; // 実行解除選択モード
  releaseModeSubject: Subject<boolean> = new Subject(); // 実行解除モードを監視
  releaseModeState = this.releaseModeSubject.asObservable();
  firstLoad: boolean = true; // 画面初期ロードフラグ
  assetList: Asset[] = []; // 資産一覧
  wfModel: WorkflowEngineTask; // ワークフロー承認情報
  rawFormData;
  updateIDAction;
  updateAssetAction;
  scanType;
  scanResult;
  wfDict: { [key: string]: AssetTypeItem[] };
  headerActionStatus = [false, false];
  formPostVariables;
  isFromLocationSettingPage: boolean = false;
  dataForScan: any;
  locationInfo: string;
  showSendBackButton: boolean = false;
  showRejectButton: boolean = false;
  showUnclaimButton: boolean = false;
  backDestination: string = '';
  comments = [];
  confirmData;
  allMustScan: boolean;
  scanAssetTaskIdList: string[] = [];
  isRentalWorkflow = false;
  showAssetLocation: boolean = false; // 資産情報表示画面で「場所」が表示されるか
  pageAction: ApprovalAssetPageService;
  isCountingType: boolean = false; // 個数管理場合
  hasScanTask = false; // スキャンタスクあるか
  stepName;
  workflowScript;
  assetTypeId; // 資産種類I D
  widgetList = {
    countHeader: false,
    subform: false,
  };
  showAssetCardType = {
    default: true,
    newW3: false,
  };
  HOME_IMAGE = 'homeImage';
  subformTitle = '';

  assetTypeItemMap: { [key: number]: AssetTypeItem };
  private assetNameDisplayName: string;
  private identityCodeDisplayName: string;
  assetColumns: WorkflowAssetColumn[] = [];
  private dynamicTaskInfo = [];
  private dynamicTantousha = [];
  isDisabled = false;
  private backupEnterScannedCount: { [key: string]: number } = {};

  /** input search 最终api传递数据 核心 */
  dataNumAndNextPage: WFDataNumAndNextPageType = {
    processInstanceId: undefined,
    taskId: undefined,
    skip: undefined,
    rows: undefined,
    keyword: undefined,
    scanState: undefined,
    moreThenLimit: false,
  };
  /** 每次后台返回多少条数据 */
  private getAmountDataNumber = 20;
  /**  Segment，只是用作中间临时传递操作 */
  workflowSegmentKey: 'workflowAll' | 'workflowScanned' | 'workflowUnscanned' = 'workflowAll';
  /** 检索资产时关键字，只是用作中间临时传递操作 */
  workflowInputKey = '';
  /** wf中的资产数量 */
  workflowexecData: WorkflowexecDataInterface = {
    totalAssets: 0,
    numberOfUnscanned: 0,
    totalSavedAssetAmountCount: 0,
    allAssetCountInKeywordSearch: 0,
    noScanningAssetCountInKeywordSearch: 0,
    scannedAssetCount: 0
  };
  /** 页码 */
  private nextPageNum = 1;
  /**
   * 只存被选中的资产（扫描选中或者手动增加）
   * key => assetId资产ID
   * val =>
   * isScan是否为扫描增加的资产，
   * initialNum初始数量,
   * updatedNum修改后数量,
   * scanNum如果isScan为true将记录扫描了多少次
   */
  private chosenAssetMap: Map<string, ChosenAsseDataInterface> = new Map();
  /** 是否有权限一时保存 */
  isPermissionWF = false;
  isPermission: boolean = false; // 権限か
  isReloadData = false; // pull down for reload data
  noData = false; //「全件表示されました」表示
  simplyMode = false; // 是否隐藏右侧图标
  // view页面回调专用
  setHasBeenChangedAsset;
  private lastInputElement: HTMLInputElement;
  // 判断键盘状态
  isKeyboardVisible: boolean = false;

  constructor(
    navController: NavController,
    private scanService: ScanService,
    route: ActivatedRoute,
    private http: HttpUtilsService,
    private WorkflowGetApplicationWorkflowListService: WorkflowGetApplicationWorkflowListService,
    private workflowActionsService: WorkflowActionsService,
    private assetService: AssetService,
    private httpGetWorkflowListService: HttpGetWorkflowListService,
    private httpAssetService: HttpAssetService,
    private alertController: AlertController,
    private assetDetailService: AssetDetailService,
    private loginService: LoginService,
    private element: ElementRef,
    private renderer: Renderer2,
    private domCtrl: DomController,
    private platform: Platform,
    private changeDetectorRef: ChangeDetectorRef,
    ngZone: NgZone,
  ) {
    super(navController, route, ngZone);
  }

  /**
   * 初期化
   * @param {type} なし
   */
  ngOnInit() {
    super.ngOnInit();
    this.activatedRoute.queryParams.subscribe(async (params) => {
      // 当页面是在切换场所的时候不做parametr的更新
      const isFromLocationSettingPage = params['isFromLocationSettingPage'];
      if (isFromLocationSettingPage === 'true') {
        AppComponent.backButtonFlag = 'location';
        if (params['location'] === '拠点指定なし') {
          this.locationInfo = '';
        } else {
          this.locationInfo = params['location'];
        }
        const scanType = params['scanType'];
        this.toScanPage(true, scanType);
        return;
      }
      if (this.firstLoad) {
        //リスト画面から
        this.allMustScan = params['allMustScan'];
        let claimNow = params['shouldClaim'];
        this.dynamicTantousha = params['dynamicTantousha'];
        this.dynamicTaskInfo = params['dynamicTaskInfo'];
        this.rawFormData = params['rawFormData'];
        this.updateIDAction = params['updateIDAction'];
        this.updateAssetAction = params['updateAssetAction'];
        this.wfModel = params['rawWorkFlowModel'];
        this.wfDict = params['wfDict'];
        this.formPostVariables = params['formPostVariables'];
        this.comments = params['comments'];
        this.confirmData = params['confirmData'];
        this.assetTypeId = this.confirmData['assetTypeIdWithFirstWf'];
        this.stepName = params['stepName'];
        this.workflowScript = params['workflowScript'];
        this.setHasBeenChangedAsset = params['setHasBeenChangedAsset'];
        this.simplyMode = params['simplyMode'] || false;
        this.pageAction = new ApprovalAssetPageService(this.rawFormData, this.navController, this.http);
        this.updateShowWidgetList();
        // 初始数据整理
        let isNewW3 = this.pageAction.workFlowStatus == WfConstService.APPROVAL_ASSET_STATUS_SCAN_CONFIRM;
        // subform特殊不需要分页
        this.getAmountDataNumber = isNewW3 ? 9999 : 20;
        this.dataNumAndNextPage = {
          processInstanceId: _.toNumber(this.wfModel.processInstanceId),
          taskId: _.toNumber(this.wfModel.id),
          scanState: undefined,
          skip: 0,
          rows: this.getAmountDataNumber,
          keyword: undefined,
          moreThenLimit: false,
        };
        console.log('💡======start==ngOnInit==========');
        console.log('💡ngOnInit isSubForm ==> ', this.isPassedSubProcessWithSubForm);
        console.log('💡ngOnInit isNewW3 ==> ', isNewW3);
        console.log('💡ngOnInit params ==> ', params);
        console.log('💡ngOnInit firstLoad ==> ', this.firstLoad);
        console.log('💡ngOnInit dataNumAndNextPage ==> ', this.dataNumAndNextPage);
        console.log('💡======end==ngOnInit==========');
        await this.http.addLoadingQueue();
        try {
          await this.loadData(this.processInstanceId, this.taskID);
        } finally {
          this.http.removeLoadingQueue();
        }

        if (claimNow) {
          this.claim();
        }

        this.firstLoad = false;
        let userName = await StorageUtils.get(StorageUtils.KEY_USER_NAME);
        this.locationInfo = await StorageUtils.getLocation(userName);
      }
      // process confirm from subform
      if (params['subform']) {
        this.newW3AfterConfrimStateReducer(params['subform']);
      }
    });

    // 差し戻しモードが解除されたら選択を解除
    this.releaseModeState.subscribe(() => {
      if (!this.isReleaseMode) {
        this.releaseSelectAll();
      }
    });

    Keyboard.addListener('keyboardWillShow', () => {
      this.isKeyboardVisible = true;
    });

    Keyboard.addListener('keyboardWillHide', () => {
      this.isKeyboardVisible = false;
    });
  }

  get isFullMode() {
    return !(this.simplyMode == true);
  }

  /**
   * 画面表示するとき
   * @param {type} なし
   */
  async ionViewWillEnter() {
    // クエリーパラメーターによってスキャン完了にする
    // 寄存代码  以下是原生扫描时切换场所，回调用 可以优化
    this.activatedRoute.queryParamMap.subscribe((params: ParamMap) => {
      const isScanComplete = params.get('scan-complete');
      console.log('💡======start==ionViewWillEnter==========');
      console.log('💡ionViewWillEnter params ==> ', params);
      console.log('💡======end==ionViewWillEnter==========');
      if (isScanComplete === 'true') {
        this.isScanComplete = true;
        // すべてのアイテムを完了ステータスにする
        this.itemsList.forEach((item) => {
          item.status = '完了';
        });
      }
    });

    await new KeyboardUtil(this.element, this.renderer, this.domCtrl).addListener('ion-footer');
  }

  /**
   * 画面widget更新
   * @param {type} なし
   */
  updateShowWidgetList() {
    let hasSubformAfterScan = this.isPassedSubProcessWithSubForm;

    this.widgetList.countHeader = this.pageAction.showWidget(WfPageWidgetService.APPROVAL_ASSETS_COUNT_BAR);
    this.widgetList.subform =
      (this.rawFormData.subformId ?? false) &&
      this.rawFormData.subformId != '' &&
      (this.pageAction.assetCardUIType == WfPageWidgetService.APPROVAL_ASSETS_TYPE_NEW_W3 || hasSubformAfterScan);
    this.showAssetCardType = {
      default: this.pageAction.assetCardUIType == WfPageWidgetService.APPROVAL_ASSETS_TYPE_DEFAULT,
      newW3: this.pageAction.assetCardUIType == WfPageWidgetService.APPROVAL_ASSETS_TYPE_NEW_W3,
    };
  }

  /**
   * 資産リスト更新
   * @param {type} なし
   */
  async updateAssetList() {
    // 並び順を調整
    let assetList: any;
    if (this.isNosubprocess) {
      assetList = this.sortAssetListData(this.assetList);
    } else {
      assetList = this.assetList;
    }
    const itemDataList = [];
    for (const asset of assetList) {
      let isFinishScan;
      if (this.isCountingType) {
        isFinishScan = asset.savedAssetAmount === asset.assetAmount;
      } else {
        isFinishScan = asset.scanState === '1';
      }

      let totalAmount = WfPageWidgetService.WF_DEFAULT_ASSET_AMOUNT;

      if (this.isRentalWorkflow) {
        totalAmount = asset['assetCount'];
      }
      if (this.isCountingType) {
        totalAmount = asset['assetAmount'];
      }
      if (totalAmount === undefined) {
        totalAmount = WfPageWidgetService.WF_DEFAULT_ASSET_AMOUNT;
      }

      let assetScanedCount;
      let isSelect = false;
      if (this.isCountingType || this.isRentalWorkflow) {
        assetScanedCount = asset.savedAssetAmount;
        if (assetScanedCount > 0) {
          isSelect = true;
        }
      } else {
        assetScanedCount = asset.scanState === '1' ? totalAmount : 0;
      }

      let step = 'スキャン';
      //セクション関連の設定
      let assetDic = JSON.parse(asset['assetText']);
      let status = asset['processStepName'] == '' ? ' - ' : asset['processStepName'];

      // ホーム画像の更新
      let homeImage = undefined;
      loopa: for (const iterator in this.assetTypeItemMap) {
        const item = this.assetTypeItemMap[iterator];
        if (item.itemType == 'image' && item.mobileFlg == '1') {
          let imageTypeItem = assetDic[item.itemName];
          if (typeof imageTypeItem == 'string' && imageTypeItem != '') {
            imageTypeItem = JSON.parse(imageTypeItem);
          }
          if (imageTypeItem instanceof Array) {
            if (imageTypeItem.length > 0) {
              const homeImageItem = imageTypeItem.find((value) => {
                return value['isHomeImage'] == true;
              });
              if (homeImageItem != undefined && homeImageItem != null) {
                let isView = true;
                // 获取item中的option并转换json
                let optionObj = JSON.parse(item.option);
                let sectionPrivateGroups = optionObj.sectionPrivateGroups;
                if (sectionPrivateGroups) {
                  isView = await this.loginService.visiblePermissionsCheck(sectionPrivateGroups);
                }
                if (!isView) {
                  continue;
                }
                homeImage = homeImageItem;
                break loopa;
              }
            }
          }
        }
      }

      let assignedBy = '';
      let subformDisabled = true;
      let subFromBytaskId = '';
      // 以下是专门为 NEW_W3 准备
      const isNewW3 = this.pageAction.assetCardUIType == WfPageWidgetService.APPROVAL_ASSETS_TYPE_NEW_W3;
      if (isNewW3) {
        const subProcessProductAssetList: any[] = this.rawFormData?.subProcessProductAssetList;
        if (!_.isEmpty(subProcessProductAssetList)) {
          const subProcessProductAsset = subProcessProductAssetList.find(
            (subPitem) => _.toString(subPitem.assetId) === _.toString(asset['assetId']),
          );
          step = subProcessProductAsset['stepName'];
          assignedBy = subProcessProductAsset['assignedBy'];
          subFromBytaskId = subProcessProductAsset['taskId'];
        }
        if (this.scanAssetTaskIdList.includes(assetDic['identityCode'])) {
          status = '完了';
        }
        // 后台规定，如果当前subform wf 中的资产已经处于终了状态将不会返回taskId
        if (_.isEmpty(subFromBytaskId)) {
          status = '完了';
        }
        if (status === '完了') {
          subformDisabled = false;
        }
      } else {
        if (isFinishScan && this.isNosubprocess) {
          status = '完了';
          step = 'スキャン済';
        } else {
          // メタルワン場合、ステータスは完了かどうかを判断する
          if (this.isPassedSubProcessWithSubForm) {
            for (let subProcessProductAsset of this.rawFormData.subProcessProductAssetList) {
              if (subProcessProductAsset.assetId + '' === asset['assetId'] + '') {
                if (subProcessProductAsset.taskId === null) {
                  status = '完了';
                  step = subProcessProductAsset.stepName;
                }
                break;
              }
            }
          }
        }

        if (this.isNosubprocess && !this.allMustScan && !this.isRentalWorkflow) {
          if (asset.savedAssetAmount > 0) {
            status = '完了';
          }
        }
      }

      if (this.backupEnterScannedCount[assetDic['identityCode'] + assetDic['location']] === undefined) {
        this.backupEnterScannedCount[assetDic['identityCode'] + assetDic['location']] = assetScanedCount;
        console.log(
          '存储首次进入时资产扫描个数信息:',
          assetDic['identityCode'] + assetDic['location'],
          ',个数:',
          assetScanedCount,
        );
      }
      const tempItem = {
        // 重点重点重点 一旦修改了item也需要修改其他文件，所需要修改的全部文件请参考 -> asset-force/item-needs-to-modify-the-file.txt
        assetItems: asset['assetItemList'],
        name: assetDic['assetName'],
        id: assetDic['identityCode'],
        assetId: asset['assetId'],
        thumbnail: homeImage,
        location: assetDic['location'],
        step: step,
        status: status,
        isSelect: isSelect,
        typeNumber: assetDic['型番'] ?? '',
        classification: asset['mpFlag'],
        assetTotalCount: totalAmount,
        assetScanedCount: assetScanedCount,
        rawAssetList: asset,
        isCurrentPageTheAsset: true,
        assignedBy: assignedBy,
        subformDisabled: subformDisabled,
        subFromBytaskId: subFromBytaskId,
        scanState: asset['scanState'],
      };
      itemDataList.push(tempItem);
    }
    return itemDataList;
  }

  /**
   * ボタン状態の初期化
   * @param {type} なし
   */
  initButtonStatus() {
    // claim button
    let isScanType = this.rawFormData['taskType'] == 'SCAN';
    let showClamin = (this.rawFormData['actions']['possibleOfClaim'] ?? false) || isScanType;
    let claimDisable = !isScanType;

    let claimButtonStatus = {
      show: showClamin,
      disabaled: claimDisable,
    };

    let comfirmButtonStatus = {
      show: true,
      disabaled: false,
    };

    let okButtonStatus = {
      show: false,
      disabaled: false,
    };
  }

  /**
   * 選択中のアイテムがある
   * 削除
   */
  get hasSelect() {
    if (_.isEmpty(this.itemsList)) {
      return false;
    }
    return this.itemsList.filter((item) => item.isSelect).length > 0;
  }

  /**
   * 実行された資産がある
   */
  get hasExec() {
    if (!this.itemsList) {
      return false;
    }
    // let hasUnscanAssets = this.items.filter((item) => item.status != '完了').length > 0;
    let hasUnscanAssets = true;
    let possibleOfClaim = this.rawFormData['actions']['possibleOfClaim'] ?? false;
    let isScanType = this.rawFormData['taskType'] == 'SCAN';
    return (
      this.isPermission && ((hasUnscanAssets && !possibleOfClaim && isScanType) || (this.pageAction?.canScan ?? false))
    );
  }

  /**
   * ランタル向けのnosubprocessスキャン
   */
  get isNosubprocess() {
    return this.nonSubProcess;
  }

  /**
   * possibleOfClaim
   */
  get canClaim() {
    let showClamin = this.rawFormData['actions']['possibleOfClaim'] ?? false;
    return showClamin;
  }

  /**
   * すべて実行完了
   */
  get isExecComplete(): boolean {
    let type = this.rawFormData['taskType'];
    if (type != 'SCAN') {
      return this.rawFormData['actions']['isPermission'] ?? false;
    }
    const { totalAssets, numberOfUnscanned } = this.workflowexecData;

    if (totalAssets === 0) {
      return false;
    }

    if (this.allMustScan == true) {
      //全資産をスキャン
      return numberOfUnscanned === 0;
    } else {
      //非全資産をスキャン
      let isWFStatus = undefined;
      if (this.isRentalWorkflow) {
        // Rental类型WF必须得扫描过其中一个资产才可以承认
        isWFStatus = totalAssets > numberOfUnscanned;
      } else {
        isWFStatus = totalAssets >= numberOfUnscanned;
      }
      return isWFStatus;
    }
  }

  /**
   * サブプロセスありの場合、資産スーテプ更新するか
   */
  get needUpdateAssetWFStatus(): boolean {
    let type = this.rawFormData['taskType'];
    if (type != 'SCAN' || this.isNosubprocess) {
      return false;
    }
    const { totalAssets, numberOfUnscanned } = this.workflowexecData;

    if (totalAssets === 0) {
      return false;
    }

    if (this.allMustScan == true && this.isCountingType) {
      //全資産をスキャン
      const isWFStatus = numberOfUnscanned === 0;
      return isWFStatus;
    } else {
      //非全資産をスキャン
      const isWFStatus = totalAssets >= numberOfUnscanned;
      return isWFStatus;
    }
  }

  /**
   * アイテム選択を切り替え
   * @param id 資産ID
   */
  @Throttle()
  async toggleSelect(id: number, isSubFromSubform: boolean = false) {
    if (this.widgetList.subform && !isSubFromSubform) {
      return;
    }

    if (this.widgetList.subform && isSubFromSubform) {
      this.itemsList.forEach((value) => {
        if (value.id == id) {
          this.checkAssetAuthority(value, true);
        }
      });
      return;
    }

    let clickable =
      this.pageAction.isActionEnable(WfPageWidgetService.APPROVAL_ACTION_ASSETS_DETAIL) ||
      this.rawFormData['taskType'] == 'SCAN';
    if (clickable) {
      this.itemsList.forEach((value) => {
        if (value.id == id) {
          this.checkAssetAuthority(value);
        }
      });
    }

    if (this.isReleaseMode) {
      this.itemsList.forEach((item) => {
        if (item.id === id) {
          item.isSelect = !item.isSelect;
        }
      });
    }
  }

  //判断権限
  async checkAssetAuthority(value: any, isSubFormType: boolean = false) {
    let isNewW3 = this.pageAction.workFlowStatus == WfConstService.APPROVAL_ASSET_STATUS_SCAN_CONFIRM;
    var assetTypeID =
      isNewW3 || isSubFormType
        ? String(this.rawFormData['productAssetTypeId'])
        : String(this.wfModel.assetTypeIdWithFirstWf);
    const result1 = await this.assetService.getAssetAuthority(value.rawAssetList['assetId'], assetTypeID);
    if (result1.count === 1) {
      this.toAssetDetail(value.rawAssetList, assetTypeID);
    } else {
      alert('資産が削除された又は権限が変更された可能性があるので、処理できません。');
    }
  }

  /**
   * 資産詳細画面に遷移する
   * @param {type}
   */
  toAssetDetail(asset, assetTypeId) {
    let navigationExtras: NavigationExtras = {
      queryParams: {
        assetText: asset.assetText,
        assetId: asset.assetId,
        assetTypeId: assetTypeId,
        fromScanPage: 'wf',
        backUrl: '/workflow/approval/w1-asset-list/list',
      },
    };
    this.navController.navigateForward('/asset/detail', navigationExtras);
  }

  /**
   * すべての選択を解除
   */
  releaseSelectAll() {
    this.itemsList.forEach((item) => (item.isSelect = false));
  }

  /**
   * 実行解除処理
   */
  doRelease() {
    // 選択済みのアイテムを差し戻し（フラグを設定）
    this.itemsList.forEach((item) => {
      if (item.isSelect) {
        // item.isSendBack = true;
        item.status = '-';
      }
    });

    // 選択不可
    this.isReleaseMode = false;
    this.releaseModeSubject.next(this.isReleaseMode);

    // すべての実行が解除されたらスキャンに戻る
    if (!this.hasExec) {
      this.backToView();
    }
  }

  /**
   * 実行解除モードにする
   */
  releaseMode() {
    this.isReleaseMode = true;
    this.releaseModeSubject.next(this.isReleaseMode);
  }

  /**
   * 実行解除モードを解除
   */
  cancelReleaseMode() {
    this.isReleaseMode = false;
    this.releaseModeSubject.next(this.isReleaseMode);
  }

  /**
   * ビューに戻る
   */
  backToView() {
    this.navController.navigateBack('/workflow/approval/w1-asset-list/view');
  }

  /**
   * スキャン画面に遷移する
   */
  @Throttle(1500)
  async toScanPage(isFromLocationSettingPage: boolean = false, scanType: string = '') {
    this.isFromLocationSettingPage = isFromLocationSettingPage;
    this.scanType = scanType;

    if (this.isNosubprocess) {
      this.toNoSubprocessScan();
    } else {
      this.toStanderScan();
    }
  }

  /**
   * subform专用扫描
   * @param {type} なし
   */
  toStanderScan() {
    this.scanService.assetWorkflowScanBacode(this.scanData).then(async (data: ScanDataInterface) => {
      console.log('💡======== toStanderScan scanback', data);
      this.workflowexecData.totalSavedAssetAmountCount = _.toNumber(data['totalSavedAssetAmountCount']);
      if (data.type == '5') {
        this.navController.navigateForward('/location-select', {
          queryParams: {
            param: data,
          },
        });
        return;
      }
      await this.statisticsScan(data.assetList, false, data.type);
    });
  }

  /**
   * 正常扫描
   * @param {type} なし
   */
  toNoSubprocessScan() {
    this.scanService.noSubprocessWorkflowScanBacode(this.scanData).then(async (data: ScanDataInterface) => {
      console.log('💡======== toNoSubprocessScan scanback', data);
      this.workflowexecData.totalSavedAssetAmountCount = _.toNumber(data['totalSavedAssetAmountCount']);
      await this.statisticsScan(data.assetList, true, data.type);
      if (data.type === '5') {
        this.navController.navigateForward('/location-select', {
          queryParams: {
            param: data,
            scanType: data.scanType,
          },
        });
        return;
      }
      await this.assetListPagePaginationTarget();
    });
  }

  // 个体情况下，当扫描的资产不在当前页时，不知道，这个资产是本次扫描已完成全部数量，还是
  async statisticsScan(dataList: ScanAssetDataInterface[], isSubprocess: boolean, scanType: string | number) {
    if (_.isEmpty(dataList)) {
      return;
    }
    // TODO 当扫描时候未处理资产要加少一个
    dataList.forEach((assetData) => {
      // 判断扫描的资产是否在当前页中
      const isContainsAssetData = !_.isEmpty(
        this.assetList.find((item) => _.toString(item.assetId) === _.toString(assetData.assetId)),
      );
      const hosenAsset: ChosenAsseDataInterface = {
        updatedNum: assetData.savedAssetAmount,
        isCurrentPageTheAsset: isContainsAssetData,
      };
      const oldChosenAsset = this.chosenAssetMap.get(_.toString(assetData.assetId));
      // 在当前页面中用户中用户非第一次操作过这条资产
      if (!_.isEmpty(oldChosenAsset)) {
        // 必须得保证资产的初期回数是第一次刚进入当前页面显示的数字为准，如果当前资产不在当前页中要以此一次扫描的数量为准
        hosenAsset.initScanCount = oldChosenAsset.initScanCount;
      } else {
        if (this.backupEnterScannedCount[assetData.barcodeObservation + assetData.location] !== undefined) {
          hosenAsset.initScanCount = this.backupEnterScannedCount[assetData.barcodeObservation + assetData.location];
        } else {
          hosenAsset.initScanCount = assetData.savedAssetAmountBeforeScan;
        }
      }
      this.chosenAssetMap.set(_.toString(assetData.assetId), hosenAsset);
    });
    this.setHasBeenChangedAsset(this.chosenAssetMap);
    console.log('💡====start==== statisticsScan ==>');
    console.log('💡 ==> assetDataList ', dataList);
    console.log('💡 ==> isSubprocess ', isSubprocess);
    console.log('💡 ==> chosenAssetMap ', this.chosenAssetMap);
    console.log('💡 ==> scanType ', scanType);
    console.log('💡=====end=== statisticsScan ==>');
    if (_.toString(scanType) === '5') {
      return;
    }
    if (isSubprocess) {
      this.processScanResult(dataList);
    } else {
      this.processNoSubprocessScanResult(dataList);
    }
    this.nextPageNum = 1;
    this.doRefresh(undefined);
  }

  /**
   * スキャン画面用のData
   */
  get scanData() {
    const isNewW3 = this.pageAction.assetCardUIType == WfPageWidgetService.APPROVAL_ASSETS_TYPE_NEW_W3;
    let data = {
      progressDefinitionId: this.wfModel['processDefinitionId'],
      assetListId: this.rawFormData['assetListId'].toString(),
      locationInfo: this.locationInfo,
      processInstanceId: this.processInstanceId,
      taskId: this.taskID,
      isMultiScan: false,
      // 'テナント区分("true"：レンタル会社、 false：レンタル以外の会社)'
      showTrim: this.isRentalWorkflow,
      // AIOCR用
      jobType: 'approvalWF',
      isFromLocationSettingPage: this.isFromLocationSettingPage,
      scanType: this.scanType,
      isCountingType: this.isCountingType,
      totalSavedAssetAmountCount: isNewW3
        ? 0
        : this.workflowexecData.scannedAssetCount,
      isNewW3: isNewW3,
    };
    console.log('💡++++++++++start+++++++scanData+++++++++++++++');
    console.log('scan data ==> ', data);
    console.log('💡==========end======scanData================');
    return data;
  }

  /**
   * barcodeリストを読み込む
   * @param {type} なし
   */
  get barcodeList() {
    var res = [];
    this.rawFormData['assetListDatas'].forEach((value) => {
      if (value['scanTarget']) {
        res.push(value['barcode'].toString());
      }

      if (this.nonSubProcess) {
        res.push(value['barcode'].toString());
      }
    });

    return res;
  }

  /**
   * get form data
   *  @param doubleRequest -> 短时间内重复发送相同请求时候，
   *                         ionic默认不发送第二个相同的请求，
   *                         要加一个不同的请求参数让第二次请求正常发送出去
   *  注释：要注意的是这个参数后台api并没有被定义
   */
  async loadData(processInstanceId: string, taskId: string, doubleRequest: boolean = false) {
    const allApi = [];
    const assetItemResultApi = this.httpAssetService.httpGetItemByAssetType(
      String(this.wfModel.assetTypeIdWithFirstWf),
    );
    allApi.push(assetItemResultApi);
    const assetColumnsApi = this.httpGetWorkflowListService.httpGetWorkflowAssetColumn(
      this.wfModel.workflowId,
      this.wfModel.processDefinitionId,
    );
    allApi.push(assetColumnsApi);
    const resultApi = this.WorkflowGetApplicationWorkflowListService.getWorkflowTaskForm(
      processInstanceId,
      taskId,
      '1',
      doubleRequest,
    );
    allApi.push(resultApi);
    const isSubformId = this.rawFormData.subformId && this.rawFormData.subformId != '';
    if (isSubformId) {
      const subforminfoApi = this.WorkflowGetApplicationWorkflowListService.getSubformLayoutInfo(
        this.rawFormData.subformId,
        this.rawFormData.subformVersion,
      );
      allApi.push(subforminfoApi);
    } else {
      allApi.push(undefined);
    }
    const isAssetCardUIType = this.pageAction.assetCardUIType == WfPageWidgetService.APPROVAL_ASSETS_TYPE_DEFAULT;
    if (isAssetCardUIType) {
      const showAssetLocationApi = this.workflowActionsService.showTenantAssetLocation();
      allApi.push(showAssetLocationApi);
    } else {
      allApi.push(undefined);
    }
    // 多请求线程同步解决方案, 强化请求速度
    const resultAll = await Promise.all(allApi);

    const assetItemResult = resultAll[0];
    const assetColumns = resultAll[1];
    const result = resultAll[2];
    const subforminfo = isSubformId ? resultAll[3] : null;
    const showAssetLocation = isAssetCardUIType ?? resultAll[4];
    this.dataNumAndNextPage.taskId = _.toNumber(taskId);
    this.assetColumns = assetColumns;
    this.showAssetLocation = showAssetLocation;
    this.assetTypeItemMap = assetItemResult.assetItemList.reduce((map, obj) => {
      map[obj.itemId] = obj;
      // 資産名の表示名を取得
      if (obj.itemName === 'assetName') {
        this.assetNameDisplayName = obj.itemDisplayName;
      }
      // 識別コードの表示名を取得
      if (obj.itemName === 'identityCode') {
        this.identityCodeDisplayName = obj.itemDisplayName;
      }
      return map;
    }, {});

    // get subform info if it has subform
    if (isSubformId) {
      this.subformTitle = _.isEmpty(subforminfo?.subform?.subformName)
        ? 'フォーム入力'
        : subforminfo?.subform?.subformName;
    } else {
      this.subformTitle = 'フォーム入力';
    }

    this.isCountingType = result['resultData']['updateAmountShow'] ?? WfPageWidgetService.WF_DEFAULT_AMOUNT_TYPE;
    this.rawFormData = result['resultData'];
    this.hasScanTask = this.rawFormData.hasScanTask ?? true;

    let actions = result['resultData']['actions'];
    this.isPermissionWF = true && (actions['isPermission'] ?? false);
    if (result['resultData']['taskType'] == 'SCAN') {
      this.showUnclaimButton = actions['isPermission'] ?? false;
    }

    if (actions['rejectionStatus'] === '1') {
      this.showRejectButton = true && (actions['isPermission'] ?? false);
    }

    if (actions['sendBackStatus'] === '1' || actions['sendBackStatus'] === '2') {
      this.showSendBackButton = true && (actions['isPermission'] ?? false);
    }

    if (actions['sendBackDestination'] != null) {
      this.backDestination = actions['sendBackDestination'];
    }

    this.isRentalWorkflow = result['resultData']['isRentalWorkflow'];
    this.isPermission = actions['isPermission'] ?? false;
    // 主体资产列表数据处理方法
    await this.assetListPagePaginationTarget();
  }

  /**
   * MとPの並び順調整
   */
  sortAssetListData(assetListData) {
    // Mの部分を取得
    const mainAssetList = assetListData.filter((item) => item.mpFlag == 'M');
    // Pの部分を取得
    const partAssetList = assetListData.filter((item) => item.mpFlag == 'P');
    // Nの部分を取得
    const nGroupAssetList = assetListData.filter((item) => item.mpFlag == 'N');
    // N,M,Pではない部分
    const noneGroupAssetList = assetListData.filter(
      (item) => item.mpFlag != 'N' && item.mpFlag != 'M' && item.mpFlag != 'P',
    );
    return nGroupAssetList.concat(noneGroupAssetList).concat(mainAssetList).concat(partAssetList);
  }

  /**
   * subform 专用
   * @param result
   */
  processScanResult(assetList: ScanAssetDataInterface[]) {
    // this.items.forEach(value => {
    //   const asset = assetList.find(item => item.assetId === value['assetId'])
    //   // 如果没有就证明扫描的资产不包含在当前页面
    //   if (_.isEmpty(asset)) {
    //     return
    //   }
    //   if (asset.isCompleted) {
    //     value.isSelect = true;
    //   }
    //   if (!this.scanAssetTaskIdList.includes(asset['barcodeObservation'])) {
    //     this.scanAssetTaskIdList.push(asset['barcodeObservation']);
    //   }
    //   value.assetScanedCount = asset.savedAssetAmount
    // });

    assetList.forEach((element) => {
      // 保证存进scanAssetTaskIdList里的barcode是唯一的
      if (!this.scanAssetTaskIdList.includes(element.barcodeObservation)) {
        this.scanAssetTaskIdList.push(element.barcodeObservation);
      }
      const asset = this.itemsList.find((item) => item['assetId'] === element['assetId']);
      if (asset) {
        if (element['isCompleted'] && element['isCompleted'] == true) {
          asset['scanState'] = '1';
        } else {
          asset['scanState'] = '0';
        }
      }
    });

    console.log('💡++++++++++start+++++++processScanResult+++++++++++++++');
    console.log('scan data 已扫描的barcode ==> ', this.scanAssetTaskIdList);
    console.log('💡==========end======processScanResult================');
  }

  /**
   * サブプロセスなしのスキャンの結果
   * @param {type} result
   */
  processNoSubprocessScanResult(assetList: ScanAssetDataInterface[]) {
    console.log('💡++++++++++++processNoSubprocessScanResult+++++++++++++++');
    let isNewW3 = this.pageAction.workFlowStatus == WfConstService.APPROVAL_ASSET_STATUS_SCAN_CONFIRM;
    this.itemsList.forEach(async (value) => {
      if (isNewW3 && this.pageAction.isAssetFinished(value.id.toString())) {
        return;
      }
      const asset = assetList.find((item) => item.assetId === value['assetId']);
      // 如果没有就证明扫描的资产不包含在当前页面
      if (_.isEmpty(asset)) {
        return;
      }

      if (!this.scanAssetTaskIdList.includes(asset['barcodeObservation'])) {
        this.scanAssetTaskIdList.push(asset['barcodeObservation']);
      }

      value.assetScanedCount = asset.savedAssetAmount;

      value.isSelect = true;

      if (isNewW3) {
        value.status = '完了';
        value['subformDisabled'] = false;
      } else if (asset.isCompleted) {
        value.step = 'スキャン済';
        value.status = '完了';
        value['scanState'] = '1';
      } else if (asset.assetAmount < asset.savedAssetAmount) {
        value.step = 'スキャン中';
        value['scanState'] = '0';
      } else {
        value.step = 'スキャン中';
        value['scanState'] = '0';
      }

      if (!this.allMustScan && !this.isRentalWorkflow) {
        if (value.assetScanedCount > 0) {
          value.status = '完了';
          value['scanState'] = '1';
        } else {
          value['scanState'] = '0';
        }
      }
    });
  }

  /**
   * スキャンした資産を更新する
   * @param {type} なし
   */
  @Throttle()
  async updateScanAssetStatus() {
    let assetDataList = this.scanResult['assetDataList'];
    await this.http.addLoadingQueue();

    for (const value of assetDataList) {
      if (value.scanTimes > 0) {
        if (value['processState'] === 'ACTIVE') {
          const param = this.updateScanedAssetsPostData(value);
          await this.apiMobileTransit(param);
        } else if (value['processState'] === 'NOT_ACTIVE') {
          const param = this.generateCommitScanAssetsData(value);
          await this.apiStartScan(param);
        }
      }
    }

    this.http.removeLoadingQueue();

    this.updateAssetAction(this.processInstanceId, this.taskID);

    this.back();
  }

  /**
   * 提出用なスキャンした資産データを組み合わせ
   * @param {type} data: any
   */
  generateCommitScanAssetsData(data: any): FormData {
    const formData = new FormData();
    formData.append('processDefinitionId', this.rawFormData['thirdWfProcessDefinitionId']);
    formData.append('barCode', data['barcode']);
    formData.append('assetListId', data['assetListId']);
    const assetText = JSON.parse(data['assetText']);
    formData.append('location', assetText['location']);

    return formData;
  }

  /**
   * スキャン開始サーバに提出
   * @param {type} data: any
   */
  async apiStartScan(data: any) {
    const result = await this.WorkflowGetApplicationWorkflowListService.StartScanWorkFlow(data);
  }

  /**
   * サーバにスキャンした資産状態を更新する
   * @param {type} data: any
   */
  async apiMobileTransit(data: any) {
    console.log('💡111🚀-------->', data);
    const result = await this.WorkflowGetApplicationWorkflowListService.approvalMobileWorkFlow(data);
  }

  // thirdWfProcessDefinitionIdを読み込む
  get thirdWfProcessDefinitionId() {
    return this.rawFormData['thirdWfProcessDefinitionId'];
  }

  // processInstanceIdを読み込む
  get processInstanceId() {
    return this.wfModel.processInstanceId;
  }

  // taskIDを読み込む
  get taskID() {
    return this.wfModel.id;
  }

  // assetListIdを読み込む
  get assetListId() {
    return this.rawFormData['assetListId'] ?? 0;
  }

  // nonSubProcessを読み込む
  get nonSubProcess() {
    return this.rawFormData['nonSubProcess'] ?? false;
  }

  /**
   * サーバ用のスキャンした資産データを更新する
   * @param {type} scanedData: any
   */
  updateScanedAssetsPostData(scanedData: any) {
    const formData = new FormData();
    formData.append('assetListId', this.assetListId);
    formData.append('processDefinitionId', this.thirdWfProcessDefinitionId);
    formData.append('barCode', scanedData['barcode']);
    formData.append('variables', JSON.stringify(this.formPostVariables));
    const assetText = JSON.parse(scanedData['assetText']);
    formData.append('location', assetText['location']);
    console.log('💡======start==updateScanedAssetsPostData====サーバ用======');
    formData.forEach((value, key) => {
      console.log('💡formData =>>> ', key + ': ' + value);
    });
    console.log('💡======end==updateScanedAssetsPostData=====サーバ用=====');
    return formData;
  }

  /**
   * 承認提出用なデータを組み合わせ
   * @param {type} isAccept: boolean 承認か
   */
  generateComfirmData(isAccept: boolean) {
    let mode = isAccept ? '0' : '1';
    var formData = new FormData();

    formData.append('variables', JSON.stringify(this.formPostVariables));
    formData.append('mode', mode);
    formData.append('processInstanceId', this.processInstanceId);
    formData.append('taskId', this.taskID);
    console.log('💡======start==generateComfirmData======承認提出====');
    formData.forEach((value, key) => {
      console.log('💡formData =>>> ', key + ': ' + value);
    });
    console.log('💡======end==generateComfirmData======承認提出====');
    return formData;
  }

  // 提出用なサブプロセスなしデータを組み合わせ
  get generateNoSubprocessComfirmData() {
    var formData = new FormData();
    formData.append('assetListId', this.assetListId);
    console.log('💡======start==generateNoSubprocessComfirmData=====提出用なサブプロセスなしデータを組み合=====');
    formData.forEach((value, key) => {
      console.log('💡formData =>>> ', key + ': ' + value);
    });
    console.log('💡======end==generateNoSubprocessComfirmData=====提出用なサブプロセスなしデータを組み合=====');
    return formData;
  }

  // 提出用な差し戻しデータを組み合わせ
  get generateSendBackData() {
    var formData = new FormData();

    formData.append('taskId', this.taskID);
    formData.append('sendBackDestination', this.rawFormData['actions']['sendBackDestination'] ?? '');
    formData.append('variables', JSON.stringify(this.formPostVariables));
    console.log('💡======start==generateSendBackData=====提出用な差し戻=====');
    formData.forEach((value, key) => {
      console.log('💡formData =>>> ', key + ': ' + value);
    });
    console.log('💡======end==generateSendBackData=====提出用な差し戻=====');
    return formData;
  }

  // フッター表示されるかを読み込む
  get isShowFooter() {
    const isScanType = this.rawFormData['taskType'] == 'SCAN';
    return (
      isScanType &&
      (this.okStatus ||
        this.claimStatus ||
        this.confrimStatus ||
        this.showRejectButton ||
        this.showSendBackButton ||
        this.showUnclaimButton)
    );
  }

  // OKボタン状態読み込む
  get okStatus() {
    return this.needUpdateAssetWFStatus && !this.isNosubprocess;
  }

  // 受取ボタン状態読み込む
  get claimStatus() {
    return this.canClaim;
  }

  // 承認ボタン状態読み込む
  get confrimStatus() {
    if (this.claimStatus) {
      return false;
    }
    let isNewW3 = this.pageAction.workFlowStatus == WfConstService.APPROVAL_ASSET_STATUS_SCAN_CONFIRM;
    if (isNewW3) {
      if (this.widgetList.subform) {
        return false;
      }
      return this.scanAssetTaskIdList.length > 0;
    }
    return (!this.needUpdateAssetWFStatus || this.isNosubprocess) && this.isExecComplete && this.isPermission;
  }

  /**
   * 戻る
   */
  async back() {
    this.navController.navigateBack('/workflow/approval/w1-asset-list/view', {
      queryParams: {},
    });
  }

  approvalBtnDisabled(that, result) {
    that.isDisabled = result;
  }

  /**
   * @description: sendBack
   * @param {type}
   * @return {type}
   */
  @Throttle(1500)
  async sendBack() {
    try {
      await this.http.addLoadingQueue();
      let isCheck: boolean = await this.workflowActionsService.checkAndGetData(this.wfDict, false);
      if (isCheck == false) {
        return;
      }
    } finally {
      await this.http.removeLoadingQueue();
    }
    let that = this;
    this.workflowActionsService.sendBackFromApproval({
      assetDict: this.wfDict,
      comments: this.comments,
      sendBackDestination: this.backDestination,
      taskId: this.taskID,
      progress: 'not-yet',
      perviewComponent: null,
      workflowScript: this.workflowScript,
      stepName: this.stepName,
      callBack: this.approvalBtnDisabled,
      that: that,
      isFromAssetListPage: true,
      buttonName: null,
    });
  }

  /**
   * @description: 普通
   * @param {type}
   * @return {type}
   */
  @Throttle(1500)
  async rejection() {
    try {
      await this.http.addLoadingQueue();
      let isCheck: boolean = await this.workflowActionsService.checkAndGetData(this.wfDict, false);
      if (isCheck == false) {
        return;
      }
    } finally {
      await this.http.removeLoadingQueue();
    }
    let that = this;
    this.workflowActionsService.comfrimWithAssets({
      assetDict: this.wfDict,
      comments: this.comments,
      processInstanceId: this.processInstanceId,
      assetList: this.confirmData.assetList,
      assetListDatas: this.confirmData.assetListDatas,
      assetListTitle: this.confirmData.assetListTitle,
      assetTypeIdWithFirstWf: this.wfModel.assetTypeIdWithFirstWf,
      taskId: this.taskID,
      progress: 'not-yet',
      perviewComponent: null,
      workflowScript: this.workflowScript,
      stepName: this.stepName,
      callBack: this.approvalBtnDisabled,
      that: that,
      isFromAssetListPage: true,
      isApproval: false,
      backPage: null,
      buttonName: null,
    });
  }

  /**
   * @description: 普通
   * @param {type}
   * @return {type}
   */
  @Throttle(1500)
  async approval() {
    if (!this.checkDynamicTaskCanSubmit()) {
      const alert = await this.alertController.create({
        message: '各ステップの承認者を選択してください。',
        buttons: [
          {
            text: 'はい',
            role: 'cancel',
            cssClass: 'font-weight-bold',
            handler: async () => {},
          },
        ],
      });
      await alert.present();
      return;
    } else {
      if (!_.isEmpty(this.dynamicTaskInfo) && !_.isEmpty(this.dynamicTantousha)) {
        for (let i = 0; i < this.dynamicTaskInfo.length; i++) {
          const element = this.dynamicTaskInfo[i];
          if (element['assignDynamicType'] === 'group') {
            element['authorizerId'] = this.dynamicTantousha[i]['roleId'];
            element['authorizerName'] = this.dynamicTantousha[i]['roleName'];
            console.log('💡approval 新增Group', element);
          } else {
            element['authorizerId'] = this.dynamicTantousha[i]['userId'];
            element['authorizerName'] =
              this.dynamicTantousha[i]['lastName'] + ' ' + this.dynamicTantousha[i]['firstName'];
            console.log('💡approval 新增User', element);
          }
        }
      }
    }

    let isNewW3ScanComfirm = this.pageAction.workFlowStatus == WfConstService.APPROVAL_ASSET_STATUS_SCAN_CONFIRM;

    try {
      await this.http.addLoadingQueue();
      let isCheck: boolean = await this.workflowActionsService.checkAndGetData(this.wfDict, false);
      if (isCheck == false) {
        return;
      }
    } finally {
      await this.http.removeLoadingQueue();
    }
    let that = this;
    if (this.isNosubprocess && this.rawFormData['taskType'] == 'SCAN') {
      const isExecutionMarker = await this.workflowActionsService.comfrimWithScanCompleteNoSubprocessWithAllMust({
        assetDict: this.wfDict,
        comments: this.comments,
        processInstanceId: this.processInstanceId,
        taskId: this.taskID,
        progress: 'done',
        isApproval: true,
        backPage: this.workflowActionsService.approvalURL,
        assignDynamicData: this.dynamicTaskInfo,
        perviewComponent: null,
        workflowScript: this.workflowScript,
        confirmData: this.confirmData,
        stepName: this.stepName,
        isFromAssetListPage: false,
        workflowexecData: this.workflowexecData,
        isAllMustScan: this.allMustScan,
        buttonName: null,
      });
      this.approvalBtnDisabled(this, isExecutionMarker);
    } else if (isNewW3ScanComfirm) {
      let taskIdList = this.pageAction.scanConfrimData(this.scanAssetTaskIdList).taskIdList;
      await this.workflowActionsService.comfrimWithScanNewW3(
        this.wfDict,
        this.comments,
        this.processInstanceId,
        taskIdList,
        this.rawFormData['assetListTitle'] ?? '',
        null,
        async (value) => {
          let result = this.pageAction.scanFinishedProcessed(value['subProcessProductAssetList']);
          this.scanAssetTaskIdList = [];
          if (result.allAssetFinished) {
            // 承認一覧画面に遷移する
            this.pageAction.backToConfrimList();
          } else {
            this.refreshAfterNewW3ScanFinished(result.newTaskId);
          }
        },
      );
    } else {
      this.workflowActionsService.comfrimWithScanComplete({
        isAssetListEditable: false,
        isCountingType: this.isCountingType,
        assetDict: this.wfDict,
        comments: this.comments,
        processInstanceId: this.processInstanceId,
        assetList: this.confirmData.assetList,
        assetListDatas: this.confirmData.assetListDatas,
        assetListTitle: this.confirmData.assetListTitle,
        assetTypeIdWithFirstWf: this.wfModel.assetTypeIdWithFirstWf,
        taskId: this.taskID,
        progress: 'done',
        isApproval: true,
        backPage: this.workflowActionsService.approvalURL,
        assignDynamicData: this.dynamicTaskInfo,
        perviewComponent: null,
        workflowScript: this.workflowScript,
        confirmData: this.confirmData,
        stepName: this.stepName,
        callBack: this.approvalBtnDisabled,
        that: that,
        isFromAssetListPage: true,
        buttonName: null,
      });
    }
  }

  // 資産IDリストを取得
  get assetIds() {
    var assetIdList = [];
    this.assetList.forEach((asset) => {
      assetIdList.push(asset.assetId);
    });
    return assetIdList;
  }

  // 承認時に利用する資産リストデータ
  get assetListInfo() {
    let assetListId = this.rawFormData['assetListId'].toString();
    return {
      assetTypeId: this.assetTypeId,
      assetIds: this.assetIds,
      assetListId: assetListId,
    };
  }

  /**
   * unclaim
   * @param {type} argName I am argument argName.
   */
  @Throttle(1500)
  async unclaim() {
    let that = this;
    this.workflowActionsService.unclaimFormApproval(
      this.wfDict,
      this.comments,
      this.processInstanceId,
      this.taskID,
      'not-yet',
      this.approvalBtnDisabled,
      that,
    );
  }

  /**
   * @description:claim
   * @param {type}
   * @return {type}
   */
  @Throttle(1500)
  async claim() {
    const assetTypeList: AssetTypeItem[] = [];
    let itemDataDictCopy = JSON.parse(JSON.stringify(this.wfDict));
    for (const sectionName in itemDataDictCopy) {
      assetTypeList.push(...this.wfDict[sectionName]);
    }
    for (const assetType of assetTypeList) {
      //非表示になっているプライベートセクション内の項目
      var isView = true;
      let optionObj = JSON.parse(assetType.option);
      let sectionPrivateGroups = optionObj.sectionPrivateGroups;
      if (sectionPrivateGroups) {
        isView = await this.loginService.visiblePermissionsCheck(sectionPrivateGroups);
      }
      if (!isView) {
        //没有权限预览
        continue;
      }
      let optionObject = assetType['optionObject'];
      if (_.isEmpty(optionObject)) {
        optionObject = optionObj;
      }
      // true 不可以编辑（只读），false可以编辑
      const isReadonly = _.isEmpty(_.toString(optionObject['readonly']))
        ? false
        : _.toString(optionObject['readonly']) === '1'
          ? true
          : false;
      if (isReadonly) {
        // 对于通过 customizeLogic setValue 进来的 通货、数字不可编辑内容
        // 需要判断是否不符合规则，如果不符合的情况则弹出 error 信息并拦截下一步操作
        // 数字或通货类型输入位数检查
        if (!validateNumericAndCurrencyField(assetType)) {
          return false;
        }
        continue;
      }
      // 必須チェック
      if (assetType.inputFlg === '1') {
        if (
          assetType.defaultData == '' ||
          assetType.defaultData == undefined ||
          assetType.defaultData == null ||
          Object.keys(assetType.defaultData).length === 0 ||
          String(assetType.defaultData).trim() === '' ||
          assetType.defaultData == '[]'
        ) {
          alert(assetType.itemName + 'を設定してください。');
          return;
        }
      }

      if (assetType.inputFlg === '1' && assetType.itemType === 'checkbox') {
        if (assetType.defaultData) {
          if (typeof assetType.defaultData === 'string') {
            if (assetType.defaultData !== '1') {
              let cbName = assetType.itemName ?? assetType.itemDisplayName;
              alert(cbName + 'を設定してください。');
              return;
            }
          }
        }
      }

      if (assetType.isShowMessageTS) {
        alert('入力エラーがあるのでご確認ください。');
        return;
      }
      if (assetType.defaultData) {
        // 桁数チェック
        if (assetType.optionObject.maxlength && typeof assetType.defaultData === 'string') {
          if (assetType.defaultData.length > assetType.optionObject.maxlength) {
            alert(`${assetType.itemName}を${assetType.optionObject.maxlength}文字以内に入力してください。`);
            return;
          }
        }

        // 数字或通货类型输入位数检查
        if (!validateNumericAndCurrencyField(assetType)) {
          return false;
        }

        // メールアドレスの有効性チェック
        if (assetType.itemType === 'email') {
          const reg = /^(\w+|[-+.])*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
          if (!reg.test(assetType.defaultData)) {
            alert(`${assetType.itemName}のフォーマットが間違っています。`);
            return;
          }
        }
      }
    }

    this.workflowActionsService.claim(
      this.wfDict,
      this.comments,
      this.processInstanceId,
      this.taskID,
      async (result) => {
        this.updateDataAfterClaim(result);
      },
    );
  }

  /**
   * 受取後データ更新
   * @param {type} result
   */
  async updateDataAfterClaim(result) {
    let processInstanceId = result['processInstanceId'];
    let taskID = result['currentTaskId'];
    this.wfModel.id = taskID;
    this.wfModel.processInstanceId = processInstanceId;
    await this.loadData(processInstanceId, taskID, true);
    await this.updateAssetAction(processInstanceId, taskID);
    await this.http.removeLoadingQueue();
  }

  // new w3 refresh
  async refreshAfterNewW3ScanFinished(taskID) {
    await this.http.addLoadingQueue();
    this.wfModel.id = taskID;
    await this.loadData(this.processInstanceId, taskID, true);
    this.updateIDAction(this.processInstanceId, taskID);
    this.http.removeLoadingQueue();
  }

  /**
   * subfrom专用
   * @param item assetItem
   */
  @Throttle()
  async clickInputForm(item) {
    let navigationExtras: NavigationExtras = {
      queryParams: {
        subformId: this.rawFormData.subformId ?? '',
        subformVersion: this.rawFormData.subformVersion ?? '1',
        processInstanceId: this.processInstanceId,
        assetId: item.assetId,
        barcode: item.rawAssetList?.barcode,
        taskId: item.subFromBytaskId,
        readOnly: this.isPassedSubProcessWithSubForm || item.subFromBytaskId == null,
        title: this.subformTitle,
        confirmData: {
          assetDict: this.wfDict,
          comments: this.comments,
          taskIds: [item.subFromBytaskId],
          wfName: this.wfModel.workflowName ?? '',
          assetListInfo: this.assetListInfo,
        },
      },
    };

    this.navController.navigateForward('/workflow/approval/w1-asset-list/subform', navigationExtras);
  }

  get isPassedSubProcessWithSubForm() {
    return (
      this.pageAction.assetCardUIType == WfPageWidgetService.APPROVAL_ASSETS_TYPE_DEFAULT &&
      this.rawFormData.hasSubProcess &&
      this.rawFormData.hasPassedSubProcess
    );
  }

  // SubForm 当某一个资产进入到承诺页面后返回会调用下面方法
  newW3AfterConfrimStateReducer(item) {
    let value = item['result'];
    let scanedBarcode = item['finishedItemBarcode'];
    if (!value) {
      return;
    }
    let result = this.pageAction.scanFinishedProcessed(value['subProcessProductAssetList']);
    const index = this.scanAssetTaskIdList.indexOf(scanedBarcode);
    if (index > -1) {
      // this.items.forEach(item => {
      //   if (item.id === this.scanAssetTaskIdList[index]) {
      //     // SubForm扫描后资产会变成蓝色，当添加完form后会变回白色，依次重复，直到终了时候固定为蓝色
      //     item.status = '';
      //     item['subformDisabled'] = true;
      //   }
      // })
      this.scanAssetTaskIdList.splice(index, 1);
    }
    if (result.allAssetFinished) {
      // 承認一覧画面に遷移する
    } else {
      this.refreshAfterNewW3ScanFinished(result.newTaskId);
    }
  }

  /**
   * 资产数量--减
   * @param item any
   */
  minusClick(item: any) {
    if (this.isKeyboardVisible) {
      Keyboard.hide();
      return;
    }
    this.updateAssetScanStatus(item, MANUAL_TYPE_ENUM.SUBTRACTION);
  }

  /**
   * 资产数量--加
   * @param item any
   */
  plusClick(item: any) {
    if (this.isKeyboardVisible) {
      Keyboard.hide();
      return;
    }
    this.updateAssetScanStatus(item, MANUAL_TYPE_ENUM.ADDITION);
  }

  /**
   * 手动输入资产数量
   * @param item any
   * @param $event 取得输入值
   */
  async updateCount(item: any, $event) {
    await this.updateAssetScanStatus(item, MANUAL_TYPE_ENUM.INPUT, $event);
    $event.target.value = item['assetScanedCount'];
  }

  /**
   * 当手动输入时候获取点击输入框获取焦点
   * @param item any
   */
  updateCountFocus(item: any) {
    // 当用户输入错误数量时候，还原之前的数量准备
    item['oldAssetScanedCount'] = item['assetScanedCount'];
    this.lastInputElement = document.activeElement as HTMLInputElement;
  }

  /**
   * 手动调节数量核心方法
   * @param value assetItem
   * @param manualType MANUAL_TYPE_ENUM
   * @param event 如果是输入的则有值
   * @returns void
   */
  async updateAssetScanStatus(value, manualType: MANUAL_TYPE_ENUM, event: any = null) {
    if (!this.isNosubprocess) {
      return;
    }
    let assetListId = this.rawFormData['assetListId'].toString();
    const formData = new FormData();
    formData.append('processInstanceId', _.toString(this.processInstanceId));
    formData.append('taskId', _.toString(this.taskID));
    formData.append('assetListId', assetListId);
    formData.append('assetId', _.toString(value['assetId']));
    formData.append('operation', _.toString(manualType));
    value['isApprovalSys'] = true;
    const oldValue = _.cloneDeep(value);
    const dataNumAndNextPageClone = _.cloneDeep(this.dataNumAndNextPage);
    dataNumAndNextPageClone.skip = 0;
    await this.http.addLoadingQueue();
    try {
      switch (manualType) {
        case MANUAL_TYPE_ENUM.SUBTRACTION:
          await this.WorkflowGetApplicationWorkflowListService.amountAssignScan(formData);
          this.workflowActionsService.minusClick(value);
          // 只为取到条数
          break;
        case MANUAL_TYPE_ENUM.ADDITION:
          await this.WorkflowGetApplicationWorkflowListService.amountAssignScan(formData);
          this.workflowActionsService.plusClick(value);
          // 只为取到条数
          break;
        case MANUAL_TYPE_ENUM.INPUT:
          const isTrue = await this.workflowActionsService.updateCount(value, event, false, undefined, false);
          if (!isTrue) {
            if (_.toNumber(value['assetScanedCount']) === 0) {
              value['assetScanedCount'] = 0;
            } else {
              value['assetScanedCount'] = _.toNumber(oldValue['oldAssetScanedCount']);
            }
          }

          formData.append('amount', value['assetScanedCount']);
          await this.WorkflowGetApplicationWorkflowListService.amountAssignScan(formData);
          break;
      }
    } catch (error) {
      if (manualType === MANUAL_TYPE_ENUM.INPUT) {
        value['assetScanedCount'] = _.toNumber(oldValue['oldAssetScanedCount']);
      }
      return;
    } finally {
      await this.getAssetsByKeywordInWfAssetListForAssignScanDataFun(dataNumAndNextPageClone);
      this.http.removeLoadingQueue();
    }
    // 资产数量为"0"时候代表资产以从 已扫描资产（非零）转变为 未扫描资产 （零）
    // if (_.toString(value['assetScanedCount']) === "0") {
    //   this.chosenAssetMap.delete(_.toString(value['assetId']))
    // } else {
    // 匹配到原始数据
    // 判断扫描的资产是否在当前页中
    const isContainsAssetData = !_.isEmpty(
      this.assetList.find((assetitem) => _.toString(assetitem.assetId) === _.toString(value.assetId)),
    );
    const oldChosenAsset = this.chosenAssetMap.get(_.toString(value['assetId']));
    // 是否操作过这条资产数量的扫描（非手动增加数量，只是扫描增加）
    const hosenAsset: ChosenAsseDataInterface = {
      updatedNum: value['assetScanedCount'],
      isCurrentPageTheAsset: isContainsAssetData,
    };
    if (!_.isEmpty(oldChosenAsset)) {
      hosenAsset.initScanCount = oldChosenAsset.initScanCount;
    } else {
      hosenAsset.initScanCount = _.toNumber(oldValue['assetScanedCount']);
      if (manualType === MANUAL_TYPE_ENUM.INPUT) {
        hosenAsset.initScanCount = _.toNumber(oldValue['oldAssetScanedCount']);
      }
    }
    this.chosenAssetMap.set(_.toString(value.assetId), hosenAsset);
    // }
    this.setHasBeenChangedAsset(this.chosenAssetMap);
    console.log('💡======start==updateAssetScanStatus=====手动调节数量=====');
    console.log('💡', this.chosenAssetMap);
    console.log('💡======end==updateAssetScanStatus=====手动调节数量=====');
    value.isSelect = value.assetScanedCount > 0;

    if (value.assetTotalCount == value.assetScanedCount) {
      value.step = 'スキャン済';
      value.status = '完了';
      value['scanState'] = '1';
    } else {
      value.step = 'スキャン中';
      value.status = ' - ';
      value['scanState'] = '0';
    }

    if (this.isNosubprocess && !this.allMustScan && !this.isRentalWorkflow) {
      if (value.assetScanedCount > 0) {
        value.status = '完了';
        value['scanState'] = '1';
      } else {
        value['scanState'] = '0';
      }
    }

    if (
      (this.workflowSegmentKey === 'workflowScanned' && value.status === ' - ') ||
      (this.workflowSegmentKey === 'workflowUnscanned' && value.status === '完了')
    ) {
      let indexofRemovedData = this.itemsList.findIndex((item) => item['assetId'] == value['assetId']);
      let cachedPosition = this.cachedPositions[indexofRemovedData];
      if (cachedPosition) {
        this.more = [];
      }
      this.removeIndexItem(indexofRemovedData);
    } else {
      this.doAfterRequest(1);
      this.updateCachedPositions(this.lastItems, this.isSplit);
    }
  }

  // @Throttle() 无网络请求将此注解去掉
  clickMore($event: MouseEvent, idx: any, b: boolean) {
    $event.stopPropagation();
    this.onMoreClick(idx, b);
    return false;
  }

  // 判断是否符合提交条件
  // 返回true时满足条件可以提交
  private checkDynamicTaskCanSubmit() {
    if (this.dynamicTaskInfo && this.dynamicTaskInfo.length > 0) {
      if (this.dynamicTantousha && this.dynamicTantousha.length > 0) {
        return this.dynamicTaskInfo.length === this.dynamicTantousha.length;
      } else {
        return false;
      }
    } else {
      return true;
    }
  }

  async getTurl(filePath) {
    let resultData = await this.assetDetailService.getTurl(filePath);
    return resultData.data.getUrl;
  }

  async errorImgLoad(item) {
    item.thumbnail.turl = await this.getTurl(item.thumbnail['url']);
  }

  /**
   * 分页资产列表数据组装
   */
  async assetListPagePaginationTarget() {
    await this.http.addLoadingQueue();
    try {
      // 迎合api需要把正常页码减去1
      let nextPage = this.nextPageNum - 1;
      // skip（开始条数） 公式：nextPage x getAmountDataNumber
      // rows (从开始条数 skip 后需要返回多少条)
      let skip = nextPage * this.getAmountDataNumber;
      if (this.dataNumAndNextPage.skip == skip && skip != 0) {
        return;
      }
      this.dataNumAndNextPage.skip = skip;
      const { getAssetListData } = await this.getAssetsByKeywordInWfAssetListForAssignScanDataFun(
        this.dataNumAndNextPage,
      );
      // 总页数
      let totalPageNumber = 0;
      switch (this.dataNumAndNextPage.scanState) {
        case undefined:
          // 当选项处于 '全て' 时候 totalPageNumber 会有多少页数
          const totalAssets =
            this.dataNumAndNextPage.keyword == undefined || this.dataNumAndNextPage.keyword == ''
              ? this.workflowexecData.totalAssets
              : this.workflowexecData.allAssetCountInKeywordSearch;
          totalPageNumber = Math.ceil(totalAssets / this.getAmountDataNumber);
          break;
        case 0:
          // 当选项处于 '未スキャン' 时候 totalPageNumber 会有多少页
          totalPageNumber = Math.ceil(
            (this.dataNumAndNextPage.keyword == undefined || this.dataNumAndNextPage.keyword == ''
              ? this.workflowexecData.numberOfUnscanned
              : this.workflowexecData.noScanningAssetCountInKeywordSearch) / this.getAmountDataNumber,
          );
          break;
        case 1:
          // 当选项处于 'スキャン済' 时候 totalPageNumber 会有多少页数
          totalPageNumber = Math.ceil(
            (this.dataNumAndNextPage.keyword == undefined || this.dataNumAndNextPage.keyword == ''
              ? this.workflowexecData.totalAssets - this.workflowexecData.numberOfUnscanned
              : this.workflowexecData.allAssetCountInKeywordSearch -
                this.workflowexecData.noScanningAssetCountInKeywordSearch) / this.getAmountDataNumber,
          );
          break;
      }
      const wfAssetList = getAssetListData.assetListDatas ?? [];
      if (nextPage === 0) {
        // 初始状态资产
        this.assetList = wfAssetList;
      } else {
        // 下一页资产
        this.assetList.push(...wfAssetList);
      }

      // 当前页数
      const currentPageNumber = Math.ceil(this.assetList.length / this.getAmountDataNumber);
      // wfAssetList为最新请求回的当前页资产数据（非全部资产数据）

      // 是否还有下一页
      this.dataNumAndNextPage.moreThenLimit =
        wfAssetList.length !== 0 && wfAssetList.length % this.getAmountDataNumber == 0;

      // for循环主要是为了上拉加载下一页和下拉刷新，从api取到最新数据时候把页面上之前操作的资产数量更新回 新请求回的数据中
      // for (const [key, value] of this.chosenAssetMap) {
      //   for (let index = 0; index < this.assetList.length; index++) {
      //     const element = this.assetList[index];
      //     if (_.toString(element.assetId) === _.toString(key)) {
      //       element['savedAssetAmount'] = value.updatedNum;
      //       continue;
      //     }
      //   }
      // }
      // 积存逻辑数据整合用
      let assetList = await this.updateAssetList();
      this.doAfterRequestWithData(nextPage, assetList, true);
    } finally {
      this.http.removeLoadingQueue();
    }
  }

  /**
   * segment Change
   */
  async segmentChanged() {
    switch (this.workflowSegmentKey) {
      case 'workflowAll':
        this.dataNumAndNextPage.scanState = undefined;
        break;
      case 'workflowScanned':
        this.dataNumAndNextPage.scanState = 1;
        break;
      case 'workflowUnscanned':
        this.dataNumAndNextPage.scanState = 0;
        break;
    }
    this.clearData();
    // 检索词清空，因需求要求检索词在切换tab时保持代码注释
    // this.dataNumAndNextPage.keyword = undefined;
    // this.workflowInputKey = '';
    this.nextPageNum = 1;
    await this.assetListPagePaginationTarget();
  }

  /**
   * 検索する
   */
  @Throttle()
  async doSearch() {
    this.clearData();
    if (_.isEmpty(this.workflowInputKey)) {
      this.dataNumAndNextPage.keyword = undefined;
    } else {
      this.dataNumAndNextPage.keyword = this.workflowInputKey;
    }
    this.nextPageNum = 1;
    await this.assetListPagePaginationTarget();
  }

  /**
   * 入力した検索キーを削除
   */
  @Throttle()
  async clearInputKey() {
    this.clearData();
    this.workflowInputKey = '';
    this.dataNumAndNextPage.keyword = undefined;
    this.nextPageNum = 1;
    await this.assetListPagePaginationTarget();
  }

  /**
   * 上滑下一页
   * @param ev
   */
  async onClicklistLoadData(ev: any) {
    this.nextPageNum = this.nextPageNum + 1;
    await this.assetListPagePaginationTarget();
    ev.target.complete();
  }

  /**
   * 下拉刷新重置数据
   * @param event
   */
  async doRefresh(event: any) {
    if (event) {
      event.target.disabled = true;
    } else {
      this.content.scrollToTop(0);
    }
    this.clearData();
    this.nextPageNum = 1;
    await this.assetListPagePaginationTarget();
    if (event) {
      event.target.complete();
      setTimeout(() => {
        event.target.disabled = false;
      }, 2000);
    }
  }

  /**
   * @description: 一时保存
   * @param {type}
   * @return {type}
   */
  @Throttle()
  async saveTemporaryFromScanTask() {
    await this.workflowActionsService.saveTemporaryFromScanTask(
      this.isCountingType,
      this.wfDict,
      this.comments,
      _.toString(this.processInstanceId),
      this.assetListId,
      this.confirmData.taskId,
      'not-yet',
      this.workflowActionsService.approvalURL,
      this.dynamicTaskInfo,
      false,
      this.rawFormData['taskType'] === 'USER' || this.rawFormData['taskType'] === 'GROUP',
    );
  }

  async getAssetsByKeywordInWfAssetListForAssignScanDataFun(
    dataNumAndNextPage: WFDataNumAndNextPageType,
  ): Promise<{ getAssetListData: AssetsByKeywordInWfAssetListForAssignScanAPI }> {
    // 分页核心api请求
    const getAssetListData =
      await this.WorkflowGetApplicationWorkflowListService.getAssetsByKeywordInWfAssetListForAssignScan(
        dataNumAndNextPage,
      );

    setTimeout(() => {
      this.workflowexecData = {
        totalAssets: getAssetListData.allAssetCount,
        numberOfUnscanned: getAssetListData.noScanningAssetCount,
        allAssetCountInKeywordSearch: getAssetListData.allAssetCountInKeywordSearch,
        totalSavedAssetAmountCount: getAssetListData.totalSavedAssetAmountCount,
        noScanningAssetCountInKeywordSearch: getAssetListData.noScanningAssetCountInKeywordSearch,
        scannedAssetCount: getAssetListData.scannedAssetCount
      };

      // 完成的资产数
      const finishScanningAssetCount = this.workflowexecData.totalAssets - this.workflowexecData.numberOfUnscanned;
      if (!this.isCountingType && !this.isRentalWorkflow) {
        this.workflowexecData.totalSavedAssetAmountCount = finishScanningAssetCount;
      }
      console.log(`【list.page.ts - getAssetsByKeywordInWfAssetListForAssignScanDataFun】workflowexecData`, this.workflowexecData);
      this.changeDetectorRef.detectChanges();
    });

    return { getAssetListData: getAssetListData };
  }

  @Throttle()
  onClickscrollToTop() {
    this.content.scrollToTop(400).catch(() => {
      // This is intentional
    });
  }

  getScanedCount(total, unScaned) {
    if (total != undefined && unScaned != undefined) {
      return (total - unScaned).toLocaleString();
    } else {
      return 0;
    }
  }

  isAutoBlur = false;
  updateCountBlur(item: any, event: any) {
    event.stopPropagation();
    if (this.isAutoBlur) {
      this.isAutoBlur = false;
      return;
    }
    if (this.platform.is('android')) {
      // 如果输入框有焦点并且有文本内容
      if (this.lastInputElement) {
        // 全选文本
        this.lastInputElement.select();

        // 执行命令（例如复制文本到剪贴板）
        document.execCommand('insertText', false, event.target.value);
        this.isAutoBlur = true;
        this.lastInputElement.blur();
        this.lastInputElement = undefined;
      }
    }
  }

  getUrl(idx: number) {
    let turl = this.getItem(idx).thumbnail.turl;
    return turl;
  }
}
