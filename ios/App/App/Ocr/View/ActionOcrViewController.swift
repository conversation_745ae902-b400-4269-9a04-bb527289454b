//
//  ActionOcrViewController.swift
//  App
//
//  Created by <PERSON><PERSON><PERSON> on 2022/11/30.
//

import UIKit
import aiocr

//更新处理ocr扫描 仿照SerialNoCameraViewController
class ActionOcrViewController: UIViewController {
    @IBOutlet weak var previewView: AITeamPreviewView!
    @IBOutlet weak var lightBtn: UIButton!
    @IBOutlet weak var shadowMark: UIView!
    @IBOutlet weak var shutterBtn: UIButton!
    @IBOutlet weak var locationBtn: UIButton!
    // MARK: OCRStockView
    @IBOutlet weak var ocrStockView: UIView!
    @IBOutlet weak var label1: UILabel!
    @IBOutlet weak var label2: UILabel!
    @IBOutlet weak var label3: UILabel!
    @IBOutlet weak var topColorV: UIView!
    @IBOutlet weak var underColorV: UIView!
    @IBOutlet weak var imageView: UIImageView!
    @IBOutlet weak var serialNOL: UILabel!
    @IBOutlet weak var topOfSerialNOConstraint: NSLayoutConstraint!
    @IBOutlet weak var toScanListBtn: UIButton!
    @IBOutlet weak var bottomOfStockView: NSLayoutConstraint!

    private let ocrStockViewFrame = CGRect(x: SCREEN_WIDTH/2-100, y: SCREEN_HEIGHT/3-164, width: 200, height: 164)
    private let defaultStockViewFrame = CGRect(x: SCREEN_WIDTH/2-100, y: SCREEN_HEIGHT/3-164, width: 200, height: 164)
    var isAssetAmountMode: Bool = true
    var scanedAssetDic: [Int: NSMutableDictionary] = [Int: NSMutableDictionary]()
    private var badge = 0
    var assetTypeId: String = ""
    var requestSerialNOSet = Set<String>()
    var isFinishLoadModel = false
    var isSkipBuffer = 0
    // システムラベル名変更用
    var assetCommonList = Array<NSDictionary>()
    /// 対象内の資産リスト
    var registeredAssetList = Array<NSDictionary>()
    /// 資産へのボタンの管理
    var toListBtnMgr : ScanToListBtnManager?
    /// スキャンされた資産リスト
    var arMobileSetttingDic = NSMutableDictionary()
    // 資産情報取れない場合のAR表示
    var defaultView: UIView!
    // エラー内容表示
    var defaultLabel: UILabel!
    // MARK: WF承認
    /// エラーインフォ
    var errorInfo = NSMutableDictionary()
    /// フォームインフォリスト
//    var formList: Array<Form>?
    // 場所情報
    var location: String = ""
    var isScanMore: Bool = false
    /// バーコードで取れた情報
    var scanResult: ScanResult?
    /// スキャンされた情報リスト
    var scanResultList = Array<ScanResult>()
    var scanResultListDic = Array<NSDictionary>()
    /// 資産情報取得リスト
    var requestIdList = Set<String>()
    /// スキャンされた資産リスト
    var assetList = Array<NSMutableDictionary>()
    /// 資産リスト
    var barcodeList = Array<String>()
    /// スキャンされたバーコード（資産IDと同じ）
    var assetIdLIst = Array<String>()
    /// スキャンされた資産id
    var realAssetidList = Set<String>()
    /// progressDefinitionId: engineIdと同じ
    var progressDefinitionId: String?
    /// 作成されたリストのID
    var assetListId: String?
    /// 資産リスト
    var assetDataList: Array<AssetListData>?
    var processInstanceId: String?
    var taskId: String?
//    var isFromNew: Bool = false
//    var scanedAssetCount = 0
//    // 是否为个数
//    var isCountingType: Bool = false
    // 所有已扫描资产中数量的总和
    var totalSavedAssetAmountCount = 0
    // 是否为复数人（正常情况下会质疑为false）
    var isMultiScan = false
    /// 是否为subform独特情况
    var isNewW3 = false
    // 1.
    typealias scanedInfoBlockFunc = (_ assetDataList: Array<AssetListData>, _ assetIdList: Array<String>, _ assetList: Array<NSMutableDictionary>)->Void
    // 2.
    var scanedInfoBlock:scanedInfoBlockFunc?
    // 3.
    func callBack( mathFunction:@escaping  (_ assetDataList: Array<AssetListData>, _ assetIdList: Array<String>, _ assetList: Array<NSMutableDictionary>)->Void){
        scanedInfoBlock = mathFunction
    }

    // MARK: Controllers that manage functionality
    private lazy var cameraFeedManager = CameraFeedManager(previewView: previewView)

    private lazy var cropRect = CGRect(x:((SCREEN_WIDTH-320)/2), y: SCREEN_HEIGHT/3, width: 320, height: 48)

    var ocrCfg: AITeamOcrModel!
    let alertMessage = "読み取りに失敗しました。もう一度撮影してください。"

    private var predictor: MobileSerialNo?

    //给闭包起一个别名
    typealias OcrResult = (Dictionary<AnyHashable, Any>?) -> ()
    //闭包属性
    var resultBlock: OcrResult?

    var getBarCodeBlock:((_ arr : NSDictionary)->())?

    var config: CarNumberConfiguration!

    var scanType: ScanType!
    //dpp上传参数
    var dppEmail: String!
    var dppAccessKey: String!
    var dppUrlStr: String = ""

    //eunomia上传参数
    var eunomiaAccessName: String!
    var eunomiaAccessKey: String!
    var eunomiaUrlStr: String!
    var modelUpdateUrlArr : [String] = []
    var serialNo: String = ""

    var isFinishPredictor = true
    var isStopTwoSeconds = false

    var editAssetList = Array<EditAssetModel>() // 作为重置资产用的数组

    // 前の画面から引き渡してくれた
    var assetActionModel: AssetAction?
    var actionunRegisteredAssetList = Array<NSDictionary>()
    // スキャン画面を閉じるときにアラートを出すか
    var showAlertWhenCloseScanWindow: String?
    var isFromActionListPage: Bool = false
    // 扫描顺序记录
    var scanOder = 0
    var isFromRegisteredListPage = false
    private let notificationCenter = NotificationCenter.default

    override func viewDidLoad() {
        super.viewDidLoad()
        cameraFeedManager.cameraMode = .video
        // システムラベル名のデータをとる
        let isFromLocation = self.getAssetItemCommon()

        cameraFeedManager.delegate = self

        cropShadowMark()

        showBtnAndARView()

        subscribeNotification()

        NotificationCenter.default.addObserver(self, selector: #selector(updateModelFiles(noti:)), name:NSNotification.Name(rawValue: "updateModelFiles"),object: nil)
        /// ロケショーンボタンが表示・非表示
        if !isFromLocation {
            DispatchQueue.main.async {
                self.locationBtn.isHidden = true
            }
        }
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        barcodeList = Utils.barcodeTrim(barcodeList: barcodeList)
        // serialNOLのframe調整
        self.topOfSerialNOConstraint.constant = SCREEN_HEIGHT/3+48

        getActionRegisteredAssetListDic(values: self.registeredAssetList)

        AITeamNetWorkTool().updateAITeamModel(email: eunomiaAccessName, accessKey: eunomiaAccessKey, urlStrArr: modelUpdateUrlArr, serialNo: serialNo, modelType: .serialNO)

        let userDefaults = SecureUserDefaults.shared.secureDefaults
        let downloadStatus = userDefaults.object(forKey: "\(serialNo)-AITeamModelDownloadStatus") as? String

        if downloadStatus == "Downloading" || downloadStatus == nil || downloadStatus == "VersionChecking" {

            SVProgressHUD.show(withStatus: "更新中...")
            self.shutterBtn.isUserInteractionEnabled = false
        }else if downloadStatus!.contains("DownloadFailed") {

            self.shutterBtn.isUserInteractionEnabled = false

            alertUpdateMessage(message: "AIモデルデータの更新に失敗しました。ネットワーク状況をご確認の上、もう一度「読み取りモードの選択」からAI-OCRを選択してください。")

        }else {

            self.shutterBtn.isUserInteractionEnabled = true
        }

        cameraFeedManager.checkCameraConfigurationAndStartSession()
        if totalSavedAssetAmountCount > 0 {
            setDetailBtnBadgeInRefund(isInit: true)
        }
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        removeNotificationCenter()
        SVProgressHUD.dismiss()
        cameraFeedManager.stopSession()

        guard let pred = self.predictor else {
            return
        }
        if pred.errorNo.intValue < 1 {
            pred.closePredictor()
        }
    }

    override var preferredStatusBarStyle: UIStatusBarStyle {
        return .lightContent
    }

    func removeNotificationCenter() {
        NotificationCenter.default.removeObserver(self, name: UIApplication.willEnterForegroundNotification, object: nil)
        NotificationCenter.default.removeObserver(self, name: UIApplication.didEnterBackgroundNotification, object: nil)
        NotificationCenter.default.removeObserver(self, name: NSNotification.Name(rawValue: "updateModelFiles"), object: nil)
    }

    func subscribeNotification() {
        NotificationCenter.default.addObserver(self, selector: #selector(willEnterForeground(notification:)), name: UIApplication.willEnterForegroundNotification, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(didEnterBackground(notification:)), name: UIApplication.didEnterBackgroundNotification, object: nil)
    }

    @objc func willEnterForeground(notification: NSNotification) {
        self.cameraFeedManager.startRunning()
    }


    @objc func didEnterBackground(notification: NSNotification){
//        cameraFeedManager.stopRunning()
    }

    // active in Workflow
    @IBAction func toAssetListBtnAction(_ sender: UIButton) {
        self.isFinishPredictor = false
        if self.badge > 0 {
            let dic = NSDictionary.init(dictionary: ["type":"2", "isFromRegisteredListPage": self.isFromRegisteredListPage, "registeredAssetList":self.mergedActionRegisteredAssetList])
            cameraFeedManager.stopSession()
            self.getBarCodeBlock?(dic)
            Utils.dismissVC(vc: self)
        }
    }

    @objc func updateModelFiles(noti: NSNotification) {
        guard noti.userInfo != nil else {
            DispatchQueue.main.async { [self] in
                SVProgressHUD.dismiss()
                self.shutterBtn.isUserInteractionEnabled = true
                initializeTFModel()
            }
            return
        }

        if noti.userInfo!["Downloading"] != nil {
            self.shutterBtn.isUserInteractionEnabled = false
            return
        }

        SVProgressHUD.dismiss()
        alertUpdateMessage(message: "AIモデルデータの更新に失敗しました。ネットワーク状況をご確認の上、もう一度「読み取りモードの選択」からAI-OCRを選択してください。")
    }

    func initializeTFModel() {
        let cachesPath = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).first! as String
        let ocrPath = cachesPath + "/AITeamFiles/\(serialNo)/Current/serialNo_ocr_model.tflite"
        let labelMapsPath = cachesPath + "/AITeamFiles/\(serialNo)/Current/serialNo_labelsMap.txt"
        let configPath = cachesPath + "/AITeamFiles/\(serialNo)/Current/serialNo.ini"
        let fileManager: FileManager = FileManager.default
        var exist = fileManager.fileExists(atPath: ocrPath)
        if (!exist) {
            Utils.toDownloadModelOnceMoreWhenError(serialNo: serialNo)
            alertUpdateMessage(message: alertMessage)
            return
        }
        exist = fileManager.fileExists(atPath: labelMapsPath)
        if (!exist) {
            Utils.toDownloadModelOnceMoreWhenError(serialNo: serialNo)
            alertUpdateMessage(message: alertMessage)
            return
        }
        exist = fileManager.fileExists(atPath: configPath)
        if (!exist) {
            Utils.toDownloadModelOnceMoreWhenError(serialNo: serialNo)
            alertUpdateMessage(message: alertMessage)
            return
        }
        guard let predictor = MobileSerialNo.init(configPath: configPath, ocrPath: ocrPath, labelPath: labelMapsPath) else {
            alertUpdateMessage(message: alertMessage)
            return
        }
        predictor.initPredictor()
        if predictor.errorNo.intValue > 0 {
            alertUpdateMessage(message: alertMessage)
        }
        self.predictor = predictor
        self.isFinishPredictor = true
    }

    func cropShadowMark() {
        let path = UIBezierPath(rect: self.previewView.bounds)
        path.append(UIBezierPath(rect: cropRect).reversing())
        let shape = CAShapeLayer()
        shape.path = path.cgPath
        self.shadowMark.layer.mask = shape
    }

    func alertUpdateMessage(message : String) {
        let alertController = UIAlertController(title: "注意",
                                                message: "\(String(describing: message))", preferredStyle: .alert)
        let cancelAction = UIAlertAction(title: "キャンセル", style: .cancel, handler: { [self]
            action in

            previewView.removeFromSuperview()
            switch config {
            case .nativeView:
                dismiss(animated: true, completion: nil)
            case .ionicView:
                self.resultBlock!(nil)
                dismiss(animated: true, completion: nil)
            case .none:
                return
            }
        })
        alertController.addAction(cancelAction)
        DispatchQueue.main.async {
            self.present(alertController, animated: true, completion: nil)
        }

    }

    func getResultBySDK(pic : UIImage) -> () {
        if !self.isFinishPredictor || self.isStopTwoSeconds {return}
        guard let result = predictor?.predict(with: pic) else {
            return
        }

        DispatchQueue.main.async {
            if !self.isStopTwoSeconds {
                self.ocrStockView.isHidden = true
            }
        }
        if let value = result.ocrResult?.object(at: 0) as? String {
            let str = getCodeStr(code: value)
            pushToResultViewWF(image: pic, drawImage: pic, resultString: str, isManualInput: false)
        }else {
            DispatchQueue.main.async {
                self.serialNOL.text = ""
            }
        }
    }

    func pushToResultViewWF(image : UIImage, drawImage :UIImage, resultString :String, isManualInput: Bool) -> () {
        DispatchQueue.main.async {
            self.serialNOL.text = resultString
        }
        let timeInterval: TimeInterval = Date().timeIntervalSince1970
        let imageNameTime = "\(Int(timeInterval))"
        let dppParameters = AITeamDppUpload()
        dppParameters.dppEmail = dppEmail
        dppParameters.dppAccessKey = dppAccessKey
        dppParameters.dppUrlStr = dppUrlStr
        dppParameters.resultStrs = [resultString]
        dppParameters.image = drawImage
        dppParameters.imageName = imageNameTime
        dppParameters.allRects = []
        dppParameters.serialNo = self.serialNo
        DispatchQueue.main.async {
            SVProgressHUD.dismiss()
            if (resultString != "") {
                DEBUGLOG.NSLog(message: "resultString ===="+resultString)
                if !(self.arMobileSetttingDic.allKeys as! [String]).contains(String(self.assetTypeId)) {
                    self.getAssetMobileSettingForMobile(assetTypeId: Int(self.assetTypeId) ?? 0, barCode: resultString) { info in
                    }
                }
                if (!self.requestSerialNOSet.contains(resultString.lowercased()) || self.isAssetAmountMode) && !self.isStopTwoSeconds {
                    let ocrCode = self.checkAssetInfo(scanResult: resultString, imageName: imageNameTime, isManualInput: isManualInput)
                    if ocrCode != "" {
                        self.getAssetInfoBySerialNumber(ocrCode: ocrCode, imageName: imageNameTime, isManualInput: isManualInput)
                    }
                    AITeamNetWorkTool().sendAIdataToDppServer(uploadParameters: dppParameters, config: .carNumber)
                }else {
                    for resultDic in self.registeredAssetList {
                        if resultDic["serialNO"] as? String ?? "" == resultString && !self.isStopTwoSeconds {
                            self.setValueForStockView(resultDic: resultDic as! NSMutableDictionary)
                        }
                    }
                }
            }else {
                if !self.isStopTwoSeconds {
                    self.ocrStockView.isHidden = true
                }
            }
        }
    }

    func pushToResultView(image : UIImage, drawImage :UIImage, resultString :String) -> () {

        let timeInterval: TimeInterval = Date().timeIntervalSince1970
        let imageNameTime = "\(Int(timeInterval))"
        let dppParameters = AITeamDppUpload()
        dppParameters.dppEmail = dppEmail
        dppParameters.dppAccessKey = dppAccessKey
        dppParameters.dppUrlStr = dppUrlStr
        dppParameters.resultStrs = [resultString]
        dppParameters.image = drawImage
        dppParameters.imageName = imageNameTime
        dppParameters.allRects = []
        dppParameters.serialNo = self.serialNo
        AITeamNetWorkTool().sendAIdataToDppServer(uploadParameters: dppParameters, config: .carNumber)
        DispatchQueue.main.async { [self] in
            SVProgressHUD.dismiss()
            switch self.config {
            case .nativeView:
                let tfLiteSB = UIStoryboard(name: "Ocr", bundle:Bundle.main)
                guard let resultVc = tfLiteSB.instantiateViewController(withIdentifier: "PlateNumberResultViewController") as? PlateNumberResultViewController else {
                    fatalError("There is No PlateNumberResultViewController")
                }
                resultVc.drawImage = drawImage
                resultVc.string = resultString
                resultVc.config = .serialNo
                resultVc.imageName = imageNameTime
                resultVc.eunomiaAccessName = eunomiaAccessName
                resultVc.eunomiaAccessKey = eunomiaAccessKey
                resultVc.eunomiaUrlStr = self.eunomiaUrlStr
                resultVc.serialNo = self.serialNo
                resultVc.resultBlock = { [self] dic in
                    self.scanSerialNumber(arr: dic as NSDictionary)
                }
                let nav = LightStatusBarNavigationController(rootViewController: resultVc)
                nav.navigationBar.tintColor = UIColor.white
                var textAttributes: [NSAttributedString.Key: AnyObject] = [:]
                textAttributes[.foregroundColor] = UIColor.white
                nav.navigationBar.titleTextAttributes = textAttributes
                nav.navigationBar.barTintColor = UIColor(red: 49.0 / 255.0, green: 66.0 / 255.0, blue: 194.0 / 255.0, alpha: 1)
                nav.modalPresentationStyle = .fullScreen
                self.present(nav, animated: true, completion: nil)
            case .ionicView:
                self.dismiss(animated: true, completion: nil)
            case .none:
                return
            }
        }
    }

    func scanSerialNumber(arr: NSDictionary) {
        var arr1:Array<Any> = []
        var arr2:Array<Any> = []
        arr1 = arr.object(forKey: "barCode") as! Array<Any>
        arr2 = arr.object(forKey: "assetDetail") as! Array<Any>

        let dataDic = NSDictionary.init(dictionary: ["type":"2", "barCode":arr1, "assetDetail":arr2, "isFromRegisteredListPage": self.isFromRegisteredListPage])
        self.getBarCodeBlock?(dataDic)
        DispatchQueue.main.async {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3, execute:{
                self.dismiss(animated: true, completion: nil)
            })
        }
    }

    @IBAction func locationSetting(_ sender: UIButton) {
        cameraFeedManager.stopSession()
        self.isFinishPredictor = false
        let dic = NSDictionary.init(dictionary:
                                        ["type": "5",
                                         "scanType":"ocr",
                                         "isFromRegisteredListPage": self.isFromRegisteredListPage,
                                         "realAssetidList": self.mergedActionRegisteredAssetList, // assetID(同じbarcode、違う場所)
                                         "editAssetList":self.editAssetList.toJSONString() ?? "","scanedAssetCount": self.totalSavedAssetAmountCount
                                        ])
        self.getBarCodeBlock?(dic)
        Utils.dismissVC(vc: self)
    }

    @IBAction func closeAction(_ sender: Any) {
        self.isFinishPredictor = false
        switch config {
        case .nativeView:
            if self.showAlertWhenCloseScanWindow == "true" {
                self.dismiss(animated: true) {}
            }else {
                if self.isFromActionListPage {  //更新处理「スキャン済リスト」页扫描时选择地点后,关闭扫描页时后路径修正
                    //                self.getBarCodeBlock?(["type":"close", "toRegistered":true, "registeredAssetList": self.registeredAssetList]) //スキャン済リスト画面から
                    self.showAlert(.WFScanClose(.customize({_ in
                        self.previewView.removeFromSuperview()
                        if self.isFromRegisteredListPage {
                            self.getBarCodeBlock?(["type":"close", "isFromRegisteredListPage": self.isFromRegisteredListPage, "registeredAssetList": self.registeredAssetList]) //スキャン済リスト画面から
                        }
                        self.dismiss(animated: true, completion: nil)
                    }), .customize({_ in
                        self.isFinishPredictor = true
                    })))
                } else {
                    self.showAlert(.WFScanClose(.customize({_ in
                        self.previewView.removeFromSuperview()
                        if self.isFromRegisteredListPage {
                            self.getBarCodeBlock?(["type":"close", "isFromRegisteredListPage": self.isFromRegisteredListPage, "registeredAssetList": self.registeredAssetList])
                        }
                        self.dismiss(animated: true, completion: nil)
                    }), .customize({_ in
                        self.isFinishPredictor = true
                    })))
                    //                self.previewView.removeFromSuperview()
                    ////                self.getBarCodeBlock?(["type":"close"])
                    //                self.dismiss(animated: true, completion: nil)
                }
            }


//            //更新处理「スキャン済リスト」页扫描时选择地点后,关闭扫描页时后路径修正
//            if self.isFromActionListPage {
//                let dic = NSDictionary.init(dictionary: ["type":"close"]) // NSDictionary.init(dictionary: ["type":"close", "registeredAssetList":self.mergedActionRegisteredAssetList])
//                self.getBarCodeBlock?(dic)
//            }
//            dismiss(animated: true, completion: nil)
        case .ionicView:
            previewView.removeFromSuperview()
            self.resultBlock!(nil)
            dismiss(animated: true, completion: nil)
        case .none:
            previewView.removeFromSuperview()
            return
        }
    }

    @IBAction func editAlert(_ sender: Any) {
        SVProgressHUD.dismiss()
        cameraFeedManager.stopSession()
        let alertController = UIAlertController(title: "編集", message: "識別コードを入力してください。 ", preferredStyle: .alert)

        let loginAction = UIAlertAction(title: "登録", style: .cancel, handler: { [self]
            action in

            guard let textField = alertController.textFields?.first else {
                self.cameraFeedManager.checkCameraConfigurationAndStartSession()
                return
            }

            if textField.text!.count > 0 {
                let image = UIImage(named: "placeholderImage")!
                pushToResultViewWF(image: image, drawImage: image, resultString: textField.text!, isManualInput: true)
                self.cameraFeedManager.checkCameraConfigurationAndStartSession()
            }else {
                SVProgressHUD.showError(withStatus: "空欄で登録できません")
                self.cameraFeedManager.checkCameraConfigurationAndStartSession()
                return
            }
        })

        let cancelAction = UIAlertAction(title: "キャンセル", style: .default, handler: {
            action in
            self.cameraFeedManager.checkCameraConfigurationAndStartSession()
        })

        alertController.addTextField { (textField :UITextField) in
            textField.text = ""
        }
        alertController.addAction(loginAction)
        alertController.addAction(cancelAction)
        self.present(alertController, animated: true, completion: nil)
    }

    @IBAction func lightAction(_ sender: Any) {
        if !lightBtn.isSelected {
            cameraFeedManager.turnOnLight()
        }else {
            cameraFeedManager.turnOffLight()
        }
        lightBtn.isSelected = !lightBtn.isSelected
    }

    @IBAction func cameraClick(_ sender: Any) {
        shutterBtn.isUserInteractionEnabled = false
        cameraFeedManager.getPhotoCapture(scanType: .action)
    }

}

// MARK: CameraFeedManagerDelegate Methods
extension ActionOcrViewController: CameraFeedManagerDelegate {
    func presentCameraPermissionsDeniedAlert() {
        Utils.presentCameraPermissionsDeniedAlert()
    }
    
    func didOutputPhotoCapture(image: UIImage) {
        if !self.isFinishPredictor || self.isStopTwoSeconds {return}
        let radioCropImage = image.crop(ratio: self.previewView.frame.width/self.previewView.frame.height)

        let x = cropRect.origin.x / previewView.frame.size.width * radioCropImage.size.width
        let y = cropRect.origin.y / previewView.frame.size.height * radioCropImage.size.height
        let w = cropRect.size.width/previewView.frame.size.width * radioCropImage.size.width
        let h = cropRect.size.height/previewView.frame.size.height * radioCropImage.size.height

        let cropImage = radioCropImage.cropping(to: CGRect(x: x, y: y, width: w, height: h))!
        self.getResultBySDK(pic: cropImage)
    }

    func didOutput(pixelBuffer: CVPixelBuffer) {

    }

    func didOutputPicture(image: UIImage) {
        isSkipBuffer += 1
        if isSkipBuffer%10 != 0 {return}
        isSkipBuffer = 0
        DispatchQueue.main.async {
            let radioCropImage = image.crop(ratio: self.previewView.frame.width/self.previewView.frame.height)

            let x = self.cropRect.origin.x / self.previewView.frame.size.width * radioCropImage.size.width
            let y = self.cropRect.origin.y / self.previewView.frame.size.height * radioCropImage.size.height
            let w = self.cropRect.size.width/self.previewView.frame.size.width * radioCropImage.size.width
            let h = self.cropRect.size.height/self.previewView.frame.size.height * radioCropImage.size.height

            guard let cropImage = radioCropImage.cropping(to: CGRect(x: x, y: y, width: w, height: h)) else {return}

            self.getResultBySDK(pic: cropImage)
        }
    }

    // MARK: Session Handling Alerts
    func sessionRunTimeErrorOccured() {

    }

    func sessionWasInterrupted(canResumeManually resumeManually: Bool) {


    }

    func sessionInterruptionEnded() {

    }

    func presentVideoConfigurationErrorAlert() {

        let alertController = UIAlertController(title: "Confirguration Failed", message: "Configuration of camera has failed.", preferredStyle: .alert)
        let okAction = UIAlertAction(title: "OK", style: .cancel, handler: nil)
        alertController.addAction(okAction)

        present(alertController, animated: true, completion: nil)
    }
}


// MARK:  Workflow新規申請向け
extension ActionOcrViewController {
    /// scanedAssetDicアップデート
    /// - Parameter assetDic: assetDic description
    func updateScanedAssetDic(assetDic: NSMutableDictionary){
        if  let assetID = assetDic["assetId"] as? Int{
            scanOder += 1
            if self.scanedAssetDic[assetID] != nil{
                if self.isAssetAmountMode{
                    self.updateScanTimesForAsset(assetID)
                }
                self.scanedAssetDic[assetID]?.setValue(scanOder, forKey: "scanOrder")
            }else{
                self.badge += 1
                assetDic.setValue(1, forKey: "assetScanedCount") // UI表示用
                assetDic.setValue(1, forKey: "assetAmount") // backend用
                assetDic.setValue(scanOder, forKey: "scanOrder") // 扫描顺序记录
                self.scanedAssetDic[assetID] = assetDic
            }
        }
    }

    /// スキャン件数をアップデート
    /// - Parameter assetID: assetID description
    private func updateScanTimesForAsset(_ assetID: Int){
        var scantimes = 1

        self.badge += 1
        scantimes = (self.scanedAssetDic[assetID]?["assetScanedCount"] as? Int  ?? 1) + 1
        self.scanedAssetDic[assetID]?.setValue(scantimes, forKey: "assetScanedCount") // UI表示用
        self.scanedAssetDic[assetID]?.setValue(scantimes, forKey: "assetAmount") // backend用
    }

    /// RegisteredAssetList作る
    private var mergedActionRegisteredAssetList: Array<NSDictionary>{
        var temp = Array<NSDictionary>()
        for (_, value) in self.scanedAssetDic{
            if value["assetText"] is String {
                value["assetText"] = Utils.getDictionaryFromJSONString(jsonString:value["assetText"] as? String ?? "")
            }
//            value["assetItemsList"] = value["assetItems"]
            temp.append(value)
        }
        temp = temp.sorted(by: { ($0["scanOrder"] as? Int ?? 0) < ($1["scanOrder"] as? Int ?? 0) })
        return temp
    }

    /// スキャンされたリストの整理
    /// - Parameter values: values description
    func getActionRegisteredAssetListDic(values: Array<NSDictionary>){
        for value in values{
            if let assetId = value["assetId"] as? Int,
               let mutableDic: NSMutableDictionary =  value.mutableCopy() as? NSMutableDictionary{
                scanOder += 1
                mutableDic["scanOrder"] = scanOder
                self.scanedAssetDic[assetId] = mutableDic
            }
            if let count = value["assetScanedCount"] as? Int{
                self.badge += count
            }
        }
        DispatchQueue.main.async {
            self.setToListBtnBadge(self.badge)
        }
    }

    /// スキャンされた件数の表示
    /// - Parameter badge: badge description
    func setToListBtnBadge(_ badge: Int) {
        if toListBtnMgr == nil{
            DispatchQueue.main.async {
                self.view.layoutIfNeeded()
                self.toListBtnMgr = ScanToListBtnManager(btn: self.toScanListBtn)
            }
        }
        toListBtnMgr?.refreshCount(count: badge)
    }

    /// ARビューの表示
    func showBtnAndARView() {
        self.shutterBtn.isHidden = true
        self.toScanListBtn.isHidden = false
        self.view.layoutIfNeeded()
        toListBtnMgr = ScanToListBtnManager(btn: self.toScanListBtn)
    }



    // 针对要重置的资产进行一个list编辑
    func creatEditAssetList (assetId:Int, assetAmout:Int) {
        var isInList = false
        for editAsset in self.editAssetList {
            if editAsset.assetId == assetId {
                isInList = true
                break
            }
        }
        if !isInList {
            let editAssetM = EditAssetModel()
            editAssetM.assetId = assetId
            editAssetM.initAssetAmount = assetAmout
            self.editAssetList.append(editAssetM)
        }
    }

    /// スキャン画像のアップロード
    /// - Parameter imageName: imageName description
    func uploadOcrResultToAITeamServer(imageName: String) {
        let date1 = Date()
        let formatter1 = DateFormatter.init()
        formatter1.dateFormat = "yyyy-MM-dd HH:mm:ss"
        let dateStr1 = formatter1.string(from: date1)
        let ocrResult = "1"
        let parameters = [
            "serialNo": serialNo,
            "datetime": dateStr1,
            "segmResult": "1",
            "ocrResult": ocrResult,
            "imageName": "\(imageName).jpeg",
            "eunomiaAccessName": eunomiaAccessName!,
            "eunomiaAccessKey": eunomiaAccessKey!
        ]
        AITeamNetWorkTool().uploadOcrResultToAITeamServer(urlStr: eunomiaUrlStr, parameters: [parameters])
    }

    /// 不要な記号を入れ替える
    /// - Parameter scanResultString: scanResultString description
    /// - Returns: description
    func replaceOccurrences(scanResultString: String) -> String {
        var string = scanResultString
        string = string.replacingOccurrences(of: "-", with: "")
        string = string.replacingOccurrences(of: "ー", with: "")
        string = string.replacingOccurrences(of: "･", with: "")
        string = string.replacingOccurrences(of: "・", with: "")
        string = string.replacingOccurrences(of: " ", with: "")
        string = string.replacingOccurrences(of: "　", with: "")
        return string
    }

    func checkAssetInfo(scanResult: String, imageName: String, isManualInput: Bool = false) -> String {
        var flag = ""
        self.isStopTwoSeconds = true
        uploadOcrResultToAITeamServer(imageName: imageName)
        let ocrCode = scanResult //replaceOccurrences(scanResultString: scanResult)
        if ocrCode.count < 6 { //扫描出的字符小于8位的话不做处理(目前扫描码最少是8位字符)
            self.reset()
            return flag
        }
        flag = ocrCode
        let urlStr = URLManager.shared.assetActionListVerify(barcode: Utils.resetCharacterSet(barcode: ocrCode), assetActionId: (self.assetActionModel?.assetActionId ?? 0))
        ApiManager.shared.getAssetInfoByBarcode(barcode: ocrCode, urlStr: urlStr, controller: self) { (data) in
            let str = String(data: data, encoding: String.Encoding.utf8)
            let dicResult = Utils.getDictionaryFromJSONString(jsonString: str!)

            if let errorCode = dicResult["errorCode"] {
                //                self.unregisterBarcodeList.append(barCode)
                switch errorCode as? String {
                case "ERROR_ASSET_TYPE":
                    self.errorInfo[ocrCode] = "\(AR_MESSAGE_NOT_THE_TARGET)\n\(ocrCode)"
                    if isManualInput == true {
                        SVProgressHUD.showError(withStatus: "\(AR_MESSAGE_NOT_THE_TARGET)\n\(ocrCode)")
                    }else {
                        self.createDefaultView(barcode: ocrCode)
                    }
                    flag = ""
                case "ERROR_NOT_EXISTS":
                    self.errorInfo[ocrCode] = "\(AR_MESSAGE_UNKNOWN_ASSET)\n\(ocrCode)"
                    if isManualInput == true {
                        SVProgressHUD.showError(withStatus: "\(AR_MESSAGE_UNKNOWN_ASSET)\n\(ocrCode)")
                    }else {
                        self.createDefaultView(barcode: ocrCode)
                    }
                    flag = ""
                case "ERROR_CONDITION":
                    self.errorInfo[ocrCode] = "\(AR_MESSAGE_NOT_THE_TARGET)\n\(ocrCode)"
                    if isManualInput == true {
                        SVProgressHUD.showError(withStatus: "\(AR_MESSAGE_NOT_THE_TARGET)\n\(ocrCode)")
                    }else {
                        self.createDefaultView(barcode: ocrCode)
                    }
                    flag = ""
                case "ERROR_AUTHORITY_FORBIDDEN":
                    self.errorInfo[ocrCode] = "この資産の閲覧権限がありません"
                    if isManualInput == true {
                        SVProgressHUD.showError(withStatus: "この資産の閲覧権限がありません")
                    }else {
                        self.createDefaultView(barcode: ocrCode)
                    }
                    flag = ""
                case "ERROR_LOCATION":
                    self.errorInfo[ocrCode] = "\(AR_MESSAGE_NOT_THE_TARGET)\n\(ocrCode)"
                    if isManualInput == true {
                        SVProgressHUD.showError(withStatus: "\(AR_MESSAGE_NOT_THE_TARGET)\n\(ocrCode)")
                    }else {
                        self.createDefaultView(barcode: ocrCode)
                    }
                    flag = ""
                default:
                    break
                }
            }
        } faild: { (err) in
            flag = ""
            DEBUGLOG.NSLog(message: "err ====== \(err)")
            self.reset()
        }
        return flag
    }

    /// - Parameters:
    ///   - scanResult: scanResult description
    ///   - imageName: imageName description
    ///   - isManualInput: isManualInput description
    func getAssetInfoBySerialNumber(ocrCode: String, imageName: String, isManualInput: Bool = false) {
        self.isStopTwoSeconds = true
        let urlStr = "/secure/Asset/findByBarCodeForMobile"
        let assetActionId = self.assetActionModel?.assetActionId ?? 0
        var location = self.location
        if location == NON_POSITION {
            location = ""
        }
        ApiManager.shared.getAssetInfoByBarcode(url: urlStr, barcode: ocrCode, location: location, scanFrom: "action", searchId: "", workflowId: 0, engineId: "", assetActionId: String(assetActionId), controller: self) { (data) in
            let str = String(data: data, encoding: String.Encoding.utf8)
            let dicResult = Utils.getDictionaryFromJSONString(jsonString: str!)

            ScanMusicUtil.feedback()
            if let arrayAsset = dicResult["arrayAsset"] {
                if (arrayAsset as! NSArray).count != 0 {
                    let dic = (arrayAsset as! NSArray)[0] as! NSDictionary
                    if (dic["assetTypeId"] is String && dic["assetTypeId"] as! String != self.assetTypeId) ||
                        (dic["assetTypeId"] is Int && String(dic["assetTypeId"] as! Int) != self.assetTypeId) {
                        self.errorInfo[ocrCode] = "\(AR_MESSAGE_NOT_THE_TARGET)\n\(ocrCode)"
                        if isManualInput == true {
                            SVProgressHUD.showError(withStatus: "\(AR_MESSAGE_NOT_THE_TARGET)\n\(ocrCode)")
                        }else {
                            self.createDefaultView(barcode: ocrCode)
                        }
                        return
                    }
                    if let assetText = dic["assetText"], assetText is String {
                        let resultDic:NSMutableDictionary = Utils.getDictionaryFromJSONString(jsonString: assetText as! String) as! NSMutableDictionary
                        resultDic.setValue(dic[ASSET_ID], forKey: ASSET_ID)
                        resultDic.setValue(dic["assetTypeId"], forKey: "assetTypeId")
                        resultDic.setValue(ocrCode, forKey: "serialNO")
                        resultDic.setValue("1", forKey: "assetScanedCount")

                        var barcodeList = Set<String>()
                        for registeredAsset in (self.registeredAssetList) {
                            if registeredAsset.object(forKey: ID) is String {
                                barcodeList.insert(registeredAsset.object(forKey: ID) as! String)
                            }
                            if registeredAsset.object(forKey: ID) is Int {
                                barcodeList.insert(String(registeredAsset.object(forKey: ID) as! Int))
                            }
                        }
                        self.updateScanedAssetDic(assetDic: dic as! NSMutableDictionary)
                        if barcodeList.contains(ocrCode) && !self.isAssetAmountMode{
                            return
                        }
                        self.requestSerialNOSet.insert(ocrCode.lowercased())
                        self.registeredAssetList.append(resultDic)

                        DispatchQueue.main.async {
                            // set stock view autolayout
                            self.setValueForStockView(resultDic: resultDic)
                            self.setToListBtnBadge(self.badge)
                        }
                    }
                    DispatchQueue.main.asyncAfter(deadline: DispatchTime.now() + 2) {
                        self.isStopTwoSeconds = false
                    }
                }else {
                    DispatchQueue.main.async {
                        self.ocrStockView.isHidden = true
                        self.reset()
                        self.errorInfo[ocrCode] = "\(AR_MESSAGE_UNKNOWN_ASSET)\n\(ocrCode)"
                        if isManualInput == true {
                            SVProgressHUD.showError(withStatus: "\(AR_MESSAGE_UNKNOWN_ASSET)\n\(ocrCode)")
                        }else {
                            self.createDefaultView(barcode: ocrCode)
                        }
                        return
                    }
                }
            } else {
                self.reset()
            }
        } faild: { (err) in
            DEBUGLOG.NSLog(message: "err ====== \(err)")
            self.reset()
        }
    }


    /// ARビューの設定
    /// - Parameter resultDic: resultDic description
    func setValueForStockView(resultDic: NSMutableDictionary){
        if self.defaultView != nil {
            self.defaultView.isHidden = true
        }
        var arMobielSettingInfoByTypeIdDic: NSMutableDictionary!
        if self.arMobileSetttingDic[(resultDic[ID] as? String ?? "").lowercased()] != nil || self.arMobileSetttingDic[(resultDic[ID] as? String ?? "").uppercased()] != nil {
            if self.arMobileSetttingDic[(resultDic[ID] as? String ?? "").lowercased()] != nil {
                arMobielSettingInfoByTypeIdDic = self.arMobileSetttingDic[(resultDic[ID] as? String ?? "").lowercased()] as? NSMutableDictionary
            }
            if self.arMobileSetttingDic[(resultDic[ID] as? String ?? "").uppercased()] != nil {
                arMobielSettingInfoByTypeIdDic = self.arMobileSetttingDic[(resultDic[ID] as? String ?? "").uppercased()] as? NSMutableDictionary
            }

            let dic = arMobielSettingInfoByTypeIdDic["assetMobileSetting"]  as! NSMutableDictionary
            var arLevel1ItemName:String?
            var arLevel2ItemName:String?
            var arLevel3ItemName:String?
            var color:String?
            ///-------------カテゴリー大--------------------------------
            arLevel1ItemName = self.setCatagoryInfo(arMobielSettingInfoByTypeIdDic: dic, arLevelItemOption: "arLevel1ItemOption", arLevelItemId: "arLevel1ItemId", arLevelItemName: "arLevel1ItemName", arLevelItemDisplayName: "arLevel1ItemDisplayName", arLevelItemType: "arLevel1ItemType",arLevelMasterLayoutSetting:"arLevel1MasterLayoutSetting", arLevelSubItemId:"arLevel1SubItemId", resultDic: resultDic)
            ///-------------カテゴリー中--------------------------------
            arLevel2ItemName = self.setCatagoryInfo(arMobielSettingInfoByTypeIdDic: dic, arLevelItemOption: "arLevel2ItemOption", arLevelItemId: "arLevel2ItemId", arLevelItemName: "arLevel2ItemName", arLevelItemDisplayName: "arLevel2ItemDisplayName", arLevelItemType: "arLevel2ItemType",arLevelMasterLayoutSetting:"arLevel2MasterLayoutSetting", arLevelSubItemId:"arLevel2SubItemId",  resultDic: resultDic)
            ///-------------カテゴリー小--------------------------------
            arLevel3ItemName = self.setCatagoryInfo(arMobielSettingInfoByTypeIdDic: dic, arLevelItemOption: "arLevel3ItemOption", arLevelItemId: "arLevel3ItemId", arLevelItemName: "arLevel3ItemName", arLevelItemDisplayName: "arLevel3ItemDisplayName", arLevelItemType: "arLevel3ItemType", arLevelMasterLayoutSetting:"arLevel3MasterLayoutSetting", arLevelSubItemId:"arLevel3SubItemId",resultDic: resultDic)
            ///-------------color--------------------------------
            if arMobielSettingInfoByTypeIdDic["arColor"] is String {
                color = (arMobielSettingInfoByTypeIdDic["arColor"]! as! String)
            }
            DispatchQueue.main.async {
                self.label1.text = arLevel1ItemName
                self.label2.text = arLevel2ItemName
                self.label3.text = arLevel3ItemName
                if color != "" && color != nil {
                    self.topColorV.backgroundColor = self.ColorHex(color ?? "")
                    self.underColorV.backgroundColor = self.ColorHex(color ?? "")
                }else {
                    self.topColorV.backgroundColor = UIColor.green
                    self.underColorV.backgroundColor = UIColor.green
                }
            }
        }else {
            DispatchQueue.main.async {
                self.label1.text = (self.getSystemLabelName().assetName ?? "")+"："+(resultDic[ASSET_NAME] as? String ?? "")
                self.label2.text = ""
                self.label3.text = (self.getSystemLabelName().identityCode ?? "")+"："+(resultDic[ID] as? String ?? "")
            }
        }
        let imageUrl = self.getImageUrl(assetDic: resultDic)
        self.imageView.sd_setImage(with: URL(string: imageUrl), placeholderImage: UIImage(named: NO_IMAGE_TRUE))
        self.ocrStockView.frame = self.ocrStockViewFrame
        self.ocrStockView.isHidden = false
    }

    /// システムラベル名を取得
    /// - Returns: description
    func getSystemLabelName() -> SystemLabelName {
        var labelTitle1 = ""
        var labelTitle3 = ""
        for assetItem in self.assetCommonList {
            if assetItem["itemName"] as? String == ASSET_NAME {
                labelTitle1 = assetItem["itemDisplayName"] as? String ?? ""
            }
            if assetItem["itemName"] as? String == ID {
                labelTitle3 = assetItem["itemDisplayName"] as? String ?? ""
            }
        }
        let model = SystemLabelName()
        model.assetName = labelTitle1
        model.identityCode = labelTitle3
        return model
    }

    /// ホーム画像をとる
    /// - Parameter assetDic: assetDic description
    /// - Returns: description
    func getImageUrl(assetDic: NSMutableDictionary) -> String {
        var imageName: String?
        out: for item in assetDic.allValues {
            if item is NSArray {
                if let array = item as? NSArray {
                    for it2 in array {
                        if it2 is NSDictionary {
                            if let dic = it2 as? NSMutableDictionary {
                                if let isHomeImage = dic["isHomeImage"] as? Bool {
                                    if isHomeImage == true {
                                        imageName = dic["turl"] as? String ?? ""
                                        let imageUrl = dic["url"] as? String ?? ""
                                        let isUrlLoaded: Bool = dic["isUrlLoaded"] as? Bool ?? false
                                        if !isUrlLoaded {
                                            self.getTurl(filePath: imageUrl, result: {
                                                url in
                                                imageName=url
                                                dic["turl"] = url
                                                dic["isUrlLoaded"]=true
                                            })
                                        }
                                        break out
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return imageName ?? ""
    }

    /// Change RGB String to UIColor
    ///
    /// - Parameter color: RGB string
    /// - Returns: return UICOlor
    func ColorHex(_ color: String) -> UIColor? {
        if color.count <= 0 || color.count != 7 || color == "(null)" || color == "<null>" {
            return nil
        }
        var red: UInt32 = 0x0
        var green: UInt32 = 0x0
        var blue: UInt32 = 0x0
        let redString = String(color[color.index(color.startIndex, offsetBy: 1)...color.index(color.startIndex, offsetBy: 2)])
        let greenString = String(color[color.index(color.startIndex, offsetBy: 3)...color.index(color.startIndex, offsetBy: 4)])
        let blueString = String(color[color.index(color.startIndex, offsetBy: 5)...color.index(color.startIndex, offsetBy: 6)])
        Scanner(string: redString).scanHexInt32(&red)
        Scanner(string: greenString).scanHexInt32(&green)
        Scanner(string: blueString).scanHexInt32(&blue)
        let hexColor = UIColor.init(red: CGFloat(red)/255.0, green: CGFloat(green)/255.0, blue: CGFloat(blue)/255.0, alpha: 1)
        return hexColor
    }

    /// カテゴリー大・中・小の設定
    ///
    /// - Parameters:
    ///   - arMobielSettingInfoByTypeIdDic: arMobielSettingInfoByTypeIdDic description
    ///   - arLevelItemId: arLevelItemId description
    ///   - arLevelItemName: arLevelItemName description
    ///   - arLevelItemType: arLevelItemType description
    ///   - model: model description
    ///   - assetName: assetName description
    /// - Returns: return value description
    func setCatagoryInfo(arMobielSettingInfoByTypeIdDic: NSMutableDictionary,arLevelItemOption: String,arLevelItemId: String, arLevelItemName: String, arLevelItemDisplayName: String, arLevelItemType: String, arLevelMasterLayoutSetting: String,arLevelSubItemId: String, resultDic: NSMutableDictionary) -> String {
        var ARLevelItemName: String?
        if arMobielSettingInfoByTypeIdDic[arLevelItemId] is Int && arMobielSettingInfoByTypeIdDic[arLevelItemId] as! Int != 0 {
            if  arMobielSettingInfoByTypeIdDic[arLevelItemName] is String {
                //keyチェック
                if let title = arMobielSettingInfoByTypeIdDic[arLevelItemDisplayName] {
                    //valueチェック
                    if let itemName = resultDic[arMobielSettingInfoByTypeIdDic[arLevelItemName] ?? ""] {

                        if arMobielSettingInfoByTypeIdDic[arLevelItemType] as? String == "master" {
                            let value = ((resultDic[arMobielSettingInfoByTypeIdDic[arLevelItemName] ?? ""] as? NSMutableDictionary)?["display"] as? NSMutableDictionary)?["\(arMobielSettingInfoByTypeIdDic[arLevelSubItemId] ?? "")"] as? String ?? ""
                            let tempString = Utils.setMasterValueByLayoutSetting(arMobielSettingInfoByTypeIdDic: arMobielSettingInfoByTypeIdDic, arLevelMasterLayoutSetting: arLevelMasterLayoutSetting, itemValue: value)
                            let masterLayoutSetting = arMobielSettingInfoByTypeIdDic[arLevelMasterLayoutSetting] as? NSMutableDictionary ?? NSMutableDictionary()
                            let masterItemTitle = masterLayoutSetting["itemDisplayName"] as? String ?? ""
                            ARLevelItemName = (title as? String ?? "") + "(" + masterItemTitle + ")：" + tempString
                        } else {
                            //checkタイプ
                            if arMobielSettingInfoByTypeIdDic[arLevelItemType] as? String != "checkbox" {
                                var itemValue = ""
                                //Intタイプ
                                if itemName is Int || itemName is Double || itemName is Float {
                                    let value = (resultDic[arMobielSettingInfoByTypeIdDic[arLevelItemName] ?? ""] ?? "")
                                    itemValue = "\(value)"
                                } else if itemName is String {
                                    itemValue = itemName as! String
                                }
                                if arMobielSettingInfoByTypeIdDic[arLevelItemOption] is String {
                                    let tempString = Utils.setCommaDecimalPoint(arMobielSettingInfoByTypeIdDic: arMobielSettingInfoByTypeIdDic,arLevelItemOption: arLevelItemOption,arLevelItemType: arLevelItemType,itemValue: itemValue)
                                    ARLevelItemName = (title as? String ?? "") + "：" + tempString
                                } else {
                                    ARLevelItemName = (title as? String ?? "") + "：" + itemValue
                                }
                            } else {
                                if itemName is String {
                                    if (itemName as! String) == "1" {
                                        ARLevelItemName = (title as? String ?? "") + "：" + "✅"
                                    } else {
                                        ARLevelItemName = (title as? String ?? "") + "：" + "☑️"
                                    }

                                }else if itemName is Int {
                                    if (itemName as! Int) == 1 {
                                        ARLevelItemName = (title as? String ?? "") + "：" + "✅"
                                    } else {
                                        ARLevelItemName = (title as? String ?? "") + "：" + "☑️"
                                    }
                                }
                            }
                        }
                    } else {
                        ARLevelItemName = (title as? String ?? "") + "：" + ""
                    }
                } else {
                    ARLevelItemName = " "
                }
            } else {
                ARLevelItemName = " "
            }
        } else if arMobielSettingInfoByTypeIdDic[arLevelItemId] is Int && arMobielSettingInfoByTypeIdDic[arLevelItemId] as! Int == 0 {
            ARLevelItemName = "資産種類：" + (resultDic[ASSET_TYPE] as? String ?? "")
        } else {
            ARLevelItemName = (self.getSystemLabelName().assetName ?? "") + "：" + (resultDic[ASSET_NAME] as? String ?? "")
        }
        //権限チェック
        if arLevelItemOption != "" {
            let resultDic:NSMutableDictionary = Utils.getDictionaryFromJSONString(jsonString: arMobielSettingInfoByTypeIdDic[arLevelItemOption] as? String ?? "") as? NSMutableDictionary ?? NSMutableDictionary()
            if resultDic["sectionPrivateGroups"] != nil && resultDic["sectionPrivateGroups"] is String {
                if resultDic["sectionPrivateGroups"] as! String != ""{
                    let privateGroups = resultDic["sectionPrivateGroups"] as! String
                    let assetSet = Utils.getArrayFromStringSeparatedBy(separator: ",", str: privateGroups) as Set<String>?
                    let commonSet = SingleInstance.shareList.userGroup.intersection(assetSet ?? Set<String>())
                    if (commonSet.count) <= 0 {//権限無し
                        ARLevelItemName = "  "
                    }
                }
            }
        }
        return ARLevelItemName ?? ""
    }

    /// 画像URL取得
    ///
    /// - Parameter assetTypeId: assetTypeId description
    func getTurl(filePath: String, result: @escaping (_ str: String)-> Void) {
        ApiManager.shared.requestGetTurl(urlStr: "/secure/s3/downloadUrl?filePath=\(filePath)", param: [:], method: "GET", success: { (data) in
            let resultDic:NSDictionary = Utils.nsdataToJSON(data: data as NSData) ?? NSDictionary()
            DEBUGLOG.NSLog(message: "Get url success")
            if let turlData = resultDic["data"] {
                if turlData is NSDictionary {
                    let turl = (turlData as? NSDictionary ?? NSDictionary())["getUrl"] as? String ?? ""
                    result(turl)
                }
            }
        }, faild: { (err) in
            result("error")
            DEBUGLOG.NSLog(message: "err ===== \(err)")
        })
    }
    
    /// 資産IDで資産項目リストをとる
    ///
    /// - Parameter assetTypeId: assetTypeId description
    func getAssetMobileSettingForMobile(assetTypeId: Int,barCode: String,  result: @escaping (_ info: Dictionary<String, String>)-> Void){
        let url = URLManager.shared.getAssetInfoForMobile(barcode: barCode)
        let encodeUrlString: String = url.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        ApiManager.shared.requestForSynchronizeJson(urlStr:encodeUrlString, param: [:], method: "GET", controller: UIViewController(), success: { (data) in
            let resultDic:NSDictionary = Utils.nsdataToJSON(data: data as NSData)!
            if let assetMobileSettingForMobileDic = resultDic["assetMobileSettingForMobile"] as? NSMutableDictionary {
                self.arMobileSetttingDic.setValue(assetMobileSettingForMobileDic, forKey: barCode)
            }
            DispatchQueue.main.async {
                result(Dictionary<String, String>())
            }
        }) { (err) in

        }
    }
}

// MARK: WF承認
extension ActionOcrViewController: Alertable {
    func setDetailBtnBadge(isInit: Bool) {
        if toListBtnMgr == nil{
            DispatchQueue.main.async {
                self.view.layoutIfNeeded()
                self.toListBtnMgr = ScanToListBtnManager(btn: self.toScanListBtn)
            }
        }
        // 初期化時全て資産スキャン済み場合、スキャン数表示しない
        toListBtnMgr?.refreshCount(count: totalSavedAssetAmountCount)
    }

    func setDetailBtnBadgeInRefund(isInit: Bool) {
        DispatchQueue.main.async {
            if self.toListBtnMgr == nil{
                self.view.layoutIfNeeded()
                self.toListBtnMgr = ScanToListBtnManager(btn: self.toScanListBtn)
            }


            self.badge = self.totalSavedAssetAmountCount

            self.setToListBtnBadge(self.badge)
        }
    }

    func isAllAssetScanned() -> Bool {
        return !self.assetDataList!.contains {
            $0.scanTimes < $0.assetCount!
        }
    }

    /// スキャンされた資産リストをリセット
    func clearScanedList() {
        if self.assetIdLIst.count > 0 {
            for assetId in self.assetIdLIst {
                self.assetDataList?
                    .filter { $0.barcode == assetId }
                    .forEach {
                        $0.isScanFinish = nil
                        $0.isHasBeenStarted = nil
                        $0.isHasNotStarted = nil
                    }
            }
            if self.scanedInfoBlock != nil {
                self.scanedInfoBlock!(assetDataList ?? Array<AssetListData>(), assetIdLIst, assetList)
            }
        }
    }

    func isScanedAll(barcode: String, isManualInput: Bool = false) -> Bool {
        var result = false
        self.assetDataList?
            .filter { $0.barcode == barcode}
            .forEach {
                if $0.scanTimes == $0.assetCount ?? 0 {
                    result = true
                    if isManualInput {
                        SVProgressHUD.showError(withStatus: "\(AR_MESSAGE_SCANNED)\n\(barcode)")
                    }
                }else if $0.scanTimes > 0 && $0.scanTimes < $0.assetCount ?? 0 {
                    result = true
                }
            }
        return result
    }

    func isCanBeManualInput(barCode: String) -> Bool {
        var result: Bool = true
        self.assetDataList?
            .filter { $0.barcode == barCode }
            .forEach {
                if $0.scanTimes == $0.assetCount ?? 0 {
                    result = false
                    SVProgressHUD.showError(withStatus: "\(AR_MESSAGE_SCANNED)\n\(barCode)")
                }
            }
        return result
    }

    /// 選択されたWFのステータスをとる （レンタル・サブプロセスなし）
    /// - Parameter barCode: barCode description
    /// - Parameter isManualInput: true：手入力
    /// - Parameter result: result description
    func getAssetInfoForNoSubprocess(resultString: String, imageName: String, isManualInput: Bool = false, result: @escaping (_ info: Dictionary<String, String>)-> Void) {
        self.isStopTwoSeconds = true
        uploadOcrResultToAITeamServer(imageName: imageName)
        let ocrCode = resultString //replaceOccurrences(scanResultString: resultString)
        let getWFScan = WorkFloHttpUtils.getAssetInfoForNoSubprocess(
             processDefinitionId: progressDefinitionId ?? "",
             barCode: ocrCode,
             originalbarCode: ocrCode,
             isMultiScan: isMultiScan,
             assetListIdInt:  Int(assetListId ?? "0") ?? 0,
             processInstanceId: self.processInstanceId ?? "",
             taskId: self.taskId ?? "",
             rfidOrFreeScanOrVIPScan: rfidOrFreeScanOrVIPScanEnum.aiocr,
             rfid: "",
             isNumberNeedToBeAutoIncreas: true, // ocr扫描默认自动增加数量
             showTrim: false)
        self.requestIdList.insert(ocrCode.lowercased())
         if getWFScan.error != [:] {
             // error 情况
             let errBarcode = Array(getWFScan.error.keys)[0]
             let errMsg = Array(getWFScan.error.values)[0]
             self.errorInfo[errBarcode] = errMsg
             if errMsg == "資産が存在しません。" {
                 self.errorInfo[errBarcode] = "\(AR_MESSAGE_UNKNOWN_ASSET)\n\(ocrCode)"
             }
             if errMsg == "該当資産の閲覧権限がありません。"  {
                 self.errorInfo[errBarcode] = "この資産の閲覧権限がありません\n\(ocrCode)"
             }
             if isManualInput == true {
                 SVProgressHUD.showError(withStatus: "\(errMsg)\n\(ocrCode)")
             } else {
                 self.createDefaultView(barcode: ocrCode)
             }
             self.reset()
         } else {
             let getAssetDic = getWFScan.success
             let newAssetId = getAssetDic["assetId"] as? Int ?? 0
             self.realAssetidList.insert("\(newAssetId)")
             let assetDic = getAssetDic["assetDataDic"] as! NSMutableDictionary
             let isSuccessScan = assetDic["isSuccessScan"] as? Bool ?? false
             DispatchQueue.main.async {
                 if !isSuccessScan {
                     if self.isAssetAmountMode  {
                         // 个数情况下显示已达到最大
                         if isManualInput == true {
                             SVProgressHUD.showSuccess(withStatus: AR_MESSAGE_SCANNED)
                         } else {
                             self.errorInfo[ocrCode] = "\(AR_MESSAGE_SCANNED)\n\(ocrCode)"
                             self.createDefaultView(barcode: ocrCode)
                         }
                     } else {
                         self.setValueForStockView(resultDic: assetDic)
                     }
                 } else {
                     /// スキャンされた資産が条件とあっているか
                     ScanMusicUtil.feedback()
                     if isManualInput == true {
                         SVProgressHUD.showSuccess(withStatus: "登録完了しました\n\(ocrCode)")
                     }
                     self.totalSavedAssetAmountCount += 1
                     self.badge = self.totalSavedAssetAmountCount
                     self.setToListBtnBadge(self.badge)
                     self.setValueForStockView(resultDic: assetDic)
                 }
             }
             let isContains = assetList.contains{ (asset) -> Bool in
                 return asset["assetId"] as! Int == newAssetId
             }
             // 保证存进去的资产是唯一的
             if !isContains {
                 assetList.append(assetDic)
             } else {
                 for (index, asseted) in assetList.enumerated() {
                     let assetedId = asseted["assetId"] as? Int ?? 0
                     if assetedId == newAssetId {
                         let constBefore = asseted["savedAssetAmountBeforeScan"] as? Int ?? 0;
                         assetDic["savedAssetAmountBeforeScan"] = constBefore
                         assetList[index] = assetDic;
                     }
                 }
             }
             DispatchQueue.main.asyncAfter(deadline: DispatchTime.now() + 2) {
                 self.isStopTwoSeconds = false
             }

             DEBUGLOG.NSLog(message: self.scanResult)
         }
    }

    /// Assetに登録してないバーコード
    ///
    /// - Returns: return value description
    func createDefaultView(barcode: String) {
        DispatchQueue.main.async {
            self.ocrStockView.isHidden = true
            if self.defaultView != nil {
                self.defaultLabel.text = self.errorInfo.object(forKey: barcode) as? String
                self.defaultView.isHidden = false
            }else {
                self.defaultView = UIView.init(frame:self.defaultStockViewFrame)
                self.defaultView.backgroundColor = .white
                self.defaultView.layer.cornerRadius = 5.0
                self.defaultView.layer.masksToBounds = true
                self.defaultView.layer.borderWidth = 1.5
                self.defaultView.layer.borderColor = UIColor.red.cgColor
                self.defaultView.alpha = 0.7
                self.defaultLabel = UILabel.init(frame: self.defaultView.bounds)
                self.defaultLabel.textColor = UIColor.red
                self.defaultLabel.textAlignment = .center
                self.defaultLabel.numberOfLines = 0
                self.defaultLabel.text = self.errorInfo.object(forKey: barcode) as? String

                self.defaultView.addSubview(self.defaultLabel)
                self.view.addSubview(self.defaultView)
            }
            DispatchQueue.main.asyncAfter(deadline: DispatchTime.now() + 2) {
                if self.defaultView != nil {
                    self.defaultView.isHidden = true
                }
                self.isStopTwoSeconds = false
            }
        }
    }
    /// システムラベル
    func getAssetItemCommon() -> Bool {
        let getAssetItemCommon = HttpUtils.shared.getAssetItemCommonAndIsShowLocation(controller: self)
        if getAssetItemCommon.success != nil {
            self.assetCommonList = getAssetItemCommon.success!.assetCommonList
        }
        return getAssetItemCommon.isLocationON
    }

    func reset(_ sec:Double = 0.2) {
        DispatchQueue.main.asyncAfter(deadline: DispatchTime.now() + sec) {
            self.isStopTwoSeconds = false
        }
    }

    func getCodeStr(code: String) -> String {
        var str = code
        if let range = code.range(of: ":", options: .backwards) {
            str = String(code.suffix(from: range.upperBound))
        }
        return str
    }

}
