@mixin assets-list-translucent-sky-color {
  background: rgba(255, 255, 255, 0.85);
}
$default-assets-list-black: #323232;
$default-assets-list-background-light-blue: #4d94ff;
$assets-list-margin-universal: 10px;
$icon-width-height-fontsize: 18px;
$assets-list-radius: 8px;
$default-assets-list-height: 45px;
$default-assets-list-blue: #0b3e86;

.search-input-search-div {
  margin: 10px 10px 0;
  border-radius: #{$assets-list-radius};
  display: flex;
  align-items: center;
  width: 95%;
  height: #{$default-assets-list-height};
  @include assets-list-translucent-sky-color;
  .icon-assets-list-search-search {
    padding-left: #{$assets-list-margin-universal};
    font-size: #{$icon-width-height-fontsize};
    opacity: 1;
  }

  @mixin assets-list-search-icon-sky-button-color($search-icon-color-val) {
    color: #{$search-icon-color-val};
  }

  .assets-list-search-icon-sky-normal {
    @include assets-list-search-icon-sky-button-color(#{$default-assets-list-blue});
  }

  .assets-list-search-icon-sky-highlight {
    @include assets-list-search-icon-sky-button-color(#{$default-assets-list-background-light-blue});
  }

  .icon-assets-list-search-input {
    margin-left: #{$assets-list-margin-universal};
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    height: 100%;
  }

  .icon-assets-list-search-button {
    font-size: #{$icon-width-height-fontsize};
    padding-right: 8px;
  }

  .icon-assets-list-search-outline {
    font-size: #{$icon-width-height-fontsize};
    padding-right: 5px;
    color: #{$default-assets-list-black};
  }
}

$default-background-blue: #394bca;
$default-background-grey: #808080;
@mixin sky-button-color($color-val) {
  --background: #{$color-val};
  --background-activated: #{$color-val};
  --background-focused: #{$color-val};
  --background-hover: #{$color-val};
  --ion-color-base: #{$color-val} !important;
}
.locked-state-sky-normal-grey {
  @include sky-button-color(#{$default-background-grey});
}
.unlocked-state-sky-normal-blue {
  @include sky-button-color(#{$default-background-blue});
}

.assets-total-quantity-div {
  color: white;
  margin: 10px;
  display: flex;
  .icon-assets-middle-label-div {
    font-size: 1rem;
    font-weight: 400;
  }
}
$default-message-margin: 12px;
.assets-message-bottomp-quantity-p {
  margin-left: #{$default-message-margin};
  margin-right: #{$default-message-margin};
  font-size: 1rem;
  font-weight: 400;
  text-align: center;
}
.assets-message-bottomp-the-top-half-quantity-p {
  margin-left: #{$default-message-margin};
  margin-right: #{$default-message-margin};
  font-size: 0.8rem;
  font-weight: 400;
  text-align: center;
  margin-bottom: 0 !important;
}
